
const express = require('express');
const router = express.Router();
const db = require('../db-config.js');

// Listar todos os armazéns
router.get('/', (req, res) => {
  db.all('SELECT * FROM armazens', [], (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

// Buscar um armazém específico
router.get('/:id', (req, res) => {
  db.get('SELECT * FROM armazens WHERE id = ?', [req.params.id], (err, row) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    if (!row) {
      res.status(404).json({ error: 'Armazém não encontrado' });
      return;
    }
    res.json(row);
  });
});

// Criar novo armazém
router.post('/', (req, res) => {
  const { codigo, nome, tipo, descricao } = req.body;
  
  db.run(
    'INSERT INTO armazens (codigo, nome, tipo, descricao) VALUES (?, ?, ?, ?)',
    [codigo, nome, tipo, descricao],
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      res.json({
        id: this.lastID,
        codigo,
        nome,
        tipo,
        descricao
      });
    }
  );
});

// Atualizar armazém
router.put('/:id', (req, res) => {
  const { codigo, nome, tipo, descricao } = req.body;
  
  db.run(
    'UPDATE armazens SET codigo = ?, nome = ?, tipo = ?, descricao = ? WHERE id = ?',
    [codigo, nome, tipo, descricao, req.params.id],
    (err) => {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      res.json({
        id: req.params.id,
        codigo,
        nome,
        tipo,
        descricao
      });
    }
  );
});

// Excluir armazém
router.delete('/:id', (req, res) => {
  db.run('DELETE FROM armazens WHERE id = ?', [req.params.id], (err) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json({ message: 'Armazém excluído com sucesso' });
  });
});

module.exports = router;
