<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Correção Direta de Saldos</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .direct-fix-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .urgent-card {
            background: #fff5f5;
            border: 3px solid #e53e3e;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { border-color: #e53e3e; }
            50% { border-color: #ff6b6b; }
            100% { border-color: #e53e3e; }
        }
        .fix-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        .fix-table th, .fix-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .fix-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .fix-table tr:hover {
            background-color: #f5f5f5;
        }
        .btn-fix-direct {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
        }
        .btn-fix-direct:hover {
            background: #c82333;
        }
        .btn-fix-all-direct {
            background: #28a745;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn-fix-all-direct:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .status-fixed {
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .progress-live {
            background: #e9ecef;
            border-radius: 10px;
            padding: 3px;
            margin: 10px 0;
        }
        .progress-live-bar {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 20px;
            border-radius: 8px;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .negative-balance {
            background: #f8d7da !important;
            color: #721c24 !important;
        }
        .positive-balance {
            background: #d1ecf1 !important;
            color: #0c5460 !important;
        }
    </style>
</head>
<body>
    <div class="direct-fix-container">
        <h1>🎯 Correção Direta de Saldos Divergentes</h1>
        
        <div class="urgent-card">
            <h3>🚨 CORREÇÃO URGENTE NECESSÁRIA</h3>
            <p><strong>15 divergências críticas detectadas!</strong></p>
            <ul>
                <li>✅ <strong>Script otimizado</strong> para correção direta</li>
                <li>🎯 <strong>Atualização forçada</strong> dos saldos incorretos</li>
                <li>📊 <strong>Recálculo baseado</strong> nas movimentações reais</li>
                <li>🔒 <strong>Backup automático</strong> dos valores anteriores</li>
            </ul>
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="carregarDivergenciasEspecificas()" class="btn-fix-all-direct">🔄 CARREGAR DIVERGÊNCIAS</button>
                <button onclick="corrigirTodasDivergenciasAgora()" class="btn-fix-all-direct" id="btnCorrigirTodas" disabled>🔧 CORRIGIR TODAS AGORA</button>
            </div>
        </div>

        <div id="progressContainer" style="display: none;">
            <h3>🔄 Progresso da Correção</h3>
            <div class="progress-live">
                <div class="progress-live-bar" id="progressBarLive" style="width: 0%">0%</div>
            </div>
            <p id="progressTextLive">Preparando...</p>
        </div>

        <div id="divergenciasContainer"></div>
        <div id="resultadosContainer"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc,
            getDoc,
            query,
            where,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let divergenciasEspecificas = [];
        let statusCorrecoes = {};

        // Divergências específicas identificadas
        const divergenciasFixas = [
            { produtoId: 'rkMjdImKYP1RRxZCaKYx', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 2, diferenca: 2 },
            { produtoId: 'K7P61qXe9oaXrUBZdTff', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 3, diferenca: 3 },
            { produtoId: 'ecXGDvJK77tVivp6Po37', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 6, diferenca: 6 },
            { produtoId: 'hudTNrbAyNZeHd5KHT1j', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 1, diferenca: 1 },
            { produtoId: 'l3r9pPctw2QKvFKZib2e', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 1, diferenca: 1 },
            { produtoId: 'n4zTgGtpMKhB2qlfx8qz', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 1, diferenca: 1 },
            { produtoId: 'N7RcG6TooBEJM1S6jnsm', armazemId: 'BtRauPc2d0XyLfeBOZFj', saldoAtual: 1, saldoCalculado: 2, diferenca: 1 },
            { produtoId: '9QlS3qMMN0Ca9Bhc2R73', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: -9.6, diferenca: -9.6 },
            { produtoId: 'ydtGNB48NFWfWUKHMHq0', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 1, diferenca: 1 },
            { produtoId: '7VOpzbUeU9yBK7aQoxGf', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 4, diferenca: 4 },
            { produtoId: '6zgcFDIzybVQHD8bsFS5', armazemId: 'BtRauPc2d0XyLfeBOZFj', saldoAtual: 20, saldoCalculado: 30, diferenca: 10 },
            { produtoId: 'T141RuEsZMFYjlW8GxdK', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 2, diferenca: 2 },
            { produtoId: 'QLcQnYrl2pFiH11FhkFl', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: -1, diferenca: -1 },
            { produtoId: 'eVaDSOTuBzCkXQwTilwn', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 2, diferenca: 2 },
            { produtoId: 'Jz3RrFgpaB1OML28SzVj', armazemId: 'NoyRLpecCpVOSKT42SPM', saldoAtual: 0, saldoCalculado: 3, diferenca: 3 }
        ];

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
        };

        window.carregarDivergenciasEspecificas = async function() {
            const container = document.getElementById('divergenciasContainer');
            container.innerHTML = '<p>🔄 Carregando divergências específicas...</p>';

            try {
                // Buscar dados dos produtos e armazéns para exibição
                const [produtosSnap, armazensSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Enriquecer dados das divergências
                divergenciasEspecificas = divergenciasFixas.map(div => {
                    const produto = produtos.find(p => p.id === div.produtoId);
                    const armazem = armazens.find(a => a.id === div.armazemId);
                    
                    return {
                        ...div,
                        produtoCodigo: produto?.codigo || 'N/A',
                        produtoDescricao: produto?.descricao || 'Sem descrição',
                        armazemCodigo: armazem?.codigo || 'N/A',
                        armazemNome: armazem?.nome || 'Sem nome'
                    };
                });

                // Inicializar status
                divergenciasEspecificas.forEach((div, index) => {
                    statusCorrecoes[index] = 'pending';
                });

                mostrarDivergenciasEspecificas();
                document.getElementById('btnCorrigirTodas').disabled = false;

            } catch (error) {
                console.error('Erro ao carregar divergências:', error);
                container.innerHTML = `<div class="urgent-card">❌ Erro: ${error.message}</div>`;
            }
        };

        function mostrarDivergenciasEspecificas() {
            const container = document.getElementById('divergenciasContainer');
            
            const positivas = divergenciasEspecificas.filter(d => d.diferenca > 0);
            const negativas = divergenciasEspecificas.filter(d => d.diferenca < 0);

            let html = `
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h3>📊 Divergências a Corrigir</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                        <div><strong>Total:</strong> ${divergenciasEspecificas.length}</div>
                        <div><strong>Positivas:</strong> <span style="color: #28a745;">${positivas.length}</span></div>
                        <div><strong>Negativas:</strong> <span style="color: #dc3545;">${negativas.length}</span></div>
                        <div><strong>Soma Diferenças:</strong> ${divergenciasEspecificas.reduce((sum, d) => sum + d.diferenca, 0).toFixed(1)}</div>
                    </div>
                </div>

                <table class="fix-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Produto</th>
                            <th>Armazém</th>
                            <th>Saldo Atual</th>
                            <th>Saldo Correto</th>
                            <th>Diferença</th>
                            <th>Status</th>
                            <th>Ação</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            divergenciasEspecificas.forEach((div, index) => {
                const rowClass = div.diferenca < 0 ? 'negative-balance' : 'positive-balance';
                const statusClass = statusCorrecoes[index] === 'fixed' ? 'status-fixed' : 
                                   statusCorrecoes[index] === 'error' ? 'status-error' : 'status-pending';
                
                const statusText = statusCorrecoes[index] === 'fixed' ? 'CORRIGIDO' : 
                                  statusCorrecoes[index] === 'error' ? 'ERRO' : 'PENDENTE';

                html += `
                    <tr class="${rowClass}">
                        <td><strong>${index + 1}</strong></td>
                        <td>
                            <strong>${div.produtoCodigo}</strong><br>
                            <small>${div.produtoDescricao}</small>
                        </td>
                        <td>
                            <strong>${div.armazemCodigo}</strong><br>
                            <small>${div.armazemNome}</small>
                        </td>
                        <td>${div.saldoAtual}</td>
                        <td><strong>${div.saldoCalculado}</strong></td>
                        <td style="color: ${div.diferenca > 0 ? '#28a745' : '#dc3545'}; font-weight: bold;">
                            ${div.diferenca > 0 ? '+' : ''}${div.diferenca.toFixed(1)}
                        </td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>
                            <button onclick="corrigirDivergenciaEspecifica(${index})" 
                                    class="btn-fix-direct" 
                                    ${statusCorrecoes[index] === 'fixed' ? 'disabled' : ''}>
                                ${statusCorrecoes[index] === 'fixed' ? '✅' : '🔧 Corrigir'}
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        window.corrigirDivergenciaEspecifica = async function(index) {
            const div = divergenciasEspecificas[index];
            
            if (statusCorrecoes[index] === 'fixed') {
                alert('Esta divergência já foi corrigida!');
                return;
            }

            try {
                statusCorrecoes[index] = 'processing';
                mostrarDivergenciasEspecificas();

                await corrigirSaldoEspecifico(div);
                
                statusCorrecoes[index] = 'fixed';
                mostrarDivergenciasEspecificas();
                
                alert(`✅ Divergência ${index + 1} corrigida!\nProduto: ${div.produtoCodigo}\nSaldo atualizado: ${div.saldoAtual} → ${div.saldoCalculado}`);

            } catch (error) {
                console.error('Erro na correção:', error);
                statusCorrecoes[index] = 'error';
                mostrarDivergenciasEspecificas();
                alert(`❌ Erro ao corrigir divergência ${index + 1}: ${error.message}`);
            }
        };

        window.corrigirTodasDivergenciasAgora = async function() {
            if (!confirm(`⚠️ CONFIRMAÇÃO FINAL\n\nEsta operação irá corrigir TODAS as ${divergenciasEspecificas.length} divergências identificadas.\n\nOs saldos serão atualizados para os valores calculados baseados nas movimentações.\n\nDeseja continuar?`)) {
                return;
            }

            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBarLive');
            const progressText = document.getElementById('progressTextLive');
            const resultadosContainer = document.getElementById('resultadosContainer');

            progressContainer.style.display = 'block';
            resultadosContainer.innerHTML = '';

            try {
                let corrigidas = 0;
                let erros = 0;
                const total = divergenciasEspecificas.length;

                for (let i = 0; i < divergenciasEspecificas.length; i++) {
                    const div = divergenciasEspecificas[i];
                    
                    try {
                        statusCorrecoes[i] = 'processing';
                        mostrarDivergenciasEspecificas();

                        await corrigirSaldoEspecifico(div);
                        
                        statusCorrecoes[i] = 'fixed';
                        corrigidas++;
                        
                    } catch (error) {
                        console.error(`Erro ao corrigir divergência ${i + 1}:`, error);
                        statusCorrecoes[i] = 'error';
                        erros++;
                    }

                    const progresso = ((i + 1) / total) * 100;
                    progressBar.style.width = `${progresso}%`;
                    progressBar.textContent = `${Math.round(progresso)}%`;
                    progressText.textContent = `Processando ${i + 1}/${total} - Corrigidas: ${corrigidas}, Erros: ${erros}`;
                    
                    mostrarDivergenciasEspecificas();
                    
                    // Pequena pausa para visualização
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                progressContainer.style.display = 'none';

                const sucessoRate = (corrigidas / total) * 100;
                const cardClass = sucessoRate === 100 ? 'success-card' : sucessoRate > 80 ? 'warning-card' : 'urgent-card';

                resultadosContainer.innerHTML = `
                    <div class="${cardClass}" style="background: ${sucessoRate === 100 ? '#d4edda' : sucessoRate > 80 ? '#fff3cd' : '#f8d7da'}; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>${sucessoRate === 100 ? '✅' : sucessoRate > 80 ? '⚠️' : '❌'} Correção ${sucessoRate === 100 ? 'Concluída' : 'Parcial'}!</h3>
                        <ul>
                            <li><strong>Total processado:</strong> ${total}</li>
                            <li><strong>Divergências corrigidas:</strong> ${corrigidas}</li>
                            <li><strong>Erros:</strong> ${erros}</li>
                            <li><strong>Taxa de sucesso:</strong> ${sucessoRate.toFixed(1)}%</li>
                        </ul>
                        ${sucessoRate === 100 ? 
                            '<p><strong>🎉 Parabéns! Todas as divergências foram corrigidas com sucesso!</strong></p>' : 
                            '<p><strong>⚠️ Algumas divergências não foram corrigidas. Verifique os erros acima.</strong></p>'
                        }
                        <p><strong>Próximo passo:</strong> Execute uma nova auditoria para confirmar que não há mais divergências.</p>
                    </div>
                `;

            } catch (error) {
                console.error('Erro na correção em lote:', error);
                progressContainer.style.display = 'none';
                resultadosContainer.innerHTML = `<div class="urgent-card">❌ Erro crítico na correção: ${error.message}</div>`;
            }
        };

        async function corrigirSaldoEspecifico(divergencia) {
            // Buscar o registro de estoque específico
            const estoqueQuery = query(
                collection(db, "estoques"),
                where("produtoId", "==", divergencia.produtoId),
                where("armazemId", "==", divergencia.armazemId)
            );
            
            const estoqueSnap = await getDocs(estoqueQuery);
            
            if (estoqueSnap.empty) {
                throw new Error(`Estoque não encontrado para produto ${divergencia.produtoId} no armazém ${divergencia.armazemId}`);
            }

            if (estoqueSnap.docs.length > 1) {
                throw new Error(`Múltiplos registros de estoque encontrados - problema de duplicação`);
            }

            const estoqueDoc = estoqueSnap.docs[0];
            const estoqueAtual = estoqueDoc.data();

            // Atualizar com o saldo correto
            await updateDoc(estoqueDoc.ref, {
                saldo: divergencia.saldoCalculado,
                saldoAnterior: estoqueAtual.saldo,
                ultimaMovimentacao: Timestamp.now(),
                corrigidoEm: Timestamp.now(),
                corrigidoPor: currentUser?.nome || 'Sistema',
                motivoCorrecao: `Correção automática de divergência: ${divergencia.saldoAtual} → ${divergencia.saldoCalculado}`,
                diferencaCorrigida: divergencia.diferenca
            });

            console.log(`✅ Saldo corrigido: ${divergencia.produtoCodigo} - ${divergencia.saldoAtual} → ${divergencia.saldoCalculado}`);
        }
    </script>
</body>
</html>
