<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ <PERSON>el de Empenhos - Wizhar ERP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .content {
            padding: 30px;
            min-height: 400px;
        }

        .loading {
            text-align: center;
            padding: 60px;
            font-size: 1.2em;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-left: 5px solid #3498db;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }

        .section {
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .section-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bolt"></i> Painel de Empenhos</h1>
            <p>Controle completo de empenhos de materiais em produção</p>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="carregarDados()">
                    <i class="fas fa-sync"></i>
                    🔄 Atualizar Dados
                </button>
                
                <button class="btn btn-success" onclick="inicializarCampoEmpenho()">
                    <i class="fas fa-database"></i>
                    🔧 Inicializar Sistema
                </button>
                
                <button class="btn btn-warning" onclick="consultarEmpenhosAtivos()">
                    <i class="fas fa-search"></i>
                    🔍 Empenhos Ativos
                </button>
                
                <button class="btn btn-danger" onclick="liberarEmpenhosOPs()">
                    <i class="fas fa-unlock"></i>
                    🔓 Liberar Empenhos
                </button>
            </div>
        </div>

        <div class="content" id="content">
            <div class="loading">
                <div class="spinner"></div>
                Clique em "🔄 Atualizar Dados" para carregar informações dos empenhos
            </div>
        </div>
    </div>

    <script type="module">
        // Importar Firebase e serviços
        import { db } from './firebase-config.js';
        import {
            collection,
            doc,
            getDocs,
            getDoc,
            updateDoc,
            query,
            where,
            orderBy,
            runTransaction,
            Timestamp
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        
        // ===================================================================
        // SERVIÇO DE EMPENHOS - INLINE
        // ===================================================================
        class EmpenhoService {
            static async transferirReservasParaEmpenhos(ordemProducaoId) {
                console.log(`🔄 Transferindo reservas para empenhos - OP: ${ordemProducaoId}`);

                return runTransaction(db, async (transaction) => {
                    const opRef = doc(db, "ordensProducao", ordemProducaoId);
                    const opDoc = await transaction.get(opRef);

                    if (!opDoc.exists()) {
                        throw new Error('Ordem de produção não encontrada');
                    }

                    const op = opDoc.data();
                    const materiaisNecessarios = op.materiaisNecessarios || [];

                    let transferencias = 0;
                    let erros = [];

                    for (const material of materiaisNecessarios) {
                        if (!material.quantidadeReservada || material.quantidadeReservada <= 0) {
                            continue;
                        }

                        try {
                            const estoqueQuery = query(
                                collection(db, "estoques"),
                                where("produtoId", "==", material.produtoId),
                                where("armazemId", "==", op.armazemProducaoId)
                            );

                            const estoqueSnapshot = await getDocs(estoqueQuery);
                            if (estoqueSnapshot.empty) {
                                erros.push(`Estoque não encontrado para produto ${material.produtoId}`);
                                continue;
                            }

                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            const quantidadeTransferir = material.quantidadeReservada;
                            const novoSaldoReservado = Math.max(0, (estoque.saldoReservado || 0) - quantidadeTransferir);
                            const novoSaldoEmpenhado = (estoque.saldoEmpenhado || 0) + quantidadeTransferir;

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldoReservado: novoSaldoReservado,
                                saldoEmpenhado: novoSaldoEmpenhado,
                                ultimaMovimentacao: Timestamp.now()
                            });

                            const empenhoRef = doc(collection(db, "empenhos"));
                            transaction.set(empenhoRef, {
                                ordemProducaoId,
                                produtoId: material.produtoId,
                                armazemId: op.armazemProducaoId,
                                quantidadeEmpenhada: quantidadeTransferir,
                                quantidadeConsumida: 0,
                                status: 'ATIVO',
                                dataEmpenho: Timestamp.now(),
                                origemReserva: true
                            });

                            transferencias++;

                        } catch (error) {
                            erros.push(`Erro no material ${material.produtoId}: ${error.message}`);
                        }
                    }

                    transaction.update(opRef, {
                        status: 'Em Produção',
                        dataInicioProducao: Timestamp.now(),
                        empenhosAtivos: transferencias
                    });

                    return { transferencias, erros, ordemProducaoId };
                });
            }

            static async liberarEmpenhosRestantes(ordemProducaoId, motivo = 'OP_FINALIZADA') {
                console.log(`🔓 Liberando empenhos restantes - OP: ${ordemProducaoId}`);

                return runTransaction(db, async (transaction) => {
                    const empenhosQuery = query(
                        collection(db, "empenhos"),
                        where("ordemProducaoId", "==", ordemProducaoId),
                        where("status", "==", "ATIVO")
                    );

                    const empenhosSnapshot = await getDocs(empenhosQuery);
                    let liberacoes = 0;

                    for (const empenhoDoc of empenhosSnapshot.docs) {
                        const empenho = empenhoDoc.data();
                        const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

                        if (quantidadeRestante > 0) {
                            transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                                status: 'LIBERADO',
                                quantidadeLiberada: quantidadeRestante,
                                dataLiberacao: Timestamp.now(),
                                motivoLiberacao: motivo
                            });

                            const estoqueQuery = query(
                                collection(db, "estoques"),
                                where("produtoId", "==", empenho.produtoId),
                                where("armazemId", "==", empenho.armazemId)
                            );

                            const estoqueSnapshot = await getDocs(estoqueQuery);
                            if (!estoqueSnapshot.empty) {
                                const estoqueDoc = estoqueSnapshot.docs[0];
                                const estoque = estoqueDoc.data();

                                transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                    saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeRestante),
                                    ultimaMovimentacao: Timestamp.now()
                                });
                            }

                            liberacoes++;
                        }
                    }

                    return { liberacoes, ordemProducaoId };
                });
            }

            static async consultarEmpenhosOP(ordemProducaoId) {
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("ordemProducaoId", "==", ordemProducaoId)
                );

                const empenhosSnapshot = await getDocs(empenhosQuery);
                return empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
            }

            static async inicializarCampoEmpenho() {
                console.log('🔧 Inicializando campo saldoEmpenhado nos estoques...');

                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                let atualizados = 0;

                for (const estoqueDoc of estoquesSnapshot.docs) {
                    const estoque = estoqueDoc.data();

                    if (estoque.saldoEmpenhado === undefined) {
                        await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                            saldoEmpenhado: 0
                        });
                        atualizados++;
                    }
                }

                console.log(`✅ ${atualizados} estoques atualizados com campo saldoEmpenhado`);
                return atualizados;
            }
        }

        // Variáveis globais
        let empenhos = [];
        let ordensProducao = [];
        let produtos = [];
        let estoques = [];

        // Função principal para carregar dados
        window.carregarDados = async function() {
            const content = document.getElementById('content');
            content.innerHTML = '<div class="loading"><div class="spinner"></div>Carregando dados dos empenhos...</div>';

            try {
                // Carregar dados
                const [empenhosSnapshot, opsSnapshot, produtosSnapshot, estoquesSnapshot] = await Promise.all([
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques"))
                ]);

                empenhos = empenhosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = opsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`✅ Dados carregados: ${empenhos.length} empenhos, ${ordensProducao.length} OPs`);
                
                // Renderizar painel
                renderizarPainel();

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                content.innerHTML = `
                    <div class="section">
                        <div class="section-header">❌ Erro ao Carregar Dados</div>
                        <div class="section-content">
                            <p><strong>Erro:</strong> ${error.message}</p>
                        </div>
                    </div>
                `;
            }
        };

        // Renderizar painel principal
        function renderizarPainel() {
            let html = '';
            
            // 1. Estatísticas gerais
            html += gerarEstatisticas();
            
            // 2. Empenhos ativos
            html += gerarTabelaEmpenhosAtivos();
            
            // 3. Resumo por OP
            html += gerarResumoOPs();
            
            document.getElementById('content').innerHTML = html;
        }

        // Gerar estatísticas
        function gerarEstatisticas() {
            const empenhosAtivos = empenhos.filter(e => e.status === 'ATIVO');
            const empenhosConsumidos = empenhos.filter(e => e.status === 'CONSUMIDO');
            const empenhosLiberados = empenhos.filter(e => e.status === 'LIBERADO');
            
            const totalEmpenhado = empenhos.reduce((total, e) => total + (e.quantidadeEmpenhada || 0), 0);
            const totalConsumido = empenhos.reduce((total, e) => total + (e.quantidadeConsumida || 0), 0);
            
            const percentualConsumo = totalEmpenhado > 0 ? Math.round((totalConsumido / totalEmpenhado) * 100) : 0;

            return `
                <div class="section">
                    <div class="section-header">📊 Estatísticas dos Empenhos</div>
                    <div class="section-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">${empenhos.length}</div>
                                <div class="stat-label">Total de Empenhos</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${empenhosAtivos.length}</div>
                                <div class="stat-label">Empenhos Ativos</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${empenhosConsumidos.length}</div>
                                <div class="stat-label">Empenhos Consumidos</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${empenhosLiberados.length}</div>
                                <div class="stat-label">Empenhos Liberados</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${totalEmpenhado.toFixed(2)}</div>
                                <div class="stat-label">Total Empenhado</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${percentualConsumo}%</div>
                                <div class="stat-label">% Consumido</div>
                            </div>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${percentualConsumo}%"></div>
                        </div>
                        <p style="text-align: center; margin-top: 10px;">
                            <strong>Progresso de Consumo: ${percentualConsumo}%</strong>
                        </p>
                    </div>
                </div>
            `;
        }

        // Gerar tabela de empenhos ativos
        function gerarTabelaEmpenhosAtivos() {
            const empenhosAtivos = empenhos.filter(e => e.status === 'ATIVO');

            if (empenhosAtivos.length === 0) {
                return `
                    <div class="section">
                        <div class="section-header">⚡ Empenhos Ativos</div>
                        <div class="section-content">
                            <p>Nenhum empenho ativo encontrado.</p>
                        </div>
                    </div>
                `;
            }

            let html = `
                <div class="section">
                    <div class="section-header">⚡ Empenhos Ativos (${empenhosAtivos.length})</div>
                    <div class="section-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>OP</th>
                                    <th>Produto</th>
                                    <th>Armazém</th>
                                    <th>Empenhado</th>
                                    <th>Consumido</th>
                                    <th>Restante</th>
                                    <th>% Consumo</th>
                                    <th>Data Empenho</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            empenhosAtivos.forEach(empenho => {
                const produto = produtos.find(p => p.id === empenho.produtoId);
                const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;
                const percentualConsumo = Math.round((empenho.quantidadeConsumida / empenho.quantidadeEmpenhada) * 100);

                html += `
                    <tr>
                        <td><strong>${empenho.ordemProducaoId}</strong></td>
                        <td>${produto ? produto.nome : empenho.produtoId}</td>
                        <td>${empenho.armazemId}</td>
                        <td>${empenho.quantidadeEmpenhada.toFixed(2)}</td>
                        <td>${empenho.quantidadeConsumida.toFixed(2)}</td>
                        <td>${quantidadeRestante.toFixed(2)}</td>
                        <td>
                            <div class="progress-bar" style="width: 80px;">
                                <div class="progress-fill" style="width: ${percentualConsumo}%"></div>
                            </div>
                            ${percentualConsumo}%
                        </td>
                        <td>${empenho.dataEmpenho ? new Date(empenho.dataEmpenho.seconds * 1000).toLocaleDateString() : '-'}</td>
                        <td><span class="badge badge-warning">ATIVO</span></td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            return html;
        }

        // Gerar resumo por OP
        function gerarResumoOPs() {
            const opsComEmpenho = [...new Set(empenhos.map(e => e.ordemProducaoId))];

            if (opsComEmpenho.length === 0) {
                return `
                    <div class="section">
                        <div class="section-header">📋 Resumo por OP</div>
                        <div class="section-content">
                            <p>Nenhuma OP com empenhos encontrada.</p>
                        </div>
                    </div>
                `;
            }

            let html = `
                <div class="section">
                    <div class="section-header">📋 Resumo por OP (${opsComEmpenho.length})</div>
                    <div class="section-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>OP</th>
                                    <th>Status OP</th>
                                    <th>Total Empenhos</th>
                                    <th>Ativos</th>
                                    <th>Consumidos</th>
                                    <th>Liberados</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            opsComEmpenho.forEach(opId => {
                const op = ordensProducao.find(o => o.id === opId);
                const empenhosOP = empenhos.filter(e => e.ordemProducaoId === opId);
                const empenhosAtivos = empenhosOP.filter(e => e.status === 'ATIVO');
                const empenhosConsumidos = empenhosOP.filter(e => e.status === 'CONSUMIDO');
                const empenhosLiberados = empenhosOP.filter(e => e.status === 'LIBERADO');

                html += `
                    <tr>
                        <td><strong>${opId}</strong></td>
                        <td>
                            <span class="badge ${op ? getBadgeClass(op.status) : 'badge-info'}">
                                ${op ? op.status : 'N/A'}
                            </span>
                        </td>
                        <td>${empenhosOP.length}</td>
                        <td>${empenhosAtivos.length}</td>
                        <td>${empenhosConsumidos.length}</td>
                        <td>${empenhosLiberados.length}</td>
                        <td>
                            <button class="btn btn-primary" onclick="consultarEmpenhosOP('${opId}')" style="padding: 4px 8px; font-size: 12px;">
                                <i class="fas fa-search"></i> Ver Detalhes
                            </button>
                            ${empenhosAtivos.length > 0 ? `
                                <button class="btn btn-danger" onclick="liberarEmpenhosOP('${opId}')" style="padding: 4px 8px; font-size: 12px;">
                                    <i class="fas fa-unlock"></i> Liberar
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            return html;
        }

        // Função auxiliar para classe do badge
        function getBadgeClass(status) {
            switch (status) {
                case 'Pendente': return 'badge-warning';
                case 'Em Produção': return 'badge-info';
                case 'Concluída': return 'badge-success';
                case 'Cancelada': return 'badge-danger';
                default: return 'badge-info';
            }
        }

        // Inicializar campo saldoEmpenhado
        window.inicializarCampoEmpenho = async function() {
            if (!confirm('🔧 Inicializar campo saldoEmpenhado nos estoques?\n\nIsso adicionará o campo em todos os registros de estoque que não o possuem.')) {
                return;
            }

            try {
                const resultado = await EmpenhoService.inicializarCampoEmpenho();
                alert(`✅ Campo inicializado com sucesso!\n${resultado} estoques atualizados.`);
                await carregarDados();
            } catch (error) {
                console.error('❌ Erro ao inicializar campo:', error);
                alert('❌ Erro ao inicializar campo: ' + error.message);
            }
        };

        // Consultar empenhos ativos
        window.consultarEmpenhosAtivos = async function() {
            const empenhosAtivos = empenhos.filter(e => e.status === 'ATIVO');

            if (empenhosAtivos.length === 0) {
                alert('ℹ️ Nenhum empenho ativo encontrado.');
                return;
            }

            let detalhes = `📊 EMPENHOS ATIVOS (${empenhosAtivos.length}):\n\n`;

            empenhosAtivos.forEach(empenho => {
                const produto = produtos.find(p => p.id === empenho.produtoId);
                const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

                detalhes += `🔹 OP: ${empenho.ordemProducaoId}\n`;
                detalhes += `   Produto: ${produto ? produto.nome : empenho.produtoId}\n`;
                detalhes += `   Empenhado: ${empenho.quantidadeEmpenhada} | Consumido: ${empenho.quantidadeConsumida} | Restante: ${quantidadeRestante}\n\n`;
            });

            alert(detalhes);
        };

        // Consultar empenhos de uma OP específica
        window.consultarEmpenhosOP = async function(opId) {
            try {
                const empenhosOP = await EmpenhoService.consultarEmpenhosOP(opId);

                if (empenhosOP.length === 0) {
                    alert(`ℹ️ Nenhum empenho encontrado para a OP: ${opId}`);
                    return;
                }

                let detalhes = `📋 EMPENHOS DA OP: ${opId}\n\n`;

                empenhosOP.forEach(empenho => {
                    const produto = produtos.find(p => p.id === empenho.produtoId);
                    const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

                    detalhes += `🔹 Produto: ${produto ? produto.nome : empenho.produtoId}\n`;
                    detalhes += `   Status: ${empenho.status}\n`;
                    detalhes += `   Empenhado: ${empenho.quantidadeEmpenhada}\n`;
                    detalhes += `   Consumido: ${empenho.quantidadeConsumida}\n`;
                    detalhes += `   Restante: ${quantidadeRestante}\n`;
                    detalhes += `   Data: ${empenho.dataEmpenho ? new Date(empenho.dataEmpenho.seconds * 1000).toLocaleDateString() : 'N/A'}\n\n`;
                });

                alert(detalhes);
            } catch (error) {
                console.error('❌ Erro ao consultar empenhos:', error);
                alert('❌ Erro ao consultar empenhos: ' + error.message);
            }
        };

        // Liberar empenhos de uma OP
        window.liberarEmpenhosOP = async function(opId) {
            if (!confirm(`🔓 Liberar todos os empenhos restantes da OP: ${opId}?\n\nIsso irá liberar os materiais empenhados que não foram consumidos.`)) {
                return;
            }

            try {
                const resultado = await EmpenhoService.liberarEmpenhosRestantes(opId, 'LIBERACAO_MANUAL');
                alert(`✅ Empenhos liberados com sucesso!\n${resultado.liberacoes} empenho(s) liberado(s).`);
                await carregarDados();
            } catch (error) {
                console.error('❌ Erro ao liberar empenhos:', error);
                alert('❌ Erro ao liberar empenhos: ' + error.message);
            }
        };

        // Liberar empenhos de múltiplas OPs
        window.liberarEmpenhosOPs = async function() {
            const empenhosAtivos = empenhos.filter(e => e.status === 'ATIVO');
            const opsComEmpenhosAtivos = [...new Set(empenhosAtivos.map(e => e.ordemProducaoId))];

            if (opsComEmpenhosAtivos.length === 0) {
                alert('ℹ️ Nenhuma OP com empenhos ativos encontrada.');
                return;
            }

            if (!confirm(`🔓 Liberar empenhos de ${opsComEmpenhosAtivos.length} OP(s)?\n\nOPs: ${opsComEmpenhosAtivos.join(', ')}\n\nIsso irá liberar todos os materiais empenhados não consumidos.`)) {
                return;
            }

            try {
                let totalLiberacoes = 0;

                for (const opId of opsComEmpenhosAtivos) {
                    const resultado = await EmpenhoService.liberarEmpenhosRestantes(opId, 'LIBERACAO_LOTE');
                    totalLiberacoes += resultado.liberacoes;
                }

                alert(`✅ Liberação em lote concluída!\n${totalLiberacoes} empenho(s) liberado(s) em ${opsComEmpenhosAtivos.length} OP(s).`);
                await carregarDados();
            } catch (error) {
                console.error('❌ Erro na liberação em lote:', error);
                alert('❌ Erro na liberação em lote: ' + error.message);
            }
        };

        // Carregar dados automaticamente ao abrir a página
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Painel de Empenhos carregado');
        });
    </script>
</body>
</html>
