<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PLAZHA - SISTEMAS - Backup do Sistema</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      font-size: 13px;
    }

    .sap-header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sap-logo {
      font-weight: bold;
      font-size: 24px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .sap-title {
      color: var(--primary-color);
      font-size: 20px;
      margin: 0 0 20px 0;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
    }

    .totvs-form {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .totvs-form h2 {
      color: var(--primary-color);
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-group {
      margin-bottom: 10px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }

    .totvs-select, .totvs-input {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid var(--border-color);
      border-radius: 3px;
      font-size: 13px;
      background-color: white;
    }

    .totvs-select:focus, .totvs-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.2);
    }

    .totvs-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-top: 15px;
      font-size: 12px;
    }

    .totvs-table th {
      background-color: var(--secondary-color);
      color: var(--primary-color);
      font-weight: 600;
      padding: 8px 5px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .totvs-table td {
      padding: 6px 5px;
      border: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .totvs-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .totvs-table tr:hover {
      background-color: #e6f2ff;
    }

    .btn-totvs-primary {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
    }

    .btn-totvs-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-totvs-secondary {
      background-color: white;
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
    }

    .btn-totvs-secondary:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-totvs-danger {
      background-color: var(--danger-color);
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
    }

    .btn-totvs-danger:hover {
      background-color: #a30000;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      justify-content: flex-end;
    }

    .notification {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      display: none;
    }

    .notification-success {
      background-color: #d4edda;
      color: #155724;
      display: block;
    }

    .notification-error {
      background-color: #f8d7da;
      color: #721c24;
      display: block;
    }

    .progress-container {
      margin-top: 10px;
      display: none;
    }

    .progress-container progress {
      width: 100%;
      height: 20px;
      margin-bottom: 5px;
    }

    .collections-list {
      margin-top: 10px;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid var(--border-color);
      padding: 15px;
      border-radius: 8px;
      background: #fafafa;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 8px;
    }

    .collections-list label {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
      transition: all 0.2s ease;
      cursor: pointer;
      font-size: 12px;
    }

    .collections-list label:hover {
      background: #f0f8ff;
      border-color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .collections-list input[type="checkbox"] {
      margin-right: 10px;
      accent-color: var(--primary-color);
    }

    .collections-list label::before {
      content: "📊";
      margin-right: 8px;
      font-size: 14px;
    }

    /* Ícones específicos para diferentes tipos de coleções */
    .collections-list label[data-type="users"]::before { content: "👥"; }
    .collections-list label[data-type="products"]::before { content: "📦"; }
    .collections-list label[data-type="orders"]::before { content: "📋"; }
    .collections-list label[data-type="finance"]::before { content: "💰"; }
    .collections-list label[data-type="warehouse"]::before { content: "🏪"; }
    .collections-list label[data-type="quality"]::before { content: "✅"; }
    .collections-list label[data-type="logs"]::before { content: "📝"; }
    .collections-list label[data-type="config"]::before { content: "⚙️"; }
    .collections-list label[data-type="reports"]::before { content: "📊"; }
    .collections-list label[data-type="ai"]::before { content: "🤖"; }
  </style>
</head>
<body>
  <div class="sap-header">
    <div class="sap-logo">PLAZHA - SISTEMAS</div>
    <div id="authContainer">Usuário: <span id="userStatus">Não logado</span> | Empresa: 1000</div>
  </div>

  <div class="container">
    <div class="sap-title"><i class="fas fa-download"></i> Backup do Sistema</div>

    <div class="totvs-form">
      <h2>Backup Manual</h2>
      <div class="form-row">
        <div class="form-group" style="grid-column: 1 / -1;">
          <label>Coleções para Backup (Total: <span id="totalCollections">0</span> | Selecionadas: <span id="selectedCount">0</span>)</label>
          <div style="margin: 10px 0; display: flex; gap: 10px; flex-wrap: wrap;">
            <button type="button" class="btn-totvs-secondary" onclick="selectAllCollections()">
              <i class="fas fa-check-square"></i> Selecionar Todas
            </button>
            <button type="button" class="btn-totvs-secondary" onclick="deselectAllCollections()">
              <i class="fas fa-square"></i> Desselecionar Todas
            </button>
            <button type="button" class="btn-totvs-secondary" onclick="selectByCategory('users')">
              <i class="fas fa-users"></i> Usuários
            </button>
            <button type="button" class="btn-totvs-secondary" onclick="selectByCategory('products')">
              <i class="fas fa-box"></i> Produtos
            </button>
            <button type="button" class="btn-totvs-secondary" onclick="selectByCategory('warehouse')">
              <i class="fas fa-warehouse"></i> Estoque
            </button>
            <button type="button" class="btn-totvs-secondary" onclick="selectByCategory('logs')">
              <i class="fas fa-file-alt"></i> Logs
            </button>
          </div>
          <div class="collections-list" id="collectionsList"></div>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <button id="exportButton" class="btn-totvs-primary" style="width: 100%; padding: 12px;">
            <i class="fas fa-download"></i> Exportar Dados Selecionados
          </button>
        </div>
      </div>
      <div class="progress-container" id="progressBar">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span id="progressText">Preparando backup...</span>
          <span id="progressPercent">0%</span>
        </div>
        <progress value="0" max="100" id="progress" style="width: 100%; height: 25px;"></progress>
        <div style="margin-top: 10px; font-size: 11px; color: #666;">
          <div>Coleção atual: <span id="currentCollection">-</span></div>
          <div>Registros processados: <span id="recordsProcessed">0</span></div>
          <div>Tamanho estimado: <span id="estimatedSize">Calculando...</span></div>
        </div>
      </div>
    </div>

    <div class="totvs-form">
      <h2>Backup Automático</h2>
      <div class="form-row">
        <div class="form-group">
          <label>Frequência</label>
          <select id="frequencia" class="totvs-select">
            <option value="diario">Diário</option>
            <option value="semanal">Semanal</option>
            <option value="mensal">Mensal</option>
          </select>
        </div>
        <div class="form-group">
          <button id="configButton" class="btn-totvs-primary"><i class="fas fa-save"></i> Salvar Configuração</button>
        </div>
      </div>
    </div>

    <div class="totvs-form">
      <h2>Histórico de Backups</h2>
      <table class="totvs-table">
        <thead>
          <tr>
            <th>Data</th>
            <th>Tipo</th>
            <th>Status</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="historicoBackup"></tbody>
      </table>
    </div>

    <div class="form-actions">
      <button class="btn-totvs-secondary" onclick="window.location.href='home.html'"><i class="fas fa-arrow-left"></i> Voltar</button>
      <button class="btn-totvs-danger" id="logoutButton" style="display: none;" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</button>
    </div>
  </div>

  <script type="module">
    import { db, storage } from './firebase-config.js';
    import { collection, getDocs, addDoc, doc, getDoc, Timestamp, writeBatch } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
    import { ref, uploadString, getDownloadURL } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";

    const collections = [
      'acessos',
      'ajustesEstoque',
      'apontamentos',
      'armazens',
      'avaliacoesFornecedores',
      'backups',
      'central',
      'centrosCusto',
      'clientes',
      'condicoesPagamento',
      'configuracoes',
      'contadores',
      'cotacoes',
      'emails',
      'empresa',
      'especificacoesInspecao',
      'estoqueQualidade',
      'estoques',
      'estruturas',
      'familias',
      'fornecedores',
      'grupos',
      'historico_produtos',
      'log_cotacoes',
      'log_envios',
      'logsAlteracoes',
      'logsAtividades',
      'logsAuditoria',
      'movimentacoesEstoque',
      'operacoes',
      'orcamentos',
      'ordensProducao',
      'parametros',
      'pedidosCompra',
      'permissoes',
      'produtos',
      'produtos_fornecedores',
      'recomendacoesIA',
      'recursos',
      'relatoriosGerados',
      'sequenciais',
      'solicitacoesCompra',
      'transferenciasArmazem',
      'usuarios'
    ];

    let usuarioAtual = null;

    window.onload = async function() {
      usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
      if (!usuarioAtual) {
        showNotification('Por favor, faça login para acessar o backup.', 'error');
        window.location.href = 'login.html';
        return;
      }
      document.getElementById('userStatus').textContent = usuarioAtual.nome;
      document.getElementById('logoutButton').style.display = 'inline';
      await atualizarHistorico();
      populateCollectionsList();

      // Adicionar ouvintes de eventos
      document.getElementById('exportButton').addEventListener('click', exportarDados);
      document.getElementById('configButton').addEventListener('click', configurarBackupAutomatico);
    };

    function populateCollectionsList() {
      const collectionsList = document.getElementById('collectionsList');
      collectionsList.innerHTML = '';

      // Categorizar coleções para ícones apropriados
      const collectionCategories = {
        'usuarios': 'users',
        'acessos': 'users',
        'permissoes': 'users',
        'produtos': 'products',
        'produtos_fornecedores': 'products',
        'familias': 'products',
        'grupos': 'products',
        'estruturas': 'products',
        'historico_produtos': 'products',
        'ordensProducao': 'orders',
        'pedidosCompra': 'orders',
        'solicitacoesCompra': 'orders',
        'apontamentos': 'orders',
        'cotacoes': 'finance',
        'orcamentos': 'finance',
        'condicoesPagamento': 'finance',
        'armazens': 'warehouse',
        'estoques': 'warehouse',
        'estoqueQualidade': 'warehouse',
        'movimentacoesEstoque': 'warehouse',
        'transferenciasArmazem': 'warehouse',
        'ajustesEstoque': 'warehouse',
        'avaliacoesFornecedores': 'quality',
        'especificacoesInspecao': 'quality',
        'logsAlteracoes': 'logs',
        'logsAtividades': 'logs',
        'logsAuditoria': 'logs',
        'log_cotacoes': 'logs',
        'log_envios': 'logs',
        'configuracoes': 'config',
        'parametros': 'config',
        'contadores': 'config',
        'sequenciais': 'config',
        'relatoriosGerados': 'reports',
        'recomendacoesIA': 'ai'
      };

      collections.forEach(collection => {
        const label = document.createElement('label');
        const category = collectionCategories[collection] || 'default';
        label.setAttribute('data-type', category);
        label.innerHTML = `
          <input type="checkbox" name="collections" value="${collection}" checked>
          ${collection}
        `;
        collectionsList.appendChild(label);
      });

      updateSelectionCount();

      // Adicionar event listeners para atualizar contador
      document.querySelectorAll('input[name="collections"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectionCount);
      });
    }

    function updateSelectionCount() {
      const total = collections.length;
      const selected = document.querySelectorAll('input[name="collections"]:checked').length;
      document.getElementById('totalCollections').textContent = total;
      document.getElementById('selectedCount').textContent = selected;
    }

    window.selectAllCollections = function() {
      document.querySelectorAll('input[name="collections"]').forEach(checkbox => {
        checkbox.checked = true;
      });
      updateSelectionCount();
    };

    window.deselectAllCollections = function() {
      document.querySelectorAll('input[name="collections"]').forEach(checkbox => {
        checkbox.checked = false;
      });
      updateSelectionCount();
    };

    window.selectByCategory = function(category) {
      // Primeiro desselecionar todas
      deselectAllCollections();

      // Depois selecionar apenas da categoria
      document.querySelectorAll(`label[data-type="${category}"] input[type="checkbox"]`).forEach(checkbox => {
        checkbox.checked = true;
      });
      updateSelectionCount();
    };

    function getSelectedCollections() {
      const checkboxes = document.querySelectorAll('input[name="collections"]:checked');
      return Array.from(checkboxes).map(checkbox => checkbox.value);
    }

    async function exportarDados() {
      const progressBar = document.getElementById('progressBar');
      const progress = document.getElementById('progress');
      const progressText = document.getElementById('progressText');
      const progressPercent = document.getElementById('progressPercent');
      const currentCollection = document.getElementById('currentCollection');
      const recordsProcessed = document.getElementById('recordsProcessed');
      const estimatedSize = document.getElementById('estimatedSize');
      const selectedCollections = getSelectedCollections();

      if (selectedCollections.length === 0) {
        showNotification('Selecione pelo menos uma coleção para o backup!', 'error');
        return;
      }

      progressBar.style.display = 'block';
      progress.value = 0;
      progressText.textContent = 'Iniciando backup...';
      progressPercent.textContent = '0%';
      currentCollection.textContent = '-';
      recordsProcessed.textContent = '0';
      estimatedSize.textContent = 'Calculando...';

      const backup = {};
      let totalRecords = 0;
      let totalSize = 0;

      try {
        for (let i = 0; i < selectedCollections.length; i++) {
          const collectionName = selectedCollections[i];
          currentCollection.textContent = collectionName;
          progressText.textContent = `Processando ${collectionName}...`;

          const snapshot = await getDocs(collection(db, collectionName));
          const collectionData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          backup[collectionName] = collectionData;
          totalRecords += collectionData.length;
          recordsProcessed.textContent = totalRecords.toLocaleString();

          // Calcular tamanho estimado
          const collectionSize = JSON.stringify(collectionData).length;
          totalSize += collectionSize;
          estimatedSize.textContent = formatBytes(totalSize);

          const progressValue = ((i + 1) / selectedCollections.length) * 100;
          progress.value = progressValue;
          progressPercent.textContent = Math.round(progressValue) + '%';

          // Pequena pausa para permitir atualização da UI
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        progressText.textContent = 'Gerando arquivo de backup...';
        const backupStr = JSON.stringify(backup, null, 2);
        const blob = new Blob([backupStr], { type: 'application/json' });
        const finalSize = blob.size;
        estimatedSize.textContent = formatBytes(finalSize);

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_naliteck_${new Date().toISOString().split('T')[0]}_${selectedCollections.length}colecoes.json`;
        a.click();
        URL.revokeObjectURL(url);

        progressText.textContent = 'Backup concluído com sucesso!';
        await registrarBackup('manual', `concluído - ${selectedCollections.length} coleções, ${totalRecords} registros, ${formatBytes(finalSize)}`);
        await atualizarHistorico();
        showNotification(`Backup concluído! ${selectedCollections.length} coleções, ${totalRecords.toLocaleString()} registros, ${formatBytes(finalSize)}`, 'success');

        // Ocultar barra de progresso após 3 segundos
        setTimeout(() => {
          progressBar.style.display = 'none';
        }, 3000);

      } catch (error) {
        console.error('Erro no backup:', error);
        progressText.textContent = 'Erro durante o backup';
        await registrarBackup('manual', `erro: ${error.message}`);
        showNotification('Erro ao fazer backup: ' + error.message, 'error');
      }
    }

    function formatBytes(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async function backupAutomatico() {
      const selectedCollections = getSelectedCollections();

      if (selectedCollections.length === 0) {
        showNotification('Selecione pelo menos uma coleção para o backup automático!', 'error');
        return;
      }

      const backup = {};
      try {
        for (const collectionName of selectedCollections) {
          const snapshot = await getDocs(collection(db, collectionName));
          backup[collectionName] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
        }

        const backupStr = JSON.stringify(backup, null, 2);
        const backupRef = ref(storage, `backups/backup_${new Date().toISOString()}.json`);
        await uploadString(backupRef, backupStr);

        await registrarBackup('automático', 'concluído');
        await atualizarHistorico();
        showNotification('Backup automático concluído!', 'success');
      } catch (error) {
        console.error('Erro no backup automático:', error);
        await registrarBackup('automático', 'erro');
        showNotification('Erro no backup automático: ' + error.message, 'error');
      }
    }

    async function configurarBackupAutomatico() {
      const frequencia = document.getElementById('frequencia').value;
      const selectedCollections = getSelectedCollections();

      if (selectedCollections.length === 0) {
        showNotification('Selecione pelo menos uma coleção para o backup automático!', 'error');
        return;
      }

      let intervalMs;

      switch (frequencia) {
        case 'diario':
          intervalMs = 24 * 60 * 60 * 1000;
          break;
        case 'semanal':
          intervalMs = 7 * 24 * 60 * 60 * 1000;
          break;
        case 'mensal':
          intervalMs = 30 * 24 * 60 * 60 * 1000;
          break;
      }

      clearInterval(window.backupInterval);
      window.backupInterval = setInterval(backupAutomatico, intervalMs);

      await registrarBackup('configuração', `frequência ${frequencia} configurada`);
      await atualizarHistorico();
      showNotification(`Backup automático configurado para ${frequencia}!`, 'success');
    }

    async function registrarBackup(tipo, status) {
      try {
        await addDoc(collection(db, 'backups'), {
          data: Timestamp.now(),
          tipo: tipo,
          status: status
        });
      } catch (error) {
        console.error('Erro ao registrar backup:', error);
        throw error; // Propaga o erro para ser tratado pela função chamadora
      }
    }

    async function atualizarHistorico() {
      const tbody = document.getElementById('historicoBackup');
      const snapshot = await getDocs(collection(db, 'backups'));

      tbody.innerHTML = '';
      snapshot.docs.forEach(doc => {
        const backup = doc.data();
        let dateStr;

        try {
          // Tenta converter para data se for um Timestamp
          if (backup.data && typeof backup.data.toDate === 'function') {
            dateStr = backup.data.toDate().toLocaleString();
          } else if (backup.data instanceof Date) {
            dateStr = backup.data.toLocaleString();
          } else if (typeof backup.data === 'string') {
            dateStr = backup.data;
          } else {
            dateStr = 'Data desconhecida';
          }
        } catch (e) {
          console.error('Erro ao formatar data do backup:', e);
          dateStr = 'Data inválida';
        }

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${dateStr}</td>
          <td>${backup.tipo || 'N/A'}</td>
          <td><span class="totvs-status ${getStatusClass(backup.status)}">${backup.status || 'N/A'}</span></td>
          <td>
            ${backup.tipo === 'automático' ? `<button class="btn-totvs-secondary download-backup" data-docid="${doc.id}"><i class="fas fa-download"></i> Download</button>` : ''}
          </td>
        `;
        tbody.appendChild(row);
      });

      // Adicionar ouvintes para botões de download
      document.querySelectorAll('.download-backup').forEach(button => {
        button.addEventListener('click', () => downloadBackup(button.dataset.docid));
      });
    }

    function getStatusClass(status) {
      switch (status) {
        case 'concluído':
          return 'status-active';
        case 'erro':
          return 'status-inactive';
        default:
          return 'status-pending';
      }
    }

    async function downloadBackup(docId) {
      try {
        const docRef = doc(db, 'backups', docId);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
          const backupData = docSnap.data();
          const backupRef = ref(storage, `backups/backup_${backupData.data.toDate().toISOString()}.json`);
          const url = await getDownloadURL(backupRef);
          const a = document.createElement('a');
          a.href = url;
          a.download = `backup_${backupData.data.toDate().toISOString()}.json`;
          a.click();
          showNotification('Download iniciado!', 'success');
        }
      } catch (error) {
        console.error('Erro ao baixar backup:', error);
        showNotification('Erro ao baixar backup: ' + error.message, 'error');
      }
    }

    window.logout = function() {
      localStorage.removeItem('currentUser');
      window.location.href = 'login.html';
    };

    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.className = `notification notification-${type}`;
      document.querySelector('.container').prepend(notification);
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    async function realizarBackup() {
      try {
        // Usar a mesma lista de coleções definida globalmente
        const colecoes = collections;

        const backup = {};
        const promises = colecoes.map(async (colecao) => {
          try {
            const snapshot = await getDocs(collection(db, colecao));
            backup[colecao] = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            console.log(`Backup da coleção ${colecao} realizado com sucesso!`);
          } catch (error) {
            console.warn(`Erro ao fazer backup da coleção ${colecao}:`, error);
            backup[colecao] = [];
          }
        });

        await Promise.all(promises);

        const backupStr = JSON.stringify(backup, null, 2);
        const blob = new Blob([backupStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString()}.json`;
        a.click();

        await registrarBackup('manual', 'concluído');
        await atualizarHistorico();
        showNotification('Backup concluído com sucesso!', 'success');

      } catch (error) {
        console.error('Erro no backup:', error);
        await registrarBackup('manual', 'erro');
        showNotification('Erro ao fazer backup: ' + error.message, 'error');
      }
    }
  </script>
</body>
</html>