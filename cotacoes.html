<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cotações</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1200px;
      margin: 30px auto;
      padding: 0;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    .quotations-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .quotations-table th,
    .quotations-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .quotations-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
    }

    .quotations-table tr:hover {
      background-color: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .status-aberta { background-color: #ffc107; color: #000; }
    .status-enviada { background-color: #17a2b8; color: #fff; }
    .status-respondida { background-color: #28a745; color: #fff; }
    .status-fechada { background-color: #6c757d; color: #fff; }
    .status-aprovada { background-color: #28a745; color: #fff; }
    .status-badge.status-cancelado { background-color: var(--danger-color); color: white; }
    .status-badge.status-rejeitada { 
    background-color: #d32f2f;
    color: white;
    border: 2px solid #b71c1c;
}

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 20px;
      width: 80%;
      max-width: 1000px;
      border-radius: 8px;
      position: relative;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .comparison-table th,
    .comparison-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .comparison-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
    }

    .supplier-section {
      margin-top: 20px;
      padding: 15px;
      background-color: var(--secondary-color);
      border-radius: 4px;
      border: 1px solid var(--border-color);
    }

    .supplier-info {
      display: flex;
      gap: 20px;
      margin-bottom: 15px;
    }

    .supplier-response {
      margin-top: 15px;
      padding: 15px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border: 1px solid var(--border-color);
    }

    .approval-section {
      margin-top: 20px;
      padding: 15px;
      background-color: var(--secondary-color);
      border-radius: 4px;
      border: 1px solid var(--border-color);
    }

    .approval-level {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin-bottom: 10px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid var(--border-color);
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .access-denied {
      text-align: center;
      padding: 50px 20px;
      background-color: #f8d7da;
      color: #721c24;
      border-radius: 8px;
      margin: 50px auto;
      max-width: 600px;
      border: 1px solid #f5c6cb;
    }

    .supplier-list {
      margin: 10px 0;
      padding: 10px;
      background-color: var(--secondary-color);
      border-radius: 4px;
      border: 1px solid var(--border-color);
    }

    .supplier-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .supplier-item:last-child {
      border-bottom: none;
    }

    .supplier-info {
      flex: 1;
    }

    .supplier-actions {
      display: flex;
      gap: 5px;
    }

    .copy-link-btn {
      background-color: #17a2b8;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }

    .remove-supplier-btn {
      background-color: var(--danger-color);
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }

    .supplier-select {
      width: 100%;
      padding: 8px 10px;
      margin-bottom: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .response-link {
      font-size: 12px;
      color: var(--text-secondary);
      word-break: break-all;
    }

    .supplierTables {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .supplierTables table {
      width: 100%;
    }

    .item-selected {
      background-color: #e8f5e9;
    }

    .item-disabled {
      background-color: #f5f5f5;
      color: #999;
    }

    .item-disabled input[type="checkbox"] {
      pointer-events: none;
      opacity: 0.5;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
      justify-content: center; /* Center the buttons */
    }

    .action-buttons button {
      padding: 6px 12px;
      font-size: 12px;
    }

    .view-button {
      background-color: var(--primary-color);
      color: white;
    }

    .send-button {
      background-color: #17a2b8;
      color: white;
    }

    .approve-button {
      background-color: var(--success-color);
      color: white;
    }

    .required::after {
      content: "*";
      color: var(--danger-color);
      margin-left: 4px;
    }

    .sort-header {
      cursor: pointer;
    }

    .action-icon {
      padding: 6px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }

    .view-icon {
      background-color: var(--primary-color);
      color: white;
    }

    .send-icon {
      background-color: #17a2b8;
      color: white;
    }

    .approve-icon {
      background-color: var(--success-color);
      color: white;
    }

    .response-indicators {
      display: flex;
      gap: 5px;
      justify-content: center;
    }

    .response-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }

    .response-dot.responded {
      background-color: var(--success-color);
    }

    .response-dot.pending {
      background-color: #ccc;
      border: 1px solid #999;
    }

    @keyframes fadeInOut {
      0% { opacity: 0; transform: translateY(20px); }
      10% { opacity: 1; transform: translateY(0); }
      90% { opacity: 1; transform: translateY(0); }
      100% { opacity: 0; transform: translateY(-20px); }
    }

    .fa-link {
      transition: transform 0.2s;
    }

    .fa-link:hover {
      transform: scale(1.2);
    }

    /* Estilos de Paginação */
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
      gap: 10px;
    }

    .pagination button {
      padding: 8px 12px;
      border: 2px solid #e9ecef;
      background: white;
      color: #495057;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;
    }

    .pagination button:hover:not(:disabled) {
      border-color: var(--primary-color);
      color: var(--primary-color);
      background: #f8f9fa;
    }

    .pagination button.active {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }

    .pagination button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: #f8f9fa;
      color: #6c757d;
    }

    .pagination-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      padding: 10px 0;
      font-size: 14px;
      color: var(--text-secondary);
    }

    .items-per-page {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .items-per-page select {
      padding: 5px 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }
    .filter-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      overflow: hidden; /* Evita que elementos saiam da área */
    }

    .filter-section h3 {
      margin-bottom: 15px;
      color: #2c3e50;
      font-size: 1.1rem;
    }

    .filter-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-item {
      display: flex;
      flex-direction: column;
      min-width: 0; /* Evita overflow */
    }

    .filter-item label {
      margin-bottom: 5px;
      font-size: 0.9rem;
      color: #495057;
      font-weight: 500;
    }

    .filter-item input,
    .filter-item select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 0.9rem;
      background-color: white;
    }

    .filter-buttons {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      flex-wrap: wrap; /* Permite que os botões quebrem linha se necessário */
    }

    /* Ajustes para telas pequenas */
    @media (max-width: 768px) {
      .filter-grid {
        grid-template-columns: 1fr;
      }

      .filter-buttons {
        justify-content: center;
      }
    }

  </style>
</head>
<body>
  <div id="mainContainer" class="container" style="display: none;">
    <div class="header">
      <h1>Cotações</h1>
      <div>
        <button onclick="generateQuotationReport()" class="btn-secondary">Relatório</button>
        <button onclick="exportQuotations()" class="btn-secondary">Exportar</button>
        <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
      </div>
    </div>

    <!-- Dashboard de Cotações -->
    <div class="dashboard-section" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; padding: 15px; background-color: var(--secondary-color); border-radius: 8px;">
      <div class="dashboard-card" style="background: white; padding: 15px; border-radius: 4px; text-align: center; border: 1px solid var(--border-color);">
        <h3 style="margin: 0 0 10px 0; color: var(--primary-color); font-size: 14px;">Total de Cotações</h3>
        <div id="totalCotacoes" style="font-size: 24px; font-weight: bold; color: var(--text-color);">0</div>
      </div>
      <div class="dashboard-card" style="background: white; padding: 15px; border-radius: 4px; text-align: center; border: 1px solid var(--border-color);">
        <h3 style="margin: 0 0 10px 0; color: var(--primary-color); font-size: 14px;">Abertas</h3>
        <div id="cotacoesAbertas" style="font-size: 24px; font-weight: bold; color: #ffc107;">0</div>
      </div>
      <div class="dashboard-card" style="background: white; padding: 15px; border-radius: 4px; text-align: center; border: 1px solid var(--border-color);">
        <h3 style="margin: 0 0 10px 0; color: var(--primary-color); font-size: 14px;">Enviadas</h3>
        <div id="cotacoesEnviadas" style="font-size: 24px; font-weight: bold; color: #17a2b8;">0</div>
      </div>
      <div class="dashboard-card" style="background: white; padding: 15px; border-radius: 4px; text-align: center; border: 1px solid var(--border-color);">
        <h3 style="margin: 0 0 10px 0; color: var(--primary-color); font-size: 14px;">Respondidas</h3>
        <div id="cotacoesRespondidas" style="font-size: 24px; font-weight: bold; color: #28a745;">0</div>
      </div>
      <div class="dashboard-card" style="background: white; padding: 15px; border-radius: 4px; text-align: center; border: 1px solid var(--border-color);">
        <h3 style="margin: 0 0 10px 0; color: var(--primary-color); font-size: 14px;">Valor Médio</h3>
        <div id="valorMedio" style="font-size: 24px; font-weight: bold; color: var(--text-color);">R$ 0,00</div>
      </div>
      <div class="dashboard-card" style="background: white; padding: 15px; border-radius: 4px; text-align: center; border: 1px solid var(--border-color);">
        <h3 style="margin: 0 0 10px 0; color: var(--primary-color); font-size: 14px;">Tempo Médio Resposta</h3>
        <div id="tempoMedioResposta" style="font-size: 24px; font-weight: bold; color: var(--text-color);">0 dias</div>
      </div>
    </div>

    <div class="filter-section">
      <h3>Filtros</h3>
      <div class="filter-grid">
  <div class="filter-item">
    <label for="filtroAglutinados">Exibir Aglutinados:</label>
    <select id="filtroAglutinados">
      <option value="todos">Todos</option>
      <option value="apenas">Apenas aglutinados</option>
      <option value="nao" selected>Ocultar aglutinados</option>
    </select>
  </div>
  <div class="filter-item">
    <label for="filtroFechados">Exibir Fechados/Cancelados:</label>
    <select id="filtroFechados">
      <option value="todos">Todos</option>
      <option value="apenas">Apenas fechados/cancelados</option>
      <option value="nao" selected>Ocultar fechados/cancelados</option>
    </select>
  </div>
        <div class="filter-item">
          <label for="filtroStatus">Status:</label>
          <select id="filtroStatus">
            <option value="">Todos</option>
            <option value="ABERTA">Aberta</option>
            <option value="ENVIADA">Enviada</option>
            <option value="RESPONDIDA">Respondida</option>
            <option value="APROVADA">Aprovada</option>
            <option value="FECHADA">Fechada</option>
            <option value="RECUSADA">Recusada</option>
          </select>
        </div>

        <div class="filter-item">
          <label for="filtroDataInicial">Data Inicial:</label>
          <input type="date" id="filtroDataInicial" placeholder="dd/mm/aaaa">
        </div>

        <div class="filter-item">
          <label for="filtroDataFinal">Data Final:</label>
          <input type="date" id="filtroDataFinal" placeholder="dd/mm/aaaa">
        </div>

        <div class="filter-item">
          <label for="filtroFornecedor">Fornecedor:</label>
          <select id="filtroFornecedor">
            <option value="">Todos</option>
            <!-- Opções de fornecedores serão preenchidas dinamicamente -->
          </select>
        </div>
      </div>

      <div class="filter-buttons">
        <button onclick="aplicarFiltrosCotacoes()" class="btn-primary">Filtrar</button>
        <button onclick="limparFiltrosCotacoes()" class="btn-secondary">Limpar</button>
        <button onclick="aglutinacaoModal()" class="btn-success">Aglutinar Selecionadas</button>
      </div>
    </div>

    <table class="quotations-table">
      <thead>
        <tr>
          <th><input type="checkbox" id="selectAllCotacoes" onclick="toggleSelectAllCotacoes(this)"></th>
<th class="sort-header" onclick="sortTable('numero')">Número</th>
          <th class="sort-header" onclick="sortTable('data')">Data</th>
          <th class="sort-header" onclick="sortTable('solicitacao')">Solicitação</th>
          <th class="sort-header" onclick="sortTable('fornecedores')">Fornecedores</th>
          <th>Respostas</th>
          <th class="sort-header" onclick="sortTable('valorTotal')">Valor Total</th>
          <th class="sort-header" onclick="sortTable('status')" style="width: 120px">Status</th>
          <th style="width: 180px; text-align: center;">Ações</th>
        </tr>
      </thead>
      <tbody id="quotationsTableBody">
<!-- Linhas serão inseridas dinamicamente -->
      </tbody>
    </table>

    <!-- Paginação -->
    <div class="pagination-info">
      <div class="items-per-page">
        <label>Itens por página:</label>
        <select id="itemsPerPage" onchange="changeItemsPerPage()">
          <option value="10">10</option>
          <option value="20" selected>20</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
      </div>
      <div id="paginationInfo">
        Mostrando 0 de 0 cotações
      </div>
    </div>

    <div class="pagination" id="pagination">
      <!-- Controles de paginação serão inseridos aqui -->
    </div>
    <style>
      .quotations-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      .quotations-table th,
      .quotations-table td {
        padding: 12px;
        text-align: left;
        border: 1px solid #ddd;
      }
      .quotations-table th {
        background-color: #f8f9fa;
      }
      .quotations-table tr:hover {
        background-color: #f5f5f5;
      }
    </style>
  </div>

  <div id="accessDenied" class="access-denied" style="display: none;">
    <h2>Acesso Negado</h2>
    <p>Você não possui permissão para acessar esta funcionalidade.</p>
    <p>Entre em contato com o administrador do sistema para solicitar acesso.</p>
    <button onclick="window.location.href='index.html'" class="back-button">Voltar para o Menu</button>
  </div>

  <div id="quotationModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('quotationModal')">&times;</span>
      <h2>Detalhes da Cotação</h2>

      <!-- Seção de Itens da Cotação -->
      <div class="items-section" style="margin-bottom: 30px; padding: 15px; background-color: var(--secondary-color); border-radius: 4px; border: 1px solid var(--border-color);">
        <h3 style="margin-bottom: 15px; color: var(--primary-color);">Itens da Cotação</h3>
        <div id="cotacaoItemsTable">
          <!-- Tabela de itens será inserida aqui dinamicamente -->
        </div>
      </div>

      <div class="supplier-section">
        <h3>Fornecedores</h3>
        
        <!-- Interface melhorada para adicionar fornecedores -->
        <div class="add-supplier-section" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px; border: 1px solid var(--border-color);">
          <h4 style="margin: 0 0 15px 0; color: var(--primary-color); font-size: 14px;">Adicionar Novo Fornecedor</h4>
          <div style="display: grid; grid-template-columns: 1fr auto; gap: 10px; align-items: end;">
            <div>
              <label style="display: block; margin-bottom: 5px; font-size: 12px; color: var(--text-secondary);">Selecione o fornecedor:</label>
              <select id="supplierSelect" class="supplier-select" style="margin: 0;">
                <option value="">Escolha um fornecedor...</option>
              </select>
              <div id="supplierPreview" style="margin-top: 8px; padding: 8px; background-color: white; border-radius: 4px; font-size: 12px; color: var(--text-secondary); min-height: 20px; display: none;">
                <!-- Preview do fornecedor selecionado -->
              </div>
            </div>
            <button id="addSupplierBtn" onclick="addSupplierQuick()" class="btn-primary" style="height: 36px; white-space: nowrap;">
              <i class="fas fa-plus"></i> Adicionar
            </button>
          </div>
          <div id="addSupplierMessage" style="margin-top: 10px; font-size: 12px; display: none;"></div>
        </div>

        <div class="supplier-list" id="suppliersList"></div>
      </div>

      <div class="comparison-section">
        <h3>Comparativo de Preços por Fornecedor</h3>
        <div id="supplierTables"></div>
      </div>

      <div class="approval-section">
        <h3>Aprovações Necessárias</h3>
        <div class="approval-level">
          <div>
            <strong>Nível 1 (até R$ 1.000)</strong>
            <div>Supervisor de Compras</div>
          </div>
          <span class="status-badge status-pendente">Pendente</span>
        </div>
        <div class="approval-level">
          <div>
            <strong>Nível 2 (até R$ 10.000)</strong>
            <div>Gerente de Compras</div>
          </div>
          <span class="status-badge status-pendente">Pendente</span>
        </div>
        <div class="approval-level">
          <div>
            <strong>Nível 3 (acima de R$ 10.000)</strong>
            <div>Diretor</div>
          </div>
          <span class="status-badge status-pendente">Pendente</span>
        </div>
      </div>

      <div style="margin-top: 20px;">
        <button id="approveBtn" onclick="approveQuotation()" class="btn-success" style="display: none;">Aprovar Cotação</button>
        <button id="generateOrderBtn" onclick="generatePurchaseOrder()" class="btn-primary" style="display: none;">Gerar Pedido</button>
        <button id="saveBtn" onclick="saveQuotation()" class="btn-primary">Salvar</button>
        <button id="editBtn" onclick="editQuotation()" class="btn-secondary">Editar</button>
        <button id="deleteBtn" onclick="deleteQuotation()" class="btn-danger">Excluir</button>
        <button id="rejectBtn" onclick="rejectQuotation()" class="btn-danger" style="display: none;">Recusar Cotação</button>
      </div>
    </div>
  </div>

  <script type="module">
// Função para atualizar preço manual
window.handleManualPriceChange = function(input) {
  const fornecedorId = input.getAttribute('data-fornecedor');
  const itemIndex = input.getAttribute('data-item');
  const value = parseFloat(input.value) || 0;
  if (!window.manualPrices) window.manualPrices = {};
  if (!window.manualPrices[fornecedorId]) window.manualPrices[fornecedorId] = {};
  window.manualPrices[fornecedorId][itemIndex] = value;
  // Recalcular a tabela deste fornecedor
  if (window.currentQuotation) {
    updateSupplierTables(window.currentQuotation);
  }
};

    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      doc,
      getDoc,
      updateDoc,
      Timestamp,
      setDoc
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let fornecedores = [];
    let cotacoes = [];
    let solicitacoes = [];
    let currentQuotation = null;
    let currentUser = null;
    let userPermissions = [];
    let selectedItems = new Map();

    // Variáveis de paginação
    let currentPage = 1;
    let itemsPerPage = 20;
    let filteredCotacoes = [];
    let totalItems = 0;

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }

      currentUser = JSON.parse(userSession);

      await checkUserPermissions();

      await loadInitialData();
      await loadQuotations();
      updateDashboard();
      populateFornecedorFilter();
    };

    // Função para atualizar dashboard
    function updateDashboard() {
      const total = cotacoes.length;
      const abertas = cotacoes.filter(c => c.status === 'ABERTA').length;
      const enviadas = cotacoes.filter(c => c.status === 'ENVIADA').length;
      const respondidas = cotacoes.filter(c => c.status === 'RESPONDIDA').length;
      
      const valorTotal = cotacoes.reduce((sum, c) => sum + (c.valorTotal || 0), 0);
      const valorMedio = total > 0 ? valorTotal / total : 0;
      
      // Calcular tempo médio de resposta
      const cotacoesComResposta = cotacoes.filter(c => 
        c.status === 'RESPONDIDA' && c.dataEnvio && c.respostas
      );
      
      let tempoMedio = 0;
      if (cotacoesComResposta.length > 0) {
        const tempoTotal = cotacoesComResposta.reduce((sum, c) => {
          const dataEnvio = new Date(c.dataEnvio.seconds * 1000);
          const dataResposta = Object.values(c.respostas)[0]?.dataResposta;
          if (dataResposta) {
            const diffTime = new Date(dataResposta.seconds * 1000) - dataEnvio;
            return sum + (diffTime / (1000 * 60 * 60 * 24)); // dias
          }
          return sum;
        }, 0);
        tempoMedio = Math.round(tempoTotal / cotacoesComResposta.length);
      }
      
      document.getElementById('totalCotacoes').textContent = total;
      document.getElementById('cotacoesAbertas').textContent = abertas;
      document.getElementById('cotacoesEnviadas').textContent = enviadas;
      document.getElementById('cotacoesRespondidas').textContent = respondidas;
      document.getElementById('valorMedio').textContent = `R$ ${valorMedio.toFixed(2)}`;
      document.getElementById('tempoMedioResposta').textContent = `${tempoMedio} dias`;
    }

    // Função para popular filtro de fornecedores
    function populateFornecedorFilter() {
      const select = document.getElementById('filtroFornecedor');
      select.innerHTML = '<option value="">Todos</option>';
      
      fornecedores.forEach(fornecedor => {
        select.innerHTML += `<option value="${fornecedor.id}">${fornecedor.razaoSocial}</option>`;
      });
    }

    // Função para aplicar filtros
    window.aplicarFiltrosCotacoes = function() {
      const filtroNumeroEl = document.getElementById('filtroNumero');
      const filtroAglutinadosEl = document.getElementById('filtroAglutinados');
      const filtroFechadosEl = document.getElementById('filtroFechados');
      const filtros = {
        numero: filtroNumeroEl ? filtroNumeroEl.value.toLowerCase() : '',
        status: document.getElementById('filtroStatus').value,
        dataInicial: document.getElementById('filtroDataInicial').value,
        dataFinal: document.getElementById('filtroDataFinal').value,
        fornecedor: document.getElementById('filtroFornecedor').value,
        aglutinados: filtroAglutinadosEl ? filtroAglutinadosEl.value : 'todos',
        fechados: filtroFechadosEl ? filtroFechadosEl.value : 'todos'
      };
      
      const tableBody = document.getElementById('quotationsTableBody');
      tableBody.innerHTML = '';
      
      let cotacoesFiltradas = cotacoes.filter(cotacao => {
        // Filtro por número
        if (filtros.numero && !cotacao.numero.toLowerCase().includes(filtros.numero)) {
          return false;
        }
        
        // Filtro por status
        if (filtros.status && cotacao.status !== filtros.status) {
          return false;
        }
        
        // Filtro por data
        if (filtros.dataInicial || filtros.dataFinal) {
          const dataCotacao = new Date(cotacao.dataCriacao.seconds * 1000);
          
          if (filtros.dataInicial) {
            const dataInicial = new Date(filtros.dataInicial);
            if (dataCotacao < dataInicial) return false;
          }
          
          if (filtros.dataFinal) {
            const dataFinal = new Date(filtros.dataFinal);
            dataFinal.setHours(23, 59, 59);
            if (dataCotacao > dataFinal) return false;
          }
        }
        
        // Filtro por fornecedor
        if (filtros.fornecedor && !cotacao.fornecedores?.includes(filtros.fornecedor)) {
          return false;
        }
        
        return true;
      });
      
      // Renderizar cotações filtradas
      cotacoesFiltradas.forEach(cotacao => {
        const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
        
        let fornecedorPrincipalNome = 'N/A';
        if (solicitacao && solicitacao.fornecedorId) {
          const fornecedorPrincipal = fornecedores.find(f => f.id === solicitacao.fornecedorId);
          if (fornecedorPrincipal) {
            fornecedorPrincipalNome = fornecedorPrincipal.razaoSocial;
          }
        }

        const fornecedoresConvidadosNomes = cotacao.fornecedores?.map(f => 
          fornecedores.find(forn => forn.id === f)?.razaoSocial
        ).filter(Boolean).join(', ') || 'Nenhum convidado';

        const fornecedoresColuna = `${fornecedorPrincipalNome} (convidados: ${fornecedoresConvidadosNomes})`;

        const row = document.createElement('tr');
        row.ondblclick = () => viewQuotation(cotacao.id);
        row.innerHTML = `
          <td><input type="checkbox" class="selectCotacao" value="${cotacao.id}"></td>
          <td>${cotacao.numero}</td>
          <td>${new Date(cotacao.dataCriacao.seconds * 1000).toLocaleDateString()}</td>
          <td>${solicitacao?.numero || 'N/A'}</td>
          <td>${fornecedoresColuna}</td>
          <td>
            <div class="response-indicators">
              ${(cotacao.fornecedores || []).map(fornecedorId => {
                const hasResponse = cotacao.respostas?.[fornecedorId];
                return `<span class="response-dot ${hasResponse ? 'responded' : 'pending'}" 
                       title="${hasResponse ? 'Respondido' : 'Aguardando'}"></span>`;
              }).join('')}
            </div>
          </td>
          <td>R$ ${cotacao.valorTotal?.toFixed(2) || '0,00'}</td>
          <td><span class="status-badge status-${cotacao.status.toLowerCase()}">${cotacao.status}</span></td>
          <td style="text-align: center;">
            <div class="action-buttons" style="display: inline-flex; gap: 5px; justify-content: center;">
              <button class="action-icon view-icon" title="Detalhes" onclick="viewQuotation('${cotacao.id}')">👁️</button>
              ${cotacao.status === 'ABERTA' && (currentUser.nivel >= 2 || userPermissions.includes('enviar_cotacoes')) ? `
                <button class="action-icon send-icon" title="Enviar" onclick="sendQuotation('${cotacao.id}')">📤</button>
              ` : ''}
              ${cotacao.status === 'RESPONDIDA' && (currentUser.nivel >= 3 || userPermissions.includes('aprovar_cotacoes')) ? `
                <button class="action-icon approve-icon" title="Aprovar" onclick="approveQuotation('${cotacao.id}')">✓</button>
              ` : ''}
            </div>
          </td>
        `;
        tableBody.appendChild(row);
      });
      
      showToast(`${cotacoesFiltradas.length} cotação(ões) encontrada(s).`, 'success');
    };

    // Função para limpar filtros
    window.limparFiltrosCotacoes = function() {
      document.getElementById('filtroNumero').value = '';
      document.getElementById('filtroStatus').value = '';
      document.getElementById('filtroDataInicial').value = '';
      document.getElementById('filtroDataFinal').value = '';
      document.getElementById('filtroFornecedor').value = '';
      
      loadQuotations(); // Recarregar todas as cotações
    };

    // Função para exportar cotações
    window.exportQuotations = function() {
      try {
        const dados = cotacoes.map(cotacao => {
          const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
          
          return {
            'Número': cotacao.numero,
            'Data': new Date(cotacao.dataCriacao.seconds * 1000).toLocaleDateString(),
            'Solicitação': solicitacao?.numero || 'N/A',
            'Status': cotacao.status,
            'Fornecedores': cotacao.fornecedores?.length || 0,
            'Itens': cotacao.itens?.length || 0,
            'Valor Total': cotacao.valorTotal ? `R$ ${cotacao.valorTotal.toFixed(2)}` : 'R$ 0,00',
            'Data Envio': cotacao.dataEnvio ? new Date(cotacao.dataEnvio.seconds * 1000).toLocaleDateString() : '-',
            'Criado Por': cotacao.criadoPor || '-'
          };
        });
        
        exportToCSV(dados, 'cotacoes');
        showToast('Relatório exportado com sucesso!', 'success');
      } catch (error) {
        console.error('Erro ao exportar:', error);
        showToast('Erro ao exportar dados.', 'error');
      }
    };

    // Função para gerar relatório de cotações
    window.generateQuotationReport = function() {
      const reportWindow = window.open('', '_blank');
      
      const cotacoesPorStatus = cotacoes.reduce((acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1;
        return acc;
      }, {});
      
      const fornecedoresMaisAtivos = cotacoes.reduce((acc, c) => {
        if (c.fornecedores) {
          c.fornecedores.forEach(fId => {
            const fornecedor = fornecedores.find(f => f.id === fId);
            if (fornecedor) {
              acc[fornecedor.razaoSocial] = (acc[fornecedor.razaoSocial] || 0) + 1;
            }
          });
        }
        return acc;
      }, {});
      
      const reportHTML = `
        <html>
          <head>
            <title>Relatório de Cotações</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              h1 { color: #0854a0; }
              .section { margin-bottom: 30px; }
              table { width: 100%; border-collapse: collapse; margin-top: 10px; }
              th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
              th { background-color: #f4f4f4; }
              .chart { margin: 20px 0; }
            </style>
          </head>
          <body>
            <h1>Relatório de Cotações - ${new Date().toLocaleDateString()}</h1>
            
            <div class="section">
              <h2>Resumo Geral</h2>
              <p>Total de Cotações: ${cotacoes.length}</p>
              <p>Valor Total: R$ ${cotacoes.reduce((sum, c) => sum + (c.valorTotal || 0), 0).toFixed(2)}</p>
            </div>
            
            <div class="section">
              <h2>Cotações por Status</h2>
              <table>
                <tr><th>Status</th><th>Quantidade</th></tr>
                ${Object.entries(cotacoesPorStatus).map(([status, count]) => 
                  `<tr><td>${status}</td><td>${count}</td></tr>`
                ).join('')}
              </table>
            </div>
            
            <div class="section">
              <h2>Fornecedores Mais Ativos</h2>
              <table>
                <tr><th>Fornecedor</th><th>Cotações</th></tr>
                ${Object.entries(fornecedoresMaisAtivos)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 10)
                  .map(([fornecedor, count]) => 
                    `<tr><td>${fornecedor}</td><td>${count}</td></tr>`
                  ).join('')}
              </table>
            </div>
          </body>
        </html>
      `;
      
      reportWindow.document.write(reportHTML);
      reportWindow.document.close();
    };

    // Função auxiliar para exportar CSV
    function exportToCSV(data, filename) {
      if (!data || data.length === 0) return;
      
      const headers = Object.keys(data[0]);
      const csvContent = [
        headers.join(';'),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(';'))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
    }

    async function checkUserPermissions() {
      try {
        if (currentUser.nivel === 9) {
          document.getElementById('mainContainer').style.display = 'block';
          return;
        }

        const permissionsDoc = await getDoc(doc(db, "permissoes", currentUser.id));

        if (permissionsDoc.exists()) {
          userPermissions = permissionsDoc.data().permissoes || [];

          if (userPermissions.includes('cotacoes')) {
            document.getElementById('mainContainer').style.display = 'block';
            return;
          }
        }

        document.getElementById('accessDenied').style.display = 'block';
      } catch (error) {
        console.error("Erro ao verificar permissões:", error);
        alert("Erro ao verificar permissões. Por favor, tente novamente.");
      }
    }

    async function loadInitialData() {
      try {
        const [fornecedoresSnap, cotacoesSnap, solicitacoesSnap] = await Promise.all([
          getDocs(collection(db, "fornecedores")),
          getDocs(collection(db, "cotacoes")),
          getDocs(collection(db, "solicitacoesCompra"))
        ]);

        fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais.");
      }
    }

    async function loadQuotations() {
      const tableBody = document.getElementById('quotationsTableBody');
      tableBody.innerHTML = '';

      cotacoes
        .filter(cotacao => {
          // Filtro aglutinados
          const filtroAglut = document.getElementById('filtroAglutinados')?.value || 'todos';
          if (filtroAglut === 'apenas' && cotacao.status !== 'AGLUTINADO') return false;
          if (filtroAglut === 'nao' && cotacao.status === 'AGLUTINADO') return false;
          // Filtro fechados/cancelados
          const filtroFech = document.getElementById('filtroFechados')?.value || 'todos';
          if (filtroFech === 'apenas' && !['FECHADA','CANCELADO'].includes(cotacao.status)) return false;
          if (filtroFech === 'nao' && ['FECHADA','CANCELADO'].includes(cotacao.status)) return false;
          return true;
        })
        .sort((a, b) => b.dataCriacao.seconds - a.dataCriacao.seconds)
        .forEach(cotacao => {
          const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);

          let fornecedorPrincipalNome = 'N/A';
          if (solicitacao && solicitacao.fornecedorId) {
            const fornecedorPrincipal = fornecedores.find(f => f.id === solicitacao.fornecedorId);
            if (fornecedorPrincipal) {
              fornecedorPrincipalNome = fornecedorPrincipal.razaoSocial;
            }
          }

          const fornecedoresConvidadosNomes = cotacao.fornecedores?.map(f => 
            fornecedores.find(forn => forn.id === f)?.razaoSocial
          ).filter(Boolean).join(', ') || 'Nenhum convidado';

          const fornecedoresColuna = `${fornecedorPrincipalNome} (convidados: ${fornecedoresConvidadosNomes})`;

          const row = document.createElement('tr');
          row.ondblclick = () => viewQuotation(cotacao.id);
          row.innerHTML = `
            <td><input type="checkbox" class="selectCotacao" value="${cotacao.id}"></td>
            <td>${cotacao.numero}</td>
            <td>${new Date(cotacao.dataCriacao.seconds * 1000).toLocaleDateString()}</td>
            <td>${solicitacao?.numero || 'N/A'}</td>
            <td>${fornecedoresColuna}</td>
            <td>
              <div class="response-indicators">
                ${(cotacao.fornecedores || []).map(fornecedorId => {
                  const hasResponse = cotacao.respostas?.[fornecedorId];
                  return `<span class="response-dot ${hasResponse ? 'responded' : 'pending'}" 
                         title="${hasResponse ? 'Respondido' : 'Aguardando'}"></span>`;
                }).join('')}
              </div>
            </td>
            <td>R$ ${cotacao.valorTotal?.toFixed(2) || '0,00'}</td>
            <td><span class="status-badge status-${cotacao.status.toLowerCase()}">${cotacao.status}</span></td>
            <td style="text-align: center;">
              <div class="action-buttons" style="display: inline-flex; gap: 5px; justify-content: center;">
                <button class="action-icon view-icon" title="Detalhes" onclick="viewQuotation('${cotacao.id}')">👁️</button>
                ${cotacao.status === 'ABERTA' && (currentUser.nivel >= 2 || userPermissions.includes('enviar_cotacoes')) ? `
                  <button class="action-icon send-icon" title="Enviar" onclick="sendQuotation('${cotacao.id}')">📤</button>
                ` : ''}
                ${cotacao.status === 'RESPONDIDA' && (currentUser.nivel >= 3 || userPermissions.includes('aprovar_cotacoes')) ? `
                  <button class="action-icon approve-icon" title="Aprovar" onclick="approveQuotation('${cotacao.id}')">✓</button>
                ` : ''}
              </div>
            </td>
          `;
          tableBody.appendChild(row);
        });
    }

    window.viewQuotation = async function(quotationId) {
      const cotacao = cotacoes.find(c => c.id === quotationId);
      if (!cotacao) return;

      currentQuotation = cotacao;
      selectedItems.clear();

      const modal = document.getElementById('quotationModal');
      modal.dataset.cotacaoId = quotationId;

      // Remover seção de histórico anterior se existir
      const existingHistory = modal.querySelector('.history-section');
      if (existingHistory) {
        existingHistory.remove();
      }

      // Se a cotação foi aglutinadora, exibir os números das cotações originais
      const existingAglut = modal.querySelector('.aglut-section');
      if (existingAglut) existingAglut.remove();
      if (Array.isArray(cotacao.cotacoesAglutinadas) && cotacao.cotacoesAglutinadas.length > 0) {
        const aglutSection = document.createElement('div');
        aglutSection.className = 'aglut-section';
        aglutSection.style.cssText = 'margin-top: 20px; padding: 15px; background-color: #ffeaea; border-radius: 4px; border: 1px solid #bb0000; color: #bb0000;';
        aglutSection.innerHTML = `<h3>Cotações Aglutinadas</h3><div>Números das cotações originais:<br><b>${cotacao.cotacoesAglutinadas.join(', ')}</b></div>`;
        const actionButtons = modal.querySelector('.modal-content > div:last-child');
        if (actionButtons) {
          modal.querySelector('.modal-content').insertBefore(aglutSection, actionButtons);
        }
      }

      // Adicionar seção de histórico de alterações
      const historicoSection = document.createElement('div');
      historicoSection.className = 'history-section';
      historicoSection.style.cssText = 'margin-top: 20px; padding: 15px; background-color: var(--secondary-color); border-radius: 4px; border: 1px solid var(--border-color);';

      historicoSection.innerHTML = `
        <h3>Histórico de Alterações</h3>
        <div class="history-list">
          ${(cotacao.alteracoes || []).map(alteracao => {
            let mensagem = '';
            switch (alteracao.tipo) {
              case 'adicao':
                mensagem = `Item adicionado: ${alteracao.item.codigo} - ${alteracao.item.descricao} (${alteracao.item.quantidade} ${alteracao.item.unidade})`;
                break;
              case 'remocao':
                mensagem = `Item removido: ${alteracao.item.codigo} - ${alteracao.item.descricao}`;
                break;
              case 'modificacao':
                mensagem = `Quantidade alterada para ${alteracao.itemNovo.codigo}: ${alteracao.itemAntigo.quantidade} → ${alteracao.itemNovo.quantidade} ${alteracao.itemNovo.unidade}`;
                break;
            }
            return `
              <div class="history-item" style="margin: 10px 0; padding: 10px; background-color: white; border-radius: 4px; border: 1px solid var(--border-color);">
                <div style="color: #666; font-size: 0.9em;">
                  ${new Date(alteracao.data.seconds * 1000).toLocaleString()}
                </div>
                <div style="margin-top: 5px;">
                  ${mensagem}
                </div>
              </div>
            `;
          }).join('') || '<p>Nenhuma alteração registrada.</p>'}
        </div>
      `;

      // Inserir a seção de histórico antes dos botões de ação
      const actionButtons = modal.querySelector('.modal-content > div:last-child');
      if (actionButtons) {
        modal.querySelector('.modal-content').insertBefore(historicoSection, actionButtons);
      }

      // Mostrar itens da cotação primeiro
      showCotacaoItems(cotacao);
      
      updateSuppliersList(cotacao);
      await updateSupplierTables(cotacao);
      // Remover seção de fornecedor principal - não é mais utilizada
      const principalSupplierInfoDiv = document.getElementById('principalSupplierInfo');
      if (principalSupplierInfoDiv) {
        principalSupplierInfoDiv.style.display = 'none';
      }

      const canAddSupplier = currentUser.nivel >= 2 || userPermissions.includes('gerenciar_cotacoes');
      const canApprove = currentUser.nivel >= 3 || userPermissions.includes('aprovar_cotacoes');
      const canGenerateOrder = currentUser.nivel >= 3 || userPermissions.includes('gerar_pedidos');
      const canReject = currentUser.nivel >= 2 || userPermissions.includes('gerenciar_cotacoes');

      document.getElementById('addSupplierBtn').style.display = canAddSupplier ? 'inline-block' : 'none';
      document.getElementById('approveBtn').style.display = canApprove ? 'inline-block' : 'none';
      document.getElementById('generateOrderBtn').style.display = canGenerateOrder ? 'inline-block' : 'none';

      // Atualizar os botões de ação
      const actionButtonsDiv = modal.querySelector('.modal-content > div:last-child');
      actionButtonsDiv.innerHTML = `
        <button id="approveBtn" onclick="approveQuotation()" class="btn-success" style="display: ${canApprove ? 'inline-block' : 'none'}">Aprovar Cotação</button>
        <button id="generateOrderBtn" onclick="generatePurchaseOrder()" class="btn-primary" style="display: ${canGenerateOrder ? 'inline-block' : 'none'}">Gerar Pedido</button>
        <button id="saveBtn" onclick="saveQuotation()" class="btn-primary">Salvar</button>
        <button id="editBtn" onclick="editQuotation()" class="btn-secondary">Editar</button>
        <button id="deleteBtn" onclick="deleteQuotation()" class="btn-danger">Excluir</button>
        <button id="rejectBtn" onclick="rejectQuotation()" class="btn-danger" style="display: ${canReject ? 'inline-block' : 'none'}">Recusar Cotação</button>
      `;

      modal.style.display = 'block';
    };

    function updateSuppliersList(cotacao) {
      const suppliersList = document.getElementById('suppliersList');
      suppliersList.innerHTML = '';

      if (cotacao.fornecedores) {
        cotacao.fornecedores.forEach(fornecedorId => {
          const fornecedor = fornecedores.find(f => f.id === fornecedorId);
          if (fornecedor) {
            const div = document.createElement('div');
            div.className = 'supplier-item';

            const responseLink = generateResponseLink(cotacao.id, fornecedorId);
            const resposta = cotacao.respostas?.[fornecedorId];

            div.innerHTML = `
              <div class="supplier-info">
                <strong>${fornecedor.razaoSocial}</strong>
                <div>CNPJ: ${fornecedor.cnpj || 'N/A'}</div>
                <div>Email: ${fornecedor.email || 'N/A'}</div>
                <div>Telefone: ${fornecedor.telefone || 'N/A'}</div>
                <div style="display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-link" style="color: #17a2b8; cursor: pointer;" 
                     onclick="copyResponseLink('${responseLink}')" 
                     title="Copiar link de resposta"></i>
                  <span style="font-size: 12px; color: #666;">Link de resposta</span>
                </div>
                ${resposta ? `
                  <div style="color: #28a745;">
                    <strong>Resposta recebida em:</strong> 
                    ${new Date(resposta.dataResposta.seconds * 1000).toLocaleString()}
                    ${resposta.condicaoPagamento ? `<br>Condição de Pagamento: ${resposta.condicaoPagamento}` : ''}
                    ${resposta.prazoEntrega ? `<br>Prazo de Entrega: ${resposta.prazoEntrega}` : ''}
                  </div>
                ` : '<div style="color: #dc3545;">Aguardando resposta</div>'}
              </div>
              <div class="supplier-actions">
                <button class="remove-supplier-btn" onclick="removeSupplier('${cotacao.id}', '${fornecedorId}')">
                  Remover
                </button>
              </div>
            `;
            suppliersList.appendChild(div);
          }
        });
      }

      const supplierSelect = document.getElementById('supplierSelect');
      if (supplierSelect) {
        supplierSelect.innerHTML = '<option value="">Escolha um fornecedor...</option>';
        
        // Filtrar fornecedores disponíveis
        const fornecedoresDisponiveis = fornecedores.filter(f => {
          // Não incluir fornecedores já adicionados
          if (cotacao.fornecedores?.includes(f.id)) return false;
          
          // Só incluir fornecedores ativos
          if (!f.ativo) return false;
          
          return true;
        });

        fornecedoresDisponiveis.forEach(fornecedor => {
          const statusInfo = fornecedor.statusHomologacao ? ` (${fornecedor.statusHomologacao})` : '';
          supplierSelect.innerHTML += `
            <option value="${fornecedor.id}" 
                    data-cnpj="${fornecedor.cnpj || ''}"
                    data-email="${fornecedor.email || ''}"
                    data-status="${fornecedor.statusHomologacao || ''}">
              ${fornecedor.razaoSocial}${statusInfo}
            </option>`;
        });

        // Adicionar evento de mudança para mostrar preview
        supplierSelect.onchange = function() {
          showSupplierPreview(this);
        };

        // Limpar preview e mensagem
        hideSupplierPreview();
        const messageDiv = document.getElementById('addSupplierMessage');
        if (messageDiv) messageDiv.style.display = 'none';
      }
    }

    async function updateSupplierTables(cotacao) {
      const supplierTables = document.getElementById('supplierTables');
      
      // Limpar completamente o container
      supplierTables.innerHTML = '';
      
      // Limpar seleções existentes se a cotação mudou
      if (currentQuotation?.id !== cotacao.id) {
        selectedItems.clear();
      }

      // Buscar vínculos produto-fornecedor
      const vinculosSnap = await getDocs(collection(db, "produtos_fornecedores"));
      const vinculos = vinculosSnap.docs.map(doc => doc.data());

      if (!cotacao.fornecedores || cotacao.fornecedores.length === 0) {
        supplierTables.innerHTML = '<p>Nenhum fornecedor adicionado.</p>';
        return;
      }

      // Remover todos os containers de fornecedores existentes
      const existingContainers = document.querySelectorAll('.supplier-table-container');
      existingContainers.forEach(container => container.remove());

      // Criar container para cada fornecedor de forma única
      const uniqueFornecedores = [...new Set(cotacao.fornecedores)]; // Remove duplicatas
      
      uniqueFornecedores.forEach((fornecedorId, fornecedorIndex) => {
        const fornecedor = fornecedores.find(f => f.id === fornecedorId);
        if (!fornecedor) return;

        // Verificar se já existe um container para este fornecedor
        if (document.getElementById(`supplier-container-${fornecedorId}`)) {
          return; // Pular se já existe
        }

        const resposta = cotacao.respostas?.[fornecedorId];
        
        // Criar div container para este fornecedor
        const supplierContainer = document.createElement('div');
        supplierContainer.className = 'supplier-table-container';
        supplierContainer.id = `supplier-container-${fornecedorId}`;
        supplierContainer.style.cssText = 'margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;';

        const table = document.createElement('table');
        table.className = 'comparison-table';
        table.id = `table-${fornecedorId}`;
        table.style.cssText = 'width: 100%; border-collapse: collapse; margin: 0;';
        
        // Header do fornecedor
        const headerRow = document.createElement('thead');
        headerRow.innerHTML = `
          <tr>
            <th colspan="6" style="background-color: ${resposta ? '#e8f5e9' : '#fff3e0'}; color: ${resposta ? '#2e7d32' : '#f57c00'}; padding: 15px; text-align: left; font-size: 16px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${fornecedor.razaoSocial}</span>
                <span style="font-size: 14px; padding: 4px 8px; border-radius: 4px; background: ${resposta ? '#2e7d32' : '#f57c00'}; color: white;">
                  ${resposta ? '✓ Respondido' : '⏳ Aguardando'}
                </span>
              </div>
              ${resposta?.dataResposta ? `<div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">Respondido em ${new Date(resposta.dataResposta.seconds * 1000).toLocaleDateString()}</div>` : ''}
            </th>
          </tr>
          <tr style="background-color: #f8f9fa;">
            <th style="width: 50px; padding: 12px; text-align: center; border: 1px solid #ddd;">Selecionar</th>
            <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Item</th>
            <th style="width: 100px; padding: 12px; text-align: center; border: 1px solid #ddd;">Quantidade</th>
            <th style="width: 80px; padding: 12px; text-align: center; border: 1px solid #ddd;">Unidade</th>
            <th style="width: 120px; padding: 12px; text-align: center; border: 1px solid #ddd;">Preço Unitário</th>
            <th style="width: 120px; padding: 12px; text-align: center; border: 1px solid #ddd;">Preço Total</th>
          </tr>
        `;

        // Body da tabela
        const tbody = document.createElement('tbody');
        tbody.id = `tbody-${fornecedorId}`;

        // Armazenar preços manuais temporários
        if (!window.manualPrices) window.manualPrices = {};
        if (!window.manualPrices[fornecedorId]) window.manualPrices[fornecedorId] = {};

        // Iterar sobre os itens da cotação de forma única
        const uniqueItems = cotacao.itens.filter((item, index, self) => 
          index === self.findIndex(i => i.produtoId === item.produtoId)
        );

        cotacao.itens.forEach((item, index) => {
          let preco = resposta?.precos?.[index] || 0;
let isManual = false;
// Se houver valor manual, usa ele
if (window.manualPrices && window.manualPrices[fornecedorId] && window.manualPrices[fornecedorId][index] !== undefined) {
  preco = parseFloat(window.manualPrices[fornecedorId][index]) || 0;
  isManual = true;
} else if (cotacao.precosManuais && cotacao.precosManuais[fornecedorId] && cotacao.precosManuais[fornecedorId][index]) {
  preco = parseFloat(cotacao.precosManuais[fornecedorId][index].preco) || 0;
  isManual = true;
}

          const precoTotal = preco * item.quantidade;
          const isSelected = selectedItems.get(index) === fornecedorId;
          const isDisabled = selectedItems.has(index) && !isSelected;
          const rowClass = isSelected ? 'item-selected' : (isDisabled ? 'item-disabled' : '');

          const row = document.createElement('tr');
          row.className = rowClass;
          row.dataset.itemIndex = index;
          row.dataset.fornecedorId = fornecedorId;
          row.style.cssText = 'border-bottom: 1px solid #eee;';
          
          row.innerHTML = `
            <td style="text-align: center; padding: 12px; border: 1px solid #ddd;">
              <input type="checkbox" 
                     id="checkbox-${fornecedorId}-${index}"
                     ${isSelected ? 'checked' : ''} 
                     ${isDisabled ? 'disabled' : ''} 
                     data-fornecedor="${fornecedorId}" 
                     data-item="${index}" 
                     onchange="handleItemSelection(this)"
                     style="transform: scale(1.2);">
            </td>
            <td title="${item.descricao}" style="padding: 12px; border: 1px solid #ddd;">
              <div style="font-weight: bold; color: #333;">${item.codigo}</div>
              <div style="font-size: 0.9em; color: #666; margin-top: 2px;">${item.descricao}</div>
            </td>
            <td style="text-align: right; padding: 12px; border: 1px solid #ddd;">
              ${parseFloat(item.quantidade).toLocaleString('pt-BR', { 
                minimumFractionDigits: 3, 
                maximumFractionDigits: 3 
              })}
            </td>
            <td style="text-align: center; padding: 12px; border: 1px solid #ddd;">${item.unidade}</td>
            <td style="text-align: right; padding: 12px; border: 1px solid #ddd;">
  <input type="number" min="0" step="0.01"
  value="${preco}"
  style="width: 90px; text-align: right; padding: 4px; border-radius: 4px; border: 1px solid #ccc;${!isSelected ? 'background:#f5f5f5;' : ''}"
  data-fornecedor="${fornecedorId}" data-item="${index}"
  onchange="handleManualPriceChange(this)" ${(window.reviewMode && isSelected) ? '' : 'readonly'}>
${isManual ? '<span style="display:inline-block;margin-left:4px;padding:2px 6px;font-size:0.85em;background:#ffe9b0;color:#b8860b;border-radius:3px;vertical-align:middle;">Manual</span>' : ''}
</td>
            <td style="text-align: right; font-weight: bold; padding: 12px; border: 1px solid #ddd; ${!resposta ? 'color: #999;' : ''}">
  ''
  ${resposta ? `R$ ${precoTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}` : 'Aguardando'}
</td>
          `;

          tbody.appendChild(row);
        });

        // Footer da tabela
        const tfoot = document.createElement('tfoot');
        tfoot.innerHTML = `
          <tr style="background-color: #f5f5f5;">
            <td colspan="5" style="text-align: right; font-weight: bold; padding: 12px; border: 1px solid #ddd;">Total Selecionado:</td>
            <td id="total-${fornecedorId}" style="text-align: right; font-weight: bold; color: #0854a0; padding: 12px; border: 1px solid #ddd; font-size: 16px;">R$ 0,00</td>
          </tr>
          ${resposta?.condicaoPagamento || resposta?.prazoEntrega ? `
          <tr style="background-color: #f8f9fa;">
            <td colspan="6" style="font-size: 0.9em; color: #666; padding: 12px; border: 1px solid #ddd;">
              ${resposta.condicaoPagamento ? `<strong>Condição de Pagamento:</strong> ${resposta.condicaoPagamento}` : ''}
              ${resposta.condicaoPagamento && resposta.prazoEntrega ? '<br>' : ''}
              ${resposta.prazoEntrega ? `<strong>Prazo de Entrega:</strong> ${resposta.prazoEntrega}` : ''}
            </td>
          </tr>
          ` : ''}
        `;

        table.appendChild(headerRow);
        table.appendChild(tbody);
        table.appendChild(tfoot);
        
        supplierContainer.appendChild(table);
        supplierTables.appendChild(supplierContainer);
      });

      
window.reviewMode = window.reviewMode || false;
// Atualizar totais baseado nas seleções anteriores
updateAllTotals();
    }

    window.handleItemSelection = function(checkbox) {
      const itemIndex = parseInt(checkbox.dataset.item);
      const fornecedorId = checkbox.dataset.fornecedor;

      if (checkbox.checked) {
        // Se outro fornecedor estava selecionado para este item, desmarcar
        if (selectedItems.has(itemIndex)) {
          const previousFornecedorId = selectedItems.get(itemIndex);
          if (previousFornecedorId !== fornecedorId) {
            const previousCheckbox = document.querySelector(`#checkbox-${previousFornecedorId}-${itemIndex}`);
            if (previousCheckbox) {
              previousCheckbox.checked = false;
              // Remover classe de seleção da linha anterior
              const previousRow = previousCheckbox.closest('tr');
              if (previousRow) {
                previousRow.classList.remove('item-selected');
              }
            }
          }
        }
        selectedItems.set(itemIndex, fornecedorId);
        
        // Adicionar classe de seleção à linha atual
        const currentRow = checkbox.closest('tr');
        if (currentRow) {
          currentRow.classList.add('item-selected');
        }
      } else {
        selectedItems.delete(itemIndex);
        
        // Remover classe de seleção da linha atual
        const currentRow = checkbox.closest('tr');
        if (currentRow) {
          currentRow.classList.remove('item-selected');
        }
      }

      // Atualizar status de todos os checkboxes para este item
      updateItemCheckboxes(itemIndex);
      
      // Atualizar totais
      updateAllTotals();
    };

    // Nova função para atualizar estado dos checkboxes
    function updateItemCheckboxes(itemIndex) {
      const selectedFornecedorId = selectedItems.get(itemIndex);
      
      // Encontrar todos os checkboxes para este item
      const allCheckboxes = document.querySelectorAll(`input[data-item="${itemIndex}"]`);
      
      allCheckboxes.forEach(checkbox => {
        const fornecedorId = checkbox.dataset.fornecedor;
        const row = checkbox.closest('tr');
        
        if (selectedFornecedorId && selectedFornecedorId !== fornecedorId) {
          // Desabilitar e desmarcar outros fornecedores
          checkbox.disabled = true;
          checkbox.checked = false;
          if (row) {
            row.classList.add('item-disabled');
            row.classList.remove('item-selected');
          }
        } else {
          // Habilitar o fornecedor selecionado ou todos se nenhum estiver selecionado
          checkbox.disabled = false;
          if (row) {
            row.classList.remove('item-disabled');
            if (selectedFornecedorId === fornecedorId) {
              row.classList.add('item-selected');
            } else {
              row.classList.remove('item-selected');
            }
          }
        }
      });
    }

    function updateAllTotals() {
      if (!currentQuotation || !currentQuotation.fornecedores) return;

      currentQuotation.fornecedores.forEach(fornecedorId => {
        const resposta = currentQuotation.respostas?.[fornecedorId];
        let total = 0;

        selectedItems.forEach((selectedFornecedorId, itemIndex) => {
  if (selectedFornecedorId === fornecedorId) {
    let preco = 0;
    // Preço manual tem prioridade
    if (window.manualPrices && window.manualPrices[fornecedorId] && window.manualPrices[fornecedorId][itemIndex] !== undefined) {
      preco = parseFloat(window.manualPrices[fornecedorId][itemIndex]) || 0;
    } else if (resposta && resposta.precos) {
      preco = resposta.precos[itemIndex] || 0;
    }
    const quantidade = currentQuotation.itens[itemIndex]?.quantidade || 0;
    total += preco * quantidade;
  }
});

        const totalElement = document.getElementById(`total-${fornecedorId}`);
        if (totalElement) {
          totalElement.textContent = `R$ ${total.toLocaleString('pt-BR', { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
          })}`;
          
          // Adicionar destaque se este fornecedor tem itens selecionados
          const hasSelectedItems = Array.from(selectedItems.values()).includes(fornecedorId);
          if (hasSelectedItems) {
            totalElement.style.color = '#0854a0';
            totalElement.style.fontWeight = 'bold';
          } else {
            totalElement.style.color = '#666';
            totalElement.style.fontWeight = 'normal';
          }
        }
      });

      // Atualizar contador de itens selecionados
      updateSelectionSummary();
    }

    // Nova função para mostrar resumo da seleção
    function updateSelectionSummary() {
      let summaryElement = document.getElementById('selectionSummary');
      
      if (!summaryElement) {
        summaryElement = document.createElement('div');
        summaryElement.id = 'selectionSummary';
        summaryElement.style.cssText = `
          margin-top: 20px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 4px;
          border: 1px solid #dee2e6;
        `;
        
        const supplierTables = document.getElementById('supplierTables');
        if (supplierTables) {
          supplierTables.parentNode.insertBefore(summaryElement, supplierTables.nextSibling);
        }
      }

      const totalItems = currentQuotation?.itens?.length || 0;
      const selectedCount = selectedItems.size;
      const percentage = totalItems > 0 ? ((selectedCount / totalItems) * 100).toFixed(1) : 0;

      let totalSelecionado = 0;
      selectedItems.forEach((fornecedorId, itemIndex) => {
  let preco = 0;
  if (window.manualPrices && window.manualPrices[fornecedorId] && window.manualPrices[fornecedorId][itemIndex] !== undefined) {
    preco = parseFloat(window.manualPrices[fornecedorId][itemIndex]) || 0;
  } else {
    const resposta = currentQuotation.respostas?.[fornecedorId];
    if (resposta && resposta.precos) {
      preco = resposta.precos[itemIndex] || 0;
    }
  }
  const quantidade = currentQuotation.itens[itemIndex]?.quantidade || 0;
  totalSelecionado += preco * quantidade;
});

      summaryElement.innerHTML = `
        <h4 style="margin: 0 0 10px 0; color: #0854a0;">Resumo da Seleção</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
          <div>
            <strong>Itens Selecionados:</strong> ${selectedCount} de ${totalItems} (${percentage}%)
          </div>
          <div>
            <strong>Valor Total Selecionado:</strong> R$ ${totalSelecionado.toLocaleString('pt-BR', { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 2 
            })}
          </div>
        </div>
        ${selectedCount > 0 ? `
          <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
            ${selectedCount === totalItems ? '✅ Todos os itens foram selecionados' : '⚠️ Alguns itens ainda precisam ser selecionados'}
          </div>
        ` : ''}
      `;
    }

    window.generateResponseLink = function(cotacaoId, fornecedorId) {
      const baseUrl = window.location.origin;
      const link = `${baseUrl}/resposta_cotacao.html?cotacao=${cotacaoId}&fornecedor=${fornecedorId}`;

      // Adicionar timestamp para evitar cache
      const timestamp = new Date().getTime();
      return `${link}&t=${timestamp}`;
    };

    window.copyResponseLink = function(link) {
      navigator.clipboard.writeText(link).then(() => {
        // Criar e mostrar um toast de sucesso
        const toast = document.createElement('div');
        toast.style.cssText = `
          position: fixed;
          bottom: 20px;
          right: 20px;
          background-color: #28a745;
          color: white;
          padding: 10px 20px;
          border-radius: 4px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          z-index: 1000;
          animation: fadeInOut 2s ease-in-out;
        `;
        toast.textContent = 'Link copiado para a área de transferência!';
        document.body.appendChild(toast);

        // Remover o toast após 2 segundos
        setTimeout(() => {
          toast.remove();
        }, 2000);
      }).catch(err => {
        console.error('Erro ao copiar link:', err);
        alert('Erro ao copiar link. Por favor, copie manualmente.');
      });
    };

    window.removeSupplier = async function(cotacaoId, fornecedorId) {
      if (!confirm('Tem certeza que deseja remover este fornecedor da cotação?')) {
        return;
      }

      try {
        const cotacao = cotacoes.find(c => c.id === cotacaoId);
        if (!cotacao) return;

        const fornecedores = cotacao.fornecedores.filter(f => f !== fornecedorId);

        await updateDoc(doc(db, "cotacoes", cotacaoId), {
          fornecedores: fornecedores
        });

        if (cotacao.respostas?.[fornecedorId]) {
          const respostas = { ...cotacao.respostas };
          delete respostas[fornecedorId];
          await updateDoc(doc(db, "cotacoes", cotacaoId), { respostas });
        }

        // Remove any selections for this supplier
        selectedItems.forEach((selectedFornecedorId, itemIndex) => {
          if (selectedFornecedorId === fornecedorId) {
            selectedItems.delete(itemIndex);
          }
        });

        await loadInitialData();
        updateSuppliersList(cotacao);
        updateSupplierTables(cotacao);
        alert('Fornecedor removido com sucesso!');
      } catch (error) {
        console.error("Erro ao remover fornecedor:", error);
        alert("Erro ao remover fornecedor.");
      }
    };

    // Nova função para adicionar fornecedor sem modal adicional
    window.addSupplierQuick = async function() {
      if (currentUser.nivel < 2 && !userPermissions.includes('gerenciar_cotacoes')) {
        showSupplierMessage('Você não tem permissão para adicionar fornecedores à cotação.', 'error');
        return;
      }

      const cotacaoId = document.querySelector('#quotationModal').dataset.cotacaoId;
      const cotacao = cotacoes.find(c => c.id === cotacaoId);
      if (!cotacao) return;

      const select = document.getElementById('supplierSelect');
      const fornecedorId = select.value;

      if (!fornecedorId) {
        showSupplierMessage('Selecione um fornecedor primeiro.', 'warning');
        return;
      }

      try {
        showSupplierMessage('Adicionando fornecedor...', 'info');

        const fornecedores = new Set(cotacao.fornecedores || []);
        fornecedores.add(fornecedorId);

        // Atualizar a cotação com o novo fornecedor
        await updateDoc(doc(db, "cotacoes", cotacaoId), {
          fornecedores: Array.from(fornecedores),
          status: 'ABERTA' // Garantir que a cotação volte para aberta quando adicionar fornecedor
        });

        // Gerar link de resposta para o novo fornecedor
        const responseLink = generateResponseLink(cotacaoId, fornecedorId);
        const fornecedor = fornecedores.find(f => f.id === fornecedorId);

        // Atualizar a interface
        await loadInitialData();
        viewQuotation(cotacaoId);

        // Limpar seleção
        select.value = '';
        hideSupplierPreview();

        showSupplierMessage(`Fornecedor ${fornecedor?.razaoSocial || ''} adicionado com sucesso!`, 'success');

        // Copiar link automaticamente para área de transferência
        if (responseLink) {
          navigator.clipboard.writeText(responseLink).then(() => {
            setTimeout(() => {
              showSupplierMessage('Link de resposta copiado para área de transferência!', 'success');
            }, 1000);
          }).catch(() => {
            console.log('Não foi possível copiar o link automaticamente');
          });
        }
      } catch (error) {
        console.error("Erro ao adicionar fornecedor:", error);
        showSupplierMessage("Erro ao adicionar fornecedor.", 'error');
      }
    };

    // Função para mostrar mensagens na seção de adição
    function showSupplierMessage(message, type = 'info') {
      const messageDiv = document.getElementById('addSupplierMessage');
      if (!messageDiv) return;

      const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
      };

      messageDiv.style.display = 'block';
      messageDiv.style.color = colors[type] || colors.info;
      messageDiv.textContent = message;

      // Auto-hide após 3 segundos para mensagens de sucesso
      if (type === 'success') {
        setTimeout(() => {
          messageDiv.style.display = 'none';
        }, 3000);
      }
    }

    // Função para mostrar itens da cotação
    function showCotacaoItems(cotacao) {
      const itemsTableContainer = document.getElementById('cotacaoItemsTable');
      // Sempre limpar o container antes de adicionar os itens
      itemsTableContainer.innerHTML = '';

      if (!cotacao.itens || cotacao.itens.length === 0) {
        itemsTableContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary); font-style: italic;">Nenhum item encontrado nesta cotação.</p>';
        return;
      }

      // Calculate total suppliers for this quotation
      const totalFornecedores = cotacao.fornecedores ? cotacao.fornecedores.length : 0;

      // Inicializar objeto de preços manuais para edição
      if (!window.manualPricesEdit) window.manualPricesEdit = {};
      // Buscar solicitação para informações extras
      const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);

      let tableHTML = `
        <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
          <div style="font-size: 14px; color: var(--text-secondary);">
            <strong>Total de itens:</strong> ${cotacao.itens.length} | 
            <strong>Solicitação:</strong> ${solicitacao?.numero || 'N/A'} | 
            <strong>Data da cotação:</strong> ${new Date(cotacao.dataCriacao.seconds * 1000).toLocaleDateString()}
          </div>
        </div>
        <table style="width: 100%; border-collapse: collapse; background-color: white; border-radius: 4px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <thead>
            <tr style="background-color: var(--primary-color); color: white;">
              <th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; font-weight: 600;">Código</th>
              <th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; font-weight: 600;">Descrição</th>
              <th style="padding: 12px; text-align: right; border-bottom: 1px solid #ddd; font-weight: 600; width: 120px;">Quantidade</th>
              <th style="padding: 12px; text-align: center; border-bottom: 1px solid #ddd; font-weight: 600; width: 80px;">Unidade</th>
              <th style="padding: 12px; text-align: right; border-bottom: 1px solid #ddd; font-weight: 600; width: 110px;">Preço Unitário</th>
              <th style="padding: 12px; text-align: center; border-bottom: 1px solid #ddd; font-weight: 600; width: 100px;">Status</th>
            </tr>
          </thead>
          <tbody>
      `;

      cotacao.itens.forEach((item, index) => {
        // Verificar se há respostas para este item
        let hasResponse = false;
        let responseCount = 0;

        if (cotacao.respostas && cotacao.fornecedores) {
          cotacao.fornecedores.forEach(fornecedorId => {
            if (cotacao.respostas[fornecedorId] && cotacao.respostas[fornecedorId].precos && cotacao.respostas[fornecedorId].precos[index] !== undefined) {
              responseCount++;
              hasResponse = true;
            }
          });
        }

        // Preencher valor manual se existir
        if (cotacao.precosManuais && cotacao.precosManuais[index]) {
          window.manualPricesEdit[index] = cotacao.precosManuais[index].preco;
        }

        let statusBadge = '';
        let statusColor = '';

        if (totalFornecedores === 0) {
          statusBadge = 'Sem fornecedores';
          statusColor = '#999';
        } else if (responseCount === 0) {
          statusBadge = 'Aguardando';
          statusColor = '#ffc107';
        } else if (responseCount === totalFornecedores) {
          statusBadge = 'Completo';
          statusColor = '#28a745';
        } else {
          statusBadge = `${responseCount}/${totalFornecedores}`;
          statusColor = '#17a2b8';
        }

        const quantidadeFormatada = parseFloat(item.quantidade).toLocaleString('pt-BR', { 
          minimumFractionDigits: 3, 
          maximumFractionDigits: 3 
        });

        tableHTML += `
          <tr style="border-bottom: 1px solid #eee;" 
              onmouseover="this.style.backgroundColor='#f8f9fa'" 
              onmouseout="this.style.backgroundColor='white'">
            <td style="padding: 12px; font-weight: bold; color: var(--primary-color);">${item.codigo || 'N/A'}</td>
            <td style="padding: 12px;">
              <div style="color: var(--text-color); font-weight: 500;">${item.descricao || 'N/A'}</div>
              ${item.observacoes ? `<div style="font-size: 0.85em; color: var(--text-secondary); margin-top: 4px;">${item.observacoes}</div>` : ''}
            </td>
            <td style="padding: 12px; text-align: right; font-weight: 500;">${quantidadeFormatada}</td>
            <td style="padding: 12px; text-align: center; font-weight: 500;">${item.unidade || 'UN'}</td>
            <td style="padding: 12px; text-align: right;">
              <input type="number" min="0" step="0.01" style="width: 90px; text-align: right; padding: 4px; border-radius: 4px; border: 1px solid #ccc;" 
                value="${(window.manualPricesEdit && window.manualPricesEdit[index] !== undefined) ? window.manualPricesEdit[index] : (cotacao.precosManuais && cotacao.precosManuais[index]?.preco) || ''}" 
                onchange="handleManualPriceEdit(this, ${index})">
              ${(window.manualPricesEdit && window.manualPricesEdit[index] !== undefined) || (cotacao.precosManuais && cotacao.precosManuais[index]) ? '<span style="display:inline-block;margin-left:4px;padding:2px 6px;font-size:0.85em;background:#ffe9b0;color:#b8860b;border-radius:3px;vertical-align:middle;">Manual</span>' : ''}
            </td>
            <td style="padding: 12px; text-align: center;">
              <span style="padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold; background-color: ${statusColor}; color: white;">
                ${statusBadge}
              </span>
            </td>
          </tr>
        `;
      });

      tableHTML += `
          </tbody>
        </table>
      `;

      // Adicionar resumo da cotação
      const valorTotalEstimado = cotacao.valorTotal || 0;
      tableHTML += `
        <div style="margin-top: 15px; padding: 15px; background-color: #f8f9fa; border-radius: 4px; border: 1px solid var(--border-color);">
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
              <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">STATUS DA COTAÇÃO</div>
              <div style="font-weight: bold; color: ${cotacao.status === 'ABERTA' ? '#ffc107' : cotacao.status === 'ENVIADA' ? '#17a2b8' : cotacao.status === 'RESPONDIDA' ? '#28a745' : '#666'};">
                ${cotacao.status}
              </div>
            </div>
            <div>
              <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">FORNECEDORES CONVIDADOS</div>
              <div style="font-weight: bold; color: var(--text-color);">${totalFornecedores}</div>
            </div>
            <div>
              <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">VALOR ESTIMADO</div>
              <div style="font-weight: bold; color: var(--primary-color);">R$ ${valorTotalEstimado.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
            </div>
            ${cotacao.dataEnvio ? `
            <div>
              <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">DATA DE ENVIO</div>
              <div style="font-weight: bold; color: var(--text-color);">${new Date(cotacao.dataEnvio.seconds * 1000).toLocaleDateString()}</div>
            </div>
            ` : ''}
          </div>
          ${cotacao.observacoes ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid var(--border-color);">
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">OBSERVAÇÕES</div>
            <div style="color: var(--text-color);">${cotacao.observacoes}</div>
          </div>
          ` : ''}
        </div>
      `;

      itemsTableContainer.innerHTML = tableHTML;
    }

    // Função para mostrar preview do fornecedor selecionado
    window.showSupplierPreview = function(select) {
      const previewDiv = document.getElementById('supplierPreview');
      const messageDiv = document.getElementById('addSupplierMessage');
      
      if (!select.value) {
        hideSupplierPreview();
        messageDiv.style.display = 'none';
        return;
      }

      const fornecedor = fornecedores.find(f => f.id === select.value);
      if (!fornecedor) {
        hideSupplierPreview();
        return;
      }

      // Verificar se já está na cotação
      const cotacaoId = document.querySelector('#quotationModal').dataset.cotacaoId;
      const cotacao = cotacoes.find(c => c.id === cotacaoId);
      
      if (cotacao && cotacao.fornecedores?.includes(select.value)) {
        showSupplierMessage('Este fornecedor já está na cotação.', 'warning');
        previewDiv.style.display = 'none';
        return;
      }

      messageDiv.style.display = 'none';

      // Contar produtos vinculados
      const produtosVinculados = cotacao ? cotacao.itens.filter(item => {
        // Aqui você pode adicionar lógica para verificar vínculos produto-fornecedor
        return true; // Por enquanto, assume que todos são compatíveis
      }).length : 0;

      previewDiv.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <strong>${fornecedor.razaoSocial}</strong>
            <div style="color: #666; font-size: 11px;">
              CNPJ: ${fornecedor.cnpj || 'N/A'} | Email: ${fornecedor.email || 'N/A'}
            </div>
            ${fornecedor.statusHomologacao ? `
              <div style="font-size: 11px; color: ${fornecedor.statusHomologacao === 'Homologado' ? '#28a745' : '#ffc107'};">
                Status: ${fornecedor.statusHomologacao}
              </div>
            ` : ''}
          </div>
          <div style="text-align: right; font-size: 11px; color: #666;">
            ${produtosVinculados} itens<br>compatíveis
          </div>
        </div>
      `;
      previewDiv.style.display = 'block';
    };

    function hideSupplierPreview() {
      const previewDiv = document.getElementById('supplierPreview');
      if (previewDiv) {
        previewDiv.style.display = 'none';
      }
    }

    // Manter função original como fallback
    window.addSupplier = async function() {
      // Redirecionar para a nova função
      await addSupplierQuick();
    };

    window.confirmAddSupplier = async function(dialog) {
      const select = dialog.querySelector('select');
      const fornecedorId = select.value;

      if (!fornecedorId) {
        alert('Selecione um fornecedor.');
        return;
      }

      try {
        const cotacaoId = document.querySelector('#quotationModal').dataset.cotacaoId;
        const cotacao = cotacoes.find(c => c.id === cotacaoId);

        if (!cotacao) return;

        const fornecedores = new Set(cotacao.fornecedores || []);
        fornecedores.add(fornecedorId);

        // Atualizar a cotação com o novo fornecedor
        await updateDoc(doc(db, "cotacoes", cotacaoId), {
          fornecedores: Array.from(fornecedores),
          status: 'ABERTA' // Garantir que a cotação volte para aberta quando adicionar fornecedor
        });

        // Gerar link de resposta para o novo fornecedor
        const responseLink = generateResponseLink(cotacaoId, fornecedorId);

        // Atualizar a interface
        await loadInitialData();
        viewQuotation(cotacaoId);
        dialog.remove();

        // Mostrar o link gerado
        alert(`Fornecedor adicionado com sucesso!\nLink de resposta: ${responseLink}`);
      } catch (error) {
        console.error("Erro ao adicionar fornecedor:", error);
        alert("Erro ao adicionar fornecedor.");
      }
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
    };

    window.sendQuotation = async function(quotationId) {
      if (currentUser.nivel < 2 && !userPermissions.includes('enviar_cotacoes')) {
        showToast('Você não tem permissão para enviar cotações', 'error');
        return;
      }

      const cotacao = cotacoes.find(c => c.id === quotationId);
      if (!cotacao) return;

      // Verificar se há fornecedores
      if (!cotacao.fornecedores || cotacao.fornecedores.length === 0) {
        showToast('Adicione pelo menos um fornecedor antes de enviar a cotação', 'error');
        return;
      }

      // Verificar se há itens
      if (!cotacao.itens || cotacao.itens.length === 0) {
        showToast('A cotação não possui itens para enviar', 'error');
        return;
      }

      // Criar modal de confirmação
      const confirmModal = document.createElement('div');
      confirmModal.className = 'modal';

      // Preparar lista de fornecedores
      const fornecedoresList = cotacao.fornecedores.map(fornecedorId => {
        const fornecedor = fornecedores.find(f => f.id === fornecedorId);
        return fornecedor ? fornecedor.razaoSocial : 'Fornecedor não encontrado';
      }).join('\n');

      // Preparar lista de itens
      const itensList = cotacao.itens.map(item => 
        `${item.codigo} - ${item.descricao} (${item.quantidade} ${item.unidade})`
      ).join('\n');

      // Preparar prévia do email para o primeiro fornecedor
      const primeiroFornecedor = fornecedores.find(f => f.id === cotacao.fornecedores[0]);
      const responseLink = primeiroFornecedor ? generateResponseLink(cotacao.id, primeiroFornecedor.id) : '';

      const emailPreview = primeiroFornecedor ? `
        <div style="margin: 20px 0; padding: 20px; background-color: #f8f9fa; border-radius: 4px; border: 1px solid #ddd;">
          <h4>Prévia do Email (${primeiroFornecedor.razaoSocial})</h4>
          <div style="background-color: white; padding: 15px; border-radius: 4px; margin-top: 10px; font-family: Arial, sans-serif;">
            <h2 style="color: #0854a0;">Cotação ${cotacao.numero}</h2>
            <p>Prezado(a) ${primeiroFornecedor.razaoSocial},</p>
            <p>Você recebeu uma nova cotação para análise.</p>

            <h3 style="color: #0854a0;">Detalhes da Cotação:</h3>
            <ul style="list-style-type: none; padding-left: 0;">
              ${cotacao.itens.map(item => `
                <li style="margin-bottom: 8px;">
                  <strong>${item.codigo}</strong> - ${item.descricao}<br>
                  <span style="color: #666;">Quantidade: ${item.quantidade} ${item.unidade}</span>
                </li>
              `).join('')}
            </ul>

            <p style="margin-top: 20px;">
              <strong>Para responder esta cotação, acesse o link abaixo:</strong><br>
              <a href="${responseLink}" style="color: #0854a0; word-break: break-all;">${responseLink}</a>
            </p>

            ${cotacao.observacoes ? `
              <p style="margin-top: 20px;">
                <strong>Observações:</strong><br>
                ${cotacao.observacoes}
              </p>
            ` : ''}

            <p style="margin-top: 20px;">
              <strong>Importante:</strong><br>
              Este link é exclusivo para sua empresa e não deve ser compartilhado.
            </p>

            <p style="margin-top: 20px;">
              Atenciosamente,<br>
              Equipe de Compras
            </p>
          </div>
        </div>
      ` : '';

      confirmModal.innerHTML = `
        <div class="modal-content" style="width: 800px; max-height: 90vh; overflow-y: auto;">
          <h3>Confirmar Envio da Cotação</h3>
          <div style="margin: 20px 0;">
            <p><strong>Número da Cotação:</strong> ${cotacao.numero}</p>
            <p><strong>Data de Criação:</strong> ${new Date(cotacao.dataCriacao.seconds * 1000).toLocaleString()}</p>
          </div>

          <div style="margin: 20px 0;">
            <h4>Fornecedores que receberão a cotação:</h4>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
              ${cotacao.fornecedores.map(fornecedorId => {
                const fornecedor = fornecedores.find(f => f.id === fornecedorId);
                return fornecedor ? `
                  <div style="margin-bottom: 5px;">
                    <strong>${fornecedor.razaoSocial}</strong>
                    <div style="font-size: 0.9em; color: #666;">
                      Email: ${fornecedor.email || 'N/A'}<br>
                      Telefone: ${fornecedor.telefone || 'N/A'}
                    </div>
                  </div>
                ` : '';
              }).join('')}
            </div>
          </div>

          <div style="margin: 20px 0;">
            <h4>Itens da Cotação:</h4>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
              ${cotacao.itens.map(item => `
                <div style="margin-bottom: 5px;">
                  <strong>${item.codigo} - ${item.descricao}</strong>
                  <div style="font-size: 0.9em; color: #666;">
                    Quantidade: ${item.quantidade} ${item.unidade}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>

          ${emailPreview}

          <div style="margin: 20px 0;">
            <label>
              <input type="checkbox" id="confirmSend" style="margin-right: 5px;">
              Confirmo que todos os dados estão corretos e desejo enviar a cotação
            </label>
          </div>

          <div style="margin-top: 20px;">
            <button class="btn-primary" onclick="confirmSendQuotation('${quotationId}', this.closest('.modal'))" 
                    id="sendButton" disabled>
              Enviar Cotação
            </button>
            <button onclick="this.closest('.modal').remove()">Cancelar</button>
          </div>
        </div>
      `;

      document.body.appendChild(confirmModal);
      confirmModal.style.display = 'block';

      // Habilitar/desabilitar botão de envio baseado no checkbox
      confirmModal.querySelector('#confirmSend').addEventListener('change', function() {
        confirmModal.querySelector('#sendButton').disabled = !this.checked;
      });
    };

    window.confirmSendQuotation = async function(quotationId, dialog) {
      try {
        const cotacao = cotacoes.find(c => c.id === quotationId);
        if (!cotacao) return;

        // Mostrar indicador de carregamento
        const loadingToast = showToast('Enviando cotação...', 'info', true);

        // Atualizar status da cotação
        await updateDoc(doc(db, "cotacoes", quotationId), {
          status: 'ENVIADA',
          dataEnvio: Timestamp.now(),
          enviadoPor: currentUser.nome
        });

        // Buscar dados do solicitante
        const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
        const solicitanteId = solicitacao?.solicitanteId;

        // Processar cada fornecedor individualmente
        for (const fornecedorId of cotacao.fornecedores) {
          try {
            const fornecedor = fornecedores.find(f => f.id === fornecedorId);
            if (!fornecedor || !fornecedor.email) {
              console.warn(`Fornecedor ${fornecedorId} não encontrado ou sem email`);
              continue;
            }

            // Gerar link único para este fornecedor
            const responseLink = generateResponseLink(cotacao.id, fornecedorId);

            // Preparar o corpo do email específico para este fornecedor
            const emailBody = `
              <h2>Cotação ${cotacao.numero}</h2>
              <p>Prezado(a) ${fornecedor.razaoSocial},</p>
              <p>Você recebeu uma nova cotação para análise.</p>

              <h3>Detalhes da Cotação:</h3>
              <ul>
                ${cotacao.itens.map(item => `
                  <li>${item.codigo} - ${item.descricao} (${item.quantidade} ${item.unidade})</li>
                `).join('')}
              </ul>

              <p>Para responder esta cotação, acesse o link abaixo:</p>
              <p><a href="${responseLink}">${responseLink}</a></p>

              <p><strong>Importante:</strong><br>
              Este link é exclusivo para sua empresa e não deve ser compartilhado.</p>

              ${cotacao.observacoes ? `
                <p>Observações: ${cotacao.observacoes}</p>
              ` : ''}

              <p>Atenciosamente,<br>Equipe de Compras</p>
            `;

            // Criar documento de email específico para este fornecedor
            const emailDoc = {
              to: fornecedor.email,
              subject: `Cotação ${cotacao.numero} - ${fornecedor.razaoSocial}`,
              body: emailBody,
              status: 'PENDENTE',
              dataCriacao: Timestamp.now(),
              cotacaoId: cotacao.id,
              fornecedorId: fornecedor.id,
              fornecedorNome: fornecedor.razaoSocial
            };

            // Salvar o email na coleção
            const emailRef = await addDoc(collection(db, "emails"), emailDoc);

            // Registrar tentativa de envio específica para este fornecedor
            await addDoc(collection(db, "log_envios"), {
              cotacaoId: cotacao.id,
              fornecedorId: fornecedor.id,
              fornecedorEmail: fornecedor.email,
              fornecedorNome: fornecedor.razaoSocial,
              dataEnvio: Timestamp.now(),
              status: 'ENVIADO',
              tipo: 'EMAIL',
              emailId: emailRef.id
            });

            // Atualizar o toast com o progresso
            loadingToast.textContent = `Enviando cotação para ${fornecedor.razaoSocial}...`;

          } catch (error) {
            console.error(`Erro ao processar fornecedor ${fornecedorId}:`, error);
            // Continuar com o próximo fornecedor mesmo se houver erro
            continue;
          }
        }

        // Criar notificação para o solicitante
        if (solicitanteId) {
          const notificacao = {
            tipo: 'cotacao_enviada',
            cotacaoId: cotacao.id,
            cotacaoNumero: cotacao.numero,
            mensagem: `A cotação ${cotacao.numero} foi enviada para ${cotacao.fornecedores.length} fornecedor(es).`,
            data: Timestamp.now(),
            lida: false,
            destinatarioId: solicitanteId,
            remetenteId: currentUser.id,
            remetenteNome: currentUser.nome
          };

          await addDoc(collection(db, "notificacoes"), notificacao);
        }

        // Registrar log de envio geral
        await addDoc(collection(db, "log_cotacoes"), {
          cotacaoId: cotacao.id,
          cotacaoNumero: cotacao.numero,
          acao: 'ENVIO',
          data: Timestamp.now(),
          usuarioId: currentUser.id,
          usuarioNome: currentUser.nome,
          detalhes: {
            fornecedores: cotacao.fornecedores.length,
            itens: cotacao.itens.length
          }
        });

        dialog.remove();
        showToast('Cotação enviada com sucesso!', 'success');

        // Recarregar dados
        await loadInitialData();
        await loadQuotations();
      } catch (error) {
        console.error("Erro ao enviar cotação:", error);
        showToast('Erro ao enviar cotação', 'error');
      }
    };

    window.approveQuotation = async function(quotationId) {
      if (currentUser.nivel < 3 && !userPermissions.includes('aprovar_cotacoes')) {
        alert('Você não tem permissão para aprovar cotações.');
        return;
      }

      const cotacao = cotacoes.find(c => c.id === quotationId);
      if (!cotacao) return;

      let bestTotal = Infinity;
      let bestFornecedor = null;

      cotacao.fornecedores.forEach(fornecedorId => {
        const resposta = cotacao.respostas?.[fornecedorId];
        if (resposta?.precos) {
          const total = resposta.precos.reduce((sum, preco, idx) => 
            sum + (preco * cotacao.itens[idx].quantidade), 0);

          if (total > 0 && total < bestTotal) {
            bestTotal = total;
            bestFornecedor = fornecedorId;
          }
        }
      });

      if (!bestFornecedor) {
        alert('Não foi possível determinar o melhor fornecedor.');
        return;
      }

      if (confirm(`Confirma a aprovação da cotação para o fornecedor selecionado?\nValor Total: R$ ${bestTotal.toFixed(2)}`)) {
        try {
          await updateDoc(doc(db, "cotacoes", quotationId), {
            status: 'APROVADA',
            fornecedorAprovado: bestFornecedor,
            valorAprovado: bestTotal,
            dataAprovacao: Timestamp.now(),
            aprovadoPor: currentUser.nome
          });

          await loadInitialData();
          await loadQuotations();
          alert('Cotação aprovada com sucesso!');
        } catch (error) {
          console.error("Erro ao aprovar cotação:", error);
          alert("Erro ao aprovar cotação.");
        }
      }
    };

    window.generatePurchaseOrder = async function() {
      if (currentUser.nivel < 3 && !userPermissions.includes('gerar_pedidos')) {
        alert('Você não tem permissão para gerar pedidos de compra.');
        return;
      }

      if (selectedItems.size === 0) {
        alert('Selecione pelo menos um item para gerar o pedido.');
        return;
      }

      try {
        // Group items by supplier
        const itemsBySupplier = new Map();
        selectedItems.forEach((fornecedorId, itemIndex) => {
          if (!itemsBySupplier.has(fornecedorId)) {
            itemsBySupplier.set(fornecedorId, []);
          }
          itemsBySupplier.get(fornecedorId).push(itemIndex);
        });

        // Create purchase orders for each supplier
        for (const [fornecedorId, itemIndices] of itemsBySupplier) {
          const fornecedor = fornecedores.find(f => f.id === fornecedorId);
          const resposta = currentQuotation.respostas[fornecedorId];

          const itensSelecionados = itemIndices.map(itemIndex => {
            const item = currentQuotation.itens[itemIndex];
            return {
              ...item,
              valorUnitario: resposta.precos[itemIndex],
              valorTotal: resposta.precos[itemIndex] * item.quantidade
            };
          });

          const totalPedido = itensSelecionados.reduce((sum, item) => sum + item.valorTotal, 0);

          const numeroSequencial = (await getDocs(collection(db, "pedidosCompra"))).size + 1;
          const numeroPedido = numeroSequencial.toString().padStart(6, '0');

          const pedido = {
            numero: numeroPedido,
            cotacaoId: currentQuotation.id,
            fornecedorId: fornecedorId,
            itens: itensSelecionados,
            valorTotal: totalPedido,
            condicaoPagamento: resposta.condicaoPagamento,
            prazoEntrega: resposta.prazoEntrega,
            status: 'ABERTO',
            dataCriacao: Timestamp.now(),
            criadoPor: currentUser.nome
          };

          await addDoc(collection(db, "pedidosCompra"), pedido);
        }

        // Update quotation status
        await updateDoc(doc(db, "cotacoes", currentQuotation.id), {
          status: 'FECHADA'
        });

        alert('Pedidos de compra gerados com sucesso!');
        await loadInitialData();
        await loadQuotations();
        closeModal('quotationModal');
      } catch (error) {
        console.error("Erro ao gerar pedidos:", error);
        alert("Erro ao gerar pedidos.");
      }
    };

    function sortTable(column) {
      const tableBody = document.getElementById('quotationsTableBody');
      const rows = Array.from(tableBody.querySelectorAll('tr'));

      rows.sort((a, b) => {
        const aValue = getValueFromColumn(a, column);
        const bValue = getValueFromColumn(b, column);

        if (column === 'data') {
          return new Date(aValue) - new Date(bValue);
        } else if (column === 'valorTotal') {
          return parseFloat(aValue.replace('R$ ', '').replace(',', '.')) - parseFloat(bValue.replace('R$ ', '').replace(',', '.'));
        } else if (column === 'fornecedores') {
          return aValue.localeCompare(bValue);
        } else {
          return aValue.localeCompare(bValue);
        }
      });

      rows.forEach(row => tableBody.appendChild(row));
    }

    function getValueFromColumn(row, column) {
      const columnIndex = Array.from(row.querySelectorAll('td')).findIndex(cell => cell.closest('tr').querySelector(`th[onclick*="sortTable('${column}')"]`));
      return columnIndex !== -1 ? row.cells[columnIndex].textContent.trim() : '';
    }

    async function generateQuotationNumber() {
      try {
        const counterRef = doc(db, "contadores", "cotacoes");
        const counterDoc = await getDoc(counterRef);

        if (!counterDoc.exists()) {
          await setDoc(counterRef, { valor: 0 });
        }

        const nextNumber = (counterDoc.exists() ? counterDoc.data().valor : 0) + 1;
        await updateDoc(counterRef, { valor: nextNumber });

        const date = new Date();
        const year = date.getFullYear().toString().substr(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const sequence = nextNumber.toString().padStart(6, '0');

        const numero = `CT${year}${month}${sequence}`;
        console.log(`Número da cotação gerado: ${numero}`);
        return numero;
      } catch (error) {
        console.error("Erro ao gerar número da cotação:", error);
        throw error;
      }
    }

    window.saveQuotation = async function() {
  if (!currentQuotation) return;

  try {
    // Atualizar a cotação com os itens selecionados
    const selectedItemsData = {};
    selectedItems.forEach((fornecedorId, itemIndex) => {
      selectedItemsData[itemIndex] = fornecedorId;
    });

    // Preparar estrutura dos preços manuais para salvar
    let manualPricesToSave = {};
    if (window.manualPrices) {
      Object.keys(window.manualPrices).forEach(fornecedorId => {
        Object.keys(window.manualPrices[fornecedorId]).forEach(itemIndex => {
          const preco = window.manualPrices[fornecedorId][itemIndex];
          if (preco !== undefined && preco !== null && preco !== "") {
            if (!manualPricesToSave[fornecedorId]) manualPricesToSave[fornecedorId] = {};
            manualPricesToSave[fornecedorId][itemIndex] = {
              preco: parseFloat(preco),
              usuario: currentUser?.nome || '',
              data: new Date().toISOString()
            };
          }
        });
      });
    }

    // Atualizar cotação no Firestore
    await updateDoc(doc(db, "cotacoes", currentQuotation.id), {
      itensSelecionados: selectedItemsData,
      precosManuais: manualPricesToSave,
      ultimaAtualizacao: Timestamp.now(),
      atualizadoPor: currentUser.nome
    });

    showToast('Cotação salva com sucesso!', 'success');
    await loadInitialData();
    await loadQuotations();
  } catch (error) {
    console.error("Erro ao salvar cotação:", error);
    showToast('Erro ao salvar cotação', 'error');
  }
};

    window.editQuotation = async function() {
      if (!currentQuotation) return;

      // Verificar permissões
      if (currentUser.nivel < 2 && !userPermissions.includes('gerenciar_cotacoes')) {
        showToast('Você não tem permissão para editar cotações', 'error');
        return;
      }

      // Verificar se a cotação pode ser editada
      if (currentQuotation.status === 'FECHADA' || currentQuotation.status === 'APROVADA') {
        showToast('Não é possível editar uma cotação fechada ou aprovada', 'error');
        return;
      }

      try {
        // Buscar produtos para o select
        const produtosSnap = await getDocs(collection(db, "produtos"));
        const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Remover modal anterior se existir
        const existingModal = document.querySelector('.edit-quotation-modal');
        if (existingModal) {
          existingModal.remove();
        }

        // Abrir modal de edição
        const editModal = document.createElement('div');
        editModal.className = 'modal edit-quotation-modal';
        editModal.innerHTML = `
          <div class="modal-content" style="width: 900px; max-height: 90vh; overflow-y: auto;">
            <span class="close-button" onclick="closeEditModal()">&times;</span>
            <h3>Editar Cotação ${currentQuotation.numero}</h3>
            
            <div style="margin: 20px 0;">
              <label>Número da Cotação:</label>
              <input type="text" id="editNumero" value="${currentQuotation.numero}" readonly>
            </div>
            
            <div style="margin: 20px 0;">
              <h4>Itens da Cotação</h4>
              <div id="editItemsList">
                ${currentQuotation.itens.map((item, index) => `
                  <div class="edit-item" data-item-id="${item.produtoId || item.id || index}" style="margin-bottom: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;">
                    <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 15px; align-items: end;">
                      <div>
                        <label>Produto:</label>
                        <select class="item-select" data-index="${index}" onchange="updateItemDetails(this)" style="width: 100%; padding: 8px;">
                          <option value="">Selecione um produto...</option>
                          ${produtos.map(produto => `
                            <option value="${produto.id}" 
                              ${produto.id === item.produtoId ? 'selected' : ''}
                              data-codigo="${produto.codigo || ''}"
                              data-descricao="${produto.descricao || ''}"
                              data-unidade="${produto.unidade || 'UN'}">
                              ${produto.codigo} - ${produto.descricao}
                            </option>
                          `).join('')}
                        </select>
                      </div>
                      <div>
                        <label>Quantidade:</label>
                        <input type="number" class="item-quantity" data-index="${index}" 
                               value="${item.quantidade}" min="0.001" step="0.001" 
                               style="width: 100%; padding: 8px;">
                      </div>
                      <div>
                        <label>Unidade:</label>
                        <input type="text" class="item-unit" data-index="${index}" 
                               value="${item.unidade}" readonly style="width: 100%; padding: 8px; background-color: #f5f5f5;">
                      </div>
                      <div>
                        <button type="button" class="btn-danger" onclick="removeEditItem(${index})" style="padding: 8px 12px;">
                          Remover
                        </button>
                      </div>
                    </div>
                    <div class="item-details" style="margin-top: 10px; font-size: 0.9em; color: #666; padding: 8px; background-color: white; border-radius: 4px;">
                      Código: <span class="item-code">${item.codigo || 'N/A'}</span><br>
                      Descrição: <span class="item-description">${item.descricao || 'N/A'}</span>
                    </div>
                  </div>
                `).join('')}
              </div>
              <button type="button" class="btn-primary" onclick="addNewEditItem()" style="margin-top: 15px;">
                <i class="fas fa-plus"></i> Adicionar Novo Item
              </button>
            </div>
            
            <div style="margin: 20px 0;">
              <label>Observações:</label>
              <textarea id="editObservacoes" rows="4" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">${currentQuotation.observacoes || ''}</textarea>
            </div>
            
            <div style="margin-top: 30px; text-align: right; border-top: 1px solid #ddd; padding-top: 20px;">
              <button type="button" class="btn-primary" onclick="saveEditedQuotation()" style="margin-right: 10px;">
                <i class="fas fa-save"></i> Salvar Alterações
              </button>
              <button type="button" class="btn-secondary" onclick="closeEditModal()">
                <i class="fas fa-times"></i> Cancelar
              </button>
            </div>
          </div>
        `;
        
        document.body.appendChild(editModal);
        editModal.style.display = 'block';
        
        // Focar no primeiro campo editável
        setTimeout(() => {
          const firstSelect = editModal.querySelector('.item-select');
          if (firstSelect) firstSelect.focus();
        }, 100);
        
      } catch (error) {
        console.error("Erro ao abrir modal de edição:", error);
        showToast('Erro ao carregar modal de edição', 'error');
      }
    };

    window.updateItemDetails = function(select) {
      const option = select.options[select.selectedIndex];
      const itemDiv = select.closest('.edit-item');

      if (option && option.value) {
        const codeSpan = itemDiv.querySelector('.item-code');
        const descSpan = itemDiv.querySelector('.item-description');
        const unitInput = itemDiv.querySelector('.item-unit');
        
        if (codeSpan) codeSpan.textContent = option.dataset.codigo || 'N/A';
        if (descSpan) descSpan.textContent = option.dataset.descricao || 'N/A';
        if (unitInput) unitInput.value = option.dataset.unidade || 'UN';
      }
    };

    window.addNewEditItem = async function() {
      try {
        const itemsList = document.getElementById('editItemsList');
        if (!itemsList) return;

        // Buscar produtos atualizados
        const produtosSnap = await getDocs(collection(db, "produtos"));
        const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        const newIndex = itemsList.children.length;
        const newItemDiv = document.createElement('div');
        newItemDiv.className = 'edit-item';
        newItemDiv.dataset.itemId = `new-${newIndex}`;
        newItemDiv.style.cssText = 'margin-bottom: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;';

        newItemDiv.innerHTML = `
          <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 15px; align-items: end;">
            <div>
              <label>Produto:</label>
              <select class="item-select" data-index="${newIndex}" onchange="updateItemDetails(this)" style="width: 100%; padding: 8px;">
                <option value="">Selecione um produto...</option>
                ${produtos.map(produto => `
                  <option value="${produto.id}"
                    data-codigo="${produto.codigo || ''}"
                    data-descricao="${produto.descricao || ''}"
                    data-unidade="${produto.unidade || 'UN'}">
                    ${produto.codigo} - ${produto.descricao}
                  </option>
                `).join('')}
              </select>
            </div>
            <div>
              <label>Quantidade:</label>
              <input type="number" class="item-quantity" data-index="${newIndex}" 
                     value="1" min="0.001" step="0.001" style="width: 100%; padding: 8px;">
            </div>
            <div>
              <label>Unidade:</label>
              <input type="text" class="item-unit" data-index="${newIndex}" 
                     value="UN" readonly style="width: 100%; padding: 8px; background-color: #f5f5f5;">
            </div>
            <div>
              <button type="button" class="btn-danger" onclick="removeEditItem(${newIndex})" style="padding: 8px 12px;">
                Remover
              </button>
            </div>
          </div>
          <div class="item-details" style="margin-top: 10px; font-size: 0.9em; color: #666; padding: 8px; background-color: white; border-radius: 4px;">
            Código: <span class="item-code">N/A</span><br>
            Descrição: <span class="item-description">N/A</span>
          </div>
        `;

        itemsList.appendChild(newItemDiv);
        
        // Focar no select do novo item
        const newSelect = newItemDiv.querySelector('.item-select');
        if (newSelect) newSelect.focus();
        
      } catch (error) {
        console.error("Erro ao adicionar novo item:", error);
        showToast('Erro ao adicionar novo item', 'error');
      }
    };

    window.removeEditItem = function(index) {
      const itemDiv = document.querySelector(`.edit-item[data-index="${index}"]`) || 
                     document.querySelector(`.edit-item:nth-child(${index + 1})`);
      
      if (itemDiv) {
        // Confirmar remoção se o item tem dados
        const select = itemDiv.querySelector('.item-select');
        const hasData = select && select.value;
        
        if (hasData) {
          if (!confirm('Tem certeza que deseja remover este item?')) {
            return;
          }
        }
        
        itemDiv.remove();
        
        // Reindexar os itens restantes
        reindexEditItems();
      }
    };

    window.reindexEditItems = function() {
      const items = document.querySelectorAll('#editItemsList .edit-item');
      items.forEach((item, index) => {
        // Atualizar data-index nos elementos
        const select = item.querySelector('.item-select');
        const quantity = item.querySelector('.item-quantity');
        const unit = item.querySelector('.item-unit');
        const button = item.querySelector('.btn-danger');
        
        if (select) select.dataset.index = index;
        if (quantity) quantity.dataset.index = index;
        if (unit) unit.dataset.index = index;
        if (button) button.onclick = () => removeEditItem(index);
      });
    };

    window.closeEditModal = function() {
      const modal = document.querySelector('.edit-quotation-modal');
      if (modal) {
        modal.remove();
      }
    };

    window.confirmEditQuotation = async function(dialog) {
      try {
        const observacoes = dialog.querySelector('#editObservacoes').value;
        const itemsList = dialog.querySelector('#editItemsList');
        const items = [];

        // Coletar dados dos itens
        itemsList.querySelectorAll('.edit-item').forEach(itemDiv => {
          const select = itemDiv.querySelector('.item-select');
          const quantidade = itemDiv.querySelector('.item-quantity').value;
          const unidade = itemDiv.querySelector('.item-unit').value;
          const codigo = itemDiv.querySelector('.item-code').textContent;
          const descricao = itemDiv.querySelector('.item-description').textContent;

          if (select.value) {
            items.push({
              produtoId: select.value,
              codigo: codigo,
              descricao: descricao,
              quantidade: parseFloat(quantidade),
              unidade: unidade
            });
          }
        });

        if (items.length === 0) {
          showToast('Adicione pelo menos um item à cotação', 'error');
          return;
        }

        // Comparar itens antigos com novos para detectar alterações
        const alteracoes = [];
        const itensAntigos = currentQuotation.itens || [];

        // Verificar itens removidos
        itensAntigos.forEach(itemAntigo => {
          if (!items.some(item => item.produtoId === itemAntigo.produtoId)) {
            alteracoes.push({
              tipo: 'remocao',
              item: itemAntigo,
              data: Timestamp.now()
            });
          }
        });

        // Verificar itens adicionados ou modificados
        items.forEach(itemNovo => {
          const itemAntigo = itensAntigos.find(item => item.produtoId === itemNovo.produtoId);

          if (!itemAntigo) {
            alteracoes.push({
              tipo: 'adicao',
              item: itemNovo,
              data: Timestamp.now()
            });
          } else if (itemAntigo.quantidade !== itemNovo.quantidade) {
            alteracoes.push({
              tipo: 'modificacao',
              itemAntigo: itemAntigo,
              itemNovo: itemNovo,
              data: Timestamp.now()
            });
          }
        });

        // Buscar dados do solicitante
        const solicitacao = solicitacoes.find(s => s.id === currentQuotation.solicitacaoId);
        const solicitanteId = solicitacao?.solicitanteId;

        // Preparar mensagem para o solicitante
        let mensagemSolicitante = `A cotação ${currentQuotation.numero} foi atualizada:\n\n`;
        alteracoes.forEach(alteracao => {
          switch (alteracao.tipo) {
            case 'adicao':
              mensagemSolicitante += `- Novo item adicionado: ${alteracao.item.codigo} - ${alteracao.item.descricao} (${alteracao.item.quantidade} ${alteracao.item.unidade})\n`;
              break;
            case 'remocao':
              mensagemSolicitante += `- Item removido: ${alteracao.item.codigo} - ${alteracao.item.descricao}\n`;
              break;
            case 'modificacao':
              mensagemSolicitante += `- Quantidade alterada para ${alteracao.itemNovo.codigo}: ${alteracao.itemAntigo.quantidade} → ${alteracao.itemNovo.quantidade} ${alteracao.itemNovo.unidade}\n`;
              break;
          }
        });

        // Atualizar a cotação com as alterações
        await updateDoc(doc(db, "cotacoes", currentQuotation.id), {
          itens: items,
          observacoes: observacoes,
          ultimaAtualizacao: Timestamp.now(),
          atualizadoPor: currentUser.nome,
          alteracoes: [...(currentQuotation.alteracoes || []), ...alteracoes]
        });

        // Se houver alterações e um solicitante, criar notificação
        if (alteracoes.length > 0 && solicitanteId) {
          const notificacao = {
            tipo: 'alteracao_cotacao',
            cotacaoId: currentQuotation.id,
            cotacaoNumero: currentQuotation.numero,
            mensagem: mensagemSolicitante,
            data: Timestamp.now(),
            lida: false,
            destinatarioId: solicitanteId,
            remetenteId: currentUser.id,
            remetenteNome: currentUser.nome
          };

          await addDoc(collection(db, "notificacoes"), notificacao);
        }

        dialog.remove();
        showToast('Cotação atualizada com sucesso!', 'success');

        // Recarregar dados
        await loadInitialData();
        await loadQuotations();
        viewQuotation(currentQuotation.id);
      } catch (error) {
        console.error("Erro ao editar cotação:", error);
        showToast('Erro ao editar cotação', 'error');
      }
    };

    window.deleteQuotation = async function() {
      if (!currentQuotation) return;

      // Verificar permissões
      if (currentUser.nivel < 2 && !userPermissions.includes('gerenciar_cotacoes')) {
        showToast('Você não tem permissão para excluir cotações', 'error');
        return;
      }

      // Verificar se a cotação pode ser excluída
      if (currentQuotation.status !== 'ABERTA') {
        showToast('Apenas cotações abertas podem ser excluídas', 'error');
        return;
      }

      if (confirm('Tem certeza que deseja excluir esta cotação?')) {
        try {
          await deleteDoc(doc(db, "cotacoes", currentQuotation.id));
          showToast('Cotação excluída com sucesso!', 'success');
          closeModal('quotationModal');

          // Recarregar dados
          await loadInitialData();
          await loadQuotations();
        } catch (error) {
          console.error("Erro ao excluir cotação:", error);
          showToast('Erro ao excluir cotação', 'error');
        }
      }
    };

    // Adicionar função para recusar cotação
    window.rejectQuotation = async function() {
      if (!currentQuotation) return;

      // Verificar permissões
      if (currentUser.nivel < 2 && !userPermissions.includes('gerenciar_cotacoes')) {
        showToast('Você não tem permissão para recusar cotações', 'error');
        return;
      }

      // Verificar se a cotação pode ser recusada
      if (currentQuotation.status === 'FECHADA' || currentQuotation.status === 'APROVADA') {
        showToast('Não é possível recusar uma cotação já fechada ou aprovada', 'error');
        return;
      }

      // Criar modal de confirmação
      const confirmModal = document.createElement('div');
      confirmModal.className = 'modal';
      confirmModal.innerHTML = `
        <div class="modal-content" style="width: 500px;">
          <h3>Recusar Cotação</h3>
          <div style="margin: 20px 0;">
            <p><strong>Número da Cotação:</strong> ${currentQuotation.numero}</p>
            <p><strong>Motivo da Recusa:</strong></p>
            <textarea id="rejectReason" rows="4" style="width: 100%; margin-top: 10px;" 
                      placeholder="Digite o motivo da recusa da cotação..."></textarea>
          </div>
          <div style="margin-top: 20px;">
            <button class="btn-danger" onclick="confirmRejectQuotation(this.closest('.modal'))">Confirmar Recusa</button>
            <button onclick="this.closest('.modal').remove()">Cancelar</button>
          </div>
        </div>
      `;

      document.body.appendChild(confirmModal);
      confirmModal.style.display = 'block';
    };

    window.confirmRejectQuotation = async function(dialog) {
      try {
        const motivo = dialog.querySelector('#rejectReason').value.trim();
        if (!motivo) {
          showToast('Por favor, informe o motivo da recusa', 'error');
          return;
        }

        // Mostrar indicador de carregamento
        const loadingToast = showToast('Processando recusa da cotação...', 'info', true);

        // Buscar a solicitação original
        const solicitacao = solicitacoes.find(s => s.id === currentQuotation.solicitacaoId);
        if (!solicitacao) {
          throw new Error('Solicitação original não encontrada');
        }

        // Atualizar status da cotação
        await updateDoc(doc(db, "cotacoes", currentQuotation.id), {
          status: 'RECUSADA',
          motivoRecusa: motivo,
          dataRecusa: Timestamp.now(),
          recusadoPor: currentUser.nome
        });

        // Criar modal de opções para o solicitante
        const optionsModal = document.createElement('div');
        optionsModal.className = 'modal';
        optionsModal.innerHTML = `
          <div class="modal-content" style="width: 600px;">
            <h3>Opções para Nova Solicitação</h3>
            <div style="margin: 20px 0;">
              <p>Como você deseja prosseguir com a nova solicitação?</p>

              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
                <h4 style="color: #0854a0;">Opção 1: Sistema MRP</h4>
                <p>Utilize o sistema MRP para gerar uma nova solicitação automaticamente, considerando:</p>
                <ul style="margin-left: 20px;">
                  <li>Ordens de venda (demanda firme)</li>
                  <li>Previsões de vendas</li>
                  <li>Estoque atual</li>
                  <li>Ordens de produção em andamento</li>
                  <li>Compras já programadas</li>
                  <li>Lead time de produção e compra</li>
                </ul>
                <button class="btn-primary" onclick="reprocessarNecessidadesMRP('${solicitacao.id}', '${currentQuotation.id}')">
                  Reprocessar via MRP
                </button>
              </div>

              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
                <h4 style="color: #0854a0;">Opção 2: Método Manual</h4>
                <p>Crie uma nova solicitação manualmente, permitindo:</p>
                <ul style="margin-left: 20px;">
                  <li>Definir quantidades específicas</li>
                  <li>Escolher fornecedores alternativos</li>
                  <li>Adicionar observações personalizadas</li>
                  <li>Definir prioridades diferentes</li>
                </ul>
                <button class="btn-secondary" onclick="createManualRequest('${solicitacao.id}', '${currentQuotation.id}')">
                  Criar Manualmente
                </button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(optionsModal);
        optionsModal.style.display = 'block';
        dialog.remove();

        // Registrar log da recusa
        await addDoc(collection(db, "log_cotacoes"), {
          cotacaoId: currentQuotation.id,
          cotacaoNumero: currentQuotation.numero,
          acao: 'RECUSA',
          data: Timestamp.now(),
          usuarioId: currentUser.id,
          usuarioNome: currentUser.nome,
          detalhes: {
            motivo: motivo,
            solicitacaoId: solicitacao.id,
            solicitacaoNumero: solicitacao.numero
          }
        });

        // Criar notificação para o solicitante
        if (solicitacao.solicitanteId) {
          const notificacao = {
            tipo: 'cotacao_recusada',
            cotacaoId: currentQuotation.id,
            cotacaoNumero: currentQuotation.numero,
            solicitacaoId: solicitacao.id,
            solicitacaoNumero: solicitacao.numero,
            mensagem: `A cotação ${currentQuotation.numero} foi recusada. Motivo: ${motivo}\nPor favor, escolha como deseja prosseguir com a nova solicitação.`,
            data: Timestamp.now(),
            lida: false,
            destinatarioId: solicitacao.solicitanteId,
            remetenteId: currentUser.id,
            remetenteNome: currentUser.nome,
            opcoes: ['MRP', 'MANUAL']
          };

          await addDoc(collection(db, "notificacoes"), notificacao);
        }

        loadingToast.remove();
        showToast('Cotação recusada com sucesso!', 'success');

        // Recarregar dados e fechar modais
        await loadInitialData();
        await loadQuotations();
        closeModal('quotationModal');
      } catch (error) {
        console.error("Erro ao recusar cotação:", error);
        showToast(`Erro ao recusar cotação: ${error.message}`, 'error');
      }
    };

    // Função para criar solicitação via MRP
    window.createMRPRequest = async function(solicitacaoId, cotacaoId) {
      try {
        const loadingToast = showToast('Criando nova solicitação via MRP...', 'info', true);

        // Buscar dados da solicitação original
        const solicitacaoOriginal = solicitacoes.find(s => s.id === solicitacaoId);
        if (!solicitacaoOriginal) {
          throw new Error('Solicitação original não encontrada');
        }

        // Buscar dados do MRP
        const mrpSnap = await getDocs(collection(db, "mrp"));
        const mrpData = mrpSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Calcular nova demanda baseada no MRP
        const novaDemanda = await calcularDemandaMRP(solicitacaoOriginal.itens, mrpData);

        // Criar nova solicitação
        const novaSolicitacao = {
          numero: await gerarNumeroSolicitacao(),
          tipo: 'MRP',
          solicitanteId: solicitacaoOriginal.solicitanteId,
          solicitanteNome: solicitacaoOriginal.solicitanteNome,
          departamento: solicitacaoOriginal.departamento,
          dataCriacao: Timestamp.now(),
          status: 'PENDENTE',
          itens: novaDemanda,
          observacoes: `Solicitação gerada via MRP após recusa da cotação ${currentQuotation.numero}`,
          cotacaoRecusadaId: cotacaoId,
          origem: 'MRP'
        };

        // Salvar nova solicitação
        const novaSolicitacaoRef = await addDoc(collection(db, "solicitacoesCompra"), novaSolicitacao);

        // Registrar log
        await addDoc(collection(db, "log_solicitacoes"), {
          solicitacaoId: novaSolicitacaoRef.id,
          solicitacaoNumero: novaSolicitacao.numero,
          acao: 'CRIACAO_MRP',
          data: Timestamp.now(),
          usuarioId: currentUser.id,
          usuarioNome: currentUser.nome,
          detalhes: {
            cotacaoRecusadaId: cotacaoId,
            itens: novaDemanda
          }
        });

        loadingToast.remove();
        showToast('Nova solicitação criada via MRP com sucesso!', 'success');

        // Redirecionar para a nova solicitação
        window.location.href = `solicitacoes.html?id=${novaSolicitacaoRef.id}`;
      } catch (error) {
        console.error("Erro ao criar solicitação via MRP:", error);
        showToast('Erro ao criar solicitação via MRP', 'error');
      }
    };

    // Função para criar solicitação manual
    window.createManualRequest = async function(solicitacaoId, cotacaoId) {
      try {
        // Buscar dados da solicitação original
        const solicitacaoOriginal = solicitacoes.find(s => s.id === solicitacaoId);
        if (!solicitacaoOriginal) {
          throw new Error('Solicitação original não encontrada');
        }

        // Validar dados do solicitante
        if (!solicitacaoOriginal.solicitanteId || !solicitacaoOriginal.solicitanteNome) {
          throw new Error('Dados do solicitante não encontrados na solicitação original');
        }

        // Validar itens
        if (!solicitacaoOriginal.itens || solicitacaoOriginal.itens.length === 0) {
          throw new Error('A solicitação original não possui itens');
        }

        // Criar nova solicitação baseada na original
        const novaSolicitacao = {
          numero: await gerarNumeroSolicitacao(),
          tipo: 'MANUAL',
          solicitanteId: solicitacaoOriginal.solicitanteId,
          solicitanteNome: solicitacaoOriginal.solicitanteNome,
          departamento: solicitacaoOriginal.departamento || 'Não especificado',
          dataCriacao: Timestamp.now(),
          status: 'PENDENTE',
          itens: solicitacaoOriginal.itens.map(item => ({
            produtoId: item.produtoId,
            codigo: item.codigo,
            descricao: item.descricao,
            quantidade: item.quantidade,
            unidade: item.unidade,
            quantidadeOriginal: item.quantidade,
            motivoAjuste: 'MANUAL'
          })),
          observacoes: `Solicitação manual após recusa da cotação ${currentQuotation.numero}`,
          cotacaoRecusadaId: cotacaoId,
          origem: 'MANUAL',
          prioridade: solicitacaoOriginal.prioridade || 'NORMAL',
          centroCusto: solicitacaoOriginal.centroCusto || null,
          dataNecessidade: solicitacaoOriginal.dataNecessidade || null
        };

        // Validar dados antes de salvar
        if (!novaSolicitacao.solicitanteId || !novaSolicitacao.solicitanteNome) {
          throw new Error('Dados do solicitante são obrigatórios');
        }

        if (!novaSolicitacao.itens || novaSolicitacao.itens.length === 0) {
          throw new Error('A solicitação deve conter pelo menos um item');
        }

        // Salvar nova solicitação
        const novaSolicitacaoRef = await addDoc(collection(db, "solicitacoesCompra"), novaSolicitacao);

        // Registrar log
        await addDoc(collection(db, "log_solicitacoes"), {
          solicitacaoId: novaSolicitacaoRef.id,
          solicitacaoNumero: novaSolicitacao.numero,
          acao: 'CRIACAO_MANUAL',
          data: Timestamp.now(),
          usuarioId: currentUser.id,
          usuarioNome: currentUser.nome,
          detalhes: {
            cotacaoRecusadaId: cotacaoId,
            itens: novaSolicitacao.itens,
            solicitanteId: novaSolicitacao.solicitanteId,
            solicitanteNome: novaSolicitacao.solicitanteNome
          }
        });

        showToast('Nova solicitação criada manualmente com sucesso!', 'success');

        // Redirecionar para a nova solicitação
        window.location.href = `solicitacoes.html?id=${novaSolicitacaoRef.id}`;
      } catch (error) {
        console.error("Erro ao criar solicitação manual:", error);
        showToast(`Erro ao criar solicitação manual: ${error.message}`, 'error');
      }
    };

    // Função auxiliar para calcular demanda via MRP
    async function calcularDemandaMRP(itens, mrpData) {
      const novaDemanda = [];

      for (const item of itens) {
        try {
          // Buscar dados do item no MRP
          const mrpItem = mrpData.find(m => m.produtoId === item.produtoId);

          if (mrpItem) {
            // Dados de demanda
            const demandaFirme = mrpItem.ordensVenda?.reduce((sum, ordem) => sum + ordem.quantidade, 0) || 0;
            const previsaoVendas = mrpItem.previsaoVendas || 0;
            const demandaTotal = demandaFirme + previsaoVendas;

            // Dados de disponibilidade
            const estoqueAtual = mrpItem.estoqueAtual || 0;
            const pedidosEmAndamento = mrpItem.pedidosEmAndamento?.reduce((sum, pedido) => sum + pedido.quantidade, 0) || 0;
            const ordensProducao = mrpItem.ordensProducao?.reduce((sum, op) => sum + op.quantidade, 0) || 0;
            const disponibilidadeTotal = estoqueAtual + pedidosEmAndamento + ordensProducao;

            // Dados de lead time
            const leadTimeCompra = mrpItem.leadTimeCompra || 0;
            const leadTimeProducao = mrpItem.leadTimeProducao || 0;
            const leadTimeTotal = Math.max(leadTimeCompra, leadTimeProducao);

            // Cálculo da necessidade líquida
            const necessidadeLiquida = Math.max(0, demandaTotal - disponibilidadeTotal);

            // Ajuste baseado no lead time
            const necessidadeAjustada = necessidadeLiquida * (1 + (leadTimeTotal / 30)); // Ajuste mensal

            // Adicionar à nova demanda
            novaDemanda.push({
              ...item,
              quantidade: Math.ceil(necessidadeAjustada), // Arredonda para cima
              quantidadeOriginal: item.quantidade,
              motivoAjuste: 'MRP',
              detalhesMRP: {
                demandaFirme,
                previsaoVendas,
                estoqueAtual,
                pedidosEmAndamento,
                ordensProducao,
                leadTimeTotal,
                necessidadeLiquida,
                necessidadeAjustada
              }
            });
          } else {
            // Se não encontrar no MRP, mantém a quantidade original
            novaDemanda.push({
              ...item,
              quantidadeOriginal: item.quantidade,
              motivoAjuste: 'SEM_DADOS_MRP',
              detalhesMRP: {
                mensagem: 'Item não encontrado no MRP'
              }
            });
          }
        } catch (error) {
          console.error(`Erro ao calcular demanda MRP para item ${item.codigo}:`, error);
          novaDemanda.push({
            ...item,
            quantidadeOriginal: item.quantidade,
            motivoAjuste: 'ERRO_CALCULO',
            detalhesMRP: {
              erro: error.message
            }
          });
        }
      }

      return novaDemanda;
    }

    // Função auxiliar para gerar número de solicitação
    async function gerarNumeroSolicitacao() {
      try {
        const counterRef = doc(db, "contadores", "solicitacoes");
        const counterDoc = await getDoc(counterRef);

        if (!counterDoc.exists()) {
          await setDoc(counterRef, { valor: 0 });
        }

        const nextNumber = (counterDoc.exists() ? counterDoc.data().valor : 0) + 1;
        await updateDoc(counterRef, { valor: nextNumber });

        const date = new Date();
        const year = date.getFullYear().toString().substr(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const sequence = nextNumber.toString().padStart(6, '0');

        return `SC${year}${month}${sequence}`;
      } catch (error) {
        console.error("Erro ao gerar número da solicitação:", error);
        throw error;
      }
    }

    // Função auxiliar para mostrar toasts com opção de loading
    function showToast(message, type = 'success', isLoading = false) {
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: fadeInOut 2s ease-in-out;
        display: flex;
        align-items: center;
        gap: 10px;
      `;

      if (isLoading) {
        toast.innerHTML = `
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="sr-only">Carregando...</span>
          </div>
          ${message}
        `;
      } else {
        toast.textContent = message;
      }

      document.body.appendChild(toast);

      if (!isLoading) {
        setTimeout(() => {
          toast.remove();
        }, 2000);
      }

      return toast;
    }

    // Função para reprocessar necessidades após cancelamento
    async function reprocessarNecessidadesMRP(solicitacaoId, cotacaoId) {
      try {
        // Buscar dados da solicitação original
        const solicitacaoOriginal = solicitacoes.find(s => s.id === solicitacaoId);
        if (!solicitacaoOriginal) {
          throw new Error('Solicitação original não encontrada');
        }

        // Buscar dados do MRP atualizados
        const mrpSnap = await getDocs(collection(db, "mrp"));
        const mrpData = mrpSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Calcular nova demanda
        const novaDemanda = await calcularDemandaMRP(solicitacaoOriginal.itens, mrpData);

        // Criar nova solicitação com as necessidades recalculadas
        const novaSolicitacao = {
          numero: await gerarNumeroSolicitacao(),
          tipo: 'MRP_REPROCESSADO',
          solicitanteId: solicitacaoOriginal.solicitanteId,
          solicitanteNome: solicitacaoOriginal.solicitanteNome,
          departamento: solicitacaoOriginal.departamento || 'Não especificado',
          dataCriacao: Timestamp.now(),
          status: 'PENDENTE',
          itens: novaDemanda,
          observacoes: `Solicitação reprocessada via MRP após cancelamento da cotação ${currentQuotation.numero}`,
          cotacaoRecusadaId: cotacaoId,
          origem: 'MRP_REPROCESSADO',
          prioridade: solicitacaoOriginal.prioridade || 'NORMAL',
          centroCusto: solicitacaoOriginal.centroCusto || null,
          dataNecessidade: solicitacaoOriginal.dataNecessidade || null
        };

        // Salvar nova solicitação
        const novaSolicitacaoRef = await addDoc(collection(db, "solicitacoesCompra"), novaSolicitacao);

        // Registrar log do reprocessamento
        await addDoc(collection(db, "log_solicitacoes"), {
          solicitacaoId: novaSolicitacaoRef.id,
          solicitacaoNumero: novaSolicitacao.numero,
          acao: 'REPROCESSAMENTO_MRP',
          data: Timestamp.now(),
          usuarioId: currentUser.id,
          usuarioNome: currentUser.nome,
          detalhes: {
            cotacaoRecusadaId: cotacaoId,
            solicitacaoOriginalId: solicitacaoId,
            itens: novaDemanda
          }
        });

        return novaSolicitacaoRef.id;
      } catch (error) {
        console.error("Erro ao reprocessar necessidades MRP:", error);
        throw error;
      }
    }

    // Definir updateItemQuantity no escopo global
    window.updateItemQuantity = async function(cotacaoId, itemId, newQuantity) {
      const cotacao = cotacoes.find(c => c.id === cotacaoId);
      if (!cotacao) return;
      const item = cotacao.itens.find(i => i.id === itemId);
      if (!item) return;
      const quantidade = parseFloat(newQuantity);
      if (isNaN(quantidade) || quantidade <= 0) {
        alert('Quantidade inválida.');
        return;
      }
      item.quantidade = quantidade;
      await updateDoc(doc(db, "cotacoes", cotacaoId), { itens: cotacao.itens });
      await loadInitialData();
      viewQuotation(cotacaoId);
    };

    // Definir saveEditedQuotation no escopo global
    window.saveEditedQuotation = async function() {
      try {
        // 1. Verificar se a cotação existe
        const cotacaoId = currentQuotation?.id;
        if (!cotacaoId) {
          showToast('Cotação não encontrada.', 'error');
          return;
        }

        // 2. Coletar itens do modal com validação
        const itemsList = document.querySelectorAll('#editItemsList .edit-item');
        const novosItens = [];
        let hasErrors = false;

        itemsList.forEach((itemDiv, index) => {
          const produtoSelect = itemDiv.querySelector('.item-select');
          const quantidadeInput = itemDiv.querySelector('.item-quantity');
          const unidadeInput = itemDiv.querySelector('.item-unit');
          
          // Validar se o produto foi selecionado
          if (!produtoSelect || !produtoSelect.value) {
            showToast(`Item ${index + 1}: Selecione um produto`, 'error');
            hasErrors = true;
            return;
          }

          // Validar quantidade
          const quantidade = parseFloat(quantidadeInput?.value || 0);
          if (isNaN(quantidade) || quantidade <= 0) {
            showToast(`Item ${index + 1}: Quantidade deve ser maior que zero`, 'error');
            hasErrors = true;
            return;
          }

          const selectedOption = produtoSelect.selectedOptions[0];
          const codigo = selectedOption?.getAttribute('data-codigo') || '';
          const descricao = selectedOption?.getAttribute('data-descricao') || '';
          const unidade = unidadeInput?.value || 'UN';

          // Verificar se já existe um item com o mesmo produto (evitar duplicação)
          const jaExiste = novosItens.some(item => item.produtoId === produtoSelect.value);
          if (jaExiste) {
            showToast(`Produto ${codigo} já foi adicionado. Remova as duplicatas.`, 'error');
            hasErrors = true;
            return;
          }

          novosItens.push({
            produtoId: produtoSelect.value,
            codigo: codigo,
            descricao: descricao,
            unidade: unidade,
            quantidade: quantidade
          });
        });

        // 3. Verificar se há erros ou se não há itens
        if (hasErrors) {
          return;
        }

        if (novosItens.length === 0) {
          showToast('Adicione pelo menos um item à cotação', 'error');
          return;
        }

        // 4. Pegar observações
        const observacoesTextarea = document.getElementById('editObservacoes');
        const observacoes = observacoesTextarea ? observacoesTextarea.value.trim() : '';

        // 5. Mostrar loading
        const loadingToast = showToast('Salvando alterações...', 'info', true);

        // 6. Comparar com itens antigos para detectar alterações
        const itensAntigos = currentQuotation.itens || [];
        const alteracoes = [];

        // Verificar itens removidos
        itensAntigos.forEach(itemAntigo => {
          if (!novosItens.some(item => item.produtoId === itemAntigo.produtoId)) {
            alteracoes.push({
              tipo: 'remocao',
              item: itemAntigo,
              data: Timestamp.now()
            });
          }
        });

        // Verificar itens adicionados ou modificados
        novosItens.forEach(itemNovo => {
          const itemAntigo = itensAntigos.find(item => item.produtoId === itemNovo.produtoId);

          if (!itemAntigo) {
            alteracoes.push({
              tipo: 'adicao',
              item: itemNovo,
              data: Timestamp.now()
            });
          } else if (itemAntigo.quantidade !== itemNovo.quantidade) {
            alteracoes.push({
              tipo: 'modificacao',
              itemAntigo: itemAntigo,
              itemNovo: itemNovo,
              data: Timestamp.now()
            });
          }
        });

        // 7. Atualizar no Firestore
        const updateData = {
          itens: novosItens,
          observacoes: observacoes,
          ultimaAtualizacao: Timestamp.now(),
          atualizadoPor: currentUser.nome
        };

        // Adicionar alterações ao histórico se houver
        if (alteracoes.length > 0) {
          updateData.alteracoes = [...(currentQuotation.alteracoes || []), ...alteracoes];
        }

        await updateDoc(doc(db, "cotacoes", cotacaoId), updateData);

        // 8. Se houver alterações, notificar o solicitante
        if (alteracoes.length > 0) {
          const solicitacao = solicitacoes.find(s => s.id === currentQuotation.solicitacaoId);
          if (solicitacao?.solicitanteId) {
            let mensagemSolicitante = `A cotação ${currentQuotation.numero} foi atualizada:\n\n`;
            alteracoes.forEach(alteracao => {
              switch (alteracao.tipo) {
                case 'adicao':
                  mensagemSolicitante += `- Novo item: ${alteracao.item.codigo} - ${alteracao.item.descricao} (${alteracao.item.quantidade} ${alteracao.item.unidade})\n`;
                  break;
                case 'remocao':
                  mensagemSolicitante += `- Item removido: ${alteracao.item.codigo} - ${alteracao.item.descricao}\n`;
                  break;
                case 'modificacao':
                  mensagemSolicitante += `- Quantidade alterada para ${alteracao.itemNovo.codigo}: ${alteracao.itemAntigo.quantidade} → ${alteracao.itemNovo.quantidade} ${alteracao.itemNovo.unidade}\n`;
                  break;
              }
            });

            const notificacao = {
              tipo: 'alteracao_cotacao',
              cotacaoId: currentQuotation.id,
              cotacaoNumero: currentQuotation.numero,
              mensagem: mensagemSolicitante,
              data: Timestamp.now(),
              lida: false,
              destinatarioId: solicitacao.solicitanteId,
              remetenteId: currentUser.id,
              remetenteNome: currentUser.nome
            };

            await addDoc(collection(db, "notificacoes"), notificacao);
          }
        }

        // 9. Fechar modal e atualizar interface
        loadingToast.remove();
        closeEditModal();
        showToast('Cotação atualizada com sucesso!', 'success');

        // 10. Recarregar dados e reabrir visualização
        await loadInitialData();
        await loadQuotations();
        updateDashboard();
        
        // Reabrir a visualização da cotação atualizada
        setTimeout(() => {
          viewQuotation(cotacaoId);
        }, 500);

      } catch (error) {
        console.error("Erro ao salvar cotação:", error);
        showToast(`Erro ao salvar alterações: ${error.message}`, 'error');
      }
    };
  // Função para tratar edição manual de preço na tela de edição de cotação
window.handleManualPriceEdit = function(input, itemIndex) {
  if (!window.manualPricesEdit) window.manualPricesEdit = {};
  window.manualPricesEdit[itemIndex] = parseFloat(input.value) || 0;
};

// Ao salvar cotação editada, inclua os preços manuais:
if (typeof window.saveEditedQuotation === 'function') {
  const originalSaveEditedQuotation = window.saveEditedQuotation;
  window.saveEditedQuotation = async function() {
    // ... coleta outros dados normalmente
    const cotacaoEditada = {/* ... */};
    // Salvar preços manuais
    cotacaoEditada.precosManuais = {};
    for (let idx in window.manualPricesEdit) {
      cotacaoEditada.precosManuais[idx] = {
        preco: window.manualPricesEdit[idx],
        usuario: window.currentUser?.nome || '',
        data: new Date().toISOString()
      };
    }
    // ... prosseguir com o salvamento
    await originalSaveEditedQuotation.apply(this, arguments);
  };
}
// Seleção múltipla de cotações
window.toggleSelectAllCotacoes = function(checkbox) {
  document.querySelectorAll('.selectCotacao').forEach(cb => {
    cb.checked = checkbox.checked;
  });
};

// Modal para aglutinar (pode ser aprimorado para um modal real)
window.aglutinacaoModal = function() {
  const selecionadas = Array.from(document.querySelectorAll('.selectCotacao:checked')).map(cb => cb.value);
  if (selecionadas.length < 2) {
    showToast('Selecione duas ou mais cotações para aglutinar.', 'warning');
    return;
  }
  if (!confirm(`Deseja realmente aglutinar as cotações: ${selecionadas.join(', ')}?`)) return;
  aglutinarCotacoes(selecionadas);
};

// Função para aglutinar cotações
window.aglutinarCotacoes = async function(ids) {
  try {
    // Carregar as cotações selecionadas
    const cotacoesSelecionadas = cotacoes.filter(c => ids.includes(c.id));
    
    if (cotacoesSelecionadas.length < 2) {
      showToast('Selecione pelo menos duas cotações para aglutinar.', 'warning');
      return;
    }
    
    // Verificar status válidos
    const statusInvalidos = cotacoesSelecionadas.filter(c => 
      ['FECHADA', 'CANCELADO', 'AGLUTINADO'].includes(c.status)
    );
    
    if (statusInvalidos.length > 0) {
      showToast('Não é possível aglutinar cotações com status: FECHADA, CANCELADO ou AGLUTINADO.', 'error');
      return;
    }
    
    // Criar nova cotação aglutinada (sem copiar o id)
    const baseCotacao = { ...cotacoesSelecionadas[0] };
    delete baseCotacao.id; // Remove o id explicitamente
    
    const novaCotacao = {
      ...baseCotacao,
      numero: await generateQuotationNumber(), // Usar a função existente para gerar número
      status: 'ABERTA', // Definir como ABERTA, não AGLUTINADO
      cotacoesAglutinadas: cotacoesSelecionadas.map(c => c.numero),
      itens: [],
      fornecedores: [],
      dataCriacao: Timestamp.now(),
      criadoPor: currentUser?.nome || 'Sistema',
      respostas: {},
      precosManuais: {},
      alteracoes: [{
        tipo: 'CRIACAO',
        data: Timestamp.now(),
        usuario: currentUser?.nome || 'Sistema',
        descricao: `Cotação aglutinada criada a partir de ${cotacoesSelecionadas.length} cotações`
      }]
    };
    
    // Consolidar itens (evitando duplicatas por código/produtoId)
    const itensMap = new Map();
    
    cotacoesSelecionadas.forEach(cotacao => {
      // Adicionar itens
      if (cotacao.itens && Array.isArray(cotacao.itens)) {
        cotacao.itens.forEach(item => {
          const key = item.produtoId || item.codigo || JSON.stringify(item);
          
          if (!itensMap.has(key)) {
            itensMap.set(key, { ...item });
          } else {
            // Se o item já existe, somar as quantidades
            const itemExistente = itensMap.get(key);
            itemExistente.quantidade = (parseFloat(itemExistente.quantidade) || 0) + 
                                      (parseFloat(item.quantidade) || 0);
          }
        });
      }
      
      // Adicionar fornecedores
      if (cotacao.fornecedores && Array.isArray(cotacao.fornecedores)) {
        novaCotacao.fornecedores = [...new Set([...novaCotacao.fornecedores, ...cotacao.fornecedores])];
      }
    });
    
    // Converter o Map de itens para array
    novaCotacao.itens = Array.from(itensMap.values());
    
    // Mostrar loading
    const loadingToast = showToast('Processando aglutinação...', 'info', true);
    
    // Salvar nova cotação aglutinada
    const docRef = await addDoc(collection(db, 'cotacoes'), novaCotacao);
    
    // Atualizar status das cotações originais
    for (const cotacao of cotacoesSelecionadas) {
      await updateDoc(doc(db, 'cotacoes', cotacao.id), { 
        status: 'AGLUTINADO', 
        cotacaoAglutinadora: docRef.id,
        alteracoes: [...(cotacao.alteracoes || []), {
          tipo: 'AGLUTINACAO',
          data: Timestamp.now(),
          usuario: currentUser?.nome || 'Sistema',
          descricao: `Cotação aglutinada em ${novaCotacao.numero}`
        }]
      });
    }
    
    // Remover loading e mostrar sucesso
    if (loadingToast) loadingToast.remove();
    showToast('Cotações aglutinadas com sucesso!', 'success');
    
    // Recarregar dados
    await loadInitialData();
    await loadQuotations();
    
  } catch (e) {
    console.error('Erro ao aglutinar:', e);
    showToast('Erro ao aglutinar: ' + (e.message || e), 'error');
  }
}
</script>
</body>
</html>
