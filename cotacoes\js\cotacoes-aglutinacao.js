// ===== COTAÇÕES - AGLUTINAÇÃO E DIVISÃO =====

// ===== SELEÇÃO DE COTAÇÕES =====
function toggleQuotationSelection(quotationId) {
    if (selectedQuotations.has(quotationId)) {
        selectedQuotations.delete(quotationId);
    } else {
        selectedQuotations.add(quotationId);
    }
    updateSelectedQuotationsCounter();
    updateAglutinacaoButtons();
}

function toggleAllQuotations() {
    const checkbox = document.getElementById('selectAllQuotations');
    const quotationCheckboxes = document.querySelectorAll('.quotation-checkbox');
    
    if (checkbox.checked) {
        quotationCheckboxes.forEach(cb => {
            cb.checked = true;
            selectedQuotations.add(cb.value);
        });
    } else {
        quotationCheckboxes.forEach(cb => {
            cb.checked = false;
            selectedQuotations.delete(cb.value);
        });
    }
    
    updateSelectedQuotationsCounter();
    updateAglutinacaoButtons();
}

function updateSelectedQuotationsCounter() {
    const counter = document.getElementById('selectedQuotationsCounter');
    if (!counter) return;
    
    const count = selectedQuotations.size;
    counter.innerHTML = `<i class="fas fa-check-square"></i> ${count} selecionadas`;
    
    // Atualizar cor baseado na quantidade
    if (count === 0) {
        counter.style.background = '#e7f3ff';
        counter.style.color = '#0066cc';
    } else if (count === 1) {
        counter.style.background = '#fff3cd';
        counter.style.color = '#856404';
    } else {
        counter.style.background = '#d4edda';
        counter.style.color = '#155724';
    }
}

function updateAglutinacaoButtons() {
    const btnAglutinar = document.getElementById('btnAglutinar');
    const btnDividir = document.getElementById('btnDividir');
    const selectedCount = selectedQuotations.size;
    
    if (btnAglutinar) {
        btnAglutinar.disabled = selectedCount < 2;
    }
    
    if (btnDividir) {
        const selectedQuotation = selectedCount === 1 ? 
            cotacoes.find(c => c.id === Array.from(selectedQuotations)[0]) : null;
        btnDividir.disabled = !(selectedCount === 1 && selectedQuotation?.aglutinada?.tipo === 'principal');
    }
}

function clearSelection() {
    selectedQuotations.clear();
    document.querySelectorAll('.quotation-checkbox').forEach(cb => cb.checked = false);
    const selectAll = document.getElementById('selectAllQuotations');
    if (selectAll) selectAll.checked = false;
    updateSelectedQuotationsCounter();
    updateAglutinacaoButtons();
}

// ===== AGLUTINAÇÃO =====
function aglutinarCotacoes() {
    if (selectedQuotations.size < 2) {
        showNotification('Selecione pelo menos 2 cotações para aglutinar', 'warning');
        return;
    }

    const selectedIds = Array.from(selectedQuotations);
    const selectedCotacoes = cotacoes.filter(c => selectedIds.includes(c.id));
    
    // Verificar se alguma já está aglutinada
    const jaAglutinadas = selectedCotacoes.filter(c => c.aglutinada);
    if (jaAglutinadas.length > 0) {
        showNotification('Não é possível aglutinar cotações que já estão aglutinadas', 'error');
        return;
    }

    // Mostrar preview no modal
    showAglutinacaoPreview(selectedCotacoes);
    openModal('aglutinacaoModal');
}

function showAglutinacaoPreview(cotacoes) {
    const container = document.getElementById('aglutinacaoPreview');
    if (!container) return;
    
    let html = `
        <h4><i class="fas fa-list"></i> Cotações Selecionadas (${cotacoes.length})</h4>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Número</th>
                        <th>Data</th>
                        <th>Status</th>
                        <th>Itens</th>
                        <th>Fornecedores</th>
                        <th>Valor</th>
                    </tr>
                </thead>
                <tbody>
    `;

    let totalItens = 0;
    let totalFornecedores = new Set();
    let valorTotal = 0;

    cotacoes.forEach(cotacao => {
        const itensCount = cotacao.itens ? cotacao.itens.length : 0;
        const fornecedoresCount = cotacao.fornecedores ? cotacao.fornecedores.length : 0;
        const valor = cotacao.valorEstimado || 0;

        totalItens += itensCount;
        if (cotacao.fornecedores) {
            cotacao.fornecedores.forEach(f => totalFornecedores.add(f));
        }
        valorTotal += valor;

        html += `
            <tr>
                <td><strong>${cotacao.numero}</strong></td>
                <td>${formatDate(cotacao.dataCriacao)}</td>
                <td><span class="status ${getStatusClass(cotacao.status)}">${getStatusText(cotacao.status)}</span></td>
                <td>${itensCount}</td>
                <td>${fornecedoresCount}</td>
                <td>R$ ${formatCurrency(valor)}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
                <tfoot style="background: #f8f9fa; font-weight: bold;">
                    <tr>
                        <td colspan="3">TOTAIS CONSOLIDADOS:</td>
                        <td>${totalItens}</td>
                        <td>${totalFornecedores.size}</td>
                        <td>R$ ${formatCurrency(valorTotal)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    // Verificar status diferentes
    const statuses = [...new Set(cotacoes.map(c => c.status))];
    if (statuses.length > 1) {
        html += `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Atenção:</strong> As cotações têm status diferentes: ${statuses.join(', ')}
            </div>
        `;
    }

    container.innerHTML = html;
}

async function confirmarAglutinacao() {
    const selectedIds = Array.from(selectedQuotations);
    const selectedCotacoes = cotacoes.filter(c => selectedIds.includes(c.id));
    
    const observacoes = document.getElementById('aglutinacaoObservacoes')?.value || '';
    const consolidarItens = document.getElementById('consolidarItens')?.checked ?? true;
    const manterFornecedores = document.getElementById('manterFornecedores')?.checked ?? true;

    try {
        // Escolher a cotação principal (mais antiga)
        const principal = selectedCotacoes.reduce((oldest, current) => 
            (oldest.dataCriacao.toDate() < current.dataCriacao.toDate()) ? oldest : current
        );

        const filhas = selectedCotacoes.filter(c => c.id !== principal.id);

        // Gerar dados da aglutinação
        const aglutinacaoId = `AGT-${Date.now()}`;
        const dataAglutinacao = Timestamp.now();

        // Consolidar dados
        const itensConsolidados = [];
        const fornecedoresConsolidados = new Set();
        let valorTotalConsolidado = 0;

        selectedCotacoes.forEach(cotacao => {
            // Consolidar itens
            if (cotacao.itens) {
                cotacao.itens.forEach(item => {
                    if (consolidarItens) {
                        const existingItem = itensConsolidados.find(i => i.codigo === item.codigo);
                        if (existingItem) {
                            existingItem.quantidade += item.quantidade || 0;
                        } else {
                            itensConsolidados.push({...item});
                        }
                    } else {
                        itensConsolidados.push({...item});
                    }
                });
            }

            // Consolidar fornecedores
            if (cotacao.fornecedores) {
                cotacao.fornecedores.forEach(f => fornecedoresConsolidados.add(f));
            }

            // Somar valores
            valorTotalConsolidado += cotacao.valorEstimado || 0;
        });

        // Atualizar cotação principal
        const principalUpdate = {
            aglutinada: {
                tipo: 'principal',
                id: aglutinacaoId,
                dataAglutinacao: dataAglutinacao,
                cotacoesFilhas: filhas.map(f => f.id),
                observacoes: observacoes || `Aglutinação de ${selectedCotacoes.length} cotações`
            },
            itens: itensConsolidados,
            fornecedores: Array.from(fornecedoresConsolidados),
            valorEstimado: valorTotalConsolidado,
            ultimaAtualizacao: dataAglutinacao,
            historico: [
                ...(principal.historico || []),
                {
                    data: dataAglutinacao,
                    acao: 'AGLUTINACAO_PRINCIPAL',
                    usuario: currentUser.nome,
                    detalhes: `Aglutinou ${filhas.length} cotações: ${filhas.map(f => f.numero).join(', ')}`
                }
            ]
        };

        await updateDoc(doc(db, "cotacoes", principal.id), principalUpdate);

        // Atualizar cotações filhas
        for (const filha of filhas) {
            const filhaUpdate = {
                aglutinada: {
                    tipo: 'filha',
                    id: aglutinacaoId,
                    principalId: principal.id,
                    dataAglutinacao: dataAglutinacao
                },
                status: 'AGLUTINADA',
                statusOriginal: filha.status,
                ultimaAtualizacao: dataAglutinacao,
                historico: [
                    ...(filha.historico || []),
                    {
                        data: dataAglutinacao,
                        acao: 'AGLUTINACAO_FILHA',
                        usuario: currentUser.nome,
                        detalhes: `Aglutinada à cotação principal ${principal.numero}`
                    }
                ]
            };

            await updateDoc(doc(db, "cotacoes", filha.id), filhaUpdate);
        }

        showNotification(`${selectedCotacoes.length} cotações aglutinadas com sucesso!`, 'success');
        
        // Fechar modal e recarregar dados
        closeModal('aglutinacaoModal');
        await loadQuotations();
        clearSelection();

    } catch (error) {
        console.error('Erro ao aglutinar cotações:', error);
        showNotification('Erro ao aglutinar cotações: ' + error.message, 'error');
    }
}

// ===== DIVISÃO =====
async function dividirCotacao(quotationId = null) {
    // Se não foi passado ID, usar a selecionada
    if (!quotationId && selectedQuotations.size === 1) {
        quotationId = Array.from(selectedQuotations)[0];
    }

    if (!quotationId) {
        showNotification('Selecione uma cotação aglutinada para dividir', 'warning');
        return;
    }

    const cotacao = cotacoes.find(c => c.id === quotationId);
    if (!cotacao || !cotacao.aglutinada || cotacao.aglutinada.tipo !== 'principal') {
        showNotification('Apenas cotações principais aglutinadas podem ser divididas', 'error');
        return;
    }

    const filhas = cotacoes.filter(c => 
        c.aglutinada && c.aglutinada.principalId === quotationId
    );

    if (!confirm(`Dividir aglutinação? Isso irá restaurar ${filhas.length + 1} cotações independentes.`)) {
        return;
    }

    try {
        const dataDivisao = Timestamp.now();

        // Restaurar cotação principal
        const principalUpdate = {
            aglutinada: null,
            ultimaAtualizacao: dataDivisao,
            historico: [
                ...(cotacao.historico || []),
                {
                    data: dataDivisao,
                    acao: 'DIVISAO_AGLUTINACAO',
                    usuario: currentUser.nome,
                    detalhes: `Dividiu aglutinação com ${filhas.length} cotações`
                }
            ]
        };

        await updateDoc(doc(db, "cotacoes", quotationId), principalUpdate);

        // Restaurar cotações filhas
        for (const filha of filhas) {
            const filhaUpdate = {
                aglutinada: null,
                status: filha.statusOriginal || 'ABERTA',
                ultimaAtualizacao: dataDivisao,
                historico: [
                    ...(filha.historico || []),
                    {
                        data: dataDivisao,
                        acao: 'DIVISAO_RESTAURACAO',
                        usuario: currentUser.nome,
                        detalhes: 'Cotação restaurada após divisão de aglutinação'
                    }
                ]
            };

            await updateDoc(doc(db, "cotacoes", filha.id), filhaUpdate);
        }

        showNotification(`Aglutinação dividida! ${filhas.length + 1} cotações restauradas.`, 'success');
        
        // Recarregar dados
        await loadQuotations();
        clearSelection();

    } catch (error) {
        console.error('Erro ao dividir cotação:', error);
        showNotification('Erro ao dividir cotação: ' + error.message, 'error');
    }
}

// ===== CONTROLES DE EXPANSÃO =====
function toggleAglutinacaoFilhas(principalId) {
    const filhas = document.querySelectorAll(`tr[data-quotation-id]`);
    
    filhas.forEach(row => {
        const quotationId = row.getAttribute('data-quotation-id');
        const quotation = cotacoes.find(c => c.id === quotationId);
        
        if (quotation?.aglutinada?.principalId === principalId) {
            row.classList.toggle('hidden');
        }
    });
    
    const icon = document.getElementById(`expand-icon-${principalId}`);
    if (icon) {
        icon.classList.toggle('fa-chevron-down');
        icon.classList.toggle('fa-chevron-up');
    }
}

// ===== FUNÇÕES AUXILIARES =====
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// Fechar modal ao clicar fora
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
}
