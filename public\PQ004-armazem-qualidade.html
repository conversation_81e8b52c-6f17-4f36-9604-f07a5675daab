<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ004 - Armazém da Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e67e22, #f39c12);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #e67e22;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #e67e22;
            margin-right: 10px;
            width: 20px;
        }

        .warehouse-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .warehouse-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .warehouse-card:hover {
            transform: translateY(-5px);
        }

        .warehouse-card.quarentena { border-left-color: #f39c12; }
        .warehouse-card.inspecao { border-left-color: #3498db; }
        .warehouse-card.aprovado { border-left-color: #27ae60; }
        .warehouse-card.rejeitado { border-left-color: #e74c3c; }

        .warehouse-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .warehouse-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            text-transform: uppercase;
            margin-top: 5px;
        }

        .movement-timeline {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .movement-timeline h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-left: 3px solid #e67e22;
            margin-bottom: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e67e22;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2em;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .timeline-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .timeline-time {
            color: #95a5a6;
            font-size: 0.8em;
            margin-left: auto;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-quarentena {
            background: #fff3cd;
            color: #856404;
        }

        .status-inspecao {
            background: #cce5ff;
            color: #004085;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .warehouse-grid {
                grid-template-columns: 1fr;
            }
            
            .filters {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .btn-action {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 PQ004 - Armazém da Qualidade</h1>
            <p>Controle de estoque específico para inspeção</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Controlar estoque específico para materiais em processo de inspeção</li>
                    <li><i class="fas fa-warehouse"></i><strong>Função:</strong> Segregar materiais até aprovação ou rejeição final</li>
                    <li><i class="fas fa-exchange-alt"></i><strong>Movimentação:</strong> Entrada, transferência e saída controladas</li>
                    <li><i class="fas fa-shield-alt"></i><strong>Rastreabilidade:</strong> Histórico completo de movimentações e permanência</li>
                </ul>
            </div>

            <!-- Cards do Armazém -->
            <div class="warehouse-grid">
                <div class="warehouse-card quarentena">
                    <h4><i class="fas fa-pause-circle"></i> Quarentena</h4>
                    <div class="warehouse-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="quarentenaItens">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="quarentenaDias">0</div>
                            <div class="stat-label">Dias Médios</div>
                        </div>
                    </div>
                    <button class="btn btn-warning btn-full" onclick="filtrarArea('QUARENTENA')">
                        <i class="fas fa-eye"></i> Ver Quarentena
                    </button>
                </div>

                <div class="warehouse-card inspecao">
                    <h4><i class="fas fa-search"></i> Em Inspeção</h4>
                    <div class="warehouse-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="inspecaoItens">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="inspecaoDias">0</div>
                            <div class="stat-label">Dias Médios</div>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" onclick="filtrarArea('INSPECAO')">
                        <i class="fas fa-eye"></i> Ver Inspeção
                    </button>
                </div>

                <div class="warehouse-card aprovado">
                    <h4><i class="fas fa-check-circle"></i> Aprovados</h4>
                    <div class="warehouse-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="aprovadoItens">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="aprovadoDias">0</div>
                            <div class="stat-label">Dias Médios</div>
                        </div>
                    </div>
                    <button class="btn btn-success btn-full" onclick="filtrarArea('APROVADO')">
                        <i class="fas fa-eye"></i> Ver Aprovados
                    </button>
                </div>

                <div class="warehouse-card rejeitado">
                    <h4><i class="fas fa-times-circle"></i> Rejeitados</h4>
                    <div class="warehouse-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="rejeitadoItens">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="rejeitadoDias">0</div>
                            <div class="stat-label">Dias Médios</div>
                        </div>
                    </div>
                    <button class="btn btn-danger btn-full" onclick="filtrarArea('REJEITADO')">
                        <i class="fas fa-eye"></i> Ver Rejeitados
                    </button>
                </div>
            </div>

            <!-- Timeline de Movimentações Recentes -->
            <div class="movement-timeline">
                <h3><i class="fas fa-history"></i> Movimentações Recentes</h3>
                <div id="timelineContainer">
                    <!-- Timeline será preenchida dinamicamente -->
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="areaFilter">Área</label>
                    <select id="areaFilter">
                        <option value="">Todas as Áreas</option>
                        <option value="QUARENTENA">Quarentena</option>
                        <option value="INSPECAO">Em Inspeção</option>
                        <option value="APROVADO">Aprovados</option>
                        <option value="REJEITADO">Rejeitados</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="produtoFilter">Produto</label>
                    <input type="text" id="produtoFilter" placeholder="Nome ou código do produto">
                </div>
                <div class="filter-group">
                    <label for="loteFilter">Lote</label>
                    <input type="text" id="loteFilter" placeholder="Número do lote">
                </div>
                <div class="filter-group">
                    <label for="dataMovimento">Data Movimento</label>
                    <input type="date" id="dataMovimento">
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="novaMovimentacao()">
                    <i class="fas fa-plus"></i> Nova Movimentação
                </button>
                <button class="btn btn-warning" onclick="transferirLote()">
                    <i class="fas fa-exchange-alt"></i> Transferir Lote
                </button>
                <button class="btn btn-primary" onclick="relatorioArmazem()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-danger" onclick="alertasVencimento()">
                    <i class="fas fa-exclamation-triangle"></i> Alertas
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando armazém da qualidade...</p>
            </div>

            <!-- Tabela de Estoque -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Produto</th>
                            <th>Lote</th>
                            <th>Quantidade</th>
                            <th>Área</th>
                            <th>Data Entrada</th>
                            <th>Dias Armazém</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-warehouse"></i>
                <h3>Armazém da qualidade vazio</h3>
                <p>Não há itens no armazém da qualidade no momento.</p>
                <button class="btn btn-primary" onclick="sincronizarRecebimento()" style="margin-top: 20px;">
                    <i class="fas fa-sync"></i> Sincronizar Recebimento
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let estoqueQualidade = [];
        let movimentacoes = [];

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);
                
                // Carregar estoque e movimentações
                const [estoqueSnap, movimentacoesSnap] = await Promise.all([
                    getDocs(collection(db, "estoqueQualidade")),
                    getDocs(query(collection(db, "movimentacoesQualidade"), orderBy("dataMovimento", "desc")))
                ]);

                estoqueQualidade = estoqueSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ004 - Dados carregados:', {
                    estoque: estoqueQualidade.length,
                    movimentacoes: movimentacoes.length
                });

                updateStats();
                renderTimeline();
                renderTable();
                showLoading(false);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ004:', error);
                showLoading(false);
                showError('Erro ao carregar dados do armazém da qualidade');
            }
        }

        function updateStats() {
            const areas = ['quarentena', 'inspecao', 'aprovado', 'rejeitado'];
            const today = new Date();
            
            areas.forEach(area => {
                const areaUpper = area.toUpperCase();
                const itens = estoqueQualidade.filter(item => item.area === areaUpper);
                
                // Calcular dias médios
                const diasTotal = itens.reduce((total, item) => {
                    if (item.dataEntrada) {
                        const dataEntrada = new Date(item.dataEntrada.seconds * 1000);
                        const dias = Math.floor((today - dataEntrada) / (1000 * 60 * 60 * 24));
                        return total + dias;
                    }
                    return total;
                }, 0);
                
                const diasMedios = itens.length > 0 ? Math.round(diasTotal / itens.length) : 0;

                document.getElementById(`${area}Itens`).textContent = itens.length;
                document.getElementById(`${area}Dias`).textContent = diasMedios;
            });
        }

        function renderTimeline() {
            const container = document.getElementById('timelineContainer');
            const recentMovements = movimentacoes.slice(0, 5); // Últimas 5 movimentações

            if (recentMovements.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #7f8c8d;">Nenhuma movimentação recente</p>';
                return;
            }

            container.innerHTML = recentMovements.map(mov => {
                const dataMovimento = mov.dataMovimento ? 
                    new Date(mov.dataMovimento.seconds * 1000).toLocaleString() : 'N/A';
                
                const iconMap = {
                    'ENTRADA': 'fa-arrow-down',
                    'SAIDA': 'fa-arrow-up',
                    'TRANSFERENCIA': 'fa-exchange-alt',
                    'INSPECAO': 'fa-search'
                };

                const icon = iconMap[mov.tipo] || 'fa-circle';

                return `
                    <div class="timeline-item">
                        <div class="timeline-icon">
                            <i class="fas ${icon}"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">${mov.tipo || 'Movimentação'}</div>
                            <div class="timeline-description">
                                ${mov.produtoNome || 'Produto'} - Lote: ${mov.lote || 'N/A'} - 
                                Qtd: ${mov.quantidade || 0} ${mov.unidade || ''}
                            </div>
                        </div>
                        <div class="timeline-time">${dataMovimento}</div>
                    </div>
                `;
            }).join('');
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (estoqueQualidade.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = estoqueQualidade.map(item => {
                const dataEntrada = item.dataEntrada ? 
                    new Date(item.dataEntrada.seconds * 1000).toLocaleDateString() : 'N/A';
                
                const diasArmazem = item.dataEntrada ? 
                    Math.floor((new Date() - new Date(item.dataEntrada.seconds * 1000)) / (1000 * 60 * 60 * 24)) : 0;

                return `
                    <tr>
                        <td><strong>${item.codigo || 'N/A'}</strong></td>
                        <td>${item.produtoNome || 'N/A'}</td>
                        <td>${item.lote || 'N/A'}</td>
                        <td>${item.quantidade || 0} ${item.unidade || ''}</td>
                        <td>${item.area || 'N/A'}</td>
                        <td>${dataEntrada}</td>
                        <td>${diasArmazem} dias</td>
                        <td><span class="status-badge status-${item.status?.toLowerCase() || 'quarentena'}">${item.status || 'QUARENTENA'}</span></td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarItem('${item.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-action" onclick="moverItem('${item.id}')" title="Mover">
                                    <i class="fas fa-arrows-alt"></i>
                                </button>
                                <button class="btn btn-secondary btn-action" onclick="historicoItem('${item.id}')" title="Histórico">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Funções globais
        window.filtrarArea = function(area) {
            document.getElementById('areaFilter').value = area;
            aplicarFiltros();
        };

        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar filtros
        };

        window.novaMovimentacao = function() {
            alert('📦 Funcionalidade em desenvolvimento: Nova Movimentação');
        };

        window.transferirLote = function() {
            alert('🔄 Funcionalidade em desenvolvimento: Transferir Lote');
        };

        window.relatorioArmazem = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório do Armazém');
        };

        window.alertasVencimento = function() {
            alert('⚠️ Funcionalidade em desenvolvimento: Alertas de Vencimento');
        };

        window.sincronizarRecebimento = function() {
            alert('🔄 Funcionalidade em desenvolvimento: Sincronizar Recebimento');
        };

        window.visualizarItem = function(id) {
            alert('👁️ Funcionalidade em desenvolvimento: Visualizar Item ' + id);
        };

        window.moverItem = function(id) {
            alert('🔄 Funcionalidade em desenvolvimento: Mover Item ' + id);
        };

        window.historicoItem = function(id) {
            alert('📋 Funcionalidade em desenvolvimento: Histórico do Item ' + id);
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
