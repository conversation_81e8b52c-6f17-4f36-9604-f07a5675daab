<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Movimentação entre Armazéns</title>
    <script type="module" src="js/main.js"></script>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 0;
        }

        .form-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .form-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .form-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 25px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-weight: 600;
            position: relative;
        }

        .form-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #e74c3c, #f39c12);
            border-radius: 2px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-col {
            flex: 1;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: block;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        input, select, textarea {
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 100%;
            background: #f8f9fa;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.1);
            background: white;
            transform: translateY(-2px);
        }

        .info-box {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            padding: 15px;
            color: #2c3e50;
            font-weight: 500;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(149, 165, 166, 0.3);
            font-weight: 600;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
        }

        .table-responsive {
            margin-top: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .items-table th,
        .items-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .items-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
            position: relative;
        }

        .items-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(135deg, #e74c3c, #f39c12);
        }

        .items-table tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        /* Paginação */
        #historyPagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        #historyPagination button {
            padding: 12px 24px;
            border: 2px solid #3498db;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            color: #3498db;
        }

        #historyPagination button:hover:not(:disabled) {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        #historyPagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #bdc3c7;
            color: #bdc3c7;
        }

        /* Animações */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-container, .table-responsive {
            animation: slideIn 0.6s ease-out;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .container {
                padding: 15px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .header {
                padding: 20px;
            }

            .form-container {
                padding: 20px;
            }

            .items-table th, .items-table td {
                padding: 10px 12px;
                font-size: 13px;
            }

            .materials-table th, .materials-table td {
                padding: 8px 10px;
                font-size: 12px;
            }

            .btn-success {
                padding: 12px 20px;
                font-size: 14px;
            }
        }

        .materials-list {
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .material-item {
            display: flex;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(52, 152, 219, 0.1);
        }

        .material-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #3498db;
        }

        .material-item input[type="checkbox"] {
            margin-right: 15px;
            width: 18px;
            height: 18px;
            accent-color: #3498db;
        }

        .material-info {
            flex-grow: 1;
        }

        .material-balance {
            margin-left: 15px;
            color: #666;
            display: flex;
            gap: 15px;
        }

        .balance-info {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .balance-label {
            font-weight: bold;
            color: #2c3e50;
        }

        .balance-value {
            color: #7f8c8d;
        }

        .material-quantity {
            width: 120px;
            margin-left: 15px;
            padding: 8px;
            border-radius: 6px;
            border: 1px solid #e1e8ed;
        }

        .material-item.disabled {
            opacity: 0.5;
            pointer-events: none;
            filter: grayscale(50%);
        }

        .materials-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .materials-table th, .materials-table td {
            border: 1px solid #e1e8ed;
            padding: 12px 15px;
            text-align: left;
        }

        .materials-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .materials-table input[type="number"] {
            width: 100px;
            padding: 8px;
            border-radius: 6px;
            border: 1px solid #e1e8ed;
        }

        .materials-table input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3498db;
        }

        /* Seções específicas para tipos de movimentação */
        #opSection, #freeSection, #returnSection {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.2);
        }

        #opSection h3, #freeSection h3, #returnSection h3 {
            color: #d68910;
            margin-bottom: 15px;
            font-weight: 600;
        }

        /* Scrollbar personalizada */
        .materials-list::-webkit-scrollbar {
            width: 8px;
        }

        .materials-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .materials-list::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 10px;
        }

        .materials-list::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2980b9, #3498db);
        }

        /* Container de busca */
        .search-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .search-container label {
            color: #2c3e50;
            font-weight: 600;
            font-size: 13px;
        }

        .search-container input,
        .search-container select {
            background: white;
            border: 2px solid #e1e8ed;
            padding: 12px;
        }

        .search-container input:focus,
        .search-container select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        /* Estilos para controle de transferências */
        .transfer-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }

        .status-completa {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-parcial {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-cancelada {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .transfer-details {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .btn-view {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 5px;
        }

        .btn-view:hover {
            background: linear-gradient(135deg, #138496 0%, #17a2b8 100%);
        }

        .btn-cancel {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .btn-cancel:hover {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        }

        .loading-indicator {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }

        .loading-indicator.show {
            display: block;
        }

        .performance-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body>
    <div class="performance-info" id="performanceInfo"></div>

    <div class="container">
        <div class="header">
            <h1>Movimentação entre Armazéns</h1>
            <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
        </div>

        <div class="form-container">
            <h2 class="form-title">Nova Movimentação</h2>

            <!-- Seletor de tipo de movimentação -->
            <div class="form-row" style="margin-bottom: 20px;">
                <div class="form-col">
                    <label>Tipo de Movimentação:</label>
                    <select id="movementType" onchange="toggleMovementType()" required>
                        <option value="">Selecione o tipo...</option>
                        <option value="OP">Via Ordem de Produção</option>
                        <option value="LIVRE">Transferência Livre</option>
                        <option value="RETORNO">Retorno de Sobras</option>
                    </select>
                </div>
            </div>

            <!-- Seção para movimentação via OP -->
            <div id="opSection" style="display: none;">
                <h3 style="color: #0854a0; margin-bottom: 15px;">Movimentação via Ordem de Produção</h3>
            </div>

            <!-- Seção para transferência livre -->
            <div id="freeSection" style="display: none;">
                <h3 style="color: #0854a0; margin-bottom: 15px;">Transferência Livre</h3>
            </div>

            <!-- Seção para retorno de sobras -->
            <div id="returnSection" style="display: none;">
                <h3 style="color: #0854a0; margin-bottom: 15px;">Retorno de Sobras da Produção</h3>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                    <strong>📋 Processo:</strong> Retornar materiais não utilizados da produção para o almoxarifado
                </div>
            </div>

            <form id="movementForm" onsubmit="handleMovement(event)">
                <!-- Campos para movimentação via OP -->
                <div id="opFields" style="display: none;">
                    <div class="form-row">
                        <div class="form-col">
                            <label>Pesquisar OP ou Produto:</label>
                            <input type="text" id="searchOP" placeholder="Digite o número da OP ou código/descrição do produto..." oninput="filterOrders()">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <label>Ordem de Produção:</label>
                            <select id="productionOrderSelect" onchange="loadOrderMaterials()">
                                <option value="">Selecione a OP...</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Armazém de Produção (Destino):</label>
                            <div id="targetWarehouseInfo" class="info-box">-</div>
                            <input type="hidden" id="targetWarehouse">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Materiais da OP:</label>
                            <div id="materialsList" class="materials-list">
                                <!-- Tabela de materiais será preenchida aqui via JS -->
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Armazém Origem:</label>
                            <select id="sourceWarehouse" onchange="updateAllMaterialBalances();updateSelectedMaterialsInfo()">
                                <option value="">Selecione o armazém...</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Funcionário Responsável:</label>
                            <input type="text" id="responsibleEmployee" placeholder="Nome do funcionário que receberá os materiais" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Tipo de Transferência:</label>
                            <select id="transferType" required>
                                <option value="">Selecione o tipo...</option>
                                <option value="TOTAL">Transferência Total (todos os materiais)</option>
                                <option value="PARCIAL">Transferência Parcial (materiais selecionados)</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Observações da Transferência:</label>
                            <textarea id="transferNotes" rows="2" placeholder="Observações específicas desta transferência..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Campos para transferência livre -->
                <div id="freeFields" style="display: none;">
                    <div class="form-row">
                        <div class="form-col">
                            <label>Produto:</label>
                            <select id="freeProductSelect" onchange="loadProductInfo()">
                                <option value="">Selecione o produto...</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Quantidade:</label>
                            <input type="number" id="freeQuantity" min="0.001" step="0.001" placeholder="0.000">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Armazém Origem:</label>
                            <select id="freeSourceWarehouse" onchange="updateFreeProductBalance()">
                                <option value="">Selecione o armazém origem...</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Armazém Destino:</label>
                            <select id="freeTargetWarehouse">
                                <option value="">Selecione o armazém destino...</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Saldo Disponível:</label>
                            <div id="freeProductBalance" class="info-box">-</div>
                        </div>
                        <div class="form-col">
                            <label>Informações do Produto:</label>
                            <div id="freeProductInfo" class="info-box">-</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Funcionário Responsável:</label>
                            <input type="text" id="freeResponsibleEmployee" placeholder="Nome do funcionário que receberá o material" required>
                        </div>
                        <div class="form-col">
                            <label>Observações da Transferência:</label>
                            <textarea id="freeTransferNotes" rows="2" placeholder="Observações específicas desta transferência..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Campos para retorno de sobras -->
                <div id="returnFields" style="display: none;">
                    <div class="form-row">
                        <div class="form-col">
                            <label>Ordem de Produção:</label>
                            <select id="returnOrderSelect" onchange="loadReturnOrderInfo()">
                                <option value="">Selecione a OP que gerou as sobras...</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Status da OP:</label>
                            <div id="returnOrderStatus" class="info-box">-</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Armazém de Produção (Origem):</label>
                            <div id="returnSourceInfo" class="info-box">-</div>
                            <input type="hidden" id="returnSourceWarehouse">
                        </div>
                        <div class="form-col">
                            <label>Armazém Destino:</label>
                            <select id="returnTargetWarehouse">
                                <option value="">Selecione o armazém destino...</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Materiais Disponíveis para Retorno:</label>
                            <div id="returnMaterialsList" class="materials-list">
                                <div style="text-align: center; color: #666; padding: 20px;">
                                    Selecione uma OP para ver os materiais disponíveis
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <label>Condição do Material:</label>
                            <select id="materialCondition" required>
                                <option value="">Selecione a condição...</option>
                                <option value="BOM">Bom Estado - Pode ser reutilizado</option>
                                <option value="DANIFICADO">Danificado - Necessita inspeção</option>
                                <option value="DESCARTE">Para Descarte - Não reutilizável</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <label>Responsável pelo Retorno:</label>
                            <input type="text" id="returnResponsible" placeholder="Nome do responsável" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Motivo da Transferência:</label>
                    <textarea id="reason" rows="3" required></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-success">Realizar Transferência</button>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <h2 class="form-title">Histórico de Transferências</h2>

            <!-- Filtros de busca -->
            <div class="search-container" style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <div class="form-row">
                    <div class="form-col">
                        <label>Buscar por Produto:</label>
                        <input type="text" id="searchProduct" placeholder="Digite código ou descrição do produto..." oninput="searchHistory()">
                    </div>
                    <div class="form-col">
                        <label>Período:</label>
                        <select id="periodFilter" onchange="searchHistory()">
                            <option value="7">Últimos 7 dias</option>
                            <option value="30">Últimos 30 dias</option>
                            <option value="90">Últimos 90 dias</option>
                            <option value="all">Todos os registros</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label>Tipo de Movimentação:</label>
                        <select id="typeFilter" onchange="searchHistory()">
                            <option value="">Todos os tipos</option>
                            <option value="OP">Via Ordem de Produção</option>
                            <option value="LIVRE">Transferência Livre</option>
                            <option value="RETORNO_SOBRAS">Retorno de Sobras</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label>Armazém:</label>
                        <select id="warehouseFilter" onchange="searchHistory()">
                            <option value="">Todos os armazéns</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <button type="button" class="btn-secondary" onclick="clearFilters()">Limpar Filtros</button>
                    </div>
                    <div class="form-col" style="text-align: right;">
                        <span id="resultsCount" style="color: #666; font-weight: 500;">-</span>
                    </div>
                </div>
            </div>

            <table class="items-table">
    <thead>
        <tr>
            <th>Data/Hora</th>
            <th>OP</th>
            <th>Produto</th>
            <th>Qtd Solicitada</th>
            <th>Qtd Transferida</th>
            <th>Status</th>
            <th>Origem</th>
            <th>Destino</th>
            <th>Funcionário</th>
            <th>Motivo</th>
            <th>Ações</th>
        </tr>
    </thead>
    <tbody id="historyTableBody"></tbody>
</table>
<div id="historyPagination" style="margin-top: 10px; text-align: right;">
  <button id="prevPageBtn" disabled>Anterior</button>
  <span id="historyPageInfo"></span>
  <button id="nextPageBtn" disabled>Próxima</button>
</div>
        </div>

        <!-- Tabela de Saldos movida para o final -->
        <div class="form-container" style="margin-top: 30px;">
            <h2 class="form-title">Saldos por Armazém</h2>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <strong>ℹ️ Informação:</strong> Esta tabela mostra os saldos atuais de todos os produtos em estoque nos armazéns.
            </div>
            <div class="table-responsive">
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Produto</th>
                            <th>Tipo</th>
                            <th>Unidade</th>
                            <th>Armazém</th>
                            <th>Saldo</th>
                        </tr>
                    </thead>
                    <tbody id="balanceTableBody">
                        <tr class="loading-indicator show">
                            <td colspan="5">⏳ Carregando saldos...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc,
            getDoc,
            getDocs,
            query,
            where,
            doc, 
            updateDoc,
            Timestamp,
            orderBy,
            onSnapshot,
            deleteDoc 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let armazens = [];
        let estoques = [];
        let ordensProducao = [];
        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let selectedOrder = null;

        let performanceMetrics = {
            loadStart: Date.now(),
            dataLoaded: null,
            interfaceReady: null
        };

        function showPerformanceInfo() {
            const info = document.getElementById('performanceInfo');
            if (performanceMetrics.interfaceReady) {
                const totalTime = performanceMetrics.interfaceReady - performanceMetrics.loadStart;
                info.textContent = `Carregado em ${totalTime}ms`;
                info.style.display = 'block';
                setTimeout(() => {
                    info.style.display = 'none';
                }, 3000);
            }
        }

        window.onload = async function() {
            if (!currentUser) {
                window.location.href = './login.html';
                return;
            }

            const parametrosDoc = await getDoc(doc(db, "parametros", "sistema"));
            if (!parametrosDoc.exists() || !parametrosDoc.data().configuracaoSistema?.controleArmazem) {
                alert('O controle de armazém não está ativo nas configurações do sistema.');
                window.location.href = './index.html';
                return;
            }

            await loadInitialData();
            setupRealTimeListeners();
            performanceMetrics.interfaceReady = Date.now();
            showPerformanceInfo();
        };

        async function loadInitialData() {
            try {
                // Mostrar loading
                document.body.style.cursor = 'wait';

                // Carregar dados essenciais primeiro (mais rápido)
                const [armazensSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "produtos"))
                ]);

                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar dados secundários em background
                loadSecondaryData();

                // Popular interface básica
                populateSourceWarehouseSelect();
                populateFreeTransferSelects();
                loadHistory();

            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados iniciais.");
            } finally {
                document.body.style.cursor = 'default';
            }
        }

        async function loadSecondaryData() {
            try {
                // Carregar estoques e ordens em paralelo, mas sem bloquear a interface
                const [estoquesSnap, ordensSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "ordensProducao"))
                ]);

                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Atualizar interface com dados completos
                populateOrderSelect();
                // Lazy load da tabela de saldos - só carrega quando necessário
                setupLazyBalanceTable();

            } catch (error) {
                console.error("Erro ao carregar dados secundários:", error);
            }
        }

        let updateTimeout;

        function setupRealTimeListeners() {
            // Listener para estoques com debounce
            onSnapshot(collection(db, "estoques"), (snapshot) => {
                estoques = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Debounce para evitar atualizações excessivas
                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(() => {
                    if (balanceTableLoaded) {
                        loadBalanceTable();
                    }
                    if (selectedOrder) {
                        loadOrderMaterials();
                    }
                }, 500);
            });

            // Listener para ordens de produção com debounce
            onSnapshot(collection(db, "ordensProducao"), (snapshot) => {
                ordensProducao = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(() => {
                    populateOrderSelect();
                }, 500);
            });

            // Listener para transferências - só quando necessário
            onSnapshot(collection(db, "transferenciasArmazem"), (snapshot) => {
                if (isSearching || filteredTransfers.length > 0) {
                    clearTimeout(updateTimeout);
                    updateTimeout = setTimeout(() => {
                        searchHistory();
                    }, 1000);
                }
            });
        }

        let balanceTableLoaded = false;

        function setupLazyBalanceTable() {
            const balanceSection = document.querySelector('.balance-section');
            if (!balanceSection) return;

            // Criar observer para carregar tabela quando visível
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !balanceTableLoaded) {
                        loadBalanceTable();
                        balanceTableLoaded = true;
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            observer.observe(balanceSection);
        }

        function populateOrderSelect() {
            const select = document.getElementById('productionOrderSelect');
            if (!select) return;

            // Usar DocumentFragment para melhor performance
            const fragment = document.createDocumentFragment();
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Selecione a OP...';
            fragment.appendChild(defaultOption);

            if (ordensProducao.length === 0) {
                select.innerHTML = '';
                select.appendChild(fragment);
                return;
            }

            // Agrupar ordens por status (sem logs excessivos)
            const ordensPendentes = ordensProducao
                .filter(op => op.status === 'Pendente' || op.status === 'Firme')
                .sort((a, b) => {
                    const dateA = a.dataEntrega?.seconds || 0;
                    const dateB = b.dataEntrega?.seconds || 0;
                    return dateA - dateB;
                });

            const ordensEmProducao = ordensProducao
                .filter(op => op.status === 'Em Produção')
                .sort((a, b) => {
                    const dateA = a.dataEntrega?.seconds || 0;
                    const dateB = b.dataEntrega?.seconds || 0;
                    return dateA - dateB;
                });

            // Criar mapa de produtos para lookup mais rápido
            const produtoMap = new Map(produtos.map(p => [p.id, p]));

            // Adicionar grupo de ordens pendentes/firmes
            if (ordensPendentes.length > 0) {
                const optgroup = document.createElement('optgroup');
                optgroup.label = 'Ordens Pendentes/Firmes';

                ordensPendentes.forEach(op => {
                    const produto = produtoMap.get(op.produtoId);
                    const dataEntrega = op.dataEntrega?.seconds ?
                        new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'N/A';

                    const option = document.createElement('option');
                    option.value = op.id;
                    option.textContent = `${op.numero} - ${produto?.codigo || 'N/A'} - ${produto?.descricao || ''} (Entrega: ${dataEntrega})`;
                    optgroup.appendChild(option);
                });

                fragment.appendChild(optgroup);
            }

            // Adicionar grupo de ordens em produção
            if (ordensEmProducao.length > 0) {
                const optgroup = document.createElement('optgroup');
                optgroup.label = 'Ordens em Produção';

                ordensEmProducao.forEach(op => {
                    const produto = produtoMap.get(op.produtoId);
                    const dataEntrega = op.dataEntrega?.seconds ?
                        new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'N/A';
                    const progresso = op.quantidadeProduzida ?
                        ((op.quantidadeProduzida / op.quantidade) * 100).toFixed(1) : 0;

                    const option = document.createElement('option');
                    option.value = op.id;
                    option.textContent = `${op.numero} - ${produto?.codigo || 'N/A'} - ${produto?.descricao || ''} (${progresso}% - Entrega: ${dataEntrega})`;
                    optgroup.appendChild(option);
                });

                fragment.appendChild(optgroup);
            }

            // Aplicar todas as mudanças de uma vez
            select.innerHTML = '';
            select.appendChild(fragment);
        }

        function populateSourceWarehouseSelect() {
            const sourceWarehouse = document.getElementById('sourceWarehouse');
            sourceWarehouse.innerHTML = '<option value="">Selecione o armazém...</option>';

            armazens
                .filter(a => a.tipo !== 'PRODUCAO') // Exclui armazéns tipo Produção para origem
                .forEach(armazem => {
                    sourceWarehouse.innerHTML += `
                        <option value="${armazem.id}">
                            ${armazem.codigo} - ${armazem.nome}
                        </option>`;
                });
        }

        function loadBalanceTable() {
            const tableBody = document.getElementById('balanceTableBody');
            if (!tableBody) return;

            // Usar DocumentFragment para melhor performance
            const fragment = document.createDocumentFragment();

            // Criar mapas para lookup mais rápido
            const produtoMap = new Map(produtos.map(p => [p.id, p]));
            const armazemMap = new Map(armazens.map(a => [a.id, a]));

            // Agrupar estoques por produto (mais eficiente)
            const produtoEstoques = new Map();

            for (const estoque of estoques) {
                if (estoque.saldo > 0) {
                    if (!produtoEstoques.has(estoque.produtoId)) {
                        produtoEstoques.set(estoque.produtoId, []);
                    }
                    produtoEstoques.get(estoque.produtoId).push(estoque);
                }
            }

            if (produtoEstoques.size === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" style="text-align: center; color: #666;">Nenhum produto com saldo em estoque</td>';
                fragment.appendChild(row);
                tableBody.innerHTML = '';
                tableBody.appendChild(fragment);
                return;
            }

            // Ordenar produtos por código
            const produtosComEstoque = Array.from(produtoEstoques.keys())
                .map(produtoId => produtoMap.get(produtoId))
                .filter(produto => produto)
                .sort((a, b) => (a.codigo || '').localeCompare(b.codigo || ''));

            for (const produto of produtosComEstoque) {
                const estoquesProduct = produtoEstoques.get(produto.id);

                for (let index = 0; index < estoquesProduct.length; index++) {
                    const estoque = estoquesProduct[index];
                    const armazem = armazemMap.get(estoque.armazemId);

                    const row = document.createElement('tr');

                    // Criar células individualmente para melhor performance
                    const cells = [
                        index === 0 ? (produto.codigo || 'SEM CÓDIGO') : '',
                        index === 0 ? (produto.tipo || 'N/A') : '',
                        index === 0 ? (produto.unidade || 'UN') : '',
                        armazem ? `${armazem.codigo} - ${armazem.nome}` : 'Armazém não encontrado',
                        estoque.saldo.toFixed(3)
                    ];

                    cells.forEach((cellText, cellIndex) => {
                        const cell = document.createElement('td');
                        cell.textContent = cellText;

                        if (cellIndex === 4) { // Coluna de saldo
                            cell.style.textAlign = 'right';
                            cell.style.fontWeight = 'bold';
                            if (estoque.saldo < 10) {
                                cell.style.color = '#d32f2f';
                            }
                        }

                        row.appendChild(cell);
                    });

                    // Destacar linha se for armazém de produção
                    if (armazem?.tipo === 'PRODUCAO') {
                        row.style.backgroundColor = '#e8f5e8';
                    }

                    fragment.appendChild(row);
                }
            }

            // Aplicar todas as mudanças de uma vez
            tableBody.innerHTML = '';
            tableBody.appendChild(fragment);
        }

        function populateFreeTransferSelects() {
            // Popular select de produtos
            const productSelect = document.getElementById('freeProductSelect');
            if (!productSelect) return;

            const productFragment = document.createDocumentFragment();
            const defaultProductOption = document.createElement('option');
            defaultProductOption.value = '';
            defaultProductOption.textContent = 'Selecione o produto...';
            productFragment.appendChild(defaultProductOption);

            const produtosAtivos = produtos
                .filter(p => p.ativo !== false)
                .sort((a, b) => (a.codigo || '').localeCompare(b.codigo || ''));

            produtosAtivos.forEach(produto => {
                const option = document.createElement('option');
                option.value = produto.id;
                option.textContent = `${produto.codigo} - ${produto.descricao}`;
                productFragment.appendChild(option);
            });

            productSelect.innerHTML = '';
            productSelect.appendChild(productFragment);

            // Popular selects de armazéns
            const sourceSelect = document.getElementById('freeSourceWarehouse');
            const targetSelect = document.getElementById('freeTargetWarehouse');

            if (!sourceSelect || !targetSelect) return;

            const armazemFragment = document.createDocumentFragment();
            const defaultArmazemOption = document.createElement('option');
            defaultArmazemOption.value = '';
            defaultArmazemOption.textContent = 'Selecione o armazém...';

            const armazensAtivos = armazens.filter(a => a.ativo !== false);

            armazensAtivos.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                armazemFragment.appendChild(option);
            });

            // Aplicar para source
            const sourceFragment = armazemFragment.cloneNode(true);
            const sourceDefault = defaultArmazemOption.cloneNode(true);
            sourceDefault.textContent = 'Selecione o armazém origem...';
            sourceFragment.insertBefore(sourceDefault, sourceFragment.firstChild);
            sourceSelect.innerHTML = '';
            sourceSelect.appendChild(sourceFragment);

            // Aplicar para target
            const targetFragment = armazemFragment.cloneNode(true);
            const targetDefault = defaultArmazemOption.cloneNode(true);
            targetDefault.textContent = 'Selecione o armazém destino...';
            targetFragment.insertBefore(targetDefault, targetFragment.firstChild);
            targetSelect.innerHTML = '';
            targetSelect.appendChild(targetFragment);
        }

        // Função para alternar entre tipos de movimentação
        window.toggleMovementType = function() {
            try {
                const movementType = document.getElementById('movementType').value;
                const opSection = document.getElementById('opSection');
                const freeSection = document.getElementById('freeSection');
                const returnSection = document.getElementById('returnSection');
                const opFields = document.getElementById('opFields');
                const freeFields = document.getElementById('freeFields');
                const returnFields = document.getElementById('returnFields');

                // Verificar se todos os elementos existem
                if (!opSection || !freeSection || !returnSection || !opFields || !freeFields || !returnFields) {
                    console.error('Alguns elementos da interface não foram encontrados');
                    return;
                }

            // Esconder todas as seções
            opSection.style.display = 'none';
            freeSection.style.display = 'none';
            returnSection.style.display = 'none';
            opFields.style.display = 'none';
            freeFields.style.display = 'none';
            returnFields.style.display = 'none';

            // Limpar campos
            document.getElementById('productionOrderSelect').value = '';
            document.getElementById('freeProductSelect').value = '';
            document.getElementById('freeQuantity').value = '';
            document.getElementById('freeSourceWarehouse').value = '';
            document.getElementById('freeTargetWarehouse').value = '';
            document.getElementById('returnOrderSelect').value = '';
            document.getElementById('returnTargetWarehouse').value = '';
            document.getElementById('materialCondition').value = '';
            document.getElementById('returnResponsible').value = '';
            document.getElementById('reason').value = '';
            document.getElementById('responsibleEmployee').value = '';
            document.getElementById('transferType').value = '';
            document.getElementById('transferNotes').value = '';
            document.getElementById('freeResponsibleEmployee').value = '';
            document.getElementById('freeTransferNotes').value = '';

            // Resetar campos obrigatórios
            document.getElementById('productionOrderSelect').required = false;
            document.getElementById('freeProductSelect').required = false;
            document.getElementById('freeQuantity').required = false;
            document.getElementById('freeSourceWarehouse').required = false;
            document.getElementById('freeTargetWarehouse').required = false;
            document.getElementById('returnOrderSelect').required = false;
            document.getElementById('returnTargetWarehouse').required = false;
            document.getElementById('materialCondition').required = false;
            document.getElementById('returnResponsible').required = false;
            document.getElementById('responsibleEmployee').required = false;
            document.getElementById('transferType').required = false;
            document.getElementById('freeResponsibleEmployee').required = false;

            // Mostrar seção apropriada
            if (movementType === 'OP') {
                opSection.style.display = 'block';
                opFields.style.display = 'block';
                document.getElementById('productionOrderSelect').required = true;
                document.getElementById('responsibleEmployee').required = true;
                document.getElementById('transferType').required = true;
            } else if (movementType === 'LIVRE') {
                freeSection.style.display = 'block';
                freeFields.style.display = 'block';
                document.getElementById('freeProductSelect').required = true;
                document.getElementById('freeQuantity').required = true;
                document.getElementById('freeSourceWarehouse').required = true;
                document.getElementById('freeTargetWarehouse').required = true;
                document.getElementById('freeResponsibleEmployee').required = true;
            } else if (movementType === 'RETORNO') {
                returnSection.style.display = 'block';
                returnFields.style.display = 'block';
                document.getElementById('returnOrderSelect').required = true;
                document.getElementById('returnTargetWarehouse').required = true;
                document.getElementById('materialCondition').required = true;
                document.getElementById('returnResponsible').required = true;
                populateReturnSelects();
            }
            } catch (error) {
                console.error('Erro em toggleMovementType:', error);
                alert('Erro ao alternar tipo de movimentação. Recarregue a página.');
            }
        };

        // Função para carregar informações do produto na transferência livre
        window.loadProductInfo = function() {
            const produtoId = document.getElementById('freeProductSelect').value;
            const productInfo = document.getElementById('freeProductInfo');

            if (!produtoId) {
                productInfo.textContent = '-';
                document.getElementById('freeProductBalance').textContent = '-';
                return;
            }

            const produto = produtos.find(p => p.id === produtoId);
            if (produto) {
                productInfo.textContent = `${produto.tipo || 'N/A'} - ${produto.unidade || 'UN'}`;
                updateFreeProductBalance();
            }
        };

        // Função para atualizar saldo do produto na transferência livre
        window.updateFreeProductBalance = function() {
            const produtoId = document.getElementById('freeProductSelect').value;
            const armazemId = document.getElementById('freeSourceWarehouse').value;
            const balanceDiv = document.getElementById('freeProductBalance');

            if (!produtoId || !armazemId) {
                balanceDiv.textContent = '-';
                return;
            }

            const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
            const produto = produtos.find(p => p.id === produtoId);
            const saldo = estoque ? estoque.saldo : 0;

            balanceDiv.textContent = `${saldo.toFixed(3)} ${produto ? produto.unidade : 'UN'}`;
            balanceDiv.style.color = saldo > 0 ? '#107e3e' : '#bb0000';
        };

        // Funções para retorno de sobras
        function populateReturnSelects() {
            try {
                // Popular select de ordens de produção (em produção ou finalizadas)
                const returnOrderSelect = document.getElementById('returnOrderSelect');
                if (!returnOrderSelect) {
                    console.error('Elemento returnOrderSelect não encontrado');
                    return;
                }

                returnOrderSelect.innerHTML = '<option value="">Selecione a OP que gerou as sobras...</option>';

                if (!ordensProducao || ordensProducao.length === 0) {
                    console.warn('Nenhuma ordem de produção encontrada');
                    return;
                }

                const ordensComSobras = ordensProducao.filter(op => {
                    try {
                        return (op.status === 'Em Produção' || op.status === 'Finalizada') &&
                               op.armazemProducaoId;
                    } catch (error) {
                        console.warn('Erro ao filtrar OP:', op, error);
                        return false;
                    }
                });

                // Ordenar por data mais recente (se disponível)
                ordensComSobras.sort((a, b) => {
                    try {
                        const dateA = a.dataInicio?.seconds || a.dataCriacao?.seconds || 0;
                        const dateB = b.dataInicio?.seconds || b.dataCriacao?.seconds || 0;
                        return dateB - dateA;
                    } catch (error) {
                        return 0;
                    }
                });

                ordensComSobras.forEach(op => {
                    try {
                        const produto = produtos.find(p => p.id === op.produtoId);
                        let dataStr = 'N/A';

                        if (op.dataInicio?.seconds) {
                            dataStr = new Date(op.dataInicio.seconds * 1000).toLocaleDateString('pt-BR');
                        } else if (op.dataCriacao?.seconds) {
                            dataStr = new Date(op.dataCriacao.seconds * 1000).toLocaleDateString('pt-BR');
                        }

                        const produtoCodigo = produto ? produto.codigo : 'N/A';
                        const produtoDesc = produto ? produto.descricao : 'N/A';

                        returnOrderSelect.innerHTML += `
                            <option value="${op.id}">
                                ${op.numero || 'N/A'} - ${produtoCodigo} - ${produtoDesc} (${op.status} - ${dataStr})
                            </option>`;
                    } catch (error) {
                        console.warn('Erro ao processar OP:', op, error);
                    }
                });

                // Popular select de armazéns destino (exceto produção)
                const returnTargetSelect = document.getElementById('returnTargetWarehouse');
                if (!returnTargetSelect) {
                    console.error('Elemento returnTargetWarehouse não encontrado');
                    return;
                }

                returnTargetSelect.innerHTML = '<option value="">Selecione o armazém destino...</option>';

                if (!armazens || armazens.length === 0) {
                    console.warn('Nenhum armazém encontrado');
                    return;
                }

                armazens
                    .filter(a => {
                        try {
                            return a.ativo !== false && a.tipo !== 'PRODUCAO';
                        } catch (error) {
                            console.warn('Erro ao filtrar armazém:', a, error);
                            return false;
                        }
                    })
                    .forEach(armazem => {
                        try {
                            returnTargetSelect.innerHTML += `
                                <option value="${armazem.id}">
                                    ${armazem.codigo || 'N/A'} - ${armazem.nome || 'N/A'}
                                </option>`;
                        } catch (error) {
                            console.warn('Erro ao processar armazém:', armazem, error);
                        }
                    });

            } catch (error) {
                console.error('Erro em populateReturnSelects:', error);
                alert('Erro ao carregar dados para retorno de sobras. Verifique o console para mais detalhes.');
            }
        }

        window.loadReturnOrderInfo = function() {
            const orderId = document.getElementById('returnOrderSelect').value;
            const statusDiv = document.getElementById('returnOrderStatus');
            const sourceInfo = document.getElementById('returnSourceInfo');
            const sourceWarehouse = document.getElementById('returnSourceWarehouse');
            const materialsList = document.getElementById('returnMaterialsList');

            // Limpar campos
            statusDiv.textContent = '-';
            sourceInfo.textContent = '-';
            sourceWarehouse.value = '';
            materialsList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">Selecione uma OP para ver os materiais disponíveis</div>';

            if (!orderId) return;

            const ordem = ordensProducao.find(op => op.id === orderId);
            if (!ordem) return;

            // Mostrar informações da OP
            statusDiv.textContent = ordem.status;
            statusDiv.style.color = ordem.status === 'Finalizada' ? '#107e3e' : '#ff9800';

            const armazemProducao = armazens.find(a => a.id === ordem.armazemProducaoId);
            if (armazemProducao) {
                sourceInfo.textContent = `${armazemProducao.codigo} - ${armazemProducao.nome}`;
                sourceWarehouse.value = armazemProducao.id;
            }

            // Carregar materiais disponíveis no armazém de produção
            loadReturnMaterials(ordem.armazemProducaoId);
        };

        function loadReturnMaterials(armazemProducaoId) {
            const materialsList = document.getElementById('returnMaterialsList');

            // Buscar todos os materiais com saldo no armazém de produção
            const materiaisDisponiveis = estoques
                .filter(e => e.armazemId === armazemProducaoId && e.saldo > 0)
                .map(estoque => {
                    const produto = produtos.find(p => p.id === estoque.produtoId);
                    return { ...estoque, produto };
                })
                .filter(item => item.produto)
                .sort((a, b) => (a.produto.codigo || '').localeCompare(b.produto.codigo || ''));

            if (materiaisDisponiveis.length === 0) {
                materialsList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">Nenhum material disponível para retorno neste armazém</div>';
                return;
            }

            // Criar tabela de materiais
            let table = document.createElement('table');
            table.className = 'materials-table';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th></th>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Saldo Disponível</th>
                        <th>Quantidade a Retornar</th>
                    </tr>
                </thead>
                <tbody></tbody>
            `;

            materiaisDisponiveis.forEach(material => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" id="return_material_${material.produtoId}"
                               onchange="updateReturnMaterialSelection('${material.produtoId}')">
                    </td>
                    <td>${material.produto.codigo}</td>
                    <td>${material.produto.descricao}</td>
                    <td>${material.saldo.toFixed(3)} ${material.produto.unidade}</td>
                    <td>
                        <input type="number" class="return-quantity"
                               id="return_quantity_${material.produtoId}"
                               value="0.000" min="0.001" step="0.001"
                               max="${material.saldo}"
                               onchange="validateReturnQuantity('${material.produtoId}', ${material.saldo})"
                               disabled>
                    </td>
                `;
                table.querySelector('tbody').appendChild(row);
            });

            materialsList.innerHTML = '';
            materialsList.appendChild(table);
        }

        window.updateReturnMaterialSelection = function(materialId) {
            const checkbox = document.getElementById(`return_material_${materialId}`);
            const quantityInput = document.getElementById(`return_quantity_${materialId}`);

            if (checkbox.checked) {
                quantityInput.disabled = false;
                quantityInput.value = quantityInput.max; // Preencher com saldo total
            } else {
                quantityInput.disabled = true;
                quantityInput.value = '0.000';
            }
        };

        window.validateReturnQuantity = function(materialId, maxQuantity) {
            const quantityInput = document.getElementById(`return_quantity_${materialId}`);
            const value = parseFloat(quantityInput.value);

            if (value > maxQuantity) {
                alert(`A quantidade não pode ser maior que ${maxQuantity.toFixed(3)}`);
                quantityInput.value = maxQuantity.toFixed(3);
            }

            if (value <= 0) {
                quantityInput.value = '0.001';
            }
        };

        window.loadOrderMaterials = function() {
            const orderId = document.getElementById('productionOrderSelect').value;
            const materialsList = document.getElementById('materialsList');
            const targetWarehouseInfo = document.getElementById('targetWarehouseInfo');
            const targetWarehouse = document.getElementById('targetWarehouse');

            materialsList.innerHTML = '';
            targetWarehouseInfo.textContent = '-';
            targetWarehouse.value = '';
            document.getElementById('sourceWarehouse').value = '';

            if (!orderId) {
                selectedOrder = null;
                return;
            }

            selectedOrder = ordensProducao.find(op => op.id === orderId);
            const armazemProducao = armazens.find(a => a.id === selectedOrder.armazemProducaoId);

            if (!armazemProducao || armazemProducao.tipo !== 'PRODUCAO') {
                alert('A ordem de produção selecionada não está associada a um armazém do tipo Produção.');
                document.getElementById('productionOrderSelect').value = '';
                selectedOrder = null;
                return;
            }

            targetWarehouseInfo.textContent = `${armazemProducao.codigo} - ${armazemProducao.nome}`;
            targetWarehouse.value = armazemProducao.id;

            if (selectedOrder.materiaisNecessarios) {
    // Montar tabela
    let table = document.createElement('table');
    table.className = 'materials-table';
    table.innerHTML = `
      <thead>
        <tr>
          <th></th>
          <th>Código</th>
          <th>Descrição</th>
          <th>Origem</th>
          <th>Produção</th>
          <th>Qtd</th>
        </tr>
      </thead>
      <tbody></tbody>
    `;
    selectedOrder.materiaisNecessarios
      .filter(material => material.necessidade > 0)
      .forEach(material => {
        const produto = produtos.find(p => p.id === material.produtoId);
        const sourceId = document.getElementById('sourceWarehouse').value;
        let saldoOrigem = '-';
        if (sourceId) {
            const sourceEstoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === sourceId);
            saldoOrigem = sourceEstoque ? `${sourceEstoque.saldo.toFixed(3)} ${produto.unidade}` : `0.000 ${produto.unidade}`;
        } else {
            // Soma de todos os armazéns de origem possíveis (exceto produção)
            const saldos = estoques
                .filter(e => e.produtoId === material.produtoId && armazens.find(a => a.id === e.armazemId && a.tipo !== 'PRODUCAO'))
                .map(e => e.saldo);
            const saldoTotal = saldos.reduce((a, b) => a + b, 0);
            saldoOrigem = `${saldoTotal.toFixed(3)} ${produto.unidade}`;
        }
        // Saldo produção
        const targetId = document.getElementById('targetWarehouse')?.value;
        let saldoProducao = '-';
        if (targetId) {
            const targetEstoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === targetId);
            saldoProducao = targetEstoque ? `${targetEstoque.saldo.toFixed(3)} ${produto.unidade}` : `0.000 ${produto.unidade}`;
        }
        const saldoNum = parseFloat(saldoOrigem.replace(/[^\d\.-]/g, ''));
const podeMover = saldoNum >= material.necessidade && saldoNum > 0;
const tooltip = podeMover ? '' : `title='Saldo insuficiente para transferência'`;
const disabledAttr = podeMover ? '' : 'disabled';
const row = document.createElement('tr');
row.innerHTML = `
  <td><input type="checkbox" id="material_${material.produtoId}" onchange="updateMaterialSelection('${material.produtoId}')" ${disabledAttr} ${tooltip}></td>
  <td>${produto.codigo}</td>
  <td>${produto.descricao}</td>
  <td><span id="balance_origem_${material.produtoId}">${saldoOrigem}</span></td>
  <td><span id="balance_producao_${material.produtoId}">${saldoProducao}</span></td>
  <td><input type="number" class="material-quantity" id="quantity_${material.produtoId}" value="${material.necessidade.toFixed(3)}" min="0.001" step="0.001" onchange="validateQuantity('${material.produtoId}', ${material.necessidade})" ${disabledAttr} ${tooltip}></td>
`;
table.querySelector('tbody').appendChild(row);
      });
    materialsList.innerHTML = '';
    materialsList.appendChild(table);
}

        };

        // Atualiza os saldos ao trocar o armazém de origem
        window.updateAllMaterialBalances = function() {
            if (!selectedOrder) return;
            const sourceId = document.getElementById('sourceWarehouse').value;
            selectedOrder.materiaisNecessarios
              .filter(material => material.necessidade > 0)
              .forEach(material => {
                  const produto = produtos.find(p => p.id === material.produtoId);
                  let saldoOrigem = '-';
                  if (sourceId) {
                      const sourceEstoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === sourceId);
                      saldoOrigem = sourceEstoque ? `${sourceEstoque.saldo.toFixed(3)} ${produto.unidade}` : `0.000 ${produto.unidade}`;
                  }
                  const saldoSpan = document.getElementById(`balance_origem_${material.produtoId}`);
                  if (saldoSpan) saldoSpan.textContent = saldoOrigem;
              });
        };

        window.updateMaterialSelection = function(materialId) {
            const checkbox = document.getElementById(`material_${materialId}`);
            const quantityInput = document.getElementById(`quantity_${materialId}`);
            const materialItem = checkbox.closest('.material-item');
            
            if (checkbox.checked) {
                materialItem.classList.remove('disabled');
                quantityInput.disabled = false;
            } else {
                materialItem.classList.add('disabled');
                quantityInput.disabled = true;
            }
            
            updateSelectedMaterialsInfo();
        };

        window.validateQuantity = function(materialId, maxQuantity) {
            const quantityInput = document.getElementById(`quantity_${materialId}`);
            const value = parseFloat(quantityInput.value);
            
            if (value > maxQuantity) {
                alert(`A quantidade não pode ser maior que ${maxQuantity.toFixed(3)}`);
                quantityInput.value = maxQuantity.toFixed(3);
            }
            
            updateSelectedMaterialsInfo();
        };

        window.updateSelectedMaterialsInfo = function() {
            const sourceId = document.getElementById('sourceWarehouse').value;
            const targetId = document.getElementById('targetWarehouse').value;
            if (!sourceId || !targetId) return;

            const checkboxes = document.querySelectorAll('#materialsList input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const materialId = checkbox.id.split('_')[1];
                    const produto = produtos.find(p => p.id === materialId);
                    
                    // Saldo no armazém de origem
                    const sourceEstoque = estoques.find(e => e.produtoId === materialId && e.armazemId === sourceId);
                    const saldoOrigem = sourceEstoque ? sourceEstoque.saldo : 0;
                    document.getElementById(`balance_origem_${materialId}`).textContent = 
                        `${saldoOrigem.toFixed(3)} ${produto.unidade}`;

                    // Saldo no armazém de produção
                    const targetEstoque = estoques.find(e => e.produtoId === materialId && e.armazemId === targetId);
                    const saldoProducao = targetEstoque ? targetEstoque.saldo : 0;
                    document.getElementById(`balance_producao_${materialId}`).textContent = 
                        `${saldoProducao.toFixed(3)} ${produto.unidade}`;
                }
            });
        };

        window.handleMovement = async function(event) {
            event.preventDefault();

            const movementType = document.getElementById('movementType').value;
            const motivo = document.getElementById('reason').value;

            if (!movementType) {
                alert('Selecione o tipo de movimentação.');
                return;
            }

            if (!motivo.trim()) {
                alert('Informe o motivo da transferência.');
                return;
            }

            if (movementType === 'OP') {
                await handleOPMovement();
            } else if (movementType === 'LIVRE') {
                await handleFreeMovement();
            } else if (movementType === 'RETORNO') {
                await handleReturnMovement();
            }
        };

        async function handleOPMovement() {
            const orderId = document.getElementById('productionOrderSelect').value;
            const sourceId = document.getElementById('sourceWarehouse').value;
            const targetId = document.getElementById('targetWarehouse').value;
            const motivo = document.getElementById('reason').value;
            const responsibleEmployee = document.getElementById('responsibleEmployee').value.trim();
            const transferType = document.getElementById('transferType').value;
            const transferNotes = document.getElementById('transferNotes').value.trim();

            if (!orderId || !sourceId || !targetId || !responsibleEmployee || !transferType) {
                alert('Preencha todos os campos obrigatórios para movimentação via OP.');
                return;
            }

            const selectedMaterials = Array.from(document.querySelectorAll('#materialsList input[type="checkbox"]:checked'))
                .map(checkbox => {
                    const materialId = checkbox.id.split('_')[1];
                    const quantity = parseFloat(document.getElementById(`quantity_${materialId}`).value);
                    return { materialId, quantity };
                });

            if (selectedMaterials.length === 0) {
                alert('Selecione pelo menos um material para transferir.');
                return;
            }

            try {
                for (const { materialId, quantity } of selectedMaterials) {
                    const material = selectedOrder.materiaisNecessarios.find(m => m.produtoId === materialId);
                    if (quantity > material.necessidade) {
                        alert(`A quantidade do material ${materialId} não pode exceder a necessidade da ordem de produção.`);
                        return;
                    }

                    const sourceEstoque = estoques.find(e => e.produtoId === materialId && e.armazemId === sourceId);
                    if (!sourceEstoque || sourceEstoque.saldo < quantity) {
                        const produto = produtos.find(p => p.id === materialId);
                        alert(`Saldo insuficiente para o material ${produto.codigo} - ${produto.descricao}.`);
                        return;
                    }

                    // Calcular quantidade solicitada original
                    const quantidadeSolicitada = material.necessidade + (material.saldoReservado || 0);
                    const quantidadeJaTransferida = material.saldoReservado || 0;
                    const statusTransferencia = (quantidadeJaTransferida + quantity >= quantidadeSolicitada) ? 'COMPLETA' : 'PARCIAL';

                    const transferencia = {
                        ordemProducaoId: orderId,
                        produtoId: materialId,
                        quantidade: quantity,
                        quantidadeSolicitada: quantidadeSolicitada,
                        quantidadeJaTransferida: quantidadeJaTransferida,
                        statusTransferencia: statusTransferencia,
                        armazemOrigemId: sourceId,
                        armazemDestinoId: targetId,
                        motivo,
                        observacoesTransferencia: transferNotes,
                        funcionarioResponsavel: responsibleEmployee,
                        tipoTransferencia: transferType,
                        tipo: 'OP',
                        dataHora: Timestamp.now(),
                        usuario: currentUser.nome
                    };

                    await addDoc(collection(db, "transferenciasArmazem"), transferencia);

                    const produto = produtos.find(p => p.id === materialId);

                    // Atualizar estoques
                    await updateDoc(doc(db, "estoques", sourceEstoque.id), {
                        saldo: sourceEstoque.saldo - quantity,
                        ultimaMovimentacao: Timestamp.now()
                    });
                    sourceEstoque.saldo -= quantity;

                    let targetEstoque = estoques.find(e => e.produtoId === materialId && e.armazemId === targetId);
                    if (targetEstoque) {
                        await updateDoc(doc(db, "estoques", targetEstoque.id), {
                            saldo: targetEstoque.saldo + quantity,
                            ultimaMovimentacao: Timestamp.now()
                        });
                        targetEstoque.saldo += quantity;
                    } else {
                        const newEstoque = {
                            produtoId: materialId,
                            armazemId: targetId,
                            saldo: quantity,
                            ultimaMovimentacao: Timestamp.now()
                        };
                        const docRef = await addDoc(collection(db, "estoques"), newEstoque);
                        estoques.push({ id: docRef.id, ...newEstoque });
                    }

                    // Registrar movimentações
                    await addDoc(collection(db, "movimentacoesEstoque"), {
                        produtoId: materialId,
                        tipo: 'SAIDA',
                        quantidade: quantity,
                        unidade: produto.unidade,
                        tipoDocumento: 'TRANSFERENCIA',
                        numeroDocumento: `TRF-${Date.now()}`,
                        observacoes: `Transferência para OP ${selectedOrder.numero} - Armazém ${armazens.find(a => a.id === targetId).codigo}`,
                        dataHora: Timestamp.now(),
                        armazemId: sourceId
                    });

                    await addDoc(collection(db, "movimentacoesEstoque"), {
                        produtoId: materialId,
                        tipo: 'ENTRADA',
                        quantidade: quantity,
                        unidade: produto.unidade,
                        tipoDocumento: 'TRANSFERENCIA',
                        numeroDocumento: `TRF-${Date.now()}`,
                        observacoes: `Transferência para OP ${selectedOrder.numero} - Do armazém ${armazens.find(a => a.id === sourceId).codigo}`,
                        dataHora: Timestamp.now(),
                        armazemId: targetId
                    });

                    // Atualizar necessidade na OP
                    const novaNecessidade = Math.max(0, material.necessidade - quantity);
                    const novoSaldoReservado = (material.saldoReservado || 0) + quantity;
                    const updatedMateriais = selectedOrder.materiaisNecessarios.map(m => {
                        if (m.produtoId === materialId) {
                            return { ...m, necessidade: novaNecessidade, saldoEstoque: (m.saldoEstoque || 0) + quantity, saldoReservado: novoSaldoReservado };
                        }
                        return m;
                    });

                    await updateDoc(doc(db, "ordensProducao", orderId), {
                        materiaisNecessarios: updatedMateriais
                    });

                    selectedOrder.materiaisNecessarios = updatedMateriais;
                }

                alert('Transferência via OP realizada com sucesso!');
                document.getElementById('movementForm').reset();
                document.getElementById('movementType').value = '';
                toggleMovementType();
                await loadInitialData();
                // Atualizar busca se houver filtros ativos
                if (filteredTransfers.length > 0) {
                    searchHistory();
                }
            } catch (error) {
                console.error("Erro ao realizar transferência via OP:", error);
                alert("Erro ao realizar transferência via OP: " + error.message);
            }
        }

        async function handleFreeMovement() {
            const produtoId = document.getElementById('freeProductSelect').value;
            const quantidade = parseFloat(document.getElementById('freeQuantity').value);
            const sourceId = document.getElementById('freeSourceWarehouse').value;
            const targetId = document.getElementById('freeTargetWarehouse').value;
            const motivo = document.getElementById('reason').value;
            const responsibleEmployee = document.getElementById('freeResponsibleEmployee').value.trim();
            const transferNotes = document.getElementById('freeTransferNotes').value.trim();

            if (!produtoId || !quantidade || !sourceId || !targetId || !responsibleEmployee) {
                alert('Preencha todos os campos obrigatórios para transferência livre.');
                return;
            }

            if (quantidade <= 0) {
                alert('A quantidade deve ser maior que zero.');
                return;
            }

            if (sourceId === targetId) {
                alert('O armazém de origem deve ser diferente do armazém de destino.');
                return;
            }

            try {
                // Verificar saldo disponível
                const sourceEstoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === sourceId);
                if (!sourceEstoque || sourceEstoque.saldo < quantidade) {
                    const produto = produtos.find(p => p.id === produtoId);
                    alert(`Saldo insuficiente para o produto ${produto.codigo} - ${produto.descricao}.`);
                    return;
                }

                const produto = produtos.find(p => p.id === produtoId);
                const armazemOrigem = armazens.find(a => a.id === sourceId);
                const armazemDestino = armazens.find(a => a.id === targetId);

                // Registrar transferência
                const transferencia = {
                    produtoId: produtoId,
                    quantidade: quantidade,
                    quantidadeSolicitada: quantidade,
                    quantidadeJaTransferida: 0,
                    statusTransferencia: 'COMPLETA',
                    armazemOrigemId: sourceId,
                    armazemDestinoId: targetId,
                    motivo: motivo,
                    observacoesTransferencia: transferNotes,
                    funcionarioResponsavel: responsibleEmployee,
                    tipoTransferencia: 'TOTAL',
                    tipo: 'LIVRE',
                    dataHora: Timestamp.now(),
                    usuario: currentUser.nome
                };

                await addDoc(collection(db, "transferenciasArmazem"), transferencia);

                // Atualizar estoque origem
                await updateDoc(doc(db, "estoques", sourceEstoque.id), {
                    saldo: sourceEstoque.saldo - quantidade,
                    ultimaMovimentacao: Timestamp.now()
                });
                sourceEstoque.saldo -= quantidade;

                // Atualizar ou criar estoque destino
                let targetEstoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === targetId);
                if (targetEstoque) {
                    await updateDoc(doc(db, "estoques", targetEstoque.id), {
                        saldo: targetEstoque.saldo + quantidade,
                        ultimaMovimentacao: Timestamp.now()
                    });
                    targetEstoque.saldo += quantidade;
                } else {
                    const newEstoque = {
                        produtoId: produtoId,
                        armazemId: targetId,
                        saldo: quantidade,
                        ultimaMovimentacao: Timestamp.now()
                    };
                    const docRef = await addDoc(collection(db, "estoques"), newEstoque);
                    estoques.push({ id: docRef.id, ...newEstoque });
                }

                // Registrar movimentações de estoque
                const numeroDoc = `TRF-${Date.now()}`;

                await addDoc(collection(db, "movimentacoesEstoque"), {
                    produtoId: produtoId,
                    tipo: 'SAIDA',
                    quantidade: quantidade,
                    unidade: produto.unidade,
                    tipoDocumento: 'TRANSFERENCIA_LIVRE',
                    numeroDocumento: numeroDoc,
                    observacoes: `Transferência livre para ${armazemDestino.codigo} - ${motivo}`,
                    dataHora: Timestamp.now(),
                    armazemId: sourceId
                });

                await addDoc(collection(db, "movimentacoesEstoque"), {
                    produtoId: produtoId,
                    tipo: 'ENTRADA',
                    quantidade: quantidade,
                    unidade: produto.unidade,
                    tipoDocumento: 'TRANSFERENCIA_LIVRE',
                    numeroDocumento: numeroDoc,
                    observacoes: `Transferência livre de ${armazemOrigem.codigo} - ${motivo}`,
                    dataHora: Timestamp.now(),
                    armazemId: targetId
                });

                alert('Transferência livre realizada com sucesso!');
                document.getElementById('movementForm').reset();
                document.getElementById('movementType').value = '';
                toggleMovementType();
                await loadInitialData();
                // Atualizar busca se houver filtros ativos
                if (filteredTransfers.length > 0) {
                    searchHistory();
                }

            } catch (error) {
                console.error("Erro ao realizar transferência livre:", error);
                alert("Erro ao realizar transferência livre: " + error.message);
            }
        }

        async function handleReturnMovement() {
            const orderId = document.getElementById('returnOrderSelect').value;
            const sourceId = document.getElementById('returnSourceWarehouse').value;
            const targetId = document.getElementById('returnTargetWarehouse').value;
            const condition = document.getElementById('materialCondition').value;
            const responsible = document.getElementById('returnResponsible').value;
            const motivo = document.getElementById('reason').value;

            if (!orderId || !sourceId || !targetId || !condition || !responsible.trim()) {
                alert('Preencha todos os campos obrigatórios para retorno de sobras.');
                return;
            }

            if (sourceId === targetId) {
                alert('O armazém de origem deve ser diferente do armazém de destino.');
                return;
            }

            // Coletar materiais selecionados
            const selectedMaterials = Array.from(document.querySelectorAll('#returnMaterialsList input[type="checkbox"]:checked'))
                .map(checkbox => {
                    const materialId = checkbox.id.split('_')[2];
                    const quantity = parseFloat(document.getElementById(`return_quantity_${materialId}`).value);
                    return { materialId, quantity };
                })
                .filter(item => item.quantity > 0);

            if (selectedMaterials.length === 0) {
                alert('Selecione pelo menos um material para retornar.');
                return;
            }

            try {
                const ordem = ordensProducao.find(op => op.id === orderId);
                const armazemOrigem = armazens.find(a => a.id === sourceId);
                const armazemDestino = armazens.find(a => a.id === targetId);

                // Processar cada material selecionado
                for (const material of selectedMaterials) {
                    const produto = produtos.find(p => p.id === material.materialId);

                    // Verificar saldo disponível
                    const sourceEstoque = estoques.find(e => e.produtoId === material.materialId && e.armazemId === sourceId);
                    if (!sourceEstoque || sourceEstoque.saldo < material.quantity) {
                        alert(`Saldo insuficiente para o produto ${produto.codigo} - ${produto.descricao}.`);
                        return;
                    }

                    // Registrar transferência de retorno
                    const transferencia = {
                        ordemProducaoId: orderId,
                        produtoId: material.materialId,
                        quantidade: material.quantity,
                        armazemOrigemId: sourceId,
                        armazemDestinoId: targetId,
                        motivo: `${motivo} - Condição: ${condition}`,
                        tipo: 'RETORNO_SOBRAS',
                        condicaoMaterial: condition,
                        responsavelRetorno: responsible,
                        dataHora: Timestamp.now(),
                        usuario: currentUser.nome
                    };

                    await addDoc(collection(db, "transferenciasArmazem"), transferencia);

                    // Atualizar estoque origem (produção)
                    await updateDoc(doc(db, "estoques", sourceEstoque.id), {
                        saldo: sourceEstoque.saldo - material.quantity,
                        ultimaMovimentacao: Timestamp.now()
                    });
                    sourceEstoque.saldo -= material.quantity;

                    // Atualizar ou criar estoque destino
                    let targetEstoque = estoques.find(e => e.produtoId === material.materialId && e.armazemId === targetId);
                    if (targetEstoque) {
                        await updateDoc(doc(db, "estoques", targetEstoque.id), {
                            saldo: targetEstoque.saldo + material.quantity,
                            ultimaMovimentacao: Timestamp.now()
                        });
                        targetEstoque.saldo += material.quantity;
                    } else {
                        const newEstoque = {
                            produtoId: material.materialId,
                            armazemId: targetId,
                            saldo: material.quantity,
                            ultimaMovimentacao: Timestamp.now()
                        };
                        const docRef = await addDoc(collection(db, "estoques"), newEstoque);
                        estoques.push({ id: docRef.id, ...newEstoque });
                    }

                    // Registrar movimentações de estoque
                    const numeroDoc = `RET-${Date.now()}-${material.materialId.slice(-4)}`;

                    await addDoc(collection(db, "movimentacoesEstoque"), {
                        produtoId: material.materialId,
                        tipo: 'SAIDA',
                        quantidade: material.quantity,
                        unidade: produto.unidade,
                        tipoDocumento: 'RETORNO_SOBRAS',
                        numeroDocumento: numeroDoc,
                        observacoes: `Retorno de sobras OP ${ordem.numero} para ${armazemDestino.codigo} - ${condition} - ${responsible}`,
                        dataHora: Timestamp.now(),
                        armazemId: sourceId,
                        ordemProducaoId: orderId
                    });

                    await addDoc(collection(db, "movimentacoesEstoque"), {
                        produtoId: material.materialId,
                        tipo: 'ENTRADA',
                        quantidade: material.quantity,
                        unidade: produto.unidade,
                        tipoDocumento: 'RETORNO_SOBRAS',
                        numeroDocumento: numeroDoc,
                        observacoes: `Retorno de sobras OP ${ordem.numero} de ${armazemOrigem.codigo} - ${condition} - ${responsible}`,
                        dataHora: Timestamp.now(),
                        armazemId: targetId,
                        ordemProducaoId: orderId
                    });
                }

                alert(`Retorno de sobras realizado com sucesso!\n${selectedMaterials.length} material(is) retornado(s).`);
                document.getElementById('movementForm').reset();
                document.getElementById('movementType').value = '';
                toggleMovementType();
                await loadInitialData();
                // Atualizar busca se houver filtros ativos
                if (filteredTransfers.length > 0) {
                    searchHistory();
                }

            } catch (error) {
                console.error("Erro ao realizar retorno de sobras:", error);
                alert("Erro ao realizar retorno de sobras: " + error.message);
            }
        }

        let historyPage = 1;
        const historyPageSize = 20;
        let filteredTransfers = [];
        let isSearching = false;

        // Inicializar sem carregar histórico
        async function loadHistory() {
            // Não carregar nada inicialmente - só quando pesquisar
            populateWarehouseFilter();
            displayEmptyHistory();
        }

        function populateWarehouseFilter() {
            const warehouseFilter = document.getElementById('warehouseFilter');
            if (!warehouseFilter) return;

            warehouseFilter.innerHTML = '<option value="">Todos os armazéns</option>';

            armazens.forEach(armazem => {
                warehouseFilter.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
            });
        }

        function displayEmptyHistory() {
            const tableBody = document.getElementById('historyTableBody');
            if (!tableBody) return;

            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" style="text-align: center; color: #666; padding: 40px;">
                        <div style="font-size: 18px; margin-bottom: 10px;">📋</div>
                        <div>Use os filtros acima para buscar transferências</div>
                        <div style="font-size: 12px; margin-top: 5px;">Exemplo: últimos 7 dias, produto específico, etc.</div>
                    </td>
                </tr>
            `;

            const resultsCount = document.getElementById('resultsCount');
            if (resultsCount) resultsCount.textContent = 'Use os filtros para buscar';

            updatePagination(0, 0);
        }

        async function searchHistory() {
            const searchProduct = document.getElementById('searchProduct')?.value.toLowerCase().trim() || '';
            const periodFilter = document.getElementById('periodFilter')?.value || '7';
            const typeFilter = document.getElementById('typeFilter')?.value || '';
            const warehouseFilter = document.getElementById('warehouseFilter')?.value || '';

            try {
                isSearching = true;
                const tableBody = document.getElementById('historyTableBody');
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 20px;">🔍 Buscando...</td></tr>';
                }

                // Construir query baseada nos filtros
                let queryConstraints = [orderBy("dataHora", "desc")];

                // Filtro de período
                if (periodFilter !== 'all') {
                    const daysAgo = parseInt(periodFilter);
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - daysAgo);
                    queryConstraints.push(where("dataHora", ">=", Timestamp.fromDate(startDate)));
                }

                // Filtro de tipo
                if (typeFilter) {
                    queryConstraints.push(where("tipo", "==", typeFilter));
                }

                // Buscar transferências
                const transferenciasSnap = await getDocs(
                    query(collection(db, "transferenciasArmazem"), ...queryConstraints)
                );

                let transfers = transferenciasSnap.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Filtros adicionais no cliente (para campos que não são indexados)
                if (searchProduct) {
                    transfers = transfers.filter(transfer => {
                        const produto = produtos.find(p => p.id === transfer.produtoId);
                        if (!produto) return false;

                        const codigo = (produto.codigo || '').toLowerCase();
                        const descricao = (produto.descricao || '').toLowerCase();

                        return codigo.includes(searchProduct) || descricao.includes(searchProduct);
                    });
                }

                if (warehouseFilter) {
                    transfers = transfers.filter(transfer =>
                        transfer.armazemOrigemId === warehouseFilter ||
                        transfer.armazemDestinoId === warehouseFilter
                    );
                }

                filteredTransfers = transfers;
                historyPage = 1;
                renderHistoryPage();

            } catch (error) {
                console.error("Erro ao buscar histórico:", error);
                const tableBody = document.getElementById('historyTableBody');
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="11" style="text-align: center; color: #d32f2f;">Erro ao buscar transferências</td></tr>';
                }
            } finally {
                isSearching = false;
            }
        }

        function clearFilters() {
            const searchProduct = document.getElementById('searchProduct');
            const periodFilter = document.getElementById('periodFilter');
            const typeFilter = document.getElementById('typeFilter');
            const warehouseFilter = document.getElementById('warehouseFilter');

            if (searchProduct) searchProduct.value = '';
            if (periodFilter) periodFilter.value = '7';
            if (typeFilter) typeFilter.value = '';
            if (warehouseFilter) warehouseFilter.value = '';

            displayEmptyHistory();
        }

        // Função global para ser chamada pelos filtros
        window.searchHistory = searchHistory;
        window.clearFilters = clearFilters;

        function renderHistoryPage() {
            const tableBody = document.getElementById('historyTableBody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            const startIndex = (historyPage - 1) * historyPageSize;
            const endIndex = startIndex + historyPageSize;
            const pageTransfers = filteredTransfers.slice(startIndex, endIndex);

            if (pageTransfers.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="11" style="text-align: center; color: #666;">Nenhuma transferência encontrada</td></tr>';
                updatePagination(0, filteredTransfers.length);
                return;
            }

            pageTransfers.forEach(transfer => {
                const produto = produtos.find(p => p.id === transfer.produtoId);
                const origem = armazens.find(a => a.id === transfer.armazemOrigemId);
                const destino = armazens.find(a => a.id === transfer.armazemDestinoId);
                const op = ordensProducao.find(op => op.id === transfer.ordemProducaoId);

                let dataHora = 'N/A';
                try {
                    if (transfer.dataHora && typeof transfer.dataHora.toDate === 'function') {
                        dataHora = transfer.dataHora.toDate().toLocaleString('pt-BR');
                    } else if (transfer.dataHora && transfer.dataHora.seconds) {
                        dataHora = new Date(transfer.dataHora.seconds * 1000).toLocaleString('pt-BR');
                    }
                } catch (error) {
                    console.warn('Erro ao formatar data:', error);
                }

                // Determinar status da transferência
                let statusBadge = '';
                let statusColor = '';
                if (transfer.statusTransferencia === 'COMPLETA') {
                    statusBadge = '✅ Completa';
                    statusColor = '#28a745';
                } else if (transfer.statusTransferencia === 'PARCIAL') {
                    statusBadge = '⚠️ Parcial';
                    statusColor = '#ffc107';
                } else {
                    statusBadge = '📋 Normal';
                    statusColor = '#6c757d';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${dataHora}</td>
                    <td>${op ? op.numero : (transfer.tipo === 'LIVRE' ? 'Livre' : 'N/A')}</td>
                    <td>${produto ? `${produto.codigo} - ${produto.descricao}` : 'Produto não encontrado'}</td>
                    <td>${transfer.quantidadeSolicitada ? transfer.quantidadeSolicitada.toFixed(3) : '0'} ${produto ? produto.unidade : ''}</td>
                    <td>${transfer.quantidade ? transfer.quantidade.toFixed(3) : '0'} ${produto ? produto.unidade : ''}</td>
                    <td><span style="color: ${statusColor}; font-weight: bold;">${statusBadge}</span></td>
                    <td>${origem ? `${origem.codigo} - ${origem.nome}` : 'Origem não encontrada'}</td>
                    <td>${destino ? `${destino.codigo} - ${destino.nome}` : 'Destino não encontrado'}</td>
                    <td>${transfer.funcionarioResponsavel || 'N/A'}</td>
                    <td title="${transfer.observacoesTransferencia || ''}">${transfer.motivo || 'N/A'}</td>
                    <td>
                        <button class="btn-secondary" onclick="verDetalhesTransferencia('${transfer.id}')"
                                style="font-size: 12px; padding: 4px 8px; margin-right: 5px;">
                            Ver
                        </button>
                        ${transfer.status !== 'CANCELADA' ? `
                        <button class="btn-secondary" onclick="cancelarTransferencia('${transfer.id}')"
                                style="font-size: 12px; padding: 4px 8px; background: #dc3545;">
                            Cancelar
                        </button>` : '<span style="color: #dc3545;">Cancelada</span>'}
                    </td>
                `;
                tableBody.appendChild(row);
            });

            updatePagination(pageTransfers.length, filteredTransfers.length);
        }

        function updatePagination(currentPageCount, totalCount) {
            const totalPages = Math.ceil(totalCount / historyPageSize);
            const pageInfo = document.getElementById('historyPageInfo');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');
            const resultsCount = document.getElementById('resultsCount');

            if (pageInfo) {
                if (totalCount === 0) {
                    pageInfo.textContent = '';
                } else {
                    pageInfo.textContent = `Página ${historyPage} de ${totalPages}`;
                }
            }

            if (resultsCount) {
                if (totalCount === 0) {
                    resultsCount.textContent = 'Nenhum resultado encontrado';
                } else {
                    resultsCount.textContent = `${totalCount} transferência(s) encontrada(s)`;
                }
            }

            if (prevBtn) {
                prevBtn.disabled = historyPage <= 1;
                prevBtn.onclick = () => {
                    if (historyPage > 1) {
                        historyPage--;
                        renderHistoryPage();
                    }
                };
            }

            if (nextBtn) {
                nextBtn.disabled = historyPage >= totalPages || totalPages === 0;
                nextBtn.onclick = () => {
                    if (historyPage < totalPages) {
                        historyPage++;
                        renderHistoryPage();
                    }
                };
            }
        }

        // Função para filtrar ordens de produção
        window.filterOrders = function() {
            const searchText = document.getElementById('searchOP').value.toLowerCase().trim();
            const select = document.getElementById('productionOrderSelect');
            const options = select.getElementsByTagName('option');
            const optgroups = select.getElementsByTagName('optgroup');

            // Se não há texto de busca, mostrar todas as opções
            if (!searchText) {
                for (let i = 0; i < options.length; i++) {
                    options[i].style.display = '';
                }
                for (let i = 0; i < optgroups.length; i++) {
                    optgroups[i].style.display = '';
                }
                return;
            }

            // Filtrar opções
            for (let i = 0; i < options.length; i++) {
                const option = options[i];
                if (option.value === '') continue; // Pular a opção padrão

                const text = option.textContent.toLowerCase();
                if (text.includes(searchText)) {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            }

            // Esconder grupos vazios
            for (let i = 0; i < optgroups.length; i++) {
                const group = optgroups[i];
                const visibleOptions = Array.from(group.getElementsByTagName('option'))
                    .filter(option => option.style.display !== 'none');

                group.style.display = visibleOptions.length > 0 ? '' : 'none';
            }
        };

        // Função para ver detalhes da transferência
        window.verDetalhesTransferencia = function(transferenciaId) {
            const transferencia = filteredTransfers.find(t => t.id === transferenciaId);
            if (!transferencia) {
                alert('Transferência não encontrada.');
                return;
            }

            const produto = produtos.find(p => p.id === transferencia.produtoId);
            const origem = armazens.find(a => a.id === transferencia.armazemOrigemId);
            const destino = armazens.find(a => a.id === transferencia.armazemDestinoId);
            const op = ordensProducao.find(op => op.id === transferencia.ordemProducaoId);

            let dataHora = 'N/A';
            try {
                if (transferencia.dataHora && typeof transferencia.dataHora.toDate === 'function') {
                    dataHora = transferencia.dataHora.toDate().toLocaleString('pt-BR');
                } else if (transferencia.dataHora && transferencia.dataHora.seconds) {
                    dataHora = new Date(transferencia.dataHora.seconds * 1000).toLocaleString('pt-BR');
                }
            } catch (error) {
                console.warn('Erro ao formatar data:', error);
            }

            const detalhes = `
📋 DETALHES DA TRANSFERÊNCIA

🕒 Data/Hora: ${dataHora}
👤 Usuário: ${transferencia.usuario || 'N/A'}
🏭 Funcionário Responsável: ${transferencia.funcionarioResponsavel || 'N/A'}

📦 PRODUTO:
• Código: ${produto ? produto.codigo : 'N/A'}
• Descrição: ${produto ? produto.descricao : 'N/A'}

📊 QUANTIDADES:
• Solicitada: ${transferencia.quantidadeSolicitada ? transferencia.quantidadeSolicitada.toFixed(3) : '0'} ${produto ? produto.unidade : ''}
• Transferida: ${transferencia.quantidade ? transferencia.quantidade.toFixed(3) : '0'} ${produto ? produto.unidade : ''}
• Status: ${transferencia.statusTransferencia || 'N/A'}

🏪 MOVIMENTAÇÃO:
• Origem: ${origem ? `${origem.codigo} - ${origem.nome}` : 'N/A'}
• Destino: ${destino ? `${destino.codigo} - ${destino.nome}` : 'N/A'}

${transferencia.ordemProducaoId ? `🔧 OP: ${op ? op.numero : 'N/A'}` : ''}
${transferencia.tipo ? `📋 Tipo: ${transferencia.tipo}` : ''}
${transferencia.tipoTransferencia ? `⚙️ Modalidade: ${transferencia.tipoTransferencia}` : ''}

💬 OBSERVAÇÕES:
• Motivo: ${transferencia.motivo || 'N/A'}
${transferencia.observacoesTransferencia ? `• Observações: ${transferencia.observacoesTransferencia}` : ''}
${transferencia.condicaoMaterial ? `• Condição: ${transferencia.condicaoMaterial}` : ''}
${transferencia.status === 'CANCELADA' ? `\n❌ CANCELADA em ${transferencia.dataCancelamento ? new Date(transferencia.dataCancelamento.seconds * 1000).toLocaleString('pt-BR') : 'N/A'} por ${transferencia.canceladoPor || 'N/A'}` : ''}
            `;

            alert(detalhes);
        };

        // Função para cancelar transferência
        window.cancelarTransferencia = async function(transferenciaId) {
            if (!confirm('Deseja realmente cancelar esta transferência? Esta ação irá reverter os movimentos de estoque.')) {
                return;
            }

            try {
                const transferencia = filteredTransfers.find(t => t.id === transferenciaId);
                if (!transferencia) {
                    alert('Transferência não encontrada.');
                    return;
                }

                // Reverter movimentações de estoque
                const sourceEstoque = estoques.find(e =>
                    e.produtoId === transferencia.produtoId &&
                    e.armazemId === transferencia.armazemOrigemId
                );

                const targetEstoque = estoques.find(e =>
                    e.produtoId === transferencia.produtoId &&
                    e.armazemId === transferencia.armazemDestinoId
                );

                if (targetEstoque && targetEstoque.saldo < transferencia.quantidade) {
                    alert('Não é possível cancelar: saldo insuficiente no armazém de destino.');
                    return;
                }

                // Atualizar estoques
                if (sourceEstoque) {
                    await updateDoc(doc(db, "estoques", sourceEstoque.id), {
                        saldo: sourceEstoque.saldo + transferencia.quantidade,
                        ultimaMovimentacao: Timestamp.now()
                    });
                }

                if (targetEstoque) {
                    await updateDoc(doc(db, "estoques", targetEstoque.id), {
                        saldo: targetEstoque.saldo - transferencia.quantidade,
                        ultimaMovimentacao: Timestamp.now()
                    });
                }

                // Marcar transferência como cancelada
                await updateDoc(doc(db, "transferenciasArmazem", transferenciaId), {
                    status: 'CANCELADA',
                    dataCancelamento: Timestamp.now(),
                    canceladoPor: currentUser.nome
                });

                alert('Transferência cancelada com sucesso!');
                await loadInitialData();
                // Atualizar busca se houver filtros ativos
                if (filteredTransfers.length > 0) {
                    searchHistory();
                }

            } catch (error) {
                console.error("Erro ao cancelar transferência:", error);
                alert("Erro ao cancelar transferência: " + error.message);
            }
        };

        // Função de teste para debug (só executa quando chamada manualmente)
        window.debugOrdensProducao = function() {
            console.log('=== DEBUG ORDENS DE PRODUÇÃO ===');
            console.log('Total de ordens:', ordensProducao.length);

            // Verificar ordens de hoje
            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0);

            const ordensHoje = ordensProducao.filter(op => {
                if (op.dataCriacao && op.dataCriacao.seconds) {
                    const dataOP = new Date(op.dataCriacao.seconds * 1000);
                    dataOP.setHours(0, 0, 0, 0);
                    return dataOP.getTime() === hoje.getTime();
                }
                return false;
            });

            console.log('Ordens de hoje:', ordensHoje.length);
            ordensHoje.forEach(op => {
                console.log(`${op.numero} - Status: ${op.status} - Materiais: ${op.materiaisNecessarios ? op.materiaisNecessarios.length : 'N/A'}`);
            });

            // Recarregar select
            populateOrderSelect();
        };

    </script>
</body>
</html>