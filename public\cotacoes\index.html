<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Cotações - Sistema Empresarial</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/cotacoes-styles.css">
    
    <!-- Firebase -->
    <script type="module">
        // ===================================================================
        // COTAÇÕES - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from '../firebase-config.js';
        import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, query, where, orderBy, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Disponibilizar db globalmente para compatibilidade
        window.db = db;
        
        // Exportar funções Firebase para uso global
        window.collection = collection;
        window.getDocs = getDocs;
        window.addDoc = addDoc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.doc = doc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.Timestamp = Timestamp;
    </script>
</head>
<body>
    <div class="container">
        <!-- ⚠️ AVISO TEMPORÁRIO -->
        <div style="background: linear-gradient(135deg, #ff9800, #f57c00); color: white; padding: 12px 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(255,152,0,0.3);">
            <div style="display: flex; align-items: center; gap: 12px;">
                <i class="fas fa-tools" style="font-size: 20px;"></i>
                <div>
                    <strong>🔧 VERSÃO EM DESENVOLVIMENTO</strong>
                    <p style="margin: 4px 0 0 0; font-size: 14px; opacity: 0.9;">
                        Sistema funcionando com segurança básica. Melhorias avançadas serão implementadas em breve.
                    </p>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-file-invoice"></i> Gestão de Cotações</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="openNewQuotationModal()">
                    <i class="fas fa-plus"></i> Nova Cotação
                </button>
                <button class="btn btn-primary" onclick="forceRefresh()" title="Atualizar lista manualmente">
                    <i class="fas fa-sync-alt"></i> Atualizar
                </button>
                <button class="btn btn-info" onclick="importFromRequests()">
                    <i class="fas fa-file-import"></i> Importar Solicitações
                </button>
                <button class="btn btn-warning" onclick="demonstrateConversion()" title="Demonstrar conversão PC → KG">
                    <i class="fas fa-exchange-alt"></i> Demo Conversão
                </button>
                <a href="../cadastro_fornecedores.html" class="btn btn-secondary" target="_blank" title="Gerenciar Fornecedores">
                    <i class="fas fa-truck"></i> Fornecedores
                </a>
                <button class="btn btn-warning" onclick="exportReport()">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <a href="../index.html" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Workflow de Cotações -->
            <div class="workflow-steps">
                <div class="workflow-step completed">
                    <div class="workflow-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="workflow-label">Solicitação</div>
                </div>
                <div class="workflow-step active">
                    <div class="workflow-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="workflow-label">Cotação</div>
                </div>
                <div class="workflow-step">
                    <div class="workflow-icon">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div class="workflow-label">Respostas</div>
                </div>
                <div class="workflow-step">
                    <div class="workflow-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="workflow-label">Pedido</div>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card abertas">
                    <div class="stat-number" id="statAbertas">0</div>
                    <div class="stat-label">Abertas</div>
                </div>
                <div class="stat-card enviadas">
                    <div class="stat-number" id="statEnviadas">0</div>
                    <div class="stat-label">Enviadas</div>
                </div>
                <div class="stat-card respondidas">
                    <div class="stat-number" id="statRespondidas">0</div>
                    <div class="stat-label">Respondidas</div>
                </div>
                <div class="stat-card aprovadas">
                    <div class="stat-number" id="statAprovadas">0</div>
                    <div class="stat-label">Aprovadas</div>
                </div>
            </div>

            <!-- Abas -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('cotacoes', event)">
                    <i class="fas fa-file-invoice"></i> Cotações
                </button>
                <button class="tab" onclick="showTab('respostas', event)">
                    <i class="fas fa-reply"></i> Respostas
                </button>
                <button class="tab" onclick="showTab('comparacao', event)">
                    <i class="fas fa-balance-scale"></i> Comparação
                </button>
                <button class="tab" onclick="showTab('integracao', event)">
                    <i class="fas fa-link"></i> Integração
                </button>
            </div>

            <!-- Aba Cotações -->
            <div id="cotacoes" class="tab-content active">
                <!-- Barra de Pesquisa -->
                <div class="search-bar">
                    <input type="text" class="search-input" id="searchInput" placeholder="Pesquisar por número, fornecedor, solicitação...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- Ações Rápidas -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="showAllQuotations()">
                        <i class="fas fa-list"></i> Todas
                    </button>
                    <button class="btn btn-warning" onclick="showOpenQuotations()">
                        <i class="fas fa-folder-open"></i> Abertas
                    </button>
                    <button class="btn btn-info" onclick="showSentQuotations()">
                        <i class="fas fa-paper-plane"></i> Enviadas
                    </button>
                    <button class="btn btn-success" onclick="showRespondedQuotations()">
                        <i class="fas fa-check"></i> Respondidas
                    </button>
                    <button class="btn btn-secondary" onclick="showPendingApproval()">
                        <i class="fas fa-clock"></i> Aguardando Aprovação
                    </button>
                </div>

                <!-- Filtros -->
                <div class="filters">
                    <h3><i class="fas fa-filter"></i> Filtros Avançados</h3>
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Data Início</label>
                            <input type="date" class="form-control" id="dataInicio">
                        </div>
                        <div class="form-group">
                            <label>Data Fim</label>
                            <input type="date" class="form-control" id="dataFim">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select class="form-control" id="statusFilter">
                                <option value="">Todos</option>
                                <option value="ABERTA">Aberta</option>
                                <option value="ENVIADA">Enviada</option>
                                <option value="RESPONDIDA">Respondida</option>
                                <option value="APROVADA">Aprovada</option>
                                <option value="FECHADA">Fechada</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Solicitação Origem</label>
                            <select class="form-control" id="solicitacaoFilter">
                                <option value="">Todas</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Fornecedor</label>
                            <select class="form-control" id="fornecedorFilter">
                                <option value="">Todos</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: flex-end;">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Aplicar Filtros
                        </button>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Limpar
                        </button>
                    </div>
                </div>

                <!-- Controles de Aglutinação -->
                <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #17a2b8;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h5 style="margin: 0; color: #2c3e50;">
                            <i class="fas fa-layer-group"></i> Gerenciamento de Cotações
                        </h5>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <div id="selectedQuotationsCounter" style="background: #e7f3ff; color: #0066cc; padding: 5px 12px; border-radius: 15px; font-size: 0.9rem; font-weight: bold;">
                                <i class="fas fa-check-square"></i> 0 selecionadas
                            </div>
                            <button type="button" class="btn btn-info btn-sm" onclick="toggleAglutinatedView()">
                                <i class="fas fa-eye" id="viewToggleIcon"></i> 
                                <span id="viewToggleText">Mostrar Aglutinadas</span>
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="toggleClosedView()">
                                <i class="fas fa-eye-slash" id="closedToggleIcon"></i> 
                                <span id="closedToggleText">Mostrar Fechadas</span>
                            </button>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button type="button" class="btn btn-success btn-sm" onclick="aglutinarCotacoes()" id="btnAglutinar" disabled>
                            <i class="fas fa-compress-arrows-alt"></i> Aglutinar Selecionadas
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="dividirCotacao()" id="btnDividir" disabled>
                            <i class="fas fa-expand-arrows-alt"></i> Dividir Cotação
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-times"></i> Limpar Seleção
                        </button>
                        <div style="margin-left: auto; font-size: 0.9rem; color: #6c757d;">
                            <i class="fas fa-info-circle"></i>
                            Selecione 2+ cotações para aglutinar ou 1 aglutinada para dividir<br>
                            <small>Cotações aglutinadas e fechadas ficam ocultas por padrão</small>
                        </div>
                    </div>
                </div>

                <!-- Tabela de Cotações -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="selectAllQuotations" onchange="toggleAllQuotations()" title="Selecionar todas">
                                </th>
                                <th>Número</th>
                                <th>Data / Prazo</th>
                                <th>Solicitação <span class="integration-badge">Integrada</span></th>
                                <th>Fornecedores <span class="integration-badge">Homologados</span></th>
                                <th>Respostas</th>
                                <th>Valor Estimado</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="quotationsTableBody">
                            <!-- Dados carregados dinamicamente -->
                        </tbody>
                    </table>

                    <!-- Paginação -->
                    <div class="pagination-info">
                        <div class="items-per-page">
                            <label>Itens por página:</label>
                            <select id="itemsPerPage" onchange="changeItemsPerPage()">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div>
                            <div id="paginationInfo">
                                Mostrando 0 de 0 cotações
                            </div>
                            <div id="hiddenQuotationsInfo" style="font-size: 0.9rem; color: #6c757d; margin-top: 5px;">
                                <!-- Info sobre cotações ocultas -->
                            </div>
                        </div>
                    </div>

                    <div class="pagination" id="pagination">
                        <!-- Controles de paginação serão inseridos aqui -->
                    </div>
                </div>
            </div>

            <!-- Outras abas serão carregadas dinamicamente -->
            <div id="respostas" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Visualize e gerencie as respostas dos fornecedores às cotações enviadas.
                </div>
            </div>

            <div id="comparacao" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Compare preços e condições dos fornecedores para tomar a melhor decisão.
                </div>
                <div class="form-group" style="max-width: 300px;">
                    <label>Selecione uma cotação para comparar:</label>
                    <select class="form-control" id="quotationCompareSelect" onchange="loadComparison()">
                        <option value="">Selecione...</option>
                    </select>
                </div>
            </div>

            <div id="integracao" class="tab-content">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Sistema Integrado!</strong> As cotações são automaticamente criadas a partir das solicitações aprovadas.
                </div>
            </div>
        </div>
    </div>

    <!-- Modais serão carregados dinamicamente -->
    <div id="modalsContainer"></div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <!-- Scripts -->
    <script src="js/cotacoes-core.js"></script>
    <script src="js/cotacoes-filters.js"></script>
    <script src="js/cotacoes-aglutinacao.js"></script>
    <script src="js/cotacoes-edicao.js"></script>
    <script src="js/cotacoes-nova.js"></script>
    <script src="js/cotacoes-analise.js"></script>

    <!-- Carregar modais dinamicamente -->
    <script>
        async function loadModals() {
            try {
                const [aglutinacaoResponse, edicaoResponse, novaResponse, analiseResponse] = await Promise.all([
                    fetch('components/modal-aglutinacao.html'),
                    fetch('components/modal-editar-cotacao.html'),
                    fetch('components/modal-nova-cotacao.html'),
                    fetch('components/modal-analise-cotacao.html')
                ]);

                const aglutinacaoHtml = await aglutinacaoResponse.text();
                const edicaoHtml = await edicaoResponse.text();
                const novaHtml = await novaResponse.text();
                const analiseHtml = await analiseResponse.text();

                document.getElementById('modalsContainer').innerHTML = aglutinacaoHtml + edicaoHtml + novaHtml + analiseHtml;
            } catch (error) {
                console.error('Erro ao carregar modais:', error);
            }
        }

        // Carregar modais após DOM estar pronto
        document.addEventListener('DOMContentLoaded', async function() {
            await loadModals();

            // Adicionar evento para fechar modais ao clicar fora
            window.onclick = function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            };
        });
    </script>
    
    <!-- Funções temporárias até implementar modais -->
    <script>
        // Função openNewQuotationModal agora está implementada em cotacoes-nova.js
        
        function importFromRequests() {
            showNotification('Importação de solicitações será implementada', 'info');
        }
        
        function exportReport() {
            showNotification('Exportação de relatórios será implementada', 'info');
        }
        
        // Função global para abrir modais
        window.openModal = function(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
            } else {
                console.error('Modal não encontrado:', modalId);
                showNotification('Erro: Modal não encontrado', 'error');
            }
        };

        // Função global para fechar modais
        window.closeModal = function(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        };

        function viewQuotation(id) {
            const quotation = cotacoes.find(c => c.id === id);
            if (!quotation) {
                showNotification('Cotação não encontrada', 'error');
                return;
            }

            // Verificar se o modal existe
            const modal = document.getElementById('editQuotationModal');
            if (!modal) {
                showNotification('Modal de edição não foi carregado ainda. Aguarde...', 'warning');
                // Tentar novamente após um pequeno delay
                setTimeout(() => viewQuotation(id), 1000);
                return;
            }

            // Abrir modal de visualização (somente leitura)
            currentEditingQuotation = quotation;
            loadQuotationForEdit(quotation);

            // Desabilitar todos os campos para visualização
            modal.querySelectorAll('input, select, textarea, button').forEach(element => {
                if (!element.classList.contains('close') && !element.onclick?.toString().includes('closeModal')) {
                    element.disabled = true;
                }
            });

            // Mudar título do modal
            const headerTitle = modal.querySelector('.modal-header h2');
            if (headerTitle) {
                headerTitle.innerHTML = '<i class="fas fa-eye"></i> Visualizar Cotação';
            }

            // Ocultar botões de ação
            const modalActions = modal.querySelector('.modal-actions');
            if (modalActions) {
                modalActions.style.display = 'none';
            }

            openModal('editQuotationModal');
        }

        // Função editQuotation agora está implementada em cotacoes-edicao.js

        function sendQuotation(id) {
            try {
                // ✅ VALIDAÇÃO RIGOROSA DE PERMISSÕES
                if (currentUser.nivel < 2 && currentUser.id !== 'sistema') {
                    // TODO: Registrar tentativa de acesso negado
                    console.log('❌ Tentativa de envio negada:', currentUser.id);
                    showNotification('❌ Você não tem permissão para enviar cotações', 'error');
                    return;
                }

                // ✅ VALIDAÇÃO DE DADOS DE ENTRADA
                if (!id || typeof id !== 'string') {
                    showNotification('❌ ID da cotação inválido', 'error');
                    return;
                }

                const quotation = cotacoes.find(c => c.id === id);
                if (!quotation) {
                    showNotification('❌ Cotação não encontrada', 'error');
                    return;
                }

                // ✅ VALIDAR STATUS DA COTAÇÃO
                if (quotation.status === 'ENVIADA') {
                    showNotification('⚠️ Esta cotação já foi enviada', 'warning');
                    return;
                }

                if (quotation.status !== 'ABERTA') {
                    showNotification('❌ Apenas cotações abertas podem ser enviadas', 'error');
                    return;
                }

                // ✅ VALIDAÇÕES DE INTEGRIDADE DE DADOS
                if (!quotation.fornecedores || quotation.fornecedores.length === 0) {
                    showNotification('❌ Adicione fornecedores antes de enviar a cotação', 'warning');
                    editQuotation(id);
                    return;
                }

                if (!quotation.itens || quotation.itens.length === 0) {
                    showNotification('❌ A cotação não possui itens para enviar', 'error');
                    return;
                }

                // ✅ VALIDAR EMAILS DOS FORNECEDORES
                const fornecedoresSemEmail = quotation.fornecedores.filter(fId => {
                    const fornecedor = fornecedores.find(f => f.id === fId);
                    return !fornecedor || !fornecedor.email || !fornecedor.email.includes('@');
                });

                if (fornecedoresSemEmail.length > 0) {
                    showNotification('❌ Alguns fornecedores não possuem email válido cadastrado', 'error');
                    return;
                }

                const confirmacao = confirm(`✅ Confirma o envio da cotação ${quotation.numero} para ${quotation.fornecedores.length} fornecedores?`);

                if (confirmacao) {
                    // TODO: Implementar envio real com validação de segurança
                    console.log('📤 Cotação enviada:', id, 'por:', currentUser.nome);
                    showNotification(`✅ Cotação enviada para ${quotation.fornecedores.length} fornecedores!`, 'success');
                }

            } catch (error) {
                console.error("❌ Erro ao enviar cotação:", error);

                // TODO: Registrar erro na auditoria
                console.log('❌ Erro no envio:', id, error.message);

                showNotification("❌ Erro ao enviar cotação: " + error.message, 'error');
            }
        }

        // Função analyzeQuotation agora está implementada em cotacoes-analise.js
        
        function loadComparison() {
            showNotification('Carregamento de comparação será implementado', 'info');
        }
    </script>
</body>
</html>
