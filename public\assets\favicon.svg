<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradiente azul para F -->
    <linearGradient id="fGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradiente verde para forma -->
    <linearGradient id="shapeGreen" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Fundo -->
  <rect width="32" height="32" rx="6" fill="#ffffff"/>
  
  <!-- Formas dinâmicas minimalistas -->
  <path d="M 4 8 Q 8 6 12 8 Q 14 9 16 7 Q 18 6 20 8 L 19 10 Q 17 9 15 10 Q 13 11 11 10 Q 9 9 7 10 L 5 11 Z" 
        fill="url(#fGradient)" 
        opacity="0.8"/>
  
  <path d="M 16 9 Q 18 7 20 9 Q 21 10 22 8 L 21 12 Q 20 13 19 12 Q 18 11 17 12 Q 16 13 15 12 Z" 
        fill="url(#shapeGreen)" 
        opacity="0.9"/>

  <!-- Letra F estilizada -->
  <text x="6" y="24"
        font-family="'Arial Black', 'Helvetica', sans-serif"
        font-size="16"
        font-weight="900"
        fill="url(#fGradient)"
        filter="url(#shadow)">
    F
  </text>
</svg>
