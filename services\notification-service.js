// Serviço para gerenciar notificações e alertas do sistema
import { db } from '../firebase-config.js';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs,
  Timestamp,
  orderBy,
  limit,
  updateDoc,
  doc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class NotificationService {
  static async createNotification(params) {
    const {
      tipo,
      titulo,
      mensagem,
      severidade = 'INFO', // INFO, AVISO, CRITICO
      destinatarios = [], // Lista de IDs de usuários
      documentoOrigem,
      modulo,
      documentoId,
      documentoTipo,
      acaoRequerida = false
    } = params;

    try {
      const notification = {
        tipo,
        titulo,
        mensagem,
        severidade,
        destinatarios,
        documentoOrigem,
        modulo,
        documentoId,
        documentoTipo,
        acaoRequerida,
        dataCriacao: Timestamp.now(),
        status: 'NAO_LIDA',
        dataLeitura: null,
        dataAcao: null
      };

      const notificationRef = await addDoc(collection(db, "notificacoes"), notification);
      
      // Se houver destinatários específicos, cria cópias individuais
      if (destinatarios.length > 0) {
        for (const usuarioId of destinatarios) {
          await addDoc(collection(db, "notificacoesUsuarios"), {
            notificacaoId: notificationRef.id,
            usuarioId,
            status: 'NAO_LIDA',
            dataCriacao: Timestamp.now()
          });
        }
      }

      return notificationRef.id;
    } catch (error) {
      console.error("Erro ao criar notificação:", error);
      throw error;
    }
  }

  static async getActiveAlerts() {
    try {
      const alertsQuery = query(
        collection(db, "alertas"),
        where("status", "==", "ATIVO"),
        orderBy("dataCriacao", "desc")
      );

      const alertsSnapshot = await getDocs(alertsQuery);
      return alertsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Erro ao buscar alertas:", error);
      throw error;
    }
  }

  static async getUserNotifications(usuarioId) {
    try {
      const notificacoesQuery = query(
        collection(db, "notificacoesUsuarios"),
        where("usuarioId", "==", usuarioId),
        where("status", "==", "NAO_LIDA"),
        orderBy("dataCriacao", "desc"),
        limit(50)
      );

      const notificacoesSnapshot = await getDocs(notificacoesQuery);
      const notificacoes = [];

      for (const doc of notificacoesSnapshot.docs) {
        const notificacaoUsuario = doc.data();
        const notificacaoRef = await getDocs(
          query(collection(db, "notificacoes"), 
          where("id", "==", notificacaoUsuario.notificacaoId))
        );

        if (notificacaoRef.docs.length > 0) {
          notificacoes.push({
            id: doc.id,
            ...notificacaoUsuario,
            detalhes: notificacaoRef.docs[0].data()
          });
        }
      }

      return notificacoes;
    } catch (error) {
      console.error("Erro ao buscar notificações do usuário:", error);
      throw error;
    }
  }

  static async markAsRead(notificacaoId, usuarioId) {
    try {
      const notificacoesQuery = query(
        collection(db, "notificacoesUsuarios"),
        where("notificacaoId", "==", notificacaoId),
        where("usuarioId", "==", usuarioId)
      );

      const notificacoesSnapshot = await getDocs(notificacoesQuery);
      
      for (const doc of notificacoesSnapshot.docs) {
        await updateDoc(doc.ref, {
          status: 'LIDA',
          dataLeitura: Timestamp.now()
        });
      }

      return true;
    } catch (error) {
      console.error("Erro ao marcar notificação como lida:", error);
      throw error;
    }
  }

  static async createSystemAlert(params) {
    const {
      tipo,
      severidade,
      mensagem,
      modulo,
      documentoId,
      documentoTipo,
      acaoRequerida = false
    } = params;

    try {
      const alert = {
        tipo,
        severidade,
        mensagem,
        modulo,
        documentoId,
        documentoTipo,
        acaoRequerida,
        status: 'ATIVO',
        dataCriacao: Timestamp.now(),
        dataResolucao: null
      };

      const alertRef = await addDoc(collection(db, "alertas"), alert);

      // Se for um alerta crítico, cria notificações para usuários relevantes
      if (severidade === 'CRITICO') {
        const usuarios = await this.getUsuariosResponsaveis(modulo);
        await this.createNotification({
          tipo: 'ALERTA_SISTEMA',
          titulo: `Alerta Crítico - ${tipo}`,
          mensagem,
          severidade: 'CRITICO',
          destinatarios: usuarios.map(u => u.id),
          modulo,
          documentoId,
          documentoTipo,
          acaoRequerida: true
        });
      }

      return alertRef.id;
    } catch (error) {
      console.error("Erro ao criar alerta do sistema:", error);
      throw error;
    }
  }

  static async resolveAlert(alertId, resolucao) {
    try {
      await updateDoc(doc(db, "alertas", alertId), {
        status: 'RESOLVIDO',
        dataResolucao: Timestamp.now(),
        resolucao
      });

      return true;
    } catch (error) {
      console.error("Erro ao resolver alerta:", error);
      throw error;
    }
  }

  static async getUsuariosResponsaveis(modulo) {
    try {
      const usuariosQuery = query(
        collection(db, "usuarios"),
        where("responsavelModulos", "array-contains", modulo)
      );

      const usuariosSnapshot = await getDocs(usuariosQuery);
      return usuariosSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Erro ao buscar usuários responsáveis:", error);
      throw error;
    }
  }

  // Método para registrar notificação de inconsistência
  static async registrarInconsistencia(dadosInconsistencia) {
    try {
      const notificacaoRef = await addDoc(collection(db, "notificacoesInconsistencia"), {
        tipo: dadosInconsistencia.tipo || 'ESTOQUE',
        nivel: dadosInconsistencia.nivel || 'AVISO',
        descricao: dadosInconsistencia.descricao,
        detalhes: dadosInconsistencia.detalhes || {},
        produtoId: dadosInconsistencia.produtoId,
        armazemId: dadosInconsistencia.armazemId,
        dataHora: Timestamp.now(),
        status: 'PENDENTE',
        resolvidoPor: null
      });

      // Enviar notificação por e-mail ou outro canal
      await this.enviarNotificacao(dadosInconsistencia);

      return notificacaoRef.id;
    } catch (error) {
      console.error("Erro ao registrar inconsistência:", error);
      throw error;
    }
  }

  // Método para enviar notificação
  static async enviarNotificacao(dadosInconsistencia) {
    // Implementação pode variar (e-mail, SMS, push notification)
    const destinatarios = await this.obterDestinatarios();
    
    const corpoNotificacao = `
      ALERTA DE INCONSISTÊNCIA DE ESTOQUE
      
      Tipo: ${dadosInconsistencia.tipo}
      Nível: ${dadosInconsistencia.nivel}
      Descrição: ${dadosInconsistencia.descricao}
      
      Detalhes Adicionais:
      ${JSON.stringify(dadosInconsistencia.detalhes, null, 2)}
    `;

    // Exemplo de envio de e-mail (necessário configurar serviço de e-mail)
    destinatarios.forEach(destinatario => {
      this.enviarEmail(destinatario.email, 'Inconsistência de Estoque', corpoNotificacao);
    });
  }

  // Método para obter destinatários de notificação
  static async obterDestinatarios() {
    try {
      const usuariosSnapshot = await db.collection('usuarios')
        .where('receberNotificacoesEstoque', '==', true)
        .get();
      
      return usuariosSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Erro ao obter destinatários:", error);
      return [];
    }
  }

  // Método de envio de e-mail (mock - substituir por serviço real)
  static async enviarEmail(email, assunto, corpo) {
    console.log(`Enviando e-mail para ${email}`);
    console.log(`Assunto: ${assunto}`);
    console.log(`Corpo: ${corpo}`);
    
    // Implementação real dependeria de serviço de e-mail
    // Exemplo com nodemailer ou serviço de terceiros
  }

  // Método para marcar inconsistência como resolvida
  static async marcarComoResolvido(notificacaoId, usuarioId) {
    try {
      await db.collection('notificacoesInconsistencia')
        .doc(notificacaoId)
        .update({
          status: 'RESOLVIDO',
          resolvidoPor: usuarioId,
          dataResolucao: Timestamp.now()
        });
    } catch (error) {
      console.error("Erro ao marcar inconsistência como resolvida:", error);
      throw error;
    }
  }
}