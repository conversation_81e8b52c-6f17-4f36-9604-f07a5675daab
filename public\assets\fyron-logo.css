/* FYRON MRP Logo Styles */

.fyron-logo {
    display: inline-block;
    transition: all 0.3s ease;
}

.fyron-logo:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Logo sizes */
.fyron-logo-large {
    width: 400px;
    height: 200px;
}

.fyron-logo-medium {
    width: 280px;
    height: 120px;
}

.fyron-logo-small {
    width: 200px;
    height: 80px;
}

.fyron-logo-compact {
    width: 150px;
    height: 60px;
}

.fyron-logo-mini {
    width: 100px;
    height: 40px;
}

/* Logo positioning */
.fyron-logo-center {
    display: block;
    margin: 0 auto;
}

.fyron-logo-left {
    float: left;
    margin-right: 20px;
}

.fyron-logo-right {
    float: right;
    margin-left: 20px;
}

/* Logo animations */
.fyron-logo-pulse {
    animation: fyron-pulse 3s infinite;
}

.fyron-logo-glow {
    animation: fyron-glow 4s infinite;
}

.fyron-logo-float {
    animation: fyron-float 6s ease-in-out infinite;
}

/* Keyframes */
@keyframes fyron-pulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

@keyframes fyron-glow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
    }
}

@keyframes fyron-float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive logo */
@media (max-width: 768px) {
    .fyron-logo-large {
        width: 280px;
        height: 120px;
    }
    
    .fyron-logo-medium {
        width: 200px;
        height: 80px;
    }
    
    .fyron-logo-small {
        width: 150px;
        height: 60px;
    }
    
    .fyron-logo-compact {
        width: 120px;
        height: 48px;
    }
    
    .fyron-logo-mini {
        width: 80px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .fyron-logo-large {
        width: 200px;
        height: 80px;
    }
    
    .fyron-logo-medium {
        width: 150px;
        height: 60px;
    }
    
    .fyron-logo-small {
        width: 120px;
        height: 48px;
    }
    
    .fyron-logo-compact {
        width: 100px;
        height: 40px;
    }
    
    .fyron-logo-mini {
        width: 60px;
        height: 24px;
    }
}

/* Logo container styles */
.fyron-logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.fyron-logo-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 0;
}

.fyron-logo-sidebar {
    display: block;
    margin: 20px auto;
    padding: 10px;
}

/* Dark theme adjustments */
.dark-theme .fyron-logo {
    filter: brightness(1.1) contrast(1.1);
}

/* Print styles */
@media print {
    .fyron-logo {
        filter: grayscale(100%);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .fyron-logo {
        filter: contrast(1.5);
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .fyron-logo-pulse,
    .fyron-logo-glow,
    .fyron-logo-float {
        animation: none;
    }
    
    .fyron-logo:hover {
        transform: none;
    }
}
