// Serviço centralizado para movimentações de estoque
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  runTransaction, 
  addDoc, 
  updateDoc, 
  getDoc,
  Timestamp,
  query,
  where,
  getDocs
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class MovementService {
  
  /**
   * Executa movimentação de estoque com transação atômica
   * @param {Object} movimentacao - Dados da movimentação
   * @returns {Promise<Object>} Resultado da operação
   */
  static async executeMovement(movimentacao) {
    try {
      const result = await runTransaction(db, async (transaction) => {
        // Validar dados obrigatórios
        this.validateMovementData(movimentacao);
        
        // Buscar estoque atual
        const estoqueRef = await this.findOrCreateStock(
          transaction, 
          movimentacao.produtoId, 
          movimentacao.armazemId
        );
        
        // Calcular novo saldo
        const estoqueDoc = await transaction.get(estoqueRef);
        const estoqueAtual = estoqueDoc.data();
        const saldoAtual = estoqueAtual?.saldo || 0;
        
        const novoSaldo = this.calculateNewBalance(
          saldoAtual, 
          movimentacao.quantidade, 
          movimentacao.tipo
        );
        
        // Validar saldo suficiente para saídas
        if (movimentacao.tipo === 'SAIDA' && novoSaldo < 0) {
          throw new Error(`Saldo insuficiente. Saldo atual: ${saldoAtual}, Tentativa de saída: ${movimentacao.quantidade}`);
        }
        
        // Registrar movimentação
        const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
        const movimentacaoData = {
          ...movimentacao,
          dataHora: Timestamp.now(),
          saldoAnterior: saldoAtual,
          saldoPosterior: novoSaldo,
          status: 'CONFIRMADA'
        };
        transaction.set(movimentacaoRef, movimentacaoData);
        
        // Atualizar estoque
        transaction.update(estoqueRef, {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now(),
          ultimoMovimentoId: movimentacaoRef.id
        });
        
        // Atualizar empenho se necessário
        if (movimentacao.atualizarEmpenho) {
          await this.updateReservation(transaction, movimentacao);
        }
        
        return {
          movimentacaoId: movimentacaoRef.id,
          saldoAnterior: saldoAtual,
          saldoPosterior: novoSaldo,
          success: true
        };
      });
      
      // Log da operação
      console.log('Movimentação executada com sucesso:', result);
      
      // Disparar eventos para outros sistemas
      await this.notifyMovement(result);
      
      return result;
      
    } catch (error) {
      console.error('Erro na movimentação:', error);
      throw new Error(`Falha na movimentação: ${error.message}`);
    }
  }
  
  /**
   * Valida dados da movimentação
   */
  static validateMovementData(movimentacao) {
    const required = ['produtoId', 'armazemId', 'tipo', 'quantidade'];
    
    for (const field of required) {
      if (!movimentacao[field]) {
        throw new Error(`Campo obrigatório não informado: ${field}`);
      }
    }
    
    if (!['ENTRADA', 'SAIDA'].includes(movimentacao.tipo)) {
      throw new Error('Tipo de movimentação inválido. Use ENTRADA ou SAIDA');
    }
    
    if (movimentacao.quantidade <= 0) {
      throw new Error('Quantidade deve ser maior que zero');
    }
  }
  
  /**
   * Encontra ou cria registro de estoque
   */
  static async findOrCreateStock(transaction, produtoId, armazemId) {
    const estoqueQuery = query(
      collection(db, "estoques"),
      where("produtoId", "==", produtoId),
      where("armazemId", "==", armazemId)
    );
    
    const estoqueSnapshot = await getDocs(estoqueQuery);
    
    if (!estoqueSnapshot.empty) {
      return estoqueSnapshot.docs[0].ref;
    }
    
    // Criar novo registro de estoque
    const novoEstoqueRef = doc(collection(db, "estoques"));
    transaction.set(novoEstoqueRef, {
      produtoId,
      armazemId,
      saldo: 0,
      saldoReservado: 0,
      dataCriacao: Timestamp.now(),
      ultimaMovimentacao: Timestamp.now()
    });
    
    return novoEstoqueRef;
  }
  
  /**
   * Calcula novo saldo
   */
  static calculateNewBalance(saldoAtual, quantidade, tipo) {
    return tipo === 'ENTRADA' 
      ? saldoAtual + quantidade 
      : saldoAtual - quantidade;
  }
  
  /**
   * Atualiza reservas/empenhos
   */
  static async updateReservation(transaction, movimentacao) {
    if (movimentacao.ordemProducaoId) {
      // Lógica para atualizar empenho de OP
      const opRef = doc(db, "ordensProducao", movimentacao.ordemProducaoId);
      const opDoc = await transaction.get(opRef);
      
      if (opDoc.exists()) {
        const opData = opDoc.data();
        // Atualizar quantidade empenhada do material
        // Implementar lógica específica conforme necessário
      }
    }
  }
  
  /**
   * Notifica outros sistemas sobre a movimentação
   */
  static async notifyMovement(result) {
    // Disparar eventos para:
    // - Sistema de alertas de estoque
    // - Recálculo de necessidades
    // - Atualização de dashboards
    
    try {
      // Exemplo: Verificar se precisa gerar alerta de estoque baixo
      await this.checkStockAlerts(result);
      
      // Exemplo: Atualizar cache de relatórios
      await this.updateReportsCache(result);
      
    } catch (error) {
      console.warn('Erro ao notificar sistemas:', error);
      // Não falhar a movimentação por erro de notificação
    }
  }
  
  /**
   * Verifica alertas de estoque
   */
  static async checkStockAlerts(result) {
    // Implementar verificação de estoque mínimo
    // Gerar alertas se necessário
  }
  
  /**
   * Atualiza cache de relatórios
   */
  static async updateReportsCache(result) {
    // Invalidar cache de relatórios que dependem do estoque
  }
  
  /**
   * Executa transferência entre armazéns
   */
  static async executeTransfer(transferData) {
    const { produtoId, armazemOrigemId, armazemDestinoId, quantidade, motivo } = transferData;
    
    try {
      const result = await runTransaction(db, async (transaction) => {
        // Saída do armazém origem
        const saidaResult = await this.executeMovement({
          produtoId,
          armazemId: armazemOrigemId,
          tipo: 'SAIDA',
          quantidade,
          tipoDocumento: 'TRANSFERENCIA',
          observacoes: `Transferência para ${armazemDestinoId} - ${motivo}`,
          numeroDocumento: `TRANSF-${Date.now()}`
        });
        
        // Entrada no armazém destino
        const entradaResult = await this.executeMovement({
          produtoId,
          armazemId: armazemDestinoId,
          tipo: 'ENTRADA',
          quantidade,
          tipoDocumento: 'TRANSFERENCIA',
          observacoes: `Transferência de ${armazemOrigemId} - ${motivo}`,
          numeroDocumento: `TRANSF-${Date.now()}`
        });
        
        // Registrar transferência
        const transferenciaRef = doc(collection(db, "transferenciasArmazem"));
        transaction.set(transferenciaRef, {
          produtoId,
          armazemOrigemId,
          armazemDestinoId,
          quantidade,
          motivo,
          dataHora: Timestamp.now(),
          movimentacaoSaidaId: saidaResult.movimentacaoId,
          movimentacaoEntradaId: entradaResult.movimentacaoId,
          status: 'CONCLUIDA'
        });
        
        return {
          transferenciaId: transferenciaRef.id,
          saida: saidaResult,
          entrada: entradaResult
        };
      });
      
      return result;
      
    } catch (error) {
      console.error('Erro na transferência:', error);
      throw new Error(`Falha na transferência: ${error.message}`);
    }
  }
  
  /**
   * Reverte movimentação (estorno)
   */
  static async reverseMovement(movimentacaoId, motivo) {
    try {
      // Buscar movimentação original
      const movOriginalDoc = await getDoc(doc(db, "movimentacoesEstoque", movimentacaoId));
      
      if (!movOriginalDoc.exists()) {
        throw new Error('Movimentação não encontrada');
      }
      
      const movOriginal = movOriginalDoc.data();
      
      // Criar movimentação de estorno (tipo inverso)
      const tipoEstorno = movOriginal.tipo === 'ENTRADA' ? 'SAIDA' : 'ENTRADA';
      
      const estorno = await this.executeMovement({
        produtoId: movOriginal.produtoId,
        armazemId: movOriginal.armazemId,
        tipo: tipoEstorno,
        quantidade: movOriginal.quantidade,
        tipoDocumento: 'ESTORNO',
        numeroDocumento: `EST-${movOriginal.numeroDocumento}`,
        observacoes: `Estorno de ${movOriginal.numeroDocumento} - ${motivo}`,
        movimentacaoOriginalId: movimentacaoId
      });
      
      // Marcar movimentação original como estornada
      await updateDoc(doc(db, "movimentacoesEstoque", movimentacaoId), {
        status: 'ESTORNADA',
        dataEstorno: Timestamp.now(),
        motivoEstorno: motivo,
        movimentacaoEstornoId: estorno.movimentacaoId
      });
      
      return estorno;
      
    } catch (error) {
      console.error('Erro no estorno:', error);
      throw new Error(`Falha no estorno: ${error.message}`);
    }
  }
}

// Exportar para uso global
window.MovementService = MovementService;
