<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ006 - Métricas para Fornecedores</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #e74c3c;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #e74c3c;
            margin-right: 10px;
            width: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-card.qualidade { border-left-color: #27ae60; }
        .metric-card.prazo { border-left-color: #3498db; }
        .metric-card.preco { border-left-color: #f39c12; }
        .metric-card.performance { border-left-color: #9b59b6; }

        .metric-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }

        .metric-label {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
        }

        .metric-trend {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .trend-up {
            color: #27ae60;
        }

        .trend-down {
            color: #e74c3c;
        }

        .trend-stable {
            color: #95a5a6;
        }

        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chart-container h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ranking-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .ranking-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ranking-list {
            display: grid;
            gap: 15px;
        }

        .ranking-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .ranking-item:hover {
            transform: translateX(5px);
        }

        .ranking-position {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 1.2em;
        }

        .ranking-position.first { background: #f1c40f; }
        .ranking-position.second { background: #95a5a6; }
        .ranking-position.third { background: #cd7f32; }
        .ranking-position.other { background: #bdc3c7; }

        .ranking-info {
            flex: 1;
        }

        .ranking-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .ranking-details {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .ranking-score {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .filters {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 PQ006 - Métricas para Fornecedores</h1>
            <p>Indicadores de performance e qualidade</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Monitorar performance de fornecedores através de indicadores-chave</li>
                    <li><i class="fas fa-chart-line"></i><strong>Métricas:</strong> Qualidade, prazo, preço, performance geral e tendências</li>
                    <li><i class="fas fa-trophy"></i><strong>Ranking:</strong> Classificação baseada em performance consolidada</li>
                    <li><i class="fas fa-bullseye"></i><strong>Melhoria:</strong> Identificação de oportunidades e ações corretivas</li>
                </ul>
            </div>

            <!-- Métricas Principais -->
            <div class="metrics-grid">
                <div class="metric-card qualidade">
                    <h4><i class="fas fa-award"></i> Qualidade Média</h4>
                    <div class="metric-value" id="qualidadeMedia">0%</div>
                    <div class="metric-label">Índice de Qualidade</div>
                    <div class="metric-trend" id="qualidadeTrend">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span>+2.5% vs mês anterior</span>
                    </div>
                </div>

                <div class="metric-card prazo">
                    <h4><i class="fas fa-clock"></i> Pontualidade</h4>
                    <div class="metric-value" id="pontualidadeMedia">0%</div>
                    <div class="metric-label">Entregas no Prazo</div>
                    <div class="metric-trend" id="pontualidadeTrend">
                        <i class="fas fa-arrow-down trend-down"></i>
                        <span>-1.2% vs mês anterior</span>
                    </div>
                </div>

                <div class="metric-card preco">
                    <h4><i class="fas fa-dollar-sign"></i> Competitividade</h4>
                    <div class="metric-value" id="competitividadeMedia">0%</div>
                    <div class="metric-label">Índice de Preço</div>
                    <div class="metric-trend" id="competitividadeTrend">
                        <i class="fas fa-minus trend-stable"></i>
                        <span>Estável vs mês anterior</span>
                    </div>
                </div>

                <div class="metric-card performance">
                    <h4><i class="fas fa-chart-bar"></i> Performance Geral</h4>
                    <div class="metric-value" id="performanceGeral">0%</div>
                    <div class="metric-label">Score Consolidado</div>
                    <div class="metric-trend" id="performanceTrend">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span>+0.8% vs mês anterior</span>
                    </div>
                </div>
            </div>

            <!-- Gráficos -->
            <div class="charts-section">
                <div class="chart-container">
                    <h3><i class="fas fa-line-chart"></i> Evolução Mensal</h3>
                    <canvas id="evolutionChart" width="400" height="200"></canvas>
                </div>

                <div class="chart-container">
                    <h3><i class="fas fa-pie-chart"></i> Distribuição por Categoria</h3>
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Ranking de Fornecedores -->
            <div class="ranking-section">
                <h3><i class="fas fa-trophy"></i> Ranking de Fornecedores</h3>
                <div class="ranking-list" id="rankingList">
                    <!-- Ranking será preenchido dinamicamente -->
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="periodoFilter">Período</label>
                    <select id="periodoFilter">
                        <option value="30">Últimos 30 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="180">Últimos 6 meses</option>
                        <option value="365">Último ano</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="categoriaFilter">Categoria</label>
                    <select id="categoriaFilter">
                        <option value="">Todas as Categorias</option>
                        <option value="MATERIA_PRIMA">Matéria Prima</option>
                        <option value="COMPONENTES">Componentes</option>
                        <option value="SERVICOS">Serviços</option>
                        <option value="EMBALAGENS">Embalagens</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="fornecedorFilter">Fornecedor</label>
                    <select id="fornecedorFilter">
                        <option value="">Todos os Fornecedores</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="metricaFilter">Métrica Principal</label>
                    <select id="metricaFilter">
                        <option value="geral">Performance Geral</option>
                        <option value="qualidade">Qualidade</option>
                        <option value="prazo">Pontualidade</option>
                        <option value="preco">Competitividade</option>
                    </select>
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="gerarRelatorio()">
                    <i class="fas fa-file-alt"></i> Gerar Relatório
                </button>
                <button class="btn btn-primary" onclick="exportarDados()">
                    <i class="fas fa-download"></i> Exportar Dados
                </button>
                <button class="btn btn-warning" onclick="alertasPerformance()">
                    <i class="fas fa-exclamation-triangle"></i> Alertas
                </button>
                <button class="btn btn-danger" onclick="planoMelhoria()">
                    <i class="fas fa-bullseye"></i> Plano de Melhoria
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando métricas...</p>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-chart-bar"></i>
                <h3>Dados insuficientes para métricas</h3>
                <p>Não há dados suficientes para gerar métricas de fornecedores.</p>
                <button class="btn btn-primary" onclick="sincronizarDados()" style="margin-top: 20px;">
                    <i class="fas fa-sync"></i> Sincronizar Dados
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let metricas = [];
        let fornecedores = [];
        let evolutionChart = null;
        let categoryChart = null;

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);
                
                // Carregar métricas e fornecedores
                const [metricasSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "metricasFornecedores")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                metricas = metricasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ006 - Dados carregados:', {
                    metricas: metricas.length,
                    fornecedores: fornecedores.length
                });

                populateFornecedorFilter();
                updateMetrics();
                renderCharts();
                renderRanking();
                showLoading(false);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ006:', error);
                showLoading(false);
                showError('Erro ao carregar métricas de fornecedores');
            }
        }

        function populateFornecedorFilter() {
            const select = document.getElementById('fornecedorFilter');
            select.innerHTML = '<option value="">Todos os Fornecedores</option>';
            
            fornecedores.forEach(fornecedor => {
                const option = document.createElement('option');
                option.value = fornecedor.id;
                option.textContent = fornecedor.razaoSocial || fornecedor.nome;
                select.appendChild(option);
            });
        }

        function updateMetrics() {
            // Calcular métricas médias (simulado)
            const qualidadeMedia = 87.5;
            const pontualidadeMedia = 92.3;
            const competitividadeMedia = 78.9;
            const performanceGeral = 86.2;

            document.getElementById('qualidadeMedia').textContent = qualidadeMedia.toFixed(1) + '%';
            document.getElementById('pontualidadeMedia').textContent = pontualidadeMedia.toFixed(1) + '%';
            document.getElementById('competitividadeMedia').textContent = competitividadeMedia.toFixed(1) + '%';
            document.getElementById('performanceGeral').textContent = performanceGeral.toFixed(1) + '%';
        }

        function renderCharts() {
            // Gráfico de Evolução
            const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
            evolutionChart = new Chart(evolutionCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                    datasets: [{
                        label: 'Qualidade',
                        data: [85, 87, 86, 88, 89, 87],
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Pontualidade',
                        data: [90, 92, 91, 93, 94, 92],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Competitividade',
                        data: [78, 79, 77, 80, 81, 79],
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // Gráfico de Categoria
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Matéria Prima', 'Componentes', 'Serviços', 'Embalagens'],
                    datasets: [{
                        data: [35, 30, 20, 15],
                        backgroundColor: [
                            '#e74c3c',
                            '#3498db',
                            '#27ae60',
                            '#f39c12'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }

        function renderRanking() {
            const container = document.getElementById('rankingList');
            
            // Dados simulados de ranking
            const ranking = [
                { nome: 'Fornecedor Alpha', score: 94.5, categoria: 'Matéria Prima' },
                { nome: 'Beta Componentes', score: 91.2, categoria: 'Componentes' },
                { nome: 'Gamma Serviços', score: 88.7, categoria: 'Serviços' },
                { nome: 'Delta Embalagens', score: 85.3, categoria: 'Embalagens' },
                { nome: 'Epsilon Materials', score: 82.1, categoria: 'Matéria Prima' }
            ];

            container.innerHTML = ranking.map((item, index) => {
                const position = index + 1;
                let positionClass = 'other';
                if (position === 1) positionClass = 'first';
                else if (position === 2) positionClass = 'second';
                else if (position === 3) positionClass = 'third';

                return `
                    <div class="ranking-item">
                        <div class="ranking-position ${positionClass}">${position}</div>
                        <div class="ranking-info">
                            <div class="ranking-name">${item.nome}</div>
                            <div class="ranking-details">${item.categoria}</div>
                        </div>
                        <div class="ranking-score">${item.score}%</div>
                    </div>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Funções globais
        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar filtros e atualizar gráficos
        };

        window.gerarRelatorio = function() {
            alert('📄 Funcionalidade em desenvolvimento: Gerar Relatório');
        };

        window.exportarDados = function() {
            alert('📊 Funcionalidade em desenvolvimento: Exportar Dados');
        };

        window.alertasPerformance = function() {
            alert('⚠️ Funcionalidade em desenvolvimento: Alertas de Performance');
        };

        window.planoMelhoria = function() {
            alert('🎯 Funcionalidade em desenvolvimento: Plano de Melhoria');
        };

        window.sincronizarDados = function() {
            alert('🔄 Funcionalidade em desenvolvimento: Sincronizar Dados');
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
