<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Produtos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --warning-color: #ff8c00;
            --danger-color: #dc3545;
            --header-bg: #354a5f;
            --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
        }

        .header {
            background: linear-gradient(135deg, var(--header-bg), var(--primary-color));
            color: white;
            padding: 20px 25px;
            border-radius: 12px 12px 0 0;
            margin: -20px -20px 25px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 26px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #0d6934;
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background-color: #e07600;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 25px;
            min-height: 600px;
        }

        .sidebar {
            background-color: var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-color);
        }

        .sidebar h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-section {
            margin-bottom: 25px;
        }

        .search-box {
            position: relative;
            margin-bottom: 15px;
        }

        .search-box input {
            width: 100%;
            padding: 10px 40px 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
        }

        .search-box .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .filters {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .filter-group select {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 13px;
        }

        .products-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: white;
        }

        .product-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .product-item:hover {
            background-color: #f8f9fa;
        }

        .product-item.selected {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-code {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 14px;
        }

        .product-desc {
            font-size: 13px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .product-type {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-top: 4px;
        }

        .type-PA { background-color: #2196F3; color: white; }
        .type-SP { background-color: #FF9800; color: white; }
        .type-MP { background-color: #4CAF50; color: white; }
        .type-SV { background-color: #9C27B0; color: white; }

        .form-content {
            background-color: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--secondary-color);
            border-radius: 8px 8px 0 0;
        }

        .tab {
            padding: 15px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab.active {
            background-color: white;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .tab:hover:not(.active) {
            color: var(--primary-color);
            background-color: rgba(8, 84, 160, 0.05);
        }

        .tab-content {
            display: none;
            padding: 25px;
        }

        .tab-content.active {
            display: block;
        }

        .section {
            margin-bottom: 30px;
            background-color: #fafbfc;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .section-header {
            background-color: var(--secondary-color);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-content {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .form-grid-4 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 14px;
        }

        .form-group label.required::after {
            content: ' *';
            color: var(--danger-color);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        .info-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .status-toggle {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .status-active {
            background-color: var(--success-color);
            color: white;
        }

        .status-inactive {
            background-color: var(--danger-color);
            color: white;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .conversion-result {
            background-color: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 6px;
            padding: 12px;
            margin-top: 15px;
            color: #2e7d32;
        }

        .actions-bar {
            position: sticky;
            bottom: 0;
            background-color: white;
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 25px -25px -25px -25px;
            border-radius: 0 0 12px 12px;
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .loading.active {
            display: flex;
        }

        .loading-content {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: var(--border-color);
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 300px 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                order: 2;
            }

            .form-content {
                order: 1;
            }

            .form-grid-2,
            .form-grid-3,
            .form-grid-4 {
                grid-template-columns: 1fr;
            }
        }

        .help-container {
            position: relative;
            display: inline-block;
            margin-left: 8px;
        }

        .help-button {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .help-tooltip {
            display: none;
            position: absolute;
            background-color: #333;
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            width: 200px;
            z-index: 1000;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-top: 5px;
        }

        .help-container:hover .help-tooltip {
            display: block;
        }

        .error-message {
            color: var(--danger-color);
            font-size: 12px;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-box"></i>
                Cadastro de Produtos
            </h1>
            <div class="header-actions">
                <button class="btn btn-warning" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Exportar Lista
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="search-section">
                    <h3>
                        <i class="fas fa-search"></i>
                        Buscar Produtos
                    </h3>
                    <div class="search-box">
                        <input type="text" 
                               id="searchInput" 
                               placeholder="Digite código ou descrição..." 
                               onkeyup="searchProducts()">
                        <i class="fas fa-search search-icon"></i>
                    </div>

                    <div class="filters">
                        <div class="filter-group">
                            <label>Tipo</label>
                            <select id="filterTipo" onchange="filterProducts()">
                                <option value="">Todos os tipos</option>
                                <option value="PA">PA - Produto Acabado</option>
                                <option value="SP">SP - Semi-Produto</option>
                                <option value="MP">MP - Matéria Prima</option>
                                <option value="SV">SV - Serviço</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Grupo</label>
                            <select id="filterGrupo" onchange="filterProducts()">
                                <option value="">Todos os grupos</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Status</label>
                            <select id="filterStatus" onchange="filterProducts()">
                                <option value="">Todos</option>
                                <option value="ativo">Ativo</option>
                                <option value="inativo">Inativo</option>
                            </select>
                        </div>

                        <button class="btn btn-secondary" onclick="clearFilters()" style="margin-top: 10px;">
                            <i class="fas fa-times"></i> Limpar Filtros
                        </button>
                    </div>
                </div>

                <div>
                    <h3>
                        <i class="fas fa-list"></i>
                        Lista de Produtos
                    </h3>
                    <div class="products-list" id="productsList">
                        <!-- Lista será preenchida dinamicamente -->
                    </div>
                </div>
            </div>

            <!-- Formulário Principal -->
            <div class="form-content">
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('basico')">
                        <i class="fas fa-info-circle"></i>
                        Dados Básicos
                    </button>
                    <button class="tab" onclick="switchTab('fiscal')">
                        <i class="fas fa-file-invoice"></i>
                        Fiscal
                    </button>
                    <button class="tab" onclick="switchTab('custos')">
                        <i class="fas fa-dollar-sign"></i>
                        Custos
                    </button>
                    <button class="tab" onclick="switchTab('estoque')">
                        <i class="fas fa-warehouse"></i>
                        Estoque
                    </button>
                    <button class="tab" onclick="switchTab('enderecamento')">
                        <i class="fas fa-map-marker-alt"></i>
                        Endereçamento
                    </button>
                </div>

                <div id="productForm">
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>Selecione um produto da lista ou clique em "Novo Produto" para começar</h3>
                        <p>Use a busca ao lado para encontrar produtos existentes</p>
                        <button class="btn btn-primary" onclick="openNewProductModal()" style="margin-top: 15px;">
                            <i class="fas fa-plus"></i> Novo Produto
                        </button>
                    </div>
                </div>

                <div class="actions-bar" id="actionsBar" style="display: none;">
                    <div>
                        <button class="btn btn-success" onclick="saveProduct()" disabled>
                            <i class="fas fa-save"></i> Salvar
                        </button>
                        <button class="btn btn-warning" onclick="exportProductToExcel()">
                            <i class="fas fa-file-excel"></i> Exportar
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-danger" onclick="deleteCurrentProduct()">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="loading">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>Processando...</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            addDoc, 
            updateDoc, 
            deleteDoc, 
            doc,
            getDoc,
            query,
            where
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let grupos = [];
        let familias = [];
        let armazens = [];
        let filteredProducts = [];
        let currentProductId = null;
        let hasChanges = false;
        let isProductActive = true;
        let usuarioAtual = null;

        // Inicialização
        window.onload = async function() {
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

            usuarioAtual = JSON.parse(userSession);
            await loadData();
            setupUI();
        };

        async function loadData() {
            try {
                document.querySelector('.loading').classList.add('active');

                const [produtosSnap, gruposSnap, familiasSnap, armazensSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "grupos")),
                    getDocs(collection(db, "familias")),
                    getDocs(collection(db, "armazens"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                filteredProducts = [...produtos];

                console.log(`Carregados: ${produtos.length} produtos, ${grupos.length} grupos, ${familias.length} famílias`);

            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados do sistema.");
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        }

        function setupUI() {
            setupFilters();
            displayProducts();
        }

        function setupFilters() {
            const filterGrupo = document.getElementById('filterGrupo');
            filterGrupo.innerHTML = '<option value="">Todos os grupos</option>';

            grupos.forEach(grupo => {
                const option = document.createElement('option');
                option.value = grupo.codigoGrupo;
                option.textContent = `${grupo.codigoGrupo} - ${grupo.nomeGrupo}`;
                filterGrupo.appendChild(option);
            });
        }

        function displayProducts() {
            const productsList = document.getElementById('productsList');

            if (filteredProducts.length === 0) {
                productsList.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                        <i class="fas fa-search" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <p>Nenhum produto encontrado</p>
                    </div>
                `;
                return;
            }

            productsList.innerHTML = filteredProducts.map(product => `
                <div class="product-item" onclick="loadProduct('${product.id}')">
                    <div class="product-code">${product.codigo}</div>
                    <div class="product-desc">${product.descricao}</div>
                    <span class="product-type type-${product.tipo}">${product.tipo}</span>
                </div>
            `).join('');
        }

        window.searchProducts = function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filterProducts();

            if (searchTerm) {
                filteredProducts = filteredProducts.filter(product => 
                    product.codigo.toLowerCase().includes(searchTerm) ||
                    product.descricao.toLowerCase().includes(searchTerm)
                );
            }

            displayProducts();
        };

        window.filterProducts = function() {
            const filterTipo = document.getElementById('filterTipo').value;
            const filterGrupo = document.getElementById('filterGrupo').value;
            const filterStatus = document.getElementById('filterStatus').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            filteredProducts = produtos.filter(product => {
                const matchesTipo = !filterTipo || product.tipo === filterTipo;
                const matchesGrupo = !filterGrupo || product.grupo === filterGrupo;
                const matchesStatus = !filterStatus || (product.status || 'ativo') === filterStatus;
                const matchesSearch = !searchTerm || 
                    product.codigo.toLowerCase().includes(searchTerm) ||
                    product.descricao.toLowerCase().includes(searchTerm);

                return matchesTipo && matchesGrupo && matchesStatus && matchesSearch;
            });

            displayProducts();
        };

        window.clearFilters = function() {
            document.getElementById('searchInput').value = '';
            document.getElementById('filterTipo').value = '';
            document.getElementById('filterGrupo').value = '';
            document.getElementById('filterStatus').value = '';
            filteredProducts = [...produtos];
            displayProducts();
        };

        window.loadProduct = function(productId) {
            if (hasChanges && !confirm('Há alterações não salvas. Deseja continuar?')) {
                return;
            }

            const product = produtos.find(p => p.id === productId);
            if (!product) return;

            currentProductId = productId;
            hasChanges = false;

            // Marcar produto como selecionado
            document.querySelectorAll('.product-item').forEach(item => {
                item.classList.remove('selected');
            });
            event?.target?.closest('.product-item')?.classList.add('selected');

            renderProductForm(product);
            document.getElementById('actionsBar').style.display = 'flex';
        };

        window.openNewProductModal = function() {
            if (hasChanges && !confirm('Há alterações não salvas. Deseja continuar?')) {
                return;
            }

            currentProductId = null;
            hasChanges = false;
            renderProductForm();
            document.getElementById('actionsBar').style.display = 'flex';
        };

        function renderProductForm(product = null) {
            const formContainer = document.getElementById('productForm');

            formContainer.innerHTML = `
                <!-- Aba Dados Básicos -->
                <div id="basicoTab" class="tab-content active">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-id-card"></i> Identificação
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="required">Código</label>
                                    <input type="text" id="codigo" value="${product?.codigo || ''}" required>
                                </div>
                                <div class="form-group">
                                    <label class="required">Tipo</label>
                                    <select id="tipo" required>
                                        <option value="PA" ${product?.tipo === 'PA' ? 'selected' : ''}>PA - Produto Acabado</option>
                                        <option value="SP" ${product?.tipo === 'SP' ? 'selected' : ''}>SP - Semi-Produto</option>
                                        <option value="MP" ${product?.tipo === 'MP' ? 'selected' : ''}>MP - Matéria Prima</option>
                                        <option value="SV" ${product?.tipo === 'SV' ? 'selected' : ''}>SV - Serviço</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="required">Unidade</label>
                                    <select id="unidade" required>
                                        <option value="PC" ${product?.unidade === 'PC' ? 'selected' : ''}>PC - Peça</option>
                                        <option value="KG" ${product?.unidade === 'KG' ? 'selected' : ''}>KG - Quilograma</option>
                                        <option value="MT" ${product?.unidade === 'MT' ? 'selected' : ''}>MT - Metro</option>
                                        <option value="M2" ${product?.unidade === 'M2' ? 'selected' : ''}>M2 - Metro Quadrado</option>
                                        <option value="M3" ${product?.unidade === 'M3' ? 'selected' : ''}>M3 - Metro Cúbico</option>
                                        <option value="LT" ${product?.unidade === 'LT' ? 'selected' : ''}>LT - Litro</option>
                                        <option value="CX" ${product?.unidade === 'CX' ? 'selected' : ''}>CX - Caixa</option>
                                        <option value="RL" ${product?.unidade === 'RL' ? 'selected' : ''}>RL - Rolo</option>
                                        <option value="TX" ${product?.unidade === 'TX' ? 'selected' : ''}>TX - Taxa</option>
                                        <option value="MM" ${product?.unidade === 'MM' ? 'selected' : ''}>MM - Milímetro</option>
                                        <option value="CM" ${product?.unidade === 'CM' ? 'selected' : ''}>CM - Centímetro</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Status</label>
                                    <button type="button" id="statusToggle" class="status-toggle ${(product?.status !== 'inativo') ? 'status-active' : 'status-inactive'}" onclick="toggleStatus()">
                                        ${(product?.status !== 'inativo') ? 'ATIVO' : 'INATIVO'}
                                    </button>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group" style="grid-column: 1 / -1;">
                                    <label class="required">Descrição</label>
                                    <input type="text" id="descricao" value="${product?.descricao || ''}" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-layer-group"></i> Classificação
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-2">
                                <div class="form-group">
                                    <label>Grupo</label>
                                    <input type="text" id="searchGrupo" placeholder="Pesquisar grupo..." oninput="filterGrupoOptions()" style="margin-bottom: 5px; width: 100%;">
                                    <select id="grupo">
                                        <option value="">Selecione um grupo</option>
                                        ${grupos
                                            .slice()
                                            .sort((a, b) => (a.nomeGrupo || '').localeCompare(b.nomeGrupo || ''))
                                            .map(g => `
                                                <option value="${g.codigoGrupo}" ${product?.grupo === g.codigoGrupo ? 'selected' : ''}>
                                                    ${g.codigoGrupo} - ${g.nomeGrupo}
                                                </option>
                                            `).join('')}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Família</label>
                                    <input type="text" id="searchFamilia" placeholder="Pesquisar família..." oninput="filterFamiliaOptions()" style="margin-bottom: 5px; width: 100%;">
                                    <select id="familia">
                                        <option value="">Selecione uma família</option>
                                        ${familias
                                            .slice()
                                            .sort((a, b) => (a.nomeFamilia || '').localeCompare(b.nomeFamilia || ''))
                                            .map(f => `
                                                <option value="${f.codigoFamilia}" ${product?.familia === f.codigoFamilia ? 'selected' : ''}>
                                                    ${f.codigoFamilia} - ${f.nomeFamilia}
                                                </option>
                                            `).join('')}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-exchange-alt"></i> Unidade de Conversão
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-3">
                                <div class="form-group">
                                    <label>Unidade Secundária</label>
                                    <select id="unidadeSecundaria">
                                        <option value="">Nenhuma</option>
                                        <option value="KG" ${product?.unidadeSecundaria === 'KG' ? 'selected' : ''}>KG - Quilograma</option>
                                        <option value="PC" ${product?.unidadeSecundaria === 'PC' ? 'selected' : ''}>PC - Peça</option>
                                        <option value="MT" ${product?.unidadeSecundaria === 'MT' ? 'selected' : ''}>MT - Metro</option>
                                        <option value="M2" ${product?.unidadeSecundaria === 'M2' ? 'selected' : ''}>M2 - Metro Quadrado</option>
                                        <option value="M3" ${product?.unidadeSecundaria === 'M3' ? 'selected' : ''}>M3 - Metro Cúbico</option>
                                        <option value="LT" ${product?.unidadeSecundaria === 'LT' ? 'selected' : ''}>LT - Litro</option>
                                        <option value="CX" ${product?.unidadeSecundaria === 'CX' ? 'selected' : ''}>CX - Caixa</option>
                                        <option value="RL" ${product?.unidadeSecundaria === 'RL' ? 'selected' : ''}>RL - Rolo</option>
                                        <option value="TX" ${product?.unidadeSecundaria === 'TX' ? 'selected' : ''}>TX - Taxa</option>
                                        <option value="MM" ${product?.unidadeSecundaria === 'MM' ? 'selected' : ''}>MM - Milímetro</option>
                                        <option value="CM" ${product?.unidadeSecundaria === 'CM' ? 'selected' : ''}>CM - Centímetro</option>
                                    </select>
                                    <div class="info-text">Para compras/fornecedores</div>
                                </div>
                                <div class="form-group">
                                    <label>Fator de Conversão</label>
                                    <input type="number" id="fatorConversao" value="${product?.fatorConversao || ''}" min="0.001" step="0.001">
                                    <div class="info-text">1 unidade principal = X secundárias</div>
                                </div>
                                <div class="form-group">
                                    <label>Teste de Conversão</label>
                                    <input type="number" id="testValue" min="0" step="0.001" placeholder="Valor para testar" oninput="testConversion()">
                                    <div class="info-text">Teste a conversão</div>
                                </div>
                            </div>
                            <div id="conversionResult" class="conversion-result" style="display: none;"></div>
                        </div>
                    </div>
                </div>

                <!-- Aba Fiscal -->
                <div id="fiscalTab" class="tab-content">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-file-invoice"></i> Dados Fiscais
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-4">
                                <div class="form-group">
                                    <label class="required">NCM</label>
                                    <input type="text" 
                                           id="ncm" 
                                           value="${product?.ncm || ''}" 
                                           placeholder="Código NCM" 
                                           maxlength="8" 
                                           pattern="[0-9]*"
                                           inputmode="numeric"
                                           oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                           required>
                                    <div class="info-text">Obrigatório. 8 dígitos numéricos.</div>
                                </div>
                                <div class="form-group">
                                    <label>CEST</label>
                                    <input type="text" id="cest" value="${product?.cest || ''}" placeholder="Código CEST" maxlength="7" pattern="\d{7}">
                                    <div class="info-text">7 dígitos numéricos (se aplicável).</div>
                                </div>
                                <div class="form-group">
                                    <label class="required">Origem</label>
                                    <select id="origem" required>
                                        <option value="">Selecione...</option>
                                        <option value="0" ${product?.origem === '0' ? 'selected' : ''}>0 - Nacional</option>
                                        <option value="1" ${product?.origem === '1' ? 'selected' : ''}>1 - Estrangeira - Importação direta</option>
                                        <option value="2" ${product?.origem === '2' ? 'selected' : ''}>2 - Estrangeira - Adquirida no mercado interno</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="required">Tipo Item</label>
                                    <select id="tipoItem" required>
                                        <option value="">Selecione...</option>
                                        <option value="00" ${product?.tipoItem === '00' ? 'selected' : ''}>00 - Mercadoria para Revenda</option>
                                        <option value="01" ${product?.tipoItem === '01' ? 'selected' : ''}>01 - Matéria-Prima</option>
                                        <option value="02" ${product?.tipoItem === '02' ? 'selected' : ''}>02 - Embalagem</option>
                                        <option value="03" ${product?.tipoItem === '03' ? 'selected' : ''}>03 - Produto em Processo</option>
                                        <option value="04" ${product?.tipoItem === '04' ? 'selected' : ''}>04 - Produto Acabado</option>
                                        <option value="05" ${product?.tipoItem === '05' ? 'selected' : ''}>05 - Subproduto</option>
                                        <option value="06" ${product?.tipoItem === '06' ? 'selected' : ''}>06 - Produto Intermediário</option>
                                        <option value="07" ${product?.tipoItem === '07' ? 'selected' : ''}>07 - Material de Uso e Consumo</option>
                                        <option value="08" ${product?.tipoItem === '08' ? 'selected' : ''}>08 - Ativo Imobilizado</option>
                                        <option value="09" ${product?.tipoItem === '09' ? 'selected' : ''}>09 - Serviços</option>
                                        <option value="10" ${product?.tipoItem === '10' ? 'selected' : ''}>10 - Outros insumos</option>
                                        <option value="99" ${product?.tipoItem === '99' ? 'selected' : ''}>99 - Outras</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>CFOP Padrão</label>
                                    <input type="text" id="cfopPadrao" value="${product?.cfopPadrao || ''}" placeholder="Ex: 5102">
                                    <div class="info-text">Opcional, mas recomendado para automação fiscal.</div>
                                </div>
                                <div class="form-group">
                                    <label>CST/CSOSN</label>
                                    <input type="text" id="cst" value="${product?.cst || ''}" placeholder="Ex: 101, 102, 201, 500, 900...">
                                    <div class="info-text">Opcional. Situação tributária do ICMS.</div>
                                </div>
                                <div class="form-group">
                                    <label>Unidade Fiscal</label>
                                    <input type="text" id="unidadeFiscal" value="${product?.unidadeFiscal || ''}" placeholder="Ex: UN, KG, M2">
                                    <div class="info-text">Opcional. Preencha se diferente da unidade comercial.</div>
                                </div>
                                <div class="form-group">
                                    <label>GTIN/EAN</label>
                                    <input type="text" id="gtin" value="${product?.gtin || ''}" placeholder="Código de barras" maxlength="14">
                                    <div class="info-text">Código de barras do produto.</div>
                                </div>
                                <div class="form-group">
                                    <label>GTIN Unidade Tributável</label>
                                    <input type="text" id="gtinTributavel" value="${product?.gtinTributavel || ''}" placeholder="Código de barras" maxlength="14">
                                    <div class="info-text">Código de barras da unidade tributável.</div>
                                </div>
                                <div class="form-group">
                                    <label>GTIN Produto Anterior</label>
                                    <input type="text" id="gtinAnterior" value="${product?.gtinAnterior || ''}" placeholder="Código de barras" maxlength="14">
                                    <div class="info-text">Código de barras do produto anterior (transformação).</div>
                                </div>
                                <div class="form-group">
                                    <label>Unidade IPI</label>
                                    <select id="unidadeIPI">
                                        <option value="">Selecione...</option>
                                        <option value="UN" ${product?.unidadeIPI === 'UN' ? 'selected' : ''}>UN - Unidade</option>
                                        <option value="PC" ${product?.unidadeIPI === 'PC' ? 'selected' : ''}>PC - Peça</option>
                                        <option value="KG" ${product?.unidadeIPI === 'KG' ? 'selected' : ''}>KG - Quilograma</option>
                                        <option value="MT" ${product?.unidadeIPI === 'MT' ? 'selected' : ''}>MT - Metro</option>
                                        <option value="M2" ${product?.unidadeIPI === 'M2' ? 'selected' : ''}>M2 - Metro Quadrado</option>
                                        <option value="M3" ${product?.unidadeIPI === 'M3' ? 'selected' : ''}>M3 - Metro Cúbico</option>
                                        <option value="LT" ${product?.unidadeIPI === 'LT' ? 'selected' : ''}>LT - Litro</option>
                                        <option value="MM" ${product?.unidadeIPI === 'MM' ? 'selected' : ''}>MM - Milímetro</option>
                                        <option value="CM" ${product?.unidadeIPI === 'CM' ? 'selected' : ''}>CM - Centímetro</option>
                                    </select>
                                    <div class="info-text">Unidade de medida para cálculo do IPI.</div>
                                </div>
                                <div class="form-group">
                                    <label>Unidade ICMS</label>
                                    <select id="unidadeICMS">
                                        <option value="">Selecione...</option>
                                        <option value="UN" ${product?.unidadeICMS === 'UN' ? 'selected' : ''}>UN - Unidade</option>
                                        <option value="PC" ${product?.unidadeICMS === 'PC' ? 'selected' : ''}>PC - Peça</option>
                                        <option value="KG" ${product?.unidadeICMS === 'KG' ? 'selected' : ''}>KG - Quilograma</option>
                                        <option value="MT" ${product?.unidadeICMS === 'MT' ? 'selected' : ''}>MT - Metro</option>
                                        <option value="M2" ${product?.unidadeICMS === 'M2' ? 'selected' : ''}>M2 - Metro Quadrado</option>
                                        <option value="M3" ${product?.unidadeICMS === 'M3' ? 'selected' : ''}>M3 - Metro Cúbico</option>
                                        <option value="LT" ${product?.unidadeICMS === 'LT' ? 'selected' : ''}>LT - Litro</option>
                                        <option value="MM" ${product?.unidadeICMS === 'MM' ? 'selected' : ''}>MM - Milímetro</option>
                                        <option value="CM" ${product?.unidadeICMS === 'CM' ? 'selected' : ''}>CM - Centímetro</option>
                                    </select>
                                    <div class="info-text">Unidade de medida para cálculo do ICMS.</div>
                                </div>
                                <div class="form-group">
                                    <label>Tempo de Produção (min)</label>
                                    <input type="number" id="tempoProducao" value="${product?.tempoProducao || ''}" min="0" step="1">
                                    <div class="info-text">Tempo médio de produção em minutos.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aba Custos -->
                <div id="custosTab" class="tab-content">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-dollar-sign"></i> Informações de Custos
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-4">
                                <div class="form-group">
                                    <label>Custo Médio</label>
                                    <input type="number" id="custoMedio" value="${product?.custoMedio || ''}" min="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label>Último Custo</label>
                                    <input type="number" id="ultimoCusto" value="${product?.ultimoCusto || ''}" min="0" step="0.01" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Preço de Venda</label>
                                    <input type="number" id="precoVenda" value="${product?.precoVenda || ''}" min="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label>Margem de Lucro (%)</label>
                                    <input type="number" id="margemLucro" value="${product?.margemLucro || ''}" min="0" max="100" step="0.01">
                                </div>
                            </div>
                            <div class="form-grid-3">
                                <div class="form-group">
                                    <label>Método de Custeio</label>
                                    <select id="metodoCusteio">
                                        <option value="padrao" ${product?.metodoCusteio === 'padrao' ? 'selected' : ''}>Custo Padrão</option>
                                        <option value="medio" ${product?.metodoCusteio === 'medio' ? 'selected' : ''}>Custo Médio</option>
                                        <option value="fifo" ${product?.metodoCusteio === 'fifo' ? 'selected' : ''}>FIFO</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Centro de Custos Obrigatório</label>
                                    <label class="switch">
                                        <input type="checkbox" id="centroCustoObrigatorio" ${product?.centroCustoObrigatorio ? 'checked' : ''}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aba Estoque -->
                <div id="estoqueTab" class="tab-content">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-warehouse"></i> Parâmetros de Estoque
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-4">
                                <div class="form-group">
                                    <label>Estoque Mínimo</label>
                                    <input type="number" id="estoqueMinimo" value="${product?.estoqueMinimo || ''}" min="0" step="0.001">
                                </div>
                                <div class="form-group">
                                    <label>Estoque Máximo</label>
                                    <input type="number" id="estoqueMaximo" value="${product?.estoqueMaximo || ''}" min="0" step="0.001">
                                </div>
                                <div class="form-group">
                                    <label>Ponto de Pedido</label>
                                    <input type="number" id="pontoPedido" value="${product?.pontoPedido || ''}" min="0" step="0.001">
                                </div>
                                <div class="form-group">
                                    <label>Lote de Compra</label>
                                    <input type="number" id="loteCompra" value="${product?.loteCompra || ''}" min="0" step="0.001">
                                </div>
                            </div>
                            <div class="form-grid-2">
                                <div class="form-group">
                                    <label>Rastreabilidade por Lote</label>
                                    <label class="switch">
                                        <input type="checkbox" id="rastreabilidadeLote" ${product?.rastreabilidadeLote ? 'checked' : ''}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>Inspeção no Recebimento</label>
                                    <select id="inspecaoRecebimento">
                                        <option value="nao" ${product?.inspecaoRecebimento === 'nao' ? 'selected' : ''}>Não</option>
                                        <option value="sim" ${product?.inspecaoRecebimento === 'sim' ? 'selected' : ''}>Sim</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aba Endereçamento -->
                <div id="enderecamentoTab" class="tab-content">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-map-marker-alt"></i> Endereçamento no Armazém
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-4">
                                <div class="form-group">
                                    <label class="required">Armazém Padrão</label>
                                    <select id="armazemPadrao" required>
                                        <option value="">Selecione o armazém...</option>
                                        ${armazens.map(a => `
                                            <option value="${a.id}" ${product?.armazemPadraoId === a.id ? 'selected' : ''}>
                                                ${a.codigo} - ${a.nome}
                                            </option>
                                        `).join('')}
                                    </select>
                                    <div class="info-text">Armazém padrão para movimentações</div>
                                </div>
                                <div class="form-group">
                                    <label>Corredor</label>
                                    <input type="text" id="corredor" value="${product?.corredor || ''}" placeholder="Ex: A1">
                                </div>
                                <div class="form-group">
                                    <label>Prateleira</label>
                                    <input type="text" id="prateleira" value="${product?.prateleira || ''}" placeholder="Ex: P01">
                                </div>
                                <div class="form-group">
                                    <label>Posição</label>
                                    <input type="text" id="posicao" value="${product?.posicao || ''}" placeholder="Ex: 001">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Configurar o status atual
            isProductActive = product ? (product.status !== 'inativo') : true;

            // Adicionar event listeners
            setupFormEventListeners();
        }

        function setupFormEventListeners() {
            // Marcar como alterado quando houver mudanças
            document.querySelectorAll('#productForm input, #productForm select, #productForm textarea').forEach(element => {
                element.addEventListener('change', markAsChanged);
                element.addEventListener('input', markAsChanged);
            });
        }

        function markAsChanged() {
            hasChanges = true;
            document.querySelector('.btn-success').disabled = false;
        }

        window.switchTab = function(tabName) {
            // Esconder todas as abas
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Mostrar a aba selecionada
            document.getElementById(`${tabName}Tab`).classList.add('active');
            event.target.classList.add('active');
        };

        window.toggleStatus = function() {
            const statusButton = document.getElementById('statusToggle');
            isProductActive = !isProductActive;

            if (isProductActive) {
                statusButton.textContent = 'ATIVO';
                statusButton.classList.remove('status-inactive');
                statusButton.classList.add('status-active');
            } else {
                statusButton.textContent = 'INATIVO';
                statusButton.classList.remove('status-active');
                statusButton.classList.add('status-inactive');
            }
            markAsChanged();
        };

        window.testConversion = function() {
            const testValue = parseFloat(document.getElementById('testValue').value);
            const unidadePrincipal = document.getElementById('unidade').value;
            const unidadeSecundaria = document.getElementById('unidadeSecundaria').value;
            const fatorConversao = parseFloat(document.getElementById('fatorConversao').value);

            const resultDiv = document.getElementById('conversionResult');

            if (!testValue || !fatorConversao || !unidadeSecundaria) {
                resultDiv.style.display = 'none';
                return;
            }

            const result = testValue * fatorConversao;
            resultDiv.innerHTML = `
                <i class="fas fa-calculator"></i>
                <strong>Resultado da Conversão:</strong><br>
                ${testValue} ${unidadePrincipal} = ${result.toFixed(3)} ${unidadeSecundaria}
                <br><small>(1 ${unidadePrincipal} = ${fatorConversao} ${unidadeSecundaria})</small>
            `;
            resultDiv.style.display = 'block';
        };

        // Função para validar o formulário
        async function validateForm() {
            try {
                // Carrega as validações do Firestore
                const validacoes = await window.getValidacoesProduto();
                
                const requiredFields = [
                    { id: 'codigo', name: 'Código' },
                    { id: 'descricao', name: 'Descrição' },
                    { id: 'tipo', name: 'Tipo' },
                    { id: 'unidade', name: 'Unidade' },
                    { id: 'armazemPadrao', name: 'Armazém Padrão' }
                ];

                let isValid = true;
                let errorMessages = [];
                
                // Validação dos campos obrigatórios
                for (const field of requiredFields) {
                    const element = document.getElementById(field.id);
                    if (!element || !element.value.trim()) {
                        element.style.borderColor = 'var(--danger-color)';
                        errorMessages.push(`O campo ${field.name} é obrigatório.`);
                        isValid = false;
                    } else {
                        element.style.borderColor = 'var(--border-color)';
                    }
                }

                // Validação específica do código
                if (validacoes.validarCodigo) {
                    const codigo = document.getElementById('codigo').value.trim();
                    if (codigo.length > validacoes.tamanhoMaxCodigo) {
                        errorMessages.push(`O código não pode ter mais de ${validacoes.tamanhoMaxCodigo} caracteres.`);
                        isValid = false;
                    }
                    
                    if (validacoes.formatoCodigo === 'numerico' && !/^\d+$/.test(codigo)) {
                        errorMessages.push('O código deve conter apenas números.');
                        isValid = false;
                    }

                    // Verificar duplicidade de código
                    if (!currentProductId) {
                        const codigoExiste = produtos.some(p => p.codigo === codigo);
                        if (codigoExiste) {
                            errorMessages.push('Já existe um produto com este código.');
                            isValid = false;
                        }
                    }
                }

                // Validação de dimensões
                if (validacoes.validarDimensoes) {
                    const altura = parseFloat(document.getElementById('altura')?.value) || 0;
                    const largura = parseFloat(document.getElementById('largura')?.value) || 0;
                    const profundidade = parseFloat(document.getElementById('profundidade')?.value) || 0;
                    
                    if (isNaN(altura) || altura < 0) {
                        errorMessages.push('A altura deve ser um número positivo.');
                        isValid = false;
                    }
                    
                    if (isNaN(largura) || largura < 0) {
                        errorMessages.push('A largura deve ser um número positivo.');
                        isValid = false;
                    }
                    
                    if (isNaN(profundidade) || profundidade < 0) {
                        errorMessages.push('A profundidade deve ser um número positivo.');
                        isValid = false;
                    }
                }

                // Validação de peso
                if (validacoes.validarPeso) {
                    const pesoBruto = parseFloat(document.getElementById('pesoBruto')?.value) || 0;
                    const pesoLiquido = parseFloat(document.getElementById('pesoLiquido')?.value) || 0;
                    
                    if (isNaN(pesoBruto) || pesoBruto < 0) {
                        errorMessages.push('O peso bruto deve ser um número positivo.');
                        isValid = false;
                    }
                    
                    if (isNaN(pesoLiquido) || pesoLiquido < 0) {
                        errorMessages.push('O peso líquido deve ser um número positivo.');
                        isValid = false;
                    }
                    
                    if (pesoLiquido > pesoBruto) {
                        errorMessages.push('O peso líquido não pode ser maior que o peso bruto.');
                        isValid = false;
                    }
                }

                // Validação de NCM
                if (validacoes.validarNCM) {
                    const ncm = document.getElementById('ncm')?.value.replace(/[^0-9]/g, '') || '';
                    if (ncm && ncm.length !== 8) {
                        errorMessages.push('O NCM deve conter 8 dígitos.');
                        isValid = false;
                    }
                }

                // Validação de GTIN/EAN
                if (validacoes.validarGTIN) {
                    const gtin = document.getElementById('gtin')?.value.replace(/[^0-9]/g, '') || '';
                    if (gtin && gtin.length !== 13) {
                        errorMessages.push('O GTIN/EAN deve conter 13 dígitos.');
                        isValid = false;
                    }
                }

                // Mostrar todas as mensagens de erro de uma vez
                if (!isValid) {
                    alert(errorMessages.join('\n'));
                }

                // Atualizar estado do botão de salvar
                const saveButton = document.querySelector('#actionsBar .btn-success');
                if (saveButton) {
                    saveButton.disabled = !isValid;
                }

                return isValid;
            } catch (error) {
                console.error('Erro ao validar formulário:', error);
                alert('Erro ao validar formulário. Por favor, tente novamente.');
                return false;
            }
        }

        // Função para salvar o produto
        window.saveProduct = async function() {
            try {
                document.querySelector('.loading').classList.add('active');
                
                // Primeiro valida o formulário básico (campos obrigatórios)
                const isValid = await validateForm();
                if (!isValid) {
                    document.querySelector('.loading').classList.remove('active');
                    return;
                }
                
                const productData = collectFormData();
                const isEdit = !!currentProductId;
                
                // Validações específicas do produto
                const isProductValid = await validateProductForm(productData, isEdit);
                if (!isProductValid) {
                    document.querySelector('.loading').classList.remove('active');
                    return;
                }

                // Adicionar informações de auditoria
                productData.ultimaAtualizacao = new Date();
                productData.usuarioAtualizacao = usuarioAtual?.email || 'sistema';
                
                if (isEdit) {
                    // Verificar se as alterações são permitidas
                    const podeAlterar = await validateProductChanges(currentProductId, productData);
                    if (!podeAlterar) {
                        document.querySelector('.loading').classList.remove('active');
                        return;
                    }

                    // Remover campos que não devem ser atualizados
                    delete productData.dataCadastro;
                    delete productData.usuarioCadastro;

                    const produtoRef = doc(db, "produtos", currentProductId);
                    await updateDoc(produtoRef, productData);
                    const index = produtos.findIndex(p => p.id === currentProductId);
                    if (index !== -1) {
                        produtos[index] = { ...produtos[index], ...productData };
                    }
                    alert('Produto atualizado com sucesso!');
                } else {
                    // Adicionar data de cadastro para novos produtos
                    productData.dataCadastro = new Date();
                    productData.usuarioCadastro = usuarioAtual?.email || 'sistema';
                    
                    const docRef = await addDoc(collection(db, "produtos"), productData);
                    const newProduct = { id: docRef.id, ...productData };
                    produtos.push(newProduct);
                    currentProductId = docRef.id;
                    alert('Produto cadastrado com sucesso!');
                }

                hasChanges = false;
                document.querySelector('.btn-success').disabled = true;
                filteredProducts = [...produtos];
                displayProducts();

                // Registrar no histórico
                await registrarHistorico(isEdit ? 'atualizacao' : 'cadastro', productData);

            } catch (error) {
                console.error("Erro ao salvar produto:", error);
                alert("Erro ao salvar produto: " + error.message);
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        // Função para registrar histórico de alterações
        async function registrarHistorico(tipo, dados) {
            try {
                const historicoRef = collection(db, 'historico_produtos');
                const historicoData = {
                    produtoId: currentProductId,
                    tipo: tipo,
                    dados: dados,
                    data: new Date(),
                    usuario: usuarioAtual?.email || 'sistema'
                };
                
                await addDoc(historicoRef, historicoData);
            } catch (error) {
                console.error('Erro ao registrar histórico:', error);
            }
        }

        // Função para verificar se o produto está em uso
        async function checkProductUsage(productId) {
            try {
                const uso = {
                    emUso: false,
                    mensagem: '',
                    detalhes: {
                        ordensProducao: 0,
                        necessidadesCompra: 0,
                        estruturas: 0,
                        cotacoes: 0,
                        pedidosCompra: 0
                    }
                };

                // Verificar uso em OPs
                const opsRef = collection(db, 'ordens_producao');
                const opsQuery = query(opsRef, 
                    where('status', 'in', ['Pendente', 'Em Andamento', 'Aguardando Material']),
                    where('itens', 'array-contains', { produtoId: productId })
                );
                const opsSnapshot = await getDocs(opsQuery);
                
                if (!opsSnapshot.empty) {
                    uso.emUso = true;
                    uso.detalhes.ordensProducao = opsSnapshot.size;
                    uso.mensagem += `- ${opsSnapshot.size} Ordem(ns) de Produção em aberto\n`;
                }

                // Verificar uso em necessidades de compra
                const comprasRef = collection(db, 'necessidades_compra');
                const comprasQuery = query(comprasRef,
                    where('status', 'in', ['Pendente', 'Em Cotação', 'Aprovada']),
                    where('itens', 'array-contains', { produtoId: productId })
                );
                const comprasSnapshot = await getDocs(comprasQuery);
                
                if (!comprasSnapshot.empty) {
                    uso.emUso = true;
                    uso.detalhes.necessidadesCompra = comprasSnapshot.size;
                    uso.mensagem += `- ${comprasSnapshot.size} Necessidade(s) de Compra em aberto\n`;
                }

                // Verificar uso em estruturas
                const estruturasRef = collection(db, 'estruturas');
                const estruturasQuery = query(estruturasRef,
                    where('itens', 'array-contains', { produtoId: productId })
                );
                const estruturasSnapshot = await getDocs(estruturasQuery);
                
                if (!estruturasSnapshot.empty) {
                    uso.emUso = true;
                    uso.detalhes.estruturas = estruturasSnapshot.size;
                    uso.mensagem += `- ${estruturasSnapshot.size} Estrutura(s) de Produto\n`;
                }

                // Verificar uso em cotações
                const cotacoesRef = collection(db, 'cotacoes');
                const cotacoesQuery = query(cotacoesRef,
                    where('status', 'in', ['Pendente', 'Em Análise']),
                    where('itens', 'array-contains', { produtoId: productId })
                );
                const cotacoesSnapshot = await getDocs(cotacoesQuery);
                
                if (!cotacoesSnapshot.empty) {
                    uso.emUso = true;
                    uso.detalhes.cotacoes = cotacoesSnapshot.size;
                    uso.mensagem += `- ${cotacoesSnapshot.size} Cotações em aberto\n`;
                }

                // Verificar uso em pedidos de compra
                const pedidosRef = collection(db, 'pedidos_compra');
                const pedidosQuery = query(pedidosRef,
                    where('status', 'in', ['Pendente', 'Aprovado', 'Em Recebimento']),
                    where('itens', 'array-contains', { produtoId: productId })
                );
                const pedidosSnapshot = await getDocs(pedidosQuery);
                
                if (!pedidosSnapshot.empty) {
                    uso.emUso = true;
                    uso.detalhes.pedidosCompra = pedidosSnapshot.size;
                    uso.mensagem += `- ${pedidosSnapshot.size} Pedido(s) de Compra em aberto\n`;
                }

                // Adicionar mensagem de restrição
                if (uso.emUso) {
                    uso.mensagem = 'Não é possível realizar esta operação pois o produto está em uso:\n\n' + uso.mensagem;
                }

                return uso;
            } catch (error) {
                console.error('Erro ao verificar uso do produto:', error);
                return { 
                    emUso: false, 
                    mensagem: 'Erro ao verificar uso do produto',
                    detalhes: {
                        ordensProducao: 0,
                        necessidadesCompra: 0,
                        estruturas: 0,
                        cotacoes: 0,
                        pedidosCompra: 0
                    }
                };
            }
        }

        // Função para validar alterações em produto em uso
        async function validateProductChanges(productId, newData) {
            const uso = await checkProductUsage(productId);
            if (!uso.emUso) return true;

            const produtoAtual = produtos.find(p => p.id === productId);
            if (!produtoAtual) return true;

            const restricoes = [];

            // Verificar alteração de unidade
            if (produtoAtual.unidade !== newData.unidade) {
                restricoes.push('Alteração de unidade de medida');
            }

            // Verificar alteração de tipo
            if (produtoAtual.tipo !== newData.tipo) {
                restricoes.push('Alteração de tipo do produto');
            }

            // Verificar inativação
            if (produtoAtual.status === 'ativo' && newData.status === 'inativo') {
                restricoes.push('Inativação do produto');
            }

            if (restricoes.length > 0) {
                const mensagem = `Não é possível realizar as seguintes alterações:\n\n${restricoes.join('\n')}\n\nMotivo: ${uso.mensagem}`;
                alert(mensagem);
                return false;
            }

            return true;
        }

        function collectFormData() {
            const unidadeSecundaria = document.getElementById('unidadeSecundaria').value;
            const fatorConversao = parseFloat(document.getElementById('fatorConversao').value);

            return {
                codigo: document.getElementById('codigo').value.trim(),
                descricao: document.getElementById('descricao').value.trim(),
                tipo: document.getElementById('tipo').value,
                unidade: document.getElementById('unidade').value,
                unidadeSecundaria: unidadeSecundaria || null,
                fatorConversao: fatorConversao || null,
                grupo: document.getElementById('grupo').value || null,
                familia: document.getElementById('familia').value || null,
                ncm: document.getElementById('ncm').value || null,
                cest: document.getElementById('cest').value || null,
                origem: document.getElementById('origem').value || '0',
                tipoItem: document.getElementById('tipoItem').value || '00',
                cfopPadrao: document.getElementById('cfopPadrao').value || null,
                cst: document.getElementById('cst').value || null,
                unidadeFiscal: document.getElementById('unidadeFiscal').value || null,
                gtin: document.getElementById('gtin').value || null,
                gtinTributavel: document.getElementById('gtinTributavel').value || null,
                gtinAnterior: document.getElementById('gtinAnterior').value || null,
                unidadeIPI: document.getElementById('unidadeIPI').value || null,
                unidadeICMS: document.getElementById('unidadeICMS').value || null,
                tempoProducao: parseInt(document.getElementById('tempoProducao').value) || null,
                custoMedio: parseFloat(document.getElementById('custoMedio').value) || 0,
                ultimoCusto: parseFloat(document.getElementById('ultimoCusto').value) || 0,
                precoVenda: parseFloat(document.getElementById('precoVenda').value) || 0,
                margemLucro: parseFloat(document.getElementById('margemLucro').value) || 0,
                estoqueMinimo: parseFloat(document.getElementById('estoqueMinimo').value) || 0,
                estoqueMaximo: parseFloat(document.getElementById('estoqueMaximo').value) || 0,
                pontoPedido: parseFloat(document.getElementById('pontoPedido').value) || 0,
                loteCompra: parseFloat(document.getElementById('loteCompra').value) || 0,
                armazemPadraoId: document.getElementById('armazemPadrao').value || null,
                corredor: document.getElementById('corredor').value || null,
                prateleira: document.getElementById('prateleira').value || null,
                posicao: document.getElementById('posicao').value || null,
                rastreabilidadeLote: document.getElementById('rastreabilidadeLote').checked,
                inspecaoRecebimento: document.getElementById('inspecaoRecebimento').value,
                metodoCusteio: document.getElementById('metodoCusteio').value,
                centroCustoObrigatorio: document.getElementById('centroCustoObrigatorio').checked,
                status: isProductActive ? 'ativo' : 'inativo',
                dataCadastro: currentProductId ? undefined : new Date(),
                dataUltimaAlteracao: new Date(),
                usuarioUltimaAlteracao: {
                    id: usuarioAtual.id,
                    nome: usuarioAtual.nome,
                    email: usuarioAtual.email
                }
            };
        }

        window.deleteCurrentProduct = async function() {
            if (!currentProductId) {
                alert("Nenhum produto está selecionado.");
                return;
            }

            if (!confirm("Tem certeza que deseja excluir este produto? Esta ação não pode ser desfeita.")) {
                return;
            }

            try {
                document.querySelector('.loading').classList.add('active');

                // Verificar se o produto está em uso
                const uso = await checkProductUsage(currentProductId);
                if (uso.emUso) {
                    alert(`Não é possível excluir o produto pois ele está em uso:\n\n${uso.mensagem}`);
                    document.querySelector('.loading').classList.remove('active');
                    return;
                }

                await deleteDoc(doc(db, "produtos", currentProductId));
                produtos = produtos.filter(p => p.id !== currentProductId);

                document.getElementById('productForm').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>Produto excluído com sucesso</h3>
                        <p>Selecione outro produto da lista ou crie um novo</p>
                        <button class="btn btn-primary" onclick="openNewProductModal()" style="margin-top: 15px;">
                            <i class="fas fa-plus"></i> Novo Produto
                        </button>
                    </div>
                `;

                currentProductId = null;
                hasChanges = false;
                document.getElementById('actionsBar').style.display = 'none';

                filteredProducts = [...produtos];
                displayProducts();

            } catch (error) {
                console.error("Erro ao excluir produto:", error);
                alert("Erro ao excluir o produto: " + error.message);
            } finally {
                document.querySelector('.loading').classList.remove('active');
            }
        };

        window.exportToExcel = function() {
            try {
                const exportData = filteredProducts.map(product => {
                    const grupo = grupos.find(g => g.codigoGrupo === product.grupo);
                    const familia = familias.find(f => f.codigoFamilia === product.familia);
                    const armazem = armazens.find(a => a.id === product.armazemPadraoId);

                    return {
                        'Código': product.codigo,
                        'Descrição': product.descricao,
                        'Tipo': product.tipo,
                        'Unidade': product.unidade,
                        'Grupo': grupo ? `${grupo.codigoGrupo} - ${grupo.nomeGrupo}` : '',
                        'Família': familia ? `${familia.codigoFamilia} - ${familia.nomeFamilia}` : '',
                        'Armazém Padrão': armazem ? `${armazem.codigo} - ${armazem.nome}` : '',
                        'Status': product.status || 'ativo'
                    };
                });

                const ws = XLSX.utils.json_to_sheet(exportData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'Produtos');

                const fileName = `Produtos_${new Date().toLocaleDateString('pt-BR').replace(/\//g, '-')}.xlsx`;
                XLSX.writeFile(wb, fileName);

                alert('Exportação concluída com sucesso!');
            } catch (error) {
                console.error('Erro ao exportar:', error);
                alert('Erro ao exportar para Excel.');
            }
        };

        window.exportProductToExcel = function() {
            if (!currentProductId) {
                alert('Selecione um produto primeiro.');
                return;
            }

            try {
                const product = produtos.find(p => p.id === currentProductId);
                const grupo = grupos.find(g => g.codigoGrupo === product.grupo);
                const familia = familias.find(f => f.codigoFamilia === product.familia);
                const armazem = armazens.find(a => a.id === product.armazemPadraoId);

                const exportData = [{
                    'Código': product.codigo,
                    'Descrição': product.descricao,
                    'Tipo': product.tipo,
                    'Unidade': product.unidade,
                    'Unidade Secundária': product.unidadeSecundaria || '',
                    'Fator de Conversão': product.fatorConversao || '',
                    'Grupo': grupo ? `${grupo.codigoGrupo} - ${grupo.nomeGrupo}` : '',
                    'Família': familia ? `${familia.codigoFamilia} - ${familia.nomeFamilia}` : '',
                    'Armazém Padrão': armazem ? `${armazem.codigo} - ${armazem.nome}` : '',
                    'NCM': product.ncm || '',
                    'CEST': product.cest || '',
                    'Custo Médio': product.custoMedio || 0,
                    'Preço de Venda': product.precoVenda || 0,
                    'Status': product.status || 'ativo'
                }];

                const ws = XLSX.utils.json_to_sheet(exportData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'Produto');

                const fileName = `Produto_${product.codigo}_${new Date().toLocaleDateString('pt-BR').replace(/\//g, '-')}.xlsx`;
                XLSX.writeFile(wb, fileName);

                alert('Produto exportado com sucesso!');
            } catch (error) {
                console.error('Erro ao exportar produto:', error);
                alert('Erro ao exportar produto.');
            }
        };

        function validarCamposFiscais() {
            const ncm = document.getElementById('ncm').value.trim();
            const origem = document.getElementById('origem').value;
            const tipoItem = document.getElementById('tipoItem').value;
            const cest = document.getElementById('cest').value.trim();
            const gtin = document.getElementById('gtin').value.trim();
            const gtinTributavel = document.getElementById('gtinTributavel').value.trim();
            const gtinAnterior = document.getElementById('gtinAnterior').value.trim();
            let erros = [];

            if (!ncm || !/^\d{8}$/.test(ncm)) {
                erros.push('NCM obrigatório e deve conter 8 dígitos numéricos.');
            }
            if (!origem) {
                erros.push('Origem do produto é obrigatória.');
            }
            if (!tipoItem) {
                erros.push('Tipo do item é obrigatório.');
            }
            if (cest && !/^\d{7}$/.test(cest)) {
                erros.push('CEST deve conter 7 dígitos numéricos.');
            }
            if (gtin && !/^\d{8,14}$/.test(gtin)) {
                erros.push('GTIN/EAN deve conter entre 8 e 14 dígitos numéricos.');
            }
            if (gtinTributavel && !/^\d{8,14}$/.test(gtinTributavel)) {
                erros.push('GTIN Unidade Tributável deve conter entre 8 e 14 dígitos numéricos.');
            }
            if (gtinAnterior && !/^\d{8,14}$/.test(gtinAnterior)) {
                erros.push('GTIN Produto Anterior deve conter entre 8 e 14 dígitos numéricos.');
            }

            return erros;
        }

        // Função para carregar e aplicar as configurações
        async function loadAndApplyConfigurations() {
            try {
                const validacoes = await window.getValidacoesProduto();
                const configs = await window.getConfiguracoesGerais();
                
                // Aplicar tooltips e validações dinâmicas
                applyDynamicValidations(validacoes);
                applyConfigurationTooltips(configs);
                setupRealTimeValidation();
            } catch (error) {
                console.error('Erro ao carregar configurações:', error);
                alert('Erro ao carregar configurações do sistema.');
            }
        }

        // Função para aplicar validações dinâmicas
        function applyDynamicValidations(validacoes) {
            // Configurar validação de código
            const codigoInput = document.getElementById('codigo');
            if (codigoInput) {
                codigoInput.pattern = validacoes.formatoCodigo === 'numerico' ? '\\d*' : '.*';
                codigoInput.maxLength = validacoes.tamanhoMaxCodigo || 20;
                codigoInput.title = `Código ${validacoes.formatoCodigo === 'numerico' ? 'numérico' : 'alfanumérico'} (máx. ${validacoes.tamanhoMaxCodigo} caracteres)`;
            }

            // Configurar validação de dimensões
            if (validacoes.validarDimensoes) {
                ['altura', 'largura', 'profundidade'].forEach(dim => {
                    const input = document.getElementById(dim);
                    if (input) {
                        input.min = '0';
                        input.step = '0.001';
                        input.title = 'Valor deve ser positivo';
                    }
                });
            }

            // Configurar validação de peso
            if (validacoes.validarPeso) {
                ['pesoBruto', 'pesoLiquido'].forEach(peso => {
                    const input = document.getElementById(peso);
                    if (input) {
                        input.min = '0';
                        input.step = '0.001';
                        input.title = 'Valor deve ser positivo';
                    }
                });
            }
        }

        // Função para aplicar tooltips baseados nas configurações
        function applyConfigurationTooltips(configs) {
            const tooltips = {
                'codigo': {
                    title: 'Código do produto',
                    help: 'Identificador único do produto no sistema'
                },
                'descricao': {
                    title: 'Descrição do produto',
                    help: 'Nome ou descrição detalhada do produto'
                },
                'tipo': {
                    title: 'Tipo do produto',
                    help: 'Classificação do produto (PA, SP, MP, SV)'
                },
                'unidade': {
                    title: 'Unidade de medida',
                    help: 'Unidade padrão para movimentações'
                },
                'ncm': {
                    title: 'Código NCM',
                    help: 'Código da Nomenclatura Comum do Mercosul (8 dígitos)'
                },
                'cest': {
                    title: 'Código CEST',
                    help: 'Código Especificador da Substituição Tributária (7 dígitos)'
                }
            };

            // Aplicar tooltips aos campos
            Object.entries(tooltips).forEach(([id, tooltip]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.title = `${tooltip.title}\n${tooltip.help}`;
                    addHelpIcon(element, tooltip.help);
                }
            });
        }

        // Função para adicionar ícone de ajuda
        function addHelpIcon(element, helpText) {
            const helpContainer = document.createElement('div');
            helpContainer.className = 'help-container';
            helpContainer.innerHTML = `
                <button class="help-button">?</button>
                <div class="help-tooltip">
                    ${helpText}
                </div>
            `;
            element.parentNode.appendChild(helpContainer);
        }

        // Função para configurar validação em tempo real
        function setupRealTimeValidation() {
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('input', async function() {
                    await validateField(this);
                });
                input.addEventListener('change', async function() {
                    await validateField(this);
                });
            });
        }

        // Função para validar campo individual
        async function validateField(field) {
            const validacoes = await window.getValidacoesProduto();
            let isValid = true;
            let errorMessage = '';

            switch (field.id) {
                case 'codigo':
                    if (validacoes.validarCodigo) {
                        if (validacoes.formatoCodigo === 'numerico' && !/^\d*$/.test(field.value)) {
                            isValid = false;
                            errorMessage = 'O código deve conter apenas números.';
                        }
                        if (field.value.length > validacoes.tamanhoMaxCodigo) {
                            isValid = false;
                            errorMessage = `O código não pode ter mais de ${validacoes.tamanhoMaxCodigo} caracteres.`;
                        }
                    }
                    break;

                case 'ncm':
                    if (field.value) {
                        if (!/^\d{8}$/.test(field.value)) {
                            isValid = false;
                            errorMessage = 'O NCM deve conter exatamente 8 dígitos.';
                        }
                    }
                    break;

                case 'cest':
                    if (field.value && !/^\d{7}$/.test(field.value)) {
                        isValid = false;
                        errorMessage = 'O CEST deve conter 7 dígitos.';
                    }
                    break;

                case 'gtin':
                    if (field.value && !/^\d{13}$/.test(field.value)) {
                        isValid = false;
                        errorMessage = 'O GTIN/EAN deve conter 13 dígitos.';
                    }
                    break;
            }

            // Atualizar visual do campo
            updateFieldVisual(field, isValid, errorMessage);
            return isValid;
        }

        // Função para atualizar visual do campo
        function updateFieldVisual(field, isValid, errorMessage) {
            const container = field.closest('.form-group');
            if (!container) return;

            // Remover mensagens de erro anteriores
            const existingError = container.querySelector('.error-message');
            if (existingError) existingError.remove();

            // Atualizar estilo do campo
            field.style.borderColor = isValid ? 'var(--border-color)' : 'var(--danger-color)';

            // Adicionar mensagem de erro se houver
            if (!isValid && errorMessage) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.style.color = 'var(--danger-color)';
                errorDiv.style.fontSize = '12px';
                errorDiv.style.marginTop = '4px';
                errorDiv.textContent = errorMessage;
                container.appendChild(errorDiv);
            }
        }

        // Expor funções globalmente
        window.loadProduct = loadProduct;
        window.openNewProductModal = openNewProductModal;
        window.searchProducts = searchProducts;
        window.filterProducts = filterProducts;
        window.clearFilters = clearFilters;
        window.saveProduct = saveProduct;
        window.deleteCurrentProduct = deleteCurrentProduct;
        window.exportToExcel = exportToExcel;
        window.exportProductToExcel = exportProductToExcel;
        window.switchTab = switchTab;
        window.toggleStatus = toggleStatus;
        window.testConversion = testConversion;

        // Função para carregar configurações gerais do Firestore
        window.getConfiguracoesGerais = async function() {
            try {
                const configRef = doc(db, "configuracoes", "geral");
                const configSnap = await getDoc(configRef);
                
                if (configSnap.exists()) {
                    return configSnap.data();
                } else {
                    // Retornar configurações padrão se não existirem no Firestore
                    return {
                        validarCodigo: true,
                        formatoCodigo: 'alfanumerico',
                        tamanhoMaxCodigo: 20,
                        validarDimensoes: true,
                        validarPeso: true,
                        validarNCM: true,
                        validarGTIN: true,
                        validarUnidade: true,
                        validarTipo: true,
                        validarStatus: true
                    };
                }
            } catch (error) {
                console.error('Erro ao carregar configurações:', error);
                throw error;
            }
        };

        // Função para carregar validações de produtos do Firestore
        window.getValidacoesProduto = async function() {
            try {
                const validacoesRef = doc(db, "configuracoes", "validacoes_produto");
                const validacoesSnap = await getDoc(validacoesRef);
                
                if (validacoesSnap.exists()) {
                    return validacoesSnap.data();
                } else {
                    // Retornar validações padrão se não existirem no Firestore
                    return {
                        validarCodigo: true,
                        formatoCodigo: 'alfanumerico',
                        tamanhoMaxCodigo: 20,
                        validarDimensoes: true,
                        validarPeso: true,
                        validarNCM: true,
                        validarGTIN: true,
                        validarUnidade: true,
                        validarTipo: true,
                        validarStatus: true,
                        validarCamposFiscais: true,
                        validarCamposEstoque: true,
                        validarCamposCustos: true
                    };
                }
            } catch (error) {
                console.error('Erro ao carregar validações:', error);
                // Em caso de erro, retornar validações padrão
                return {
                    validarCodigo: true,
                    formatoCodigo: 'alfanumerico',
                    tamanhoMaxCodigo: 20,
                    validarDimensoes: true,
                    validarPeso: true,
                    validarNCM: true,
                    validarGTIN: true,
                    validarUnidade: true,
                    validarTipo: true,
                    validarStatus: true,
                    validarCamposFiscais: true,
                    validarCamposEstoque: true,
                    validarCamposCustos: true
                };
            }
        };

        // Função para validar o formulário de produto
        async function validateProductForm(productData, isEdit) {
            try {
                const validacoes = await window.getValidacoesProduto();
                let isValid = true;
                let errorMessages = [];

                // Validações específicas do produto
                if (validacoes.validarCamposFiscais) {
                    const errosFiscais = validarCamposFiscais();
                    if (errosFiscais.length > 0) {
                        errorMessages.push(...errosFiscais);
                        isValid = false;
                    }
                }

                // Validação de campos de estoque
                if (validacoes.validarCamposEstoque) {
                    if (productData.estoqueMinimo < 0) {
                        errorMessages.push('O estoque mínimo não pode ser negativo.');
                        isValid = false;
                    }
                    if (productData.estoqueMaximo < 0) {
                        errorMessages.push('O estoque máximo não pode ser negativo.');
                        isValid = false;
                    }
                    if (productData.estoqueMaximo > 0 && productData.estoqueMinimo > productData.estoqueMaximo) {
                        errorMessages.push('O estoque mínimo não pode ser maior que o estoque máximo.');
                        isValid = false;
                    }
                    if (productData.pontoPedido < 0) {
                        errorMessages.push('O ponto de pedido não pode ser negativo.');
                        isValid = false;
                    }
                    if (productData.loteCompra < 0) {
                        errorMessages.push('O lote de compra não pode ser negativo.');
                        isValid = false;
                    }
                }

                // Validação de campos de custos
                if (validacoes.validarCamposCustos) {
                    if (productData.custoMedio < 0) {
                        errorMessages.push('O custo médio não pode ser negativo.');
                        isValid = false;
                    }
                    if (productData.ultimoCusto < 0) {
                        errorMessages.push('O último custo não pode ser negativo.');
                        isValid = false;
                    }
                    if (productData.precoVenda < 0) {
                        errorMessages.push('O preço de venda não pode ser negativo.');
                        isValid = false;
                    }
                    if (productData.margemLucro < 0 || productData.margemLucro > 100) {
                        errorMessages.push('A margem de lucro deve estar entre 0 e 100%.');
                        isValid = false;
                    }
                }

                // Validação de unidade secundária e fator de conversão
                if (productData.unidadeSecundaria && !productData.fatorConversao) {
                    errorMessages.push('Informe o fator de conversão quando selecionar unidade secundária.');
                    isValid = false;
                }

                // Validação de endereçamento
                if (!productData.armazemPadraoId) {
                    errorMessages.push('O armazém padrão é obrigatório.');
                    isValid = false;
                }

                // Se for edição, verificar restrições de alteração
                if (isEdit) {
                    const uso = await checkProductUsage(currentProductId);
                    if (uso.emUso) {
                        // Verificar alterações restritas
                        const produtoAtual = produtos.find(p => p.id === currentProductId);
                        if (produtoAtual) {
                            if (validacoes.validarUnidade && produtoAtual.unidade !== productData.unidade) {
                                errorMessages.push('Não é possível alterar a unidade de medida pois o produto está em uso.');
                                isValid = false;
                            }
                            if (validacoes.validarTipo && produtoAtual.tipo !== productData.tipo) {
                                errorMessages.push('Não é possível alterar o tipo do produto pois ele está em uso.');
                                isValid = false;
                            }
                            if (validacoes.validarStatus && produtoAtual.status === 'ativo' && productData.status === 'inativo') {
                                errorMessages.push('Não é possível inativar o produto pois ele está em uso.');
                                isValid = false;
                            }
                        }
                    }
                }

                // Mostrar mensagens de erro se houver
                if (!isValid) {
                    alert(errorMessages.join('\n'));
                }

                return isValid;
            } catch (error) {
                console.error('Erro ao validar formulário de produto:', error);
                alert('Erro ao validar formulário. Por favor, tente novamente.');
                return false;
            }
        }

        function filterGrupoOptions() {
            const input = document.getElementById('searchGrupo').value.toLowerCase();
            const select = document.getElementById('grupo');
            const selectedValue = select.value;
            select.innerHTML = '<option value="">Selecione um grupo</option>';
            grupos
                .slice()
                .sort((a, b) => (a.nomeGrupo || '').localeCompare(b.nomeGrupo || ''))
                .forEach(g => {
                    const texto = `${g.codigoGrupo} - ${g.nomeGrupo}`.toLowerCase();
                    if (texto.includes(input)) {
                        const option = document.createElement('option');
                        option.value = g.codigoGrupo;
                        option.textContent = `${g.codigoGrupo} - ${g.nomeGrupo}`;
                        if (g.codigoGrupo === selectedValue) option.selected = true;
                        select.appendChild(option);
                    }
                });
        }

        function filterFamiliaOptions() {
            const input = document.getElementById('searchFamilia').value.toLowerCase();
            const select = document.getElementById('familia');
            const selectedValue = select.value;
            select.innerHTML = '<option value="">Selecione uma família</option>';
            familias
                .slice()
                .sort((a, b) => (a.nomeFamilia || '').localeCompare(b.nomeFamilia || ''))
                .forEach(f => {
                    const texto = `${f.codigoFamilia} - ${f.nomeFamilia}`.toLowerCase();
                    if (texto.includes(input)) {
                        const option = document.createElement('option');
                        option.value = f.codigoFamilia;
                        option.textContent = `${f.codigoFamilia} - ${f.nomeFamilia}`;
                        if (f.codigoFamilia === selectedValue) option.selected = true;
                        select.appendChild(option);
                    }
                });
        }

        window.filterGrupoOptions = filterGrupoOptions;
        window.filterFamiliaOptions = filterFamiliaOptions;
    </script>
</body>
</html>
