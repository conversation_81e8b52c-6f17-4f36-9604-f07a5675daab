# Ferramenta de Auditoria de Compras

## Visão Geral

A ferramenta de auditoria foi desenvolvida para identificar e corrigir problemas de numeração redundante e inconsistências no processo de compras. Esta ferramenta analisa **Solicitações de Compra**, **Cotações** e **Pedidos de Compra** para detectar:

- ✅ **Números Duplicados**: Documentos com a mesma numeração
- ⚠️ **Problemas de Sequência**: Lacunas na numeração sequencial
- ℹ️ **Status Inconsistente**: Documentos com status inadequados por muito tempo
- 🔢 **Sem Numeração**: Documentos que não possuem número atribuído

## Como Usar

### 1. Acessar a Ferramenta
- Abra o arquivo `ferramenta_alterar_status.html`
- Clique na aba **"Auditoria"** (primeira aba)

### 2. Executar Auditoria
- Clique no botão **"Executar Auditoria"** para analisar todos os documentos
- O sistema irá verificar automaticamente todos os problemas
- Os resultados aparecerão na tabela abaixo

### 3. Filtrar Problemas
Use os filtros para focar em tipos específicos de problemas:
- **Tipo de Problema**: Duplicados, Sequência, Status Inconsistente, Sem Numeração
- **Tipo de Documento**: Solicitações, Cotações, Pedidos
- Clique em **"Filtrar Problemas"** após selecionar os filtros

### 4. Corrigir Problemas

#### Correção Individual
- Clique no botão **🔧** (Corrigir) na linha do problema
- Confirme a correção com o número sugerido

#### Correção Automática
- Clique em **"Corrigir Automaticamente"** para corrigir todos os problemas de numeração e duplicados
- ⚠️ **ATENÇÃO**: Esta ação não pode ser desfeita!

#### Correção em Lote
- Selecione os problemas desejados usando os checkboxes
- Clique em **"Corrigir Selecionados"**

#### Renumeração Sequencial
- Selecione o tipo de documento no filtro
- Clique em **"Renumerar Sequencialmente"**
- ⚠️ **ATENÇÃO**: Renumera TODOS os documentos do tipo selecionado em ordem cronológica

#### Exclusão de Duplicados
- Selecione documentos duplicados
- Clique em **"Excluir Duplicados"**
- ⚠️ **ATENÇÃO**: Marca os documentos como EXCLUÍDA permanentemente

## Tipos de Problemas Detectados

### 1. Números Duplicados 🔴
**Problema**: Dois ou mais documentos com o mesmo número
**Solução**: Atribui novos números sequenciais aos duplicados
**Exemplo**: SC20240001 aparece em 3 solicitações diferentes

### 2. Problemas de Sequência ⚠️
**Problema**: Lacunas na numeração sequencial
**Solução**: Sugere números para preencher as lacunas
**Exemplo**: SC20240001, SC20240003 (falta SC20240002)

### 3. Status Inconsistente ℹ️
**Problema**: Documentos com status inadequados por muito tempo
**Detecção**:
- Solicitações aprovadas há mais de 30 dias sem cotação
- Cotações abertas há mais de 15 dias
**Solução**: Revisão manual do status

### 4. Sem Numeração 🔢
**Problema**: Documentos sem número atribuído
**Solução**: Atribui automaticamente o próximo número disponível

## Estatísticas da Auditoria

O painel superior mostra:
- **Total de Problemas**: Quantidade total de inconsistências encontradas
- **Duplicados**: Número de documentos com numeração duplicada
- **Problemas de Sequência**: Lacunas na numeração
- **Status Inconsistente**: Documentos com status problemático
- **Sem Numeração**: Documentos sem número

## Segurança e Backup

### Antes de Usar a Ferramenta:
1. **Faça backup** do banco de dados Firebase
2. **Teste** em ambiente de desenvolvimento primeiro
3. **Documente** as alterações realizadas

### Histórico de Alterações:
Todas as correções são registradas com:
- Data e hora da alteração
- Usuário responsável: "Auditoria - [Tipo de Correção]"
- Número anterior (quando aplicável)

## Casos de Uso Comuns

### Cenário 1: Numeração Duplicada
**Situação**: Várias solicitações com número SC20240015
**Solução**: 
1. Execute a auditoria
2. Filtre por "Duplicados"
3. Use "Corrigir Automaticamente" ou corrija individualmente

### Cenário 2: Sequência Quebrada
**Situação**: Numeração pulou números (SC001, SC003, SC005...)
**Solução**:
1. Execute a auditoria
2. Filtre por "Problemas de Sequência"
3. Use "Renumerar Sequencialmente" para reorganizar tudo

### Cenário 3: Limpeza Geral
**Situação**: Sistema com muitos problemas acumulados
**Solução**:
1. Execute auditoria completa
2. Use "Renumerar Sequencialmente" por tipo de documento
3. Verifique e corrija status inconsistentes manualmente

## Troubleshooting

### Problema: Auditoria não encontra problemas
**Solução**: Verifique se os dados foram carregados corretamente

### Problema: Erro ao corrigir
**Solução**: Verifique permissões do Firebase e conexão

### Problema: Números sugeridos incorretos
**Solução**: Execute "Renumerar Sequencialmente" para reorganizar completamente

## Manutenção Preventiva

### Recomendações:
- Execute auditoria **mensalmente**
- Monitore a criação de novos documentos
- Mantenha padrões de numeração consistentes
- Treine usuários sobre a importância da numeração correta

### Indicadores de Alerta:
- Mais de 5 duplicados por mês
- Lacunas frequentes na sequência
- Documentos sem numeração
- Status inconsistentes acumulando

---

**Desenvolvido para otimizar o processo de compras e manter a integridade dos dados.**
