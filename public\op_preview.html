<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Preview de Ordem de Produção</title>
  <style>
    @page {
      size: A4 portrait;
      margin: 15mm;
    }

    @media print {
      body {
        margin: 0;
        padding: 0;
      }
      .no-print {
        display: none;
      }
      .order-page {
        page-break-after: always;
        padding: 0 !important;
        margin: 0 !important;
        box-shadow: none !important;
      }
      .order-page:last-child {
        page-break-after: avoid;
      }
      .report-info {
        display: none;
      }
      .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
      }
      .header {
        margin: 0 0 10mm 0 !important;
      }
      .logo {
        width: 120px !important;
      }
      .order-info {
        margin-top: 0 !important;
      }
      .order-info h2 {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
      }
    }

    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 210mm;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 2px solid #333;
      padding-bottom: 10px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: bold;
    }

    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }

    button:hover {
      background-color: #45a049;
    }

    .back-button {
      background-color: #6c757d;
    }

    .order-page {
      width: 210mm;
      min-height: 297mm;
      padding: 20mm;
      margin: 0 auto;
      background-color: white;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
      border-bottom: 2px solid #000;
      padding-bottom: 10px;
    }

    .logo {
      width: 120px;
      height: auto;
    }

    .order-title {
      text-align: center;
      flex-grow: 1;
      margin: 0 20px;
    }

    .order-title h1 {
      margin: 0;
      font-size: 24px;
    }

    .order-title h2 {
      margin: 5px 0;
      font-size: 18px;
    }

    .order-info {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      margin-bottom: 20px;
      border: 1px solid #ccc;
      padding: 10px;
    }

    .info-item {
      border: 1px solid #ddd;
      padding: 5px;
    }

    .info-item strong {
      display: block;
      font-size: 12px;
      color: #666;
    }

    .info-item span {
      display: block;
      font-size: 14px;
      margin-top: 2px;
    }

    .section {
      margin-bottom: 20px;
    }

    .section-title {
      background-color: #f0f0f0;
      padding: 5px 10px;
      font-weight: bold;
      border: 1px solid #ccc;
      margin-top: 20px;
    }

    .materials-table,
    .operations-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .materials-table th,
    .materials-table td,
    .operations-table th,
    .operations-table td {
      padding: 8px;
      border: 1px solid #ddd;
      text-align: left;
    }

    .materials-table th,
    .operations-table th {
      background-color: #f8f9fa;
      font-weight: bold;
    }

    .signatures {
      margin-top: 40px;
      display: flex;
      justify-content: space-between;
    }

    .signature {
      flex: 1;
      margin: 0 20px;
      text-align: center;
    }

    .signature-line {
      width: 100%;
      border-top: 1px solid #000;
      margin-top: 40px;
      padding-top: 5px;
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }

    .status-pendente { background-color: #ffc107; color: #000; }
    .status-em-producao { background-color: #17a2b8; color: #fff; }
    .status-concluida { background-color: #28a745; color: #fff; }
    .status-cancelada { background-color: #dc3545; color: #fff; }
  </style>
</head>
<body>
  <div class="container">
    <div class="no-print">
      <div class="form-group">
        <div class="form-row">
          <div class="form-col">
            <label>Produto:</label>
            <select id="productSelect" required>
              <option value="">Selecione o produto...</option>
            </select>
          </div>
          <div class="form-col">
            <label>Quantidade:</label>
            <input type="number" id="quantity" min="0.001" step="0.001" required>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label>Data de Entrega:</label>
            <input type="date" id="dueDate" required>
          </div>
          <div class="form-col">
            <label>Prioridade:</label>
            <select id="priority" required>
              <option value="normal">Normal</option>
              <option value="alta">Alta</option>
              <option value="urgente">Urgente</option>
            </select>
          </div>
        </div>

        <div style="margin-top: 20px;">
          <button onclick="generatePreview()">Gerar Preview</button>
          <button onclick="window.print()" id="printButton" style="display: none;">Imprimir</button>
          <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
        </div>
      </div>
    </div>

    <div id="previewContent">
      <!-- O conteúdo do preview será gerado aqui -->
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let operacoes = [];
    let recursos = [];

    window.onload = async function() {
      await loadData();
      updateProductSelect();
    };

    async function loadData() {
      try {
        const [produtosSnap, estruturasSnap, operacoesSnap, recursosSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "operacoes")),
          getDocs(collection(db, "recursos"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados. Por favor, recarregue a página.");
      }
    }

    function updateProductSelect() {
      const select = document.getElementById('productSelect');
      select.innerHTML = '<option value="">Selecione o produto...</option>';

      produtos
        .filter(p => p.tipo === 'PA' || p.tipo === 'SP')
        .forEach(produto => {
          select.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
        });
    }

    function generateOrderNumber() {
      const date = new Date();
      const year = date.getFullYear().toString().substr(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
      return `OP${year}${month}${random}`;
    }

    async function generateBOMStructure(produtoId, quantidade, level = 0, path = []) {
      const produto = produtos.find(p => p.id === produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!produto) return null;
      if (path.includes(produtoId)) return null;

      let materialsRows = [];

      // Adiciona o produto atual com sua quantidade total
      materialsRows.push({
        level,
        codigo: produto.codigo,
        descricao: produto.descricao,
        tipo: produto.tipo,
        quantidade: quantidade.toFixed(3),
        unidade: produto.unidade
      });

      // Se tiver estrutura e componentes, processa cada um
      if (estrutura && estrutura.componentes) {
        for (const comp of estrutura.componentes) {
          // Calcula a quantidade total do componente multiplicando a quantidade do nível superior
          // pela quantidade necessária do componente na estrutura
          const quantidadeTotal = quantidade * comp.quantidade;

          const subStructure = await generateBOMStructure(
            comp.componentId, 
            quantidadeTotal,
            level + 1,
            [...path, produtoId]
          );

          if (subStructure) {
            materialsRows = materialsRows.concat(subStructure);
          }
        }
      }

      return materialsRows;
    }

    window.generatePreview = async function() {
      const produtoId = document.getElementById('productSelect').value;
      const quantidade = parseFloat(document.getElementById('quantity').value);
      const dataEntrega = document.getElementById('dueDate').value;
      const prioridade = document.getElementById('priority').value;

      if (!produtoId || !quantidade || !dataEntrega) {
        alert('Por favor, preencha todos os campos.');
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!estrutura) {
        alert('Este produto não possui estrutura cadastrada.');
        return;
      }

      const orderNumber = generateOrderNumber();
      const previewContent = document.getElementById('previewContent');

      const bomStructure = await generateBOMStructure(produtoId, quantidade);

      const orderPage = document.createElement('div');
      orderPage.className = 'order-page';

      orderPage.innerHTML = `
        <div class="order-header">
          <img src="https://www.naliteck.com.br/img/logo.png" alt="Logo" class="logo">
          <div class="order-title">
            <h1>ORDEM DE PRODUÇÃO</h1>
            <h2>${orderNumber}</h2>
          </div>
          <div style="text-align: right;">
            <strong>Data: </strong>${new Date().toLocaleDateString()}<br>
            <strong>Hora: </strong>${new Date().toLocaleTimeString()}
          </div>
        </div>

        <div class="order-info">
          <div class="info-item">
            <strong>Produto:</strong>
            <span>${produto.codigo} - ${produto.descricao}</span>
          </div>
          <div class="info-item">
            <strong>Tipo:</strong>
            <span>${produto.tipo}</span>
          </div>
          <div class="info-item">
            <strong>Quantidade:</strong>
            <span>${quantidade} ${produto.unidade}</span>
          </div>
          <div class="info-item">
            <strong>Prioridade:</strong>
            <span>${prioridade}</span>
          </div>
          <div class="info-item">
            <strong>Data de Entrega:</strong>
            <span>${new Date(dataEntrega).toLocaleDateString()}</span>
          </div>
          <div class="info-item">
            <strong>Status:</strong>
            <span class="status-badge status-pendente">Pendente</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">LISTA DE MATERIAIS</div>
          <table class="materials-table">
            <thead>
              <tr>
                <th>Nível</th>
                <th>Código</th>
                <th>Descrição</th>
                <th>Tipo</th>
                <th>Quantidade</th>
                <th>Unidade</th>
              </tr>
            </thead>
            <tbody>
              ${bomStructure.map(item => `
                <tr>
                  <td>${'→'.repeat(item.level)}</td>
                  <td>${item.codigo}</td>
                  <td>${item.descricao}</td>
                  <td>${item.tipo}</td>
                  <td>${item.quantidade}</td>
                  <td>${item.unidade}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="section">
          <div class="section-title">ROTEIRO DE PRODUÇÃO</div>
          <table class="operations-table">
            <thead>
              <tr>
                <th style="width: 30px;">Seq.</th>
                <th>Operação</th>
                <th style="width: 80px;">Recurso</th>
                <th style="width: 30px;">Min.</th>
                <th>Descrição</th>
                <th style="width: 60px;">Status</th>
              </tr>
            </thead>
            <tbody>
              ${estrutura.operacoes.sort((a, b) => a.sequencia - b.sequencia).map(op => {
                const operacao = operacoes.find(o => o.id === op.operacaoId);
                const recurso = recursos.find(r => r.id === op.recursoId);
                return `
                  <tr>
                    <td>${op.sequencia}</td>
                    <td>${operacao.operacao}</td>
                    <td>${recurso.codigo}</td>
                    <td>${op.tempo}</td>
                    <td>${op.descricao || ''}</td>
                    <td></td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>
        </div>

        <div class="signatures">
          <div class="signature">
            <div class="signature-line">Produção</div>
          </div>
          <div class="signature">
            <div class="signature-line">Qualidade</div>
          </div>
          <div class="signature">
            <div class="signature-line">Supervisor</div>
          </div>
        </div>
      `;

      previewContent.innerHTML = '';
      previewContent.appendChild(orderPage);
      document.getElementById('printButton').style.display = 'inline-block';
    };
  </script>
<button onclick="exportToExcel()">Exportar para Excel</button>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.4/xlsx.full.min.js"></script>
<script>
function exportToExcel() {
    let wb = XLSX.utils.book_new();

    // Exportar lista de materiais
    let materialsTable = document.querySelector(".materials-table");
    if (materialsTable) {
        let wsMaterials = XLSX.utils.table_to_sheet(materialsTable);
        XLSX.utils.book_append_sheet(wb, wsMaterials, "Lista de Materiais");
    }

    // Exportar roteiro de produção
    let operationsTable = document.querySelector(".operations-table");
    if (operationsTable) {
        let wsOperations = XLSX.utils.table_to_sheet(operationsTable);
        XLSX.utils.book_append_sheet(wb, wsOperations, "Roteiro de Produção");
    }

    // Baixar o arquivo
    XLSX.writeFile(wb, "Ordem_de_Producao.xlsx");
}
</script>

</body>
</html>

</body>
</html>