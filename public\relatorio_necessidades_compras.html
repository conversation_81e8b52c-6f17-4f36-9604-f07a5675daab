<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório de Necessidades de Compras</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --secondary-color: #6c757d;
      --secondary-hover: #5a6268;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 30px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    .filters {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .form-col {
      flex: 1;
      min-width: 200px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 500;
    }

    select, button {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
      color: var(--text-color);
      background-color: #fff;
      transition: border-color 0.2s, background-color 0.2s;
    }

    select:focus {
      border-color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    button {
      cursor: pointer;
      font-weight: 500;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: #fff;
      border: none;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: #fff;
      border: none;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-secondary {
      background-color: var(--secondary-color);
      color: #fff;
      border: none;
    }

    .btn-secondary:hover {
      background-color: var(--secondary-hover);
    }

    .btn-info {
      background-color: #17a2b8;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .btn-info:hover {
      background-color: #138496;
      transform: translateY(-1px);
    }

    .btn-warning {
      background-color: #ffc107;
      color: #212529;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-warning:hover {
      background-color: #e0a800;
      transform: translateY(-1px);
    }

    .btn-back {
      background-color: var(--secondary-color);
      color: #fff;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .btn-back:hover {
      background-color: var(--secondary-hover);
    }

    .btn-back i {
      font-size: 16px;
    }

    .header-buttons {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      font-size: 14px;
      background-color: #fff;
      table-layout: fixed;
    }

    .data-table th {
      background-color: #f8f9fa;
      padding: 12px 8px;
      text-align: left;
      font-weight: 600;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);
      white-space: nowrap;
    }

    .data-table td {
      padding: 10px 8px;
      border: 1px solid var(--border-color);
      vertical-align: middle;
    }

    /* Definir larguras específicas para cada coluna (10 colunas agora) */
    .data-table th:nth-child(1), /* Checkbox */
    .data-table td:nth-child(1) {
      width: 30px;
    }

    .data-table th:nth-child(2), /* Código */
    .data-table td:nth-child(2) {
      width: 10%;
    }

    .data-table th:nth-child(3), /* Descrição */
    .data-table td:nth-child(3) {
      width: 25%;
    }

    .data-table th:nth-child(4),
    .data-table td:nth-child(4), /* Necessidade */
    .data-table th:nth-child(5),
    .data-table td:nth-child(5), /* Saldo Atual */
    .data-table th:nth-child(6),
    .data-table td:nth-child(6) { /* Déficit */
      width: 8%; /* Ajustado para serem menores */
      text-align: right;
    }

    .data-table th:nth-child(7),
    .data-table td:nth-child(7) { /* Fornecedor Principal */
      width: 15%; /* Espaço para o select */
    }

     .data-table th:nth-child(8),
    .data-table td:nth-child(8) { /* Status Solicitação */
      width: 10%; /* Espaço para o status */
      text-align: center;
    }

     .data-table th:nth-child(9),
    .data-table td:nth-child(9) { /* Data Programada Entrega */
      width: 10%; /* Espaço para a data */
      text-align: center;
    }

    .data-table th:nth-child(10),
    .data-table td:nth-child(10) { /* Criticidade */
      width: 8%; /* Ajustado */
      text-align: center;
    }

    .data-table th:nth-child(11),
    .data-table td:nth-child(11) { /* Reservado */
      width: 8%; /* Ajustado */
      text-align: right;
    }

    .data-table th:nth-child(12),
    .data-table td:nth-child(12) { /* Disponível */
      width: 8%; /* Ajustado */
      text-align: right;
    }

    .data-table tbody tr:hover {
      background-color: #f5f5f5;
    }

    .criticidade-alta {
      background-color: #fff3f3;
    }

    .criticidade-media {
      background-color: #fff9e6;
    }

    .criticidade-baixa {
      background-color: rgba(0, 128, 0, 0.1);
    }

    .section {
      margin-bottom: 30px;
      padding: 15px;
      background-color: #fff;
      border: 1px solid var(--border-color);
      border-radius: 8px;
    }

    .section-title {
      font-size: 18px;
      color: var(--primary-color);
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid var(--primary-color);
    }

    .subsection {
      margin: 15px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }

    .subsection h3 {
      font-size: 16px;
      color: var(--text-secondary);
      margin-bottom: 10px;
    }

    .table-container {
      overflow-x: auto;
      margin-top: 10px;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .summary {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 15px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .table-container {
      overflow-x: auto;
    }

    #sortDirection {
      width: 100%;
      padding: 8px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    #sortDirection:hover {
      background-color: var(--secondary-hover);
    }

    @media (max-width: 768px) {
      .filters {
        flex-direction: column;
      }

      .form-col {
        min-width: 100%;
      }

      .data-table th, .data-table td {
        font-size: 12px;
        padding: 8px;
      }
    }

    @media print {
      body {
        background: white;
        font-size: 12pt;
      }

      .container {
        width: 100%;
        margin: 0;
        padding: 10px;
        box-shadow: none;
      }

      .no-print {
        display: none;
      }

      .header {
        display: none;
      }

      /* Estilos do cabeçalho de impressão */
      .print-header {
        display: grid;
        grid-template-columns: 150px 1fr 150px;
        align-items: center;
        gap: 20px;
        padding: 10px 0;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
      }

      .print-header .logo {
        height: 50px;
        width: auto;
      }

      .print-header .title {
        text-align: center;
        font-size: 16px;
        font-weight: bold;
      }

      .print-header .info {
        text-align: right;
        font-size: 12px;
        line-height: 1.4;
      }

      /* Configurações de página */
      @page {
        size: A4;
        margin: 20mm 15mm;
      }

      /* Estilos da tabela */
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        page-break-inside: auto;
      }

      .data-table th {
        background-color: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .data-table th,
      .data-table td {
        border: 1px solid #000;
        padding: 8px;
        font-size: 10pt;
      }

      /* Controle de quebra de página */
      .familia-section {
        page-break-inside: avoid;
      }

      /* Cores de criticidade na impressão */
      .criticidade-alta {
        background-color: #ffebee !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .criticidade-media {
        background-color: #fff3e0 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .criticidade-baixa {
        background-color: #f1f8e9 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      min-width: 80px;
      text-align: center;
    }

    .status-alta {
      background-color: #dc3545;
      color: white;
    }

    .status-media {
      background-color: #ffc107;
      color: black;
    }

    .status-baixa {
      background-color: #28a745;
      color: white;
    }

    .familia-title {
      background-color: #f8f9fa;
      padding: 12px;
      margin: 20px 0 10px 0;
      border: 1px solid var(--border-color);
      font-weight: 600;
      color: var(--text-secondary);
    }

    /* Hide report header by default */
    .report-header {
      display: none;
    }

    /* Estilos específicos para impressão */
    .no-screen {
      display: none;
    }

    @media print {
      .no-screen {
        display: block;
      }

      body {
        background: white;
        font-size: 12pt;
      }

      .container {
        width: 100%;
        margin: 0;
        padding: 10px;
        box-shadow: none;
      }

      .no-print {
        display: none;
      }

      .header {
        display: none;
      }

      .print-header {
        display: grid;
        grid-template-columns: 150px 1fr 150px;
        align-items: center;
        gap: 20px;
        margin-bottom: 20px;
        padding: 10px 0;
        border-bottom: 2px solid var(--border-color);
      }

      .print-header .logo {
        height: 50px;
        width: auto;
      }

      .print-header .title {
        text-align: center;
        font-size: 16px;
        font-weight: bold;
      }

      .print-header .info {
        text-align: right;
        font-size: 12px;
        line-height: 1.4;
      }

      /* Ajustes para tabelas na impressão */
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        page-break-inside: auto;
      }

      .data-table th {
        background-color: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .data-table th,
      .data-table td {
        border: 1px solid #000;
        padding: 8px;
        font-size: 10pt;
      }

      .familia-section {
        page-break-inside: avoid;
      }

      @page {
        margin: 15mm;
        size: A4;
      }
    }

    .btn-edit {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .btn-edit:hover {
      background-color: var(--primary-hover);
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }

    .modal-content {
      position: relative;
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      width: 80%;
      max-width: 600px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .modal-title {
      font-size: 18px;
      color: var(--primary-color);
      margin: 0;
    }

    .close-modal {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .modal-body {
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding-top: 15px;
      border-top: 1px solid var(--border-color);
    }

    /* Add notification styles */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 25px;
      border-radius: 4px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }

    .notification.success {
      background-color: #4caf50;
    }

    .notification.error {
      background-color: #f44336;
    }

    .notification.info {
      background-color: #2196f3;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .multi-select-container {
      position: relative;
    }

    .search-wrapper {
      position: relative;
      display: flex;
      align-items: center;
    }

    .search-input-enhanced {
      width: 100%;
      padding: 10px 35px 10px 12px;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      font-size: 14px;
      color: var(--text-color);
      background-color: #fff;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .search-input-enhanced:focus {
      border-color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-1px);
    }

    .clear-search-btn {
      position: absolute;
      right: 8px;
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      font-size: 18px;
      padding: 4px;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .clear-search-btn:hover {
      background-color: #f0f0f0;
      color: var(--primary-color);
    }

    .dropdown-list-enhanced {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 2px solid var(--primary-color);
      border-radius: 8px;
      max-height: 250px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      margin-top: 4px;
    }

    .dropdown-item {
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.2s ease;
      font-size: 14px;
    }

    .dropdown-item:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateX(4px);
    }

    .dropdown-item:last-child {
      border-bottom: none;
    }

    .selected-items-enhanced {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 8px 0;
      min-height: 30px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border: 1px dashed var(--border-color);
    }

    .selected-item {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 13px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 2px 6px rgba(8, 84, 160, 0.3);
      transition: all 0.2s ease;
    }

    .selected-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(8, 84, 160, 0.4);
    }

    .selected-item .remove-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 2px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .selected-item .remove-btn:hover {
      background-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .produtos-count {
      text-align: center;
      padding: 6px;
      font-size: 12px;
      color: var(--text-secondary);
      background-color: #f0f7ff;
      border-radius: 4px;
      margin-top: 4px;
      border: 1px solid #e3f2fd;
    }

    .select-enhanced {
      background: linear-gradient(135deg, #fff, #f8f9fa);
      border: 2px solid var(--border-color);
      border-radius: 8px;
      padding: 10px 12px;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .select-enhanced:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-1px);
    }

    .action-buttons-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .btn-filter-selected {
      background: linear-gradient(135deg, var(--success-color), var(--success-hover));
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      font-size: 13px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(16, 126, 62, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .btn-filter-selected:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(16, 126, 62, 0.4);
    }

    .btn-filter-selected:disabled {
      background: linear-gradient(135deg, #ccc, #bbb);
      cursor: not-allowed;
      box-shadow: none;
      transform: none;
    }

    .btn-sort {
      background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover));
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      font-size: 13px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .btn-sort:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Relatório de Necessidades de Compras Geral x Ordens de Produção</h1>
      <div class="header-buttons">
        <button class="btn-back" onclick="window.location.href='index.html'">
          <i>←</i> Voltar
        </button>
        <button class="btn-secondary" onclick="abrirModalConfigRelatorio()">Imprimir</button>
        <button class="btn-secondary" onclick="exportarParaExcel()">Exportar para Excel</button>
      </div>
    </div>

    <div class="print-header no-screen">
      <div class="logo-container">
        <img id="companyLogo" src="" alt="Logo" class="logo">
      </div>
      <div class="title">
        Relatório de Necessidades de Compras<br>
        Geral x Ordens de Produção
      </div>
      <div class="info">
        Data: <span id="printDate"></span><br>
        Hora: <span id="printTime"></span>
      </div>
    </div>

    <div class="filters no-print">
      <div class="form-col">
        <label>Grupo</label>
        <select id="filterGrupo" onchange="filtrarDados()" title="Selecione um grupo para filtrar">
          <option value="">Todos</option>
        </select>
      </div>
      <div class="form-col">
        <label>Família</label>
        <select id="filterFamilia" onchange="filtrarDados()" title="Selecione uma família para filtrar">
          <option value="">Todas</option>
        </select>
      </div>
      <div class="form-col">
        <label>Criticidade</label>
        <select id="filterCriticidade" onchange="filtrarDados()" title="Selecione um nível de criticidade para filtrar">
          <option value="">Todas</option>
          <option value="alta">Alta</option>
          <option value="media">Média</option>
          <option value="baixa">Baixa</option>
        </select>
      </div>
      <div class="form-col">
        <label>Ordenar por</label>
        <select id="sortField" onchange="filtrarDados()" title="Selecione o campo para ordenar os resultados">
          <option value="codigo">Código</option>
          <option value="tipo">Tipo</option>
          <option value="grupo">Grupo</option>
          <option value="familia">Família</option>
        </select>
      </div>
      <div class="form-col">
        <label for="filterStatusSolicitacao">Status da Solicitação</label>
        <select id="filterStatusSolicitacao" onchange="filtrarDados()" title="Filtrar por status da solicitação">
          <option value="">Todas</option>
          <option value="sem" selected>Sem Solicitação</option>
          <option value="solicitada">Solicitada</option>
          <option value="recebida">Recebida</option>
        </select>
      </div>
      <div class="form-col">
        <label for="filterProdutoPai">🔍 Produtos Pai</label>
        <div class="multi-select-container">
          <div class="search-wrapper">
            <input type="text" id="searchProdutoPai" placeholder="🔍 Digite para buscar produtos pai..." 
                   onkeyup="filtrarProdutosPai(this.value)" class="search-input-enhanced">
            <button type="button" class="clear-search-btn" onclick="limparBuscaProdutosPai()" title="Limpar busca">
              ×
            </button>
          </div>
          <div id="produtosPaiSelecionados" class="selected-items-enhanced"></div>
          <div id="listaProdutosPai" class="dropdown-list-enhanced" style="display: none;">
            <!-- Lista será preenchida dinamicamente -->
          </div>
          <div class="produtos-count">
            <span id="produtosCount">0 produtos selecionados</span>
          </div>
        </div>
      </div>
      <div class="form-col">
        <label for="filterOrdemProducao">📋 Ordem de Produção</label>
        <select id="filterOrdemProducao" onchange="filtrarDados()" title="Filtrar por ordem de produção" class="select-enhanced">
          <option value="">Todas as Ordens</option>
        </select>
      </div>
      <div class="form-col">
        <label>📊 Ações de Filtro</label>
        <div class="action-buttons-group">
          <button id="btnFiltrarSelecionados" class="btn-filter-selected" onclick="filtrarApenasSeleccionados()" 
                  title="Mostrar apenas produtos dos itens selecionados" disabled>
            🎯 Filtrar Selecionados
          </button>
          <button id="btnLimparFiltros" class="btn-sort" onclick="limparTodosFiltros()" 
                  title="Limpar todos os filtros e mostrar todas as necessidades">
            🔄 Limpar Filtros
          </button>
          <button id="sortDirection" class="btn-sort" onclick="toggleSortDirection()" title="Alterar direção da ordenação">
            📈 Ascendente ▲
          </button>
        </div>
      </div>
    </div>

    <div id="loading" style="display: none; text-align: center;">
      <span>Carregando...</span>
    </div>

    <div id="reportContent"></div>

    <div class="no-print" style="margin-top: 20px; text-align: right;">
      <button onclick="gerarRelatorioEmpenhos()" class="btn-info" style="margin-right: 10px;" title="Verificar saúde dos empenhos">
        <i class="fas fa-chart-line"></i> Relatório Empenhos
      </button>
      <button onclick="limparEmpenhosOrfaos()" class="btn-warning" style="margin-right: 10px;" title="Limpar empenhos órfãos">
        <i class="fas fa-broom"></i> Limpar Empenhos Órfãos
      </button>
      <button onclick="limparSelecoes()" class="btn-secondary" style="margin-right: 10px;">
        Limpar Seleções
      </button>
      <button onclick="gerarSolicitacoes()" class="btn-success">
        Gerar Solicitações
      </button>
    </div>

    <div class="report-footer">
      Sistema MRP - Relatório de Necessidades de Compras
    </div>
  </div>

  <!-- Modal de configuração do relatório -->
  <div id="modal-config-relatorio" class="modal-config-relatorio no-print" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.3); z-index:1000; align-items:center; justify-content:center;">
    <div style="background:#fff; padding:24px 20px 16px 20px; border-radius:8px; min-width:320px; max-width:90vw; box-shadow:0 2px 16px rgba(0,0,0,0.2); position:relative;">
      <h3 style="margin-bottom: 10px;">Configurar Relatório</h3>
      <div style="margin-bottom: 10px;">
        <label><input type="checkbox" class="col-toggle" value="codigo" checked> Código</label>
        <label><input type="checkbox" class="col-toggle" value="descricao" checked> Descrição</label>
        <label><input type="checkbox" class="col-toggle" value="necessidade" checked> Necessidade</label>
        <label><input type="checkbox" class="col-toggle" value="saldo" checked> Saldo Atual</label>
        <label><input type="checkbox" class="col-toggle" value="deficit" checked> Déficit</label>
        <label><input type="checkbox" class="col-toggle" value="status" checked> Status Solicitação</label>
        <label><input type="checkbox" class="col-toggle" value="dataEntrega" checked> Data Programada Entrega</label>
        <label><input type="checkbox" class="col-toggle" value="criticidade" checked> Criticidade</label>
      </div>
      <div style="margin-bottom: 10px;">
        <label>Famílias a exibir:</label>
        <select id="familias-relatorio" multiple style="min-width: 200px; min-height: 60px;"></select>
      </div>
      <div style="text-align:right;">
        <button onclick="fecharModalConfigRelatorio()" class="btn-secondary" style="margin-right:10px;">Cancelar</button>
        <button onclick="gerarRelatorioEImprimir()" class="btn-primary">Gerar e Imprimir</button>
      </div>
    </div>
  </div>

  <!-- Barra de progresso para carregamento das necessidades -->
  <div id="progressBarContainer" style="display:none; width:100%; margin: 20px 0;">
    <div style="background:#e0e0e0; border-radius:8px; overflow:hidden; height:24px;">
      <div id="progressBar" style="background:#0854a0; width:0%; height:24px; color:white; text-align:center; line-height:24px; font-weight:bold; transition:width 0.2s;"></div>
    </div>
  </div>

  <div id="editModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Editar Necessidade</h3>
        <button class="close-modal" onclick="fecharModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="editCodigo">Código</label>
          <input type="text" id="editCodigo" readonly>
        </div>
        <div class="form-group">
          <label for="editQuantidade">Quantidade</label>
          <input type="number" id="editQuantidade" step="0.01" min="0">
        </div>
        <div class="form-group">
          <label for="editGrupo">Grupo</label>
          <select id="editGrupo">
            <!-- Será preenchido dinamicamente -->
          </select>
        </div>
        <div class="form-group">
          <label for="editFamilia">Família</label>
          <select id="editFamilia">
            <!-- Será preenchido dinamicamente -->
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" onclick="fecharModal()">Cancelar</button>
        <button class="btn-primary" onclick="window.salvarEdicao()">Salvar</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import {
      collection,
      getDocs,
      addDoc,
      doc,
      getDoc,
      setDoc,
      query,
      orderBy,
      limit,
      Timestamp,
      updateDoc
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let grupos = [];
    let familias = [];
    let produtos = [];
    let ordensProducao = [];
    let estoques = [];
    let solicitacoes = [];
    let necessidades = [];
    let filteredNecessidades = [];
    let centrosCusto = [];
    let fornecedores = [];
    let currentPage = 1;
    const ITEMS_PER_PAGE = 50;
    let sortField = 'codigo';
    let sortDirection = 'asc';
    let selecoesPersistentes = new Set();
    let necessidadeEditando = null;
    let produtosPaiSelecionados = new Set();
    let todosProdutosPai = [];

    // ===== FUNÇÕES DE CÁLCULO DE ESTOQUE =====

    /**
     * Função centralizada para calcular saldo disponível
     * Corrige inconsistências no cálculo de empenho/reserva
     */
    function calcularSaldoDisponivel(estoque) {
      if (!estoque) return 0;

      const saldoTotal = Number(estoque.saldo) || 0;
      const saldoReservado = Number(estoque.saldoReservado) || 0;

      // Garantir que o saldo disponível nunca seja negativo
      return Math.max(0, saldoTotal - saldoReservado);
    }

    /**
     * Função para calcular déficit corretamente
     */
    function calcularDeficit(necessidadeAjustada, estoque) {
      const saldoDisponivel = calcularSaldoDisponivel(estoque);
      return Math.max(0, necessidadeAjustada - saldoDisponivel);
    }

    /**
     * Função para limpar empenhos órfãos
     * Remove reservas de estoque que não têm OPs ativas correspondentes
     */
    async function limparEmpenhosOrfaos() {
      if (!confirm('Esta operação irá limpar empenhos órfãos (reservas sem OPs ativas). Deseja continuar?')) {
        return;
      }

      try {
        console.log('Iniciando limpeza de empenhos órfãos...');
        let empenhosLiberados = 0;
        let valorLiberado = 0;

        // Buscar todos os estoques com saldoReservado > 0
        const estoquesSnap = await getDocs(query(
          collection(db, "estoques"),
          where("saldoReservado", ">", 0)
        ));

        // Buscar todas as OPs ativas
        const opsAtivasSnap = await getDocs(query(
          collection(db, "ordensProducao"),
          where("status", "in", ["Pendente", "Firme", "Em Produção"])
        ));
        const opsAtivas = opsAtivasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        for (const estoqueDoc of estoquesSnap.docs) {
          const estoque = { id: estoqueDoc.id, ...estoqueDoc.data() };
          const saldoReservado = Number(estoque.saldoReservado) || 0;

          if (saldoReservado <= 0) continue;

          // Verificar se existe OP ativa que justifique a reserva
          const temOPAtiva = opsAtivas.some(op =>
            op.materiaisNecessarios?.some(material =>
              material.produtoId === estoque.produtoId &&
              material.quantidadeReservada > 0
            )
          );

          if (!temOPAtiva) {
            // Liberar reserva órfã
            await updateDoc(doc(db, "estoques", estoque.id), {
              saldoReservado: 0,
              ultimaMovimentacao: Timestamp.now(),
              observacao: `Reserva órfã liberada: ${saldoReservado} unidades em ${new Date().toLocaleString()}`
            });

            empenhosLiberados++;
            valorLiberado += saldoReservado;

            console.log(`Liberada reserva órfã: Produto ${estoque.produtoId}, Quantidade: ${saldoReservado}`);
          }
        }

        alert(`Limpeza concluída!\n\nEmpenhos órfãos liberados: ${empenhosLiberados}\nQuantidade total liberada: ${valorLiberado.toFixed(2)} unidades`);

        // Recarregar dados para refletir as mudanças
        await reloadData();

      } catch (error) {
        console.error('Erro ao limpar empenhos órfãos:', error);
        alert('Erro ao limpar empenhos órfãos. Verifique o console para detalhes.');
      }
    }

    /**
     * Função para gerar relatório de saúde dos empenhos
     */
    async function gerarRelatorioEmpenhos() {
      try {
        console.log('Gerando relatório de saúde dos empenhos...');

        // Buscar estoques com reserva
        const estoquesComReservaSnap = await getDocs(query(
          collection(db, "estoques"),
          where("saldoReservado", ">", 0)
        ));

        // Buscar OPs ativas
        const opsAtivasSnap = await getDocs(query(
          collection(db, "ordensProducao"),
          where("status", "in", ["Pendente", "Firme", "Em Produção"])
        ));

        const estoquesComReserva = estoquesComReservaSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const opsAtivas = opsAtivasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        let totalReservado = 0;
        let empenhosOrfaos = 0;
        let valorOrfao = 0;

        const relatorio = {
          estoquesComReserva: estoquesComReserva.length,
          opsAtivas: opsAtivas.length,
          detalhes: []
        };

        estoquesComReserva.forEach(estoque => {
          const saldoReservado = Number(estoque.saldoReservado) || 0;
          totalReservado += saldoReservado;

          const temOPAtiva = opsAtivas.some(op =>
            op.materiaisNecessarios?.some(material =>
              material.produtoId === estoque.produtoId &&
              material.quantidadeReservada > 0
            )
          );

          if (!temOPAtiva) {
            empenhosOrfaos++;
            valorOrfao += saldoReservado;
          }

          relatorio.detalhes.push({
            produtoId: estoque.produtoId,
            saldoReservado,
            temOPAtiva,
            status: temOPAtiva ? 'OK' : 'ÓRFÃO'
          });
        });

        relatorio.totalReservado = totalReservado;
        relatorio.empenhosOrfaos = empenhosOrfaos;
        relatorio.valorOrfao = valorOrfao;

        console.log('Relatório de empenhos:', relatorio);

        const mensagem = `RELATÓRIO DE SAÚDE DOS EMPENHOS\n\n` +
          `📊 Estoques com reserva: ${relatorio.estoquesComReserva}\n` +
          `🏭 OPs ativas: ${relatorio.opsAtivas}\n` +
          `💰 Total reservado: ${totalReservado.toFixed(2)} unidades\n` +
          `⚠️ Empenhos órfãos: ${empenhosOrfaos}\n` +
          `🚨 Valor órfão: ${valorOrfao.toFixed(2)} unidades\n\n` +
          `${empenhosOrfaos > 0 ? '⚠️ Recomenda-se executar limpeza de empenhos órfãos!' : '✅ Sistema de empenhos saudável!'}`;

        alert(mensagem);
        return relatorio;

      } catch (error) {
        console.error('Erro ao gerar relatório de empenhos:', error);
        alert('Erro ao gerar relatório. Verifique o console para detalhes.');
      }
    }

    // Função para gerar número único de solicitação usando padrão unificado
    async function generateRequestNumber() {
      try {
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2); // Últimos 2 dígitos do ano
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0'); // Mês com 2 dígitos
        const anoMes = ano + mes; // Formato AAMM

        // Usar a coleção contadores para gerar numeração sequencial
        const counterRef = doc(db, "contadores", "solicitacoesCompra");
        const counterDoc = await getDoc(counterRef);

        let nextNumber = 1;

        if (counterDoc.exists()) {
          const counterData = counterDoc.data();

          // Verificar se mudou o mês/ano (reset automático)
          if (counterData.anoMes === anoMes) {
            nextNumber = counterData.valor + 1;
          } else {
            nextNumber = 1;
          }
        } else {
          // Criar contador se não existir
          await setDoc(counterRef, {
            valor: 1,
            anoMes: anoMes,
            ultimaAtualizacao: Timestamp.now(),
            descricao: "Contador para numeração de solicitações de compra"
          });
        }

        // Atualizar contador
        await updateDoc(counterRef, {
          valor: nextNumber,
          anoMes: anoMes,
          ultimaAtualizacao: Timestamp.now()
        });

        // Retornar número no formato padrão SC-AAMM-XXXX
        const numeroFormatado = `SC-${anoMes}-${nextNumber.toString().padStart(4, '0')}`;

        console.log('📄 Número de solicitação MRP gerado (relatório):', numeroFormatado);
        return numeroFormatado;

      } catch (error) {
        console.error('Erro ao gerar número da solicitação MRP:', error);
        // Fallback para método seguro em caso de erro
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2);
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0');
        const anoMes = ano + mes;
        const timestamp = Date.now().toString().slice(-4);
        return `SC-${anoMes}-${timestamp}`;
      }
    }

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }

      await loadCompanyData();
      await reloadData();
      await mostrarRelatorio();
      document.getElementById('sortDirection').textContent = 'Ascendente ▲';
    };

    async function loadCompanyData() {
      try {
        const docRef = doc(db, "empresa", "config");
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const data = docSnap.data();

          // Atualizar logo no cabeçalho de impressão
          const logoElement = document.querySelector('.print-header .logo');
          if (data.logoUrl && logoElement) {
            logoElement.src = data.logoUrl;
          }

          // Atualizar data e hora
          const now = new Date();
          const dateElement = document.getElementById('printDate');
          const timeElement = document.getElementById('printTime');

          if (dateElement) {
            dateElement.textContent = now.toLocaleDateString('pt-BR');
          }
          if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('pt-BR');
          }
        }
      } catch (error) {
        console.error("Erro ao carregar dados da empresa:", error);
      }
    }

    async function reloadData() {
      document.getElementById('loading').style.display = 'block';
      try {
        const [gruposSnap, familiasSnap, produtosSnap, ordensSnap, estoquesSnap, centrosCustoSnap, solicitacoesSnap, fornecedoresSnap] = await Promise.all([
          getDocs(collection(db, "grupos")),
          getDocs(collection(db, "familias")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "estoques")),
          getDocs(collection(db, "centrosCusto")),
          getDocs(collection(db, "solicitacoesCompra")),
          getDocs(collection(db, "fornecedores"))
        ]);

        grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log('Grupos carregados:', grupos.length);
        console.log('Famílias carregadas:', familias.length);
        console.log('Produtos carregados:', produtos.length);
        console.log('Ordens de Produção carregadas:', ordensProducao.length, ordensProducao.map(op => ({id: op.id, numero: op.numero, status: op.status})));
        console.log('Estoques carregados:', estoques.length);
        console.log('Centros de Custo carregados:', centrosCusto.length);
        console.log('Solicitações carregadas:', solicitacoes.length);
        console.log('Fornecedores carregados:', fornecedores.length);

        const filterGrupoSelect = document.getElementById('filterGrupo');
        filterGrupoSelect.innerHTML = '<option value="">Todos</option>';
        grupos.forEach(grupo => {
          filterGrupoSelect.innerHTML += `<option value="${grupo.codigoGrupo}">${grupo.codigoGrupo} - ${grupo.nomeGrupo}</option>`;
        });

        const filterFamiliaSelect = document.getElementById('filterFamilia');
        filterFamiliaSelect.innerHTML = '<option value="">Todas</option>';
        familias.forEach(familia => {
          filterFamiliaSelect.innerHTML += `<option value="${familia.codigoFamilia}">${familia.codigoFamilia} - ${familia.nomeFamilia}</option>`;
        });

        // Carregar produtos pai para o campo de busca múltipla
        todosProdutosPai = produtos.filter(p => p.tipo === 'PA').sort((a, b) => a.codigo.localeCompare(b.codigo));

        const filterOrdemProducaoSelect = document.getElementById('filterOrdemProducao');
        filterOrdemProducaoSelect.innerHTML = '<option value="">Todas</option>';
        ordensProducao.forEach(ordem => {
            filterOrdemProducaoSelect.innerHTML += `<option value="${ordem.id}">${ordem.numero}</option>`;
        });

        // Processar necessidades e atualizar filteredNecessidades
        necessidades = await processarNecessidades();
        console.log('Necessidades processadas inicialmente:', necessidades.length, necessidades.map(n => ({codigo: n.produto.codigo, necessidade: n.necessidadeAjustada, ordens: n.ordens})));

        // Definir filtro de status padrão e aplicar filtros
        document.getElementById('filterStatusSolicitacao').value = 'sem';
        await filtrarDados();

      } catch (error) {
        console.error('Erro ao recarregar dados:', error);
        document.getElementById('reportContent').innerHTML = '<p>Erro ao carregar relatório. Verifique o console para mais detalhes.</p>';
      } finally {
        document.getElementById('loading').style.display = 'none';
      }
    }

    // Função para carregar estruturas de produtos
    async function carregarEstruturas() {
      try {
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        return estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error('Erro ao carregar estruturas:', error);
        return [];
      }
    }

    // Função para explodir estrutura recursivamente e calcular necessidades de MP
    async function explodirEstrutura(produtoId, quantidade, estruturas, nivel = 0, visited = new Set()) {
      if (nivel > 10) {
        console.warn('Estrutura muito profunda, possível ciclo detectado');
        return [];
      }

      if (visited.has(produtoId)) {
        console.warn('Ciclo detectado na estrutura:', produtoId);
        return [];
      }

      visited.add(produtoId);

      const produto = produtos.find(p => p.id === produtoId);
      if (!produto) {
        console.warn('Produto não encontrado:', produtoId);
        return [];
      }

      // Se é MP, retorna a necessidade direta
      if (produto.tipo === 'MP') {
        return [{
          produtoId: produtoId,
          produto: produto,
          quantidade: quantidade
        }];
      }

      // Se é PA ou SP, busca a estrutura e explode
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
      if (!estrutura || !estrutura.componentes) {
        console.warn('Estrutura não encontrada para produto:', produto.codigo);
        return [];
      }

      const necessidadesMP = [];

      for (const componente of estrutura.componentes) {
        const quantidadeComponente = quantidade * componente.quantidade;
        const componenteProduto = produtos.find(p => p.id === componente.componentId);

        if (!componenteProduto) {
          console.warn('Componente não encontrado:', componente.componentId);
          continue;
        }

        console.log(`🔍 Explodindo ${produto.codigo} -> ${componenteProduto.codigo} (${componenteProduto.tipo}) - Qtd: ${quantidadeComponente}`);

        if (componenteProduto.tipo === 'MP') {
          // É MP, adiciona diretamente
          necessidadesMP.push({
            produtoId: componente.componentId,
            produto: componenteProduto,
            quantidade: quantidadeComponente
          });
        } else if (componenteProduto.tipo === 'SP' || componenteProduto.tipo === 'PA') {
          // É SP/PA, explode recursivamente
          const subNecessidades = await explodirEstrutura(
            componente.componentId,
            quantidadeComponente,
            estruturas,
            nivel + 1,
            new Set(visited)
          );
          necessidadesMP.push(...subNecessidades);
        }
      }

      return necessidadesMP;
    }

    async function processarNecessidades() {
      console.log('🔄 Iniciando processamento COMPLETO de necessidades com explosão de SPs...');
      console.log('Ordens de produção disponíveis para processamento inicial:', ordensProducao.length);

      // Carregar estruturas
      const estruturas = await carregarEstruturas();
      console.log('📋 Estruturas carregadas:', estruturas.length);

      // Mapa para armazenar necessidades agregadas por produto
      const necessidadesMap = new Map();

      // Filtrar apenas ordens pendentes ou em produção (qualquer variação de maiúsculas/minúsculas)
      const ordensValidas = ordensProducao.filter(op => {
        const status = (op.status || '').toLowerCase();
        return status === 'pendente' || status === 'em produção';
      });

      console.log(`Total de ordens pendentes ou em produção encontradas: ${ordensValidas.length}. Detalhes:`, ordensValidas.map(op => ({numero: op.numero, status: op.status})));

      // Função para atualizar a barra de progresso
      function atualizarBarraProgresso(valor, total) {
        const container = document.getElementById('progressBarContainer');
        const bar = document.getElementById('progressBar');
        if (valor < total) {
          container.style.display = 'block';
          const percent = Math.round((valor / total) * 100);
          bar.style.width = percent + '%';
          bar.textContent = percent + '%';
        } else {
          bar.style.width = '100%';
          bar.textContent = '100%';
          setTimeout(() => { container.style.display = 'none'; }, 500);
        }
      }

      let idxProgresso = 0;
      const totalProgresso = ordensValidas.length;
      for (const ordem of ordensValidas) {
        atualizarBarraProgresso(idxProgresso, totalProgresso);
        console.log(`\nProcessando OP ${ordem.numero} (ID: ${ordem.id}, Status: ${ordem.status})...`);

        // Verificar se tem materiais necessários
        if (!ordem.materiaisNecessarios || ordem.materiaisNecessarios.length === 0) {
          console.log(`OP ${ordem.numero} pulada: sem materiais necessários.`);
          continue;
        }
         console.log(`OP ${ordem.numero}: ${ordem.materiaisNecessarios.length} materiais necessários encontrados.`, ordem.materiaisNecessarios);

        // Processar cada material da ordem
        for (const material of ordem.materiaisNecessarios) {
          const produto = produtos.find(p => p.id === material.produtoId);
          if (!produto) {
            console.warn(`Produto não encontrado para material com produtoId ${material.produtoId} na OP ${ordem.numero}. Pulando material.`);
            continue;
          }
           console.log(`Processando material ${produto.codigo} (${produto.descricao}) para OP ${ordem.numero}. Tipo: ${produto.tipo}`);

          // Ignorar produtos do tipo PA ou SP
          if (produto.tipo === 'PA' || produto.tipo === 'SP') {
            console.log(`Produto ${produto.codigo} pulado (tipo: ${produto.tipo}).`);
            continue;
          }

          // Usar diretamente a quantidade do material necessário
          const quantidadeNecessaria = Number(material.quantidade) || 0;

          // Log detalhado do cálculo
          console.log(`Cálculo inicial para ${produto.codigo} na OP ${ordem.numero}: Quantidade necessária do material: ${material.quantidade}, Convertido para número: ${quantidadeNecessaria}`);

          // Se a quantidade calculada for 0 ou NaN, pular
          if (!quantidadeNecessaria || isNaN(quantidadeNecessaria)) {
            console.warn(`Quantidade necessária inválida (${quantidadeNecessaria}) para ${produto.codigo} na OP ${ordem.numero}. Pulando material.`);
            continue;
          }
           console.log(`Quantidade válida (${quantidadeNecessaria}) para ${produto.codigo} na OP ${ordem.numero}.`);

          // Criar ou atualizar a necessidade no mapa
          if (!necessidadesMap.has(produto.id)) {
            necessidadesMap.set(produto.id, {
              produtoId: produto.id,
              produto: {
                id: produto.id,
                codigo: produto.codigo,
                descricao: produto.descricao,
                unidade: produto.unidade,
                grupo: produto.grupo || 'Sem Grupo',
                familia: produto.familia || 'Sem Família',
                tipo: produto.tipo,
                leadTime: produto.leadTime || 0,
                pontoPedido: produto.pontoPedido || 0,
                estoqueMinimo: produto.estoqueMinimo || 0,
                loteCompra: produto.loteCompra || 0
              },
              quantidade: 0,
              ordens: new Set(),
              consumoPorOP: new Map(),
              saldoAtual: 0,
              necessidadeAjustada: 0,
              criticidade: '-'
            });
          }

          const necessidade = necessidadesMap.get(produto.id);

          // Adicionar quantidade necessária para esta OP
            necessidade.quantidade += quantidadeNecessaria;
            necessidade.ordens.add(ordem.numero);
          necessidade.consumoPorOP.set(ordem.numero, quantidadeNecessaria);

           console.log(`Necessidade agregada para ${produto.codigo}: ${necessidade.quantidade}. OPs: ${Array.from(necessidade.ordens).join(', ')}`);
        }
        idxProgresso++;
      }
      atualizarBarraProgresso(totalProgresso, totalProgresso);

      // Converter o mapa em array e calcular necessidades ajustadas e criticidade
      const necessidadesProcessadas = [];
      console.log('Calculando saldos, necessidades ajustadas e criticidade...');
      for (const [produtoId, dados] of necessidadesMap) {
        const estoque = estoques.find(e => e.produtoId === produtoId);
        const saldoTotal = estoque ? (Number(estoque.saldo) || 0) : 0;
          const saldoReservado = estoque ? (Number(estoque.saldoReservado) || 0) : 0;
          const saldoDisponivel = calcularSaldoDisponivel(estoque);

        // Calcular necessidade ajustada
        let necessidadeAjustada = dados.quantidade;

        // Ajustar com base no estoque mínimo e ponto de pedido apenas se houver necessidade gerada pelas OPs
         console.log(`Ajuste para ${dados.produto.codigo}: Necessidade OP: ${dados.quantidade}, Saldo Total: ${saldoTotal}, Saldo Reservado: ${saldoReservado}, Saldo Disponível: ${saldoDisponivel}, Ponto Pedido: ${dados.produto.pontoPedido}, Estoque Mínimo: ${dados.produto.estoqueMinimo}`);
        if (necessidadeAjustada > 0 || saldoDisponivel < dados.produto.pontoPedido) {
          const necessidadeEstoqueMinimo = Math.max(0, dados.produto.estoqueMinimo - saldoDisponivel);
           console.log(`Necessidade Estoque Mínimo: ${necessidadeEstoqueMinimo}`);
           necessidadeAjustada = Math.max(necessidadeAjustada, necessidadeEstoqueMinimo);
          }
         console.log(`Necessidade após ajuste Estoque/PP: ${necessidadeAjustada}`);

        // Ajustar para lote econômico de compra
        if (dados.produto.loteCompra > 0 && necessidadeAjustada > 0) {
          const multiplosLote = Math.ceil(necessidadeAjustada / dados.produto.loteCompra);
          necessidadeAjustada = multiplosLote * dados.produto.loteCompra;
           console.log(`Necessidade após ajuste Lote Compra (${dados.produto.loteCompra}): ${necessidadeAjustada}`);
        }

        // Calcular criticidade
        const criticidade = calcularCriticidade(necessidadeAjustada, saldoDisponivel, dados.produto.leadTime);
         console.log(`Criticidade calculada para ${dados.produto.codigo}: ${criticidade}`);

        necessidadesProcessadas.push({
          produtoId: produtoId,
          produto: dados.produto,
          quantidade: dados.quantidade,
          necessidadeAjustada: necessidadeAjustada,
          saldoTotal: saldoTotal,
          saldoReservado: saldoReservado,
          saldoDisponivel: saldoDisponivel,
          ordens: Array.from(dados.ordens),
          criticidade: criticidade
        });
      }

      console.log('Processamento de necessidades finalizado. Total de necessidades processadas:', necessidadesProcessadas.length);
      return necessidadesProcessadas;
    }

    function calcularCriticidade(necessidadeAjustada, saldoAtual, leadTime) {
      console.log('Calculando criticidade:', { necessidadeAjustada, saldoAtual, leadTime });

      if (necessidadeAjustada > saldoAtual && leadTime >= 20) {
         console.log('Criticidade ALTA: Necessidade > Saldo e Lead Time >= 20');
        return 'alta';
      }

       if (necessidadeAjustada > saldoAtual && leadTime >= 7) {
         console.log('Criticidade MÉDIA: Necessidade > Saldo e Lead Time >= 7');
        return 'media';
      }

       if (necessidadeAjustada > 0 && saldoAtual <= 0) {
         console.log('Criticidade ALTA: Necessidade > 0 e Saldo <= 0');
        return 'alta';
      }

       if (necessidadeAjustada > saldoAtual) {
         console.log('Criticidade MÉDIA: Necessidade > Saldo');
        return 'media';
      }

      console.log('Criticidade BAIXA: Necessidade Ajustada <= Saldo Atual');
      return 'baixa';
    }

    window.filtrarDados = async function() {
      // Salvar estado atual antes de filtrar
      const selects = document.querySelectorAll('.fornecedor-select');
      selects.forEach(select => {
        if (select.value) {
          fornecedoresSelecionados[select.dataset.produtoId] = select.value;
        }
      });

      const grupo = document.getElementById('filterGrupo').value;
      const familia = document.getElementById('filterFamilia').value;
      const criticidade = document.getElementById('filterCriticidade').value;
      sortField = document.getElementById('sortField').value;
      const statusSolicitacao = document.getElementById('filterStatusSolicitacao').value;
      const ordemProducaoId = document.getElementById('filterOrdemProducao').value;

        console.log('Aplicando filtros:', { 
          grupo, 
          familia, 
          criticidade, 
          sortField, 
          statusSolicitacao, 
          produtosPaiSelecionados: Array.from(produtosPaiSelecionados), 
          ordemProducaoId,
          totalOrdensProducao: ordensProducao.length,
          totalProdutosPaiSelecionados: produtosPaiSelecionados.size
        });
        console.log('Total de necessidades antes do filtro:', necessidades.length);
        console.log('Ordens de produção disponíveis:', ordensProducao.map(op => ({
          id: op.id,
          numero: op.numero,
          produtoPaiId: op.produtoPaiId,
          temMateriais: !!op.materiaisNecessarios?.length
        })));

      // Carregar solicitações existentes para filtrar por status
      let solicitacoesExistentes = [];
      try {
        const solicitacoesSnap = await getDocs(collection(db, "solicitacoesCompra"));
        solicitacoesExistentes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (e) {
        console.error('Erro ao carregar solicitações para filtro de status:', e);
      }

      filteredNecessidades = necessidades.filter(n => {
        console.log('Avaliando necessidade:', {
          produto: n.produto.codigo,
          tipo: n.produto.tipo,
          produtoId: n.produtoId,
          ordens: n.ordens
        });

        const matchesGrupo = !grupo || n.produto.grupo === grupo;
        const matchesFamilia = !familia || n.produto.familia === familia;
        const matchesCriticidade = !criticidade || n.criticidade === criticidade;

                console.log('🔍 DEBUG FILTRO - Produtos pai selecionados:', Array.from(produtosPaiSelecionados));
        console.log('🔍 DEBUG FILTRO - Necessidade sendo analisada:', {
          codigo: n.produto.codigo,
          tipo: n.produto.tipo,
          ordens: Array.from(n.ordens || [])
        });

        let matchesProdutoPai = false;
        const produtosSelecionados = Array.from(produtosPaiSelecionados);

        if (produtosSelecionados.length === 0 && !ordemProducaoId) {
          matchesProdutoPai = true;
        } else if (n.produto.tipo !== 'MP') {
          matchesProdutoPai = true;
        } else {
          // LÓGICA CORRIGIDA: Encontrar todas as OPs relacionadas aos produtos pai selecionados
          const opsRelacionadas = ordensProducao.filter(op => {
            // OP principal: produtoPaiId pode ser igual ao produtoId ou undefined
            // OP filha: produtoPaiId aponta para o produto pai
            return produtosPaiSelecionados.has(op.produtoPaiId) ||
                   (op.nivel === 0 && produtosPaiSelecionados.has(op.produtoId));
          });

          console.log(`🔍 Filtro Produto Pai - Produtos selecionados:`, Array.from(produtosPaiSelecionados));
          console.log(`🔍 Filtro Produto Pai - OPs relacionadas encontradas:`, opsRelacionadas.map(op => ({
            numero: op.numero,
            produtoId: op.produtoId,
            produtoPaiId: op.produtoPaiId,
            nivel: op.nivel
          })));

          // Verifica se a necessidade está ligada a alguma dessas OPs
          matchesProdutoPai = (n.ordens || []).some(ordemNumero => {
            const match = opsRelacionadas.some(op => op.numero === ordemNumero);
            if (match) {
              console.log(`✅ Necessidade ${n.produto.codigo} matched com OP ${ordemNumero}`);
            }
            return match;
          });

          console.log(`🎯 Resultado filtro produto pai para ${n.produto.codigo}: ${matchesProdutoPai}`);
        }

        console.log('Resultados parciais:', { matchesGrupo, matchesFamilia, matchesCriticidade, matchesProdutoPai });
        const matchesOrdemProducao = !ordemProducaoId || (n.ordens || []).some(ordem => {
          const op = ordensProducao.find(op => op.id === ordemProducaoId);
          const match = op && op.numero === ordem;
          if (match) {
            console.log('Match encontrado para ordem de produção:', {
              ordemProducaoId,
              numeroOrdem: op.numero,
              produtoId: n.produtoId
            });
          }
          return match;
        });

        // Lógica de status da solicitação
        let matchesStatus = true;
        console.log('Verificando status:', { statusSolicitacao });

        if (statusSolicitacao === 'sem') {
          matchesStatus = !solicitacoesExistentes.some(s =>
            s.itens?.some(item => 
              item.codigo === n.produto.codigo && 
              (item.ordensOrigem || []).some(ordem => (n.ordens || []).includes(ordem))
            ) &&
            s.status !== 'REJEITADA' && s.status !== 'CANCELADA'
          );
        } else if (statusSolicitacao === 'solicitada') {
          matchesStatus = solicitacoesExistentes.some(s =>
            s.itens?.some(item => 
              item.codigo === n.produto.codigo && 
              (item.ordensOrigem || []).some(ordem => (n.ordens || []).includes(ordem))
            ) &&
            s.status !== 'REJEITADA' && s.status !== 'CANCELADA' && s.status !== 'RECEBIDA'
          );
        } else if (statusSolicitacao === 'recebida') {
          matchesStatus = solicitacoesExistentes.some(s =>
            s.itens?.some(item => 
              item.codigo === n.produto.codigo && 
              (item.ordensOrigem || []).some(ordem => (n.ordens || []).includes(ordem))
            ) &&
            s.status === 'RECEBIDA'
          );
        }

        return matchesGrupo && matchesFamilia && matchesCriticidade && matchesStatus && matchesProdutoPai && matchesOrdemProducao;
      });

      console.log('Necessidades após filtro:', filteredNecessidades.length);

      filteredNecessidades.sort((a, b) => {
        let valueA, valueB;

        switch (sortField) {
          case 'tipo':
            valueA = a.produto.tipo || '';
            valueB = b.produto.tipo || '';
            break;
          case 'grupo':
            valueA = a.produto.grupo || '';
            valueB = b.produto.grupo || '';
            break;
          case 'familia':
            valueA = a.produto.familia || '';
            valueB = b.produto.familia || '';
            break;
          case 'codigo':
          default:
            valueA = a.produto.codigo || '';
            valueB = b.produto.codigo || '';
            break;
        }

        if (sortDirection === 'asc') {
          return valueA.localeCompare(valueB);
        } else {
          return valueB.localeCompare(valueA);
        }
      });

      await mostrarRelatorio();
    };

    window.toggleSortDirection = function() {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      const sortButton = document.getElementById('sortDirection');
      sortButton.innerHTML = sortDirection === 'asc' ? '📈 Ascendente ▲' : '📉 Descendente ▼';
      filtrarDados();
    };

    async function mostrarRelatorio() {
      const reportContent = document.getElementById('reportContent');
      reportContent.innerHTML = '';

      if (filteredNecessidades.length === 0) {
        reportContent.innerHTML = '<p>Nenhuma necessidade de compra encontrada com os filtros aplicados.</p>';
        return;
      }

      // Carregar fornecedores para seleção
      let fornecedores = [];
      let solicitacoesExistentes = [];
      try {
        const fornecedoresSnap = await getDocs(collection(db, "fornecedores"));
        fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        console.log('Fornecedores carregados:', fornecedores.length);
        const solicitacoesSnap = await getDocs(collection(db, "solicitacoesCompra"));
        solicitacoesExistentes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (e) {
        console.error('Erro ao carregar fornecedores ou solicitações:', e);
      }

      // Agrupar por família
      const necessidadesPorFamilia = {};
      filteredNecessidades.forEach(necessidade => {
        const familiaKey = necessidade.produto.familia || 'Sem Família';
        if (!necessidadesPorFamilia[familiaKey]) {
          necessidadesPorFamilia[familiaKey] = [];
        }
        necessidadesPorFamilia[familiaKey].push(necessidade);
      });

      // Criar e preencher tabela para cada família
      Object.entries(necessidadesPorFamilia).forEach(([familiaKey, itens]) => {
        const familiaDiv = document.createElement('div');
        familiaDiv.className = 'familia-section';

        const familia = familias.find(f => f.codigoFamilia === familiaKey);
        const familiaNome = familia ? `${familia.codigoFamilia} - ${familia.nomeFamilia}` : familiaKey;

        let tabelaHtml = `
          <h3 class="familia-title">${familiaNome}</h3>
          <table class="data-table">
            <thead>
              <tr>
                <th data-col="checkbox"><input type="checkbox" id="selectAll_${familiaKey}" onchange="toggleSelectAll('${familiaKey}')"></th>
                <th data-col="codigo">Código</th>
                <th data-col="descricao">Descrição</th>
                <th data-col="necessidade">Necessidade</th>
                <th data-col="saldo">Saldo </th>
                <th data-col="reservado">Reservado</th>
                <th data-col="disponivel">Disponível</th>
                <th data-col="deficit">Déficit</th>
                <th data-col="status">Status</th>
                <th data-col="dataEntrega">Prev. Entrega</th>
                <th data-col="criticidade">Criticidade</th>
                <th data-col="acoes">Ações</th>
              </tr>
            </thead>
            <tbody>
        `;

        // Adicionar linhas de dados
        itens.forEach((necessidade, idx) => {
          const estoque = estoques.find(e => e.produtoId === necessidade.produtoId) || { saldo: 0, saldoReservado: 0 };

          // Usar funções centralizadas para cálculos corretos
          const saldoReservado = Number(estoque.saldoReservado) || 0;
          const saldoDisponivel = calcularSaldoDisponivel(estoque);
          const deficit = calcularDeficit(necessidade.necessidadeAjustada, estoque);

          const checkboxId = `checkbox_${necessidade.produtoId}_${familiaKey}`;
          const isChecked = selecoesPersistentes.has(necessidade.produtoId) ? 'checked' : '';

          // Adicionar o produto às seleções persistentes se estiver marcado
          if (isChecked) {
            selecoesPersistentes.add(necessidade.produtoId);
          }

          // Buscar solicitação relacionada
          const solicitacaoRel = solicitacoesExistentes.find(s =>
            s.itens?.some(item => item.codigo === necessidade.produto.codigo && item.ordensOrigem?.some(ordem => necessidade.ordens.includes(ordem)))
          );
          const statusSolicitacao = solicitacaoRel ? (solicitacaoRel.status || '-') : '-';
          const dataEntrega = solicitacaoRel && solicitacaoRel.dataProgramadaEntrega
            ? new Date(solicitacaoRel.dataProgramadaEntrega.seconds * 1000).toLocaleDateString()
            : '-';

          tabelaHtml += `
            <tr class="criticidade-${necessidade.criticidade}" data-produto-id="${necessidade.produtoId}" data-codigo="${necessidade.produto.codigo}">
              <td data-col="checkbox"><input type="checkbox" class="select-necessidade" data-familia="${familiaKey}" data-produto-id="${necessidade.produtoId}" data-codigo="${necessidade.produto.codigo}" id="${checkboxId}" ${isChecked} onchange="handleCheckboxChange(this)"></td>
              <td data-col="codigo">${necessidade.produto.codigo}</td>
              <td data-col="descricao">${necessidade.produto.descricao}</td>
              <td data-col="necessidade">${necessidade.necessidadeAjustada.toFixed(2)} ${necessidade.produto.unidade}</td>
              <td data-col="saldo">${necessidade.saldoTotal.toFixed(2)} ${necessidade.produto.unidade}</td>
              <td data-col="reservado">${necessidade.saldoReservado.toFixed(2)} ${necessidade.produto.unidade}</td>
              <td data-col="disponivel">${necessidade.saldoDisponivel.toFixed(2)} ${necessidade.produto.unidade}</td>
              <td data-col="deficit">${deficit.toFixed(2)} ${necessidade.produto.unidade}</td>

              <td data-col="status">${statusSolicitacao}</td>
              <td data-col="dataEntrega">${dataEntrega}</td>
              <td data-col="criticidade">
                <span class="status-badge status-${necessidade.criticidade}">
                  ${necessidade.criticidade.toUpperCase()}
                </span>
              </td>
              <td data-col="acoes">
                ${statusSolicitacao === 'REJEITADA' ? 
                  `<button class="btn-edit" onclick="editarNecessidade('${necessidade.produtoId}', '${necessidade.produto.codigo}')">
                    <i class="fas fa-edit"></i> Editar
                  </button>` : ''}
              </td>
            </tr>
          `;
        });

        tabelaHtml += `
            </tbody>
          </table>
        `;

        familiaDiv.innerHTML = tabelaHtml;
        reportContent.appendChild(familiaDiv);

        // Atualizar estado do checkbox "Selecionar Todos"
        updateSelectAllState(familiaKey);
      });

      // Atualizar botão de filtrar selecionados após renderizar
      setTimeout(() => {
        atualizarBotaoFiltrarSelecionados();
      }, 100);
    }

    // Função para atualizar o estado do checkbox "Selecionar Todos"
    function updateSelectAllState(familiaKey) {
      const checkboxes = document.querySelectorAll(`.select-necessidade[data-familia='${familiaKey}']`);
      const selectAll = document.getElementById(`selectAll_${familiaKey}`);

      if (checkboxes.length === 0) return;

      const allChecked = Array.from(checkboxes).every(cb => cb.checked);
      const someChecked = Array.from(checkboxes).some(cb => cb.checked);

      selectAll.checked = allChecked;
      selectAll.indeterminate = someChecked && !allChecked;
    }

    // Função para lidar com mudanças nos checkboxes individuais
    window.handleCheckboxChange = function(checkbox) {
      const produtoId = checkbox.dataset.produtoId;
      const familiaKey = checkbox.dataset.familia;

      if (checkbox.checked) {
        selecoesPersistentes.add(produtoId);
      } else {
        selecoesPersistentes.delete(produtoId);
      }

      updateSelectAllState(familiaKey);
      atualizarBotaoFiltrarSelecionados();
    };

    // Função para selecionar/deselecionar todos os itens de uma família
    window.toggleSelectAll = function(familiaKey) {
      const selectAll = document.getElementById(`selectAll_${familiaKey}`);
      const checkboxes = document.querySelectorAll(`.select-necessidade[data-familia='${familiaKey}']`);

      checkboxes.forEach(checkbox => {
        const produtoId = checkbox.dataset.produtoId;
        checkbox.checked = selectAll.checked;

        if (selectAll.checked) {
          selecoesPersistentes.add(produtoId);
        } else {
          selecoesPersistentes.delete(produtoId);
        }
      });

      atualizarBotaoFiltrarSelecionados();
    };

    window.limparSelecoes = function() {
      console.log('Limpando todas as seleções');
      selecoesPersistentes.clear();
      document.querySelectorAll('.select-necessidade').forEach(cb => cb.checked = false);
      document.querySelectorAll('[id^="selectAll_"]').forEach(cb => {
        cb.checked = false;
        cb.indeterminate = false;
      });
      atualizarBotaoFiltrarSelecionados();
    };

    window.gerarSolicitacoes = async function() {
      try {
        console.log('Iniciando geração de solicitações...');

        // Recarregar dados antes de gerar solicitações
        await reloadData();

        const necessidadesSelecionadas = [];
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"].select-necessidade:checked');

        if (allCheckboxes.length === 0) {
          alert('Selecione pelo menos uma necessidade para gerar solicitações.');
          return;
        }

        for (const checkbox of allCheckboxes) {
          const produtoId = checkbox.dataset.produtoId;
          const codigo = checkbox.dataset.codigo;

          const necessidade = filteredNecessidades.find(n => 
            n.produtoId === produtoId && 
            n.produto.codigo === codigo
          );

          if (!necessidade) {
            console.warn(`Necessidade não encontrada para o produto ${codigo} (ID: ${produtoId}). Pulando.`);
            continue;
          }

          necessidadesSelecionadas.push(necessidade);
        }

        // Restante da função permanece igual, mas remova a parte que agrupa por fornecedor
        // Em vez disso, você pode agrupar apenas por família ou outro critério
        const solicitacoesPorFamilia = {};

        necessidadesSelecionadas.forEach(necessidade => {
          const familiaKey = necessidade.produto.familia || 'Sem Família';

          if (!solicitacoesPorFamilia[familiaKey]) {
            solicitacoesPorFamilia[familiaKey] = {
              itens: [], 
              familia: familias.find(f => f.codigoFamilia === familiaKey) 
            };
          }

          solicitacoesPorFamilia[familiaKey].itens.push({
            produtoId: necessidade.produtoId,
            codigo: necessidade.produto.codigo,
            descricao: necessidade.produto.descricao,
            quantidade: necessidade.necessidadeAjustada,
            unidade: necessidade.produto.unidade,
            ordensOrigem: necessidade.ordens
          });
        });

        // Agora, ao invés de criar uma solicitação por fornecedor, você pode criar uma por família
        for (const [familiaKey, dados] of Object.entries(solicitacoesPorFamilia)) {
          if (dados.itens.length === 0) continue;

          try {
            const numeroSolicitacao = await generateRequestNumber();

            const solicitacaoData = {
              numero: numeroSolicitacao,
              dataCriacao: Timestamp.now(),
              status: 'PENDENTE',
              tipo: 'PLANEJADA',
              origem: 'MRP',
              familia: familiaKey,
              solicitante: 'Sistema MRP',
              departamento: 'PRODUCAO',
              centroCustoId: centrosCusto.find(cc => cc.codigo === 'PROD' || cc.codigo === 'PRODUCAO')?.id || (centrosCusto.length > 0 ? centrosCusto[0].id : null),
              prioridade: 'NORMAL',
              // Fornecedor será definido na tela de solicitação de compras
              itens: dados.itens,
              justificativa: `Solicitação gerada automaticamente pelo MRP para família ${dados.familia?.nomeFamilia || familiaKey}.\nOrdens de Produção: ${[...new Set(dados.itens.flatMap(i => i.ordensOrigem))].join(', ')}`,
              mrpInfo: {
                dataAnalise: Timestamp.now(),
                ordensProducao: [...new Set(dados.itens.flatMap(i => i.ordensOrigem))]
              }
            };

            const docRef = await addDoc(collection(db, "solicitacoesCompra"), solicitacaoData);
            console.log(`Solicitação ${solicitacaoData.numero} gerada com sucesso. ID: ${docRef.id}`);

          } catch (error) {
            console.error('Erro ao gerar solicitação:', error);
            throw new Error(`Erro ao gerar solicitação para família ${familiaKey}: ${error.message}`);
          }
        }

        alert('Solicitações geradas com sucesso!');
        await reloadData();
        await mostrarRelatorio();

      } catch (error) {
        console.error('Erro ao gerar solicitações:', error);
        alert('Erro ao gerar solicitações: ' + error.message);
      }
    };

    // Atualizar os filtros para usar códigos
    function updateFilters() {
      const grupoSelect = document.getElementById('filterGrupo');
      const familiaSelect = document.getElementById('filterFamilia');

      // Limpar e preencher grupos
      grupoSelect.innerHTML = '<option value="">Todos</option>';
      grupos
        .sort((a, b) => a.codigoGrupo.localeCompare(b.codigoGrupo))
        .forEach(grupo => {
          grupoSelect.innerHTML += `
            <option value="${grupo.codigoGrupo}">
              ${grupo.codigoGrupo} - ${grupo.nomeGrupo}
            </option>
          `;
        });

      // Limpar e preencher famílias
      familiaSelect.innerHTML = '<option value="">Todas</option>';
      const grupoSelecionado = grupoSelect.value;
      familias
        .filter(f => !grupoSelecionado || f.grupo === grupoSelecionado)
        .sort((a, b) => a.codigoFamilia.localeCompare(b.codigoFamilia))
        .forEach(familia => {
          familiaSelect.innerHTML += `
            <option value="${familia.codigoFamilia}">
              ${familia.codigoFamilia} - ${familia.nomeFamilia}
            </option>
          `;
        });
    }

    // Atualizar o filtro de famílias quando o grupo é alterado
    document.getElementById('filterGrupo').addEventListener('change', function() {
      updateFilters();
      generateReport();
    });

    // Preencher famílias no select múltiplo ao abrir o modal
    function preencherSelectFamilias() {
      const select = document.getElementById('familias-relatorio');
      if (!select) return;
      select.innerHTML = '';
      familias.forEach(f => {
        const opt = document.createElement('option');
        opt.value = f.codigoFamilia;
        opt.textContent = `${f.codigoFamilia} - ${f.nomeFamilia}`;
        select.appendChild(opt);
      });
    }

    // Funções para abrir e fechar o modal
    window.abrirModalConfigRelatorio = function() {
      document.getElementById('modal-config-relatorio').style.display = 'flex';
      preencherSelectFamilias();
    }

    window.fecharModalConfigRelatorio = function() {
      document.getElementById('modal-config-relatorio').style.display = 'none';
    }

    // Função para gerar relatório personalizado e imprimir
    window.gerarRelatorioEImprimir = function() {
      window.gerarRelatorioPersonalizado();
      fecharModalConfigRelatorio();
      setTimeout(() => window.print(), 200); // Pequeno delay para garantir renderização
    }

    // Função para gerar relatório personalizado
    window.gerarRelatorioPersonalizado = function() {
      // Obter colunas selecionadas
      const colunasSelecionadas = Array.from(document.querySelectorAll('.col-toggle:checked')).map(cb => cb.value);
      // Obter famílias selecionadas
      const familiasSelecionadas = Array.from(document.getElementById('familias-relatorio').selectedOptions).map(opt => opt.value);

      // Esconder/mostrar colunas
      document.querySelectorAll('.data-table').forEach(table => {
        // Cabeçalho
        table.querySelectorAll('th').forEach((th, idx) => {
          const col = th.getAttribute('data-col');
          th.style.display = colunasSelecionadas.includes(col) ? '' : 'none';
        });
        // Linhas
        table.querySelectorAll('tbody tr').forEach(tr => {
          tr.querySelectorAll('td').forEach((td, idx) => {
            const col = td.getAttribute('data-col');
            td.style.display = colunasSelecionadas.includes(col) ? '' : 'none';
          });
        });
      });

      // Esconder/mostrar famílias
      document.querySelectorAll('.familia-section').forEach(div => {
        const familiaKey = div.getAttribute('data-familia-key');
        div.style.display = (familiasSelecionadas.length === 0 || familiasSelecionadas.includes(familiaKey)) ? '' : 'none';
      });
    };

    window.exportarParaExcel = function() {
      const tables = document.querySelectorAll('.data-table');
      if (!tables.length) {
        alert('Nenhuma tabela encontrada para exportar.');
        return;
      }
      // Cria uma tabela temporária para juntar todas as linhas
      const tempTable = document.createElement('table');
      let headerAdded = false;
      tables.forEach(table => {
        const thead = table.querySelector('thead');
        const tbody = table.querySelector('tbody');
        // Adiciona o cabeçalho apenas uma vez
        if (!headerAdded && thead) {
          tempTable.appendChild(thead.cloneNode(true));
          headerAdded = true;
        }
        // Adiciona todas as linhas do corpo
        if (tbody) {
          Array.from(tbody.rows).forEach(row => {
            tempTable.appendChild(row.cloneNode(true));
          });
        }
      });
      // Converte a tabela única para planilha
      const ws = XLSX.utils.table_to_sheet(tempTable);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Relatório');
      XLSX.writeFile(wb, 'relatorio_necessidades_compras.xlsx');
    }

    window.editarNecessidade = async function(produtoId, codigo) {
      const necessidade = filteredNecessidades.find(n => n.produtoId === produtoId);
      if (!necessidade) return;

      necessidadeEditando = necessidade;

      // Preencher o modal com os dados atuais
      document.getElementById('editCodigo').value = codigo;
      document.getElementById('editQuantidade').value = necessidade.necessidadeAjustada;

      // Preencher grupos
      const grupoSelect = document.getElementById('editGrupo');
      grupoSelect.innerHTML = '<option value="">Selecione um grupo</option>';
      grupos.forEach(grupo => {
        const option = document.createElement('option');
        option.value = grupo.codigoGrupo;
        option.textContent = `${grupo.codigoGrupo} - ${grupo.nomeGrupo}`;
        option.selected = grupo.codigoGrupo === necessidade.produto.grupo;
        grupoSelect.appendChild(option);
      });

      // Preencher famílias
      const familiaSelect = document.getElementById('editFamilia');
      familiaSelect.innerHTML = '<option value="">Selecione uma família</option>';
      familias.forEach(familia => {
        const option = document.createElement('option');
        option.value = familia.codigoFamilia;
        option.textContent = `${familia.codigoFamilia} - ${familia.nomeFamilia}`;
        option.selected = familia.codigoFamilia === necessidade.produto.familia;
        familiaSelect.appendChild(option);
      });

      // Mostrar o modal
      document.getElementById('editModal').style.display = 'block';
    }

    function fecharModal() {
      document.getElementById('editModal').style.display = 'none';
      necessidadeEditando = null;
    }

    window.salvarEdicao = async function() {
      if (!necessidadeEditando) return;

      const quantidade = parseFloat(document.getElementById('editQuantidade').value);
      const grupo = document.getElementById('editGrupo').value;
      const familia = document.getElementById('editFamilia').value;

      if (!quantidade || quantidade <= 0) {
        showNotification('Por favor, informe uma quantidade válida', 'error');
        return;
      }

      if (!grupo || !familia) {
        showNotification('Por favor, selecione o grupo e a família', 'error');
        return;
      }

      try {
        // Atualizar a necessidade no array local
        necessidadeEditando.necessidadeAjustada = quantidade;
        necessidadeEditando.produto.grupo = grupo;
        necessidadeEditando.produto.familia = familia;

        // Atualizar no Firestore - Corrigindo a referência do documento
        const necessidadeRef = doc(db, 'necessidadesCompra', necessidadeEditando.produtoId);
        await updateDoc(necessidadeRef, {
          necessidadeAjustada: quantidade,
          'produto.grupo': grupo,
          'produto.familia': familia,
          ultimaAtualizacao: Timestamp.now()
        });

        // Atualizar a exibição
        await mostrarRelatorio();
        fecharModal();
        showNotification('Necessidade atualizada com sucesso', 'success');
      } catch (error) {
        console.error('Erro ao atualizar necessidade:', error);
        showNotification('Erro ao atualizar necessidade: ' + error.message, 'error');
      }
    }

    // Fechar modal ao clicar fora dele
    window.onclick = function(event) {
      const modal = document.getElementById('editModal');
      if (event.target === modal) {
        fecharModal();
      }
    }

    // Add showNotification function
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;

      document.body.appendChild(notification);

      // Remove notification after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Funções para gerenciar produtos pai múltiplos (definidas no escopo global)
    window.filtrarProdutosPai = function(termo) {
      const lista = document.getElementById('listaProdutosPai');

      if (termo.length < 2) {
        lista.style.display = 'none';
        return;
      }

      const produtosFiltrados = todosProdutosPai.filter(produto => 
        produto.codigo.toLowerCase().includes(termo.toLowerCase()) ||
        produto.descricao.toLowerCase().includes(termo.toLowerCase())
      );

      lista.innerHTML = '';
      lista.className = 'dropdown-list-enhanced';

      if (produtosFiltrados.length === 0) {
        lista.innerHTML = '<div class="dropdown-item">🚫 Nenhum produto encontrado</div>';
      } else {
        produtosFiltrados.forEach(produto => {
          if (!produtosPaiSelecionados.has(produto.id)) {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.innerHTML = `<strong>${produto.codigo}</strong> - ${produto.descricao}`;
            item.onclick = () => window.adicionarProdutoPai(produto);
            lista.appendChild(item);
          }
        });
      }

      lista.style.display = 'block';
    };

    window.limparBuscaProdutosPai = function() {
      document.getElementById('searchProdutoPai').value = '';
      document.getElementById('listaProdutosPai').style.display = 'none';
    };

    window.adicionarProdutoPai = function(produto) {
      produtosPaiSelecionados.add(produto.id);
      atualizarProdutosPaiSelecionados();
      document.getElementById('searchProdutoPai').value = '';
      document.getElementById('listaProdutosPai').style.display = 'none';
      atualizarBotaoFiltrarSelecionados();
      filtrarDados();
    }

    window.removerProdutoPai = function(produtoId) {
      produtosPaiSelecionados.delete(produtoId);
      atualizarProdutosPaiSelecionados();
      atualizarBotaoFiltrarSelecionados();
      filtrarDados();
    };

    function atualizarProdutosPaiSelecionados() {
      const container = document.getElementById('produtosPaiSelecionados');
      const countElement = document.getElementById('produtosCount');

      container.innerHTML = '';

      if (produtosPaiSelecionados.size === 0) {
        container.innerHTML = '<div style="color: #666; font-style: italic; padding: 8px;">✨ Nenhum produto selecionado</div>';
      }

      produtosPaiSelecionados.forEach(produtoId => {
        const produto = todosProdutosPai.find(p => p.id === produtoId);
        if (produto) {
          const item = document.createElement('div');
          item.className = 'selected-item';
          item.innerHTML = `
            <span>📦 ${produto.codigo}</span>
            <button class="remove-btn" onclick="window.removerProdutoPai('${produtoId}')" title="Remover produto">×</button>
          `;
          container.appendChild(item);
        }
      });

      // Atualizar contador
      countElement.textContent = `${produtosPaiSelecionados.size} produto${produtosPaiSelecionados.size !== 1 ? 's' : ''} selecionado${produtosPaiSelecionados.size !== 1 ? 's' : ''}`;
    }

    function atualizarBotaoFiltrarSelecionados() {
      const btnFiltrar = document.getElementById('btnFiltrarSelecionados');
      const temSelecoes = selecoesPersistentes.size > 0 ||    produtosPaiSelecionados.size > 0;

      btnFiltrar.disabled = !temSelecoes;
      btnFiltrar.title = temSelecoes ? 
        'Mostrar apenas produtos dos itens selecionados na tabela' : 
        'Selecione alguns itens na tabela ou produtos pai para habilitar este filtro';
    }

    // Nova função para filtrar apenas pelos produtos selecionados na tabela
    window.filtrarApenasSeleccionados = function() {
      const checkboxesSelecionados = document.querySelectorAll('.select-necessidade:checked');

      if (checkboxesSelecionados.length === 0 && produtosPaiSelecionados.size === 0) {
        alert('⚠️ Selecione pelo menos um item na tabela ou produto pai para usar este filtro.');
        return;
      }

      // Coletar IDs dos produtos selecionados na tabela
      const produtosSelecionadosIds = new Set();
      checkboxesSelecionados.forEach(checkbox => {
        const produtoId = checkbox.dataset.produtoId;
        produtosSelecionadosIds.add(produtoId);
      });

      // Se há produtos selecionados na tabela, aplicar filtro por eles
      if (produtosSelecionadosIds.size > 0) {
        console.log('Aplicando filtro para produtos selecionados:', Array.from(produtosSelecionadosIds));

        // Filtrar apenas as necessidades dos produtos selecionados
        filteredNecessidades = necessidades.filter(n => 
          produtosSelecionadosIds.has(n.produtoId)
        );

        console.log('Necessidades após filtro de selecionados:', filteredNecessidades.length);

        // Atualizar a exibição imediatamente
        mostrarRelatorio();
        showNotification(`🎯 Filtro aplicado! Mostrando apenas os ${checkboxesSelecionados.length} itens selecionados.`, 'success');
        return;
      }

      // Se não há produtos selecionados na tabela mas há produtos pai, manter filtro de produtos pai
      if (produtosPaiSelecionados.size > 0) {
        filtrarDados();
        showNotification('🎯 Filtro de produtos pai mantido.', 'success');
      }
    };

    // Fechar dropdown ao clicar fora
    document.addEventListener('click', function(event) {
      const container = document.querySelector('.multi-select-container');
      const lista = document.getElementById('listaProdutosPai');

      if (container && !container.contains(event.target)) {
        lista.style.display = 'none';
      }
    });

    // Função para debug do filtro de produtos pai
    window.debugFiltroProdutosPai = function() {
      console.log('🔍 DEBUG FILTRO PRODUTOS PAI:');
      console.log('Produtos pai selecionados:', Array.from(produtosPaiSelecionados));

      const produtosSelecionados = Array.from(produtosPaiSelecionados);
      if (produtosSelecionados.length === 0) {
        console.log('❌ Nenhum produto pai selecionado');
        return;
      }

      // Encontrar OPs relacionadas
      const opsRelacionadas = ordensProducao.filter(op => {
        return produtosPaiSelecionados.has(op.produtoPaiId) ||
               (op.nivel === 0 && produtosPaiSelecionados.has(op.produtoId));
      });

      console.log('📋 OPs relacionadas encontradas:', opsRelacionadas.map(op => ({
        numero: op.numero,
        produtoId: op.produtoId,
        produtoPaiId: op.produtoPaiId,
        nivel: op.nivel,
        status: op.status
      })));

      // Verificar necessidades que deveriam aparecer
      const necessidadesRelacionadas = necessidades.filter(n => {
        if (n.produto.tipo !== 'MP') return false;
        return (n.ordens || []).some(ordemNumero =>
          opsRelacionadas.some(op => op.numero === ordemNumero)
        );
      });

      console.log('🎯 Necessidades que deveriam aparecer:', necessidadesRelacionadas.map(n => ({
        codigo: n.produto.codigo,
        descricao: n.produto.descricao,
        ordens: Array.from(n.ordens || [])
      })));

      return {
        produtosPaiSelecionados: Array.from(produtosPaiSelecionados),
        opsRelacionadas,
        necessidadesRelacionadas
      };
    };

    // Função para limpar todos os filtros
    window.limparTodosFiltros = function() {
      // Limpar seleções de produtos pai
      produtosPaiSelecionados.clear();
      atualizarProdutosPaiSelecionados();

      // Limpar filtros dos selects
      document.getElementById('filterGrupo').value = '';
      document.getElementById('filterFamilia').value = '';
      document.getElementById('filterCriticidade').value = '';
      document.getElementById('filterStatusSolicitacao').value = 'sem';
      document.getElementById('filterOrdemProducao').value = '';
      document.getElementById('searchProdutoPai').value = '';

      // Aplicar filtros (que agora estarão limpos)
      filtrarDados();

      showNotification('🔄 Todos os filtros foram limpos!', 'info');
    };

    // Chamar inicialmente para garantir que o botão esteja no estado correto
    atualizarBotaoFiltrarSelecionados();

    // Adicionar botão de atualização no topo do relatório
    const relatorioHeader = document.querySelector('.relatorio-header') || document.body;
    if (!document.getElementById('btnAtualizarDados')) {
      const btnAtualizar = document.createElement('button');
      btnAtualizar.id = 'btnAtualizarDados';
      btnAtualizar.className = 'btn-primary';
      btnAtualizar.style = 'margin: 16px 0 16px 16px; float: right;';
      btnAtualizar.innerHTML = '🔄 Atualizar Dados';
      btnAtualizar.onclick = async function() {
        btnAtualizar.disabled = true;
        btnAtualizar.textContent = 'Atualizando...';
        await reloadData();
        necessidades = await processarNecessidades(); // Recalcula necessidades após recarregar OPs
        await filtrarDados();
        await mostrarRelatorio();
        btnAtualizar.disabled = false;
        btnAtualizar.textContent = '🔄 Atualizar Dados';
      };
      relatorioHeader.prepend(btnAtualizar);
    }
  </script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
</body>
</html>