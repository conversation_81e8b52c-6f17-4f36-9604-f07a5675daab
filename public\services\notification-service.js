// Serviço para gerenciar notificações e alertas do sistema
import { db } from '../firebase-config.js';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs,
  Timestamp,
  orderBy,
  limit,
  updateDoc,
  doc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class NotificationService {
  static async createNotification(params) {
    const {
      tipo,
      titulo,
      mensagem,
      severidade = 'INFO', // INFO, AVISO, CRITICO
      destinatarios = [], // Lista de IDs de usuários
      documentoOrigem,
      modulo,
      documentoId,
      documentoTipo,
      acaoRequerida = false
    } = params;

    try {
      const notification = {
        tipo,
        titulo,
        mensagem,
        severidade,
        destinatarios,
        documentoOrigem,
        modulo,
        documentoId,
        documentoTipo,
        acaoRequerida,
        dataCriacao: Timestamp.now(),
        status: 'NAO_LIDA',
        dataLeitura: null,
        dataAcao: null
      };

      const notificationRef = await addDoc(collection(db, "notificacoes"), notification);
      
      // Se houver destinatários específicos, cria cópias individuais
      if (destinatarios.length > 0) {
        for (const usuarioId of destinatarios) {
          await addDoc(collection(db, "notificacoesUsuarios"), {
            notificacaoId: notificationRef.id,
            usuarioId,
            status: 'NAO_LIDA',
            dataCriacao: Timestamp.now()
          });
        }
      }

      return notificationRef.id;
    } catch (error) {
      console.error("Erro ao criar notificação:", error);
      throw error;
    }
  }

  static async getActiveAlerts() {
    try {
      const alertsQuery = query(
        collection(db, "alertas"),
        where("status", "==", "ATIVO"),
        orderBy("dataCriacao", "desc")
      );

      const alertsSnapshot = await getDocs(alertsQuery);
      return alertsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Erro ao buscar alertas:", error);
      throw error;
    }
  }

  static async getUserNotifications(usuarioId) {
    try {
      const notificacoesQuery = query(
        collection(db, "notificacoesUsuarios"),
        where("usuarioId", "==", usuarioId),
        where("status", "==", "NAO_LIDA"),
        orderBy("dataCriacao", "desc"),
        limit(50)
      );

      const notificacoesSnapshot = await getDocs(notificacoesQuery);
      const notificacoes = [];

      for (const doc of notificacoesSnapshot.docs) {
        const notificacaoUsuario = doc.data();
        const notificacaoRef = await getDocs(
          query(collection(db, "notificacoes"), 
          where("id", "==", notificacaoUsuario.notificacaoId))
        );

        if (notificacaoRef.docs.length > 0) {
          notificacoes.push({
            id: doc.id,
            ...notificacaoUsuario,
            detalhes: notificacaoRef.docs[0].data()
          });
        }
      }

      return notificacoes;
    } catch (error) {
      console.error("Erro ao buscar notificações do usuário:", error);
      throw error;
    }
  }

  static async markAsRead(notificacaoId, usuarioId) {
    try {
      const notificacoesQuery = query(
        collection(db, "notificacoesUsuarios"),
        where("notificacaoId", "==", notificacaoId),
        where("usuarioId", "==", usuarioId)
      );

      const notificacoesSnapshot = await getDocs(notificacoesQuery);
      
      for (const doc of notificacoesSnapshot.docs) {
        await updateDoc(doc.ref, {
          status: 'LIDA',
          dataLeitura: Timestamp.now()
        });
      }

      return true;
    } catch (error) {
      console.error("Erro ao marcar notificação como lida:", error);
      throw error;
    }
  }

  static async createSystemAlert(params) {
    const {
      tipo,
      severidade,
      mensagem,
      modulo,
      documentoId,
      documentoTipo,
      acaoRequerida = false
    } = params;

    try {
      const alert = {
        tipo,
        severidade,
        mensagem,
        modulo,
        documentoId,
        documentoTipo,
        acaoRequerida,
        status: 'ATIVO',
        dataCriacao: Timestamp.now(),
        dataResolucao: null
      };

      const alertRef = await addDoc(collection(db, "alertas"), alert);

      // Se for um alerta crítico, cria notificações para usuários relevantes
      if (severidade === 'CRITICO') {
        const usuarios = await this.getUsuariosResponsaveis(modulo);
        await this.createNotification({
          tipo: 'ALERTA_SISTEMA',
          titulo: `Alerta Crítico - ${tipo}`,
          mensagem,
          severidade: 'CRITICO',
          destinatarios: usuarios.map(u => u.id),
          modulo,
          documentoId,
          documentoTipo,
          acaoRequerida: true
        });
      }

      return alertRef.id;
    } catch (error) {
      console.error("Erro ao criar alerta do sistema:", error);
      throw error;
    }
  }

  static async resolveAlert(alertId, resolucao) {
    try {
      await updateDoc(doc(db, "alertas", alertId), {
        status: 'RESOLVIDO',
        dataResolucao: Timestamp.now(),
        resolucao
      });

      return true;
    } catch (error) {
      console.error("Erro ao resolver alerta:", error);
      throw error;
    }
  }

  static async getUsuariosResponsaveis(modulo) {
    try {
      const usuariosQuery = query(
        collection(db, "usuarios"),
        where("responsavelModulos", "array-contains", modulo)
      );

      const usuariosSnapshot = await getDocs(usuariosQuery);
      return usuariosSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Erro ao buscar usuários responsáveis:", error);
      throw error;
    }
  }

  // Método para registrar notificação de inconsistência
  static async registrarInconsistencia(dadosInconsistencia) {
    try {
      const notificacaoRef = await addDoc(collection(db, "notificacoesInconsistencia"), {
        tipo: dadosInconsistencia.tipo || 'ESTOQUE',
        nivel: dadosInconsistencia.nivel || 'AVISO',
        descricao: dadosInconsistencia.descricao,
        detalhes: dadosInconsistencia.detalhes || {},
        produtoId: dadosInconsistencia.produtoId,
        armazemId: dadosInconsistencia.armazemId,
        dataHora: Timestamp.now(),
        status: 'PENDENTE',
        resolvidoPor: null
      });

      // Enviar notificação por e-mail ou outro canal
      await this.enviarNotificacao(dadosInconsistencia);

      return notificacaoRef.id;
    } catch (error) {
      console.error("Erro ao registrar inconsistência:", error);
      throw error;
    }
  }

  // Método para enviar notificação
  static async enviarNotificacao(dadosInconsistencia) {
    // Implementação pode variar (e-mail, SMS, push notification)
    const destinatarios = await this.obterDestinatarios();
    
    const corpoNotificacao = `
      ALERTA DE INCONSISTÊNCIA DE ESTOQUE
      
      Tipo: ${dadosInconsistencia.tipo}
      Nível: ${dadosInconsistencia.nivel}
      Descrição: ${dadosInconsistencia.descricao}
      
      Detalhes Adicionais:
      ${JSON.stringify(dadosInconsistencia.detalhes, null, 2)}
    `;

    // Exemplo de envio de e-mail (necessário configurar serviço de e-mail)
    destinatarios.forEach(destinatario => {
      this.enviarEmail(destinatario.email, 'Inconsistência de Estoque', corpoNotificacao);
    });
  }

  // Método para obter destinatários de notificação
  static async obterDestinatarios() {
    try {
      const usuariosSnapshot = await db.collection('usuarios')
        .where('receberNotificacoesEstoque', '==', true)
        .get();
      
      return usuariosSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Erro ao obter destinatários:", error);
      return [];
    }
  }

  // Método de envio de e-mail (mock - substituir por serviço real)
  static async enviarEmail(email, assunto, corpo) {
    console.log(`Enviando e-mail para ${email}`);
    console.log(`Assunto: ${assunto}`);
    console.log(`Corpo: ${corpo}`);

    // Implementação real dependeria de serviço de e-mail
    // Exemplo com nodemailer ou serviço de terceiros
  }

  // Método para marcar inconsistência como resolvida
  static async marcarComoResolvido(notificacaoId, usuarioId) {
    try {
      await db.collection('notificacoesInconsistencia')
        .doc(notificacaoId)
        .update({
          status: 'RESOLVIDO',
          resolvidoPor: usuarioId,
          dataResolucao: Timestamp.now()
        });
    } catch (error) {
      console.error("Erro ao marcar inconsistência como resolvida:", error);
      throw error;
    }
  }

  // ===== NOTIFICAÇÕES PARA PROCESSO PÓS-APROVAÇÃO =====

  /**
   * Notificar aprovação de pedido
   */
  static async notifyPedidoAprovado(pedidoData, approvedBy) {
    try {
      await this.createNotification({
        tipo: 'PEDIDO_APROVADO',
        titulo: '✅ Pedido Aprovado',
        mensagem: `Pedido ${pedidoData.numero} foi aprovado por ${approvedBy}`,
        severidade: 'INFO',
        destinatarios: await this.getComprasTeam(),
        modulo: 'PEDIDOS_COMPRA',
        documentoId: pedidoData.id,
        documentoTipo: 'PEDIDO_COMPRA',
        acaoRequerida: false
      });

      // Agendar lembrete de entrega se houver data
      if (pedidoData.dataEntregaPrevista) {
        await this.scheduleDeliveryReminder(pedidoData);
      }

      console.log('🔔 Notificação de aprovação criada:', pedidoData.numero);
    } catch (error) {
      console.error('❌ Erro ao notificar aprovação:', error);
    }
  }

  /**
   * Notificar envio para fornecedor
   */
  static async notifyPedidoEnviado(pedidoData, emailDestino, sentBy) {
    try {
      await this.createNotification({
        tipo: 'PEDIDO_ENVIADO',
        titulo: '📧 Pedido Enviado ao Fornecedor',
        mensagem: `Pedido ${pedidoData.numero} foi enviado para ${emailDestino}`,
        severidade: 'INFO',
        destinatarios: await this.getComprasTeam(),
        modulo: 'PEDIDOS_COMPRA',
        documentoId: pedidoData.id,
        documentoTipo: 'PEDIDO_COMPRA',
        acaoRequerida: false
      });

      console.log('🔔 Notificação de envio criada:', pedidoData.numero);
    } catch (error) {
      console.error('❌ Erro ao notificar envio:', error);
    }
  }

  /**
   * Notificar atraso na entrega
   */
  static async notifyAtrasoEntrega(pedidoData, diasAtraso) {
    try {
      const severidade = diasAtraso > 7 ? 'CRITICO' : 'AVISO';

      await this.createNotification({
        tipo: 'ATRASO_ENTREGA',
        titulo: '⚠️ Atraso na Entrega',
        mensagem: `Pedido ${pedidoData.numero} está ${diasAtraso} dias em atraso`,
        severidade: severidade,
        destinatarios: await this.getComprasGestao(),
        modulo: 'PEDIDOS_COMPRA',
        documentoId: pedidoData.id,
        documentoTipo: 'PEDIDO_COMPRA',
        acaoRequerida: true
      });

      console.log('🔔 Notificação de atraso criada:', pedidoData.numero, diasAtraso, 'dias');
    } catch (error) {
      console.error('❌ Erro ao notificar atraso:', error);
    }
  }

  /**
   * Agendar lembrete de entrega
   */
  static async scheduleDeliveryReminder(pedidoData) {
    try {
      const dataEntrega = new Date(pedidoData.dataEntregaPrevista.seconds * 1000);
      const hoje = new Date();
      const diasRestantes = Math.ceil((dataEntrega - hoje) / (1000 * 60 * 60 * 24));

      // Lembrete 3 dias antes
      if (diasRestantes === 3) {
        await this.createNotification({
          tipo: 'LEMBRETE_ENTREGA',
          titulo: '📅 Lembrete de Entrega',
          mensagem: `Pedido ${pedidoData.numero} deve ser entregue em 3 dias`,
          severidade: 'AVISO',
          destinatarios: await this.getComprasTeam(),
          modulo: 'PEDIDOS_COMPRA',
          documentoId: pedidoData.id,
          documentoTipo: 'PEDIDO_COMPRA',
          acaoRequerida: false
        });

        console.log('🔔 Lembrete de entrega agendado:', pedidoData.numero);
      }
    } catch (error) {
      console.error('❌ Erro ao agendar lembrete:', error);
    }
  }

  /**
   * Verificar pedidos em atraso
   */
  static async checkDelayedOrders() {
    try {
      console.log('🔍 Verificando pedidos em atraso...');

      const hoje = new Date();
      const q = query(
        collection(db, "pedidosCompra"),
        where("status", "in", ["APROVADO", "ENVIADO"]),
        orderBy("dataEntregaPrevista", "asc")
      );

      const querySnapshot = await getDocs(q);
      let pedidosVerificados = 0;
      let notificacoesEnviadas = 0;

      for (const doc of querySnapshot.docs) {
        const pedido = { id: doc.id, ...doc.data() };
        pedidosVerificados++;

        if (pedido.dataEntregaPrevista) {
          const dataEntrega = new Date(pedido.dataEntregaPrevista.seconds * 1000);
          const diffTime = hoje - dataEntrega;
          const diasAtraso = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          if (diasAtraso > 0) {
            // Verificar se já foi notificado hoje
            const jaNotificado = await this.wasNotifiedToday(pedido.id, 'ATRASO_ENTREGA');

            if (!jaNotificado) {
              await this.notifyAtrasoEntrega(pedido, diasAtraso);
              notificacoesEnviadas++;
            }
          }
        }
      }

      console.log(`✅ Verificação concluída: ${pedidosVerificados} pedidos verificados, ${notificacoesEnviadas} notificações enviadas`);
    } catch (error) {
      console.error('❌ Erro ao verificar pedidos em atraso:', error);
    }
  }

  /**
   * Verificar se já foi notificado hoje
   */
  static async wasNotifiedToday(pedidoId, tipo) {
    try {
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);

      const q = query(
        collection(db, "notificacoes"),
        where("documentoId", "==", pedidoId),
        where("tipo", "==", tipo),
        where("dataCriacao", ">=", Timestamp.fromDate(hoje)),
        limit(1)
      );

      const querySnapshot = await getDocs(q);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('❌ Erro ao verificar notificação:', error);
      return false;
    }
  }

  /**
   * Obter equipe de compras
   */
  static async getComprasTeam() {
    try {
      // TODO: Implementar busca real de usuários
      return ['<EMAIL>'];
    } catch (error) {
      console.error('❌ Erro ao obter equipe de compras:', error);
      return [];
    }
  }

  /**
   * Obter gestão de compras
   */
  static async getComprasGestao() {
    try {
      // TODO: Implementar busca real de usuários
      return ['<EMAIL>', '<EMAIL>'];
    } catch (error) {
      console.error('❌ Erro ao obter gestão de compras:', error);
      return [];
    }
  }

  /**
   * Inicializar verificação automática
   */
  static initializeDelayCheck() {
    try {
      // Verificar a cada 4 horas
      setInterval(() => {
        this.checkDelayedOrders();
      }, 4 * 60 * 60 * 1000);

      // Verificar imediatamente
      this.checkDelayedOrders();

      console.log('🔔 Sistema de notificações inicializado');
    } catch (error) {
      console.error('❌ Erro ao inicializar sistema de notificações:', error);
    }
  }
}