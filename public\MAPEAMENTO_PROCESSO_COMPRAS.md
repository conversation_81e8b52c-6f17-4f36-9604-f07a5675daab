# 🗺️ **MAPEAMENTO COMPLETO DO PROCESSO DE COMPRAS - WIZAR ERP**

## 📊 **VISÃO GERAL DO FLUXO**

```mermaid
graph TD
    A[solicitacao_compras_melhorada.html] --> B[cotacoes.html]
    B --> C[resposta_cotacao.html]
    B --> D[pedidos_compra.html]
    A --> E[gestao_compras_integrada.html]
    D --> E
    F[index.html] --> A
    F --> B
    F --> D
    F --> E
    
    subgraph "Coleções Firebase"
        G[solicitacoesCompra]
        H[cotacoes]
        I[pedidosCompra]
        J[fornecedores]
        K[produtos]
        L[centrosCusto]
    end
    
    A --> G
    B --> H
    D --> I
    A --> J
    B --> J
    D --> J
```

---

## 🔗 **ARQUIVOS PRINCIPAIS E SUAS CONEXÕES**

### **1. 📋 SOLICITACAO_COMPRAS_MELHORADA.HTML**
**Função:** Ponto de entrada do processo - criação e gestão de solicitações

#### **🔗 Conexões de Saída:**
- **➡️ cotacoes_melhorada.html** - Gera cotações a partir de solicitações aprovadas
- **➡️ pedidos_compra.html** - Links de rastreabilidade para pedidos gerados
- **➡️ cadastro_centro_custo.html** - Gerenciamento de centros de custo
- **➡️ cadastro_setores.html** - Gerenciamento de setores
- **➡️ index.html** - Retorno ao menu principal

#### **📊 Coleções Firebase Utilizadas:**
- **solicitacoesCompra** (CRUD completo)
- **centrosCusto** (leitura)
- **setores** (leitura)
- **fornecedores** (leitura)
- **produtos** (leitura)
- **contadores** (cotacoes - para gerar numeração)

#### **🔄 Status de Integração:**
- **EM_COTACAO** - Quando gera cotação
- **FINALIZADA** - Quando vira pedido de compra
- **Links bidirecionais** com cotações e pedidos

---

### **2. 📋 COTACOES.HTML**
**Função:** Gestão de cotações e envio para fornecedores

#### **🔗 Conexões de Entrada:**
- **⬅️ solicitacao_compras_melhorada.html** - Recebe solicitações aprovadas

#### **🔗 Conexões de Saída:**
- **➡️ resposta_cotacao.html** - Gera links únicos para fornecedores
- **➡️ pedidos_compra.html** - Gera pedidos a partir de cotações aprovadas
- **➡️ solicitacoes.html** - Cria novas solicitações (MRP/Manual)
- **➡️ index.html** - Retorno ao menu principal

#### **📊 Coleções Firebase Utilizadas:**
- **cotacoes** (CRUD completo)
- **solicitacoesCompra** (leitura e atualização de status)
- **fornecedores** (leitura)
- **pedidosCompra** (criação)
- **emails** (criação para envio)
- **log_envios** (auditoria)
- **log_cotacoes** (auditoria)
- **notificacoes** (comunicação)
- **mrp** (para reprocessamento)

#### **🔄 Fluxo de Status:**
- **ABERTA** → **ENVIADA** → **APROVADA** → **FECHADA**
- **Integração bidirecional** com solicitações e pedidos

---

### **3. 📝 RESPOSTA_COTACAO.HTML**
**Função:** Interface externa para fornecedores responderem cotações

#### **🔗 Conexões de Entrada:**
- **⬅️ cotacoes.html** - Recebe links únicos por fornecedor

#### **🔗 Conexões de Saída:**
- **Nenhuma navegação** - Interface isolada para fornecedores

#### **📊 Coleções Firebase Utilizadas:**
- **cotacoes** (leitura e atualização de respostas)
- **fornecedores** (leitura)
- **produtos** (leitura para conversões)
- **empresa** (dados da empresa)

#### **🔄 Processo:**
- **Validação de link** único por fornecedor
- **Preenchimento de preços** e condições
- **Atualização automática** da cotação principal

---

### **4. 📦 PEDIDOS_COMPRA.HTML**
**Função:** Gestão de pedidos de compra e acompanhamento

#### **🔗 Conexões de Entrada:**
- **⬅️ cotacoes.html** - Recebe pedidos gerados de cotações
- **⬅️ solicitacao_compras_melhorada.html** - Links de rastreabilidade

#### **🔗 Conexões de Saída:**
- **➡️ gestao_compras_integrada.html** - Aprovações e gestão integrada
- **➡️ cadastro_centro_custo.html** - Gerenciamento de centros de custo
- **➡️ index.html** - Retorno ao menu principal

#### **📊 Coleções Firebase Utilizadas:**
- **pedidosCompra** (CRUD completo)
- **cotacoes** (leitura para criação de pedidos)
- **fornecedores** (leitura)
- **produtos** (leitura)
- **centrosCusto** (leitura)
- **setores** (leitura)
- **condicoesPagamento** (leitura)

#### **🔄 Fluxo de Status:**
- **PENDENTE** → **APROVADO** → **ENVIADO** → **RECEBIDO**

---

### **5. 🎛️ GESTAO_COMPRAS_INTEGRADA.HTML**
**Função:** Dashboard central para aprovações e gestão

#### **🔗 Conexões de Entrada:**
- **⬅️ pedidos_compra.html** - Aprovações de pedidos
- **⬅️ index.html** - Acesso direto do menu

#### **🔗 Conexões de Saída:**
- **➡️ cotacoes/index.html** - Análise de cotações
- **➡️ pedidos_compra.html** - Acompanhamento de pedidos
- **➡️ recebimento_materiais_melhorado.html** - Recebimento

#### **📊 Coleções Firebase Utilizadas:**
- **Todas as coleções** do processo de compras
- **Dashboard integrado** com visão completa

---

### **6. 🏠 INDEX.HTML**
**Função:** Menu principal e navegação

#### **🔗 Conexões de Saída:**
- **➡️ solicitacao_compras_melhorada.html** - Módulo de solicitações
- **➡️ cotacoes/index.html** - Módulo de cotações
- **➡️ pedidos_compra.html** - Módulo de pedidos
- **➡️ gestao_compras.html** - Gestão de compras

---

## 📊 **COLEÇÕES FIREBASE E RELACIONAMENTOS**

### **🗃️ Coleções Principais:**
1. **solicitacoesCompra** - Solicitações de compra
2. **cotacoes** - Cotações de preços
3. **pedidosCompra** - Pedidos de compra
4. **fornecedores** - Base de fornecedores
5. **produtos** - Catálogo de produtos

### **🗃️ Coleções de Apoio:**
6. **centrosCusto** - Centros de custo
7. **setores** - Setores da empresa
8. **condicoesPagamento** - Condições de pagamento
9. **empresa** - Dados da empresa

### **🗃️ Coleções de Controle:**
10. **contadores** - Numeração sequencial
11. **emails** - Fila de emails
12. **log_envios** - Log de envios
13. **log_cotacoes** - Log de cotações
14. **notificacoes** - Notificações do sistema
15. **mrp** - Dados do MRP

---

## 🔄 **FLUXO COMPLETO DO PROCESSO**

### **📋 Etapa 1: Solicitação**
1. **Usuário acessa** `solicitacao_compras_melhorada.html`
2. **Cria solicitação** → salva em `solicitacoesCompra`
3. **Aprovação** → status muda para `APROVADA`

### **📋 Etapa 2: Cotação**
1. **Gera cotação** → cria documento em `cotacoes`
2. **Atualiza solicitação** → status `EM_COTACAO`
3. **Envia para fornecedores** → cria `emails`
4. **Fornecedores respondem** via `resposta_cotacao.html`

### **📋 Etapa 3: Pedido**
1. **Aprova cotação** → status `APROVADA`
2. **Gera pedido** → cria documento em `pedidosCompra`
3. **Atualiza cotação** → status `FECHADA`
4. **Atualiza solicitação** → status `FINALIZADA`

### **📋 Etapa 4: Gestão**
1. **Aprovação de pedidos** via `gestao_compras_integrada.html`
2. **Acompanhamento** via `pedidos_compra.html`
3. **Recebimento** via módulos específicos

---

## 🎯 **PONTOS DE INTEGRAÇÃO CRÍTICOS**

### **🔗 Rastreabilidade Bidirecional:**
- **Solicitação ↔ Cotação** via `cotacaoId` e `solicitacaoId`
- **Cotação ↔ Pedido** via `cotacaoId` e `pedidoId`
- **Links visuais** nas interfaces para navegação

### **🔄 Sincronização de Status:**
- **Mudanças automáticas** de status entre documentos
- **Notificações** para usuários relevantes
- **Logs de auditoria** para rastreamento

### **📊 Dados Compartilhados:**
- **Fornecedores** utilizados em todos os módulos
- **Produtos** com conversões de unidades
- **Centros de custo** para controle orçamentário

---

## ✅ **CONCLUSÃO**

O processo de compras está **totalmente integrado** com:
- **5 arquivos principais** interconectados
- **15+ coleções Firebase** relacionadas
- **Fluxo bidirecional** de dados
- **Rastreabilidade completa** do processo
- **Interfaces específicas** para cada etapa

**Todos os arquivos trabalham em conjunto** para formar um sistema completo e robusto de gestão de compras.
