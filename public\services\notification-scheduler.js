// Serviço para agendar e enviar notificações automáticas
import { db } from '../firebase-config.js';
import { 
  collection, 
  query, 
  where, 
  getDocs,
  Timestamp,
  addDoc,
  writeBatch,
  doc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { NotificationService } from './notification-service.js';

export class NotificationScheduler {
  // Verifica faturas próximas do vencimento
  static async checkInvoiceDueDates() {
    try {
      const hoje = new Date();
      const em3Dias = new Date();
      em3Dias.setDate(hoje.getDate() + 3);

      const faturasQuery = query(
        collection(db, "faturas"),
        where("status", "==", "PENDENTE"),
        where("dataVencimento", "<=", Timestamp.fromDate(em3Dias))
      );

      const faturasSnapshot = await getDocs(faturasQuery);

      for (const faturaDoc of faturasSnapshot.docs) {
        const fatura = faturaDoc.data();
        
        // Verifica se já foi notificado hoje
        if (fatura.ultimaNotificacao?.toDate().toDateString() === hoje.toDateString()) {
          continue;
        }

        // Calcula dias até vencimento
        const diasAteVencimento = Math.ceil(
          (fatura.dataVencimento.toDate() - hoje) / (1000 * 60 * 60 * 24)
        );

        // Cria notificação no sistema
        await NotificationService.createNotification({
          tipo: 'FATURA_PROXIMA_VENCIMENTO',
          titulo: `Fatura ${fatura.numero} vence em ${diasAteVencimento} dias`,
          mensagem: `A fatura no valor de R$ ${fatura.valor.toFixed(2)} vencerá em ${diasAteVencimento} dias`,
          destinatarios: ['FINANCEIRO', fatura.clienteId],
          prioridade: diasAteVencimento <= 1 ? 'ALTA' : 'NORMAL',
          dados: {
            faturaId: faturaDoc.id,
            clienteId: fatura.clienteId,
            valor: fatura.valor,
            vencimento: fatura.dataVencimento
          }
        });

        // Envia e-mail se cliente tiver e-mail cadastrado
        if (fatura.cliente?.email) {
          await addDoc(collection(db, "filaEmails"), {
            para: fatura.cliente.email,
            assunto: `Lembrete de Vencimento - Fatura ${fatura.numero}`,
            corpo: this.generateDueDateEmailBody(fatura, diasAteVencimento),
            status: 'PENDENTE',
            dataEnvio: Timestamp.now()
          });
        }

        // Envia SMS se cliente tiver celular cadastrado
        if (fatura.cliente?.celular) {
          await addDoc(collection(db, "filaSMS"), {
            numero: fatura.cliente.celular,
            mensagem: `Sua fatura ${fatura.numero} vence em ${diasAteVencimento} dias. Valor: R$ ${fatura.valor.toFixed(2)}`,
            status: 'PENDENTE',
            dataEnvio: Timestamp.now()
          });
        }
      }
    } catch (error) {
      console.error('Erro ao verificar vencimentos:', error);
      throw error;
    }
  }

  // Verifica faturas vencidas
  static async checkOverdueInvoices() {
    try {
      const hoje = new Date();
      
      const faturasQuery = query(
        collection(db, "faturas"),
        where("status", "==", "PENDENTE"),
        where("dataVencimento", "<", Timestamp.fromDate(hoje))
      );

      const faturasSnapshot = await getDocs(faturasQuery);

      for (const faturaDoc of faturasSnapshot.docs) {
        const fatura = faturaDoc.data();
        
        // Verifica se já foi notificado hoje
        if (fatura.ultimaNotificacaoAtraso?.toDate().toDateString() === hoje.toDateString()) {
          continue;
        }

        // Calcula dias de atraso
        const diasAtraso = Math.floor(
          (hoje - fatura.dataVencimento.toDate()) / (1000 * 60 * 60 * 24)
        );

        // Cria notificação no sistema
        await NotificationService.createNotification({
          tipo: 'FATURA_VENCIDA',
          titulo: `Fatura ${fatura.numero} vencida há ${diasAtraso} dias`,
          mensagem: `A fatura no valor de R$ ${fatura.valor.toFixed(2)} está vencida há ${diasAtraso} dias`,
          destinatarios: ['FINANCEIRO', 'COBRANCA', fatura.clienteId],
          prioridade: 'ALTA',
          dados: {
            faturaId: faturaDoc.id,
            clienteId: fatura.clienteId,
            valor: fatura.valor,
            vencimento: fatura.dataVencimento,
            diasAtraso
          }
        });

        // Envia e-mail se cliente tiver e-mail cadastrado
        if (fatura.cliente?.email) {
          await addDoc(collection(db, "filaEmails"), {
            para: fatura.cliente.email,
            assunto: `Fatura ${fatura.numero} Vencida`,
            corpo: this.generateOverdueEmailBody(fatura, diasAtraso),
            status: 'PENDENTE',
            dataEnvio: Timestamp.now()
          });
        }

        // Envia SMS se cliente tiver celular cadastrado
        if (fatura.cliente?.celular) {
          await addDoc(collection(db, "filaSMS"), {
            numero: fatura.cliente.celular,
            mensagem: `Sua fatura ${fatura.numero} está vencida há ${diasAtraso} dias. Entre em contato conosco.`,
            status: 'PENDENTE',
            dataEnvio: Timestamp.now()
          });
        }
      }
    } catch (error) {
      console.error('Erro ao verificar faturas vencidas:', error);
      throw error;
    }
  }

  // Verifica faturas emitidas
  static async checkNewInvoices() {
    try {
      const hoje = Timestamp.now();
      const ontemTimestamp = new Timestamp(
        hoje.seconds - (24 * 60 * 60),
        hoje.nanoseconds
      );

      // Busca faturas emitidas nas últimas 24h que ainda não foram notificadas
      const faturasQuery = query(
        collection(db, "faturas"),
        where("dataEmissao", ">=", ontemTimestamp),
        where("notificacaoEnviada", "==", false)
      );

      const faturasSnapshot = await getDocs(faturasQuery);
      const batch = writeBatch(db);

      for (const doc of faturasSnapshot.docs) {
        const fatura = { id: doc.id, ...doc.data() };
        
        // Cria notificação
        const notificacaoRef = doc(collection(db, "notificacoes"));
        batch.set(notificacaoRef, {
          tipo: 'FATURA_EMITIDA',
          titulo: `Nova Fatura Emitida - ${fatura.numero}`,
          mensagem: `Foi emitida uma nova fatura no valor de R$ ${fatura.valor.toFixed(2)}`,
          destinatarios: fatura.destinatariosNotificacao || [],
          dataEnvio: Timestamp.now(),
          lida: false,
          fatura: {
            id: fatura.id,
            numero: fatura.numero,
            valor: fatura.valor
          }
        });

        // Marca fatura como notificada
        const faturaRef = doc(db, "faturas", fatura.id);
        batch.update(faturaRef, {
          notificacaoEnviada: true,
          dataNotificacao: Timestamp.now()
        });
      }

      await batch.commit();
    } catch (error) {
      console.error('Erro ao verificar novas faturas:', error);
      // Não propaga o erro para não interromper outras tarefas
    }
  }

  // Templates de e-mail
  static generateDueDateEmailBody(fatura, diasAteVencimento) {
    return `
      <h2>Lembrete de Vencimento - Fatura ${fatura.numero}</h2>
      <p>Prezado cliente,</p>
      <p>Sua fatura vencerá em ${diasAteVencimento} dias. Confira os detalhes:</p>
      <ul>
        <li>Número: ${fatura.numero}</li>
        <li>Valor: R$ ${fatura.valor.toFixed(2)}</li>
        <li>Vencimento: ${fatura.dataVencimento.toDate().toLocaleDateString()}</li>
      </ul>
      <p>Para sua comodidade, você pode efetuar o pagamento através de:</p>
      <ul>
        <li>PIX</li>
        <li>Boleto Bancário</li>
        <li>Cartão de Crédito</li>
      </ul>
      <p>Em caso de dúvidas, entre em contato conosco.</p>
    `;
  }

  static generateOverdueEmailBody(fatura, diasAtraso) {
    return `
      <h2>Fatura ${fatura.numero} Vencida</h2>
      <p>Prezado cliente,</p>
      <p>Identificamos que sua fatura está vencida há ${diasAtraso} dias. Confira os detalhes:</p>
      <ul>
        <li>Número: ${fatura.numero}</li>
        <li>Valor: R$ ${fatura.valor.toFixed(2)}</li>
        <li>Vencimento: ${fatura.dataVencimento.toDate().toLocaleDateString()}</li>
      </ul>
      <p>Para regularizar sua situação, entre em contato com nosso setor financeiro:</p>
      <ul>
        <li>Telefone: (XX) XXXX-XXXX</li>
        <li>E-mail: <EMAIL></li>
      </ul>
      <p>Evite a suspensão de serviços e negativação.</p>
    `;
  }

  static generateNewInvoiceEmailBody(fatura) {
    return `
      <h2>Nova Fatura Emitida - ${fatura.numero}</h2>
      <p>Prezado cliente,</p>
      <p>Uma nova fatura foi emitida para sua empresa. Confira os detalhes:</p>
      <ul>
        <li>Número: ${fatura.numero}</li>
        <li>Valor: R$ ${fatura.valor.toFixed(2)}</li>
        <li>Vencimento: ${fatura.dataVencimento.toDate().toLocaleDateString()}</li>
      </ul>
      <p>A fatura está anexada a este e-mail em formato PDF.</p>
      <p>Para sua comodidade, você pode efetuar o pagamento através de:</p>
      <ul>
        <li>PIX</li>
        <li>Boleto Bancário</li>
        <li>Cartão de Crédito</li>
      </ul>
      <p>Em caso de dúvidas, entre em contato conosco.</p>
    `;
  }
} 