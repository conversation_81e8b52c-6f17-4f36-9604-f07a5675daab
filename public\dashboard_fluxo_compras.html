<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard - Fluxo de Materiais</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .dashboard-container {
            max-width: 1800px;
            margin: 20px auto;
            padding: 20px;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .filters-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-3px);
        }
        .stat-number {
            font-size: 2.2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }
        .flow-matrix {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1400px;
        }
        .matrix-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 13px;
        }
        .matrix-table td {
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
            position: relative;
            min-width: 100px;
            height: 70px;
            vertical-align: middle;
        }
        .product-header {
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            font-weight: bold;
            text-align: left;
            padding: 12px 15px;
            position: sticky;
            left: 0;
            z-index: 5;
            min-width: 250px;
            max-width: 250px;
            border-right: 2px solid #007bff;
        }
        .stage-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 100px;
            font-weight: bold;
            color: #495057;
        }
        
        /* Status Colors - Fluxo de Compras */
        .status-solicitado { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
            color: #856404; 
            border-left: 4px solid #ffc107;
        }
        .status-cotacao { 
            background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%); 
            color: #004085; 
            border-left: 4px solid #007bff;
        }
        .status-pedido { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); 
            color: #155724; 
            border-left: 4px solid #28a745;
        }
        .status-qualidade { 
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%); 
            color: #6c5ce7; 
            border-left: 4px solid #e17055;
        }
        .status-estoque { 
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); 
            color: white; 
            border-left: 4px solid #0984e3;
        }
        .status-parado { 
            background: linear-gradient(135deg, #ff7675 0%, #e84393 100%); 
            color: white; 
            border-left: 4px solid #e84393;
            animation: pulse-red 2s infinite;
        }
        
        @keyframes pulse-red {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .material-cell {
            border-radius: 8px;
            margin: 3px;
            padding: 8px;
            font-size: 11px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 55px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        .material-cell:hover {
            transform: scale(1.05);
            z-index: 100;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .quantity-badge {
            font-size: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 2px 6px;
            margin-top: 3px;
        }
        .days-badge {
            font-size: 9px;
            background: rgba(255,255,255,0.8);
            color: #333;
            border-radius: 8px;
            padding: 1px 4px;
            margin-top: 2px;
        }
        .btn-refresh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        .btn-refresh:hover {
            transform: translateY(-2px);
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-size: 18px;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        .legend-color {
            width: 25px;
            height: 25px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .alert-section {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 2px solid #e53e3e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
        }
        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 900px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 32px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        .close:hover {
            color: #333;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .timeline-date {
            min-width: 120px;
            font-weight: bold;
            color: #6c757d;
            font-size: 12px;
        }
        .timeline-content {
            flex: 1;
            margin-left: 20px;
        }
        .timeline-stage {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>📊 Dashboard de Fluxo de Compras</h1>
            <p>Acompanhe onde seus materiais estão parados no processo de compra</p>
            <p><strong>Fluxo:</strong> Solicitação → Cotação → Pedido → Qualidade → Estoque</p>
        </div>

        <!-- Filtros -->
        <div class="filters-section">
            <h3>🎛️ Filtros e Controles</h3>
            <div class="filters-grid">
                <div>
                    <label><strong>Período:</strong></label>
                    <select id="periodoFiltro">
                        <option value="15">Últimos 15 dias</option>
                        <option value="30" selected>Últimos 30 dias</option>
                        <option value="60">Últimos 60 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="180">Últimos 6 meses</option>
                    </select>
                </div>
                <div>
                    <label><strong>Produto:</strong></label>
                    <input type="text" id="produtoFiltro" placeholder="Filtrar por código/nome...">
                </div>
                <div>
                    <label><strong>Status:</strong></label>
                    <select id="statusFiltro">
                        <option value="all">Todos os status</option>
                        <option value="parados">Apenas Parados (>7 dias)</option>
                        <option value="solicitado">Solicitados</option>
                        <option value="cotacao">Em Cotação</option>
                        <option value="pedido">Pedidos Feitos</option>
                        <option value="qualidade">Em Qualidade</option>
                    </select>
                </div>
                <div>
                    <label><strong>Fornecedor:</strong></label>
                    <input type="text" id="fornecedorFiltro" placeholder="Filtrar por fornecedor...">
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="carregarDashboard()" class="btn-refresh">🔄 Atualizar Dashboard</button>
                <button onclick="exportarRelatorio()" class="btn btn-secondary">📊 Exportar Relatório</button>
                <button onclick="mostrarAlertas()" class="btn btn-warning">⚠️ Materiais Parados</button>
            </div>
        </div>

        <!-- Estatísticas -->
        <div id="statsSection">
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <!-- Alertas de Materiais Parados -->
        <div id="alertsSection" style="display: none;"></div>

        <!-- Legenda -->
        <div class="legend" id="legendaStatus">
            <div class="legend-item">
                <div class="legend-color status-solicitado"></div>
                <span>Solicitado</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-cotacao"></div>
                <span>Em Cotação</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-pedido"></div>
                <span>Pedido Feito</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-qualidade"></div>
                <span>Em Qualidade</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-estoque"></div>
                <span>Em Estoque</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-parado"></div>
                <span>Parado (>7 dias)</span>
            </div>
        </div>

        <!-- Matriz de Fluxo -->
        <div class="flow-matrix">
            <h3>🗺️ Mapa de Fluxo - Onde estão seus materiais?</h3>
            <div id="matrixContainer" class="loading">
                <p>Clique em "🔄 Atualizar Dashboard" para carregar os dados...</p>
            </div>
        </div>
    </div>

    <!-- Modal para detalhes -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="fecharModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let dashboardData = {};
        let materiaisFluxo = [];

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
        };

        window.carregarDashboard = async function() {
            const container = document.getElementById('matrixContainer');
            container.innerHTML = '<div class="loading"><p>🔄 Carregando dados do fluxo de compras...</p></div>';

            try {
                await carregarDadosCompras();
                processarFluxoCompras();
                mostrarEstatisticas();
                renderizarMatrizFluxo();

            } catch (error) {
                console.error('Erro ao carregar dashboard:', error);
                container.innerHTML = `<div style="color: #e53e3e; text-align: center; padding: 20px;">❌ Erro: ${error.message}</div>`;
            }
        };

        async function carregarDadosCompras() {
            console.log('Carregando dados das coleções...');

            // Carregar dados de todas as suas coleções
            const [
                solicitacoesSnap,
                cotacoesSnap,
                pedidosSnap,
                estoquesSnap,
                estoqueQualidadeSnap,
                movimentacoesSnap,
                transferenciasSnap,
                produtosSnap,
                armazensSnap
            ] = await Promise.all([
                getDocs(query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"), limit(200))),
                getDocs(query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"), limit(150))),
                getDocs(query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"), limit(150))),
                getDocs(collection(db, "estoques")),
                getDocs(collection(db, "estoqueQualidade")),
                getDocs(query(collection(db, "movimentacoesEstoque"), orderBy("dataHora", "desc"), limit(300))),
                getDocs(query(collection(db, "transferenciasArmazem"), orderBy("dataTransferencia", "desc"), limit(100))),
                getDocs(collection(db, "produtos")),
                getDocs(collection(db, "armazens"))
            ]);

            dashboardData = {
                solicitacoes: solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                cotacoes: cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                pedidos: pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                estoques: estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                estoqueQualidade: estoqueQualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                movimentacoes: movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                transferencias: transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                produtos: produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                armazens: armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
            };

            console.log('Dados carregados:', {
                solicitacoes: dashboardData.solicitacoes.length,
                cotacoes: dashboardData.cotacoes.length,
                pedidos: dashboardData.pedidos.length,
                estoques: dashboardData.estoques.length,
                estoqueQualidade: dashboardData.estoqueQualidade.length,
                movimentacoes: dashboardData.movimentacoes.length,
                transferencias: dashboardData.transferencias.length,
                produtos: dashboardData.produtos.length,
                armazens: dashboardData.armazens.length
            });
        }

        function processarFluxoCompras() {
            materiaisFluxo = [];
            const materiaisMap = new Map();

            // Aplicar filtros
            const periodo = parseInt(document.getElementById('periodoFiltro').value);
            const dataLimite = new Date();
            dataLimite.setDate(dataLimite.getDate() - periodo);
            const produtoFiltro = document.getElementById('produtoFiltro').value.toLowerCase();
            const statusFiltro = document.getElementById('statusFiltro').value;
            const fornecedorFiltro = document.getElementById('fornecedorFiltro').value.toLowerCase();

            console.log('Processando fluxo com filtros:', { periodo, produtoFiltro, statusFiltro, fornecedorFiltro });

            // 1. ETAPA: Processar Solicitações de Compra
            dashboardData.solicitacoes.forEach(sol => {
                const dataSol = sol.dataCriacao?.seconds ? new Date(sol.dataCriacao.seconds * 1000) : new Date();
                if (dataSol >= dataLimite) {

                    // Processar itens da solicitação
                    if (sol.itens && Array.isArray(sol.itens)) {
                        sol.itens.forEach(item => {
                            const produto = dashboardData.produtos.find(p =>
                                p.id === item.produtoId || p.codigo === item.codigo || p.codigo === item.produtoId
                            );

                            // Aplicar filtro de produto
                            if (produtoFiltro && produto) {
                                const textoFiltro = `${produto.codigo} ${produto.descricao}`.toLowerCase();
                                if (!textoFiltro.includes(produtoFiltro)) return;
                            }

                            const chave = `${item.produtoId || item.codigo}_${sol.id}`;
                            const agora = new Date();
                            const diasParado = Math.floor((agora - dataSol) / (1000 * 60 * 60 * 24));

                            materiaisMap.set(chave, {
                                produtoId: item.produtoId || item.codigo,
                                codigo: produto?.codigo || item.codigo || item.produtoId,
                                descricao: produto?.descricao || item.descricao || 'Sem descrição',
                                quantidade: item.quantidade || item.qtdSolicitada,
                                unidade: produto?.unidade || item.unidade || 'UN',
                                status: 'SOLICITADO',
                                etapa: 'Solicitação',
                                localizacao: 'SOLICITACAO',
                                solicitacaoId: sol.id,
                                dataInicio: sol.dataCriacao,
                                ultimaAtualizacao: sol.dataCriacao,
                                diasParado: diasParado,
                                parado: diasParado > 7,
                                prioridade: sol.prioridade || 'NORMAL',
                                solicitante: sol.solicitante || sol.solicitadoPor || 'Sistema',
                                observacoes: sol.justificativa || sol.observacoes,
                                historico: [{
                                    etapa: 'SOLICITADO',
                                    data: sol.dataCriacao,
                                    observacao: `Solicitado por ${sol.solicitante || sol.solicitadoPor || 'Sistema'} - Prioridade: ${sol.prioridade || 'NORMAL'}`
                                }]
                            });
                        });
                    }
                }
            });

            // 2. ETAPA: Atualizar com Cotações
            dashboardData.cotacoes.forEach(cot => {
                if (cot.itens && Array.isArray(cot.itens)) {
                    cot.itens.forEach(item => {
                        // Buscar material correspondente
                        const chaves = Array.from(materiaisMap.keys()).filter(k => {
                            const material = materiaisMap.get(k);
                            return material && (
                                material.produtoId === item.produtoId ||
                                material.codigo === item.codigo ||
                                material.produtoId === item.codigo
                            );
                        });

                        chaves.forEach(chave => {
                            const material = materiaisMap.get(chave);
                            if (material && material.status === 'SOLICITADO') {
                                // Aplicar filtro de fornecedor
                                if (fornecedorFiltro && cot.fornecedor) {
                                    if (!cot.fornecedor.toLowerCase().includes(fornecedorFiltro)) return;
                                }

                                const agora = new Date();
                                const dataCot = cot.dataCotacao?.seconds ? new Date(cot.dataCotacao.seconds * 1000) : new Date();
                                const diasParado = Math.floor((agora - dataCot) / (1000 * 60 * 60 * 24));

                                material.status = 'COTACAO';
                                material.etapa = 'Cotação';
                                material.localizacao = 'COTACAO';
                                material.cotacaoId = cot.id;
                                material.fornecedor = cot.fornecedor;
                                material.valorCotado = item.valorUnitario;
                                material.ultimaAtualizacao = cot.dataCotacao;
                                material.diasParado = diasParado;
                                material.parado = diasParado > 7;
                                material.historico.push({
                                    etapa: 'COTACAO',
                                    data: cot.dataCotacao,
                                    observacao: `Cotação recebida - ${cot.fornecedor} - Valor: ${item.valorUnitario || 'N/A'}`
                                });
                            }
                        });
                    });
                }
            });

            // 3. ETAPA: Atualizar com Pedidos de Compra
            dashboardData.pedidos.forEach(ped => {
                if (ped.itens && Array.isArray(ped.itens)) {
                    ped.itens.forEach(item => {
                        // Buscar material correspondente
                        const chaves = Array.from(materiaisMap.keys()).filter(k => {
                            const material = materiaisMap.get(k);
                            return material && (
                                material.produtoId === item.produtoId ||
                                material.codigo === item.codigo ||
                                material.produtoId === item.codigo
                            );
                        });

                        chaves.forEach(chave => {
                            const material = materiaisMap.get(chave);
                            if (material && ['SOLICITADO', 'COTACAO'].includes(material.status)) {
                                // Aplicar filtro de fornecedor
                                if (fornecedorFiltro && ped.fornecedor) {
                                    if (!ped.fornecedor.toLowerCase().includes(fornecedorFiltro)) return;
                                }

                                const agora = new Date();
                                const dataPed = ped.dataPedido?.seconds ? new Date(ped.dataPedido.seconds * 1000) : new Date();
                                const diasParado = Math.floor((agora - dataPed) / (1000 * 60 * 60 * 24));

                                material.status = 'PEDIDO';
                                material.etapa = 'Pedido Feito';
                                material.localizacao = 'PEDIDO';
                                material.pedidoId = ped.id;
                                material.fornecedor = ped.fornecedor;
                                material.numeroPedido = ped.numeroPedido;
                                material.valorPedido = item.valorUnitario;
                                material.prazoEntrega = ped.prazoEntrega;
                                material.ultimaAtualizacao = ped.dataPedido;
                                material.diasParado = diasParado;
                                material.parado = diasParado > 7;
                                material.historico.push({
                                    etapa: 'PEDIDO',
                                    data: ped.dataPedido,
                                    observacao: `Pedido feito - ${ped.fornecedor} - Nº: ${ped.numeroPedido || 'N/A'} - Prazo: ${ped.prazoEntrega || 'N/A'}`
                                });
                            }
                        });
                    });
                }
            });

            // 4. ETAPA: Atualizar com Estoque de Qualidade
            dashboardData.estoqueQualidade.forEach(qual => {
                // Buscar material correspondente
                const chaves = Array.from(materiaisMap.keys()).filter(k => {
                    const material = materiaisMap.get(k);
                    return material && (
                        material.produtoId === qual.produtoId ||
                        material.codigo === qual.codigo ||
                        material.produtoId === qual.codigo
                    );
                });

                chaves.forEach(chave => {
                    const material = materiaisMap.get(chave);
                    if (material && material.status === 'PEDIDO') {
                        const agora = new Date();
                        const dataQual = qual.dataEntrada?.seconds ? new Date(qual.dataEntrada.seconds * 1000) : new Date();
                        const diasParado = Math.floor((agora - dataQual) / (1000 * 60 * 60 * 24));

                        material.status = 'QUALIDADE';
                        material.etapa = 'Em Qualidade';
                        material.localizacao = 'QUALIDADE';
                        material.qualidadeId = qual.id;
                        material.statusQualidade = qual.status;
                        material.ultimaAtualizacao = qual.dataEntrada;
                        material.diasParado = diasParado;
                        material.parado = diasParado > 3; // Qualidade tem prazo menor
                        material.historico.push({
                            etapa: 'QUALIDADE',
                            data: qual.dataEntrada,
                            observacao: `Em qualidade - Status: ${qual.status || 'PENDENTE'}`
                        });
                    }
                });
            });

            // 5. ETAPA: Atualizar com Estoque Final
            dashboardData.estoques.forEach(est => {
                if (est.saldo > 0) {
                    // Buscar material correspondente
                    const chaves = Array.from(materiaisMap.keys()).filter(k => {
                        const material = materiaisMap.get(k);
                        return material && (
                            material.produtoId === est.produtoId ||
                            material.codigo === est.produtoId
                        );
                    });

                    chaves.forEach(chave => {
                        const material = materiaisMap.get(chave);
                        if (material && ['PEDIDO', 'QUALIDADE'].includes(material.status)) {
                            material.status = 'ESTOQUE';
                            material.etapa = 'Em Estoque';
                            material.localizacao = est.armazemId;
                            material.estoqueId = est.id;
                            material.saldoAtual = est.saldo;
                            material.armazemId = est.armazemId;
                            material.ultimaAtualizacao = est.ultimaMovimentacao;
                            material.diasParado = 0; // Chegou ao destino final
                            material.parado = false;
                            material.historico.push({
                                etapa: 'ESTOQUE',
                                data: est.ultimaMovimentacao,
                                observacao: `Disponível em estoque - Saldo: ${est.saldo}`
                            });
                        }
                    });
                }
            });

            // Aplicar filtro de status
            let materiaisFiltrados = Array.from(materiaisMap.values());

            if (statusFiltro !== 'all') {
                switch (statusFiltro) {
                    case 'parados':
                        materiaisFiltrados = materiaisFiltrados.filter(m => m.parado);
                        break;
                    case 'solicitado':
                        materiaisFiltrados = materiaisFiltrados.filter(m => m.status === 'SOLICITADO');
                        break;
                    case 'cotacao':
                        materiaisFiltrados = materiaisFiltrados.filter(m => m.status === 'COTACAO');
                        break;
                    case 'pedido':
                        materiaisFiltrados = materiaisFiltrados.filter(m => m.status === 'PEDIDO');
                        break;
                    case 'qualidade':
                        materiaisFiltrados = materiaisFiltrados.filter(m => m.status === 'QUALIDADE');
                        break;
                }
            }

            materiaisFluxo = materiaisFiltrados;
            console.log('Materiais processados:', materiaisFluxo.length);
        }

        function mostrarEstatisticas() {
            const stats = {
                total: materiaisFluxo.length,
                solicitados: materiaisFluxo.filter(m => m.status === 'SOLICITADO').length,
                cotacao: materiaisFluxo.filter(m => m.status === 'COTACAO').length,
                pedidos: materiaisFluxo.filter(m => m.status === 'PEDIDO').length,
                qualidade: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                estoque: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length,
                parados: materiaisFluxo.filter(m => m.parado).length,
                tempoMedio: calcularTempoMedio()
            };

            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total}</div>
                    <div>Total de Materiais</div>
                </div>
                <div class="stat-card" style="border-left-color: #ffc107;">
                    <div class="stat-number" style="color: #ffc107;">${stats.solicitados}</div>
                    <div>Solicitados</div>
                </div>
                <div class="stat-card" style="border-left-color: #007bff;">
                    <div class="stat-number" style="color: #007bff;">${stats.cotacao}</div>
                    <div>Em Cotação</div>
                </div>
                <div class="stat-card" style="border-left-color: #28a745;">
                    <div class="stat-number" style="color: #28a745;">${stats.pedidos}</div>
                    <div>Pedidos Feitos</div>
                </div>
                <div class="stat-card" style="border-left-color: #fd7e14;">
                    <div class="stat-number" style="color: #fd7e14;">${stats.qualidade}</div>
                    <div>Em Qualidade</div>
                </div>
                <div class="stat-card" style="border-left-color: #0984e3;">
                    <div class="stat-number" style="color: #0984e3;">${stats.estoque}</div>
                    <div>Em Estoque</div>
                </div>
                <div class="stat-card" style="border-left-color: #e84393;">
                    <div class="stat-number" style="color: #e84393;">${stats.parados}</div>
                    <div>Parados</div>
                </div>
                <div class="stat-card" style="border-left-color: #6c757d;">
                    <div class="stat-number" style="color: #6c757d;">${stats.tempoMedio}</div>
                    <div>Tempo Médio (dias)</div>
                </div>
            `;
        }

        function calcularTempoMedio() {
            if (materiaisFluxo.length === 0) return 0;
            const totalDias = materiaisFluxo.reduce((sum, m) => sum + (m.diasParado || 0), 0);
            return Math.round(totalDias / materiaisFluxo.length);
        }

        function renderizarMatrizFluxo() {
            const container = document.getElementById('matrixContainer');

            if (materiaisFluxo.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">Nenhum material encontrado no período selecionado com os filtros aplicados.</div>';
                return;
            }

            // Agrupar materiais por produto
            const produtosMap = new Map();
            materiaisFluxo.forEach(material => {
                const chave = material.produtoId;
                if (!produtosMap.has(chave)) {
                    produtosMap.set(chave, {
                        produtoId: chave,
                        codigo: material.codigo,
                        descricao: material.descricao,
                        materiais: []
                    });
                }
                produtosMap.get(chave).materiais.push(material);
            });

            // Definir colunas do fluxo
            const colunas = [
                { id: 'SOLICITACAO', nome: 'Solicitação', tipo: 'etapa' },
                { id: 'COTACAO', nome: 'Cotação', tipo: 'etapa' },
                { id: 'PEDIDO', nome: 'Pedido', tipo: 'etapa' },
                { id: 'QUALIDADE', nome: 'Qualidade', tipo: 'etapa' },
                { id: 'ESTOQUE', nome: 'Estoque', tipo: 'etapa' }
            ];

            // Construir tabela
            let html = `
                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th class="product-header">Produto</th>
            `;

            colunas.forEach(col => {
                html += `<th class="stage-header">${col.nome}</th>`;
            });

            html += `
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Renderizar linhas dos produtos
            Array.from(produtosMap.values()).forEach(produto => {
                html += `
                    <tr>
                        <td class="product-header">
                            <strong>${produto.codigo}</strong><br>
                            <small>${produto.descricao}</small><br>
                            <small style="color: #6c757d;">Total: ${produto.materiais.length} solicitação(ões)</small>
                        </td>
                `;

                colunas.forEach(coluna => {
                    const materiaisNaColuna = produto.materiais.filter(material => {
                        return material.localizacao === coluna.id;
                    });

                    html += '<td>';

                    if (materiaisNaColuna.length > 0) {
                        materiaisNaColuna.forEach(material => {
                            let statusClass = `status-${material.status.toLowerCase()}`;
                            if (material.parado) {
                                statusClass = 'status-parado';
                            }

                            html += `
                                <div class="material-cell ${statusClass}"
                                     onclick="mostrarDetalhes('${material.produtoId}', '${material.solicitacaoId}')"
                                     title="${material.etapa} - ${material.quantidade} ${material.unidade} - ${material.diasParado} dias">
                                    <div>${material.etapa}</div>
                                    <div class="quantity-badge">${material.quantidade} ${material.unidade}</div>
                                    <div class="days-badge">${material.diasParado}d</div>
                                </div>
                            `;
                        });
                    }

                    html += '</td>';
                });

                html += '</tr>';
            });

            html += `
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        window.mostrarDetalhes = function(produtoId, solicitacaoId) {
            const material = materiaisFluxo.find(m =>
                m.produtoId === produtoId && m.solicitacaoId === solicitacaoId
            );

            if (!material) {
                alert('Material não encontrado');
                return;
            }

            const modal = document.getElementById('detailModal');
            const modalContent = document.getElementById('modalContent');

            const armazem = dashboardData.armazens?.find(a => a.id === material.armazemId);

            let html = `
                <h3>📦 Detalhes do Material</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 25px 0;">
                    <div>
                        <h4>📋 Informações do Produto</h4>
                        <p><strong>Código:</strong> ${material.codigo}</p>
                        <p><strong>Descrição:</strong> ${material.descricao}</p>
                        <p><strong>Quantidade:</strong> ${material.quantidade} ${material.unidade}</p>
                        <p><strong>Status Atual:</strong> <span style="color: ${material.parado ? '#e84393' : '#28a745'};">${material.etapa}</span></p>
                        <p><strong>Dias no Status:</strong> <span style="color: ${material.parado ? '#e84393' : '#6c757d'};">${material.diasParado} dias ${material.parado ? '⚠️' : ''}</span></p>
                        <p><strong>Prioridade:</strong> ${material.prioridade || 'NORMAL'}</p>
                    </div>
                    <div>
                        <h4>🔍 Rastreamento</h4>
                        <p><strong>Solicitação:</strong> ${material.solicitacaoId}</p>
                        <p><strong>Solicitante:</strong> ${material.solicitante || 'N/A'}</p>
                        <p><strong>Cotação:</strong> ${material.cotacaoId || 'N/A'}</p>
                        <p><strong>Pedido:</strong> ${material.pedidoId || 'N/A'}</p>
                        <p><strong>Nº Pedido:</strong> ${material.numeroPedido || 'N/A'}</p>
                        <p><strong>Fornecedor:</strong> ${material.fornecedor || 'N/A'}</p>
                        <p><strong>Valor:</strong> ${material.valorPedido || material.valorCotado || 'N/A'}</p>
                        <p><strong>Prazo Entrega:</strong> ${material.prazoEntrega || 'N/A'}</p>
                    </div>
                </div>

                ${material.armazemId ? `
                <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4>📍 Localização Atual</h4>
                    <p><strong>Armazém:</strong> ${armazem?.codigo || material.armazemId} - ${armazem?.nome || 'Nome não encontrado'}</p>
                    <p><strong>Saldo Atual:</strong> ${material.saldoAtual || 'N/A'}</p>
                </div>
                ` : ''}

                ${material.observacoes ? `
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4>📝 Observações</h4>
                    <p>${material.observacoes}</p>
                </div>
                ` : ''}

                <h4>📅 Histórico Completo</h4>
                <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px;">
            `;

            material.historico.forEach(hist => {
                const data = hist.data ? new Date(hist.data.seconds * 1000).toLocaleString() : 'N/A';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-date">${data}</div>
                        <div class="timeline-content">
                            <div class="timeline-stage">${hist.etapa}</div>
                            <div>${hist.observacao}</div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            modalContent.innerHTML = html;
            modal.style.display = 'block';
        };

        window.fecharModal = function() {
            document.getElementById('detailModal').style.display = 'none';
        };

        window.mostrarAlertas = function() {
            const alertsSection = document.getElementById('alertsSection');
            const materiaisParados = materiaisFluxo.filter(m => m.parado);

            if (materiaisParados.length === 0) {
                alertsSection.innerHTML = `
                    <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>✅ Nenhum Material Parado!</h3>
                        <p>Todos os materiais estão dentro do prazo esperado.</p>
                    </div>
                `;
            } else {
                let html = `
                    <div class="alert-section">
                        <h3>⚠️ Materiais Parados - Ação Necessária!</h3>
                        <p><strong>${materiaisParados.length} materiais</strong> estão parados há mais de 7 dias (3 dias para qualidade).</p>
                        <div style="max-height: 400px; overflow-y: auto; margin: 15px 0;">
                `;

                materiaisParados.forEach(material => {
                    html += `
                        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #e84393;">
                            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 15px; align-items: center;">
                                <div>
                                    <strong>${material.codigo}</strong><br>
                                    <small>${material.descricao}</small>
                                </div>
                                <div>
                                    <strong>Status:</strong> ${material.etapa}<br>
                                    <strong>Parado:</strong> <span style="color: #e84393;">${material.diasParado} dias</span>
                                </div>
                                <div>
                                    <strong>Qtd:</strong> ${material.quantidade} ${material.unidade}<br>
                                    <strong>Fornecedor:</strong> ${material.fornecedor || 'N/A'}
                                </div>
                                <div>
                                    <button onclick="mostrarDetalhes('${material.produtoId}', '${material.solicitacaoId}')"
                                            style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                                        Ver Detalhes
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div></div>';
                alertsSection.innerHTML = html;
            }

            alertsSection.style.display = alertsSection.style.display === 'none' ? 'block' : 'none';
        };

        window.exportarRelatorio = function() {
            const dados = {
                dataExportacao: new Date().toISOString(),
                usuario: currentUser.nome,
                filtros: {
                    periodo: document.getElementById('periodoFiltro').value + ' dias',
                    produto: document.getElementById('produtoFiltro').value,
                    status: document.getElementById('statusFiltro').value,
                    fornecedor: document.getElementById('fornecedorFiltro').value
                },
                estatisticas: {
                    totalMateriais: materiaisFluxo.length,
                    materiaisParados: materiaisFluxo.filter(m => m.parado).length,
                    tempoMedio: calcularTempoMedio(),
                    porStatus: {
                        solicitados: materiaisFluxo.filter(m => m.status === 'SOLICITADO').length,
                        cotacao: materiaisFluxo.filter(m => m.status === 'COTACAO').length,
                        pedidos: materiaisFluxo.filter(m => m.status === 'PEDIDO').length,
                        qualidade: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                        estoque: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length
                    }
                },
                materiais: materiaisFluxo.map(m => ({
                    codigo: m.codigo,
                    descricao: m.descricao,
                    quantidade: m.quantidade,
                    unidade: m.unidade,
                    status: m.status,
                    etapa: m.etapa,
                    diasParado: m.diasParado,
                    parado: m.parado,
                    fornecedor: m.fornecedor,
                    valorPedido: m.valorPedido,
                    prazoEntrega: m.prazoEntrega,
                    solicitacaoId: m.solicitacaoId,
                    pedidoId: m.pedidoId,
                    ultimaAtualizacao: m.ultimaAtualizacao
                }))
            };

            const blob = new Blob([JSON.stringify(dados, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `relatorio_fluxo_compras_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('detailModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        };
    </script>
</body>
</html>
