<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ001 - Inspeção de Recebimento</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #3498db;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #3498db;
            margin-right: 10px;
            width: 20px;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .status-inspecao {
            background: #cce5ff;
            color: #004085;
        }

        .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            text-align: center;
        }

        .stat-card.pendente { border-left-color: #f39c12; }
        .stat-card.aprovado { border-left-color: #27ae60; }
        .stat-card.rejeitado { border-left-color: #e74c3c; }
        .stat-card.inspecao { border-left-color: #3498db; }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #27ae60;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #27ae60;
            box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2);
        }

        .inspecao-details {
            display: grid;
            gap: 20px;
        }

        .detail-section {
            padding: 15px;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
        }

        .detail-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .detail-grid div {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .timeline {
            display: grid;
            gap: 15px;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .timeline-item i {
            color: #27ae60;
            font-size: 1.2em;
            margin-top: 2px;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .filters {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .btn-action {
                width: 100%;
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .form-grid, .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 PQ001 - Inspeção de Recebimento</h1>
            <p>Controle de qualidade na entrada de materiais</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Garantir que todos os materiais recebidos atendam aos padrões de qualidade estabelecidos</li>
                    <li><i class="fas fa-cogs"></i><strong>Processo:</strong> Inspeção obrigatória de materiais antes da liberação para estoque</li>
                    <li><i class="fas fa-check-circle"></i><strong>Resultado:</strong> Aprovação ou rejeição com base em critérios pré-definidos</li>
                    <li><i class="fas fa-database"></i><strong>Integração:</strong> Conectado com recebimento de materiais e armazém de qualidade</li>
                </ul>
            </div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card pendente">
                    <div class="stat-value" id="statPendente">0</div>
                    <div class="stat-label">Pendentes</div>
                </div>
                <div class="stat-card inspecao">
                    <div class="stat-value" id="statInspecao">0</div>
                    <div class="stat-label">Em Inspeção</div>
                </div>
                <div class="stat-card aprovado">
                    <div class="stat-value" id="statAprovado">0</div>
                    <div class="stat-label">Aprovados</div>
                </div>
                <div class="stat-card rejeitado">
                    <div class="stat-value" id="statRejeitado">0</div>
                    <div class="stat-label">Rejeitados</div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter">
                        <option value="">Todos os Status</option>
                        <option value="PENDENTE">Pendente</option>
                        <option value="EM_INSPECAO">Em Inspeção</option>
                        <option value="APROVADO">Aprovado</option>
                        <option value="REJEITADO">Rejeitado</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="fornecedorFilter">Fornecedor</label>
                    <select id="fornecedorFilter">
                        <option value="">Todos os Fornecedores</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dataInicio">Data Início</label>
                    <input type="date" id="dataInicio">
                </div>
                <div class="filter-group">
                    <label for="dataFim">Data Fim</label>
                    <input type="date" id="dataFim">
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="novaInspecao()">
                    <i class="fas fa-plus"></i> Nova Inspeção
                </button>
                <button class="btn btn-primary" onclick="exportarRelatorio()">
                    <i class="fas fa-file-excel"></i> Exportar
                </button>
                <button class="btn btn-warning" onclick="sincronizarRecebimento()">
                    <i class="fas fa-sync"></i> Sincronizar Recebimento
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando inspeções...</p>
            </div>

            <!-- Tabela de Inspeções -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Produto</th>
                            <th>Lote</th>
                            <th>Quantidade</th>
                            <th>Fornecedor</th>
                            <th>Data Recebimento</th>
                            <th>Status</th>
                            <th>Inspetor</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-search"></i>
                <h3>Nenhuma inspeção encontrada</h3>
                <p>Não há inspeções de recebimento para exibir no momento.</p>
                <button class="btn btn-primary" onclick="novaInspecao()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Criar Primeira Inspeção
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let inspecoes = [];
        let fornecedores = [];

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);
                
                // Carregar inspeções e fornecedores
                const [inspecoesSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "inspecoesRecebimento")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                inspecoes = inspecoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ001 - Dados carregados:', {
                    inspecoes: inspecoes.length,
                    fornecedores: fornecedores.length
                });

                populateFornecedorFilter();
                updateStats();
                renderTable();
                showLoading(false);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ001:', error);
                showLoading(false);
                showError('Erro ao carregar dados de inspeção');
            }
        }

        function populateFornecedorFilter() {
            const select = document.getElementById('fornecedorFilter');
            select.innerHTML = '<option value="">Todos os Fornecedores</option>';
            
            fornecedores.forEach(fornecedor => {
                const option = document.createElement('option');
                option.value = fornecedor.id;
                option.textContent = fornecedor.razaoSocial || fornecedor.nome;
                select.appendChild(option);
            });
        }

        function updateStats() {
            const stats = {
                pendente: inspecoes.filter(i => i.status === 'PENDENTE').length,
                inspecao: inspecoes.filter(i => i.status === 'EM_INSPECAO').length,
                aprovado: inspecoes.filter(i => i.status === 'APROVADO').length,
                rejeitado: inspecoes.filter(i => i.status === 'REJEITADO').length
            };

            document.getElementById('statPendente').textContent = stats.pendente;
            document.getElementById('statInspecao').textContent = stats.inspecao;
            document.getElementById('statAprovado').textContent = stats.aprovado;
            document.getElementById('statRejeitado').textContent = stats.rejeitado;
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (inspecoes.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = inspecoes.map(inspecao => {
                const fornecedor = fornecedores.find(f => f.id === inspecao.fornecedorId);
                const dataRecebimento = inspecao.dataRecebimento ? 
                    new Date(inspecao.dataRecebimento.seconds * 1000).toLocaleDateString() : 'N/A';

                return `
                    <tr>
                        <td><strong>${inspecao.codigo || 'N/A'}</strong></td>
                        <td>${inspecao.produtoNome || 'N/A'}</td>
                        <td>${inspecao.lote || 'N/A'}</td>
                        <td>${inspecao.quantidade || 0} ${inspecao.unidade || ''}</td>
                        <td>${fornecedor?.razaoSocial || 'N/A'}</td>
                        <td>${dataRecebimento}</td>
                        <td><span class="status-badge status-${inspecao.status?.toLowerCase() || 'pendente'}">${inspecao.status || 'PENDENTE'}</span></td>
                        <td>${inspecao.inspetor || 'N/A'}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarInspecao('${inspecao.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${inspecao.status === 'PENDENTE' ? `
                                    <button class="btn btn-warning btn-action" onclick="iniciarInspecao('${inspecao.id}')" title="Iniciar Inspeção">
                                        <i class="fas fa-play"></i>
                                    </button>
                                ` : ''}
                                ${inspecao.status === 'EM_INSPECAO' ? `
                                    <button class="btn btn-success btn-action" onclick="finalizarInspecao('${inspecao.id}')" title="Finalizar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Funções globais
        window.aplicarFiltros = function() {
            // TODO: Implementar filtros
            console.log('🔍 Aplicando filtros...');
        };

        window.novaInspecao = function() {
            mostrarModalNovaInspecao();
        };

        // Mostrar modal de nova inspeção
        function mostrarModalNovaInspecao() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus"></i> Nova Inspeção de Recebimento</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="formNovaInspecao">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="numeroRecebimento">
                                        <i class="fas fa-hashtag"></i>
                                        Número do Recebimento
                                    </label>
                                    <input type="text" id="numeroRecebimento" placeholder="Ex: REC-123456" required>
                                </div>
                                <div class="form-group">
                                    <label for="fornecedorInspecao">
                                        <i class="fas fa-building"></i>
                                        Fornecedor
                                    </label>
                                    <select id="fornecedorInspecao" required>
                                        <option value="">Selecione o fornecedor</option>
                                        ${fornecedores.map(f => `<option value="${f.id}">${f.razaoSocial || f.nome}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tipoInspecao">
                                        <i class="fas fa-search"></i>
                                        Tipo de Inspeção
                                    </label>
                                    <select id="tipoInspecao" required>
                                        <option value="">Selecione o tipo</option>
                                        <option value="VISUAL">Inspeção Visual</option>
                                        <option value="DIMENSIONAL">Inspeção Dimensional</option>
                                        <option value="FUNCIONAL">Teste Funcional</option>
                                        <option value="QUIMICA">Análise Química</option>
                                        <option value="COMPLETA">Inspeção Completa</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="prioridadeInspecao">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Prioridade
                                    </label>
                                    <select id="prioridadeInspecao" required>
                                        <option value="BAIXA">Baixa</option>
                                        <option value="MEDIA" selected>Média</option>
                                        <option value="ALTA">Alta</option>
                                        <option value="URGENTE">Urgente</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="inspetorResponsavel">
                                        <i class="fas fa-user"></i>
                                        Inspetor Responsável
                                    </label>
                                    <select id="inspetorResponsavel" required>
                                        <option value="">Selecione o inspetor</option>
                                        <option value="inspetor1">João Silva</option>
                                        <option value="inspetor2">Maria Santos</option>
                                        <option value="inspetor3">Pedro Costa</option>
                                        <option value="inspetor4">Ana Oliveira</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="dataLimiteInspecao">
                                        <i class="fas fa-calendar"></i>
                                        Data Limite
                                    </label>
                                    <input type="date" id="dataLimiteInspecao" required>
                                </div>
                            </div>

                            <div class="form-group" style="margin-top: 20px;">
                                <label for="observacoesInspecao">
                                    <i class="fas fa-comment"></i>
                                    Observações
                                </label>
                                <textarea id="observacoesInspecao" rows="3" placeholder="Observações sobre a inspeção..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button class="btn btn-success" onclick="criarInspecao()">
                            <i class="fas fa-check"></i> Criar Inspeção
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Definir data limite padrão (3 dias)
            const dataLimite = new Date();
            dataLimite.setDate(dataLimite.getDate() + 3);
            document.getElementById('dataLimiteInspecao').value = dataLimite.toISOString().split('T')[0];
        }

        // Criar inspeção
        window.criarInspecao = async function() {
            const form = document.getElementById('formNovaInspecao');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            try {
                const novaInspecao = {
                    numero: 'INS-' + Date.now(),
                    numeroRecebimento: document.getElementById('numeroRecebimento').value,
                    fornecedorId: document.getElementById('fornecedorInspecao').value,
                    tipo: document.getElementById('tipoInspecao').value,
                    prioridade: document.getElementById('prioridadeInspecao').value,
                    inspetorResponsavel: document.getElementById('inspetorResponsavel').value,
                    dataLimite: Timestamp.fromDate(new Date(document.getElementById('dataLimiteInspecao').value)),
                    observacoes: document.getElementById('observacoesInspecao').value,
                    status: 'PENDENTE',
                    dataCriacao: Timestamp.now(),
                    usuarioCriacao: 'usuario_atual'
                };

                await addDoc(collection(db, "inspecoesRecebimento"), novaInspecao);

                showAlert('success', '✅ Inspeção criada com sucesso!');
                fecharModal();
                loadData(); // Recarregar dados

            } catch (error) {
                console.error('❌ Erro ao criar inspeção:', error);
                showAlert('danger', 'Erro ao criar inspeção');
            }
        };

        window.exportarRelatorio = function() {
            const dados = inspecoes.map(inspecao => {
                const fornecedor = fornecedores.find(f => f.id === inspecao.fornecedorId);
                return {
                    'Número': inspecao.numero,
                    'Recebimento': inspecao.numeroRecebimento,
                    'Fornecedor': fornecedor?.razaoSocial || 'N/A',
                    'Tipo': inspecao.tipo,
                    'Status': inspecao.status,
                    'Prioridade': inspecao.prioridade,
                    'Inspetor': inspecao.inspetorResponsavel,
                    'Data Criação': inspecao.dataCriacao ? new Date(inspecao.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'
                };
            });

            if (dados.length === 0) {
                showAlert('warning', 'Não há dados para exportar');
                return;
            }

            // Converter para CSV
            const headers = Object.keys(dados[0]);
            const csvContent = [
                headers.join(','),
                ...dados.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\\n');

            // Download do arquivo
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `inspecoes_recebimento_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showAlert('success', '📊 Relatório exportado com sucesso!');
        };

        window.sincronizarRecebimento = async function() {
            try {
                showLoading(true);

                // Buscar recebimentos pendentes de inspeção
                const recebimentosSnap = await getDocs(
                    query(collection(db, "recebimentosMateriais"),
                          where("status", "==", "AGUARDANDO_INSPECAO"))
                );

                let novasInspecoes = 0;

                for (const recDoc of recebimentosSnap.docs) {
                    const recebimento = recDoc.data();

                    // Verificar se já existe inspeção para este recebimento
                    const inspecaoExistente = inspecoes.find(i => i.numeroRecebimento === recebimento.numero);

                    if (!inspecaoExistente) {
                        // Criar inspeção automática
                        const novaInspecao = {
                            numero: 'INS-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5),
                            numeroRecebimento: recebimento.numero,
                            fornecedorId: recebimento.fornecedorId,
                            tipo: 'VISUAL', // Tipo padrão
                            prioridade: 'MEDIA',
                            inspetorResponsavel: 'inspetor1', // Inspetor padrão
                            dataLimite: Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)), // 3 dias
                            observacoes: 'Inspeção criada automaticamente via sincronização',
                            status: 'PENDENTE',
                            dataCriacao: Timestamp.now(),
                            usuarioCriacao: 'sistema_automatico'
                        };

                        await addDoc(collection(db, "inspecoesRecebimento"), novaInspecao);
                        novasInspecoes++;
                    }
                }

                showAlert('success', `🔄 Sincronização concluída! ${novasInspecoes} novas inspeções criadas.`);
                loadData(); // Recarregar dados
                showLoading(false);

            } catch (error) {
                console.error('❌ Erro na sincronização:', error);
                showAlert('danger', 'Erro na sincronização com recebimentos');
                showLoading(false);
            }
        };

        window.visualizarInspecao = function(id) {
            const inspecao = inspecoes.find(i => i.id === id);
            if (!inspecao) {
                showAlert('danger', 'Inspeção não encontrada');
                return;
            }

            const fornecedor = fornecedores.find(f => f.id === inspecao.fornecedorId);
            mostrarModalVisualizacao(inspecao, fornecedor);
        };

        window.iniciarInspecao = async function(id) {
            if (!confirm('Deseja iniciar esta inspeção?')) return;

            try {
                await updateDoc(doc(db, "inspecoesRecebimento", id), {
                    status: 'EM_INSPECAO',
                    dataInicio: Timestamp.now(),
                    usuarioInicio: 'usuario_atual'
                });

                showAlert('success', '▶️ Inspeção iniciada com sucesso!');
                loadData();

            } catch (error) {
                console.error('❌ Erro ao iniciar inspeção:', error);
                showAlert('danger', 'Erro ao iniciar inspeção');
            }
        };

        window.finalizarInspecao = async function(id) {
            const resultado = prompt('Resultado da inspeção (APROVADO/REJEITADO):');
            if (!resultado || !['APROVADO', 'REJEITADO'].includes(resultado.toUpperCase())) {
                showAlert('warning', 'Resultado inválido. Use APROVADO ou REJEITADO.');
                return;
            }

            try {
                await updateDoc(doc(db, "inspecoesRecebimento", id), {
                    status: resultado.toUpperCase(),
                    dataFinalizacao: Timestamp.now(),
                    usuarioFinalizacao: 'usuario_atual'
                });

                showAlert('success', `✅ Inspeção finalizada como ${resultado.toUpperCase()}!`);
                loadData();

            } catch (error) {
                console.error('❌ Erro ao finalizar inspeção:', error);
                showAlert('danger', 'Erro ao finalizar inspeção');
            }
        };

        // Mostrar modal de visualização
        function mostrarModalVisualizacao(inspecao, fornecedor) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-eye"></i> Visualizar Inspeção ${inspecao.numero}</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="inspecao-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-info-circle"></i> Dados Gerais</h4>
                                <div class="detail-grid">
                                    <div><strong>Número:</strong> ${inspecao.numero}</div>
                                    <div><strong>Status:</strong> <span class="status-badge status-${inspecao.status?.toLowerCase()}">${inspecao.status}</span></div>
                                    <div><strong>Recebimento:</strong> ${inspecao.numeroRecebimento}</div>
                                    <div><strong>Fornecedor:</strong> ${fornecedor?.razaoSocial || 'N/A'}</div>
                                    <div><strong>Tipo:</strong> ${inspecao.tipo}</div>
                                    <div><strong>Prioridade:</strong> ${inspecao.prioridade}</div>
                                    <div><strong>Inspetor:</strong> ${inspecao.inspetorResponsavel}</div>
                                    <div><strong>Data Limite:</strong> ${inspecao.dataLimite ? new Date(inspecao.dataLimite.seconds * 1000).toLocaleDateString() : 'N/A'}</div>
                                </div>
                            </div>

                            ${inspecao.observacoes ? `
                                <div class="detail-section">
                                    <h4><i class="fas fa-comment"></i> Observações</h4>
                                    <p>${inspecao.observacoes}</p>
                                </div>
                            ` : ''}

                            <div class="detail-section">
                                <h4><i class="fas fa-clock"></i> Histórico</h4>
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <i class="fas fa-plus-circle"></i>
                                        <div>
                                            <strong>Criação:</strong> ${inspecao.dataCriacao ? new Date(inspecao.dataCriacao.seconds * 1000).toLocaleString() : 'N/A'}
                                            <br><small>Por: ${inspecao.usuarioCriacao || 'N/A'}</small>
                                        </div>
                                    </div>
                                    ${inspecao.dataInicio ? `
                                        <div class="timeline-item">
                                            <i class="fas fa-play-circle"></i>
                                            <div>
                                                <strong>Início:</strong> ${new Date(inspecao.dataInicio.seconds * 1000).toLocaleString()}
                                                <br><small>Por: ${inspecao.usuarioInicio || 'N/A'}</small>
                                            </div>
                                        </div>
                                    ` : ''}
                                    ${inspecao.dataFinalizacao ? `
                                        <div class="timeline-item">
                                            <i class="fas fa-check-circle"></i>
                                            <div>
                                                <strong>Finalização:</strong> ${new Date(inspecao.dataFinalizacao.seconds * 1000).toLocaleString()}
                                                <br><small>Por: ${inspecao.usuarioFinalizacao || 'N/A'}</small>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                        ${inspecao.status === 'PENDENTE' ? `
                            <button class="btn btn-warning" onclick="fecharModal(); iniciarInspecao('${inspecao.id}');">
                                <i class="fas fa-play"></i> Iniciar Inspeção
                            </button>
                        ` : ''}
                        ${inspecao.status === 'EM_INSPECAO' ? `
                            <button class="btn btn-success" onclick="fecharModal(); finalizarInspecao('${inspecao.id}');">
                                <i class="fas fa-check"></i> Finalizar Inspeção
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Fechar modal
        window.fecharModal = function() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        };

        // Função para mostrar alertas
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer') || document.body;
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${message}
            `;

            alertContainer.appendChild(alert);
            alert.style.display = 'block';

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
