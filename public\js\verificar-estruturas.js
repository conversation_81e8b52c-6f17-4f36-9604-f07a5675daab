// Funções para verificação de estruturas de produtos
import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs,
    query,
    where 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

let produtos = [];
let estruturas = [];

/**
 * Carrega os dados necessários para verificação
 * @returns {Promise<{produtos: Array, estruturas: Array}>}
 */
export async function carregarDadosEstruturas() {
    try {
        const [produtosSnap, estruturasSnap] = await Promise.all([
            getDocs(collection(db, "produtos")),
            getDocs(collection(db, "estruturas"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log("Dados carregados:", {
            produtos: produtos.length,
            estruturas: estruturas.length
        });

        return { produtos, estruturas };
    } catch (error) {
        console.error("Erro ao carregar dados:", error);
        throw new Error("Erro ao carregar dados: " + error.message);
    }
}

/**
 * Verifica se um produto específico tem estrutura válida
 * @param {string} produtoId - ID do produto a ser verificado
 * @returns {Promise<Object>} Resultado da verificação
 */
export async function verificarProduto(produtoId) {
    if (!produtos.length || !estruturas.length) {
        await carregarDadosEstruturas();
    }

    const produto = produtos.find(p => p.id === produtoId);
    if (!produto) {
        throw new Error(`Produto não encontrado: ${produtoId}`);
    }

    const erros = [];
    const avisos = [];
    const usoComoComponente = [];

    // Verificar se é um produto que deve ter estrutura
    if (produto.tipo === 'PA' || produto.tipo === 'SP') {
        const estrutura = estruturas.find(e => e.produtoPaiId === produto.id);

        // Verificar se tem estrutura
        if (!estrutura) {
            erros.push('Produto não possui estrutura cadastrada');
        } else {
            // Verificar componentes da estrutura
            if (!estrutura.componentes || estrutura.componentes.length === 0) {
                erros.push('Estrutura não possui componentes');
            } else {
                estrutura.componentes.forEach(componente => {
                    const produtoComponente = produtos.find(p => p.id === componente.componentId);
                    if (!produtoComponente) {
                        erros.push(`Componente ${componente.componentId} não encontrado no cadastro de produtos`);
                    } else if (produtoComponente.tipo === 'PA' || produtoComponente.tipo === 'SP') {
                        // Verificar se o componente PA/SP tem sua própria estrutura
                        const subEstrutura = estruturas.find(e => e.produtoPaiId === componente.componentId);
                        if (!subEstrutura) {
                            avisos.push(`Componente ${produtoComponente.codigo} (${produtoComponente.descricao}) não possui estrutura cadastrada`);
                        }
                    }
                });
            }
        }
    }

    // Verificar onde o produto é utilizado como componente
    const estruturasQueUsamProduto = estruturas.filter(e => 
        e.componentes && e.componentes.some(c => c.componentId === produto.id)
    );

    if (estruturasQueUsamProduto.length > 0) {
        estruturasQueUsamProduto.forEach(e => {
            const produtoPai = produtos.find(p => p.id === e.produtoPaiId);
            if (produtoPai) {
                usoComoComponente.push(produtoPai);
            }
        });
    }

    return {
        produto,
        erros,
        avisos,
        usoComoComponente
    };
}

/**
 * Verifica se um produto é utilizado como componente em outras estruturas
 * @param {string} produtoId - ID do produto a ser verificado
 * @returns {Promise<Array>} Lista de produtos que utilizam o componente
 */
export async function verificarUsoComoComponente(produtoId) {
    if (!produtos.length || !estruturas.length) {
        await carregarDadosEstruturas();
    }

    const estruturasQueUsamProduto = estruturas.filter(e => 
        e.componentes && e.componentes.some(c => c.componentId === produtoId)
    );

    return estruturasQueUsamProduto.map(e => {
        const produtoPai = produtos.find(p => p.id === e.produtoPaiId);
        return produtoPai || null;
    }).filter(Boolean);
}

/**
 * Verifica se uma estrutura é válida para geração de ordem de produção
 * @param {string} produtoId - ID do produto a ser verificado
 * @returns {Promise<Object>} Resultado da verificação
 */
export async function verificarEstruturaParaOP(produtoId) {
    const resultado = await verificarProduto(produtoId);

    if (resultado.erros.length > 0) {
        throw new Error(`Estrutura inválida para geração de OP: ${resultado.erros.join(', ')}`);
    }

    return resultado;
}

/**
 * Verifica todas as estruturas do sistema
 * @returns {Promise<Array>} Lista de problemas encontrados
 */
export async function verificarTodasEstruturas() {
    if (!produtos.length || !estruturas.length) {
        await carregarDadosEstruturas();
    }

    const problemas = [];

    // Verificar produtos PA e SP
    for (const produto of produtos.filter(p => p.tipo === 'PA' || p.tipo === 'SP')) {
        const resultado = await verificarProduto(produto.id);
        if (resultado.erros.length > 0 || resultado.avisos.length > 0 || resultado.usoComoComponente.length > 0) {
            problemas.push(resultado);
        }
    }

    return problemas;
} 