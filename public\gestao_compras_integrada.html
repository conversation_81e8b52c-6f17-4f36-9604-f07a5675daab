<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FYRON MRP - Central de Aprovação</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            border-bottom: 3px solid #ecf0f1;
            margin-bottom: 30px;
            overflow-x: auto;
        }

        .tab {
            padding: 15px 25px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #7f8c8d;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tab.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            transform: translateY(-2px);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.pendentes {
            border-left-color: #f39c12;
        }

        .stat-card.aprovadas {
            border-left-color: #27ae60;
        }

        .stat-card.cotacao {
            border-left-color: #6c757d;
        }

        .stat-card.cotadas {
            border-left-color: #17a2b8;
        }

        .stat-card.total {
            border-left-color: #3498db;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-cotacao {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .status-cotado {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .status-comprado {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-enviada {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .status-respondida {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .status-rascunho {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .status-finalizada {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-recebido {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            display: none;
            animation: slideIn 0.3s ease;
        }

        .notification-success {
            background: #27ae60;
        }

        .notification-error {
            background: #e74c3c;
        }

        .notification-warning {
            background: #f39c12;
        }

        .notification-info {
            background: #3498db;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .table-container {
                overflow-x: auto;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .actions {
                flex-direction: column;
            }
        }

        /* Estilos para Ações em Lote */
        .batch-actions-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .batch-info {
            font-weight: 600;
            font-size: 16px;
        }

        .batch-buttons {
            display: flex;
            gap: 10px;
        }

        .batch-buttons .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .batch-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .batch-buttons .btn-success {
            background-color: #28a745;
            color: white;
        }

        .batch-buttons .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .batch-buttons .btn-info {
            background-color: #17a2b8;
            color: white;
        }

        .batch-buttons .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        /* Checkbox personalizado */
        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #007bff;
        }

        /* Linha selecionada */
        .table tbody tr.selected {
            background-color: #e3f2fd !important;
            border-left: 4px solid #2196f3;
        }

        .table tbody tr.selected:hover {
            background-color: #bbdefb !important;
        }

        /* Controles de visibilidade */
        .visibility-controls {
            background: #f8f9fa;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .visibility-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
            color: #495057;
        }

        .hidden-count {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 12px;
        }

        .visibility-toggles {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toggle-btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 5px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .toggle-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .toggle-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .toggle-btn.active:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        /* Estilos do Banner de Produção */
        .production-banner {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 25px;
            margin: -20px -20px 0 -20px;
            position: relative;
            overflow: hidden;
        }

        .production-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="prod-pattern" width="15" height="15" patternUnits="userSpaceOnUse"><circle cx="7.5" cy="7.5" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23prod-pattern)"/></svg>');
            opacity: 0.3;
        }

        .production-content {
            display: flex;
            align-items: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .production-icon {
            font-size: 32px;
            color: #ffd700;
        }

        .production-text h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 700;
        }

        .production-text p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .production-actions {
            margin-left: auto;
            display: flex;
            gap: 10px;
        }

        .btn-production-demo {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-production-demo:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-production-help {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-production-help:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .production-label {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 700;
            margin-left: 10px;
            animation: glow 2s infinite;
        }

        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
            50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.8); }
            100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Banner do Sistema Real -->
        <div class="production-banner">
            <div class="production-content">
                <div class="production-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="production-text">
                    <h3>🚀 SISTEMA DE PRODUÇÃO</h3>
                    <p>Ambiente real com dados persistentes e integração completa.</p>
                </div>
                <div class="production-actions">
                    <button class="btn-production-demo" onclick="switchToDemo()">
                        <i class="fas fa-theater-masks"></i>
                        Ver Demonstração
                    </button>
                    <button class="btn-production-help" onclick="showProductionHelp()">
                        <i class="fas fa-question-circle"></i>
                        Ajuda
                    </button>
                </div>
            </div>
        </div>

        <div class="header">
            <h1>
                <i class="fas fa-check-circle"></i>
                FYRON MRP - Central de Aprovação <span class="production-label">REAL</span>
            </h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="novasolicitacao()">
                    <i class="fas fa-plus"></i>
                    Nova Solicitação
                </button>
                <button class="btn btn-primary" onclick="gerarRelatorio()">
                    <i class="fas fa-chart-bar"></i>
                    Relatórios
                </button>
                <button class="btn btn-info" onclick="voltarIndex()">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Alerta de Melhorias -->
            <div class="alert alert-info" style="margin-bottom: 25px;">
                <i class="fas fa-rocket"></i>
                <strong>Sistema Atualizado!</strong>
                Novos recursos implementados: Sistema de cotações com análise inteligente, seleção de vencedores,
                links de resposta para fornecedores, cotações aglutinadas e interface melhorada.
            </div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card pendentes">
                    <div class="stat-number" id="statPendentes">0</div>
                    <div class="stat-label">Solicitações Pendentes</div>
                </div>
                <div class="stat-card aprovadas">
                    <div class="stat-number" id="statAprovadas">0</div>
                    <div class="stat-label">Aprovadas</div>
                </div>
                <div class="stat-card cotacao">
                    <div class="stat-number" id="statCotacao">0</div>
                    <div class="stat-label">Cotações Ativas</div>
                </div>
                <div class="stat-card cotadas">
                    <div class="stat-number" id="statCotadas">0</div>
                    <div class="stat-label">Com Respostas</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-number" id="statTotal">0</div>
                    <div class="stat-label">Total Geral</div>
                </div>
            </div>

            <!-- Abas -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('pedidos', event)">
                    <i class="fas fa-check-circle"></i>
                    Aprovação de Pedidos
                </button>
                <button class="tab" onclick="showTab('solicitacoes', event)">
                    <i class="fas fa-list"></i>
                    Solicitações
                </button>
                <button class="tab" onclick="showTab('cotacao', event)">
                    <i class="fas fa-handshake"></i>
                    Cotações
                </button>
                <button class="tab" onclick="showTab('recebimento', event)">
                    <i class="fas fa-truck"></i>
                    Recebimento
                </button>
            </div>

            <!-- Aba Solicitações -->
            <div id="solicitacoes" class="tab-content">
                <!-- Filtros Padronizados -->
                <div class="filters" style="background: #f8f9fa; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-filter"></i> Filtros Avançados
                    </h3>
                    <div class="filter-row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Data Início</label>
                            <input type="date" class="form-control" id="dataInicioSol" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Data Fim</label>
                            <input type="date" class="form-control" id="dataFimSol" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Status</label>
                            <select class="form-control" id="statusFilterSol" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todos</option>
                                <option value="PENDENTE">Pendente</option>
                                <option value="APROVADA">Aprovada</option>
                                <option value="REJEITADA">Rejeitada</option>
                                <option value="EM_COTACAO">Em Cotação</option>
                                <option value="COTADO">Cotado</option>
                                <option value="FINALIZADA">Finalizada</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Prioridade</label>
                            <select class="form-control" id="priorityFilterSol" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todas</option>
                                <option value="NORMAL">Normal</option>
                                <option value="ALTA">Alta</option>
                                <option value="URGENTE">Urgente</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Departamento</label>
                            <select class="form-control" id="departmentFilterSol" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todos</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Solicitante</label>
                            <input type="text" class="form-control" id="requesterFilterSol" placeholder="Nome do solicitante" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Exibição</label>
                            <select class="form-control" id="displayFilterSol" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="active">🟢 Apenas Ativas (Pendente/Aprovada)</option>
                                <option value="quoted">📋 Em Cotação</option>
                                <option value="finalized">✅ Finalizadas</option>
                                <option value="all">📋 Todas</option>
                            </select>
                        </div>
                        <div class="form-group" style="display: flex; align-items: end;">
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-primary" onclick="applyFiltersSolicitacoes()">
                                    <i class="fas fa-filter"></i> Aplicar
                                </button>
                                <button class="btn btn-secondary" onclick="clearFiltersSolicitacoes()">
                                    <i class="fas fa-times"></i> Limpar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controles de Visibilidade -->
                <div class="visibility-controls">
                    <div class="visibility-info">
                        <i class="fas fa-eye"></i>
                        <span>Exibindo itens filtrados</span>
                        <span class="hidden-count" id="hiddenSolicitacoesCount">0 finalizadas ocultas</span>
                    </div>
                    <div class="visibility-toggles">
                        <button class="toggle-btn" id="toggleFinalizadasSol" onclick="toggleFinalizadas('solicitacoes')">
                            <i class="fas fa-eye-slash"></i> Mostrar Finalizadas
                        </button>
                        <button class="toggle-btn" id="toggleRejeitadasSol" onclick="toggleRejeitadas('solicitacoes')">
                            <i class="fas fa-ban"></i> Mostrar Rejeitadas
                        </button>
                    </div>
                </div>

                <!-- Barra de Ações em Lote -->
                <div class="batch-actions-bar" id="batchActionsBar" style="display: none;">
                    <div class="batch-info">
                        <span id="selectedCount">0</span> item(ns) selecionado(s)
                    </div>
                    <div class="batch-buttons">
                        <button class="btn btn-success" onclick="batchApprove()" id="batchApproveBtn">
                            <i class="fas fa-check"></i> Aprovar Selecionados
                        </button>
                        <button class="btn btn-danger" onclick="batchReject()" id="batchRejectBtn">
                            <i class="fas fa-times"></i> Rejeitar Selecionados
                        </button>
                        <button class="btn btn-info" onclick="batchCreateQuotation()" id="batchQuotationBtn">
                            <i class="fas fa-handshake"></i> Criar Cotações
                        </button>
                        <button class="btn btn-secondary" onclick="clearSelection()">
                            <i class="fas fa-times-circle"></i> Limpar Seleção
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="selectAllSolicitacoes" onchange="toggleSelectAllSolicitacoes()" title="Selecionar todas">
                                </th>
                                <th>Número</th>
                                <th>Data</th>
                                <th>Solicitante</th>
                                <th>Descrição</th>
                                <th>Valor Total</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="solicitacoesTableBody">
                            <!-- Dados carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>
            </div>



            <!-- Aba Cotação -->
            <div id="cotacao" class="tab-content">
                <!-- Filtros Padronizados -->
                <div class="filters" style="background: #f8f9fa; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-filter"></i> Filtros Avançados
                    </h3>
                    <div class="filter-row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Data Início</label>
                            <input type="date" class="form-control" id="dataInicioCot" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Data Fim</label>
                            <input type="date" class="form-control" id="dataFimCot" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Status</label>
                            <select class="form-control" id="statusFilterCot" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todos</option>
                                <option value="ABERTA">Aberta</option>
                                <option value="ENVIADA">Enviada</option>
                                <option value="RESPONDIDA">Respondida</option>
                                <option value="FECHADA">Fechada</option>
                                <option value="CANCELADA">Cancelada</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Solicitação Origem</label>
                            <select class="form-control" id="solicitacaoFilterCot" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todas</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Fornecedor</label>
                            <select class="form-control" id="fornecedorFilterCot" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todos</option>
                            </select>
                        </div>
                        <div class="form-group" style="display: flex; align-items: end;">
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-primary" onclick="applyFiltersCotacoes()">
                                    <i class="fas fa-filter"></i> Aplicar
                                </button>
                                <button class="btn btn-secondary" onclick="clearFiltersCotacoes()">
                                    <i class="fas fa-times"></i> Limpar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controles de Visibilidade -->
                <div class="visibility-controls">
                    <div class="visibility-info">
                        <i class="fas fa-eye"></i>
                        <span>Exibindo cotações filtradas</span>
                        <span class="hidden-count" id="hiddenCotacoesCount">0 fechadas ocultas</span>
                    </div>
                    <div class="visibility-toggles">
                        <button class="toggle-btn" id="toggleFechadasCot" onclick="toggleFechadas('cotacoes')">
                            <i class="fas fa-eye-slash"></i> Mostrar Fechadas
                        </button>
                        <button class="toggle-btn" id="toggleCanceladasCot" onclick="toggleCanceladas('cotacoes')">
                            <i class="fas fa-ban"></i> Mostrar Canceladas
                        </button>
                    </div>
                </div>

                <div class="alert alert-success">
                    <i class="fas fa-star"></i>
                    <strong>Sistema de Cotações Melhorado:</strong>
                    Agora com análise inteligente de respostas, seleção automática de melhores preços,
                    links de resposta para fornecedores e sistema de cotações aglutinadas.
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Número/Origem</th>
                                <th>Data Criação</th>
                                <th>Fornecedores/Prazo</th>
                                <th>Respostas</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="cotacaoTableBody">
                            <!-- Dados carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Aba Pedidos -->
            <div id="pedidos" class="tab-content active">
                <!-- Seção de Aprovação de Pedidos -->
                <div class="alert alert-info" style="margin-bottom: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <i class="fas fa-user-shield" style="font-size: 24px;"></i>
                        <div>
                            <h4 style="margin: 0; font-size: 18px;">Central de Aprovação - Gestor</h4>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">
                                Aprove ou rejeite pedidos de compra. Controle total sobre o processo de aprovação com filtros avançados e ações em lote.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Dashboard de Aprovação -->
                <div class="approval-dashboard" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                    <div class="approval-card" style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-clock" style="color: #ffc107; font-size: 20px;"></i>
                            <div>
                                <h4 style="margin: 0; color: #856404;">Aguardando Aprovação</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #856404;" id="pendingCount">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="approval-card" style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-exclamation-triangle" style="color: #17a2b8; font-size: 20px;"></i>
                            <div>
                                <h4 style="margin: 0; color: #0c5460;">Alto Valor</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #0c5460;" id="highValueCount">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="approval-card" style="background: #d4edda; border-left: 4px solid #28a745; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-check-circle" style="color: #28a745; font-size: 20px;"></i>
                            <div>
                                <h4 style="margin: 0; color: #155724;">Aprovados Hoje</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #155724;" id="approvedTodayCount">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="approval-card" style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-dollar-sign" style="color: #dc3545; font-size: 20px;"></i>
                            <div>
                                <h4 style="margin: 0; color: #721c24;">Valor Total</h4>
                                <p style="margin: 5px 0 0 0; font-size: 18px; font-weight: bold; color: #721c24;" id="totalValuePending">R$ 0,00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtros Padronizados -->
                <div class="filters" style="background: #f8f9fa; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-filter"></i> Filtros Avançados
                    </h3>
                    <div class="filter-row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Período</label>
                            <input type="date" class="form-control" id="startDatePed" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Até</label>
                            <input type="date" class="form-control" id="endDatePed" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Fornecedor</label>
                            <select class="form-control" id="supplierFilterPed" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todos</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Número</label>
                            <input type="text" class="form-control" id="numeroFilterPed" placeholder="Número do pedido" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                    </div>
                    <div class="filter-row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Observações</label>
                            <input type="text" class="form-control" id="observacaoFilterPed" placeholder="Observações" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Status</label>
                            <select class="form-control" id="statusFilterPed" style="padding: 12px; border: 2px solid #bdc3c7; border-radius: 8px;">
                                <option value="">Todos</option>
                                <option value="PENDENTE">Pendente</option>
                                <option value="ABERTO">Aberto</option>
                                <option value="APROVADO">Aprovado</option>
                                <option value="RECEBIDO">Recebido</option>
                                <option value="CANCELADO">Cancelado</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="font-weight: 600; margin-bottom: 8px; color: #2c3e50; display: block;">Opções</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" id="ocultarRecebidosPed" checked>
                                <span>Ocultar pedidos recebidos</span>
                            </div>
                        </div>
                        <div class="form-group" style="display: flex; align-items: end;">
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-primary" onclick="applyFiltersPedidos()">
                                    <i class="fas fa-filter"></i> Aplicar
                                </button>
                                <button class="btn btn-secondary" onclick="clearFiltersPedidos()">
                                    <i class="fas fa-times"></i> Limpar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ações Rápidas do Gestor -->
                <div class="manager-quick-actions" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff;">
                    <h4 style="margin-bottom: 15px; color: #2c3e50;">
                        <i class="fas fa-bolt"></i> Ações Rápidas do Gestor
                    </h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                        <button class="btn btn-warning" onclick="showOnlyPendingApproval()">
                            <i class="fas fa-clock"></i> Apenas Pendentes
                        </button>
                        <button class="btn btn-danger" onclick="showHighValueOrders()">
                            <i class="fas fa-exclamation-triangle"></i> Alto Valor (>R$ 10.000)
                        </button>
                        <button class="btn btn-info" onclick="showUrgentOrders()">
                            <i class="fas fa-fire"></i> Urgentes
                        </button>
                        <button class="btn btn-success" onclick="approveAllVisible()">
                            <i class="fas fa-check-double"></i> Aprovar Todos Visíveis
                        </button>
                        <button class="btn btn-secondary" onclick="exportApprovalReport()">
                            <i class="fas fa-file-pdf"></i> Relatório de Aprovação
                        </button>
                    </div>
                </div>

                <!-- Ações em Lote para Aprovação -->
                <div id="batchApprovalActions" class="batch-actions-bar" style="display: none; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                    <div class="batch-info">
                        <i class="fas fa-check-square"></i>
                        <span id="selectedPedidosCount">0</span> pedidos selecionados para aprovação
                    </div>
                    <div class="batch-buttons">
                        <button class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="batchApprovePedidos()">
                            <i class="fas fa-check"></i> Aprovar Selecionados
                        </button>
                        <button class="btn" style="background: rgba(220,53,69,0.8); color: white; border: 1px solid rgba(220,53,69,0.5);" onclick="batchRejectPedidos()">
                            <i class="fas fa-times"></i> Rejeitar Selecionados
                        </button>
                        <button class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="batchExportPedidos()">
                            <i class="fas fa-file-export"></i> Exportar Selecionados
                        </button>
                        <button class="btn" style="background: rgba(108,117,125,0.8); color: white; border: 1px solid rgba(108,117,125,0.5);" onclick="clearPedidosSelection()">
                            <i class="fas fa-times-circle"></i> Limpar Seleção
                        </button>
                    </div>
                </div>

                <!-- Controles de Visibilidade -->
                <div class="visibility-controls">
                    <div class="visibility-info">
                        <i class="fas fa-eye"></i>
                        <span>Exibindo pedidos pendentes (aprovados ocultos por padrão)</span>
                        <span class="hidden-count" id="hiddenPedidosCount">0 finalizados ocultos</span>
                    </div>
                    <div class="visibility-toggles">
                        <button class="toggle-btn" id="toggleAprovadosPed" onclick="toggleAprovados('pedidos')">
                            <i class="fas fa-check"></i> Mostrar Aprovados
                        </button>
                        <button class="toggle-btn" id="toggleRecebidosPed" onclick="toggleRecebidos('pedidos')">
                            <i class="fas fa-eye-slash"></i> Mostrar Recebidos
                        </button>
                        <button class="toggle-btn" id="toggleCanceladosPed" onclick="toggleCancelados('pedidos')">
                            <i class="fas fa-ban"></i> Mostrar Cancelados
                        </button>
                        <button class="toggle-btn" id="toggleFinalizadosPed" onclick="toggleFinalizados('pedidos')">
                            <i class="fas fa-check-circle"></i> Mostrar Finalizados
                        </button>
                        <button class="toggle-btn" onclick="togglePendingApprovalOnly()">
                            <i class="fas fa-clock"></i> Apenas Pendentes
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllPedidos" onchange="toggleSelectAllPedidos()">
                                </th>
                                <th>Número PC</th>
                                <th>SC Origem</th>
                                <th>Fornecedor</th>
                                <th>Data Pedido</th>
                                <th>Valor Total</th>
                                <th>Status</th>
                                <th>Previsão Entrega</th>
                                <th>Prioridade</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="pedidosTableBody">
                            <!-- Dados carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Aba Recebimento -->
            <div id="recebimento" class="tab-content">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    Acompanhe o recebimento de materiais e controle de qualidade.
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Pedido</th>
                                <th>Fornecedor</th>
                                <th>Data Prevista</th>
                                <th>Status Entrega</th>
                                <th>Itens Pendentes</th>
                                <th>Valor Pendente</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="recebimentoTableBody">
                            <!-- Dados carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            addDoc,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            query,
            where,
            orderBy,
            limit,
            startAfter,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let solicitacoes = [];
        let cotacoes = [];
        let pedidosCompra = [];
        let fornecedores = [];
        let produtos = [];
        let setores = [];

        // Controles de visibilidade
        let showFinalizadas = false;
        let showRejeitadas = false;
        let showFechadas = false;
        let showCanceladas = false;
        let showRecebidos = false;
        let showCancelados = false; // Para pedidos
        let showFinalizados = false;
        let showAprovados = false; // Ocultar aprovados por padrão

        // Status que devem ser ocultados por padrão
        const statusOcultosSolicitacoes = ['FINALIZADA', 'CANCELADA', 'REJEITADA'];
        const statusOcultosCotacoes = ['FECHADA', 'CANCELADA'];
        const statusOcultosPedidos = ['RECEBIDO', 'CANCELADO', 'FINALIZADO'];

        // Verificar autenticação
        if (!currentUser) {
            window.location.href = 'login.html';
        }

        // Carregar dados iniciais
        window.onload = async function() {
            await loadInitialData();
            updateStats();
            carregarDadosFiltros();
            updateApprovalDashboard();
            verificarPedidoPendente();

            // Focar na aba de pedidos por padrão
            showTab('pedidos');
        };

        async function loadInitialData() {
            try {
                showNotification('Carregando dados...', 'info');

                const [solicitacoesSnap, cotacoesSnap, pedidosSnap, fornecedoresSnap, produtosSnap, setoresSnap] = await Promise.all([
                    getDocs(query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))),
                    getDocs(query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"))),
                    getDocs(query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"))),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "setores"))
                ]);

                solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                pedidosCompra = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                setores = setoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', {
                    solicitacoes: solicitacoes.length,
                    cotacoes: cotacoes.length,
                    pedidos: pedidosCompra.length,
                    fornecedores: fornecedores.length,
                    produtos: produtos.length,
                    setores: setores.length
                });

                // Renderizar dados nas abas
                renderSolicitacoes();
                renderCotacoes();
                renderPedidos();
                renderRecebimento();

                showNotification('Dados carregados com sucesso!', 'success');

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados iniciais', 'error');
            }
        }

        function updateStats() {
            const pendentes = solicitacoes.filter(s => s.status === 'PENDENTE').length;
            const aprovadas = solicitacoes.filter(s => s.status === 'APROVADA').length;

            // Contar cotações em andamento (enviadas, abertas)
            const emCotacao = cotacoes.filter(c =>
                c.status === 'ENVIADA' || c.status === 'ABERTA' || c.status === 'EM_COTACAO'
            ).length;

            // Contar cotações com respostas (respondidas, fechadas)
            const cotadas = cotacoes.filter(c =>
                c.status === 'RESPONDIDA' || c.status === 'FECHADA' ||
                (c.respostas && Object.keys(c.respostas).length > 0)
            ).length;

            const total = solicitacoes.length;

            document.getElementById('statPendentes').textContent = pendentes;
            document.getElementById('statAprovadas').textContent = aprovadas;
            document.getElementById('statCotacao').textContent = emCotacao;
            document.getElementById('statCotadas').textContent = cotadas;
            document.getElementById('statTotal').textContent = total;
        }

        function renderSolicitacoes() {
            const tbody = document.getElementById('solicitacoesTableBody');
            tbody.innerHTML = '';

            // Filtrar solicitações baseado na visibilidade
            const solicitacoesFiltradas = solicitacoes.filter(solicitacao => {
                if (!showFinalizadas && solicitacao.status === 'FINALIZADA') return false;
                if (!showRejeitadas && solicitacao.status === 'REJEITADA') return false;
                if (!showCanceladas && solicitacao.status === 'CANCELADA') return false;
                return true;
            });

            // Atualizar contador de itens ocultos
            const totalOcultas = solicitacoes.length - solicitacoesFiltradas.length;
            const hiddenCount = document.getElementById('hiddenSolicitacoesCount');
            if (hiddenCount) {
                hiddenCount.textContent = `${totalOcultas} finalizadas ocultas`;
                hiddenCount.style.display = totalOcultas > 0 ? 'inline' : 'none';
            }

            if (solicitacoesFiltradas.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6c757d;">Nenhuma solicitação encontrada</td></tr>';
                return;
            }

            // Ordenar por número (mais recentes primeiro)
            solicitacoesFiltradas.sort((a, b) => {
                return compareNumbers(b.numero, a.numero);
            });

            solicitacoesFiltradas.slice(0, 20).forEach(solicitacao => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', solicitacao.id);
                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="row-checkbox" value="${solicitacao.id}"
                               onchange="updateSelection()" data-status="${solicitacao.status}">
                    </td>
                    <td><strong>${solicitacao.numero || 'N/A'}</strong></td>
                    <td>${formatDate(solicitacao.dataCriacao)}</td>
                    <td>${solicitacao.solicitante || currentUser?.nome || 'Sistema'}</td>
                    <td>${solicitacao.justificativa || 'N/A'}</td>
                    <td>R$ ${formatCurrency(solicitacao.valorTotal || 0)}</td>
                    <td><span class="status status-${getStatusClass(solicitacao.status)}">${getStatusText(solicitacao.status)}</span></td>
                    <td class="actions">
                        <button class="btn btn-primary btn-sm" onclick="visualizarSolicitacao('${solicitacao.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${solicitacao.status === 'PENDENTE' ? `
                            <button class="btn btn-warning btn-sm" onclick="editarSolicitacao('${solicitacao.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${solicitacao.status === 'APROVADA' ? `
                            <button class="btn btn-info btn-sm" onclick="criarCotacao('${solicitacao.id}')">
                                <i class="fas fa-handshake"></i>
                            </button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }



        function renderCotacoes() {
            const tbody = document.getElementById('cotacaoTableBody');
            tbody.innerHTML = '';

            // Filtrar cotações baseado na visibilidade
            const cotacoesFiltradas = cotacoes.filter(cotacao => {
                if (!showFechadas && cotacao.status === 'FECHADA') return false;
                if (!showCanceladas && cotacao.status === 'CANCELADA') return false;
                return true;
            });

            // Atualizar contador de itens ocultos
            const totalOcultas = cotacoes.length - cotacoesFiltradas.length;
            const hiddenCount = document.getElementById('hiddenCotacoesCount');
            if (hiddenCount) {
                hiddenCount.textContent = `${totalOcultas} fechadas ocultas`;
                hiddenCount.style.display = totalOcultas > 0 ? 'inline' : 'none';
            }

            if (cotacoesFiltradas.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #6c757d;">Nenhuma cotação encontrada</td></tr>';
                return;
            }

            cotacoesFiltradas.slice(0, 20).forEach(cotacao => {
                const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
                const totalFornecedores = cotacao.fornecedores ? cotacao.fornecedores.length : 0;
                const respostas = cotacao.respostas ? Object.keys(cotacao.respostas).length : 0;

                // Verificar se há cotações aglutinadas
                const isAglutinada = cotacao.tipo === 'AGLUTINADA';
                const cotacaoNumero = cotacao.numero || `CT-${new Date().getFullYear()}-${cotacao.id?.slice(-4)}`;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <strong>${cotacaoNumero}</strong>
                        ${isAglutinada ? '<br><small class="text-info"><i class="fas fa-layer-group"></i> Aglutinada</small>' : ''}
                        ${solicitacao ? `<br><small class="text-muted">${solicitacao.numero || 'N/A'}</small>` : ''}
                    </td>
                    <td>${formatDate(cotacao.dataCriacao)}</td>
                    <td>
                        ${totalFornecedores} fornecedores
                        ${cotacao.dataLimite ? `<br><small class="text-muted">Limite: ${formatDate(cotacao.dataLimite)}</small>` : ''}
                    </td>
                    <td>
                        <span class="${respostas > 0 ? 'text-success' : 'text-muted'}">${respostas}/${totalFornecedores}</span> respostas
                        ${respostas > 0 ? `<br><small class="text-success"><i class="fas fa-check-circle"></i> Com respostas</small>` : ''}
                    </td>
                    <td><span class="status status-${getStatusClass(cotacao.status)}">${getStatusText(cotacao.status)}</span></td>
                    <td class="actions">
                        <button class="btn btn-primary btn-sm" onclick="visualizarCotacao('${cotacao.id}')" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${(cotacao.status === 'ABERTA' || cotacao.status === 'ENVIADA') ? `
                            <button class="btn btn-warning btn-sm" onclick="acompanharCotacao('${cotacao.id}')" title="Editar/Acompanhar">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${respostas > 0 ? `
                            <button class="btn btn-success btn-sm" onclick="analisarCotacoes('${cotacao.id}')" title="Analisar Respostas">
                                <i class="fas fa-chart-line"></i>
                            </button>
                        ` : ''}
                        ${cotacao.status === 'RESPONDIDA' && respostas >= 2 ? `
                            <button class="btn btn-info btn-sm" onclick="selecionarVencedores('${cotacao.id}')" title="Selecionar Vencedores">
                                <i class="fas fa-trophy"></i>
                            </button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function renderPedidos() {
            const tbody = document.getElementById('pedidosTableBody');
            tbody.innerHTML = '';

            // Aplicar filtros padronizados
            const statusFilter = document.getElementById('statusFilterPed')?.value || '';
            const fornecedorFilter = document.getElementById('supplierFilterPed')?.value || '';
            const ocultarRecebidos = document.getElementById('ocultarRecebidosPed')?.checked || false;

            // Filtrar pedidos baseado na visibilidade e filtros padronizados
            const pedidosFiltrados = pedidosCompra.filter(pedido => {
                // Filtros de visibilidade
                if (!showAprovados && pedido.status === 'APROVADO') return false;
                if (!showRecebidos && pedido.status === 'RECEBIDO') return false;
                if (!showCancelados && pedido.status === 'CANCELADO') return false;
                if (!showFinalizados && pedido.status === 'FINALIZADO') return false;

                // Filtros padronizados
                if (statusFilter && pedido.status !== statusFilter) return false;
                if (fornecedorFilter && pedido.fornecedorId !== fornecedorFilter) return false;
                if (ocultarRecebidos && pedido.status === 'RECEBIDO') return false;

                return true;
            });

            // Atualizar contador de itens ocultos
            const totalOcultos = pedidosCompra.length - pedidosFiltrados.length;
            const hiddenCount = document.getElementById('hiddenPedidosCount');
            if (hiddenCount) {
                const aprovadosOcultos = pedidosCompra.filter(p => p.status === 'APROVADO').length;
                const recebidosOcultos = pedidosCompra.filter(p => p.status === 'RECEBIDO').length;
                const canceladosOcultos = pedidosCompra.filter(p => p.status === 'CANCELADO').length;

                let textoOcultos = [];
                if (!showAprovados && aprovadosOcultos > 0) textoOcultos.push(`${aprovadosOcultos} aprovados`);
                if (!showRecebidos && recebidosOcultos > 0) textoOcultos.push(`${recebidosOcultos} recebidos`);
                if (!showCancelados && canceladosOcultos > 0) textoOcultos.push(`${canceladosOcultos} cancelados`);

                hiddenCount.textContent = textoOcultos.length > 0 ? `${textoOcultos.join(', ')} ocultos` : '';
                hiddenCount.style.display = totalOcultos > 0 ? 'inline' : 'none';
            }

            if (pedidosFiltrados.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center; color: #6c757d;">Nenhum pedido encontrado</td></tr>';
                return;
            }

            // Ordenar por prioridade (pendentes primeiro)
            pedidosFiltrados.sort((a, b) => {
                const priorityOrder = { 'PENDENTE': 1, 'ABERTO': 2, 'APROVADO': 3, 'RECEBIDO': 4, 'CANCELADO': 5 };
                return (priorityOrder[a.status] || 6) - (priorityOrder[b.status] || 6);
            });

            pedidosFiltrados.slice(0, 20).forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const isPendingApproval = ['PENDENTE', 'ABERTO'].includes(pedido.status);
                const isHighValue = (pedido.valorTotal || 0) > 10000; // Valor alto para prioridade

                const row = document.createElement('tr');
                if (isPendingApproval) {
                    row.style.backgroundColor = '#fff3cd'; // Destaque para pendentes
                }

                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="pedido-checkbox" value="${pedido.id}"
                               data-status="${pedido.status}" onchange="updateSelectedPedidosCount()">
                    </td>
                    <td><strong>${pedido.numero || 'N/A'}</strong></td>
                    <td>${pedido.solicitacaoNumero || 'N/A'}</td>
                    <td>${fornecedor?.razaoSocial || pedido.fornecedorNome || 'N/A'}</td>
                    <td>${formatDate(pedido.dataPedido || pedido.dataCriacao)}</td>
                    <td>
                        <strong style="color: ${isHighValue ? '#e74c3c' : '#2c3e50'};">
                            R$ ${formatCurrency(pedido.valorTotal || 0)}
                        </strong>
                    </td>
                    <td><span class="status status-${getStatusClass(pedido.status)}">${getStatusText(pedido.status)}</span></td>
                    <td>${formatDate(pedido.dataEntregaPrevista)}</td>
                    <td>
                        <span class="status status-${isHighValue ? 'rejeitado' : isPendingApproval ? 'pendente' : 'aprovado'}">
                            ${isHighValue ? 'ALTA' : isPendingApproval ? 'URGENTE' : 'NORMAL'}
                        </span>
                    </td>
                    <td class="actions">
                        ${isPendingApproval ? `
                            <button class="btn btn-success btn-sm" onclick="aprovarPedidoIndividual('${pedido.id}')" title="Aprovar Pedido">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="rejeitarPedidoIndividual('${pedido.id}')" title="Rejeitar Pedido">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-primary btn-sm" onclick="acompanharPedido('${pedido.id}')" title="Acompanhar">
                            <i class="fas fa-truck"></i>
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="imprimirPedido('${pedido.id}')" title="Imprimir">
                            <i class="fas fa-print"></i>
                        </button>
                        ${pedido.status === 'APROVADO' ? `
                            <button class="btn btn-info btn-sm" onclick="receberMaterial('${pedido.id}')" title="Receber Material">
                                <i class="fas fa-box"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-secondary btn-sm" onclick="visualizarPedidoDetalhes('${pedido.id}')" title="Ver Detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Atualizar contador de seleção
            updateSelectedPedidosCount();
        }

        function renderRecebimento() {
            const tbody = document.getElementById('recebimentoTableBody');
            tbody.innerHTML = '';

            // Filtrar pedidos que estão aguardando recebimento
            const pedidosPendentes = pedidosCompra.filter(p =>
                p.status === 'APROVADO' &&
                p.itens &&
                p.itens.some(item => (item.quantidadeRecebida || 0) < item.quantidade)
            );

            if (pedidosPendentes.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6c757d;">Nenhum pedido pendente de recebimento</td></tr>';
                return;
            }

            pedidosPendentes.forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const itensPendentes = pedido.itens.filter(item => (item.quantidadeRecebida || 0) < item.quantidade).length;
                const valorPendente = pedido.itens.reduce((total, item) => {
                    const qtdPendente = item.quantidade - (item.quantidadeRecebida || 0);
                    return total + (qtdPendente * (item.valorUnitario || 0));
                }, 0);

                const isDelayed = isOrderDelayed(pedido);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${pedido.numero || 'N/A'}</strong></td>
                    <td>${fornecedor?.razaoSocial || pedido.fornecedorNome || 'N/A'}</td>
                    <td>${formatDate(pedido.dataEntregaPrevista)}</td>
                    <td>
                        <span class="status status-${isDelayed ? 'rejeitado' : 'pendente'}">
                            ${isDelayed ? 'ATRASADO' : 'NO PRAZO'}
                        </span>
                    </td>
                    <td>${itensPendentes} itens</td>
                    <td>R$ ${formatCurrency(valorPendente)}</td>
                    <td class="actions">
                        <button class="btn btn-success btn-sm" onclick="receberMaterial('${pedido.id}')">
                            <i class="fas fa-box"></i>
                            Receber
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="acompanharPedido('${pedido.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function isOrderDelayed(order) {
            if (!order.dataEntregaPrevista) return false;

            const today = new Date();
            const deliveryDate = order.dataEntregaPrevista.toDate ?
                order.dataEntregaPrevista.toDate() :
                new Date(order.dataEntregaPrevista);

            return today > deliveryDate;
        }

        // Função para comparar números de solicitação de forma inteligente
        function compareNumbers(numA, numB) {
            // Extrair partes do número SC-AAMM-XXXX
            const parseNumber = (num) => {
                if (!num) return { year: 0, month: 0, sequence: 0 };

                // Formato SC-AAMM-XXXX
                const match = num.match(/SC-(\d{2})(\d{2})-(\d{4})/);
                if (match) {
                    return {
                        year: parseInt(match[1]),
                        month: parseInt(match[2]),
                        sequence: parseInt(match[3])
                    };
                }

                // Fallback: extrair apenas números
                const numbers = num.replace(/\D/g, '');
                return {
                    year: 0,
                    month: 0,
                    sequence: parseInt(numbers) || 0
                };
            };

            const a = parseNumber(numA);
            const b = parseNumber(numB);

            // Comparar por ano, depois mês, depois sequência
            if (a.year !== b.year) return a.year - b.year;
            if (a.month !== b.month) return a.month - b.month;
            return a.sequence - b.sequence;
        }

        // Funções auxiliares
        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleDateString('pt-BR');
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        function getStatusClass(status) {
            switch (status) {
                case 'PENDENTE': return 'pendente';
                case 'APROVADA': case 'APROVADO': return 'aprovado';
                case 'REJEITADA': case 'REJEITADO': return 'rejeitado';
                case 'EM_COTACAO': return 'cotacao';
                case 'COTADO': return 'cotado';
                case 'COMPRADO': return 'comprado';
                case 'ABERTA': return 'pendente';
                case 'ENVIADA': return 'cotacao';
                case 'RESPONDIDA': return 'cotado';
                case 'FECHADA': return 'aprovado';
                case 'CANCELADA': return 'rejeitado';
                case 'RASCUNHO': return 'pendente';
                case 'FINALIZADA': return 'aprovado';
                case 'RECEBIDO': return 'comprado';
                default: return 'pendente';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'PENDENTE': return 'Pendente';
                case 'APROVADA': case 'APROVADO': return 'Aprovado';
                case 'REJEITADA': case 'REJEITADO': return 'Rejeitado';
                case 'EM_COTACAO': return 'Em Cotação';
                case 'COTADO': return 'Cotado';
                case 'COMPRADO': return 'Comprado';
                case 'ABERTA': return 'Aberta';
                case 'ENVIADA': return 'Enviada';
                case 'RESPONDIDA': return 'Respondida';
                case 'FECHADA': return 'Fechada';
                case 'CANCELADA': return 'Cancelada';
                case 'RASCUNHO': return 'Rascunho';
                case 'FINALIZADA': return 'Finalizada';
                case 'RECEBIDO': return 'Recebido';
                default: return 'Pendente';
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type}`;
            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Função para mostrar/ocultar abas
        window.showTab = function(tabName, event) {
            // Ocultar todas as abas
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Remover classe active de todos os botões
            const tabButtons = document.querySelectorAll('.tab');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Mostrar aba selecionada
            const targetTab = document.getElementById(tabName);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // Adicionar classe active ao botão correspondente
            const targetButton = Array.from(tabButtons).find(button =>
                button.getAttribute('onclick')?.includes(tabName)
            );
            if (targetButton) {
                targetButton.classList.add('active');
            }

            // Se há um evento, usar o target do evento
            if (event && event.target) {
                // Remover active de todos primeiro
                tabButtons.forEach(button => button.classList.remove('active'));
                // Adicionar ao botão clicado
                event.target.classList.add('active');
            }
        };

        // Funções de navegação
        window.novasolicitacao = function() {
            window.location.href = 'solicitacao_compras_melhorada.html';
        };

        window.voltarIndex = function() {
            window.location.href = 'index.html';
        };

        window.gerarRelatorio = function() {
            showNotification('Funcionalidade de relatórios em desenvolvimento...', 'info');
        };

        // Funções específicas do sistema
        window.aprovarSolicitacao = async function(id) {
            if (confirm('Confirma a aprovação desta solicitação?')) {
                try {
                    await updateDoc(doc(db, "solicitacoesCompra", id), {
                        status: 'APROVADA',
                        dataAprovacao: new Date(),
                        aprovadoPor: currentUser?.nome || 'Sistema',
                        uidAprovacao: currentUser?.uid || 'sistema'
                    });

                    showNotification('Solicitação aprovada com sucesso!', 'success');
                    await loadInitialData();
                    updateStats();
                } catch (error) {
                    console.error('Erro ao aprovar solicitação:', error);
                    showNotification('Erro ao aprovar solicitação', 'error');
                }
            }
        };

        window.rejeitarSolicitacao = async function(id) {
            const motivo = prompt('Motivo da rejeição:');
            if (motivo) {
                try {
                    await updateDoc(doc(db, "solicitacoesCompra", id), {
                        status: 'REJEITADA',
                        dataRejeicao: new Date(),
                        rejeitadoPor: currentUser?.nome || 'Sistema',
                        motivoRejeicao: motivo,
                        uidRejeicao: currentUser?.uid || 'sistema'
                    });

                    showNotification('Solicitação rejeitada', 'warning');
                    await loadInitialData();
                    updateStats();
                } catch (error) {
                    console.error('Erro ao rejeitar solicitação:', error);
                    showNotification('Erro ao rejeitar solicitação', 'error');
                }
            }
        };

        window.criarCotacao = function(solicitacaoId) {
            // Redirecionar para o sistema de cotações atualizado
            window.location.href = `cotacoes/index.html?nova=true&solicitacao=${solicitacaoId}`;
        };

        window.visualizarSolicitacao = function(id) {
            // Redirecionar para visualização detalhada
            window.location.href = `solicitacao_compras_melhorada.html?view=${id}`;
        };

        window.editarSolicitacao = function(id) {
            // Redirecionar para edição
            window.location.href = `solicitacao_compras_melhorada.html?edit=${id}`;
        };

        window.visualizarCotacao = function(id) {
            // Redirecionar para visualização de cotação
            window.location.href = `cotacoes/index.html?view=${id}`;
        };

        window.acompanharCotacao = function(id) {
            // Redirecionar para acompanhamento de cotação
            window.location.href = `cotacoes/index.html?edit=${id}`;
        };

        window.analisarCotacoes = function(id) {
            // Redirecionar para análise de cotações com novo sistema
            window.location.href = `cotacoes/index.html?analise=${id}`;
        };

        window.selecionarVencedores = function(id) {
            // Redirecionar para seleção de vencedores
            window.location.href = `cotacoes/index.html?selecao=${id}`;
        };

        window.acompanharPedido = function(id) {
            // Redirecionar para acompanhamento
            window.location.href = `pedidos_compra.html?view=${id}`;
        };

        window.imprimirPedido = function(id) {
            // Implementar impressão
            window.open(`print_pedido.html?id=${id}`, '_blank');
        };

        window.receberMaterial = function(id) {
            // Redirecionar para recebimento
            window.location.href = `recebimento_materiais_melhorado.html?pedido=${id}`;
        };

        // ===== FUNÇÕES DE AÇÕES EM LOTE =====

        // Variáveis para controle de seleção
        let selectedItems = new Set();

        // Função para alternar seleção de todas as solicitações
        window.toggleSelectAllSolicitacoes = function() {
            const selectAllCheckbox = document.getElementById('selectAllSolicitacoes');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');

            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    selectedItems.add(checkbox.value);
                    checkbox.closest('tr').classList.add('selected');
                } else {
                    selectedItems.delete(checkbox.value);
                    checkbox.closest('tr').classList.remove('selected');
                }
            });

            updateSelection();
        };

        // Função para atualizar seleção individual
        window.updateSelection = function() {
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllSolicitacoes');

            // Atualizar conjunto de itens selecionados
            selectedItems.clear();
            rowCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedItems.add(checkbox.value);
                    checkbox.closest('tr').classList.add('selected');
                } else {
                    checkbox.closest('tr').classList.remove('selected');
                }
            });

            // Atualizar checkbox "selecionar todos"
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
            const totalCount = rowCheckboxes.length;

            selectAllCheckbox.checked = checkedCount === totalCount && totalCount > 0;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;

            // Mostrar/ocultar barra de ações
            const batchBar = document.getElementById('batchActionsBar');
            const selectedCount = document.getElementById('selectedCount');

            if (selectedItems.size > 0) {
                batchBar.style.display = 'flex';
                selectedCount.textContent = selectedItems.size;
                updateBatchButtons();
            } else {
                batchBar.style.display = 'none';
            }
        };

        // Função para atualizar botões baseado no status dos itens selecionados
        function updateBatchButtons() {
            const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            const statuses = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-status'));

            const approveBtn = document.getElementById('batchApproveBtn');
            const rejectBtn = document.getElementById('batchRejectBtn');
            const quotationBtn = document.getElementById('batchQuotationBtn');

            // Habilitar aprovação apenas para itens PENDENTES
            const hasPendentes = statuses.includes('PENDENTE');
            approveBtn.disabled = !hasPendentes;
            rejectBtn.disabled = !hasPendentes;

            // Habilitar cotação apenas para itens APROVADOS
            const hasAprovados = statuses.includes('APROVADA');
            quotationBtn.disabled = !hasAprovados;

            // Atualizar texto dos botões
            const pendentesCount = statuses.filter(s => s === 'PENDENTE').length;
            const aprovadosCount = statuses.filter(s => s === 'APROVADA').length;

            if (pendentesCount > 0) {
                approveBtn.innerHTML = `<i class="fas fa-check"></i> Aprovar ${pendentesCount} item(ns)`;
                rejectBtn.innerHTML = `<i class="fas fa-times"></i> Rejeitar ${pendentesCount} item(ns)`;
            }

            if (aprovadosCount > 0) {
                quotationBtn.innerHTML = `<i class="fas fa-handshake"></i> Criar ${aprovadosCount} Cotação(ões)`;
            }
        }

        // Função para limpar seleção
        window.clearSelection = function() {
            selectedItems.clear();
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.closest('tr').classList.remove('selected');
            });
            document.getElementById('selectAllSolicitacoes').checked = false;
            document.getElementById('batchActionsBar').style.display = 'none';
        };

        // Função para criar pedido de compra a partir de uma cotação aprovada
        async function criarPedidoCompra(solicitacaoId) {
            try {
                // Buscar os dados completos da solicitação
                const solicitacaoDoc = await getDoc(doc(db, "solicitacoesCompra", solicitacaoId));
                if (!solicitacaoDoc.exists()) {
                    console.error('Solicitação não encontrada:', solicitacaoId);
                    return null;
                }

                const solicitacao = { id: solicitacaoDoc.id, ...solicitacaoDoc.data() };
                
                // Verificar se já existe um pedido para esta solicitação
                if (solicitacao.pedidoCriado) {
                    console.log('Já existe um pedido para esta solicitação');
                    return null;
                }

                // Criar o objeto do pedido de compra
                const pedidoCompra = {
                    numero: 'PC-' + new Date().getTime(),
                    solicitacaoId: solicitacaoId,
                    dataCriacao: new Date(),
                    status: 'PENDENTE',
                    itens: solicitacao.itens || [],
                    valorTotal: solicitacao.valorTotal || 0,
                    fornecedorId: solicitacao.fornecedorId,
                    departamento: solicitacao.departamento,
                    centroCusto: solicitacao.centroCusto,
                    aprovadoPor: currentUser?.nome || 'Sistema',
                    dataAprovacao: new Date(),
                    criadoPor: currentUser?.nome || 'Sistema',
                    uidCriacao: currentUser?.uid || 'sistema'
                };

                // Salvar o pedido de compra
                const pedidoRef = await addDoc(collection(db, "pedidosCompra"), pedidoCompra);
                
                // Atualizar a solicitação para marcar que o pedido foi criado
                await updateDoc(doc(db, "solicitacoesCompra", solicitacaoId), {
                    pedidoCriado: true,
                    pedidoId: pedidoRef.id
                });

                return pedidoRef.id;
                
            } catch (error) {
                console.error('Erro ao criar pedido de compra:', error);
                return null;
            }
        }

        // Função para aprovação em lote
        window.batchApprove = async function() {
            const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            const pendentesIds = Array.from(selectedCheckboxes)
                .filter(cb => cb.getAttribute('data-status') === 'PENDENTE')
                .map(cb => cb.value);

            if (pendentesIds.length === 0) {
                showNotification('Nenhuma solicitação pendente selecionada', 'warning');
                return;
            }


            if (!confirm(`Confirma a aprovação de ${pendentesIds.length} solicitação(ões)?`)) {
                return;
            }

            try {
                showNotification('Processando aprovações...', 'info');
                let pedidosCriados = 0;
                const erros = [];

                // Processar cada solicitação individualmente
                for (const id of pendentesIds) {
                    try {
                        // Atualizar status para APROVADA
                        await updateDoc(doc(db, "solicitacoesCompra", id), {
                            status: 'APROVADA',
                            dataAprovacao: new Date(),
                            aprovadoPor: currentUser?.nome || 'Sistema',
                            uidAprovacao: currentUser?.uid || 'sistema'
                        });

                        // Criar pedido de compra
                        const pedidoId = await criarPedidoCompra(id);
                        if (pedidoId) {
                            pedidosCriados++;
                        }
                    } catch (error) {
                        console.error(`Erro ao processar solicitação ${id}:`, error);
                        erros.push(`Erro na solicitação ${id}: ${error.message}`);
                    }
                }


                // Exibir mensagem de sucesso
                let mensagem = `${pendentesIds.length} solicitação(ões) aprovada(s) com sucesso!`;
                if (pedidosCriados > 0) {
                    mensagem += ` ${pedidosCriados} pedido(s) de compra criado(s).`;
                }
                if (erros.length > 0) {
                    mensagem += `\n\nOcorreram erros em ${erros.length} item(ns):\n${erros.join('\n')}`;
                }

                showNotification(mensagem, erros.length > 0 ? 'warning' : 'success');
                
                // Atualizar a interface
                clearSelection();
                await loadInitialData();
                updateStats();

            } catch (error) {
                console.error('Erro ao aprovar solicitações:', error);
                showNotification('Erro ao processar aprovações em lote: ' + error.message, 'error');
            }
        };

        // Função para rejeição em lote
        window.batchReject = async function() {
            const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            const pendentesIds = Array.from(selectedCheckboxes)
                .filter(cb => cb.getAttribute('data-status') === 'PENDENTE')
                .map(cb => cb.value);

            if (pendentesIds.length === 0) {
                showNotification('Nenhuma solicitação pendente selecionada', 'warning');
                return;
            }

            const motivo = prompt(`Motivo da rejeição para ${pendentesIds.length} solicitação(ões):`);
            if (!motivo) return;

            try {
                showNotification('Processando rejeições...', 'info');

                const promises = pendentesIds.map(id =>
                    updateDoc(doc(db, "solicitacoesCompra", id), {
                        status: 'REJEITADA',
                        dataRejeicao: new Date(),
                        rejeitadoPor: currentUser?.nome || 'Sistema',
                        motivoRejeicao: motivo,
                        uidRejeicao: currentUser?.uid || 'sistema'
                    })
                );

                await Promise.all(promises);

                showNotification(`${pendentesIds.length} solicitação(ões) rejeitada(s)`, 'warning');
                clearSelection();
                await loadInitialData();
                updateStats();

            } catch (error) {
                console.error('Erro ao rejeitar solicitações:', error);
                showNotification('Erro ao processar rejeições em lote', 'error');
            }
        };

        // Função para criar cotações em lote
        window.batchCreateQuotation = function() {
            const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            const aprovadosIds = Array.from(selectedCheckboxes)
                .filter(cb => cb.getAttribute('data-status') === 'APROVADA')
                .map(cb => cb.value);

            if (aprovadosIds.length === 0) {
                showNotification('Nenhuma solicitação aprovada selecionada', 'warning');
                return;
            }

            if (aprovadosIds.length === 1) {
                // Redirecionar para cotação única no novo sistema
                window.location.href = `cotacoes/index.html?nova=true&solicitacao=${aprovadosIds[0]}`;
            } else {
                // Redirecionar para cotação múltipla (aglutinação) no novo sistema
                const idsParam = aprovadosIds.join(',');
                window.location.href = `cotacoes/index.html?nova=true&aglutinar=${idsParam}`;
            }
        };

        // ===== FUNÇÕES DE CONTROLE DE VISIBILIDADE =====

        // Função para alternar visibilidade de finalizadas
        window.toggleFinalizadas = function(modulo) {
            if (modulo === 'solicitacoes') {
                showFinalizadas = !showFinalizadas;
                const btn = document.getElementById('toggleFinalizadasSol');
                if (showFinalizadas) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Finalizadas';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-eye-slash"></i> Mostrar Finalizadas';
                }
                renderSolicitacoes();
            }
        };

        // Função para alternar visibilidade de rejeitadas
        window.toggleRejeitadas = function(modulo) {
            if (modulo === 'solicitacoes') {
                showRejeitadas = !showRejeitadas;
                const btn = document.getElementById('toggleRejeitadasSol');
                if (showRejeitadas) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Rejeitadas';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-ban"></i> Mostrar Rejeitadas';
                }
                renderSolicitacoes();
            }
        };

        // Função para alternar visibilidade de fechadas (cotações)
        window.toggleFechadas = function(modulo) {
            if (modulo === 'cotacoes') {
                showFechadas = !showFechadas;
                const btn = document.getElementById('toggleFechadasCot');
                if (showFechadas) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Fechadas';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-eye-slash"></i> Mostrar Fechadas';
                }
                renderCotacoes();
            }
        };

        // Função para alternar visibilidade de canceladas
        window.toggleCanceladas = function(modulo) {
            if (modulo === 'cotacoes') {
                showCanceladas = !showCanceladas;
                const btn = document.getElementById('toggleCanceladasCot');
                if (showCanceladas) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Canceladas';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-ban"></i> Mostrar Canceladas';
                }
                renderCotacoes();
            }
        };

        // Função para alternar visibilidade de recebidos (pedidos)
        window.toggleRecebidos = function(modulo) {
            if (modulo === 'pedidos') {
                showRecebidos = !showRecebidos;
                const btn = document.getElementById('toggleRecebidosPed');
                if (showRecebidos) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Recebidos';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-eye-slash"></i> Mostrar Recebidos';
                }
                renderPedidos();
            }
        };

        // Função para alternar visibilidade de cancelados (pedidos)
        window.toggleCancelados = function(modulo) {
            if (modulo === 'pedidos') {
                showCancelados = !showCancelados;
                const btn = document.getElementById('toggleCanceladosPed');
                if (showCancelados) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Cancelados';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-ban"></i> Mostrar Cancelados';
                }
                renderPedidos();
            }
        };

        // Função para alternar visibilidade de aprovados (pedidos)
        window.toggleAprovados = function(modulo) {
            if (modulo === 'pedidos') {
                showAprovados = !showAprovados;
                const btn = document.getElementById('toggleAprovadosPed');
                if (showAprovados) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Aprovados';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-check"></i> Mostrar Aprovados';
                }
                renderPedidos();
                updateApprovalDashboard();
            }
        };

        // Função para alternar visibilidade de finalizados (pedidos)
        window.toggleFinalizados = function(modulo) {
            if (modulo === 'pedidos') {
                showFinalizados = !showFinalizados;
                const btn = document.getElementById('toggleFinalizadosPed');
                if (showFinalizados) {
                    btn.classList.add('active');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Ocultar Finalizados';
                } else {
                    btn.classList.remove('active');
                    btn.innerHTML = '<i class="fas fa-check-circle"></i> Mostrar Finalizados';
                }
                renderPedidos();
            }
        };

        // ===== FUNÇÕES DE APROVAÇÃO DE PEDIDOS =====

        // Função para alternar seleção de todos os pedidos
        window.toggleSelectAllPedidos = function() {
            const selectAll = document.getElementById('selectAllPedidos');
            const checkboxes = document.querySelectorAll('.pedido-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelectedPedidosCount();
        };

        // Função para atualizar contador de pedidos selecionados
        window.updateSelectedPedidosCount = function() {
            const selectedCheckboxes = document.querySelectorAll('.pedido-checkbox:checked');
            const count = selectedCheckboxes.length;

            const countElement = document.getElementById('selectedPedidosCount');
            if (countElement) {
                countElement.textContent = count;
            }

            const batchActions = document.getElementById('batchApprovalActions');
            if (count > 0) {
                batchActions.style.display = 'flex';
            } else {
                batchActions.style.display = 'none';
            }
        }



        // Função para aprovar pedidos em lote
        window.batchApprovePedidos = async function() {
            const selectedCheckboxes = document.querySelectorAll('.pedido-checkbox:checked');
            const pedidosIds = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (pedidosIds.length === 0) {
                showNotification('Nenhum pedido selecionado', 'warning');
                return;
            }

            const confirmacao = confirm(`Confirma a aprovação de ${pedidosIds.length} pedido(s)?`);
            if (!confirmacao) return;

            try {
                let aprovados = 0;
                let erros = 0;

                for (const pedidoId of pedidosIds) {
                    try {
                        await updateDoc(doc(db, "pedidosCompra", pedidoId), {
                            status: 'APROVADO',
                            dataAprovacao: new Date(),
                            aprovadoPor: currentUser?.nome || 'Sistema'
                        });
                        aprovados++;
                    } catch (error) {
                        console.error(`Erro ao aprovar pedido ${pedidoId}:`, error);
                        erros++;
                    }
                }

                if (erros === 0) {
                    showNotification(`${aprovados} pedido(s) aprovado(s) com sucesso!`, 'success');
                } else {
                    showNotification(`${aprovados} aprovados, ${erros} erro(s)`, 'warning');
                }

                // Recarregar dados
                await loadInitialData();
                clearPedidosSelection();

            } catch (error) {
                console.error('Erro na aprovação em lote:', error);
                showNotification('Erro na aprovação em lote', 'error');
            }
        };

        // Função para rejeitar pedidos em lote
        window.batchRejectPedidos = async function() {
            const selectedCheckboxes = document.querySelectorAll('.pedido-checkbox:checked');
            const pedidosIds = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (pedidosIds.length === 0) {
                showNotification('Nenhum pedido selecionado', 'warning');
                return;
            }

            const motivo = prompt('Motivo da rejeição:');
            if (!motivo) return;

            const confirmacao = confirm(`Confirma a rejeição de ${pedidosIds.length} pedido(s)?`);
            if (!confirmacao) return;

            try {
                let rejeitados = 0;
                let erros = 0;

                for (const pedidoId of pedidosIds) {
                    try {
                        await updateDoc(doc(db, "pedidosCompra", pedidoId), {
                            status: 'CANCELADO',
                            dataRejeicao: new Date(),
                            rejeitadoPor: currentUser?.nome || 'Sistema',
                            motivoRejeicao: motivo
                        });
                        rejeitados++;
                    } catch (error) {
                        console.error(`Erro ao rejeitar pedido ${pedidoId}:`, error);
                        erros++;
                    }
                }

                if (erros === 0) {
                    showNotification(`${rejeitados} pedido(s) rejeitado(s) com sucesso!`, 'success');
                } else {
                    showNotification(`${rejeitados} rejeitados, ${erros} erro(s)`, 'warning');
                }

                // Recarregar dados
                await loadInitialData();
                clearPedidosSelection();

            } catch (error) {
                console.error('Erro na rejeição em lote:', error);
                showNotification('Erro na rejeição em lote', 'error');
            }
        };

        // Função para limpar seleção de pedidos
        window.clearPedidosSelection = function() {
            const checkboxes = document.querySelectorAll('.pedido-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            document.getElementById('selectAllPedidos').checked = false;
            updateSelectedPedidosCount();
        };

        // Função para exportar pedidos selecionados
        window.batchExportPedidos = function() {
            const selectedCheckboxes = document.querySelectorAll('.pedido-checkbox:checked');
            const pedidosIds = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (pedidosIds.length === 0) {
                showNotification('Nenhum pedido selecionado', 'warning');
                return;
            }

            // Implementar exportação
            showNotification(`Exportando ${pedidosIds.length} pedido(s)...`, 'info');
        };

        // Função para alternar apenas pendentes de aprovação
        window.togglePendingApprovalOnly = function() {
            renderPedidos();
        };

        // Função para aprovar pedido individual
        window.aprovarPedidoIndividual = async function(pedidoId) {
            const confirmacao = confirm('Confirma a aprovação deste pedido?');
            if (!confirmacao) return;

            try {
                await updateDoc(doc(db, "pedidosCompra", pedidoId), {
                    status: 'APROVADO',
                    dataAprovacao: new Date(),
                    aprovadoPor: currentUser?.nome || 'Sistema',
                    uidAprovacao: currentUser?.uid || 'sistema'
                });

                showNotification('Pedido aprovado com sucesso!', 'success');
                await loadInitialData();

            } catch (error) {
                console.error('Erro ao aprovar pedido:', error);
                showNotification('Erro ao aprovar pedido: ' + error.message, 'error');
            }
        };

        // Função para rejeitar pedido individual
        window.rejeitarPedidoIndividual = async function(pedidoId) {
            const motivo = prompt('Motivo da rejeição:');
            if (!motivo) return;

            const confirmacao = confirm('Confirma a rejeição deste pedido?');
            if (!confirmacao) return;

            try {
                await updateDoc(doc(db, "pedidosCompra", pedidoId), {
                    status: 'CANCELADO',
                    dataRejeicao: new Date(),
                    rejeitadoPor: currentUser?.nome || 'Sistema',
                    motivoRejeicao: motivo,
                    uidRejeicao: currentUser?.uid || 'sistema'
                });

                showNotification('Pedido rejeitado', 'warning');
                await loadInitialData();

            } catch (error) {
                console.error('Erro ao rejeitar pedido:', error);
                showNotification('Erro ao rejeitar pedido: ' + error.message, 'error');
            }
        };

        // Função para visualizar detalhes do pedido
        window.visualizarPedidoDetalhes = function(pedidoId) {
            window.location.href = `pedidos_compra.html?view=${pedidoId}`;
        };

        // Função para carregar fornecedores no filtro
        function carregarFornecedoresFiltro() {
            const select = document.getElementById('fornecedorFilterPedidos');
            if (!select) return;

            select.innerHTML = '<option value="">Todos</option>';

            fornecedores.forEach(fornecedor => {
                select.innerHTML += `
                    <option value="${fornecedor.id}">
                        ${fornecedor.razaoSocial || fornecedor.nome}
                    </option>
                `;
            });
        }

        // Verificar se há pedido específico para aprovação (vindo do pedidos_compra.html)
        function verificarPedidoPendente() {
            const urlParams = new URLSearchParams(window.location.search);
            const pedidoId = urlParams.get('id');
            const action = urlParams.get('action');

            if (pedidoId && action === 'approve') {
                // Destacar o pedido específico
                setTimeout(() => {
                    const checkbox = document.querySelector(`input[value="${pedidoId}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        checkbox.closest('tr').style.backgroundColor = '#d4edda';
                        checkbox.closest('tr').scrollIntoView({ behavior: 'smooth', block: 'center' });
                        updateSelectedPedidosCount();
                    }
                }, 1000);
            }
        }

        // ===== FUNÇÕES DE FILTROS PADRONIZADOS =====

        // Filtros para Solicitações
        window.applyFiltersSolicitacoes = async function() {
            const filters = {
                dataInicio: document.getElementById('dataInicioSol').value,
                dataFim: document.getElementById('dataFimSol').value,
                status: document.getElementById('statusFilterSol').value,
                priority: document.getElementById('priorityFilterSol').value,
                department: document.getElementById('departmentFilterSol').value,
                requester: document.getElementById('requesterFilterSol').value.toLowerCase(),
                display: document.getElementById('displayFilterSol').value
            };

            try {
                const solicitacoesSnap = await getDocs(
                    query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))
                );
                let filteredRequests = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Aplicar filtro de exibição primeiro
                filteredRequests = filterByDisplaySol(filteredRequests, filters.display);

                if (filters.dataInicio) {
                    const startDate = new Date(filters.dataInicio);
                    filteredRequests = filteredRequests.filter(req => {
                        const reqDate = req.dataCriacao.toDate ? req.dataCriacao.toDate() : new Date(req.dataCriacao);
                        return reqDate >= startDate;
                    });
                }

                if (filters.dataFim) {
                    const endDate = new Date(filters.dataFim);
                    filteredRequests = filteredRequests.filter(req => {
                        const reqDate = req.dataCriacao.toDate ? req.dataCriacao.toDate() : new Date(req.dataCriacao);
                        return reqDate <= endDate;
                    });
                }

                if (filters.status) {
                    filteredRequests = filteredRequests.filter(req => req.status === filters.status);
                }

                if (filters.priority) {
                    filteredRequests = filteredRequests.filter(req => req.prioridade === filters.priority);
                }

                if (filters.department) {
                    filteredRequests = filteredRequests.filter(req => req.departamento === filters.department);
                }

                if (filters.requester) {
                    filteredRequests = filteredRequests.filter(req =>
                        (req.solicitante || currentUser?.nome || 'Sistema').toLowerCase().includes(filters.requester)
                    );
                }

                solicitacoes = filteredRequests;
                renderSolicitacoes();
                showNotification(`${filteredRequests.length} solicitações encontradas`, 'success');

            } catch (error) {
                console.error('Erro ao aplicar filtros:', error);
                showNotification('Erro ao aplicar filtros', 'error');
            }
        };

        window.clearFiltersSolicitacoes = function() {
            document.getElementById('dataInicioSol').value = '';
            document.getElementById('dataFimSol').value = '';
            document.getElementById('statusFilterSol').value = '';
            document.getElementById('priorityFilterSol').value = '';
            document.getElementById('departmentFilterSol').value = '';
            document.getElementById('requesterFilterSol').value = '';
            document.getElementById('displayFilterSol').value = 'active';
            renderSolicitacoes();
        };

        function filterByDisplaySol(requests, displayFilter) {
            switch (displayFilter) {
                case 'active':
                    return requests.filter(r => r.status === 'PENDENTE' || r.status === 'APROVADA');
                case 'quoted':
                    return requests.filter(r => r.status === 'EM_COTACAO' || r.status === 'COTADO');
                case 'finalized':
                    return requests.filter(r => r.status === 'FINALIZADA');
                case 'all':
                    return requests.filter(r => r.status !== 'EXCLUIDA');
                default:
                    return requests.filter(r => r.status === 'PENDENTE' || r.status === 'APROVADA');
            }
        }

        // Filtros para Cotações
        window.applyFiltersCotacoes = async function() {
            const filters = {
                dataInicio: document.getElementById('dataInicioCot').value,
                dataFim: document.getElementById('dataFimCot').value,
                status: document.getElementById('statusFilterCot').value,
                solicitacao: document.getElementById('solicitacaoFilterCot').value,
                fornecedor: document.getElementById('fornecedorFilterCot').value
            };

            try {
                const cotacoesSnap = await getDocs(
                    query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"))
                );
                let filteredCotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                if (filters.dataInicio) {
                    const startDate = new Date(filters.dataInicio);
                    filteredCotacoes = filteredCotacoes.filter(cot => {
                        const cotDate = cot.dataCriacao.toDate ? cot.dataCriacao.toDate() : new Date(cot.dataCriacao);
                        return cotDate >= startDate;
                    });
                }

                if (filters.dataFim) {
                    const endDate = new Date(filters.dataFim);
                    filteredCotacoes = filteredCotacoes.filter(cot => {
                        const cotDate = cot.dataCriacao.toDate ? cot.dataCriacao.toDate() : new Date(cot.dataCriacao);
                        return cotDate <= endDate;
                    });
                }

                if (filters.status) {
                    filteredCotacoes = filteredCotacoes.filter(cot => cot.status === filters.status);
                }

                if (filters.solicitacao) {
                    filteredCotacoes = filteredCotacoes.filter(cot => cot.solicitacaoId === filters.solicitacao);
                }

                if (filters.fornecedor) {
                    filteredCotacoes = filteredCotacoes.filter(cot =>
                        cot.fornecedores && cot.fornecedores.includes(filters.fornecedor)
                    );
                }

                cotacoes = filteredCotacoes;
                renderCotacoes();
                showNotification(`${filteredCotacoes.length} cotações encontradas`, 'success');

            } catch (error) {
                console.error('Erro ao aplicar filtros:', error);
                showNotification('Erro ao aplicar filtros', 'error');
            }
        };

        window.clearFiltersCotacoes = function() {
            document.getElementById('dataInicioCot').value = '';
            document.getElementById('dataFimCot').value = '';
            document.getElementById('statusFilterCot').value = '';
            document.getElementById('solicitacaoFilterCot').value = '';
            document.getElementById('fornecedorFilterCot').value = '';
            renderCotacoes();
        };

        // Filtros para Pedidos
        window.applyFiltersPedidos = async function() {
            const filters = {
                startDate: document.getElementById('startDatePed').value,
                endDate: document.getElementById('endDatePed').value,
                fornecedor: document.getElementById('supplierFilterPed').value,
                numero: document.getElementById('numeroFilterPed').value,
                observacao: document.getElementById('observacaoFilterPed').value,
                status: document.getElementById('statusFilterPed').value,
                ocultarRecebidos: document.getElementById('ocultarRecebidosPed').checked
            };

            try {
                const pedidosSnap = await getDocs(
                    query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"))
                );
                let filteredPedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                if (filters.startDate) {
                    const startDate = new Date(filters.startDate);
                    startDate.setHours(0, 0, 0, 0);
                    filteredPedidos = filteredPedidos.filter(pedido => {
                        const pedidoDate = pedido.dataCriacao && typeof pedido.dataCriacao.seconds === 'number' ?
                            new Date(pedido.dataCriacao.seconds * 1000) : null;
                        return pedidoDate && pedidoDate >= startDate;
                    });
                }

                if (filters.endDate) {
                    const endDate = new Date(filters.endDate);
                    endDate.setHours(23, 59, 59);
                    filteredPedidos = filteredPedidos.filter(pedido => {
                        const pedidoDate = pedido.dataCriacao && typeof pedido.dataCriacao.seconds === 'number' ?
                            new Date(pedido.dataCriacao.seconds * 1000) : null;
                        return pedidoDate && pedidoDate <= endDate;
                    });
                }

                if (filters.fornecedor) {
                    filteredPedidos = filteredPedidos.filter(pedido => pedido.fornecedorId === filters.fornecedor);
                }

                if (filters.numero) {
                    filteredPedidos = filteredPedidos.filter(pedido =>
                        (pedido.numero || '').includes(filters.numero)
                    );
                }

                if (filters.observacao) {
                    filteredPedidos = filteredPedidos.filter(pedido =>
                        (pedido.observacoes || '').includes(filters.observacao)
                    );
                }

                if (filters.status) {
                    filteredPedidos = filteredPedidos.filter(pedido => pedido.status === filters.status);
                }

                // Ocultar pedidos recebidos se a opção estiver marcada
                if (filters.ocultarRecebidos) {
                    filteredPedidos = filteredPedidos.filter(pedido => pedido.status !== 'RECEBIDO');
                }

                pedidosCompra = filteredPedidos;
                renderPedidos();
                showNotification(`${filteredPedidos.length} pedidos encontrados`, 'success');

            } catch (error) {
                console.error('Erro ao aplicar filtros:', error);
                showNotification('Erro ao aplicar filtros', 'error');
            }
        };

        window.clearFiltersPedidos = function() {
            document.getElementById('startDatePed').value = '';
            document.getElementById('endDatePed').value = '';
            document.getElementById('supplierFilterPed').value = '';
            document.getElementById('numeroFilterPed').value = '';
            document.getElementById('observacaoFilterPed').value = '';
            document.getElementById('statusFilterPed').value = '';
            document.getElementById('ocultarRecebidosPed').checked = true; // Manter ocultar recebidos como padrão
            renderPedidos();
        };

        // ===== FUNÇÕES PARA AÇÕES RÁPIDAS DO GESTOR =====

        // Mostrar apenas pedidos pendentes de aprovação
        window.showOnlyPendingApproval = function() {
            // Garantir que aprovados estejam ocultos
            showAprovados = false;
            showRecebidos = false;
            showCancelados = false;

            // Atualizar botões
            const btnAprovados = document.getElementById('toggleAprovadosPed');
            const btnRecebidos = document.getElementById('toggleRecebidosPed');
            const btnCancelados = document.getElementById('toggleCanceladosPed');

            if (btnAprovados) {
                btnAprovados.classList.remove('active');
                btnAprovados.innerHTML = '<i class="fas fa-check"></i> Mostrar Aprovados';
            }
            if (btnRecebidos) {
                btnRecebidos.classList.remove('active');
                btnRecebidos.innerHTML = '<i class="fas fa-eye-slash"></i> Mostrar Recebidos';
            }
            if (btnCancelados) {
                btnCancelados.classList.remove('active');
                btnCancelados.innerHTML = '<i class="fas fa-ban"></i> Mostrar Cancelados';
            }

            // Aplicar filtros
            document.getElementById('statusFilterPed').value = '';
            document.getElementById('ocultarRecebidosPed').checked = true;
            renderPedidos();
            updateApprovalDashboard();
            showNotification('Exibindo apenas pedidos pendentes de aprovação', 'info');
        };

        // Mostrar pedidos de alto valor
        window.showHighValueOrders = function() {
            // Filtrar pedidos com valor > R$ 10.000
            const filteredPedidos = pedidosCompra.filter(pedido =>
                (pedido.valorTotal || 0) > 10000 &&
                ['PENDENTE', 'ABERTO'].includes(pedido.status)
            );

            pedidosCompra = filteredPedidos;
            renderPedidos();
            showNotification(`${filteredPedidos.length} pedidos de alto valor encontrados`, 'warning');
        };

        // Mostrar pedidos urgentes
        window.showUrgentOrders = function() {
            const hoje = new Date();
            const filteredPedidos = pedidosCompra.filter(pedido => {
                if (!pedido.dataEntregaPrevista || !['PENDENTE', 'ABERTO'].includes(pedido.status)) return false;

                const dataEntrega = new Date(pedido.dataEntregaPrevista.seconds * 1000);
                const diffDays = Math.ceil((dataEntrega - hoje) / (1000 * 60 * 60 * 24));

                return diffDays <= 3; // Urgente se entrega em 3 dias ou menos
            });

            pedidosCompra = filteredPedidos;
            renderPedidos();
            showNotification(`${filteredPedidos.length} pedidos urgentes encontrados`, 'danger');
        };

        // Aprovar todos os pedidos visíveis
        window.approveAllVisible = async function() {
            const visiblePedidos = pedidosCompra.filter(pedido =>
                ['PENDENTE', 'ABERTO'].includes(pedido.status)
            );

            if (visiblePedidos.length === 0) {
                showNotification('Nenhum pedido pendente visível para aprovação', 'warning');
                return;
            }

            const confirmacao = confirm(`Confirma a aprovação de TODOS os ${visiblePedidos.length} pedidos visíveis?`);
            if (!confirmacao) return;

            try {
                let aprovados = 0;
                let erros = 0;

                for (const pedido of visiblePedidos) {
                    try {
                        await updateDoc(doc(db, "pedidosCompra", pedido.id), {
                            status: 'APROVADO',
                            dataAprovacao: new Date(),
                            aprovadoPor: currentUser?.nome || 'Sistema',
                            uidAprovacao: currentUser?.uid || 'sistema'
                        });
                        aprovados++;
                    } catch (error) {
                        console.error(`Erro ao aprovar pedido ${pedido.id}:`, error);
                        erros++;
                    }
                }

                if (erros === 0) {
                    showNotification(`${aprovados} pedidos aprovados com sucesso!`, 'success');
                } else {
                    showNotification(`${aprovados} aprovados, ${erros} erro(s)`, 'warning');
                }

                await loadInitialData();
                updateApprovalDashboard();

            } catch (error) {
                console.error('Erro na aprovação em massa:', error);
                showNotification('Erro na aprovação em massa', 'error');
            }
        };

        // Exportar relatório de aprovação
        window.exportApprovalReport = function() {
            const hoje = new Date();
            const dataFormatada = hoje.toLocaleDateString('pt-BR');

            let csv = 'Número,Data,Fornecedor,Valor,Status,Aprovado Por,Data Aprovação\n';

            pedidosCompra.forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const dataAprovacao = pedido.dataAprovacao ?
                    new Date(pedido.dataAprovacao.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A';

                csv += `${pedido.numero || 'N/A'},`;
                csv += `${pedido.dataCriacao ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A'},`;
                csv += `${fornecedor?.razaoSocial || 'N/A'},`;
                csv += `R$ ${(pedido.valorTotal || 0).toFixed(2)},`;
                csv += `${pedido.status},`;
                csv += `${pedido.aprovadoPor || 'N/A'},`;
                csv += `${dataAprovacao}\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `relatorio_aprovacao_${dataFormatada.replace(/\//g, '-')}.csv`;
            link.click();

            showNotification('Relatório de aprovação exportado com sucesso!', 'success');
        };

        // Atualizar dashboard de aprovação
        function updateApprovalDashboard() {
            const pendentes = pedidosCompra.filter(p => ['PENDENTE', 'ABERTO'].includes(p.status)).length;
            const altoValor = pedidosCompra.filter(p =>
                ['PENDENTE', 'ABERTO'].includes(p.status) && (p.valorTotal || 0) > 10000
            ).length;

            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0);
            const aprovadosHoje = pedidosCompra.filter(p => {
                if (p.status !== 'APROVADO' || !p.dataAprovacao) return false;
                const dataAprovacao = new Date(p.dataAprovacao.seconds * 1000);
                dataAprovacao.setHours(0, 0, 0, 0);
                return dataAprovacao.getTime() === hoje.getTime();
            }).length;

            const valorTotalPendente = pedidosCompra
                .filter(p => ['PENDENTE', 'ABERTO'].includes(p.status))
                .reduce((total, p) => total + (p.valorTotal || 0), 0);

            document.getElementById('pendingCount').textContent = pendentes;
            document.getElementById('highValueCount').textContent = altoValor;
            document.getElementById('approvedTodayCount').textContent = aprovadosHoje;
            document.getElementById('totalValuePending').textContent = `R$ ${valorTotalPendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        }

        // Função para carregar dados nos selects de filtros
        function carregarDadosFiltros() {
            // Carregar departamentos
            const departmentSelect = document.getElementById('departmentFilterSol');
            if (departmentSelect && setores.length > 0) {
                departmentSelect.innerHTML = '<option value="">Todos</option>';
                setores
                    .filter(setor => setor.status === 'Ativo')
                    .sort((a, b) => a.nome.localeCompare(b.nome))
                    .forEach(setor => {
                        const option = document.createElement('option');
                        option.value = setor.id;
                        option.textContent = setor.nome;
                        departmentSelect.appendChild(option);
                    });
            }

            // Carregar fornecedores para cotações
            const fornecedorCotSelect = document.getElementById('fornecedorFilterCot');
            if (fornecedorCotSelect && fornecedores.length > 0) {
                fornecedorCotSelect.innerHTML = '<option value="">Todos</option>';
                fornecedores.forEach(fornecedor => {
                    const option = document.createElement('option');
                    option.value = fornecedor.id;
                    option.textContent = fornecedor.razaoSocial || fornecedor.nome;
                    fornecedorCotSelect.appendChild(option);
                });
            }

            // Carregar fornecedores para pedidos
            const fornecedorPedSelect = document.getElementById('supplierFilterPed');
            if (fornecedorPedSelect && fornecedores.length > 0) {
                fornecedorPedSelect.innerHTML = '<option value="">Todos</option>';
                fornecedores.forEach(fornecedor => {
                    const option = document.createElement('option');
                    option.value = fornecedor.id;
                    option.textContent = fornecedor.razaoSocial || fornecedor.nome;
                    fornecedorPedSelect.appendChild(option);
                });
            }

            // Carregar solicitações para cotações
            const solicitacaoSelect = document.getElementById('solicitacaoFilterCot');
            if (solicitacaoSelect && solicitacoes.length > 0) {
                solicitacaoSelect.innerHTML = '<option value="">Todas</option>';
                solicitacoes.forEach(sol => {
                    const option = document.createElement('option');
                    option.value = sol.id;
                    option.textContent = `${sol.numero || 'N/A'} - ${sol.solicitante || 'N/A'}`;
                    solicitacaoSelect.appendChild(option);
                });
            }
        }

        // Funções específicas do sistema de produção
        function switchToDemo() {
            if(confirm('🎭 Deseja acessar o modo demonstração?\n\nNo modo demo você pode:\n✅ Explorar funcionalidades sem riscos\n✅ Ver dados simulados para apresentação\n✅ Treinar usuários com segurança\n✅ Testar interface sem afetar dados reais\n\n⚠️ Nenhuma ação no demo afetará dados reais')) {
                window.location.href = 'gestao_compras.html';
            }
        }

        function showProductionHelp() {
            showProductionNotification(`🚀 SISTEMA DE PRODUÇÃO ATIVO

📊 FUNCIONALIDADES REAIS:
• Dados persistentes no Firebase
• Integração completa entre módulos
• Relatórios com dados reais
• Ações que afetam o sistema

⚠️ IMPORTANTE:
• Todas as ações são permanentes
• Dados são salvos automaticamente
• Integra com outros módulos do sistema
• Gera histórico de auditoria

💡 DICAS:
• Use filtros para encontrar dados específicos
• Ações em lote aceleram o trabalho
• Verifique sempre antes de aprovar
• Mantenha backup dos dados importantes

🎭 Para treinar ou demonstrar, use o Modo Demo`, 'info');
        }

        // Função para mostrar notificações do sistema de produção
        function showProductionNotification(message, type = 'info') {
            // Remover notificação existente
            const existing = document.querySelector('.production-notification');
            if (existing) existing.remove();

            const notification = document.createElement('div');
            notification.className = `production-notification production-notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 20px 25px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 2000;
                max-width: 450px;
                font-weight: 500;
                line-height: 1.4;
                animation: slideInRight 0.3s ease;
                white-space: pre-line;
                border-left: 4px solid rgba(255,255,255,0.3);
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 10px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}" style="font-size: 20px; margin-top: 2px;"></i>
                    <div style="flex: 1;">
                        ${message}
                        <div style="margin-top: 15px; text-align: right;">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 5px 10px; border-radius: 5px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-times"></i> Fechar
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remover após 10 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 10000);
        }

        // Adicionar estilos de animação se não existirem
        if (!document.querySelector('#production-animations')) {
            const style = document.createElement('style');
            style.id = 'production-animations';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        // Mostrar notificação de boas-vindas do sistema real
        setTimeout(() => {
            showProductionNotification(`🚀 Sistema de Produção Ativo!

Você está no ambiente real com dados persistentes.

💡 Todas as ações aqui são permanentes
📊 Dados integrados com outros módulos
🔒 Sistema com auditoria completa

🎭 Para demonstrações, use o Modo Demo`, 'success');
        }, 1500);
    </script>
</body>
</html>
