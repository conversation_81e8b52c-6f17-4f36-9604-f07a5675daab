// Serviço inteligente para entrada de materiais baseado em parâmetros
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  runTransaction, 
  addDoc, 
  updateDoc, 
  getDoc,
  getDocs,
  query,
  where,
  Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class MaterialEntryService {
  
  /**
   * Processa entrada de material baseado nos parâmetros do sistema
   * @param {Object} entryData - Dados da entrada de material
   * @returns {Promise<Object>} Resultado do processamento
   */
  static async processEntry(entryData) {
    try {
      // 1. Carregar parâmetros do sistema
      const config = await this.loadSystemConfig();
      
      // 2. Determinar destino do material
      const destination = await this.determineDestination(entryData, config);
      
      // 3. Processar entrada baseada no destino
      const result = await this.executeEntry(entryData, destination, config);
      
      console.log('Entrada processada:', result);
      return result;
      
    } catch (error) {
      console.error('Erro no processamento de entrada:', error);
      throw new Error(`Falha no processamento: ${error.message}`);
    }
  }
  
  /**
   * Carrega configurações do sistema
   */
  static async loadSystemConfig() {
    try {
      const configDoc = await getDoc(doc(db, "parametros", "sistema"));

      if (!configDoc.exists()) {
        // Retornar configuração padrão se não existir
        return {
          configuracaoSistema: {
            controleQualidade: false,
            armazemQualidade: false,
            inspecaoRecebimento: 'manual'
          }
        };
      }

      const data = configDoc.data();

      // ✅ Estrutura corrigida para usar parametros organizados por categoria
      return {
        configuracaoSistema: {
          // Parâmetros de qualidade
          controleQualidade: data.parametros_qualidade?.controleQualidade ||
                           data.configuracaoSistema?.controleQualidade || false,
          inspecaoRecebimento: data.parametros_qualidade?.inspecaoRecebimento ||
                             data.configuracaoSistema?.inspecaoRecebimento || 'manual',
          armazemQualidade: data.parametros_qualidade?.armazemQualidade ||
                          data.configuracaoSistema?.armazemQualidade || false,

          // Parâmetros de estoque
          toleranciaRecebimento: data.parametros_estoque?.toleranciaRecebimento || 10,
          toleranciaPreco: data.parametros_estoque?.toleranciaPreco || 5,
          permitirRecebimentoParcial: data.parametros_estoque?.permitirRecebimentoParcial !== false,

          // Parâmetros de frete
          ratearFreteNoCusto: data.parametros_frete?.ratearFreteNoCusto !== false,
          criterioRateioFrete: data.parametros_frete?.criterioRateioFrete || 'valor'
        },

        // Manter estrutura original para compatibilidade
        ...data
      };

    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
      throw new Error('Falha ao carregar configurações do sistema');
    }
  }
  
  /**
   * Determina o destino do material baseado nos parâmetros
   */
  static async determineDestination(entryData, config) {
    const conf = config.configuracaoSistema || {};
    
    // Se módulo de qualidade está desabilitado, vai direto para estoque
    if (!conf.controleQualidade) {
      return {
        type: 'DIRECT_STOCK',
        warehouse: await this.getDestinationWarehouse(entryData),
        reason: 'Módulo de qualidade desabilitado'
      };
    }
    
    // Se não tem armazém de qualidade, vai direto para estoque
    if (!conf.armazemQualidade) {
      return {
        type: 'DIRECT_STOCK',
        warehouse: await this.getDestinationWarehouse(entryData),
        reason: 'Armazém de qualidade desabilitado'
      };
    }
    
    // Verificar política de inspeção
    const inspectionPolicy = conf.inspecaoRecebimento || 'manual';
    
    switch (inspectionPolicy) {
      case 'todos':
        return {
          type: 'QUALITY_INSPECTION',
          warehouse: await this.getQualityWarehouse(),
          reason: 'Inspeção obrigatória para todos os materiais'
        };
        
      case 'criticos':
        const isCritical = await this.isCriticalMaterial(entryData.produtoId);
        return isCritical ? {
          type: 'QUALITY_INSPECTION',
          warehouse: await this.getQualityWarehouse(),
          reason: 'Material crítico - inspeção obrigatória'
        } : {
          type: 'DIRECT_STOCK',
          warehouse: await this.getDestinationWarehouse(entryData),
          reason: 'Material não crítico - direto para estoque'
        };
        
      case 'manual':
        // Verificar se produto tem inspeção definida individualmente
        const productInspection = await this.getProductInspectionSetting(entryData.produtoId);
        return productInspection ? {
          type: 'QUALITY_INSPECTION',
          warehouse: await this.getQualityWarehouse(),
          reason: 'Produto configurado para inspeção'
        } : {
          type: 'DIRECT_STOCK',
          warehouse: await this.getDestinationWarehouse(entryData),
          reason: 'Produto sem necessidade de inspeção'
        };
        
      default:
        return {
          type: 'DIRECT_STOCK',
          warehouse: await this.getDestinationWarehouse(entryData),
          reason: 'Configuração padrão'
        };
    }
  }
  
  /**
   * Executa a entrada baseada no destino determinado
   */
  static async executeEntry(entryData, destination, config) {
    return await runTransaction(db, async (transaction) => {
      if (destination.type === 'DIRECT_STOCK') {
        return await this.processDirectStock(transaction, entryData, destination);
      } else {
        return await this.processQualityInspection(transaction, entryData, destination);
      }
    });
  }
  
  /**
   * Processa entrada direta no estoque
   */
  static async processDirectStock(transaction, entryData, destination) {
    // 1. Buscar ou criar estoque
    const estoqueQuery = query(
      collection(db, "estoques"),
      where("produtoId", "==", entryData.produtoId),
      where("armazemId", "==", destination.warehouse.id)
    );
    const estoqueSnap = await getDocs(estoqueQuery);
    
    let estoqueRef;
    let saldoAtual = 0;
    
    if (!estoqueSnap.empty) {
      estoqueRef = estoqueSnap.docs[0].ref;
      saldoAtual = estoqueSnap.docs[0].data().saldo || 0;
      
      transaction.update(estoqueRef, {
        saldo: saldoAtual + entryData.quantidade,
        ultimaMovimentacao: Timestamp.now()
      });
    } else {
      estoqueRef = doc(collection(db, "estoques"));
      transaction.set(estoqueRef, {
        produtoId: entryData.produtoId,
        armazemId: destination.warehouse.id,
        saldo: entryData.quantidade,
        saldoReservado: 0,
        ultimaMovimentacao: Timestamp.now()
      });
    }
    
    // 2. Registrar movimentação
    const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
    transaction.set(movimentacaoRef, {
      produtoId: entryData.produtoId,
      armazemId: destination.warehouse.id,
      tipo: 'ENTRADA',
      quantidade: entryData.quantidade,
      tipoDocumento: 'RECEBIMENTO',
      numeroDocumento: entryData.numeroNF,
      observacoes: `Recebimento direto - ${destination.reason}`,
      dataHora: Timestamp.now(),
      saldoAnterior: saldoAtual,
      saldoPosterior: saldoAtual + entryData.quantidade,
      recebimentoId: entryData.recebimentoId
    });
    
    return {
      type: 'DIRECT_STOCK',
      warehouse: destination.warehouse,
      saldoFinal: saldoAtual + entryData.quantidade,
      movimentacaoId: movimentacaoRef.id,
      message: `Material recebido diretamente no ${destination.warehouse.codigo}`
    };
  }
  
  /**
   * Processa entrada para inspeção de qualidade
   */
  static async processQualityInspection(transaction, entryData, destination) {
    // 1. Criar registro na fila de qualidade
    const qualidadeRef = doc(collection(db, "estoqueQualidade"));
    transaction.set(qualidadeRef, {
      produtoId: entryData.produtoId,
      codigo: entryData.codigo,
      descricao: entryData.descricao,
      quantidade: entryData.quantidade,
      unidade: entryData.unidade,
      armazemDestinoId: destination.warehouse.id,
      status: 'PENDENTE',
      dataEntrada: Timestamp.now(),
      recebimentoId: entryData.recebimentoId,
      numeroNF: entryData.numeroNF,
      loteFornecedor: entryData.loteFornecedor,
      loteInterno: entryData.loteInterno,
      motivoInspecao: destination.reason
    });
    
    // 2. Registrar movimentação para qualidade
    const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
    transaction.set(movimentacaoRef, {
      produtoId: entryData.produtoId,
      armazemId: destination.warehouse.id,
      tipo: 'ENTRADA',
      quantidade: entryData.quantidade,
      tipoDocumento: 'QUALIDADE',
      numeroDocumento: entryData.numeroNF,
      observacoes: `Entrada para inspeção - ${destination.reason}`,
      dataHora: Timestamp.now(),
      qualidadeId: qualidadeRef.id,
      status: 'PENDENTE_INSPECAO'
    });
    
    return {
      type: 'QUALITY_INSPECTION',
      warehouse: destination.warehouse,
      qualidadeId: qualidadeRef.id,
      movimentacaoId: movimentacaoRef.id,
      message: `Material enviado para inspeção de qualidade - ${destination.reason}`
    };
  }
  
  /**
   * Obtém armazém de destino baseado no produto ou configuração
   */
  static async getDestinationWarehouse(entryData) {
    // 1. Tentar usar armazém padrão do produto
    if (entryData.produtoId) {
      const produtoDoc = await getDoc(doc(db, "produtos", entryData.produtoId));
      if (produtoDoc.exists() && produtoDoc.data().armazemPadraoId) {
        const armazemDoc = await getDoc(doc(db, "armazens", produtoDoc.data().armazemPadraoId));
        if (armazemDoc.exists()) {
          return { id: armazemDoc.id, ...armazemDoc.data() };
        }
      }
    }
    
    // 2. Usar armazém selecionado pelo usuário
    if (entryData.armazemId) {
      const armazemDoc = await getDoc(doc(db, "armazens", entryData.armazemId));
      if (armazemDoc.exists()) {
        return { id: armazemDoc.id, ...armazemDoc.data() };
      }
    }
    
    // 3. Buscar armazém principal como fallback
    const armazensSnap = await getDocs(collection(db, "armazens"));
    const almoxarifado = armazensSnap.docs.find(doc => {
      const data = doc.data();
      return data.codigo === 'ALM01' || data.tipo === 'ALMOXARIFADO';
    });
    
    if (almoxarifado) {
      return { id: almoxarifado.id, ...almoxarifado.data() };
    }
    
    throw new Error('Nenhum armazém de destino encontrado');
  }
  
  /**
   * Obtém armazém de qualidade
   */
  static async getQualityWarehouse() {
    const armazensSnap = await getDocs(collection(db, "armazens"));
    const qualidade = armazensSnap.docs.find(doc => {
      const data = doc.data();
      return data.codigo === 'QUAL01' || data.tipo === 'QUALIDADE';
    });
    
    if (qualidade) {
      return { id: qualidade.id, ...qualidade.data() };
    }
    
    throw new Error('Armazém de qualidade não encontrado');
  }
  
  /**
   * Verifica se material é crítico
   */
  static async isCriticalMaterial(produtoId) {
    try {
      const produtoDoc = await getDoc(doc(db, "produtos", produtoId));
      if (produtoDoc.exists()) {
        const produto = produtoDoc.data();
        return produto.critico === true || produto.tipo === 'CRITICO';
      }
      return false;
    } catch (error) {
      console.error('Erro ao verificar criticidade:', error);
      return false;
    }
  }
  
  /**
   * Obtém configuração de inspeção do produto
   */
  static async getProductInspectionSetting(produtoId) {
    try {
      const produtoDoc = await getDoc(doc(db, "produtos", produtoId));
      if (produtoDoc.exists()) {
        const produto = produtoDoc.data();
        return produto.inspecaoRecebimento === true || produto.inspecaoRecebimento === 'sim';
      }
      return false;
    } catch (error) {
      console.error('Erro ao verificar inspeção do produto:', error);
      return false;
    }
  }
  
  /**
   * Aprova material da qualidade e transfere para estoque
   */
  static async approveQualityMaterial(qualidadeId, aprovadoPor) {
    try {
      return await runTransaction(db, async (transaction) => {
        // 1. Buscar item da qualidade
        const qualidadeRef = doc(db, "estoqueQualidade", qualidadeId);
        const qualidadeDoc = await transaction.get(qualidadeRef);
        
        if (!qualidadeDoc.exists()) {
          throw new Error('Item de qualidade não encontrado');
        }
        
        const item = qualidadeDoc.data();
        
        // 2. Atualizar status para aprovado
        transaction.update(qualidadeRef, {
          status: 'APROVADO',
          dataAprovacao: Timestamp.now(),
          aprovadoPor: aprovadoPor
        });
        
        // 3. Transferir para estoque definitivo
        const armazemDestino = await this.getDestinationWarehouse({
          produtoId: item.produtoId,
          armazemId: item.armazemDestinoId
        });
        
        // 4. Buscar ou criar estoque
        const estoqueQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", item.produtoId),
          where("armazemId", "==", armazemDestino.id)
        );
        const estoqueSnap = await getDocs(estoqueQuery);
        
        let saldoAtual = 0;
        if (!estoqueSnap.empty) {
          const estoqueDoc = estoqueSnap.docs[0];
          saldoAtual = estoqueDoc.data().saldo || 0;
          
          transaction.update(estoqueDoc.ref, {
            saldo: saldoAtual + item.quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          const novoEstoqueRef = doc(collection(db, "estoques"));
          transaction.set(novoEstoqueRef, {
            produtoId: item.produtoId,
            armazemId: armazemDestino.id,
            saldo: item.quantidade,
            saldoReservado: 0,
            ultimaMovimentacao: Timestamp.now()
          });
        }
        
        // 5. Registrar movimentação de aprovação
        const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movimentacaoRef, {
          produtoId: item.produtoId,
          armazemId: armazemDestino.id,
          tipo: 'ENTRADA',
          quantidade: item.quantidade,
          tipoDocumento: 'APROVACAO_QUALIDADE',
          numeroDocumento: item.numeroNF,
          observacoes: `Aprovação qualidade - Lote: ${item.loteInterno}`,
          dataHora: Timestamp.now(),
          saldoAnterior: saldoAtual,
          saldoPosterior: saldoAtual + item.quantidade,
          qualidadeId: qualidadeId,
          aprovadoPor: aprovadoPor
        });
        
        return {
          success: true,
          armazem: armazemDestino,
          saldoFinal: saldoAtual + item.quantidade,
          message: `Material aprovado e transferido para ${armazemDestino.codigo}`
        };
      });
      
    } catch (error) {
      console.error('Erro na aprovação:', error);
      throw new Error(`Falha na aprovação: ${error.message}`);
    }
  }
  
  /**
   * Reprova material da qualidade
   */
  static async rejectQualityMaterial(qualidadeId, reprovadoPor, motivo) {
    try {
      const qualidadeRef = doc(db, "estoqueQualidade", qualidadeId);
      
      await updateDoc(qualidadeRef, {
        status: 'REJEITADO',
        dataRejeicao: Timestamp.now(),
        reprovadoPor: reprovadoPor,
        motivoRejeicao: motivo
      });
      
      return {
        success: true,
        message: 'Material rejeitado na inspeção de qualidade'
      };
      
    } catch (error) {
      console.error('Erro na rejeição:', error);
      throw new Error(`Falha na rejeição: ${error.message}`);
    }
  }
}

// Exportar para uso global
window.MaterialEntryService = MaterialEntryService;
