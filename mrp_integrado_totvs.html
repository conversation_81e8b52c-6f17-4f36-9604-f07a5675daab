<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MRP Integrado - TOTVS Style</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2.2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .mrp-dashboard {
            padding: 30px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
        }

        .kpi-card.critical {
            border-left-color: #e74c3c;
        }

        .kpi-card.warning {
            border-left-color: #f39c12;
        }

        .kpi-card.success {
            border-left-color: #27ae60;
        }

        .kpi-card.info {
            border-left-color: #3498db;
        }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .kpi-title {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-transform: uppercase;
            font-weight: bold;
        }

        .kpi-icon {
            font-size: 1.5rem;
        }

        .kpi-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .kpi-subtitle {
            font-size: 0.9rem;
            color: #95a5a6;
            margin-top: 5px;
        }

        .mrp-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .mrp-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }

        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .suggestion-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .suggestion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .suggestion-product {
            font-weight: bold;
            color: #2c3e50;
        }

        .suggestion-priority {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .priority-high {
            background: #ffebee;
            color: #c62828;
        }

        .priority-medium {
            background: #fff3e0;
            color: #ef6c00;
        }

        .priority-low {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .suggestion-details {
            font-size: 0.9rem;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .suggestion-actions {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 5px 12px;
            font-size: 0.8rem;
        }

        .timeline-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 25px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #3498db;
        }

        .timeline-date {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #3498db;
        }

        .filters-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 0.9rem;
            color: #2c3e50;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group select,
        .form-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-cogs"></i>
                MRP Integrado
            </h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="executarMRP()">
                    <i class="fas fa-play"></i> Executar MRP
                </button>
                <button class="btn btn-success" onclick="gerarSolicitacoes()">
                    <i class="fas fa-file-plus"></i> Gerar Solicitações
                </button>
                <button class="btn btn-warning" onclick="exportarRelatorio()">
                    <i class="fas fa-download"></i> Exportar
                </button>
            </div>
        </div>

        <div class="mrp-dashboard">
            <!-- Filtros -->
            <div class="filters-section">
                <div class="filters-grid">
                    <div class="form-group">
                        <label>Período de Análise</label>
                        <select id="periodoAnalise">
                            <option value="30">30 dias</option>
                            <option value="60" selected>60 dias</option>
                            <option value="90">90 dias</option>
                            <option value="120">120 dias</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Família de Produtos</label>
                        <select id="familiaFiltro">
                            <option value="">Todas</option>
                            <option value="MP">Matéria Prima</option>
                            <option value="COMP">Componentes</option>
                            <option value="EMBAL">Embalagens</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Criticidade</label>
                        <select id="criticidadeFiltro">
                            <option value="">Todas</option>
                            <option value="ALTA">Alta</option>
                            <option value="MEDIA">Média</option>
                            <option value="BAIXA">Baixa</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="aplicarFiltros()">
                            <i class="fas fa-filter"></i> Aplicar
                        </button>
                    </div>
                </div>
            </div>

            <!-- KPIs -->
            <div class="kpi-grid">
                <div class="kpi-card critical">
                    <div class="kpi-header">
                        <div class="kpi-title">Itens Críticos</div>
                        <i class="fas fa-exclamation-triangle kpi-icon" style="color: #e74c3c;"></i>
                    </div>
                    <div class="kpi-value" id="itensCriticos">23</div>
                    <div class="kpi-subtitle">Estoque zerado ou negativo</div>
                </div>

                <div class="kpi-card warning">
                    <div class="kpi-header">
                        <div class="kpi-title">Necessidades</div>
                        <i class="fas fa-shopping-cart kpi-icon" style="color: #f39c12;"></i>
                    </div>
                    <div class="kpi-value" id="necessidadesTotal">R$ 125.430</div>
                    <div class="kpi-subtitle">Valor total a comprar</div>
                </div>

                <div class="kpi-card info">
                    <div class="kpi-header">
                        <div class="kpi-title">Lead Time Médio</div>
                        <i class="fas fa-clock kpi-icon" style="color: #3498db;"></i>
                    </div>
                    <div class="kpi-value" id="leadTimeMedio">15</div>
                    <div class="kpi-subtitle">dias úteis</div>
                </div>

                <div class="kpi-card success">
                    <div class="kpi-header">
                        <div class="kpi-title">Cobertura Média</div>
                        <i class="fas fa-chart-line kpi-icon" style="color: #27ae60;"></i>
                    </div>
                    <div class="kpi-value" id="coberturaMedia">45</div>
                    <div class="kpi-subtitle">dias de estoque</div>
                </div>
            </div>

            <!-- Seções MRP -->
            <div class="mrp-sections">
                <!-- Sugestões de Compra -->
                <div class="mrp-section">
                    <div class="section-title">
                        <i class="fas fa-lightbulb"></i>
                        Sugestões de Compra
                    </div>
                    <div id="sugestoesCompra">
                        <!-- Sugestões carregadas dinamicamente -->
                    </div>
                </div>

                <!-- Análise de Fornecedores -->
                <div class="mrp-section">
                    <div class="section-title">
                        <i class="fas fa-truck"></i>
                        Análise de Fornecedores
                    </div>
                    <div id="analiseFornecedores">
                        <!-- Análise carregada dinamicamente -->
                    </div>
                </div>
            </div>

            <!-- Timeline de Necessidades -->
            <div class="timeline-container">
                <div class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    Timeline de Necessidades
                </div>
                <div class="timeline" id="timelineNecessidades">
                    <!-- Timeline carregada dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // ===================================================================
        // MRP INTEGRADO TOTVS - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { collection, getDocs, addDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        let produtos = [];
        let estoques = [];
        let ordensProducao = [];
        let fornecedores = [];

        async function carregarDados() {
            try {
                const [produtosSnap, estoquesSnap, ordensSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', { produtos: produtos.length, estoques: estoques.length });
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
            }
        }

        function renderSugestoesCompra() {
            const container = document.getElementById('sugestoesCompra');
            
            const sugestoes = [
                {
                    produto: 'Parafuso M6x20',
                    codigo: 'PAR001',
                    necessidade: 500,
                    estoque: 50,
                    leadTime: 7,
                    prioridade: 'high',
                    fornecedor: 'Parafusos SA'
                },
                {
                    produto: 'Chapa Aço 1020',
                    codigo: 'CHA001',
                    necessidade: 100,
                    estoque: 25,
                    leadTime: 15,
                    prioridade: 'medium',
                    fornecedor: 'Aços Ltda'
                },
                {
                    produto: 'Tinta Primer',
                    codigo: 'TIN001',
                    necessidade: 20,
                    estoque: 5,
                    leadTime: 10,
                    prioridade: 'low',
                    fornecedor: 'Tintas Brasil'
                }
            ];

            container.innerHTML = sugestoes.map(item => `
                <div class="suggestion-item">
                    <div class="suggestion-header">
                        <div class="suggestion-product">${item.produto} (${item.codigo})</div>
                        <div class="suggestion-priority priority-${item.prioridade}">
                            ${item.prioridade === 'high' ? 'Alta' : item.prioridade === 'medium' ? 'Média' : 'Baixa'}
                        </div>
                    </div>
                    <div class="suggestion-details">
                        Necessidade: ${item.necessidade} un | Estoque: ${item.estoque} un<br>
                        Lead Time: ${item.leadTime} dias | Fornecedor: ${item.fornecedor}
                    </div>
                    <div class="suggestion-actions">
                        <button class="btn btn-success btn-sm" onclick="criarSolicitacao('${item.codigo}')">
                            <i class="fas fa-plus"></i> Solicitar
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="verDetalhes('${item.codigo}')">
                            <i class="fas fa-eye"></i> Detalhes
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function renderTimelineNecessidades() {
            const container = document.getElementById('timelineNecessidades');
            
            const necessidades = [
                { data: '2024-01-15', descricao: 'Parafusos M6x20 - 500 unidades', tipo: 'critico' },
                { data: '2024-01-20', descricao: 'Chapa Aço 1020 - 100 unidades', tipo: 'normal' },
                { data: '2024-01-25', descricao: 'Tinta Primer - 20 litros', tipo: 'normal' },
                { data: '2024-02-01', descricao: 'Rolamentos SKF - 50 unidades', tipo: 'critico' }
            ];

            container.innerHTML = necessidades.map(item => `
                <div class="timeline-item">
                    <div class="timeline-date">${item.data}</div>
                    <div class="timeline-content">
                        <strong>${item.descricao}</strong>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${Math.random() * 100}%"></div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        window.executarMRP = async function() {
            alert('Executando análise MRP...\n\nProcessando:\n✓ Estruturas de produtos\n✓ Ordens de produção\n✓ Níveis de estoque\n✓ Lead times\n✓ Pontos de reposição\n\nAnálise concluída!');
            renderSugestoesCompra();
            renderTimelineNecessidades();
        };

        window.gerarSolicitacoes = function() {
            alert('Gerando solicitações automáticas...\n\n✓ 3 solicitações criadas\n✓ Fornecedores notificados\n✓ Workflow iniciado');
        };

        window.criarSolicitacao = function(codigo) {
            alert(`Criando solicitação para ${codigo}...\n\nSolicitação SC${Date.now()} criada com sucesso!`);
        };

        window.verDetalhes = function(codigo) {
            alert(`Detalhes do produto ${codigo}:\n\n• Estoque atual: 50 un\n• Ponto de reposição: 100 un\n• Estoque máximo: 500 un\n• Lead time: 7 dias\n• Fornecedor principal: Parafusos SA`);
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarDados();
            renderSugestoesCompra();
            renderTimelineNecessidades();
        });
    </script>
</body>
</html>
