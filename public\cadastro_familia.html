<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>👨‍👩‍👧‍👦 Cadastro de Família</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    /* ========================================
       🎨 CSS PADRONIZADO - CADASTRO FAMÍLIA
       Baseado em: gestao_compras_integrada.html
       ======================================== */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .form-container {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }

    .form-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
      display: block;
    }

    .form-control {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      width: 100%;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .families-table {
      width: 100%;
      border-collapse: collapse;
    }

    .families-table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .families-table th:hover {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    .families-table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .families-table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .edit-btn {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .delete-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .edit-btn:hover, .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 2px solid #e9ecef;
    }

    .search-container {
      margin-bottom: 20px;
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .search-container input {
      flex: 1;
      min-width: 250px;
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .search-container input:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .required::after {
      content: " *";
      color: #e74c3c;
      font-weight: bold;
    }

    .sort-indicator {
      margin-left: 8px;
      font-size: 14px;
      color: #ecf0f1;
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid;
      font-weight: 500;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: #17a2b8;
    }

    /* Responsividade */
    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .table-container {
        overflow-x: auto;
      }

      .search-container {
        flex-direction: column;
      }

      .search-container input {
        min-width: auto;
      }

      .form-actions {
        flex-direction: column;
      }

      .action-buttons {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>
        <i class="fas fa-sitemap"></i>
        Cadastro de Família
      </h1>
      <div class="header-actions">
        <button class="btn btn-warning" onclick="window.location.href='index.html'">
          <i class="fas fa-home"></i>
          Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- Formulário de Cadastro -->
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-plus-circle"></i>
          Cadastrar Nova Família
        </h2>
        <form id="familyForm">
          <input type="hidden" id="editingId">

          <div class="form-group">
            <label for="codigoFamilia" class="required">Código da Família</label>
            <input type="text" id="codigoFamilia" name="codigoFamilia" class="form-control" required>
          </div>

          <div class="form-group">
            <label for="nomeFamilia" class="required">Nome da Família</label>
            <input type="text" id="nomeFamilia" name="nomeFamilia" class="form-control" required>
          </div>

          <div class="form-group">
            <label for="grupo" class="required">Grupo</label>
            <select id="grupo" name="grupo" class="form-control" required>
              <option value="">Selecione um grupo</option>
            </select>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="cancelEdit()" id="cancelButton" style="display: none;">
              <i class="fas fa-times"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-success" id="submitButton">
              <i class="fas fa-save"></i>
              Cadastrar Família
            </button>
          </div>
        </form>
      </div>

      <!-- Lista de Famílias -->
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-list"></i>
          Famílias Cadastradas
        </h2>

        <div class="search-container">
          <input type="text" id="searchCodigo" class="form-control" placeholder="🔍 Pesquisar por Código" oninput="filterFamilies()">
          <input type="text" id="searchNome" class="form-control" placeholder="🔍 Pesquisar por Nome" oninput="filterFamilies()">
        </div>

        <div class="table-container">
          <table class="families-table">
            <thead>
              <tr>
                <th onclick="sortTable('codigoFamilia')">
                  <i class="fas fa-code"></i>
                  Código
                  <span id="sortCodigo" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('nomeFamilia')">
                  <i class="fas fa-tag"></i>
                  Nome
                  <span id="sortNome" class="sort-indicator"></span>
                </th>
                <th onclick="sortTable('grupo')">
                  <i class="fas fa-layer-group"></i>
                  Grupo
                  <span id="sortGrupo" class="sort-indicator"></span>
                </th>
                <th>
                  <i class="fas fa-cogs"></i>
                  Ações
                </th>
              </tr>
            </thead>
            <tbody id="familiesTableBody">
              <tr>
                <td colspan="4" style="text-align: center; padding: 40px;">
                  <i class="fas fa-spinner fa-spin"></i>
                  Carregando famílias...
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs, 
      doc, 
      updateDoc, 
      deleteDoc,
      query,
      where 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let familias = [];
    let grupos = [];
    let sortDirection = 'asc'; // Direção da ordenação
    let currentSortColumn = ''; // Coluna atual sendo ordenada

    // Função global para ordenação
    window.sortTable = function(sortBy) {
      if (sortBy === currentSortColumn) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        currentSortColumn = sortBy;
        sortDirection = 'asc';
      }

      if (sortBy === 'codigoFamilia') {
        familias.sort((a, b) => sortDirection === 'asc'
          ? a.codigoFamilia.localeCompare(b.codigoFamilia)
          : b.codigoFamilia.localeCompare(a.codigoFamilia));
      } else if (sortBy === 'nomeFamilia') {
        familias.sort((a, b) => sortDirection === 'asc'
          ? a.nomeFamilia.localeCompare(b.nomeFamilia)
          : b.nomeFamilia.localeCompare(a.nomeFamilia));
      } else if (sortBy === 'grupo') {
        familias.sort((a, b) => sortDirection === 'asc'
          ? a.grupo.localeCompare(b.grupo)
          : b.grupo.localeCompare(a.grupo));
      }

      updateSortIndicators(sortBy, sortDirection);
      displayFamilies();
    };

    // Função para atualizar os indicadores visuais
    function updateSortIndicators(column, direction) {
      document.getElementById('sortCodigo').innerHTML = '';
      document.getElementById('sortNome').innerHTML = '';
      document.getElementById('sortGrupo').innerHTML = '';

      if (column === 'codigoFamilia') {
        document.getElementById('sortCodigo').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'nomeFamilia') {
        document.getElementById('sortNome').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'grupo') {
        document.getElementById('sortGrupo').innerHTML = direction === 'asc' ? '▲' : '▼';
      }
    }

    window.onload = async function() {
      await loadData();
      displayFamilies();
    };

    async function loadData() {
      try {
        const [familiasSnapshot, gruposSnapshot] = await Promise.all([
          getDocs(collection(db, "familias")),
          getDocs(collection(db, "grupos"))
        ]);

        familias = [];
        familiasSnapshot.forEach((doc) => {
          familias.push({ id: doc.id, ...doc.data() });
        });

        grupos = [];
        gruposSnapshot.forEach((doc) => {
          grupos.push({ id: doc.id, ...doc.data() });
        });

        const grupoSelect = document.getElementById('grupo');
        grupoSelect.innerHTML = '<option value="">Selecione um grupo</option>';
        grupos.forEach(grupo => {
          grupoSelect.innerHTML += `<option value="${grupo.codigoGrupo}">${grupo.codigoGrupo} - ${grupo.nomeGrupo}</option>`;
        });
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados: " + error.message);
      }
    }

    function getGrupoNome(codigoGrupo) {
      const grupo = grupos.find(g => g.codigoGrupo === codigoGrupo);
      return grupo ? `${grupo.codigoGrupo} - ${grupo.nomeGrupo}` : 'N/A';
    }

    function displayFamilies(filteredFamilies = familias) {
      const tableBody = document.getElementById('familiesTableBody');
      tableBody.innerHTML = '';

      filteredFamilies.forEach(family => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><i class="fas fa-code"></i> ${family.codigoFamilia}</td>
          <td><i class="fas fa-tag"></i> ${family.nomeFamilia}</td>
          <td><i class="fas fa-layer-group"></i> ${getGrupoNome(family.grupo)}</td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editFamily('${family.id}')">
              <i class="fas fa-edit"></i> Editar
            </button>
            <button class="delete-btn" onclick="deleteFamily('${family.id}')">
              <i class="fas fa-trash"></i> Excluir
            </button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.filterFamilies = function() {
      const searchCodigo = document.getElementById('searchCodigo').value.toLowerCase();
      const searchNome = document.getElementById('searchNome').value.toLowerCase();

      let filteredFamilies = familias.filter(family => {
        const matchCodigo = family.codigoFamilia.toLowerCase().includes(searchCodigo);
        const matchNome = family.nomeFamilia.toLowerCase().includes(searchNome);
        return matchCodigo && matchNome;
      });

      // Aplicar ordenação aos dados filtrados, se houver uma coluna ordenada
      if (currentSortColumn) {
        if (currentSortColumn === 'codigoFamilia') {
          filteredFamilies.sort((a, b) => sortDirection === 'asc'
            ? a.codigoFamilia.localeCompare(b.codigoFamilia)
            : b.codigoFamilia.localeCompare(a.codigoFamilia));
        } else if (currentSortColumn === 'nomeFamilia') {
          filteredFamilies.sort((a, b) => sortDirection === 'asc'
            ? a.nomeFamilia.localeCompare(b.nomeFamilia)
            : b.nomeFamilia.localeCompare(a.nomeFamilia));
        } else if (currentSortColumn === 'grupo') {
          filteredFamilies.sort((a, b) => sortDirection === 'asc'
            ? a.grupo.localeCompare(b.grupo)
            : b.grupo.localeCompare(a.grupo));
        }
      }

      displayFamilies(filteredFamilies);
    };

    window.editFamily = function(familyId) {
      const family = familias.find(f => f.id === familyId);
      if (family) {
        document.getElementById('editingId').value = familyId;
        document.getElementById('codigoFamilia').value = family.codigoFamilia;
        document.getElementById('nomeFamilia').value = family.nomeFamilia;
        document.getElementById('grupo').value = family.grupo;
        
        document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Atualizar Família';
        document.getElementById('cancelButton').style.display = 'block';
      }
    };

    window.cancelEdit = function() {
      document.getElementById('familyForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Cadastrar Família';
      document.getElementById('cancelButton').style.display = 'none';
    };

    window.deleteFamily = async function(familyId) {
      if (confirm('Tem certeza que deseja excluir esta família?')) {
        try {
          await deleteDoc(doc(db, "familias", familyId));
          await loadData();
          displayFamilies();
          alert('Família excluída com sucesso!');
        } catch (error) {
          console.error("Erro ao excluir família:", error);
          alert("Erro ao excluir família: " + error.message);
        }
      }
    };

    document.getElementById('familyForm').addEventListener('submit', async function(event) {
      event.preventDefault();
      
      const grupo = document.getElementById('grupo').value;
      if (!grupo) {
        alert("Por favor, selecione um grupo.");
        return;
      }

      const formData = {
        codigoFamilia: document.getElementById('codigoFamilia').value,
        nomeFamilia: document.getElementById('nomeFamilia').value,
        grupo: grupo,
        dataCadastro: new Date()
      };

      const editingId = document.getElementById('editingId').value;

      try {
        if (editingId) {
          await updateDoc(doc(db, "familias", editingId), formData);
          alert("Família atualizada com sucesso!");
        } else {
          await addDoc(collection(db, "familias"), formData);
          alert("Família cadastrada com sucesso!");
        }

        await loadData();
        displayFamilies();
        event.target.reset();
        cancelEdit();
      } catch (error) {
        console.error("Erro ao salvar família:", error);
        alert("Erro ao salvar família: " + error.message);
      }
    });
  </script>
</body>
</html>