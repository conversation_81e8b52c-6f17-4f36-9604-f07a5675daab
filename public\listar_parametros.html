<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Listar Parâmetros do Sistema (Firestore)</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 30px; }
    textarea { width: 100%; height: 120px; margin-bottom: 10px; }
    pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
    .btn { padding: 8px 16px; background: #0854a0; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
    .btn:hover { background: #0a4d8c; }
    .section { margin-bottom: 30px; }
  </style>
</head>
<body>
  <h1>Listar Parâmetros do Sistema (Firestore)</h1>
  <div class="section">
    <h3>1. Suas credenciais do Firebase já estão preenchidas:</h3>
    <textarea id="firebaseConfig"></textarea>
    <button class="btn" onclick="initFirebase()">Conectar e Listar Parâmetros</button>
  </div>
  <div class="section">
    <h3>2. Parâmetros encontrados (documento 'sistema'):</h3>
    <pre id="parametros"></pre>
  </div>
  <script type="module">
    // ===================================================================
    // LISTAR PARÂMETROS - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
    // ===================================================================
    import { db, firebaseConfig } from './firebase-config.js';

    // Preencher campo com configuração centralizada
    document.getElementById('firebaseConfig').value = JSON.stringify(firebaseConfig, null, 2);

    // Importar funções do Firestore
    import { doc, getDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    window.initFirebase = async function() {
      try {
        document.getElementById('parametros').textContent = 'Carregando...';

        // Usar configuração centralizada
        const parametrosDoc = await getDoc(doc(db, 'parametros', 'sistema'));

        if (!parametrosDoc.exists()) {
          document.getElementById('parametros').textContent = 'Documento "parametros/sistema" não encontrado!';
          return;
        }

        document.getElementById('parametros').textContent = JSON.stringify(parametrosDoc.data(), null, 2);

      } catch (e) {
        document.getElementById('parametros').textContent = 'Erro: ' + e;
      }
    }
  </script>
</body>
</html>