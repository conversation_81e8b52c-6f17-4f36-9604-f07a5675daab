<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Produtos - Padronizado</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles/sistema-padronizado.css">
    <style>
        /* Estilos específicos apenas para esta página */
        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: var(--spacing-lg);
            min-height: 600px;
        }

        .sidebar {
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
        }

        .sidebar h3 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
            font-size: var(--font-size-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .products-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--bg-primary);
        }

        .product-item {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color var(--transition-fast);
        }

        .product-item:hover {
            background-color: var(--gray-100);
        }

        .product-item.selected {
            background-color: var(--primary-light);
            border-left: 4px solid var(--primary-color);
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-code {
            font-weight: var(--font-weight-semibold);
            color: var(--primary-color);
            font-size: var(--font-size-sm);
        }

        .product-desc {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .product-type {
            display: inline-block;
            padding: 2px 6px;
            border-radius: var(--border-radius-xl);
            font-size: 10px;
            font-weight: var(--font-weight-bold);
            margin-top: 4px;
        }

        .type-PA { background-color: #2196F3; color: white; }
        .type-SP { background-color: #FF9800; color: white; }
        .type-MP { background-color: #4CAF50; color: white; }
        .type-SV { background-color: #9C27B0; color: white; }

        .form-content {
            background-color: var(--bg-primary);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .section {
            margin-bottom: var(--spacing-xl);
            background-color: #fafbfc;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .section-header {
            background-color: var(--bg-secondary);
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-content {
            padding: var(--spacing-lg);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
        }

        .form-grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
        }

        .form-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: var(--spacing-lg);
        }

        .form-grid-4 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-lg);
        }

        .actions-bar {
            position: sticky;
            bottom: 0;
            background-color: var(--bg-primary);
            padding: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: var(--spacing-lg) calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-lg));
            border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        }

        @media (max-width: 992px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
            
            .sidebar {
                order: 2;
            }
            
            .form-content {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header usando o sistema padronizado -->
        <div class="header">
            <h1><i class="fas fa-box"></i> Cadastro de Produtos</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="salvarProduto()">
                    <i class="fas fa-save"></i> Salvar
                </button>
                <button class="btn btn-primary" onclick="novoProduto()">
                    <i class="fas fa-plus"></i> Novo
                </button>
                <button class="btn btn-warning" onclick="duplicarProduto()">
                    <i class="fas fa-copy"></i> Duplicar
                </button>
                <a href="index.html" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <h3><i class="fas fa-search"></i> Buscar Produtos</h3>
                
                <!-- Barra de pesquisa usando componente padronizado -->
                <div class="search-bar mb-3">
                    <input type="text" class="search-input" id="searchInput" placeholder="Pesquisar por código ou descrição...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- Filtros usando componente padronizado -->
                <div class="filters">
                    <h3><i class="fas fa-filter"></i> Filtros</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Tipo</label>
                            <select id="filterTipo">
                                <option value="">Todos</option>
                                <option value="PA">Produto Acabado</option>
                                <option value="SP">Semi-Produto</option>
                                <option value="MP">Matéria Prima</option>
                                <option value="SV">Serviço</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Grupo</label>
                            <select id="filterGrupo">
                                <option value="">Todos</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select id="filterStatus">
                                <option value="">Todos</option>
                                <option value="ativo">Ativo</option>
                                <option value="inativo">Inativo</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Lista de produtos -->
                <div class="products-list" id="productsList">
                    <!-- Produtos serão carregados dinamicamente -->
                </div>
            </div>

            <!-- Formulário principal -->
            <div class="form-content">
                <!-- Abas usando componente padronizado -->
                <div class="tabs">
                    <button class="tab active" onclick="showTab('dados-basicos')">
                        <i class="fas fa-info-circle"></i> Dados Básicos
                    </button>
                    <button class="tab" onclick="showTab('custos')">
                        <i class="fas fa-dollar-sign"></i> Custos
                    </button>
                    <button class="tab" onclick="showTab('estoque')">
                        <i class="fas fa-warehouse"></i> Estoque
                    </button>
                    <button class="tab" onclick="showTab('fornecedores')">
                        <i class="fas fa-truck"></i> Fornecedores
                    </button>
                </div>

                <!-- Conteúdo das abas -->
                <div id="dados-basicos" class="tab-content active">
                    <!-- Seção Identificação -->
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-id-card"></i> Identificação
                            </div>
                        </div>
                        <div class="section-content">
                            <div class="form-grid-2">
                                <div class="form-group">
                                    <label class="required">Código</label>
                                    <input type="text" id="codigo" maxlength="20">
                                    <div class="info-text">Código único do produto</div>
                                </div>
                                <div class="form-group">
                                    <label>Código de Barras</label>
                                    <input type="text" id="codigoBarras" maxlength="50">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="required">Descrição</label>
                                <input type="text" id="descricao" maxlength="100">
                            </div>
                            
                            <div class="form-grid-3">
                                <div class="form-group">
                                    <label class="required">Tipo</label>
                                    <select id="tipo">
                                        <option value="PA">Produto Acabado</option>
                                        <option value="SP">Semi-Produto</option>
                                        <option value="MP">Matéria Prima</option>
                                        <option value="SV">Serviço</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Grupo</label>
                                    <select id="grupo">
                                        <option value="">Selecione...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Família</label>
                                    <select id="familia">
                                        <option value="">Selecione...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Outras abas seriam implementadas aqui -->
                <div id="custos" class="tab-content">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Aba de custos será implementada
                    </div>
                </div>

                <div id="estoque" class="tab-content">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Aba de estoque será implementada
                    </div>
                </div>

                <div id="fornecedores" class="tab-content">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Aba de fornecedores será implementada
                    </div>
                </div>

                <!-- Barra de ações -->
                <div class="actions-bar">
                    <div>
                        <span class="status aberta">Produto Ativo</span>
                    </div>
                    <div class="d-flex" style="gap: var(--spacing-sm);">
                        <button class="btn btn-secondary" onclick="limparFormulario()">
                            <i class="fas fa-eraser"></i> Limpar
                        </button>
                        <button class="btn btn-success" onclick="salvarProduto()">
                            <i class="fas fa-save"></i> Salvar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação usando componente padronizado -->
    <div id="notification" class="notification"></div>

    <script>
        // Funções JavaScript básicas
        function showTab(tabName) {
            // Ocultar todas as abas
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Mostrar aba selecionada
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        function salvarProduto() {
            showNotification('Produto salvo com sucesso!', 'success');
        }

        function novoProduto() {
            showNotification('Novo produto criado', 'info');
        }

        function duplicarProduto() {
            showNotification('Produto duplicado', 'info');
        }

        function limparFormulario() {
            showNotification('Formulário limpo', 'warning');
        }
    </script>
</body>
</html>
