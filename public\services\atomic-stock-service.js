/**
 * SERVIÇO DE TRANSAÇÕES ATÔMICAS DE ESTOQUE - WIZAR ERP
 * Implementa operações de estoque com controle de concorrência e integridade
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc, 
    getDocs,
    query,
    where,
    runTransaction,
    addDoc,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

import { ValidationService } from './validation-service.js';

export class AtomicStockService {
    
    /**
     * 📦 ENTRADA DE MATERIAL ATÔMICA
     */
    static async processStockEntry(entryData) {
        return await runTransaction(db, async (transaction) => {
            try {
                // Validar dados de entrada
                const validation = this.validateStockEntry(entryData);
                if (!validation.valid) {
                    throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
                }
                
                const sanitizedData = validation.data;
                
                // Buscar produto
                const produtoRef = doc(db, "produtos", sanitizedData.produtoId);
                const produtoDoc = await transaction.get(produtoRef);
                
                if (!produtoDoc.exists()) {
                    throw new Error(`Produto não encontrado: ${sanitizedData.produtoId}`);
                }
                
                const produto = produtoDoc.data();
                
                // Buscar estoque atual
                const estoqueQuery = query(
                    collection(db, "estoques"),
                    where("produtoId", "==", sanitizedData.produtoId),
                    where("armazemId", "==", sanitizedData.armazemId)
                );
                
                const estoqueSnapshot = await getDocs(estoqueQuery);
                let estoqueRef;
                let estoqueAtual = { quantidade: 0, valorTotal: 0 };
                
                if (estoqueSnapshot.empty) {
                    // Criar novo registro de estoque
                    estoqueRef = doc(collection(db, "estoques"));
                    estoqueAtual = {
                        produtoId: sanitizedData.produtoId,
                        armazemId: sanitizedData.armazemId,
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        unidade: produto.unidade,
                        quantidade: 0,
                        valorTotal: 0,
                        custoMedio: 0,
                        saldoReservado: 0,
                        saldoEmpenhado: 0,
                        ultimaMovimentacao: Timestamp.now(),
                        criadoEm: Timestamp.now()
                    };
                } else {
                    estoqueRef = estoqueSnapshot.docs[0].ref;
                    estoqueAtual = estoqueSnapshot.docs[0].data();
                }
                
                // Calcular novos valores
                const quantidadeAnterior = estoqueAtual.quantidade || 0;
                const valorAnterior = estoqueAtual.valorTotal || 0;
                const novaQuantidade = quantidadeAnterior + sanitizedData.quantidade;
                const valorEntrada = sanitizedData.quantidade * sanitizedData.valorUnitario;
                const novoValorTotal = valorAnterior + valorEntrada;
                const novoCustoMedio = novaQuantidade > 0 ? novoValorTotal / novaQuantidade : 0;
                
                // Atualizar estoque
                const novoEstoque = {
                    ...estoqueAtual,
                    quantidade: novaQuantidade,
                    valorTotal: novoValorTotal,
                    custoMedio: novoCustoMedio,
                    ultimaMovimentacao: Timestamp.now()
                };
                
                transaction.set(estoqueRef, novoEstoque);
                
                // Registrar movimentação
                const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                const movimentacao = {
                    tipo: 'ENTRADA',
                    produtoId: sanitizedData.produtoId,
                    armazemId: sanitizedData.armazemId,
                    codigo: produto.codigo,
                    descricao: produto.descricao,
                    quantidade: sanitizedData.quantidade,
                    valorUnitario: sanitizedData.valorUnitario,
                    valorTotal: valorEntrada,
                    saldoAnterior: quantidadeAnterior,
                    saldoAtual: novaQuantidade,
                    custoMedioAnterior: estoqueAtual.custoMedio || 0,
                    custoMedioAtual: novoCustoMedio,
                    documento: {
                        tipo: sanitizedData.documentoTipo || 'RECEBIMENTO',
                        numero: sanitizedData.documentoNumero || '',
                        id: sanitizedData.documentoId || ''
                    },
                    lote: sanitizedData.lote || null,
                    dataVencimento: sanitizedData.dataVencimento || null,
                    observacoes: sanitizedData.observacoes || '',
                    usuario: sanitizedData.usuario || 'Sistema',
                    timestamp: Timestamp.now(),
                    ip: sanitizedData.ip || 'unknown'
                };
                
                transaction.set(movimentacaoRef, movimentacao);
                
                // Registrar auditoria
                const auditoriaRef = doc(collection(db, "auditoria"));
                const auditoria = {
                    acao: 'ENTRADA_ESTOQUE',
                    modulo: 'ESTOQUE',
                    produtoId: sanitizedData.produtoId,
                    armazemId: sanitizedData.armazemId,
                    dadosAnteriores: {
                        quantidade: quantidadeAnterior,
                        valorTotal: valorAnterior,
                        custoMedio: estoqueAtual.custoMedio || 0
                    },
                    dadosNovos: {
                        quantidade: novaQuantidade,
                        valorTotal: novoValorTotal,
                        custoMedio: novoCustoMedio
                    },
                    usuario: sanitizedData.usuario || 'Sistema',
                    timestamp: Timestamp.now(),
                    ip: sanitizedData.ip || 'unknown'
                };
                
                transaction.set(auditoriaRef, auditoria);
                
                return {
                    success: true,
                    estoqueId: estoqueRef.id,
                    movimentacaoId: movimentacaoRef.id,
                    saldoAnterior: quantidadeAnterior,
                    saldoAtual: novaQuantidade,
                    custoMedio: novoCustoMedio
                };
                
            } catch (error) {
                console.error('Erro na entrada de estoque:', error);
                throw error;
            }
        });
    }
    
    /**
     * 📤 SAÍDA DE MATERIAL ATÔMICA
     */
    static async processStockExit(exitData) {
        return await runTransaction(db, async (transaction) => {
            try {
                // Validar dados de saída
                const validation = this.validateStockExit(exitData);
                if (!validation.valid) {
                    throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
                }
                
                const sanitizedData = validation.data;
                
                // Buscar estoque atual
                const estoqueQuery = query(
                    collection(db, "estoques"),
                    where("produtoId", "==", sanitizedData.produtoId),
                    where("armazemId", "==", sanitizedData.armazemId)
                );
                
                const estoqueSnapshot = await getDocs(estoqueQuery);
                
                if (estoqueSnapshot.empty) {
                    throw new Error('Produto não encontrado no estoque');
                }
                
                const estoqueRef = estoqueSnapshot.docs[0].ref;
                const estoqueAtual = estoqueSnapshot.docs[0].data();
                
                // Verificar saldo disponível
                const saldoDisponivel = (estoqueAtual.quantidade || 0) - 
                                       (estoqueAtual.saldoReservado || 0) - 
                                       (estoqueAtual.saldoEmpenhado || 0);
                
                if (saldoDisponivel < sanitizedData.quantidade) {
                    throw new Error(`Saldo insuficiente. Disponível: ${saldoDisponivel}, Solicitado: ${sanitizedData.quantidade}`);
                }
                
                // Calcular novos valores
                const quantidadeAnterior = estoqueAtual.quantidade || 0;
                const valorAnterior = estoqueAtual.valorTotal || 0;
                const custoMedioAtual = estoqueAtual.custoMedio || 0;
                const novaQuantidade = quantidadeAnterior - sanitizedData.quantidade;
                const valorSaida = sanitizedData.quantidade * custoMedioAtual;
                const novoValorTotal = Math.max(0, valorAnterior - valorSaida);
                
                // Atualizar estoque
                const novoEstoque = {
                    ...estoqueAtual,
                    quantidade: novaQuantidade,
                    valorTotal: novoValorTotal,
                    ultimaMovimentacao: Timestamp.now()
                };
                
                transaction.update(estoqueRef, novoEstoque);
                
                // Registrar movimentação
                const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                const movimentacao = {
                    tipo: 'SAIDA',
                    produtoId: sanitizedData.produtoId,
                    armazemId: sanitizedData.armazemId,
                    codigo: estoqueAtual.codigo,
                    descricao: estoqueAtual.descricao,
                    quantidade: sanitizedData.quantidade,
                    valorUnitario: custoMedioAtual,
                    valorTotal: valorSaida,
                    saldoAnterior: quantidadeAnterior,
                    saldoAtual: novaQuantidade,
                    custoMedioAnterior: custoMedioAtual,
                    custoMedioAtual: custoMedioAtual,
                    documento: {
                        tipo: sanitizedData.documentoTipo || 'CONSUMO',
                        numero: sanitizedData.documentoNumero || '',
                        id: sanitizedData.documentoId || ''
                    },
                    observacoes: sanitizedData.observacoes || '',
                    usuario: sanitizedData.usuario || 'Sistema',
                    timestamp: Timestamp.now(),
                    ip: sanitizedData.ip || 'unknown'
                };
                
                transaction.set(movimentacaoRef, movimentacao);
                
                return {
                    success: true,
                    estoqueId: estoqueRef.id,
                    movimentacaoId: movimentacaoRef.id,
                    saldoAnterior: quantidadeAnterior,
                    saldoAtual: novaQuantidade,
                    valorSaida: valorSaida
                };
                
            } catch (error) {
                console.error('Erro na saída de estoque:', error);
                throw error;
            }
        });
    }
    
    /**
     * 🔄 TRANSFERÊNCIA ENTRE ARMAZÉNS ATÔMICA
     */
    static async processStockTransfer(transferData) {
        return await runTransaction(db, async (transaction) => {
            try {
                // Validar dados de transferência
                const validation = this.validateStockTransfer(transferData);
                if (!validation.valid) {
                    throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
                }
                
                const sanitizedData = validation.data;
                
                // Processar saída do armazém origem
                const exitResult = await this.processStockExit({
                    ...sanitizedData,
                    armazemId: sanitizedData.armazemOrigemId,
                    documentoTipo: 'TRANSFERENCIA',
                    documentoNumero: sanitizedData.numeroTransferencia
                });
                
                // Processar entrada no armazém destino
                const entryResult = await this.processStockEntry({
                    ...sanitizedData,
                    armazemId: sanitizedData.armazemDestinoId,
                    documentoTipo: 'TRANSFERENCIA',
                    documentoNumero: sanitizedData.numeroTransferencia
                });
                
                return {
                    success: true,
                    origem: exitResult,
                    destino: entryResult,
                    numeroTransferencia: sanitizedData.numeroTransferencia
                };
                
            } catch (error) {
                console.error('Erro na transferência de estoque:', error);
                throw error;
            }
        });
    }

    /**
     * ✅ VALIDAR DADOS DE ENTRADA
     */
    static validateStockEntry(data) {
        const errors = [];
        const sanitizedData = {};

        // Validar produto ID
        if (!data.produtoId || typeof data.produtoId !== 'string') {
            errors.push('ID do produto é obrigatório');
        } else {
            sanitizedData.produtoId = data.produtoId.trim();
        }

        // Validar armazém ID
        if (!data.armazemId || typeof data.armazemId !== 'string') {
            errors.push('ID do armazém é obrigatório');
        } else {
            sanitizedData.armazemId = data.armazemId.trim();
        }

        // Validar quantidade
        const quantityValidation = ValidationService.validateQuantity(data.quantidade, {
            min: 0.001,
            max: 999999,
            decimals: 3
        });

        if (!quantityValidation.valid) {
            errors.push(quantityValidation.message);
        } else {
            sanitizedData.quantidade = quantityValidation.value;
        }

        // Validar valor unitário
        const priceValidation = ValidationService.validateCurrency(data.valorUnitario, {
            min: 0,
            max: 999999.99
        });

        if (!priceValidation.valid) {
            errors.push(priceValidation.message);
        } else {
            sanitizedData.valorUnitario = priceValidation.value;
        }

        // Campos opcionais
        if (data.lote) {
            sanitizedData.lote = ValidationService.sanitizeString(data.lote);
        }

        if (data.dataVencimento) {
            const dateValidation = ValidationService.validateDate(data.dataVencimento, {
                required: false,
                minDate: new Date()
            });

            if (dateValidation.valid) {
                sanitizedData.dataVencimento = dateValidation.value;
            }
        }

        if (data.observacoes) {
            sanitizedData.observacoes = ValidationService.sanitizeString(data.observacoes);
        }

        if (data.usuario) {
            sanitizedData.usuario = ValidationService.sanitizeString(data.usuario);
        }

        if (data.documentoTipo) {
            sanitizedData.documentoTipo = ValidationService.sanitizeString(data.documentoTipo);
        }

        if (data.documentoNumero) {
            sanitizedData.documentoNumero = ValidationService.sanitizeString(data.documentoNumero);
        }

        if (data.documentoId) {
            sanitizedData.documentoId = ValidationService.sanitizeString(data.documentoId);
        }

        return {
            valid: errors.length === 0,
            errors,
            data: sanitizedData
        };
    }

    /**
     * ✅ VALIDAR DADOS DE SAÍDA
     */
    static validateStockExit(data) {
        const errors = [];
        const sanitizedData = {};

        // Validar produto ID
        if (!data.produtoId || typeof data.produtoId !== 'string') {
            errors.push('ID do produto é obrigatório');
        } else {
            sanitizedData.produtoId = data.produtoId.trim();
        }

        // Validar armazém ID
        if (!data.armazemId || typeof data.armazemId !== 'string') {
            errors.push('ID do armazém é obrigatório');
        } else {
            sanitizedData.armazemId = data.armazemId.trim();
        }

        // Validar quantidade
        const quantityValidation = ValidationService.validateQuantity(data.quantidade, {
            min: 0.001,
            max: 999999,
            decimals: 3
        });

        if (!quantityValidation.valid) {
            errors.push(quantityValidation.message);
        } else {
            sanitizedData.quantidade = quantityValidation.value;
        }

        // Campos opcionais
        if (data.observacoes) {
            sanitizedData.observacoes = ValidationService.sanitizeString(data.observacoes);
        }

        if (data.usuario) {
            sanitizedData.usuario = ValidationService.sanitizeString(data.usuario);
        }

        if (data.documentoTipo) {
            sanitizedData.documentoTipo = ValidationService.sanitizeString(data.documentoTipo);
        }

        if (data.documentoNumero) {
            sanitizedData.documentoNumero = ValidationService.sanitizeString(data.documentoNumero);
        }

        if (data.documentoId) {
            sanitizedData.documentoId = ValidationService.sanitizeString(data.documentoId);
        }

        return {
            valid: errors.length === 0,
            errors,
            data: sanitizedData
        };
    }

    /**
     * 📊 CONSULTAR SALDO DISPONÍVEL
     */
    static async getAvailableStock(produtoId, armazemId) {
        try {
            const estoqueQuery = query(
                collection(db, "estoques"),
                where("produtoId", "==", produtoId),
                where("armazemId", "==", armazemId)
            );

            const estoqueSnapshot = await getDocs(estoqueQuery);

            if (estoqueSnapshot.empty) {
                return {
                    quantidade: 0,
                    saldoReservado: 0,
                    saldoEmpenhado: 0,
                    saldoDisponivel: 0,
                    custoMedio: 0
                };
            }

            const estoque = estoqueSnapshot.docs[0].data();
            const saldoDisponivel = (estoque.quantidade || 0) -
                                   (estoque.saldoReservado || 0) -
                                   (estoque.saldoEmpenhado || 0);

            return {
                quantidade: estoque.quantidade || 0,
                saldoReservado: estoque.saldoReservado || 0,
                saldoEmpenhado: estoque.saldoEmpenhado || 0,
                saldoDisponivel: Math.max(0, saldoDisponivel),
                custoMedio: estoque.custoMedio || 0,
                valorTotal: estoque.valorTotal || 0
            };

        } catch (error) {
            console.error('Erro ao consultar saldo:', error);
            throw error;
        }
    }
}
