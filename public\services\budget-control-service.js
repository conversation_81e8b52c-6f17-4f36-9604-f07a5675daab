/**
 * SERVIÇO DE CONTROLE ORÇAMENTÁRIO - WIZAR ERP
 * Implementa validação rigorosa de orçamento sem possibilidade de bypass fácil
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc,
    getDocs,
    addDoc,
    updateDoc,
    query,
    where,
    orderBy,
    runTransaction,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

import { ValidationService } from './validation-service.js';
import { AuditService } from './audit-service.js';
import { AuthService } from './auth-service.js';

export class BudgetControlService {
    
    // Tipos de orçamento
    static BUDGET_TYPES = {
        MONTHLY: 'MONTHLY',
        QUARTERLY: 'QUARTERLY',
        ANNUAL: 'ANNUAL',
        PROJECT: 'PROJECT'
    };
    
    // Status de orçamento
    static BUDGET_STATUS = {
        ACTIVE: 'ACTIVE',
        EXCEEDED: 'EXCEEDED',
        BLOCKED: 'BLOCKED',
        EXPIRED: 'EXPIRED'
    };
    
    // Níveis de alerta
    static ALERT_LEVELS = {
        GREEN: { threshold: 0.7, color: '#28a745', message: 'Orçamento saudável' },
        YELLOW: { threshold: 0.85, color: '#ffc107', message: 'Atenção: orçamento próximo do limite' },
        ORANGE: { threshold: 0.95, color: '#fd7e14', message: 'Alerta: orçamento quase esgotado' },
        RED: { threshold: 1.0, color: '#dc3545', message: 'Crítico: orçamento esgotado' }
    };
    
    /**
     * 💰 VALIDAR DISPONIBILIDADE ORÇAMENTÁRIA
     */
    static async validateBudgetAvailability(centroCusto, valor, periodo = null) {
        try {
            // Validar entrada
            const valueValidation = ValidationService.validateCurrency(valor, { min: 0.01 });
            if (!valueValidation.valid) {
                throw new Error(valueValidation.message);
            }
            
            const sanitizedValue = valueValidation.value;
            
            // Obter orçamento do centro de custo
            const budget = await this.getBudgetByCostCenter(centroCusto, periodo);
            
            if (!budget) {
                throw new Error(`Orçamento não encontrado para o centro de custo: ${centroCusto}`);
            }
            
            // Verificar se orçamento está ativo
            if (budget.status !== this.BUDGET_STATUS.ACTIVE) {
                throw new Error(`Orçamento está ${budget.status.toLowerCase()}`);
            }
            
            // Verificar se não expirou
            if (budget.endDate && budget.endDate < new Date()) {
                throw new Error('Orçamento expirado');
            }
            
            // Calcular utilização atual
            const currentUsage = await this.getCurrentBudgetUsage(centroCusto, budget.period);
            const availableAmount = budget.totalAmount - currentUsage.totalUsed;
            const projectedUsage = currentUsage.totalUsed + sanitizedValue;
            const usagePercentage = (projectedUsage / budget.totalAmount) * 100;
            
            // Determinar nível de alerta
            const alertLevel = this.getAlertLevel(projectedUsage / budget.totalAmount);
            
            // Verificar se excede o orçamento
            if (projectedUsage > budget.totalAmount) {
                const exceedAmount = projectedUsage - budget.totalAmount;
                
                // Registrar tentativa de estouro
                await AuditService.logEvent(AuditService.EVENT_TYPES.BUDGET_EXCEEDED, {
                    centroCusto,
                    budgetId: budget.id,
                    requestedValue: sanitizedValue,
                    availableAmount,
                    exceedAmount,
                    usagePercentage
                }, {
                    severity: AuditService.SEVERITY_LEVELS.HIGH,
                    module: 'BUDGET'
                });
                
                // Verificar se há aprovação especial
                const specialApproval = await this.checkSpecialApproval(centroCusto, exceedAmount, budget);
                
                if (!specialApproval.approved) {
                    return {
                        valid: false,
                        reason: 'BUDGET_EXCEEDED',
                        message: `Orçamento insuficiente. Disponível: R$ ${availableAmount.toFixed(2)}, Solicitado: R$ ${sanitizedValue.toFixed(2)}`,
                        budget: {
                            total: budget.totalAmount,
                            used: currentUsage.totalUsed,
                            available: availableAmount,
                            usagePercentage: usagePercentage.toFixed(2)
                        },
                        exceedAmount,
                        requiresSpecialApproval: true,
                        alertLevel
                    };
                }
            }
            
            return {
                valid: true,
                budget: {
                    id: budget.id,
                    total: budget.totalAmount,
                    used: currentUsage.totalUsed,
                    available: availableAmount,
                    usagePercentage: usagePercentage.toFixed(2)
                },
                alertLevel,
                projectedUsage: projectedUsage,
                specialApprovalUsed: projectedUsage > budget.totalAmount
            };
            
        } catch (error) {
            console.error('Erro na validação orçamentária:', error);
            throw error;
        }
    }
    
    /**
     * 📊 OBTER ORÇAMENTO POR CENTRO DE CUSTO
     */
    static async getBudgetByCostCenter(centroCusto, periodo = null) {
        try {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            
            // Se período não especificado, usar período atual
            if (!periodo) {
                periodo = {
                    year: currentYear,
                    month: currentMonth,
                    type: this.BUDGET_TYPES.MONTHLY
                };
            }
            
            const budgetQuery = query(
                collection(db, "orcamentos"),
                where("centroCusto", "==", centroCusto),
                where("period.year", "==", periodo.year),
                where("period.month", "==", periodo.month),
                where("period.type", "==", periodo.type),
                where("status", "==", this.BUDGET_STATUS.ACTIVE)
            );
            
            const budgetSnapshot = await getDocs(budgetQuery);
            
            if (budgetSnapshot.empty) {
                return null;
            }
            
            const budgetDoc = budgetSnapshot.docs[0];
            const budgetData = budgetDoc.data();
            
            return {
                id: budgetDoc.id,
                ...budgetData,
                startDate: budgetData.startDate?.toDate(),
                endDate: budgetData.endDate?.toDate(),
                createdAt: budgetData.createdAt?.toDate(),
                updatedAt: budgetData.updatedAt?.toDate()
            };
            
        } catch (error) {
            console.error('Erro ao obter orçamento:', error);
            throw error;
        }
    }
    
    /**
     * 📈 OBTER UTILIZAÇÃO ATUAL DO ORÇAMENTO
     */
    static async getCurrentBudgetUsage(centroCusto, period) {
        try {
            const startDate = new Date(period.year, period.month - 1, 1);
            const endDate = new Date(period.year, period.month, 0, 23, 59, 59);
            
            // Buscar solicitações aprovadas no período
            const requestsQuery = query(
                collection(db, "solicitacoesCompra"),
                where("centroCusto", "==", centroCusto),
                where("status", "in", ["APROVADA", "EM_COTACAO", "COTADO", "PEDIDO_GERADO"]),
                where("dataAprovacao", ">=", Timestamp.fromDate(startDate)),
                where("dataAprovacao", "<=", Timestamp.fromDate(endDate))
            );
            
            const requestsSnapshot = await getDocs(requestsQuery);
            
            let totalUsed = 0;
            let requestCount = 0;
            const requests = [];
            
            requestsSnapshot.docs.forEach(doc => {
                const data = doc.data();
                const requestValue = data.valorTotal || 0;
                totalUsed += requestValue;
                requestCount++;
                
                requests.push({
                    id: doc.id,
                    numero: data.numero,
                    valor: requestValue,
                    dataAprovacao: data.dataAprovacao?.toDate(),
                    status: data.status
                });
            });
            
            // Buscar pedidos de compra no período
            const ordersQuery = query(
                collection(db, "pedidosCompra"),
                where("centroCusto", "==", centroCusto),
                where("status", "in", ["APROVADO", "ENVIADO", "RECEBIDO"]),
                where("dataAprovacao", ">=", Timestamp.fromDate(startDate)),
                where("dataAprovacao", "<=", Timestamp.fromDate(endDate))
            );
            
            const ordersSnapshot = await getDocs(ordersQuery);
            
            ordersSnapshot.docs.forEach(doc => {
                const data = doc.data();
                const orderValue = data.valorTotal || 0;
                
                // Evitar dupla contagem se já foi contado na solicitação
                const alreadyCounted = requests.some(req => req.id === data.solicitacaoId);
                
                if (!alreadyCounted) {
                    totalUsed += orderValue;
                    requestCount++;
                }
            });
            
            return {
                totalUsed,
                requestCount,
                period,
                calculatedAt: new Date(),
                requests
            };
            
        } catch (error) {
            console.error('Erro ao calcular utilização do orçamento:', error);
            throw error;
        }
    }
    
    /**
     * 🚨 OBTER NÍVEL DE ALERTA
     */
    static getAlertLevel(usageRatio) {
        if (usageRatio >= this.ALERT_LEVELS.RED.threshold) {
            return { level: 'RED', ...this.ALERT_LEVELS.RED };
        } else if (usageRatio >= this.ALERT_LEVELS.ORANGE.threshold) {
            return { level: 'ORANGE', ...this.ALERT_LEVELS.ORANGE };
        } else if (usageRatio >= this.ALERT_LEVELS.YELLOW.threshold) {
            return { level: 'YELLOW', ...this.ALERT_LEVELS.YELLOW };
        } else {
            return { level: 'GREEN', ...this.ALERT_LEVELS.GREEN };
        }
    }
    
    /**
     * 🔐 VERIFICAR APROVAÇÃO ESPECIAL
     */
    static async checkSpecialApproval(centroCusto, exceedAmount, budget) {
        try {
            // Buscar aprovações especiais pendentes ou aprovadas para este centro de custo
            const approvalQuery = query(
                collection(db, "aprovacoesEspeciais"),
                where("centroCusto", "==", centroCusto),
                where("budgetId", "==", budget.id),
                where("status", "in", ["PENDING", "APPROVED"]),
                orderBy("createdAt", "desc")
            );
            
            const approvalSnapshot = await getDocs(approvalQuery);
            
            if (!approvalSnapshot.empty) {
                const approval = approvalSnapshot.docs[0].data();
                
                if (approval.status === "APPROVED" && approval.approvedAmount >= exceedAmount) {
                    return {
                        approved: true,
                        approvalId: approvalSnapshot.docs[0].id,
                        approvedAmount: approval.approvedAmount,
                        approvedBy: approval.approvedBy,
                        approvedAt: approval.approvedAt?.toDate()
                    };
                }
            }
            
            return {
                approved: false,
                requiresApproval: true,
                exceedAmount,
                message: 'Aprovação especial necessária para exceder orçamento'
            };
            
        } catch (error) {
            console.error('Erro ao verificar aprovação especial:', error);
            return { approved: false, error: error.message };
        }
    }
    
    /**
     * 📝 SOLICITAR APROVAÇÃO ESPECIAL
     */
    static async requestSpecialApproval(centroCusto, exceedAmount, justification, budgetId) {
        try {
            const currentUser = await AuthService.getCurrentUser();
            
            if (!currentUser) {
                throw new Error('Usuário não autenticado');
            }
            
            // Validar justificativa
            if (!justification || justification.trim().length < 20) {
                throw new Error('Justificativa deve ter pelo menos 20 caracteres');
            }
            
            const approvalData = {
                centroCusto,
                budgetId,
                exceedAmount,
                justification: ValidationService.sanitizeString(justification),
                requestedBy: currentUser.id,
                requestedByName: currentUser.nome,
                requestedAt: Timestamp.now(),
                status: 'PENDING',
                createdAt: Timestamp.now(),
                updatedAt: Timestamp.now()
            };
            
            const approvalRef = await addDoc(collection(db, "aprovacoesEspeciais"), approvalData);
            
            // Registrar auditoria
            await AuditService.logEvent(AuditService.EVENT_TYPES.BUDGET_EXCEEDED, {
                centroCusto,
                budgetId,
                exceedAmount,
                justification,
                approvalId: approvalRef.id
            }, {
                severity: AuditService.SEVERITY_LEVELS.HIGH,
                module: 'BUDGET'
            });
            
            return {
                success: true,
                approvalId: approvalRef.id,
                message: 'Solicitação de aprovação especial criada com sucesso'
            };
            
        } catch (error) {
            console.error('Erro ao solicitar aprovação especial:', error);
            throw error;
        }
    }
    
    /**
     * 📊 RELATÓRIO DE UTILIZAÇÃO ORÇAMENTÁRIA
     */
    static async getBudgetReport(filters = {}) {
        try {
            let budgetQuery = collection(db, "orcamentos");
            
            // Aplicar filtros
            if (filters.centroCusto) {
                budgetQuery = query(budgetQuery, where("centroCusto", "==", filters.centroCusto));
            }
            
            if (filters.year) {
                budgetQuery = query(budgetQuery, where("period.year", "==", filters.year));
            }
            
            if (filters.month) {
                budgetQuery = query(budgetQuery, where("period.month", "==", filters.month));
            }
            
            budgetQuery = query(budgetQuery, orderBy("centroCusto"));
            
            const budgetSnapshot = await getDocs(budgetQuery);
            const budgetReport = [];
            
            for (const doc of budgetSnapshot.docs) {
                const budgetData = doc.data();
                const usage = await this.getCurrentBudgetUsage(budgetData.centroCusto, budgetData.period);
                const usagePercentage = (usage.totalUsed / budgetData.totalAmount) * 100;
                const alertLevel = this.getAlertLevel(usage.totalUsed / budgetData.totalAmount);
                
                budgetReport.push({
                    id: doc.id,
                    centroCusto: budgetData.centroCusto,
                    totalAmount: budgetData.totalAmount,
                    usedAmount: usage.totalUsed,
                    availableAmount: budgetData.totalAmount - usage.totalUsed,
                    usagePercentage: usagePercentage.toFixed(2),
                    requestCount: usage.requestCount,
                    alertLevel,
                    period: budgetData.period,
                    status: budgetData.status
                });
            }
            
            return {
                budgets: budgetReport,
                summary: {
                    totalBudgets: budgetReport.length,
                    totalAmount: budgetReport.reduce((sum, b) => sum + b.totalAmount, 0),
                    totalUsed: budgetReport.reduce((sum, b) => sum + b.usedAmount, 0),
                    averageUsage: budgetReport.length > 0 
                        ? (budgetReport.reduce((sum, b) => sum + parseFloat(b.usagePercentage), 0) / budgetReport.length).toFixed(2)
                        : 0,
                    alertCounts: {
                        green: budgetReport.filter(b => b.alertLevel.level === 'GREEN').length,
                        yellow: budgetReport.filter(b => b.alertLevel.level === 'YELLOW').length,
                        orange: budgetReport.filter(b => b.alertLevel.level === 'ORANGE').length,
                        red: budgetReport.filter(b => b.alertLevel.level === 'RED').length
                    }
                }
            };
            
        } catch (error) {
            console.error('Erro ao gerar relatório orçamentário:', error);
            throw error;
        }
    }
}
