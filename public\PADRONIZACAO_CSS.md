# 🎨 GUIA DE PADRONIZAÇÃO CSS

## 📋 **BASEADO EM: gestao_compras_integrada.html**

Este documento explica como padronizar **NOVAS TELAS** do sistema usando o CSS padrão criado baseado na tela `gestao_compras_integrada.html`.

⚠️ **IMPORTANTE:** Este CSS padrão deve ser usado apenas para **NOVAS TELAS** ou telas que precisam ser **COMPLETAMENTE REFEITAS**. Telas existentes que já funcionam bem devem manter seu CSS específico.

---

## 🔧 **QUANDO USAR O CSS PADRÃO**

### ✅ **USE PARA:**
- **Novas telas** sendo criadas do zero
- **Telas problemáticas** que precisam ser refeitas
- **Telas muito simples** que se beneficiariam do padrão
- **Protótipos** e telas de teste

### ❌ **NÃO USE PARA:**
- **Telas funcionais** que já têm boa aparência
- **Telas complexas** com layout específico
- **Telas com funcionalidades únicas** que precisam de CSS customizado
- **Relatórios especializados** como `relatorio_movimentacoes.html`

---

## 🔧 **COMO APLICAR O CSS PADRÃO (APENAS NOVAS TELAS)**

### **1️⃣ INCLUIR O CSS PADRÃO**

```html
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sua Tela</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/standard-theme.css">
</head>
```

### **2️⃣ ESTRUTURA HTML PADRÃO**

```html
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-icon"></i>
                Título da Tela
            </h1>
            <div class="header-actions">
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Ação Principal
                </button>
                <button class="btn btn-warning" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <!-- Conteúdo Principal -->
        <div class="main-content">
            <!-- Seu conteúdo aqui -->
        </div>
    </div>
</body>
```

---

## 🎯 **COMPONENTES DISPONÍVEIS**

### **📊 CARDS DE ESTATÍSTICAS**

```html
<div class="stats-grid">
    <div class="stat-card pendentes">
        <div class="stat-number">25</div>
        <div class="stat-label">Pendentes</div>
    </div>
    <div class="stat-card aprovadas">
        <div class="stat-number">150</div>
        <div class="stat-label">Aprovadas</div>
    </div>
</div>
```

**Classes disponíveis:**
- `stat-card pendentes` (laranja)
- `stat-card aprovadas` (verde)
- `stat-card cotacao` (cinza)
- `stat-card cotadas` (azul claro)
- `stat-card total` (azul)

### **🔍 FILTROS PADRONIZADOS**

```html
<div class="filters">
    <h3>
        <i class="fas fa-filter"></i>
        Filtros Avançados
    </h3>
    <div class="filter-row">
        <div class="form-group">
            <label>Data Início:</label>
            <input type="date" class="form-control" id="dataInicio">
        </div>
        <div class="form-group">
            <label>Status:</label>
            <select class="form-control" id="statusFilter">
                <option value="">Todos</option>
                <option value="PENDENTE">Pendente</option>
            </select>
        </div>
    </div>
</div>
```

### **📋 TABELAS PADRONIZADAS**

```html
<div class="table-container">
    <table class="table">
        <thead>
            <tr>
                <th>Coluna 1</th>
                <th>Coluna 2</th>
                <th>Ações</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Dados</td>
                <td>Mais dados</td>
                <td>
                    <div class="actions">
                        <button class="btn btn-sm btn-primary">Editar</button>
                        <button class="btn btn-sm btn-danger">Excluir</button>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

### **🏷️ STATUS E BADGES**

```html
<!-- Status -->
<span class="status status-pendente">Pendente</span>
<span class="status status-aprovado">Aprovado</span>
<span class="status status-rejeitado">Rejeitado</span>

<!-- Badges -->
<span class="badge badge-primary">Novo</span>
<span class="badge badge-success">Ativo</span>
<span class="badge badge-warning">Atenção</span>
```

### **🔔 ALERTAS**

```html
<div class="alert alert-info">
    <i class="fas fa-info-circle"></i>
    <strong>Informação:</strong> Mensagem informativa.
</div>

<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <strong>Sucesso:</strong> Operação realizada com sucesso.
</div>

<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>Atenção:</strong> Verifique os dados.
</div>

<div class="alert alert-danger">
    <i class="fas fa-times-circle"></i>
    <strong>Erro:</strong> Algo deu errado.
</div>
```

### **🔘 BOTÕES**

```html
<!-- Botões principais -->
<button class="btn btn-primary">Primário</button>
<button class="btn btn-success">Sucesso</button>
<button class="btn btn-warning">Atenção</button>
<button class="btn btn-danger">Perigo</button>
<button class="btn btn-info">Informação</button>
<button class="btn btn-secondary">Secundário</button>

<!-- Botões pequenos -->
<button class="btn btn-sm btn-primary">Pequeno</button>

<!-- Com ícones -->
<button class="btn btn-primary">
    <i class="fas fa-plus"></i>
    Adicionar
</button>
```

### **📑 SISTEMA DE TABS**

```html
<div class="tabs">
    <button class="tab active" onclick="showTab('tab1', event)">
        <i class="fas fa-list"></i>
        Tab 1
    </button>
    <button class="tab" onclick="showTab('tab2', event)">
        <i class="fas fa-chart"></i>
        Tab 2
    </button>
</div>

<div id="tab1" class="tab-content active">
    Conteúdo da Tab 1
</div>

<div id="tab2" class="tab-content">
    Conteúdo da Tab 2
</div>
```

---

## 🎨 **CORES E TEMAS**

### **🎯 PALETA DE CORES**

```css
/* Cores Principais */
--primary: #3498db (azul)
--success: #27ae60 (verde)
--warning: #f39c12 (laranja)
--danger: #e74c3c (vermelho)
--info: #17a2b8 (azul claro)
--secondary: #6c757d (cinza)

/* Gradientes */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* Fundo */
background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); /* Header */
```

### **📊 STATUS COLORS**

```css
.status-pendente { background: #fff3cd; color: #856404; }
.status-aprovado { background: #d4edda; color: #155724; }
.status-rejeitado { background: #f8d7da; color: #721c24; }
.status-cotacao { background: #e2e3e5; color: #383d41; }
.status-recebido { background: #d1ecf1; color: #0c5460; }
```

---

## 🛠️ **CLASSES UTILITÁRIAS**

### **📐 ESPAÇAMENTO**

```css
.mb-0, .mb-1, .mb-2, .mb-3, .mb-4, .mb-5 /* margin-bottom */
.mt-0, .mt-1, .mt-2, .mt-3, .mt-4, .mt-5 /* margin-top */
.p-0, .p-1, .p-2, .p-3, .p-4, .p-5 /* padding */
```

### **📝 TEXTO**

```css
.text-center, .text-left, .text-right
.text-muted, .text-primary, .text-success, .text-warning, .text-danger
.font-weight-bold, .font-weight-normal
```

### **👁️ VISIBILIDADE**

```css
.d-none, .d-block, .d-flex, .d-grid
```

### **✨ EFEITOS**

```css
.hover-lift /* Eleva no hover */
.hover-glow /* Brilho no hover */
.fade-in /* Animação de entrada */
.slide-up /* Desliza para cima */
```

---

## 📱 **RESPONSIVIDADE**

O CSS já inclui breakpoints responsivos:

```css
@media (max-width: 768px) { /* Tablets */ }
@media (max-width: 480px) { /* Celulares */ }
```

---

## 🔄 **EXEMPLO DE MIGRAÇÃO**

### **❌ ANTES (CSS próprio)**

```html
<div class="minha-tela">
    <div class="cabecalho">
        <h1>Título</h1>
    </div>
    <div class="conteudo">
        <div class="filtros">...</div>
        <div class="tabela">...</div>
    </div>
</div>
```

### **✅ DEPOIS (CSS padrão)**

```html
<div class="container">
    <div class="header">
        <h1><i class="fas fa-icon"></i> Título</h1>
        <div class="header-actions">...</div>
    </div>
    <div class="main-content">
        <div class="filters">...</div>
        <div class="table-container">...</div>
    </div>
</div>
```

---

## 🎯 **BENEFÍCIOS DA PADRONIZAÇÃO**

✅ **Consistência visual** em todo o sistema
✅ **Manutenção simplificada** - um CSS para todas as telas
✅ **Responsividade automática** em todos os componentes
✅ **Animações e efeitos** padronizados
✅ **Carregamento mais rápido** - CSS reutilizado
✅ **Desenvolvimento acelerado** - componentes prontos

---

## 📋 **CHECKLIST DE MIGRAÇÃO**

- [ ] Incluir `standard-theme.css` e Font Awesome
- [ ] Substituir estrutura HTML pela padrão
- [ ] Migrar filtros para `.filters` e `.filter-row`
- [ ] Atualizar tabelas para `.table-container` e `.table`
- [ ] Converter cards para `.stat-card` ou `.dashboard-card`
- [ ] Padronizar botões com classes `.btn`
- [ ] Atualizar status com classes `.status-*`
- [ ] Testar responsividade
- [ ] Remover CSS específico desnecessário

---

## ⚠️ **LIÇÕES APRENDIDAS**

### **🚫 ERRO COMETIDO:**
Tentamos aplicar o CSS padrão no `relatorio_movimentacoes.html` que já funcionava perfeitamente, resultando em:
- **Perda de funcionalidade** específica
- **Layout quebrado** e inconsistente
- **Experiência do usuário prejudicada**
- **Tempo perdido** revertendo mudanças

### **✅ ABORDAGEM CORRETA:**
- **Manter telas funcionais** como estão
- **Usar CSS padrão apenas para novas telas**
- **Avaliar cada caso individualmente**
- **Priorizar funcionalidade sobre padronização**

### **🎯 REGRA DE OURO:**
> **"Se está funcionando bem, não mexa!"**
>
> Padronização é importante, mas não deve quebrar funcionalidades existentes.

---

**🎯 Use este CSS padrão sabiamente - apenas para NOVAS telas que se beneficiarão da padronização!**
