/* Performance optimizations for estoques.html */

/* GPU acceleration for smooth animations */
.table-container,
.modal,
.toast {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize table rendering */
.table {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
}

.table th,
.table td {
  contain: layout style paint;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Virtual scrolling container */
.virtual-scroll-container {
  height: 400px;
  overflow-y: auto;
  position: relative;
}

.virtual-scroll-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

/* Optimize form rendering */
.form-group,
.form-row,
.form-col {
  contain: layout style;
}

/* Lazy loading images */
img[data-src] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[data-src].loaded {
  opacity: 1;
}

/* Optimize modal animations */
.modal {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

/* Optimize button interactions */
.btn {
  transition: background-color 0.2s ease, transform 0.1s ease;
  will-change: background-color, transform;
}

.btn:active {
  transform: translateY(1px);
}

/* Optimize search input */
#searchInput {
  will-change: border-color;
  transition: border-color 0.2s ease;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Optimize pagination */
.pagination {
  contain: layout style;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  min-width: 80px;
}

/* Memory optimization for large lists */
.large-list {
  contain: strict;
  height: 300px;
  overflow-y: auto;
}

.large-list-item {
  contain: layout style paint;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-bottom: 1px solid #eee;
}

/* Optimize select dropdowns */
select {
  contain: layout style;
}

select option {
  contain: layout style paint;
}

/* Critical CSS for above-the-fold content */
.header,
.form-group:first-child {
  contain: layout style paint;
}

/* Defer non-critical animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize for touch devices */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
  }
  
  .table td,
  .table th {
    padding: 12px 8px;
  }
}

/* Print optimizations */
@media print {
  .btn,
  .form-group,
  .pagination,
  .modal {
    display: none !important;
  }
  
  .table {
    border-collapse: collapse;
  }
  
  .table th,
  .table td {
    border: 1px solid #000;
    padding: 8px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .table th,
  .table td {
    border: 2px solid;
  }
  
  .btn {
    border: 2px solid;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .skeleton-line {
    background: linear-gradient(90deg, #333 25%, #444 50%, #333 75%);
  }
  
  .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

/* Optimize for low-end devices */
@media (max-width: 480px) and (max-height: 800px) {
  .table-container {
    height: 300px;
    overflow-y: auto;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
    overflow-y: auto;
  }
}

/* Optimize font loading */
.text-content {
  font-display: swap;
}

/* Optimize background images */
.bg-image {
  background-attachment: scroll;
  background-size: cover;
  background-position: center;
  will-change: transform;
}

/* Container queries for responsive design */
@container (max-width: 600px) {
  .form-row {
    flex-direction: column;
  }
}

/* Optimize focus states */
.btn:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Optimize hover states */
@media (hover: hover) {
  .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  
  .table tbody tr:hover {
    background-color: #f8f9fa;
  }
}
