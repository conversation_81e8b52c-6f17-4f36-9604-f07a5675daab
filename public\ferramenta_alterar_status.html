<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ferramenta - Alterar Status</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        .tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: white;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .form-control {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #2c3e50;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-select {
            padding: 8px;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            font-size: 0.9rem;
            min-width: 150px;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente { background: #ffeaa7; color: #2d3436; }
        .status-aprovada { background: #74b9ff; color: white; }
        .status-rejeitada { background: #fd79a8; color: white; }
        .status-em_cotacao { background: #a29bfe; color: white; }
        .status-cotado { background: #6c5ce7; color: white; }
        .status-excluida { background: #636e72; color: white; }
        .status-aberta { background: #00b894; color: white; }
        .status-enviada { background: #0984e3; color: white; }
        .status-respondida { background: #00cec9; color: white; }
        .status-fechada { background: #2d3436; color: white; }
        .status-emitido { background: #fdcb6e; color: #2d3436; }
        .status-confirmado { background: #55a3ff; color: white; }
        .status-recebido { background: #00b894; color: white; }
        .status-cancelado { background: #e17055; color: white; }

        .actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .bulk-actions {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success { background: #27ae60; }
        .notification-error { background: #e74c3c; }
        .notification-warning { background: #f39c12; }
        .notification-info { background: #3498db; }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> Ferramenta de Alteração de Status</h1>
            <p>Altere manualmente o status de Solicitações, Cotações e Pedidos de Compra</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('auditoria')">
                <i class="fas fa-search"></i> Auditoria
            </button>
            <button class="tab" onclick="showTab('solicitacoes')">
                <i class="fas fa-file-alt"></i> Solicitações de Compra
            </button>
            <button class="tab" onclick="showTab('cotacoes')">
                <i class="fas fa-file-invoice"></i> Cotações
            </button>
            <button class="tab" onclick="showTab('pedidos')">
                <i class="fas fa-shopping-cart"></i> Pedidos de Compra
            </button>
        </div>

        <!-- Tab Auditoria -->
        <div id="auditoria" class="tab-content active">
            <div class="stats" id="auditoriaStats">
                <!-- Stats de auditoria carregadas dinamicamente -->
            </div>

            <div class="filters">
                <div class="form-group">
                    <label>Tipo de Problema</label>
                    <select class="form-control" id="tipoProblemaFilter">
                        <option value="">Todos os Problemas</option>
                        <option value="duplicados">Números Duplicados</option>
                        <option value="sequencia">Problemas de Sequência</option>
                        <option value="status_inconsistente">Status Inconsistente</option>
                        <option value="sem_numero">Sem Numeração</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Tipo de Documento</label>
                    <select class="form-control" id="tipoDocumentoFilter">
                        <option value="">Todos</option>
                        <option value="solicitacoes">Solicitações</option>
                        <option value="cotacoes">Cotações</option>
                        <option value="pedidos">Pedidos</option>
                    </select>
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="executarAuditoria()">
                        <i class="fas fa-search"></i> Executar Auditoria
                    </button>
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-secondary" onclick="filtrarProblemasAuditoria()">
                        <i class="fas fa-filter"></i> Filtrar Problemas
                    </button>
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-warning" onclick="corrigirNumeracaoAutomatica()">
                        <i class="fas fa-magic"></i> Corrigir Automaticamente
                    </button>
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-info" onclick="renumerarSequencialmente()">
                        <i class="fas fa-sort-numeric-up"></i> Renumerar Sequencialmente
                    </button>
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-warning" onclick="corrigirDatasPedidos()">
                        <i class="fas fa-calendar-plus"></i> Corrigir Datas de Entrega
                    </button>
                </div>
            </div>

            <div class="bulk-actions">
                <label><input type="checkbox" id="selectAllProblemas" onchange="toggleSelectAll('problemas')"> Selecionar Todos</label>
                <button class="btn btn-success" onclick="corrigirSelecionados()">
                    <i class="fas fa-wrench"></i> Corrigir Selecionados
                </button>
                <button class="btn btn-danger" onclick="excluirDuplicados()">
                    <i class="fas fa-trash"></i> Excluir Duplicados
                </button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAllProblemasHeader" onchange="toggleSelectAll('problemas')"></th>
                            <th>Tipo</th>
                            <th>Problema</th>
                            <th>Documento</th>
                            <th>Número Atual</th>
                            <th>Número Sugerido</th>
                            <th>Data</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="auditoriaTableBody">
                        <tr>
                            <td colspan="9" class="loading">
                                <i class="fas fa-spinner"></i><br>
                                Clique em "Executar Auditoria" para identificar problemas
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab Solicitações -->
        <div id="solicitacoes" class="tab-content">
            <div class="stats" id="solicitacoesStats">
                <!-- Stats carregadas dinamicamente -->
            </div>

            <div class="filters">
                <div class="form-group">
                    <label>Status Atual</label>
                    <select class="form-control" id="solicitacoesStatusFilter">
                        <option value="">Todos</option>
                        <option value="PENDENTE">Pendente</option>
                        <option value="APROVADA">Aprovada</option>
                        <option value="REJEITADA">Rejeitada</option>
                        <option value="EM_COTACAO">Em Cotação</option>
                        <option value="COTADO">Cotado</option>
                        <option value="EXCLUIDA">Excluída</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Buscar</label>
                    <input type="text" class="form-control" id="solicitacoesBusca" placeholder="Número ou solicitante...">
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="filtrarSolicitacoes()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <div class="bulk-actions">
                <label><input type="checkbox" id="selectAllSolicitacoes" onchange="toggleSelectAll('solicitacoes')"> Selecionar Todos</label>
                <select class="form-control" id="bulkStatusSolicitacoes" style="max-width: 200px;">
                    <option value="">Alterar status para...</option>
                    <option value="PENDENTE">Pendente</option>
                    <option value="APROVADA">Aprovada</option>
                    <option value="REJEITADA">Rejeitada</option>
                    <option value="EM_COTACAO">Em Cotação</option>
                    <option value="COTADO">Cotado</option>
                    <option value="EXCLUIDA">Excluída</option>
                </select>
                <button class="btn btn-warning" onclick="alterarStatusEmLote('solicitacoes')">
                    <i class="fas fa-edit"></i> Alterar Selecionados
                </button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAllSolicitacoesHeader" onchange="toggleSelectAll('solicitacoes')"></th>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Solicitante</th>
                            <th>Valor</th>
                            <th>Status Atual</th>
                            <th>Novo Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="solicitacoesTableBody">
                        <tr>
                            <td colspan="8" class="loading">
                                <i class="fas fa-spinner"></i><br>
                                Carregando solicitações...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab Cotações -->
        <div id="cotacoes" class="tab-content">
            <div class="stats" id="cotacoesStats">
                <!-- Stats carregadas dinamicamente -->
            </div>

            <div class="filters">
                <div class="form-group">
                    <label>Status Atual</label>
                    <select class="form-control" id="cotacoesStatusFilter">
                        <option value="">Todos</option>
                        <option value="ABERTA">Aberta</option>
                        <option value="ENVIADA">Enviada</option>
                        <option value="RESPONDIDA">Respondida</option>
                        <option value="APROVADA">Aprovada</option>
                        <option value="FECHADA">Fechada</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Buscar</label>
                    <input type="text" class="form-control" id="cotacoesBusca" placeholder="Número da cotação...">
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="filtrarCotacoes()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <div class="bulk-actions">
                <label><input type="checkbox" id="selectAllCotacoes" onchange="toggleSelectAll('cotacoes')"> Selecionar Todos</label>
                <select class="form-control" id="bulkStatusCotacoes" style="max-width: 200px;">
                    <option value="">Alterar status para...</option>
                    <option value="ABERTA">Aberta</option>
                    <option value="ENVIADA">Enviada</option>
                    <option value="RESPONDIDA">Respondida</option>
                    <option value="APROVADA">Aprovada</option>
                    <option value="FECHADA">Fechada</option>
                </select>
                <button class="btn btn-warning" onclick="alterarStatusEmLote('cotacoes')">
                    <i class="fas fa-edit"></i> Alterar Selecionados
                </button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAllCotacoesHeader" onchange="toggleSelectAll('cotacoes')"></th>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Solicitação</th>
                            <th>Fornecedores</th>
                            <th>Status Atual</th>
                            <th>Novo Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="cotacoesTableBody">
                        <tr>
                            <td colspan="8" class="loading">
                                <i class="fas fa-spinner"></i><br>
                                Carregando cotações...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab Pedidos -->
        <div id="pedidos" class="tab-content">
            <div class="stats" id="pedidosStats">
                <!-- Stats carregadas dinamicamente -->
            </div>

            <div class="filters">
                <div class="form-group">
                    <label>Status Atual</label>
                    <select class="form-control" id="pedidosStatusFilter">
                        <option value="">Todos</option>
                        <option value="EMITIDO">Emitido</option>
                        <option value="CONFIRMADO">Confirmado</option>
                        <option value="RECEBIDO">Recebido</option>
                        <option value="CANCELADO">Cancelado</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Buscar</label>
                    <input type="text" class="form-control" id="pedidosBusca" placeholder="Número do pedido...">
                </div>
                <div class="form-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="filtrarPedidos()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <div class="bulk-actions">
                <label><input type="checkbox" id="selectAllPedidos" onchange="toggleSelectAll('pedidos')"> Selecionar Todos</label>
                <select class="form-control" id="bulkStatusPedidos" style="max-width: 200px;">
                    <option value="">Alterar status para...</option>
                    <option value="EMITIDO">Emitido</option>
                    <option value="CONFIRMADO">Confirmado</option>
                    <option value="RECEBIDO">Recebido</option>
                    <option value="CANCELADO">Cancelado</option>
                </select>
                <button class="btn btn-warning" onclick="alterarStatusEmLote('pedidos')">
                    <i class="fas fa-edit"></i> Alterar Selecionados
                </button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAllPedidosHeader" onchange="toggleSelectAll('pedidos')"></th>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Fornecedor</th>
                            <th>Valor</th>
                            <th>Status Atual</th>
                            <th>Novo Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="pedidosTableBody">
                        <tr>
                            <td colspan="8" class="loading">
                                <i class="fas fa-spinner"></i><br>
                                Carregando pedidos...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            updateDoc,
            query,
            orderBy
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let solicitacoes = [];
        let cotacoes = [];
        let pedidos = [];
        let fornecedores = [];
        let problemasAuditoria = [];

        // Inicialização
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarDados();
            await carregarSolicitacoes();
            await carregarCotacoes();
            await carregarPedidos();
            atualizarStats();
        });

        // Carregar dados auxiliares
        async function carregarDados() {
            try {
                const fornecedoresSnap = await getDocs(collection(db, "fornecedores"));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            } catch (error) {
                console.error('Erro ao carregar dados auxiliares:', error);
            }
        }

        // ===== FUNÇÕES DE CARREGAMENTO =====
        async function carregarSolicitacoes() {
            try {
                const solicitacoesSnap = await getDocs(
                    query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))
                );
                solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                renderizarSolicitacoes();
            } catch (error) {
                console.error('Erro ao carregar solicitações:', error);
                showNotification('Erro ao carregar solicitações', 'error');
            }
        }

        async function carregarCotacoes() {
            try {
                const cotacoesSnap = await getDocs(
                    query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"))
                );
                cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                renderizarCotacoes();
            } catch (error) {
                console.error('Erro ao carregar cotações:', error);
                showNotification('Erro ao carregar cotações', 'error');
            }
        }

        async function carregarPedidos() {
            try {
                const pedidosSnap = await getDocs(
                    query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"))
                );
                pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                renderizarPedidos();
            } catch (error) {
                console.error('Erro ao carregar pedidos:', error);
                showNotification('Erro ao carregar pedidos', 'error');
            }
        }

        // ===== FUNÇÕES DE RENDERIZAÇÃO =====
        function renderizarSolicitacoes(dados = solicitacoes) {
            const tbody = document.getElementById('solicitacoesTableBody');
            tbody.innerHTML = '';

            if (dados.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6c757d;">Nenhuma solicitação encontrada</td></tr>';
                return;
            }

            dados.forEach(solicitacao => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="checkbox solicitacao-checkbox" value="${solicitacao.id}"></td>
                    <td><strong>${solicitacao.numero || 'N/A'}</strong></td>
                    <td>${formatDate(solicitacao.dataCriacao)}</td>
                    <td>${solicitacao.solicitante || 'N/A'}</td>
                    <td>R$ ${formatCurrency(solicitacao.valorTotal || 0)}</td>
                    <td><span class="status-badge status-${(solicitacao.status || 'pendente').toLowerCase()}">${getStatusText(solicitacao.status)}</span></td>
                    <td>
                        <select class="status-select" data-id="${solicitacao.id}" data-type="solicitacao">
                            <option value="">Selecione...</option>
                            <option value="PENDENTE" ${solicitacao.status === 'PENDENTE' ? 'selected' : ''}>Pendente</option>
                            <option value="APROVADA" ${solicitacao.status === 'APROVADA' ? 'selected' : ''}>Aprovada</option>
                            <option value="REJEITADA" ${solicitacao.status === 'REJEITADA' ? 'selected' : ''}>Rejeitada</option>
                            <option value="EM_COTACAO" ${solicitacao.status === 'EM_COTACAO' ? 'selected' : ''}>Em Cotação</option>
                            <option value="COTADO" ${solicitacao.status === 'COTADO' ? 'selected' : ''}>Cotado</option>
                            <option value="EXCLUIDA" ${solicitacao.status === 'EXCLUIDA' ? 'selected' : ''}>Excluída</option>
                        </select>
                    </td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-success btn-sm" onclick="alterarStatusIndividual('${solicitacao.id}', 'solicitacao')">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function renderizarCotacoes(dados = cotacoes) {
            const tbody = document.getElementById('cotacoesTableBody');
            tbody.innerHTML = '';

            if (dados.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6c757d;">Nenhuma cotação encontrada</td></tr>';
                return;
            }

            dados.forEach(cotacao => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="checkbox cotacao-checkbox" value="${cotacao.id}"></td>
                    <td><strong>${cotacao.numero || 'N/A'}</strong></td>
                    <td>${formatDate(cotacao.dataCriacao)}</td>
                    <td>${cotacao.solicitacaoId ? 'SC-' + (solicitacoes.find(s => s.id === cotacao.solicitacaoId)?.numero || 'N/A') : 'N/A'}</td>
                    <td>${cotacao.fornecedores ? cotacao.fornecedores.length : 0} fornecedores</td>
                    <td><span class="status-badge status-${(cotacao.status || 'aberta').toLowerCase()}">${getStatusText(cotacao.status)}</span></td>
                    <td>
                        <select class="status-select" data-id="${cotacao.id}" data-type="cotacao">
                            <option value="">Selecione...</option>
                            <option value="ABERTA" ${cotacao.status === 'ABERTA' ? 'selected' : ''}>Aberta</option>
                            <option value="ENVIADA" ${cotacao.status === 'ENVIADA' ? 'selected' : ''}>Enviada</option>
                            <option value="RESPONDIDA" ${cotacao.status === 'RESPONDIDA' ? 'selected' : ''}>Respondida</option>
                            <option value="APROVADA" ${cotacao.status === 'APROVADA' ? 'selected' : ''}>Aprovada</option>
                            <option value="FECHADA" ${cotacao.status === 'FECHADA' ? 'selected' : ''}>Fechada</option>
                        </select>
                    </td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-success btn-sm" onclick="alterarStatusIndividual('${cotacao.id}', 'cotacao')">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function renderizarPedidos(dados = pedidos) {
            const tbody = document.getElementById('pedidosTableBody');
            tbody.innerHTML = '';

            if (dados.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6c757d;">Nenhum pedido encontrado</td></tr>';
                return;
            }

            dados.forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="checkbox pedido-checkbox" value="${pedido.id}"></td>
                    <td><strong>${pedido.numero || 'N/A'}</strong></td>
                    <td>${formatDate(pedido.dataCriacao)}</td>
                    <td>${fornecedor ? fornecedor.razaoSocial : 'N/A'}</td>
                    <td>R$ ${formatCurrency(pedido.valorTotal || 0)}</td>
                    <td><span class="status-badge status-${(pedido.status || 'pendente').toLowerCase()}">${getStatusText(pedido.status)}</span></td>
                    <td>
                        <select class="status-select" data-id="${pedido.id}" data-type="pedido">
                            <option value="">Selecione...</option>
                            <option value="PENDENTE" ${pedido.status === 'PENDENTE' ? 'selected' : ''}>Pendente</option>
                            <option value="ABERTO" ${pedido.status === 'ABERTO' ? 'selected' : ''}>Aberto</option>
                            <option value="APROVADO" ${pedido.status === 'APROVADO' ? 'selected' : ''}>Aprovado</option>
                            <option value="RECEBIDO" ${pedido.status === 'RECEBIDO' ? 'selected' : ''}>Recebido</option>
                            <option value="CANCELADO" ${pedido.status === 'CANCELADO' ? 'selected' : ''}>Cancelado</option>
                        </select>
                    </td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-success btn-sm" onclick="alterarStatusIndividual('${pedido.id}', 'pedido')">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // ===== FUNÇÕES DE ALTERAÇÃO DE STATUS =====
        window.alterarStatusIndividual = async function(id, tipo) {
            const select = document.querySelector(`.status-select[data-id="${id}"][data-type="${tipo}"]`);
            const novoStatus = select.value;

            if (!novoStatus) {
                showNotification('Selecione um status', 'warning');
                return;
            }

            try {
                let collection_name;
                switch (tipo) {
                    case 'solicitacao': collection_name = 'solicitacoesCompra'; break;
                    case 'cotacao': collection_name = 'cotacoes'; break;
                    case 'pedido': collection_name = 'pedidosCompra'; break;
                }

                await updateDoc(doc(db, collection_name, id), {
                    status: novoStatus,
                    ultimaAtualizacao: new Date(),
                    alteradoPor: 'Ferramenta Manual'
                });

                showNotification(`Status alterado para ${getStatusText(novoStatus)}`, 'success');

                // Recarregar dados
                switch (tipo) {
                    case 'solicitacao': await carregarSolicitacoes(); break;
                    case 'cotacao': await carregarCotacoes(); break;
                    case 'pedido': await carregarPedidos(); break;
                }

                atualizarStats();

            } catch (error) {
                console.error('Erro ao alterar status:', error);
                showNotification('Erro ao alterar status: ' + error.message, 'error');
            }
        };

        window.alterarStatusEmLote = async function(tipo) {
            const checkboxes = document.querySelectorAll(`.${tipo.slice(0, -1)}-checkbox:checked`);
            const bulkStatus = document.getElementById(`bulkStatus${tipo.charAt(0).toUpperCase() + tipo.slice(1)}`).value;

            if (checkboxes.length === 0) {
                showNotification('Selecione pelo menos um item', 'warning');
                return;
            }

            if (!bulkStatus) {
                showNotification('Selecione um status', 'warning');
                return;
            }

            if (!confirm(`Alterar status de ${checkboxes.length} ${tipo} para ${getStatusText(bulkStatus)}?`)) {
                return;
            }

            try {
                let collection_name;
                switch (tipo) {
                    case 'solicitacoes': collection_name = 'solicitacoesCompra'; break;
                    case 'cotacoes': collection_name = 'cotacoes'; break;
                    case 'pedidos': collection_name = 'pedidosCompra'; break;
                }

                const promises = Array.from(checkboxes).map(checkbox =>
                    updateDoc(doc(db, collection_name, checkbox.value), {
                        status: bulkStatus,
                        ultimaAtualizacao: new Date(),
                        alteradoPor: 'Ferramenta Manual - Lote'
                    })
                );

                await Promise.all(promises);

                showNotification(`${checkboxes.length} ${tipo} alterados com sucesso!`, 'success');

                // Recarregar dados
                switch (tipo) {
                    case 'solicitacoes': await carregarSolicitacoes(); break;
                    case 'cotacoes': await carregarCotacoes(); break;
                    case 'pedidos': await carregarPedidos(); break;
                }

                atualizarStats();

                // Limpar seleções
                document.getElementById(`selectAll${tipo.charAt(0).toUpperCase() + tipo.slice(1)}`).checked = false;
                document.getElementById(`bulkStatus${tipo.charAt(0).toUpperCase() + tipo.slice(1)}`).value = '';

            } catch (error) {
                console.error('Erro ao alterar status em lote:', error);
                showNotification('Erro ao alterar status em lote: ' + error.message, 'error');
            }
        };

        // ===== FUNÇÕES DE AUDITORIA =====
        window.executarAuditoria = async function() {
            problemasAuditoria = [];

            showNotification('Executando auditoria...', 'info');

            // Verificar números duplicados
            verificarNumerosDuplicados();

            // Verificar sequência de numeração
            verificarSequenciaNumeracao();

            // Verificar status inconsistentes
            verificarStatusInconsistentes();

            // Verificar documentos sem numeração
            verificarDocumentosSemNumero();

            renderizarProblemasAuditoria();
            atualizarStatsAuditoria();

            showNotification(`Auditoria concluída. ${problemasAuditoria.length} problemas encontrados.`, 'success');
        };

        function verificarNumerosDuplicados() {
            // Verificar solicitações duplicadas
            const numerosSolicitacoes = {};
            solicitacoes.forEach(sol => {
                if (sol.numero) {
                    if (numerosSolicitacoes[sol.numero]) {
                        problemasAuditoria.push({
                            id: sol.id,
                            tipo: 'solicitacao',
                            problema: 'duplicados',
                            descricao: `Número ${sol.numero} duplicado`,
                            numeroAtual: sol.numero,
                            numeroSugerido: gerarProximoNumero('solicitacao'),
                            data: sol.dataCriacao,
                            status: sol.status,
                            documento: sol
                        });
                    } else {
                        numerosSolicitacoes[sol.numero] = true;
                    }
                }
            });

            // Verificar cotações duplicadas
            const numerosCotacoes = {};
            cotacoes.forEach(cot => {
                if (cot.numero) {
                    if (numerosCotacoes[cot.numero]) {
                        problemasAuditoria.push({
                            id: cot.id,
                            tipo: 'cotacao',
                            problema: 'duplicados',
                            descricao: `Número ${cot.numero} duplicado`,
                            numeroAtual: cot.numero,
                            numeroSugerido: gerarProximoNumero('cotacao'),
                            data: cot.dataCriacao,
                            status: cot.status,
                            documento: cot
                        });
                    } else {
                        numerosCotacoes[cot.numero] = true;
                    }
                }
            });

            // Verificar pedidos duplicados
            const numerosPedidos = {};
            pedidos.forEach(ped => {
                if (ped.numero) {
                    if (numerosPedidos[ped.numero]) {
                        problemasAuditoria.push({
                            id: ped.id,
                            tipo: 'pedido',
                            problema: 'duplicados',
                            descricao: `Número ${ped.numero} duplicado`,
                            numeroAtual: ped.numero,
                            numeroSugerido: gerarProximoNumero('pedido'),
                            data: ped.dataCriacao,
                            status: ped.status,
                            documento: ped
                        });
                    } else {
                        numerosPedidos[ped.numero] = true;
                    }
                }
            });
        }

        function verificarSequenciaNumeracao() {
            // Verificar sequência de solicitações
            const numerosSol = solicitacoes
                .filter(s => s.numero && !isNaN(s.numero))
                .map(s => parseInt(s.numero))
                .sort((a, b) => a - b);

            for (let i = 1; i < numerosSol.length; i++) {
                if (numerosSol[i] - numerosSol[i-1] > 1) {
                    // Há lacuna na sequência
                    const solicitacao = solicitacoes.find(s => parseInt(s.numero) === numerosSol[i]);
                    if (solicitacao) {
                        problemasAuditoria.push({
                            id: solicitacao.id,
                            tipo: 'solicitacao',
                            problema: 'sequencia',
                            descricao: `Lacuna na sequência: ${numerosSol[i-1]} → ${numerosSol[i]}`,
                            numeroAtual: solicitacao.numero,
                            numeroSugerido: (numerosSol[i-1] + 1).toString(),
                            data: solicitacao.dataCriacao,
                            status: solicitacao.status,
                            documento: solicitacao
                        });
                    }
                }
            }

            // Verificar sequência de cotações
            const numerosCot = cotacoes
                .filter(c => c.numero && !isNaN(c.numero))
                .map(c => parseInt(c.numero))
                .sort((a, b) => a - b);

            for (let i = 1; i < numerosCot.length; i++) {
                if (numerosCot[i] - numerosCot[i-1] > 1) {
                    const cotacao = cotacoes.find(c => parseInt(c.numero) === numerosCot[i]);
                    if (cotacao) {
                        problemasAuditoria.push({
                            id: cotacao.id,
                            tipo: 'cotacao',
                            problema: 'sequencia',
                            descricao: `Lacuna na sequência: ${numerosCot[i-1]} → ${numerosCot[i]}`,
                            numeroAtual: cotacao.numero,
                            numeroSugerido: (numerosCot[i-1] + 1).toString(),
                            data: cotacao.dataCriacao,
                            status: cotacao.status,
                            documento: cotacao
                        });
                    }
                }
            }

            // Verificar sequência de pedidos
            const numerosPed = pedidos
                .filter(p => p.numero && !isNaN(p.numero))
                .map(p => parseInt(p.numero))
                .sort((a, b) => a - b);

            for (let i = 1; i < numerosPed.length; i++) {
                if (numerosPed[i] - numerosPed[i-1] > 1) {
                    const pedido = pedidos.find(p => parseInt(p.numero) === numerosPed[i]);
                    if (pedido) {
                        problemasAuditoria.push({
                            id: pedido.id,
                            tipo: 'pedido',
                            problema: 'sequencia',
                            descricao: `Lacuna na sequência: ${numerosPed[i-1]} → ${numerosPed[i]}`,
                            numeroAtual: pedido.numero,
                            numeroSugerido: (numerosPed[i-1] + 1).toString(),
                            data: pedido.dataCriacao,
                            status: pedido.status,
                            documento: pedido
                        });
                    }
                }
            }
        }

        function verificarStatusInconsistentes() {
            // Verificar solicitações com status inconsistente
            solicitacoes.forEach(sol => {
                if (sol.status === 'APROVADA' && sol.dataCriacao) {
                    const diasAtraso = (new Date() - sol.dataCriacao.toDate()) / (1000 * 60 * 60 * 24);
                    if (diasAtraso > 30) { // Mais de 30 dias aprovada sem cotação
                        problemasAuditoria.push({
                            id: sol.id,
                            tipo: 'solicitacao',
                            problema: 'status_inconsistente',
                            descricao: `Aprovada há ${Math.floor(diasAtraso)} dias sem cotação`,
                            numeroAtual: sol.numero,
                            numeroSugerido: '-',
                            data: sol.dataCriacao,
                            status: sol.status,
                            documento: sol
                        });
                    }
                }
            });

            // Verificar cotações abertas há muito tempo
            cotacoes.forEach(cot => {
                if (cot.status === 'ABERTA' && cot.dataCriacao) {
                    const diasAtraso = (new Date() - cot.dataCriacao.toDate()) / (1000 * 60 * 60 * 24);
                    if (diasAtraso > 15) { // Mais de 15 dias aberta
                        problemasAuditoria.push({
                            id: cot.id,
                            tipo: 'cotacao',
                            problema: 'status_inconsistente',
                            descricao: `Aberta há ${Math.floor(diasAtraso)} dias`,
                            numeroAtual: cot.numero,
                            numeroSugerido: '-',
                            data: cot.dataCriacao,
                            status: cot.status,
                            documento: cot
                        });
                    }
                }
            });
        }

        function verificarDocumentosSemNumero() {
            // Verificar solicitações sem número
            solicitacoes.forEach(sol => {
                if (!sol.numero || sol.numero === '' || sol.numero === null) {
                    problemasAuditoria.push({
                        id: sol.id,
                        tipo: 'solicitacao',
                        problema: 'sem_numero',
                        descricao: 'Documento sem numeração',
                        numeroAtual: 'N/A',
                        numeroSugerido: gerarProximoNumero('solicitacao'),
                        data: sol.dataCriacao,
                        status: sol.status,
                        documento: sol
                    });
                }
            });

            // Verificar cotações sem número
            cotacoes.forEach(cot => {
                if (!cot.numero || cot.numero === '' || cot.numero === null) {
                    problemasAuditoria.push({
                        id: cot.id,
                        tipo: 'cotacao',
                        problema: 'sem_numero',
                        descricao: 'Documento sem numeração',
                        numeroAtual: 'N/A',
                        numeroSugerido: gerarProximoNumero('cotacao'),
                        data: cot.dataCriacao,
                        status: cot.status,
                        documento: cot
                    });
                }
            });

            // Verificar pedidos sem número
            pedidos.forEach(ped => {
                if (!ped.numero || ped.numero === '' || ped.numero === null) {
                    problemasAuditoria.push({
                        id: ped.id,
                        tipo: 'pedido',
                        problema: 'sem_numero',
                        descricao: 'Documento sem numeração',
                        numeroAtual: 'N/A',
                        numeroSugerido: gerarProximoNumero('pedido'),
                        data: ped.dataCriacao,
                        status: ped.status,
                        documento: ped
                    });
                }
            });
        }

        function gerarProximoNumero(tipo) {
            let maiorNumero = 0;

            switch (tipo) {
                case 'solicitacao':
                    solicitacoes.forEach(s => {
                        if (s.numero && !isNaN(s.numero)) {
                            maiorNumero = Math.max(maiorNumero, parseInt(s.numero));
                        }
                    });
                    break;
                case 'cotacao':
                    cotacoes.forEach(c => {
                        if (c.numero && !isNaN(c.numero)) {
                            maiorNumero = Math.max(maiorNumero, parseInt(c.numero));
                        }
                    });
                    break;
                case 'pedido':
                    pedidos.forEach(p => {
                        if (p.numero && !isNaN(p.numero)) {
                            maiorNumero = Math.max(maiorNumero, parseInt(p.numero));
                        }
                    });
                    break;
            }

            return (maiorNumero + 1).toString();
        }

        window.renumerarSequencialmente = async function() {
            const tipoSelecionado = document.getElementById('tipoDocumentoFilter').value;

            if (!tipoSelecionado) {
                showNotification('Selecione um tipo de documento para renumerar', 'warning');
                return;
            }

            if (!confirm(`ATENÇÃO: Esta operação irá renumerar TODOS os documentos do tipo "${tipoSelecionado}" em ordem cronológica. Esta ação não pode ser desfeita! Deseja continuar?`)) {
                return;
            }

            showNotification('Renumerando documentos sequencialmente...', 'info');

            try {
                let documentos = [];
                let collection_name = '';
                let prefixo = '';

                switch (tipoSelecionado) {
                    case 'solicitacoes':
                        documentos = [...solicitacoes].sort((a, b) => {
                            const dataA = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao);
                            const dataB = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao);
                            return dataA - dataB;
                        });
                        collection_name = 'solicitacoesCompra';
                        prefixo = 'SC';
                        break;
                    case 'cotacoes':
                        documentos = [...cotacoes].sort((a, b) => {
                            const dataA = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao);
                            const dataB = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao);
                            return dataA - dataB;
                        });
                        collection_name = 'cotacoes';
                        prefixo = 'COT';
                        break;
                    case 'pedidos':
                        documentos = [...pedidos].sort((a, b) => {
                            const dataA = a.dataEmissao?.toDate ? a.dataEmissao.toDate() : new Date(a.dataEmissao);
                            const dataB = b.dataEmissao?.toDate ? b.dataEmissao.toDate() : new Date(b.dataEmissao);
                            return dataA - dataB;
                        });
                        collection_name = 'pedidosCompra';
                        prefixo = 'PC';
                        break;
                }

                let contador = 1;
                const ano = new Date().getFullYear();

                for (const documento of documentos) {
                    const novoNumero = `${prefixo}${ano}${contador.toString().padStart(4, '0')}`;

                    await updateDoc(doc(db, collection_name, documento.id), {
                        numero: novoNumero,
                        ultimaAtualizacao: new Date(),
                        alteradoPor: 'Auditoria - Renumeração Sequencial',
                        numeroAnterior: documento.numero // Guardar número anterior para histórico
                    });

                    contador++;
                }

                showNotification(`${documentos.length} documentos renumerados sequencialmente!`, 'success');

                // Recarregar dados e executar auditoria novamente
                await carregarDados();
                await carregarSolicitacoes();
                await carregarCotacoes();
                await carregarPedidos();
                executarAuditoria();

            } catch (error) {
                console.error('Erro na renumeração sequencial:', error);
                showNotification('Erro na renumeração sequencial: ' + error.message, 'error');
            }
        };

        // ===== FUNÇÕES DE FILTRO =====
        window.filtrarSolicitacoes = function() {
            const statusFilter = document.getElementById('solicitacoesStatusFilter').value;
            const busca = document.getElementById('solicitacoesBusca').value.toLowerCase();

            let filtradas = solicitacoes;

            if (statusFilter) {
                filtradas = filtradas.filter(s => s.status === statusFilter);
            }

            if (busca) {
                filtradas = filtradas.filter(s =>
                    (s.numero && s.numero.toString().toLowerCase().includes(busca)) ||
                    (s.solicitante && s.solicitante.toLowerCase().includes(busca))
                );
            }

            renderizarSolicitacoes(filtradas);
        };

        window.filtrarCotacoes = function() {
            const statusFilter = document.getElementById('cotacoesStatusFilter').value;
            const busca = document.getElementById('cotacoesBusca').value.toLowerCase();

            let filtradas = cotacoes;

            if (statusFilter) {
                filtradas = filtradas.filter(c => c.status === statusFilter);
            }

            if (busca) {
                filtradas = filtradas.filter(c =>
                    (c.numero && c.numero.toString().toLowerCase().includes(busca))
                );
            }

            renderizarCotacoes(filtradas);
        };

        window.filtrarPedidos = function() {
            const statusFilter = document.getElementById('pedidosStatusFilter').value;
            const busca = document.getElementById('pedidosBusca').value.toLowerCase();

            let filtrados = pedidos;

            if (statusFilter) {
                filtrados = filtrados.filter(p => p.status === statusFilter);
            }

            if (busca) {
                filtrados = filtrados.filter(p =>
                    (p.numero && p.numero.toString().toLowerCase().includes(busca))
                );
            }

            renderizarPedidos(filtrados);
        };

        window.filtrarProblemasAuditoria = function() {
            const tipoProblema = document.getElementById('tipoProblemaFilter').value;
            const tipoDocumento = document.getElementById('tipoDocumentoFilter').value;

            let filtrados = problemasAuditoria;

            if (tipoProblema) {
                filtrados = filtrados.filter(p => p.problema === tipoProblema);
            }

            if (tipoDocumento) {
                filtrados = filtrados.filter(p => p.tipo === tipoDocumento.slice(0, -1)); // Remove 's' do final
            }

            renderizarProblemasAuditoria(filtrados);
        };

        function renderizarProblemasAuditoria(dados = problemasAuditoria) {
            const tbody = document.getElementById('auditoriaTableBody');
            tbody.innerHTML = '';

            if (dados.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; color: #6c757d;">Nenhum problema encontrado</td></tr>';
                return;
            }

            dados.forEach(problema => {
                const row = document.createElement('tr');
                const tipoIcon = {
                    'solicitacao': 'fas fa-file-alt',
                    'cotacao': 'fas fa-file-invoice',
                    'pedido': 'fas fa-shopping-cart'
                };

                const problemaBadge = {
                    'duplicados': 'badge-danger',
                    'sequencia': 'badge-warning',
                    'status_inconsistente': 'badge-info',
                    'sem_numero': 'badge-secondary'
                };

                row.innerHTML = `
                    <td><input type="checkbox" class="checkbox problema-checkbox" value="${problema.id}" data-tipo="${problema.tipo}"></td>
                    <td><i class="${tipoIcon[problema.tipo]}"></i> ${problema.tipo.charAt(0).toUpperCase() + problema.tipo.slice(1)}</td>
                    <td><span class="badge ${problemaBadge[problema.problema]}">${problema.descricao}</span></td>
                    <td>${problema.documento.solicitante || problema.documento.fornecedorId || 'N/A'}</td>
                    <td><strong>${problema.numeroAtual}</strong></td>
                    <td><strong style="color: #27ae60;">${problema.numeroSugerido}</strong></td>
                    <td>${formatDate(problema.data)}</td>
                    <td><span class="status-badge status-${(problema.status || 'pendente').toLowerCase()}">${getStatusText(problema.status)}</span></td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-success btn-sm" onclick="corrigirProblemaIndividual('${problema.id}', '${problema.tipo}', '${problema.problema}', '${problema.numeroSugerido}')" title="Corrigir">
                                <i class="fas fa-wrench"></i>
                            </button>
                            ${problema.problema === 'duplicados' ? `
                            <button class="btn btn-danger btn-sm" onclick="excluirDocumento('${problema.id}', '${problema.tipo}')" title="Excluir">
                                <i class="fas fa-trash"></i>
                            </button>
                            ` : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        window.corrigirProblemaIndividual = async function(id, tipo, problema, numeroSugerido) {
            if (!confirm(`Deseja corrigir este problema alterando o número para ${numeroSugerido}?`)) {
                return;
            }

            try {
                let collection_name;
                switch (tipo) {
                    case 'solicitacao': collection_name = 'solicitacoesCompra'; break;
                    case 'cotacao': collection_name = 'cotacoes'; break;
                    case 'pedido': collection_name = 'pedidosCompra'; break;
                }

                await updateDoc(doc(db, collection_name, id), {
                    numero: numeroSugerido,
                    ultimaAtualizacao: new Date(),
                    alteradoPor: 'Auditoria - Correção Manual'
                });

                showNotification('Problema corrigido com sucesso!', 'success');

                // Recarregar dados e executar auditoria novamente
                await carregarDados();
                await carregarSolicitacoes();
                await carregarCotacoes();
                await carregarPedidos();
                executarAuditoria();

            } catch (error) {
                console.error('Erro ao corrigir problema:', error);
                showNotification('Erro ao corrigir problema: ' + error.message, 'error');
            }
        };

        window.corrigirNumeracaoAutomatica = async function() {
            if (!confirm('Deseja corrigir automaticamente todos os problemas de numeração? Esta ação não pode ser desfeita.')) {
                return;
            }

            showNotification('Corrigindo numeração automaticamente...', 'info');

            try {
                let corrigidos = 0;

                for (const problema of problemasAuditoria) {
                    if (problema.problema === 'sem_numero' || problema.problema === 'duplicados') {
                        let collection_name;
                        switch (problema.tipo) {
                            case 'solicitacao': collection_name = 'solicitacoesCompra'; break;
                            case 'cotacao': collection_name = 'cotacoes'; break;
                            case 'pedido': collection_name = 'pedidosCompra'; break;
                        }

                        await updateDoc(doc(db, collection_name, problema.id), {
                            numero: problema.numeroSugerido,
                            ultimaAtualizacao: new Date(),
                            alteradoPor: 'Auditoria - Correção Automática'
                        });

                        corrigidos++;
                    }
                }

                showNotification(`${corrigidos} problemas corrigidos automaticamente!`, 'success');

                // Recarregar dados e executar auditoria novamente
                await carregarDados();
                await carregarSolicitacoes();
                await carregarCotacoes();
                await carregarPedidos();
                executarAuditoria();

            } catch (error) {
                console.error('Erro na correção automática:', error);
                showNotification('Erro na correção automática: ' + error.message, 'error');
            }
        };

        window.corrigirSelecionados = async function() {
            const checkboxes = document.querySelectorAll('.problema-checkbox:checked');

            if (checkboxes.length === 0) {
                showNotification('Selecione pelo menos um problema para corrigir', 'warning');
                return;
            }

            if (!confirm(`Deseja corrigir ${checkboxes.length} problemas selecionados?`)) {
                return;
            }

            showNotification('Corrigindo problemas selecionados...', 'info');

            try {
                let corrigidos = 0;

                for (const checkbox of checkboxes) {
                    const problema = problemasAuditoria.find(p => p.id === checkbox.value && p.tipo === checkbox.dataset.tipo);
                    if (problema && (problema.problema === 'sem_numero' || problema.problema === 'duplicados')) {
                        let collection_name;
                        switch (problema.tipo) {
                            case 'solicitacao': collection_name = 'solicitacoesCompra'; break;
                            case 'cotacao': collection_name = 'cotacoes'; break;
                            case 'pedido': collection_name = 'pedidosCompra'; break;
                        }

                        await updateDoc(doc(db, collection_name, problema.id), {
                            numero: problema.numeroSugerido,
                            ultimaAtualizacao: new Date(),
                            alteradoPor: 'Auditoria - Correção em Lote'
                        });

                        corrigidos++;
                    }
                }

                showNotification(`${corrigidos} problemas corrigidos!`, 'success');

                // Recarregar dados e executar auditoria novamente
                await carregarDados();
                await carregarSolicitacoes();
                await carregarCotacoes();
                await carregarPedidos();
                executarAuditoria();

            } catch (error) {
                console.error('Erro ao corrigir problemas selecionados:', error);
                showNotification('Erro ao corrigir problemas: ' + error.message, 'error');
            }
        };

        window.excluirDuplicados = async function() {
            const checkboxes = document.querySelectorAll('.problema-checkbox:checked');
            const duplicados = Array.from(checkboxes).filter(cb => {
                const problema = problemasAuditoria.find(p => p.id === cb.value && p.tipo === cb.dataset.tipo);
                return problema && problema.problema === 'duplicados';
            });

            if (duplicados.length === 0) {
                showNotification('Selecione pelo menos um documento duplicado para excluir', 'warning');
                return;
            }

            if (!confirm(`ATENÇÃO: Deseja EXCLUIR permanentemente ${duplicados.length} documentos duplicados? Esta ação não pode ser desfeita!`)) {
                return;
            }

            showNotification('Excluindo documentos duplicados...', 'info');

            try {
                for (const checkbox of duplicados) {
                    const problema = problemasAuditoria.find(p => p.id === checkbox.value && p.tipo === checkbox.dataset.tipo);
                    if (problema) {
                        let collection_name;
                        switch (problema.tipo) {
                            case 'solicitacao': collection_name = 'solicitacoesCompra'; break;
                            case 'cotacao': collection_name = 'cotacoes'; break;
                            case 'pedido': collection_name = 'pedidosCompra'; break;
                        }

                        await updateDoc(doc(db, collection_name, problema.id), {
                            status: 'EXCLUIDA',
                            ultimaAtualizacao: new Date(),
                            alteradoPor: 'Auditoria - Exclusão de Duplicado',
                            observacoes: 'Documento excluído por duplicação durante auditoria'
                        });
                    }
                }

                showNotification(`${duplicados.length} documentos duplicados excluídos!`, 'success');

                // Recarregar dados e executar auditoria novamente
                await carregarDados();
                await carregarSolicitacoes();
                await carregarCotacoes();
                await carregarPedidos();
                executarAuditoria();

            } catch (error) {
                console.error('Erro ao excluir duplicados:', error);
                showNotification('Erro ao excluir duplicados: ' + error.message, 'error');
            }
        };

        function atualizarStatsAuditoria() {
            const stats = {
                total: problemasAuditoria.length,
                duplicados: problemasAuditoria.filter(p => p.problema === 'duplicados').length,
                sequencia: problemasAuditoria.filter(p => p.problema === 'sequencia').length,
                status_inconsistente: problemasAuditoria.filter(p => p.problema === 'status_inconsistente').length,
                sem_numero: problemasAuditoria.filter(p => p.problema === 'sem_numero').length
            };

            document.getElementById('auditoriaStats').innerHTML = `
                <div class="stat-card">
                    <div class="stat-number" style="color: ${stats.total > 0 ? '#e74c3c' : '#27ae60'}">${stats.total}</div>
                    <div class="stat-label">Total de Problemas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #e74c3c">${stats.duplicados}</div>
                    <div class="stat-label">Duplicados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f39c12">${stats.sequencia}</div>
                    <div class="stat-label">Problemas de Sequência</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #3498db">${stats.status_inconsistente}</div>
                    <div class="stat-label">Status Inconsistente</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #6c757d">${stats.sem_numero}</div>
                    <div class="stat-label">Sem Numeração</div>
                </div>
            `;
        }





        // ===== FUNÇÕES DE INTERFACE =====
        window.showTab = function(tabName) {
            // Ocultar todas as abas
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remover classe active de todos os botões
            document.querySelectorAll('.tab').forEach(btn => {
                btn.classList.remove('active');
            });

            // Mostrar aba selecionada
            document.getElementById(tabName).classList.add('active');

            // Ativar botão correspondente
            event.target.classList.add('active');

            // Carregar dados da aba se necessário
            switch (tabName) {
                case 'auditoria':
                    // Não executar auditoria automaticamente, deixar para o usuário clicar
                    break;
                case 'cotacoes':
                    if (cotacoes.length === 0) carregarCotacoes();
                    break;
                case 'pedidos':
                    if (pedidos.length === 0) carregarPedidos();
                    break;
            }
        };

        window.toggleSelectAll = function(tipo) {
            const selectAll = document.getElementById(`selectAll${tipo.charAt(0).toUpperCase() + tipo.slice(1)}`);
            const checkboxes = document.querySelectorAll(`.${tipo.slice(0, -1)}-checkbox`);

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        };

        function atualizarStats() {
            // Stats Solicitações
            const solicitacoesStats = {
                total: solicitacoes.length,
                pendentes: solicitacoes.filter(s => s.status === 'PENDENTE').length,
                aprovadas: solicitacoes.filter(s => s.status === 'APROVADA').length,
                em_cotacao: solicitacoes.filter(s => s.status === 'EM_COTACAO').length
            };

            document.getElementById('solicitacoesStats').innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${solicitacoesStats.total}</div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${solicitacoesStats.pendentes}</div>
                    <div class="stat-label">Pendentes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${solicitacoesStats.aprovadas}</div>
                    <div class="stat-label">Aprovadas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${solicitacoesStats.em_cotacao}</div>
                    <div class="stat-label">Em Cotação</div>
                </div>
            `;

            // Stats Cotações
            if (cotacoes.length > 0) {
                const cotacoesStats = {
                    total: cotacoes.length,
                    abertas: cotacoes.filter(c => c.status === 'ABERTA').length,
                    enviadas: cotacoes.filter(c => c.status === 'ENVIADA').length,
                    fechadas: cotacoes.filter(c => c.status === 'FECHADA').length
                };

                document.getElementById('cotacoesStats').innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${cotacoesStats.total}</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${cotacoesStats.abertas}</div>
                        <div class="stat-label">Abertas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${cotacoesStats.enviadas}</div>
                        <div class="stat-label">Enviadas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${cotacoesStats.fechadas}</div>
                        <div class="stat-label">Fechadas</div>
                    </div>
                `;
            }

            // Stats Pedidos
            if (pedidos.length > 0) {
                const pedidosStats = {
                    total: pedidos.length,
                    emitidos: pedidos.filter(p => p.status === 'EMITIDO').length,
                    confirmados: pedidos.filter(p => p.status === 'CONFIRMADO').length,
                    recebidos: pedidos.filter(p => p.status === 'RECEBIDO').length
                };

                document.getElementById('pedidosStats').innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${pedidosStats.total}</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${pedidosStats.emitidos}</div>
                        <div class="stat-label">Emitidos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${pedidosStats.confirmados}</div>
                        <div class="stat-label">Confirmados</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${pedidosStats.recebidos}</div>
                        <div class="stat-label">Recebidos</div>
                    </div>
                `;
            }
        }

        // ===== FUNÇÕES AUXILIARES =====
        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleDateString('pt-BR');
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        function getStatusText(status) {
            const statusMap = {
                // Status de Solicitações
                'PENDENTE': 'Pendente',
                'APROVADA': 'Aprovada',
                'REJEITADA': 'Rejeitada',
                'EM_COTACAO': 'Em Cotação',
                'COTADO': 'Cotado',
                'EXCLUIDA': 'Excluída',

                // Status de Cotações
                'ABERTA': 'Aberta',
                'ENVIADA': 'Enviada',
                'RESPONDIDA': 'Respondida',
                'FECHADA': 'Fechada',

                // Status de Pedidos de Compra
                'ABERTO': 'Aberto',
                'APROVADO': 'Aprovado',
                'RECEBIDO': 'Recebido',
                'CANCELADO': 'Cancelado'
            };
            return statusMap[status] || status;
        }

        function showNotification(message, type = 'success', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    </script>
</body>
</html>
