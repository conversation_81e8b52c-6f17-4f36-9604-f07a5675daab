<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ Configuração de Parâmetros do Sistema</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .params-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .params-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .params-table tr:hover {
            background: #f8f9fa;
        }

        .param-key {
            font-weight: 600;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .param-collection {
            font-size: 12px;
            color: #6c757d;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        .param-value {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #28a745;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-switch.active::before {
            transform: translateX(30px);
        }

        .param-input {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            width: 200px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        .param-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .param-input[type="number"] {
            width: 120px;
        }

        .param-input select {
            width: 220px;
            cursor: pointer;
        }

        .param-description {
            font-size: 13px;
            color: #6c757d;
            margin-top: 5px;
            font-style: italic;
        }

        .collection-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .collection-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .actions-bar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-indicator.connected {
            background: #d4edda;
            color: #155724;
        }

        .status-indicator.disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .inconsistency-warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .inconsistency-warning h4 {
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-cogs"></i>
                Configuração de Parâmetros do Sistema
            </h1>
            <div class="header-actions">
                <span id="connectionStatus" class="status-indicator disconnected">
                    <i class="fas fa-database"></i>
                    Conectando...
                </span>
            </div>
        </div>

        <div class="main-content">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Editor Unificado:</strong>
                Este editor mostra TODOS os parâmetros do sistema em uma única tabela, permitindo visualizar e editar configurações de diferentes coleções.
            </div>

            <div class="actions-bar">
                <button class="btn btn-primary" onclick="carregarParametros()">
                    <i class="fas fa-sync"></i>
                    🔄 Recarregar Parâmetros
                </button>
                
                <button class="btn btn-success" onclick="salvarTodos()">
                    <i class="fas fa-save"></i>
                    💾 Salvar Todos
                </button>
                
                <button class="btn btn-warning" onclick="verificarInconsistencias()">
                    <i class="fas fa-exclamation-triangle"></i>
                    ⚠️ Verificar Inconsistências
                </button>
                
                <button class="btn btn-danger" onclick="resetarPadroes()">
                    <i class="fas fa-undo"></i>
                    🔄 Resetar Padrões
                </button>
            </div>

            <div id="content">
                <div class="loading">
                    <div class="spinner"></div>
                    Carregando parâmetros do sistema...
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <script>
        // Variáveis globais
        let firebaseConfig = {};
        let db = null;
        let parametros = {};
        let parametrosOriginais = {};

        // Definição ORGANIZADA por categorias de parâmetros
        const CATEGORIAS_PARAMETROS = {
            'configuracao_sistema': {
                titulo: '⚙️ Configuração do Sistema',
                icone: 'fas fa-cogs',
                parametros: {
                    controleArmazem: { tipo: 'boolean', padrao: false, descricao: 'Ativa/desativa controle de múltiplos armazéns', usado_em: ['Sistema geral de armazéns'] },
                    moduloQualidadeAtivo: { tipo: 'boolean', padrao: false, descricao: 'Ativa/desativa o módulo completo de controle de qualidade', usado_em: ['pedidos_compra.html', 'inspecao_recebimento.html'] },
                    inspecaoRecebimento: { tipo: 'boolean', padrao: false, descricao: 'Exige inspeção obrigatória no recebimento de materiais', usado_em: ['pedidos_compra.html', 'recebimento_materiais_melhorado.html'] },
                    armazemQualidade: { tipo: 'boolean', padrao: false, descricao: 'Ativa armazém específico para inspeção', usado_em: ['recebimento_materiais_melhorado.html'] },
                    controleQualidade: { tipo: 'boolean', padrao: false, descricao: 'Ativa módulo completo de controle de qualidade', usado_em: ['recebimento_materiais_melhorado.html'] }
                }
            },

            'parametros_gerais': {
                titulo: '🔧 Parâmetros Gerais',
                icone: 'fas fa-sliders-h',
                parametros: {
                    diasAlerta: { tipo: 'number', padrao: 7, descricao: 'Dias para alertas de vencimento', usado_em: ['Sistema geral'] },
                    moedaPadrao: { tipo: 'select', padrao: 'BRL', opcoes: ['BRL', 'USD', 'EUR'], descricao: 'Moeda padrão do sistema', usado_em: ['Sistema geral de preços'] },
                    formatoData: { tipo: 'select', padrao: 'DD/MM/YYYY', opcoes: ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'], descricao: 'Formato de exibição de datas', usado_em: ['Sistema geral'] },
                    fusoHorario: { tipo: 'select', padrao: 'America/Sao_Paulo', opcoes: ['America/Sao_Paulo', 'UTC'], descricao: 'Fuso horário do sistema', usado_em: ['Sistema geral'] }
                }
            },

            'parametros_producao': {
                titulo: '🏭 Parâmetros de Produção',
                icone: 'fas fa-industry',
                parametros: {
                    aglutinarOps: { tipo: 'boolean', padrao: false, descricao: 'Aglutinar OPs do mesmo produto e data', usado_em: ['ordens_producao.html'] },
                    usarSaldoEstoque: { tipo: 'boolean', padrao: true, descricao: 'Considerar saldo disponível em estoque ✅ OBRIGATÓRIO', usado_em: ['ordens_producao.html'] },
                    opsFirmes: { tipo: 'boolean', padrao: false, descricao: 'Criar OPs como firmes por padrão', usado_em: ['ordens_producao.html'] },
                    reservarEstoque: { tipo: 'boolean', padrao: true, descricao: 'Reservar estoque automaticamente ao criar OPs ✅ OBRIGATÓRIO', usado_em: ['ordens_producao.html'] },
                    tipoApontamento: { tipo: 'select', padrao: 'simples', opcoes: ['simples', 'detalhado'], descricao: 'Tipo de apontamento de produção', usado_em: ['apontamento_producao.html'] },
                    diasUteisMes: { tipo: 'number', padrao: 22, descricao: 'Dias úteis por mês para cálculos', usado_em: ['Cálculos de capacidade'] },
                    horasTurno: { tipo: 'number', padrao: 8, descricao: 'Horas por turno de trabalho', usado_em: ['Cálculos de capacidade'] },
                    permitirProducaoSemEstoque: { tipo: 'boolean', padrao: false, descricao: 'Permite produção sem estoque suficiente', usado_em: ['ordens_producao.html'] },
                    controleQualidadeObrigatorio: { tipo: 'boolean', padrao: false, descricao: 'Controle de qualidade obrigatório', usado_em: ['recebimento_materiais_melhorado.html'] }
                }
            },

            'parametros_estoque': {
                titulo: '📦 Parâmetros de Estoque',
                icone: 'fas fa-boxes',
                parametros: {
                    metodoValorizacao: { tipo: 'select', padrao: 'FIFO', opcoes: ['FIFO', 'LIFO', 'CUSTO_MEDIO'], descricao: 'Método de valorização do estoque', usado_em: ['services/inventory-service.js'] },
                    permitirEstoqueNegativo: { tipo: 'boolean', padrao: false, descricao: 'Permite saldo de estoque negativo', usado_em: ['services/inventory-service.js'] },
                    toleranciaRecebimento: { tipo: 'number', padrao: 10, descricao: 'Tolerância no recebimento (%)', usado_em: ['recebimento_materiais_melhorado.html'] },
                    toleranciaPreco: { tipo: 'number', padrao: 5, descricao: 'Tolerância de preço (%)', usado_em: ['recebimento_materiais_melhorado.html'] },
                    bloquearPrecoDivergente: { tipo: 'boolean', padrao: false, descricao: 'Bloquear recebimento com preço divergente', usado_em: ['recebimento_materiais_melhorado.html'] },
                    permitirRecebimentoParcial: { tipo: 'boolean', padrao: true, descricao: 'Permite recebimento em múltiplas entregas', usado_em: ['recebimento_materiais_melhorado.html'] },
                    diasAlerteAtraso: { tipo: 'number', padrao: 3, descricao: 'Alertar pedidos em atraso (dias)', usado_em: ['recebimento_materiais_melhorado.html'] },
                    exigirAutorizacaoExcesso: { tipo: 'boolean', padrao: false, descricao: 'Exige autorização para receber excesso', usado_em: ['recebimento_materiais_melhorado.html'] },
                    controlarHistoricoEntregas: { tipo: 'boolean', padrao: true, descricao: 'Controlar histórico de entregas parciais', usado_em: ['recebimento_materiais_melhorado.html'] }
                }
            },

            'parametros_frete': {
                titulo: '🚚 Parâmetros de Frete',
                icone: 'fas fa-truck',
                parametros: {
                    controleFreteObrigatorio: { tipo: 'boolean', padrao: false, descricao: 'Controle de frete obrigatório', usado_em: ['recebimento_materiais_melhorado.html'] },
                    tipoFretePadrao: { tipo: 'select', padrao: 'CIF', opcoes: ['CIF', 'FOB'], descricao: 'Tipo de frete padrão', usado_em: ['recebimento_materiais_melhorado.html'] },
                    ratearFreteNoCusto: { tipo: 'boolean', padrao: true, descricao: 'Incluir frete rateado no custo', usado_em: ['recebimento_materiais_melhorado.html'] },
                    criterioRateioFrete: { tipo: 'select', padrao: 'valor', opcoes: ['valor', 'peso', 'quantidade'], descricao: 'Critério de rateio do frete', usado_em: ['recebimento_materiais_melhorado.html'] },
                    exigirDadosTransportadora: { tipo: 'boolean', padrao: false, descricao: 'Exigir dados da transportadora', usado_em: ['recebimento_materiais_melhorado.html'] },
                    validarChaveNFe: { tipo: 'boolean', padrao: false, descricao: 'Validar formato da chave NFe', usado_em: ['recebimento_materiais_melhorado.html'] }
                }
            },

            'parametros_compras': {
                titulo: '🛒 Parâmetros de Compras',
                icone: 'fas fa-shopping-cart',
                parametros: {
                    exigirAprovacaoTolerancia: { tipo: 'boolean', padrao: false, descricao: 'Exigir aprovação quando excede tolerância', usado_em: ['gestao_compras_integrada.html'] },
                    minimoCotacoes: { tipo: 'number', padrao: 3, descricao: 'Número mínimo de cotações', usado_em: ['cotacoes_melhorada.html'] },
                    diasVencimentoCotacao: { tipo: 'number', padrao: 7, descricao: 'Dias para vencimento de cotações', usado_em: ['cotacoes_melhorada.html'] },
                    valorAprovacaoAutomatica: { tipo: 'number', padrao: 1000, descricao: 'Valor limite para aprovação automática', usado_em: ['gestao_compras_integrada.html'] },
                    diasBloqueioFornecedor: { tipo: 'number', padrao: 30, descricao: 'Dias de atraso para bloquear fornecedor', usado_em: ['gestao_compras_integrada.html'] },
                    homologacaoFornecedor: { tipo: 'boolean', padrao: false, descricao: 'Exigir homologação de fornecedores', usado_em: ['cotacoes_melhorada.html'] }
                }
            },

            'parametros_qualidade': {
                titulo: '🔍 Parâmetros de Qualidade',
                icone: 'fas fa-search',
                parametros: {
                    diasReanalise: { tipo: 'number', padrao: 90, descricao: 'Dias para reanálise de lote', usado_em: ['Sistema de qualidade'] },
                    rastreabilidadeLote: { tipo: 'boolean', padrao: false, descricao: 'Controle de lotes para rastreabilidade', usado_em: ['recebimento_materiais_melhorado.html'] }
                }
            },

            'parametros_mrp': {
                titulo: '📈 Parâmetros de MRP',
                icone: 'fas fa-chart-line',
                parametros: {
                    horizontePlanejamento: { tipo: 'number', padrao: 60, descricao: 'Horizonte de planejamento (dias)', usado_em: ['ordens_producao.html'] },
                    politicaLote: { tipo: 'select', padrao: 'fixo', opcoes: ['fixo', 'economico', 'minimo'], descricao: 'Política de cálculo de lotes', usado_em: ['ordens_producao.html'] },
                    considerarPrevisao: { tipo: 'boolean', padrao: false, descricao: 'Incluir previsão de vendas no MRP', usado_em: ['ordens_producao.html'] },
                    diasSeguranca: { tipo: 'number', padrao: 5, descricao: 'Dias de segurança nos prazos', usado_em: ['ordens_producao.html'] }
                }
            },

            'parametros_custos': {
                titulo: '💰 Parâmetros de Custos',
                icone: 'fas fa-dollar-sign',
                parametros: {
                    metodoCusteio: { tipo: 'select', padrao: 'padrao', opcoes: ['padrao', 'medio', 'fifo'], descricao: 'Método de cálculo de custos', usado_em: ['Sistema de custos'] },
                    centroCustoObrigatorio: { tipo: 'boolean', padrao: false, descricao: 'Centro de custos obrigatório', usado_em: ['solicitacao_compras_melhorada.html'] }
                }
            },

            'parametros_vendas': {
                titulo: '🏷️ Parâmetros de Vendas',
                icone: 'fas fa-tags',
                parametros: {
                    precosObrigatoriosTabela: { tipo: 'boolean', padrao: false, descricao: 'Obrigar uso de preços da tabela', usado_em: ['Sistema de vendas'] },
                    permitirLiberacaoPrecos: { tipo: 'boolean', padrao: false, descricao: 'Permitir liberação de preços especiais', usado_em: ['Sistema de vendas'] },
                    nivelLiberacaoPrecos: { tipo: 'select', padrao: 3, opcoes: [2, 3, 7], descricao: 'Nível mínimo para liberar preços', usado_em: ['Sistema de vendas'] },
                    toleranciaDesconto: { tipo: 'number', padrao: 10, descricao: 'Tolerância de desconto (%)', usado_em: ['Sistema de vendas'] }
                }
            },

            'parametros_codificacao': {
                titulo: '🔢 Parâmetros de Codificação',
                icone: 'fas fa-barcode',
                parametros: {
                    tipoCodificacao: { tipo: 'select', padrao: 'manual', opcoes: ['manual', 'inteligente', 'semi-inteligente'], descricao: 'Tipo de codificação de produtos', usado_em: ['cadastro_produto.html'] },
                    digitosSequenciais: { tipo: 'number', padrao: 3, descricao: 'Dígitos sequenciais no código', usado_em: ['cadastro_produto.html'] },
                    usarTipo: { tipo: 'boolean', padrao: true, descricao: 'Incluir tipo no código', usado_em: ['cadastro_produto.html'] },
                    usarGrupo: { tipo: 'boolean', padrao: true, descricao: 'Incluir grupo no código', usado_em: ['cadastro_produto.html'] },
                    usarFamilia: { tipo: 'boolean', padrao: true, descricao: 'Incluir família no código', usado_em: ['cadastro_produto.html'] }
                }
            }
        };

        // Manter compatibilidade com código existente
        const PARAMETROS_DEFINIDOS = {
            'parametros/sistema': {}
        };

        // Consolidar todos os parâmetros em uma única estrutura
        for (const categoria of Object.values(CATEGORIAS_PARAMETROS)) {
            Object.assign(PARAMETROS_DEFINIDOS['parametros/sistema'], categoria.parametros);
        }

        // Função para detectar e inicializar Firebase
        async function initializeFirebase() {
            try {
                // Configurações conhecidas
                const configsConhecidas = [
                    // Banco original
                    {
                        apiKey: "AIzaSyDud1ir9Ta-0_jSB8dAaBCQau99RJY66RQ",
                        authDomain: "banco-mrp.firebaseapp.com",
                        projectId: "banco-mrp",
                        storageBucket: "banco-mrp.firebasestorage.app",
                        messagingSenderId: "740147152218",
                        appId: "1:740147152218:web:2d301340bf314e68d75f63",
                        measurementId: "G-YNNQ1VX1EH"
                    },
                    // Banco de teste
                    {
                        apiKey: "AIzaSyCG-VW7vhGZf2EvXzVkssajO10x0krfHCM",
                        authDomain: "naliteck-mrp.firebaseapp.com",
                        projectId: "naliteck-mrp",
                        storageBucket: "naliteck-mrp.firebasestorage.app",
                        messagingSenderId: "755022520906",
                        appId: "1:755022520906:web:efc8b69186289325c6fcb3",
                        measurementId: "G-C7W8FG17KG"
                    }
                ];

                // Testar cada configuração
                for (const config of configsConhecidas) {
                    try {
                        if (firebase.apps.length > 0) {
                            await firebase.app().delete();
                        }

                        firebase.initializeApp(config);
                        const testDb = firebase.firestore();

                        // Testar conexão
                        await testDb.collection('produtos').limit(1).get();

                        firebaseConfig = config;
                        db = testDb;

                        // Atualizar status
                        const statusEl = document.getElementById('connectionStatus');
                        statusEl.className = 'status-indicator connected';
                        statusEl.innerHTML = `<i class="fas fa-database"></i> ${config.projectId}`;

                        console.log('✅ Conectado ao:', config.projectId);
                        return true;

                    } catch (testError) {
                        console.warn(`❌ Falha ao conectar em ${config.projectId}:`, testError.message);
                        continue;
                    }
                }

                throw new Error('Nenhuma configuração Firebase funcionou');

            } catch (error) {
                console.error('❌ Erro ao inicializar Firebase:', error);

                const statusEl = document.getElementById('connectionStatus');
                statusEl.className = 'status-indicator disconnected';
                statusEl.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Erro`;

                throw error;
            }
        }

        // Função para carregar todos os parâmetros
        async function carregarParametros() {
            const content = document.getElementById('content');
            content.innerHTML = '<div class="loading"><div class="spinner"></div>Carregando parâmetros...</div>';

            try {
                if (!db) {
                    await initializeFirebase();
                }

                parametros = {};
                parametrosOriginais = {};

                // Carregar apenas da fonte única: parametros/sistema
                const colecaoPath = 'parametros/sistema';
                const [colecao, documento] = colecaoPath.split('/');

                try {
                    const doc = await db.collection(colecao).doc(documento).get();
                    const dados = doc.exists ? doc.data() : {};

                    parametros[colecaoPath] = dados;
                    parametrosOriginais[colecaoPath] = JSON.parse(JSON.stringify(dados));

                    console.log(`✅ Carregado da fonte única ${colecaoPath}:`, dados);

                } catch (error) {
                    console.warn(`⚠️ Erro ao carregar ${colecaoPath}:`, error.message);
                    parametros[colecaoPath] = {};
                    parametrosOriginais[colecaoPath] = {};
                }

                renderizarTabela();

            } catch (error) {
                console.error('❌ Erro ao carregar parâmetros:', error);
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ Erro ao Carregar:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Função para renderizar a tabela de parâmetros ORGANIZADA POR CATEGORIAS
        function renderizarTabela() {
            let html = '';

            // Verificar inconsistências primeiro
            const inconsistencias = verificarInconsistenciasInternas();

            if (inconsistencias.length > 0) {
                html += `
                    <div class="inconsistency-warning">
                        <h4><i class="fas fa-exclamation-triangle"></i> ${inconsistencias.length} Inconsistência(s) Detectada(s)</h4>
                        ${inconsistencias.map(inc => `<div>• ${inc}</div>`).join('')}
                    </div>
                `;
            }

            const valores = parametros['parametros/sistema'] || {};

            // Renderizar por categorias organizadas
            for (const [categoriaId, categoria] of Object.entries(CATEGORIAS_PARAMETROS)) {
                html += `
                    <div class="collection-section">
                        <div class="collection-title">
                            <i class="${categoria.icone}"></i>
                            ${categoria.titulo}
                        </div>

                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>Parâmetro</th>
                                    <th>Valor Atual</th>
                                    <th>Padrão</th>
                                    <th>Descrição</th>
                                    <th>Usado Em</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                for (const [chave, definicao] of Object.entries(categoria.parametros)) {
                    // Corrigir valores undefined para boolean
                    let valorAtual = valores[chave];
                    if (definicao.tipo === 'boolean' && valorAtual === undefined) {
                        valorAtual = definicao.padrao;
                    }
                    const valorPadrao = definicao.padrao;
                    const isDiferente = valorAtual !== valorPadrao;

                    // Gerar controle baseado no tipo
                    let controleHtml = '';

                    if (definicao.tipo === 'boolean') {
                        controleHtml = `
                            <div class="toggle-switch ${valorAtual ? 'active' : ''}"
                                 onclick="toggleParameter('parametros/sistema', '${chave}')">
                            </div>
                            <span>${valorAtual ? 'ATIVO' : 'INATIVO'}</span>
                        `;
                    } else if (definicao.tipo === 'select') {
                        const opcoes = definicao.opcoes || [];
                        controleHtml = `
                            <select class="param-input"
                                    onchange="updateParameter('parametros/sistema', '${chave}', this.value)">
                                ${opcoes.map(opcao =>
                                    `<option value="${opcao}" ${valorAtual === opcao ? 'selected' : ''}>${opcao}</option>`
                                ).join('')}
                            </select>
                        `;
                    } else if (definicao.tipo === 'number') {
                        controleHtml = `
                            <input type="number" class="param-input"
                                   value="${valorAtual !== undefined ? valorAtual : definicao.padrao}"
                                   onchange="updateParameter('parametros/sistema', '${chave}', this.value)">
                        `;
                    } else {
                        controleHtml = `
                            <input type="text" class="param-input"
                                   value="${valorAtual || ''}"
                                   onchange="updateParameter('parametros/sistema', '${chave}', this.value)">
                        `;
                    }

                    // Destacar parâmetros críticos
                    const isCritico = chave === 'usarSaldoEstoque' || chave === 'reservarEstoque';
                    const rowClass = isCritico ? 'style="background: #e8f5e8; border-left: 4px solid #28a745;"' :
                                   isDiferente ? 'style="background: #fff3cd;"' : '';

                    html += `
                        <tr ${rowClass}>
                            <td>
                                <div class="param-key">${chave}</div>
                                <div class="param-collection">parametros/sistema</div>
                                ${isCritico ? '<div class="param-description">🔥 CRÍTICO PARA RESERVAS</div>' : ''}
                            </td>
                            <td>
                                <div class="param-value">
                                    ${controleHtml}
                                </div>
                            </td>
                            <td>
                                <strong>${valorPadrao}</strong>
                            </td>
                            <td>
                                <div>${definicao.descricao}</div>
                                ${isDiferente ? '<div class="param-description">⚠️ Valor diferente do padrão</div>' : ''}
                            </td>
                            <td>
                                ${Array.isArray(definicao.usado_em) ?
                                    definicao.usado_em.map(arquivo =>
                                        `<span class="param-collection">${arquivo}</span>`
                                    ).join('<br>') :
                                    `<span class="param-collection">${definicao.usado_em}</span>`
                                }
                            </td>
                        </tr>
                    `;
                }

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            document.getElementById('content').innerHTML = html;
        }

        // Função para alternar parâmetro boolean - CORRIGIDA
        function toggleParameter(colecaoPath, chave) {
            if (!parametros[colecaoPath]) {
                parametros[colecaoPath] = {};
            }

            // Corrigir lógica: se undefined, usar valor padrão da definição
            const definicao = PARAMETROS_DEFINIDOS[colecaoPath][chave];
            const valorAtual = parametros[colecaoPath][chave];

            if (valorAtual === undefined) {
                // Se undefined, definir como oposto do padrão
                parametros[colecaoPath][chave] = !definicao.padrao;
            } else {
                // Se já tem valor, inverter
                parametros[colecaoPath][chave] = !valorAtual;
            }

            console.log(`🔄 Toggle ${chave}: ${parametros[colecaoPath][chave]}`);
            renderizarTabela();
        }

        // Função para atualizar parâmetro
        function updateParameter(colecaoPath, chave, valor) {
            if (!parametros[colecaoPath]) {
                parametros[colecaoPath] = {};
            }

            // Converter tipo se necessário
            const definicao = PARAMETROS_DEFINIDOS[colecaoPath][chave];
            if (definicao.tipo === 'boolean') {
                valor = valor === 'true' || valor === true;
            } else if (definicao.tipo === 'number') {
                valor = parseFloat(valor) || 0;
            }

            parametros[colecaoPath][chave] = valor;
        }

        // Função para verificar inconsistências internas - SIMPLIFICADA
        function verificarInconsistenciasInternas() {
            // Sem duplicidades, sem inconsistências! 🎉
            return [];
        }

        // Função para verificar inconsistências (chamada pelo botão)
        function verificarInconsistencias() {
            const inconsistencias = verificarInconsistenciasInternas();

            if (inconsistencias.length === 0) {
                alert('✅ Nenhuma inconsistência encontrada!\nTodos os parâmetros estão sincronizados.');
            } else {
                alert(`⚠️ ${inconsistencias.length} inconsistência(s) encontrada(s):\n\n${inconsistencias.join('\n')}\n\nUse "Salvar Todos" para sincronizar.`);
            }
        }

        // Função para salvar todos os parâmetros - SIMPLIFICADA
        async function salvarTodos() {
            if (!confirm('💾 Salvar parâmetros na fonte única?\n\nIsso irá atualizar parametros/sistema no Firebase.')) {
                return;
            }

            try {
                const content = document.getElementById('content');
                const originalContent = content.innerHTML;
                content.innerHTML = '<div class="loading"><div class="spinner"></div>Salvando parâmetros...</div>';

                // Salvar apenas na fonte única
                const colecaoPath = 'parametros/sistema';
                const [colecao, documento] = colecaoPath.split('/');

                await db.collection(colecao).doc(documento).set(parametros[colecaoPath], { merge: true });
                parametrosOriginais[colecaoPath] = JSON.parse(JSON.stringify(parametros[colecaoPath]));

                console.log(`✅ Salvo na fonte única: ${colecaoPath}`);

                content.innerHTML = originalContent;
                alert(`✅ Sucesso!\n\nParâmetros salvos na fonte única: ${colecaoPath}`);

                // Recarregar para mostrar dados atualizados
                await carregarParametros();

            } catch (error) {
                console.error('❌ Erro ao salvar:', error);
                alert('❌ Erro ao salvar parâmetros: ' + error.message);
            }
        }

        // Função para resetar para valores padrão
        async function resetarPadroes() {
            if (!confirm('🔄 Resetar todos os parâmetros para valores padrão?\n\n⚠️ ATENÇÃO: Isso irá sobrescrever todas as configurações atuais!')) {
                return;
            }

            try {
                // Resetar para valores padrão
                for (const colecaoPath of Object.keys(PARAMETROS_DEFINIDOS)) {
                    parametros[colecaoPath] = {};

                    for (const [chave, definicao] of Object.entries(PARAMETROS_DEFINIDOS[colecaoPath])) {
                        parametros[colecaoPath][chave] = definicao.padrao;
                    }
                }

                renderizarTabela();
                alert('🔄 Parâmetros resetados para valores padrão!\n\nClique em "Salvar Todos" para aplicar as mudanças.');

            } catch (error) {
                console.error('❌ Erro ao resetar:', error);
                alert('❌ Erro ao resetar parâmetros: ' + error.message);
            }
        }

        // Tornar funções globais
        window.carregarParametros = carregarParametros;
        window.toggleParameter = toggleParameter;
        window.updateParameter = updateParameter;
        window.verificarInconsistencias = verificarInconsistencias;
        window.salvarTodos = salvarTodos;
        window.resetarPadroes = resetarPadroes;

        // Inicialização
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await initializeFirebase();
                await carregarParametros();
            } catch (error) {
                console.error('❌ Erro na inicialização:', error);
            }
        });
    </script>
</body>
</html>
