<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Monitor Universal da Qualidade</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .monitor-container {
            max-width: 1600px;
            margin: 20px auto;
            padding: 20px;
        }
        .monitor-header {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .status-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 6px solid #8e44ad;
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
        }
        .status-card.alert {
            border-left-color: #e74c3c;
            animation: pulse-red 2s infinite;
        }
        .status-card.warning {
            border-left-color: #f39c12;
        }
        .status-card.success {
            border-left-color: #27ae60;
        }
        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
            50% { box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3); }
        }
        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .status-icon {
            font-size: 2.5em;
            margin-right: 15px;
        }
        .status-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .status-value {
            font-size: 2.5em;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .status-description {
            color: #7f8c8d;
            text-align: center;
            font-size: 14px;
        }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            color: white;
        }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .item-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 6px solid #95a5a6;
        }
        .item-card.pendente {
            border-left-color: #f39c12;
        }
        .item-card.aprovado {
            border-left-color: #27ae60;
        }
        .item-card.rejeitado {
            border-left-color: #e74c3c;
        }
        .item-card.parado {
            border-left-color: #e74c3c;
            animation: pulse-red 2s infinite;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .item-code {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .badge-pendente {
            background: #fff3cd;
            color: #856404;
        }
        .badge-aprovado {
            background: #d4edda;
            color: #155724;
        }
        .badge-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }
        .badge-parado {
            background: #f8d7da;
            color: #721c24;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .item-details {
            margin: 15px 0;
            font-size: 14px;
            line-height: 1.5;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
        }
        .detail-value {
            color: #7f8c8d;
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #27ae60; font-weight: bold; }
        .error { color: #e74c3c; font-weight: bold; }
        .warning { color: #f39c12; font-weight: bold; }
        .info { color: #3498db; font-weight: bold; }
        .auto-refresh {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .refresh-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        .alerts-section {
            background: #fff5f5;
            border: 2px solid #e74c3c;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
        }
        .alert-item {
            background: white;
            border-left: 4px solid #e74c3c;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .alert-title {
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 5px;
        }
        .alert-description {
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="monitor-header">
            <h1>🛡️ Monitor Universal da Qualidade</h1>
            <p>Monitoramento em tempo real de todos os processos de qualidade</p>
            <p><strong>Status do Sistema:</strong> <span id="statusSistema">Carregando...</span></p>
        </div>

        <!-- Indicadores de Status -->
        <div class="status-grid" id="statusGrid">
            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">⏳</div>
                    <div class="status-title">Itens Pendentes</div>
                </div>
                <div class="status-value" id="itensPendentes">-</div>
                <div class="status-description">Aguardando aprovação</div>
            </div>

            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">⚠️</div>
                    <div class="status-title">Itens Parados</div>
                </div>
                <div class="status-value" id="itensParados">-</div>
                <div class="status-description">Mais de 7 dias na qualidade</div>
            </div>

            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">✅</div>
                    <div class="status-title">Aprovados Hoje</div>
                </div>
                <div class="status-value" id="aprovadosHoje">-</div>
                <div class="status-description">Itens aprovados nas últimas 24h</div>
            </div>

            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">📊</div>
                    <div class="status-title">Taxa de Aprovação</div>
                </div>
                <div class="status-value" id="taxaAprovacao">-</div>
                <div class="status-description">Últimos 30 dias</div>
            </div>

            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">⏱️</div>
                    <div class="status-title">Tempo Médio</div>
                </div>
                <div class="status-value" id="tempoMedio">-</div>
                <div class="status-description">Tempo médio na qualidade</div>
            </div>

            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">🔄</div>
                    <div class="status-title">Última Atualização</div>
                </div>
                <div class="status-value" id="ultimaAtualizacao" style="font-size: 1.2em;">-</div>
                <div class="status-description">Sistema em tempo real</div>
            </div>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <h3>🎛️ Controles do Monitor</h3>
            <div style="text-align: center; margin: 20px 0;">
                <button onclick="atualizarMonitor()" class="btn btn-primary">🔄 Atualizar Agora</button>
                <button onclick="configurarAlertas()" class="btn btn-warning">⚠️ Configurar Alertas</button>
                <button onclick="exportarRelatorio()" class="btn btn-success">📊 Exportar Relatório</button>
                <button onclick="verDetalhes()" class="btn btn-primary">📋 Ver Detalhes</button>
            </div>
        </div>

        <!-- Atualização Automática -->
        <div class="auto-refresh">
            <h4><span class="refresh-indicator"></span>Monitoramento Automático</h4>
            <p>Atualização automática a cada <strong>30 segundos</strong></p>
            <p>Próxima atualização: <span id="proximaAtualizacao">30s</span></p>
            <label>
                <input type="checkbox" id="autoRefresh" checked> Atualização automática ativada
            </label>
        </div>

        <!-- Alertas Críticos -->
        <div class="alerts-section" id="alertsSection" style="display: none;">
            <h3>🚨 Alertas Críticos</h3>
            <div id="alertsContent"></div>
        </div>

        <!-- Log de Atividades -->
        <div class="log-area" id="logArea"></div>

        <!-- Itens em Destaque -->
        <div id="itemsSection" style="display: none;">
            <h3>📋 Itens que Precisam de Atenção</h3>
            <div class="items-grid" id="itemsGrid"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let monitorData = {};
        let intervalId = null;
        let countdownId = null;
        let nextUpdate = 30;

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateCountdown() {
            const element = document.getElementById('proximaAtualizacao');
            if (element) {
                element.textContent = `${nextUpdate}s`;
                nextUpdate--;
                
                if (nextUpdate < 0) {
                    nextUpdate = 30;
                    if (document.getElementById('autoRefresh').checked) {
                        atualizarMonitor();
                    }
                }
            }
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Monitor universal da qualidade carregado', 'info');
            
            // Iniciar monitoramento
            atualizarMonitor();
            
            // Configurar atualização automática
            intervalId = setInterval(() => {
                if (document.getElementById('autoRefresh').checked) {
                    atualizarMonitor();
                }
            }, 30000);
            
            // Configurar countdown
            countdownId = setInterval(updateCountdown, 1000);
        };

        window.onbeforeunload = function() {
            if (intervalId) clearInterval(intervalId);
            if (countdownId) clearInterval(countdownId);
        };

        window.atualizarMonitor = async function() {
            log('🔄 Atualizando monitor da qualidade...', 'info');
            document.getElementById('statusSistema').textContent = 'Atualizando...';
            
            try {
                // Carregar dados
                const [estoqueQualidadeSnap, produtosSnap, movimentacoesSnap] = await Promise.all([
                    getDocs(collection(db, "estoqueQualidade")),
                    getDocs(collection(db, "produtos")),
                    getDocs(query(collection(db, "movimentacoesEstoque"), 
                        orderBy("dataHora", "desc"), limit(200)))
                ]);

                const estoqueQualidade = estoqueQualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Enriquecer dados
                estoqueQualidade.forEach(item => {
                    const produto = produtos.find(p => p.id === item.produtoId);
                    if (produto) {
                        item.codigo = produto.codigo;
                        item.descricao = produto.descricao;
                        item.unidade = produto.unidade;
                    }
                });

                // Calcular métricas
                const agora = new Date();
                const ontem = new Date(agora.getTime() - 24 * 60 * 60 * 1000);
                const seteDiasAtras = new Date(agora.getTime() - 7 * 24 * 60 * 60 * 1000);
                const trintaDiasAtras = new Date(agora.getTime() - 30 * 24 * 60 * 60 * 1000);

                // Itens pendentes
                const itensPendentes = estoqueQualidade.filter(item => 
                    !item.status || item.status === 'PENDENTE'
                ).length;

                // Itens parados (mais de 7 dias)
                const itensParados = estoqueQualidade.filter(item => {
                    if (!item.dataEntrada) return false;
                    const dataEntrada = item.dataEntrada.seconds ? 
                        new Date(item.dataEntrada.seconds * 1000) : new Date(item.dataEntrada);
                    return dataEntrada < seteDiasAtras && (!item.status || item.status === 'PENDENTE');
                }).length;

                // Aprovados hoje
                const aprovadosHoje = movimentacoes.filter(mov => {
                    if (mov.tipoDocumento !== 'APROVACAO_QUALIDADE') return false;
                    const dataHora = mov.dataHora.seconds ? 
                        new Date(mov.dataHora.seconds * 1000) : new Date(mov.dataHora);
                    return dataHora > ontem;
                }).length;

                // Taxa de aprovação (últimos 30 dias)
                const movimentacoesTrintaDias = movimentacoes.filter(mov => {
                    const dataHora = mov.dataHora.seconds ? 
                        new Date(mov.dataHora.seconds * 1000) : new Date(mov.dataHora);
                    return dataHora > trintaDiasAtras;
                });
                
                const aprovacoesTotais = movimentacoesTrintaDias.filter(mov => 
                    mov.tipoDocumento === 'APROVACAO_QUALIDADE'
                ).length;
                
                const rejeicoesTotais = movimentacoesTrintaDias.filter(mov => 
                    mov.tipoDocumento === 'REJEICAO_QUALIDADE'
                ).length;
                
                const taxaAprovacao = aprovacoesTotais + rejeicoesTotais > 0 ? 
                    Math.round((aprovacoesTotais / (aprovacoesTotais + rejeicoesTotais)) * 100) : 0;

                // Tempo médio na qualidade
                const itensComTempo = estoqueQualidade.filter(item => 
                    item.dataEntrada && (item.status === 'APROVADO' || item.status === 'REJEITADO')
                );
                
                let tempoMedio = 0;
                if (itensComTempo.length > 0) {
                    const tempoTotal = itensComTempo.reduce((total, item) => {
                        const entrada = item.dataEntrada.seconds ? 
                            new Date(item.dataEntrada.seconds * 1000) : new Date(item.dataEntrada);
                        const saida = item.dataAprovacao ? 
                            (item.dataAprovacao.seconds ? new Date(item.dataAprovacao.seconds * 1000) : new Date(item.dataAprovacao)) :
                            agora;
                        return total + (saida - entrada);
                    }, 0);
                    tempoMedio = Math.round(tempoTotal / (itensComTempo.length * 24 * 60 * 60 * 1000));
                }

                // Atualizar interface
                document.getElementById('itensPendentes').textContent = itensPendentes;
                document.getElementById('itensParados').textContent = itensParados;
                document.getElementById('aprovadosHoje').textContent = aprovadosHoje;
                document.getElementById('taxaAprovacao').textContent = `${taxaAprovacao}%`;
                document.getElementById('tempoMedio').textContent = `${tempoMedio}d`;
                document.getElementById('ultimaAtualizacao').textContent = agora.toLocaleTimeString();

                // Aplicar classes de alerta
                const statusCards = document.querySelectorAll('.status-card');
                statusCards[0].className = itensPendentes > 10 ? 'status-card warning' : 'status-card success';
                statusCards[1].className = itensParados > 0 ? 'status-card alert' : 'status-card success';
                statusCards[2].className = aprovadosHoje > 0 ? 'status-card success' : 'status-card';
                statusCards[3].className = taxaAprovacao < 80 ? 'status-card warning' : 'status-card success';

                // Mostrar alertas críticos
                mostrarAlertas(itensParados, itensPendentes, taxaAprovacao);

                // Mostrar itens que precisam de atenção
                mostrarItensAtencao(estoqueQualidade, seteDiasAtras);

                document.getElementById('statusSistema').textContent = 'Sistema operacional';
                log(`✅ Monitor atualizado: ${itensPendentes} pendentes, ${itensParados} parados`, 'success');
                
                // Reset countdown
                nextUpdate = 30;

            } catch (error) {
                log(`❌ Erro ao atualizar monitor: ${error.message}`, 'error');
                document.getElementById('statusSistema').textContent = 'Erro na atualização';
            }
        };
