<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Relatório de Estruturas Pendentes</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --light-bg: #f8f9fa;
      --white: #ffffff;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
      --border-radius: 12px;
      --transition: all 0.3s ease;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: var(--text-color);
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: var(--white);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      overflow: hidden;
      animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .header {
      background: linear-gradient(135deg, var(--header-bg), var(--primary-color));
      color: var(--white);
      padding: 30px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.05) 10px,
        rgba(255,255,255,0.05) 20px
      );
      animation: float 20s linear infinite;
    }

    @keyframes float {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
      position: relative;
      z-index: 1;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .subtitle {
      font-size: 1.1rem;
      opacity: 0.9;
      position: relative;
      z-index: 1;
    }

    .content {
      padding: 30px;
    }

    .controls-section {
      background: linear-gradient(135deg, var(--light-bg), #e9ecef);
      border-radius: var(--border-radius);
      padding: 25px;
      margin-bottom: 30px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid rgba(255,255,255,0.8);
      text-align: center;
    }

    button {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: var(--white);
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      min-width: 160px;
    }

    button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255,255,255,0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.3s, height 0.3s;
    }

    button:hover::before {
      width: 300px;
      height: 300px;
    }

    button:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-hover);
    }

    .table-container {
      background: var(--white);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th {
      background: linear-gradient(135deg, var(--header-bg), #34495e);
      color: var(--white);
      padding: 20px 16px;
      text-align: left;
      font-weight: 600;
      font-size: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
    }

    th::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    }

    td {
      padding: 16px;
      border-bottom: 1px solid #ecf0f1;
      transition: var(--transition);
      vertical-align: middle;
    }

    tr:hover {
      background: linear-gradient(135deg, #f8f9fa, #ecf0f1);
      transform: scale(1.01);
    }

    tr:nth-child(even) {
      background: rgba(8, 84, 160, 0.02);
    }

    .missing {
      background: linear-gradient(135deg, #ffebee, #ffcdd2) !important;
      color: var(--danger-color);
      font-weight: 600;
      padding: 8px 12px;
      border-radius: 6px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(187, 0, 0, 0.2);
      position: relative;
      overflow: hidden;
    }

    .missing::before {
      content: '⚠️';
      margin-right: 5px;
    }

    .ok {
      background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
      color: var(--success-color);
      font-weight: 600;
      padding: 8px 12px;
      border-radius: 6px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(16, 126, 62, 0.2);
      position: relative;
      overflow: hidden;
    }

    .ok::before {
      content: '✅';
      margin-right: 5px;
    }

    .status-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: var(--transition);
    }

    .status-badge:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        border-radius: 8px;
      }

      .header {
        padding: 20px;
      }

      h1 {
        font-size: 2rem;
      }

      .content {
        padding: 20px;
      }

      table {
        font-size: 0.9rem;
      }

      th, td {
        padding: 12px 8px;
      }

      button {
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px;
      font-size: 1.2rem;
      color: var(--text-secondary);
    }

    .loading::after {
      content: '';
      width: 20px;
      height: 20px;
      border: 2px solid var(--border-color);
      border-top: 2px solid var(--primary-color);
      border-radius: 50%;
      margin-left: 10px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .summary-section {
      background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
      border: 2px solid var(--success-color);
      border-radius: var(--border-radius);
      padding: 20px;
      margin-bottom: 30px;
      text-align: center;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .summary-item {
      background: var(--white);
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      font-weight: 600;
      font-size: 1.1rem;
      transition: var(--transition);
      border: 2px solid transparent;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .summary-item:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-hover);
    }

    .summary-number {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
      background: linear-gradient(135deg, var(--primary-color), var(--success-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📋 Relatório de Estruturas Pendentes</h1>
      <p class="subtitle">Análise de estruturas com operações ou materiais faltantes</p>
    </div>

    <div class="content">
      <!-- Resumo -->
      <div class="summary-section" id="summarySection" style="display: none;">
        <h3>📊 Resumo da Análise</h3>
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-number" id="totalEstruturas">0</div>
            <div>Total de Estruturas</div>
          </div>
          <div class="summary-item">
            <div class="summary-number" id="faltamOperacoes">0</div>
            <div>Faltam Operações</div>
          </div>
          <div class="summary-item">
            <div class="summary-number" id="faltamMateriais">0</div>
            <div>Faltam Materiais</div>
          </div>
          <div class="summary-item">
            <div class="summary-number" id="estruturasCompletas">0</div>
            <div>Estruturas Completas</div>
          </div>
        </div>
      </div>

      <!-- Controles -->
      <div class="controls-section">
        <button onclick="exportarCSV()">
          📊 Exportar para CSV
        </button>
        <button onclick="atualizarRelatorio()" style="background: linear-gradient(135deg, var(--success-color), var(--success-hover));">
          🔄 Atualizar Dados
        </button>
      </div>

      <!-- Tabela -->
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>📦 Código</th>
              <th>📝 Descrição</th>
              <th>⚙️ Falta Operações</th>
              <th>🧱 Falta Materiais</th>
            </tr>
          </thead>
          <tbody id="tableBody">
            <tr>
              <td colspan="4" class="loading">Carregando estruturas...</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <script type="module">
    import { db } from '../firebase-config.js';
    import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
    let linhas = [];
    let estatisticas = {
      total: 0,
      faltamOperacoes: 0,
      faltamMateriais: 0,
      completas: 0
    };

    async function carregarEstruturas() {
      try {
        // Mostrar loading
        const tbody = document.getElementById('tableBody');
        tbody.innerHTML = '<tr><td colspan="4" class="loading">Carregando estruturas...</td></tr>';

        const [produtosSnap, estruturasSnap] = await Promise.all([
          getDocs(collection(db, 'produtos')),
          getDocs(collection(db, 'estruturas'))
        ]);

        const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        tbody.innerHTML = '';
        linhas = [];

        // Resetar estatísticas
        estatisticas = {
          total: estruturas.length,
          faltamOperacoes: 0,
          faltamMateriais: 0,
          completas: 0
        };

        if (estruturas.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="4" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                <div style="font-size: 3rem; margin-bottom: 15px;">📋</div>
                <div style="font-weight: 600; margin-bottom: 10px;">Nenhuma estrutura encontrada</div>
                <div>Não há estruturas cadastradas no sistema.</div>
              </td>
            </tr>
          `;
          return;
        }

        for (const estrutura of estruturas) {
          const produto = produtos.find(p => p.id === estrutura.produtoPaiId);
          const faltaOperacoes = !estrutura.operacoes || estrutura.operacoes.length === 0;
          const faltaMateriais = !estrutura.componentes || estrutura.componentes.length === 0;

          // Atualizar estatísticas
          if (faltaOperacoes) estatisticas.faltamOperacoes++;
          if (faltaMateriais) estatisticas.faltamMateriais++;
          if (!faltaOperacoes && !faltaMateriais) estatisticas.completas++;

          const tr = document.createElement('tr');
          tr.style.animationDelay = `${linhas.length * 0.05}s`;
          tr.style.animation = 'slideUp 0.5s ease-out forwards';

          // Determinar ícone do tipo de produto
          const tipoIcon = {
            'PA': '🏭',
            'SP': '⚙️',
            'MP': '🧱'
          }[produto?.tipo] || '📦';

          tr.innerHTML = `
            <td style="font-weight: 600; color: var(--primary-color);">
              ${tipoIcon} ${produto ? produto.codigo : estrutura.produtoPaiId}
            </td>
            <td style="max-width: 300px; word-wrap: break-word;">
              ${produto ? produto.descricao : '-'}
            </td>
            <td class="${faltaOperacoes ? 'missing' : 'ok'}">
              ${faltaOperacoes ? 'SIM' : 'NÃO'}
            </td>
            <td class="${faltaMateriais ? 'missing' : 'ok'}">
              ${faltaMateriais ? 'SIM' : 'NÃO'}
            </td>
          `;

          tbody.appendChild(tr);

          linhas.push({
            codigo: produto ? produto.codigo : estrutura.produtoPaiId,
            descricao: produto ? produto.descricao : '-',
            faltaOperacoes: faltaOperacoes ? 'SIM' : 'NÃO',
            faltaMateriais: faltaMateriais ? 'SIM' : 'NÃO',
            tipo: produto?.tipo || 'N/A'
          });
        }

        // Atualizar resumo
        atualizarResumo();

      } catch (error) {
        console.error('Erro ao carregar estruturas:', error);
        const tbody = document.getElementById('tableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="4" style="text-align: center; padding: 40px; color: var(--danger-color);">
              <div style="font-size: 3rem; margin-bottom: 15px;">❌</div>
              <div style="font-weight: 600; margin-bottom: 10px;">Erro ao carregar dados</div>
              <div>Ocorreu um erro ao carregar as estruturas. Tente novamente.</div>
            </td>
          </tr>
        `;
      }
    }

    function atualizarResumo() {
      document.getElementById('totalEstruturas').textContent = estatisticas.total;
      document.getElementById('faltamOperacoes').textContent = estatisticas.faltamOperacoes;
      document.getElementById('faltamMateriais').textContent = estatisticas.faltamMateriais;
      document.getElementById('estruturasCompletas').textContent = estatisticas.completas;

      // Mostrar seção de resumo
      document.getElementById('summarySection').style.display = 'block';
    }

    window.atualizarRelatorio = async function() {
      await carregarEstruturas();

      // Feedback visual
      const btn = event.target;
      const originalText = btn.innerHTML;
      btn.innerHTML = '🔄 Atualizando...';
      btn.disabled = true;

      setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;

        // Toast de sucesso
        const toast = document.createElement('div');
        toast.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, var(--success-color), #229954);
          color: white;
          padding: 15px 20px;
          border-radius: 8px;
          box-shadow: var(--shadow-hover);
          z-index: 1000;
          font-weight: 600;
          animation: slideIn 0.3s ease-out;
        `;
        toast.innerHTML = '✅ Dados atualizados com sucesso!';
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.style.animation = 'slideOut 0.3s ease-out';
          setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
      }, 1000);
    };

    // Carregar dados iniciais
    carregarEstruturas();
    window.exportarCSV = function() {
      if (linhas.length === 0) {
        // Notificação de erro
        const toast = document.createElement('div');
        toast.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, var(--warning-color), #e67e22);
          color: white;
          padding: 15px 20px;
          border-radius: 8px;
          box-shadow: var(--shadow-hover);
          z-index: 1000;
          font-weight: 600;
          animation: slideIn 0.3s ease-out;
        `;
        toast.innerHTML = '⚠️ Nenhum dado para exportar!';
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.style.animation = 'slideOut 0.3s ease-out';
          setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
        return;
      }

      // Feedback visual durante exportação
      const btn = event.target;
      const originalText = btn.innerHTML;
      btn.innerHTML = '📊 Exportando...';
      btn.disabled = true;

      setTimeout(() => {
        // Criar CSV com mais informações
        let csv = 'Código,Descrição,Tipo,Falta Operações,Falta Materiais,Status\n';

        for (const l of linhas) {
          const status = (l.faltaOperacoes === 'SIM' || l.faltaMateriais === 'SIM') ? 'Pendente' : 'Completa';
          const linha = [
            l.codigo,
            `"${l.descricao.replace(/"/g, '""')}"`,
            l.tipo || 'N/A',
            l.faltaOperacoes,
            l.faltaMateriais,
            status
          ].join(',');
          csv += linha + '\n';
        }

        // Adicionar resumo no final
        csv += '\n--- RESUMO ---\n';
        csv += `Total de Estruturas,${estatisticas.total}\n`;
        csv += `Faltam Operações,${estatisticas.faltamOperacoes}\n`;
        csv += `Faltam Materiais,${estatisticas.faltamMateriais}\n`;
        csv += `Estruturas Completas,${estatisticas.completas}\n`;
        csv += `Data da Exportação,"${new Date().toLocaleString('pt-BR')}"\n`;

        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `relatorio_estruturas_pendentes_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        // Restaurar botão
        btn.innerHTML = originalText;
        btn.disabled = false;

        // Toast de sucesso
        const toast = document.createElement('div');
        toast.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, var(--success-color), #229954);
          color: white;
          padding: 15px 20px;
          border-radius: 8px;
          box-shadow: var(--shadow-hover);
          z-index: 1000;
          font-weight: 600;
          animation: slideIn 0.3s ease-out;
        `;
        toast.innerHTML = '✅ Relatório exportado com sucesso!';
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.style.animation = 'slideOut 0.3s ease-out';
          setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
      }, 1000);
    }

    // Adicionar animações CSS para toasts
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateX(100%);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideOut {
        from {
          opacity: 1;
          transform: translateX(0);
        }
        to {
          opacity: 0;
          transform: translateX(100%);
        }
      }
    `;
    document.head.appendChild(style);
  </script>
</body>
</html> 