// ===== COTAÇÕES - EDIÇÃO AVANÇADA =====

let currentEditingQuotation = null;
let editingItems = [];
let editingSuppliers = [];

// ===== ABRIR MODAL DE EDIÇÃO =====
window.editQuotation = function(quotationId) {
    const quotation = cotacoes.find(c => c.id === quotationId);
    if (!quotation) {
        showNotification('Cotação não encontrada', 'error');
        return;
    }

    currentEditingQuotation = quotation;
    loadQuotationForEdit(quotation);
    openModal('editQuotationModal');
};

function loadQuotationForEdit(quotation) {
    // Carregar dados básicos
    document.getElementById('editQuotationNumber').value = quotation.numero || '';
    document.getElementById('editQuotationDate').value = formatDateForInput(quotation.dataCriacao);
    document.getElementById('editQuotationDeadline').value = formatDateForInput(quotation.dataLimite);
    document.getElementById('editQuotationStatus').value = quotation.status || 'ABERTA';

    // Carregar itens
    editingItems = quotation.itens ? [...quotation.itens] : [];
    renderEditItems();

    // Carregar fornecedores
    editingSuppliers = quotation.fornecedores ? [...quotation.fornecedores] : [];
    renderEditSuppliers();

    // Carregar condições comerciais
    loadCommercialConditions(quotation);

    // Carregar observações
    loadObservations(quotation);

    // Calcular totais
    calculateTotals();
}

// ===== GERENCIAMENTO DE ABAS =====
window.showEditTab = function(tabName, event) {
    // Ocultar todas as abas
    document.querySelectorAll('.edit-tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remover classe active de todos os botões
    document.querySelectorAll('.edit-tab').forEach(btn => {
        btn.classList.remove('active');
    });

    // Mostrar aba selecionada
    const selectedTab = document.getElementById(`editTab${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Ativar botão correspondente
    if (event && event.target) {
        event.target.classList.add('active');
    } else {
        // Fallback: encontrar o botão pela aba
        const tabButton = document.querySelector(`[onclick*="showEditTab('${tabName}')"]`);
        if (tabButton) {
            tabButton.classList.add('active');
        }
    }
};

// ===== GERENCIAMENTO DE ITENS =====
function renderEditItems() {
    const tbody = document.getElementById('editItemsTableBody');
    tbody.innerHTML = '';

    editingItems.forEach((item, index) => {
        // Buscar produto para verificar conversão
        const produto = produtos.find(p => p.codigo === item.codigo);
        const hasConversion = produto && produto.unidadeSecundaria && produto.fatorConversao;

        // Calcular quantidade convertida se necessário
        let quantidadeConvertida = '';
        let unidadeConvertida = '';
        if (hasConversion && item.quantidade) {
            if (produto.unidade === 'PC' && produto.unidadeSecundaria === 'KG') {
                quantidadeConvertida = (item.quantidade * produto.fatorConversao).toFixed(3);
                unidadeConvertida = 'KG';
            }
        }

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="text" class="item-input" value="${item.codigo || ''}"
                       onchange="updateItemField(${index}, 'codigo', this.value)"
                       onblur="searchProductByCode(${index}, this.value)">
            </td>
            <td>
                <input type="text" class="item-input" value="${item.descricao || ''}"
                       onchange="updateItemField(${index}, 'descricao', this.value)">
            </td>
            <td>
                <div class="unit-column">
                    <div class="unit-value">${item.unidade || 'UN'}</div>
                </div>
            </td>
            <td>
                <div class="unit-column">
                    <div class="unit-value">${hasConversion ? produto.unidadeSecundaria : (item.unidade || 'UN')}</div>
                </div>
            </td>
            <td>
                <div class="quantity-column">
                    <input type="number" class="item-input quantity-input" value="${item.quantidade || 0}" step="0.01"
                           onchange="updateItemField(${index}, 'quantidade', parseFloat(this.value) || 0)"
                           data-index="${index}">
                    <small class="quantity-unit">${item.unidade || 'UN'}</small>
                </div>
            </td>
            <td>
                <div class="quantity-column">
                    ${hasConversion ? `
                        <div class="converted-quantity">
                            <span class="converted-qty" id="convertedQty_${index}">${quantidadeConvertida}</span>
                            <small class="quantity-unit">${unidadeConvertida}</small>
                        </div>
                    ` : `
                        <div class="same-quantity">
                            <span class="same-qty">${item.quantidade || 0}</span>
                            <small class="quantity-unit">${item.unidade || 'UN'}</small>
                        </div>
                    `}
                </div>
            </td>
            <td>
                <div class="price-container">
                    <input type="number" class="item-input ${hasConversion ? 'price-conversion-highlight' : ''}"
                           value="${item.valorUnitario || 0}" step="0.01"
                           onchange="updateItemField(${index}, 'valorUnitario', parseFloat(this.value) || 0)"
                           placeholder="0,00"
                           title="${hasConversion ? 'Preço baseado na unidade de compra (' + produto.unidadeSecundaria + ')' : 'Preço unitário'}">
                    <small class="price-unit-info">
                        por ${hasConversion ? produto.unidadeSecundaria : (item.unidade || 'UN')}
                    </small>
                </div>
            </td>
            <td>
                <input type="number" class="item-input" value="${item.ipi || 0}" step="0.01" max="100"
                       onchange="updateItemField(${index}, 'ipi', parseFloat(this.value) || 0)"
                       placeholder="0,00">
            </td>
            <td>
                <input type="number" class="item-input" value="${item.icms || 0}" step="0.01" max="100"
                       onchange="updateItemField(${index}, 'icms', parseFloat(this.value) || 0)"
                       placeholder="0,00">
            </td>
            <td class="item-total">
                R$ ${formatCurrency(calculateItemTotal(item))}
            </td>
            <td>
                <button class="btn btn-danger btn-sm" onclick="removeEditItem(${index})" title="Remover item">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });

    calculateTotals();
}

window.addQuotationItem = function() {
    const newItem = {
        codigo: '',
        descricao: '',
        unidade: 'UN',
        quantidade: 1,
        valorUnitario: 0,
        ipi: 0,
        icms: 0
    };
    
    editingItems.push(newItem);
    renderEditItems();
};

window.removeEditItem = function(index) {
    if (confirm('Remover este item da cotação?')) {
        editingItems.splice(index, 1);
        renderEditItems();
    }
};

window.updateItemField = function(index, field, value) {
    if (editingItems[index]) {
        editingItems[index][field] = value;

        // Se mudou quantidade, atualizar conversão
        if (field === 'quantidade') {
            updateQuantityConversion(index);
        }

        // Se mudou quantidade ou valor, recalcular
        if (['quantidade', 'valorUnitario', 'ipi', 'icms'].includes(field)) {
            renderEditItems();
        }
    }
};

// ===== FUNÇÃO PARA ATUALIZAR CONVERSÃO DE QUANTIDADE =====
function updateQuantityConversion(index) {
    const item = editingItems[index];
    if (!item || !item.codigo) return;

    const produto = produtos.find(p => p.codigo === item.codigo);
    if (!produto || !produto.unidadeSecundaria || !produto.fatorConversao) return;

    const convertedQtyElement = document.getElementById(`convertedQty_${index}`);
    if (!convertedQtyElement) return;

    const quantidade = parseFloat(item.quantidade) || 0;
    let quantidadeConvertida = 0;

    // Conversão de PC para KG (ou outras unidades)
    if (produto.unidade === 'PC' && produto.unidadeSecundaria === 'KG') {
        quantidadeConvertida = quantidade * produto.fatorConversao;
    } else if (produto.unidade !== produto.unidadeSecundaria) {
        // Outras conversões usando o fator
        quantidadeConvertida = quantidade * produto.fatorConversao;
    }

    convertedQtyElement.textContent = quantidadeConvertida.toFixed(3);
}

function calculateItemTotal(item) {
    const valorUnitario = parseFloat(item.valorUnitario) || 0;
    const ipi = parseFloat(item.ipi) || 0;
    const icms = parseFloat(item.icms) || 0;

    // Buscar produto para verificar conversão
    const produto = produtos.find(p => p.codigo === item.codigo);
    let quantidadeParaCalculo = parseFloat(item.quantidade) || 0;

    // Se tem conversão, usar a quantidade convertida (segunda unidade) para o cálculo
    if (produto && produto.unidadeSecundaria && produto.fatorConversao) {
        quantidadeParaCalculo = quantidadeParaCalculo * produto.fatorConversao;
    }

    // Cálculo: Quantidade Compra × Valor Unitário (baseado na segunda unidade)
    const subtotal = quantidadeParaCalculo * valorUnitario;
    const valorIpi = subtotal * (ipi / 100);
    const valorIcms = subtotal * (icms / 100);

    return subtotal + valorIpi + valorIcms;
}

// ===== BUSCA DE PRODUTO POR CÓDIGO =====
window.searchProductByCode = function(index, codigo) {
    if (!codigo.trim()) return;

    const produto = produtos.find(p => p.codigo === codigo.trim());
    if (produto) {
        editingItems[index].descricao = produto.descricao || '';
        editingItems[index].unidade = produto.unidade || 'UN';

        // Adicionar informações de conversão ao item
        if (produto.unidadeSecundaria && produto.fatorConversao) {
            editingItems[index].unidadeSecundaria = produto.unidadeSecundaria;
            editingItems[index].fatorConversao = produto.fatorConversao;
            editingItems[index].temConversao = true;
        } else {
            editingItems[index].temConversao = false;
        }

        renderEditItems();

        // Mostrar notificação com informações de conversão
        let message = 'Produto encontrado e dados preenchidos';
        if (produto.unidadeSecundaria && produto.fatorConversao) {
            message += ` (Conversão: 1 ${produto.unidade} = ${produto.fatorConversao} ${produto.unidadeSecundaria})`;
        }
        showNotification(message, 'success', 3000);
    }
};

// ===== GERENCIAMENTO DE FORNECEDORES =====
function renderEditSuppliers() {
    const container = document.getElementById('editSuppliersContainer');
    container.innerHTML = '';

    editingSuppliers.forEach(fornecedorId => {
        const fornecedor = fornecedores.find(f => f.id === fornecedorId);
        if (!fornecedor) return;

        const supplierCard = document.createElement('div');
        supplierCard.className = 'supplier-card';
        // Obter nome do fornecedor com fallbacks
        const nomeExibicao = fornecedor.nome || fornecedor.razaoSocial || fornecedor.razao_social || fornecedor.nomeFantasia || fornecedor.nome_fantasia || 'Nome não informado';
        const razaoSocial = fornecedor.razaoSocial || fornecedor.razao_social || '';

        supplierCard.innerHTML = `
            <div class="supplier-header">
                <div class="supplier-name">
                    ${nomeExibicao}
                    <div style="font-size: 0.9rem; color: #6c757d; font-weight: normal;">
                        ${razaoSocial}
                    </div>
                </div>
                <div>
                    <span class="supplier-status active">Ativo</span>
                    <button class="btn btn-danger btn-sm" onclick="removeEditSupplier('${fornecedorId}')" style="margin-left: 10px;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="supplier-details">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Código:</strong> ${fornecedor.codigo || 'N/A'}<br>
                        <strong>CNPJ:</strong> ${fornecedor.cnpj || 'N/A'}<br>
                        <strong>Email:</strong> ${fornecedor.email || 'N/A'}
                    </div>
                    <div class="col-md-6">
                        <strong>Telefone:</strong> ${fornecedor.telefone || 'N/A'}<br>
                        <strong>Cidade:</strong> ${fornecedor.cidade || 'N/A'}<br>
                        <strong>Status:</strong> ${fornecedor.status || 'N/A'}
                    </div>
                </div>
            </div>
        `;
        container.appendChild(supplierCard);
    });

    if (editingSuppliers.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Nenhum fornecedor selecionado. Clique em "Adicionar Fornecedor" para incluir fornecedores nesta cotação.
            </div>
        `;
    }
}

window.addSupplierToQuotation = function() {
    // Remover modal existente se houver
    const existingModal = document.getElementById('selectSupplierModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Criar modal de seleção de fornecedores
    const availableSuppliers = fornecedores.filter(f => !editingSuppliers.includes(f.id));

    if (availableSuppliers.length === 0) {
        showNotification('Todos os fornecedores disponíveis já foram adicionados', 'warning');
        return;
    }

    const supplierOptions = availableSuppliers.map(f => {
        // Tentar diferentes campos para o nome
        const nome = f.nome || f.razaoSocial || f.razao_social || f.nomeFantasia || f.nome_fantasia || 'Nome não informado';
        const codigo = f.codigo || f.id || 'S/Código';
        return `<option value="${f.id}">${nome} - ${codigo}</option>`;
    }).join('');

    const modalHtml = `
        <div id="selectSupplierModal" class="modal" style="display: block;">
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>Selecionar Fornecedor</h3>
                    <span class="close" onclick="closeSupplierModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Fornecedor:</label>
                        <select class="form-control" id="selectedSupplierId">
                            <option value="">Selecione um fornecedor...</option>
                            ${supplierOptions}
                        </select>
                    </div>
                    <div style="text-align: right; margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="closeSupplierModal()">Cancelar</button>
                        <button class="btn btn-success" onclick="confirmAddSupplier()">Adicionar</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
};

// Função específica para fechar o modal de fornecedor
window.closeSupplierModal = function() {
    const modal = document.getElementById('selectSupplierModal');
    if (modal) {
        modal.remove();
    }
};

window.confirmAddSupplier = function() {
    const selectedId = document.getElementById('selectedSupplierId').value;
    if (selectedId) {
        // Verificar se o fornecedor já não foi adicionado (dupla verificação)
        if (!editingSuppliers.includes(selectedId)) {
            editingSuppliers.push(selectedId);
            renderEditSuppliers();
            showNotification('Fornecedor adicionado com sucesso', 'success');
        } else {
            showNotification('Este fornecedor já foi adicionado', 'warning');
        }
        closeSupplierModal();
    } else {
        showNotification('Selecione um fornecedor', 'warning');
    }
};

window.removeEditSupplier = function(supplierId) {
    if (confirm('Remover este fornecedor da cotação?')) {
        editingSuppliers = editingSuppliers.filter(id => id !== supplierId);
        renderEditSuppliers();
        showNotification('Fornecedor removido', 'success');
    }
};

// ===== CONDIÇÕES COMERCIAIS =====
function loadCommercialConditions(quotation) {
    document.getElementById('editDeliveryTerm').value = quotation.prazoEntrega || '';
    document.getElementById('editPaymentTerms').value = quotation.condicoesPagamento || '';
    document.getElementById('editCustomPayment').value = quotation.condicoesPagamentoCustom || '';
    document.getElementById('editFreightType').value = quotation.tipoFrete || 'CIF';
    document.getElementById('editFreightValue').value = quotation.valorFrete || '';
    document.getElementById('editProposalValidity').value = quotation.validadeProposta || '';
    document.getElementById('editDeliveryLocation').value = quotation.localEntrega || '';
    document.getElementById('editWarranty').value = quotation.garantia || '';
    document.getElementById('editCertifications').value = quotation.certificacoes || '';

    // Configurar eventos
    setupCommercialConditionsEvents();
}

function setupCommercialConditionsEvents() {
    // Mostrar/ocultar campo de pagamento personalizado
    document.getElementById('editPaymentTerms').addEventListener('change', function() {
        const customGroup = document.getElementById('customPaymentGroup');
        customGroup.style.display = this.value === 'PERSONALIZADO' ? 'block' : 'none';
    });

    // Mostrar/ocultar campo de valor do frete
    document.getElementById('editFreightType').addEventListener('change', function() {
        const freightGroup = document.getElementById('freightValueGroup');
        freightGroup.style.display = this.value === 'VALOR_FIXO' ? 'block' : 'none';
        calculateTotals();
    });

    // Recalcular totais quando valor do frete mudar
    document.getElementById('editFreightValue').addEventListener('input', calculateTotals);
}

// ===== OBSERVAÇÕES =====
function loadObservations(quotation) {
    document.getElementById('editInternalNotes').value = quotation.observacoesInternas || '';
    document.getElementById('editSupplierNotes').value = quotation.observacoesFornecedores || '';
    document.getElementById('editTechnicalSpecs').value = quotation.especificacoesTecnicas || '';
}

// ===== CÁLCULOS FINANCEIROS =====
function calculateTotals() {
    let subtotal = 0;
    let totalIPI = 0;

    editingItems.forEach(item => {
        const itemSubtotal = (item.quantidade || 0) * (item.valorUnitario || 0);
        const itemIPI = itemSubtotal * ((item.ipi || 0) / 100);
        
        subtotal += itemSubtotal;
        totalIPI += itemIPI;
    });

    const freightValue = parseFloat(document.getElementById('editFreightValue')?.value || 0);
    const grandTotal = subtotal + totalIPI + freightValue;

    // Atualizar displays
    document.getElementById('editSubtotal').textContent = `R$ ${formatCurrency(subtotal)}`;
    document.getElementById('editTotalIPI').textContent = `R$ ${formatCurrency(totalIPI)}`;
    document.getElementById('editFreightTotal').textContent = `R$ ${formatCurrency(freightValue)}`;
    document.getElementById('editGrandTotal').textContent = `R$ ${formatCurrency(grandTotal)}`;
    document.getElementById('editTotalValue').textContent = `R$ ${formatCurrency(grandTotal)}`;
}

// ===== FUNÇÕES AUXILIARES =====
function formatDateForInput(timestamp) {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toISOString().split('T')[0];
}

// ===== SALVAMENTO E ENVIO =====
window.saveQuotationDraft = async function() {
    try {
        const updatedQuotation = buildQuotationData();
        updatedQuotation.status = 'ABERTA';
        updatedQuotation.ultimaAtualizacao = Timestamp.now();

        // Adicionar ao histórico
        updatedQuotation.historico = [
            ...(currentEditingQuotation.historico || []),
            {
                data: Timestamp.now(),
                acao: 'EDICAO_RASCUNHO',
                usuario: currentUser.nome,
                detalhes: 'Cotação salva como rascunho'
            }
        ];

        await updateDoc(doc(db, "cotacoes", currentEditingQuotation.id), updatedQuotation);

        showNotification('Rascunho salvo com sucesso!', 'success');
        closeModal('editQuotationModal');
        await loadQuotations();

    } catch (error) {
        console.error('Erro ao salvar rascunho:', error);
        showNotification('Erro ao salvar rascunho: ' + error.message, 'error');
    }
};

window.sendQuotationForApproval = async function() {
    if (!validateQuotationData()) {
        return;
    }

    try {
        const updatedQuotation = buildQuotationData();
        updatedQuotation.status = 'AGUARDANDO_APROVACAO';
        updatedQuotation.dataEnvioAprovacao = Timestamp.now();
        updatedQuotation.ultimaAtualizacao = Timestamp.now();

        // Adicionar ao histórico
        updatedQuotation.historico = [
            ...(currentEditingQuotation.historico || []),
            {
                data: Timestamp.now(),
                acao: 'ENVIO_APROVACAO',
                usuario: currentUser.nome,
                detalhes: 'Cotação enviada para aprovação'
            }
        ];

        await updateDoc(doc(db, "cotacoes", currentEditingQuotation.id), updatedQuotation);

        showNotification('Cotação enviada para aprovação!', 'success');
        closeModal('editQuotationModal');
        await loadQuotations();

    } catch (error) {
        console.error('Erro ao enviar para aprovação:', error);
        showNotification('Erro ao enviar para aprovação: ' + error.message, 'error');
    }
};

window.sendQuotationToSuppliers = async function() {
    if (!validateQuotationData()) {
        return;
    }

    if (editingSuppliers.length === 0) {
        showNotification('Adicione pelo menos um fornecedor antes de enviar', 'warning');
        return;
    }

    try {
        const updatedQuotation = buildQuotationData();
        updatedQuotation.status = 'ENVIADA';
        updatedQuotation.dataEnvio = Timestamp.now();
        updatedQuotation.ultimaAtualizacao = Timestamp.now();

        // Adicionar ao histórico
        updatedQuotation.historico = [
            ...(currentEditingQuotation.historico || []),
            {
                data: Timestamp.now(),
                acao: 'ENVIO_FORNECEDORES',
                usuario: currentUser.nome,
                detalhes: `Cotação enviada para ${editingSuppliers.length} fornecedores`
            }
        ];

        await updateDoc(doc(db, "cotacoes", currentEditingQuotation.id), updatedQuotation);

        // Aqui você pode implementar o envio real por email
        // await sendEmailToSuppliers(updatedQuotation);

        showNotification(`Cotação enviada para ${editingSuppliers.length} fornecedores!`, 'success');
        closeModal('editQuotationModal');
        await loadQuotations();

    } catch (error) {
        console.error('Erro ao enviar cotação:', error);
        showNotification('Erro ao enviar cotação: ' + error.message, 'error');
    }
};

function buildQuotationData() {
    return {
        numero: document.getElementById('editQuotationNumber').value,
        dataLimite: document.getElementById('editQuotationDeadline').value ?
            Timestamp.fromDate(new Date(document.getElementById('editQuotationDeadline').value)) : null,

        // Itens
        itens: editingItems.filter(item => item.codigo && item.descricao),

        // Fornecedores
        fornecedores: editingSuppliers,

        // Condições comerciais
        prazoEntrega: parseInt(document.getElementById('editDeliveryTerm').value) || null,
        condicoesPagamento: document.getElementById('editPaymentTerms').value,
        condicoesPagamentoCustom: document.getElementById('editCustomPayment').value,
        tipoFrete: document.getElementById('editFreightType').value,
        valorFrete: parseFloat(document.getElementById('editFreightValue').value) || 0,
        validadeProposta: parseInt(document.getElementById('editProposalValidity').value) || null,
        localEntrega: document.getElementById('editDeliveryLocation').value,
        garantia: document.getElementById('editWarranty').value,
        certificacoes: document.getElementById('editCertifications').value,

        // Observações
        observacoesInternas: document.getElementById('editInternalNotes').value,
        observacoesFornecedores: document.getElementById('editSupplierNotes').value,
        especificacoesTecnicas: document.getElementById('editTechnicalSpecs').value,

        // Valores calculados
        valorEstimado: calculateGrandTotal()
    };
}

function validateQuotationData() {
    // Validar itens
    const validItems = editingItems.filter(item => item.codigo && item.descricao && item.quantidade > 0);
    if (validItems.length === 0) {
        showNotification('Adicione pelo menos um item válido à cotação', 'warning');
        showEditTab('itens', null);
        return false;
    }

    // Validar fornecedores para envio
    if (editingSuppliers.length === 0) {
        showNotification('Adicione pelo menos um fornecedor à cotação', 'warning');
        showEditTab('fornecedores', null);
        return false;
    }

    // Validar data limite
    const deadline = document.getElementById('editQuotationDeadline').value;
    if (deadline) {
        const deadlineDate = new Date(deadline);
        const today = new Date();
        if (deadlineDate <= today) {
            showNotification('A data limite deve ser posterior à data atual', 'warning');
            showEditTab('itens', null);
            return false;
        }
    }

    return true;
}

function calculateGrandTotal() {
    let total = 0;
    editingItems.forEach(item => {
        total += calculateItemTotal(item);
    });

    const freightValue = parseFloat(document.getElementById('editFreightValue')?.value || 0);
    return total + freightValue;
}

// ===== FECHAR MODAL COM CONFIRMAÇÃO =====
function closeModal(modalId) {
    if (modalId === 'editQuotationModal' && hasUnsavedChanges()) {
        if (confirm('Existem alterações não salvas. Deseja realmente fechar?')) {
            document.getElementById(modalId).style.display = 'none';
            currentEditingQuotation = null;
            editingItems = [];
            editingSuppliers = [];
        }
    } else {
        document.getElementById(modalId).style.display = 'none';
        if (modalId === 'editQuotationModal') {
            currentEditingQuotation = null;
            editingItems = [];
            editingSuppliers = [];
        }
    }
}

function hasUnsavedChanges() {
    // Implementar lógica para detectar mudanças
    // Por simplicidade, sempre retorna false por enquanto
    return false;
}

// ===== LINKS DE RESPOSTA PARA FORNECEDORES =====

// Função para gerar e exibir links de resposta
window.generateResponseLinks = function() {
    if (!currentEditingQuotation || !editingSuppliers.length) {
        showNotification('Nenhum fornecedor selecionado para gerar links', 'warning');
        return;
    }

    renderResponseLinks();
    showNotification('Links de resposta atualizados', 'success');
};

// Função para renderizar os links de resposta
function renderResponseLinks() {
    const container = document.getElementById('responseLinksContainer');

    if (!currentEditingQuotation || !editingSuppliers.length) {
        container.innerHTML = `
            <div class="no-suppliers-message">
                <i class="fas fa-info-circle"></i>
                Adicione fornecedores para gerar os links de resposta
            </div>
        `;
        return;
    }

    const baseUrl = window.location.origin + window.location.pathname.replace('/cotacoes/index.html', '');

    const linksHtml = editingSuppliers.map(supplierId => {
        const supplier = fornecedores.find(f => f.id === supplierId);
        if (!supplier) return '';

        const responseUrl = `${baseUrl}/resposta_cotacao.html?cotacao=${currentEditingQuotation.id}&fornecedor=${supplierId}`;

        // Obter nome com fallbacks
        const nomeCompleto = supplier.nome || supplier.razaoSocial || supplier.razao_social || supplier.nomeFantasia || supplier.nome_fantasia || 'Nome não informado';
        const supplierInitials = nomeCompleto.substring(0, 2).toUpperCase();

        return `
            <div class="response-link-item">
                <div class="supplier-info">
                    <div class="supplier-avatar">${supplierInitials}</div>
                    <div class="supplier-details">
                        <h6>${nomeCompleto}</h6>
                        <small>${supplier.codigo || supplier.id || 'Código não informado'} • ${supplier.email || 'Email não informado'}</small>
                    </div>
                </div>
                <div class="link-actions">
                    <div class="link-url" title="${responseUrl}">${responseUrl}</div>
                    <button class="copy-link-btn" onclick="copyLink('${responseUrl}', this)">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = linksHtml;
}

// Função para copiar um link específico
window.copyLink = async function(url, button) {
    try {
        await navigator.clipboard.writeText(url);

        // Feedback visual
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('copied');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('copied');
        }, 2000);

        showNotification('Link copiado para a área de transferência', 'success');
    } catch (err) {
        console.error('Erro ao copiar link:', err);
        showNotification('Erro ao copiar link', 'error');
    }
};

// Função para copiar todos os links
window.copyAllLinks = async function() {
    if (!currentEditingQuotation || !editingSuppliers.length) {
        showNotification('Nenhum link para copiar', 'warning');
        return;
    }

    const baseUrl = window.location.origin + window.location.pathname.replace('/cotacoes/index.html', '');

    const allLinks = editingSuppliers.map(supplierId => {
        const supplier = fornecedores.find(f => f.id === supplierId);
        const supplierName = supplier ?
            (supplier.nome || supplier.razaoSocial || supplier.razao_social || supplier.nomeFantasia || supplier.nome_fantasia || 'Fornecedor') :
            'Fornecedor';
        const responseUrl = `${baseUrl}/resposta_cotacao.html?cotacao=${currentEditingQuotation.id}&fornecedor=${supplierId}`;

        return `${supplierName}: ${responseUrl}`;
    }).join('\n\n');

    try {
        await navigator.clipboard.writeText(allLinks);
        showNotification(`${editingSuppliers.length} links copiados para a área de transferência`, 'success');
    } catch (err) {
        console.error('Erro ao copiar links:', err);
        showNotification('Erro ao copiar links', 'error');
    }
};

// Atualizar renderização de fornecedores para incluir links
const originalRenderEditSuppliers = renderEditSuppliers;
renderEditSuppliers = function() {
    originalRenderEditSuppliers();
    renderResponseLinks();
};
