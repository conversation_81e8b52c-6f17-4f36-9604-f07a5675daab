// ===== COTAÇÕES - CORE FUNCTIONS =====

// Variáveis globais
let cotacoes = [];
let solicitacoes = [];
let fornecedores = [];
let produtos = [];
// ✅ AUTENTICAÇÃO MELHORADA - Versão temporária
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || {
    nome: 'Sistema',
    id: 'sistema',
    uid: 'sistema',
    nivel: 9 // Super usuário temporário
};

// Variáveis de paginação e filtros
let currentPage = 1;
let itemsPerPage = 20;
let totalItems = 0;
let filteredCotacoes = [];

// Variáveis de controle de visualização
let showAglutinadas = false; // Por padrão, ocultar cotações aglutinadas filhas
let showFechadas = false;
let selectedQuotations = new Set();

// Variáveis de controle de cache
let lastUpdateTime = null; // Controle de cache
let isLoading = false; // Prevenir múltiplos carregamentos simultâneos

// Função para comparar números de cotação de forma inteligente
function compareNumbers(numA, numB) {
    // Extrair partes do número COT-AAMM-XXXX
    const parseNumber = (num) => {
        if (!num) return { year: 0, month: 0, sequence: 0 };

        // Formato COT-AAMM-XXXX
        const match = num.match(/COT-(\d{2})(\d{2})-(\d{4})/);
        if (match) {
            return {
                year: parseInt(match[1]),
                month: parseInt(match[2]),
                sequence: parseInt(match[3])
            };
        }

        // Fallback: extrair apenas números
        const numbers = num.replace(/\D/g, '');
        return {
            year: 0,
            month: 0,
            sequence: parseInt(numbers) || 0
        };
    };

    const a = parseNumber(numA);
    const b = parseNumber(numB);

    // Comparar por ano, depois mês, depois sequência
    if (a.year !== b.year) return a.year - b.year;
    if (a.month !== b.month) return a.month - b.month;
    return a.sequence - b.sequence;
}

// ===== INICIALIZAÇÃO =====
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log("🔐 Verificando autenticação...");

        // ✅ VERIFICAÇÃO BÁSICA DE AUTENTICAÇÃO
        if (!currentUser || !currentUser.nome) {
            console.warn('⚠️ Usuário não encontrado, redirecionando para login...');
            window.location.href = '../login.html';
            return;
        }

        console.log("✅ Usuário autenticado:", currentUser.nome);

        // ✅ VERIFICAÇÃO BÁSICA DE PERMISSÕES
        if (currentUser.nivel < 2 && currentUser.id !== 'sistema') {
            alert('❌ Você não tem permissão para acessar cotações.');
            window.location.href = '../index.html';
            return;
        }

        showNotification('📊 Carregando dados...', 'info');
        await loadInitialData();
        await loadQuotations();
        updateStats();
        setupEventListeners();

        // TODO: Registrar acesso ao módulo
        console.log('📝 Acesso ao módulo de cotações:', currentUser.nome);

        showNotification('✅ Sistema carregado com sucesso!', 'success');
    } catch (error) {
        console.error('❌ Erro na inicialização:', error);
        showNotification('❌ Erro ao carregar sistema: ' + error.message, 'error');
    }
});

// ===== CARREGAMENTO DE DADOS =====
async function loadInitialData() {
    try {
        // Carregar todos os fornecedores primeiro
        const fornecedoresSnap = await getDocs(collection(db, "fornecedores"));
        const todosFornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        // Filtrar fornecedores ativos e homologados (mais flexível)
        fornecedores = todosFornecedores.filter(f => {
            const isAtivo = f.status === 'ATIVO' || f.status === 'Ativo' || f.ativo === true;
            const isHomologado = f.homologado === true || f.status === 'HOMOLOGADO';
            return isAtivo && isHomologado;
        });
        
        // Se não encontrar fornecedores com filtros rigorosos, usar todos os ativos
        if (fornecedores.length === 0) {
            fornecedores = todosFornecedores.filter(f => 
                f.status === 'ATIVO' || f.status === 'Ativo' || f.ativo === true
            );
        }
        
        // Se ainda não encontrar, usar todos
        if (fornecedores.length === 0) {
            fornecedores = todosFornecedores;
            console.warn('Nenhum fornecedor ativo encontrado, carregando todos os fornecedores');
        }

        // Carregar outros dados
        const [produtosSnap, solicitacoesSnap] = await Promise.all([
            getDocs(collection(db, "produtos")),
            getDocs(collection(db, "solicitacoesCompra"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log('✅ Dados carregados com segurança:', {
            fornecedores: fornecedores.length,
            produtos: produtos.length,
            solicitacoes: solicitacoes.length,
            usuario: currentUser.nome,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Erro ao carregar dados iniciais:', error);
        throw error;
    }
}

async function loadQuotations(forceReload = false) {
    // Prevenir múltiplos carregamentos simultâneos
    if (isLoading && !forceReload) {
        console.log("⏳ Carregamento já em andamento, aguardando...");
        return;
    }

    // Verificar cache (não recarregar se foi carregado há menos de 30 segundos)
    const now = Date.now();
    if (!forceReload && lastUpdateTime && (now - lastUpdateTime) < 30000) {
        console.log("📋 Usando dados do cache (carregados há menos de 30s)");
        renderQuotations();
        updateStats();
        return;
    }

    isLoading = true;

    try {
        console.log("🔄 Carregando cotações do Firebase...");

        const cotacoesSnap = await getDocs(collection(db, "cotacoes"));
        cotacoes = cotacoesSnap.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            _loadedAt: now
        }));

        // Ordenar por número (mais recentes primeiro)
        cotacoes.sort((a, b) => {
            return compareNumbers(b.numero, a.numero);
        });

        // Atualizar timestamp do cache
        lastUpdateTime = now;

        // Garantir que os botões estejam no estado correto ANTES de renderizar
        if (typeof updateToggleButtons === 'function') {
            updateToggleButtons();
        }

        renderQuotations();
        updateStats();
        loadFilters();

        console.log(`✅ ${cotacoes.length} cotações carregadas para usuário: ${currentUser.nome}`);

        // Adicionar dados de teste se necessário
        addTestDataIfNeeded();

    } catch (error) {
        console.error('❌ Erro ao carregar cotações:', error);
        throw error;
    } finally {
        isLoading = false;
    }
}

// ===== RENDERIZAÇÃO =====
function renderQuotations(cotacoesToRender = null) {
    const tbody = document.getElementById('quotationsTableBody');
    tbody.innerHTML = '';

    // Se não foram passadas cotações específicas, usar todas
    if (!cotacoesToRender) {
        filteredCotacoes = [...cotacoes];
    } else {
        filteredCotacoes = cotacoesToRender;
    }

    // Aplicar filtros
    // Filtrar cotações aglutinadas se necessário
    if (!showAglutinadas) {
        filteredCotacoes = filteredCotacoes.filter(c => {
            // Se tem status AGLUTINADO ou AGLUTINADA (cotação filha), ocultar
            if (c.status === 'AGLUTINADO' || c.status === 'AGLUTINADA') return false;

            // Se não tem propriedade aglutinada, mostrar (cotação normal)
            if (!c.aglutinada) return true;

            // Se tem aglutinada mas é principal, mostrar
            if (c.aglutinada.tipo === 'principal') return true;

            // Se é filha, ocultar
            if (c.aglutinada.tipo === 'filha') return false;

            // Caso padrão: mostrar
            return true;
        });
    }
    
    // Filtrar cotações fechadas se necessário
    if (!showFechadas) {
        filteredCotacoes = filteredCotacoes.filter(c => c.status !== 'FECHADA');
    }

    totalItems = filteredCotacoes.length;

    // Calcular paginação
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const cotacoesPage = filteredCotacoes.slice(startIndex, endIndex);

    cotacoesPage.forEach(quotation => {
        const row = document.createElement('tr');
        row.className = 'quotation-row';
        row.setAttribute('data-quotation-id', quotation.id);

        // Aplicar classes especiais
        if (quotation.aglutinada) {
            if (quotation.aglutinada.tipo === 'principal') {
                row.classList.add('aglutinada');
            } else if (quotation.aglutinada.tipo === 'filha') {
                row.classList.add('aglutinada-filha');
                if (!showAglutinadas) {
                    row.classList.add('hidden');
                }
            }
        }

        if (quotation.status === 'FECHADA') {
            row.classList.add('fechada');
        }

        const statusClass = getStatusClass(quotation.status);
        const solicitacao = solicitacoes.find(s => s.id === quotation.solicitacaoId);
        const fornecedoresCount = quotation.fornecedores ? quotation.fornecedores.length : 0;
        const respostasCount = quotation.respostas ? Object.keys(quotation.respostas).length : 0;
        const totalItens = quotation.itens ? quotation.itens.length : (solicitacao?.itens?.length || 0);
        const valorTotal = quotation.itens ?
            quotation.itens.reduce((sum, item) => sum + (item.quantidade * (item.valorUnitario || 0)), 0) :
            (quotation.valorEstimado || 0);

        // Obter nomes dos fornecedores
        const fornecedoresNomes = quotation.fornecedores ?
            quotation.fornecedores.map(fornecedorId => {
                const fornecedor = fornecedores.find(f => f.id === fornecedorId);
                return fornecedor ? (fornecedor.nome || fornecedor.razaoSocial || 'Nome não informado') : 'Fornecedor não encontrado';
            }).join(', ') : 'Nenhum fornecedor';

        // Informações de status especiais
        let statusInfo = '';
        
        // Badge para cotações fechadas
        if (quotation.status === 'FECHADA') {
            statusInfo += `<span class="fechada-badge">FECHADA</span>`;
        }
        
        // Informações de aglutinação
        if (quotation.aglutinada) {
            if (quotation.aglutinada.tipo === 'principal') {
                const filhas = cotacoes.filter(c => c.aglutinada && c.aglutinada.principalId === quotation.id);
                statusInfo += `
                    <span class="aglutinacao-badge">PRINCIPAL</span>
                    <div class="aglutinacao-info">
                        <button class="expand-collapse-btn" onclick="toggleAglutinacaoFilhas('${quotation.id}')">
                            <i class="fas fa-chevron-down" id="expand-icon-${quotation.id}"></i> ${filhas.length} cotações aglutinadas
                        </button>
                    </div>
                `;
            } else if (quotation.aglutinada.tipo === 'filha') {
                statusInfo += `<span class="aglutinacao-badge filha">AGLUTINADA</span>`;
            }
        }

        row.innerHTML = `
            <td>
                <input type="checkbox" 
                       class="quotation-checkbox" 
                       value="${quotation.id}"
                       onchange="toggleQuotationSelection('${quotation.id}')"
                       ${selectedQuotations.has(quotation.id) ? 'checked' : ''}>
            </td>
            <td>
                <strong>${quotation.numero || 'N/A'}</strong>
                ${statusInfo}
            </td>
            <td>
                ${formatDate(quotation.dataCriacao)}
                ${quotation.dataLimite ? `<br><small style="color: #dc3545;"><i class="fas fa-clock"></i> Limite: ${formatDate(quotation.dataLimite)}</small>` : ''}
            </td>
            <td>
                ${solicitacao ? `SC-${solicitacao.numero || 'N/A'}` : 'N/A'}
                <span class="integration-badge">Auto</span>
                <br><small style="color: #6c757d;">${totalItens} itens</small>
            </td>
            <td>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="flex: 1;">
                        <div style="font-weight: 500; margin-bottom: 2px;">
                            ${fornecedoresCount} fornecedor${fornecedoresCount !== 1 ? 'es' : ''}
                        </div>
                        <div style="font-size: 0.85rem; color: #6c757d; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${fornecedoresNomes}">
                            ${fornecedoresNomes}
                        </div>
                    </div>
                    <div class="response-indicators">
                        ${Array.from({length: fornecedoresCount}, (_, i) =>
                            `<div class="response-dot ${i < respostasCount ? 'responded' : 'pending'}"></div>`
                        ).join('')}
                    </div>
                </div>
            </td>
            <td>${respostasCount}/${fornecedoresCount}</td>
            <td>R$ ${formatCurrency(valorTotal)}</td>
            <td><span class="status ${statusClass}">${getStatusText(quotation.status)}</span></td>
            <td>
                <div class="actions">
                    <button class="btn btn-info btn-sm" onclick="viewQuotation('${quotation.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="editQuotation('${quotation.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="sendQuotation('${quotation.id}')">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    ${quotation.respostas && Object.keys(quotation.respostas).length > 0 ? `
                        <button class="btn btn-success btn-sm" onclick="analyzeQuotation('${quotation.id}')" title="Analisar Cotação">
                            <i class="fas fa-chart-line"></i>
                        </button>
                    ` : ''}
                    ${quotation.aglutinada && quotation.aglutinada.tipo === 'principal' ? `
                        <button class="btn btn-warning btn-sm" onclick="dividirCotacao('${quotation.id}')" title="Dividir aglutinação">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });

    updatePaginationInfo();
    updatePaginationControls();
    updateSelectedQuotationsCounter();
}

// ===== FUNÇÕES AUXILIARES =====
function formatDate(timestamp) {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('pt-BR');
}

function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value || 0);
}

function getStatusClass(status) {
    const statusMap = {
        'ABERTA': 'aberta',
        'ENVIADA': 'enviada',
        'RESPONDIDA': 'respondida',
        'APROVADA': 'aprovada',
        'FECHADA': 'fechada',
        'AGLUTINADO': 'aglutinada',
        'AGLUTINADA': 'aglutinada'
    };
    return statusMap[status] || 'aberta';
}

function getStatusText(status) {
    const statusMap = {
        'ABERTA': 'Aberta',
        'ENVIADA': 'Enviada',
        'RESPONDIDA': 'Respondida',
        'APROVADA': 'Aprovada',
        'FECHADA': 'Fechada',
        'AGLUTINADO': 'Aglutinada',
        'AGLUTINADA': 'Aglutinada'
    };
    return statusMap[status] || status;
}

// ===== NOTIFICAÇÕES =====
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.getElementById('notification');
    notification.textContent = message;
    notification.className = `notification notification-${type} show`;
    
    setTimeout(() => {
        notification.classList.remove('show');
    }, duration);
}

// ===== SETUP DE EVENT LISTENERS =====
function setupEventListeners() {
    // Pesquisa
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(performSearch, 300));
    }

    // Filtros
    const filterElements = ['dataInicio', 'dataFim', 'statusFilter', 'solicitacaoFilter', 'fornecedorFilter'];
    filterElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', applyFilters);
        }
    });
}

// Função debounce para otimizar pesquisa
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Função para forçar atualização manual
window.forceRefresh = async function() {
    console.log("🔄 Forçando atualização manual...");

    try {
        // Aguardar um pouco para garantir que o Firebase processou as mudanças
        await new Promise(resolve => setTimeout(resolve, 500));

        // Recarregar dados forçadamente (ignora cache)
        await loadQuotations(true);

        console.log("✅ Atualização manual concluída");

        // Mostrar feedback visual
        showSuccessMessage("Dados atualizados com sucesso!");

    } catch (error) {
        console.error("❌ Erro na atualização manual:", error);
        showNotification("Erro ao atualizar dados: " + error.message, "error");
    }
};

// Função para mostrar mensagem de sucesso
function showSuccessMessage(message) {
    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease-out;
    `;

    notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;

    // Adicionar CSS da animação
    if (!document.getElementById('notificationStyles')) {
        const style = document.createElement('style');
        style.id = 'notificationStyles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Remover após 3 segundos
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ===== ADICIONAR DADOS DE TESTE =====
function addTestDataIfNeeded() {
    // Verificar se já existe cotação com respostas
    const hasResponseData = cotacoes.some(c => c.respostas && Object.keys(c.respostas).length > 0);

    if (!hasResponseData && fornecedores.length >= 2) {
        // Criar cotação de teste com respostas
        const testQuotation = {
            id: 'test-cot-001',
            numero: 'CT-2506-TEST',
            dataCriacao: Timestamp.now(),
            dataLimite: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)),
            status: 'RESPONDIDA',
            solicitacaoId: null,
            fornecedores: [fornecedores[0].id, fornecedores[1].id],
            itens: [
                {
                    codigo: 'TEST001',
                    descricao: 'Produto de Teste A',
                    quantidade: 100,
                    unidade: 'UN',
                    valorUnitario: 15.50
                },
                {
                    codigo: 'TEST002',
                    descricao: 'Produto de Teste B',
                    quantidade: 50,
                    unidade: 'KG',
                    valorUnitario: 25.00
                }
            ],
            valorEstimado: 2800.00,
            respostas: {}
        };

        // Resposta do primeiro fornecedor
        testQuotation.respostas[fornecedores[0].id] = {
            itens: [
                {
                    indiceItem: 0,
                    codigo: 'TEST001',
                    descricao: 'Produto de Teste A',
                    quantidadeSolicitada: 100,
                    quantidadeFornecida: 100,
                    unidade: 'UN',
                    precoUnitario: 14.50,
                    ipi: 5,
                    icms: 12,
                    valorTotal: 1450.00
                },
                {
                    indiceItem: 1,
                    codigo: 'TEST002',
                    descricao: 'Produto de Teste B',
                    quantidadeSolicitada: 50,
                    quantidadeFornecida: 50,
                    unidade: 'KG',
                    precoUnitario: 23.00,
                    ipi: 3,
                    icms: 12,
                    valorTotal: 1150.00
                }
            ],
            totalItensRespondidos: 2,
            totalItensDisponiveis: 2,
            prazoEntrega: 15,
            condicaoPagamento: '30 dias',
            validadeProposta: 30,
            observacoes: 'Preços especiais para este pedido',
            dataResposta: Timestamp.now()
        };

        // Resposta do segundo fornecedor (parcial)
        testQuotation.respostas[fornecedores[1].id] = {
            itens: [
                {
                    indiceItem: 0,
                    codigo: 'TEST001',
                    descricao: 'Produto de Teste A',
                    quantidadeSolicitada: 100,
                    quantidadeFornecida: 80,
                    unidade: 'UN',
                    precoUnitario: 13.80,
                    ipi: 5,
                    icms: 12,
                    valorTotal: 1104.00,
                    observacaoItem: 'Quantidade ajustada: solicitado 100, fornecido 80'
                }
            ],
            totalItensRespondidos: 1,
            totalItensDisponiveis: 2,
            prazoEntrega: 20,
            condicaoPagamento: '45 dias',
            validadeProposta: 15,
            observacoes: 'Não temos o segundo produto em estoque',
            dataResposta: Timestamp.now()
        };

        // Adicionar à lista de cotações
        cotacoes.unshift(testQuotation);

        console.log('Dados de teste adicionados para análise de cotações');

        // Re-renderizar para mostrar a nova cotação
        renderQuotations();
    }
}

// ===== FUNÇÕES DE CONVERSÃO DE UNIDADES =====

// Função para calcular conversão de unidades
window.calculateUnitConversion = function(quantidade, produto) {
    if (!produto || !produto.unidadeSecundaria || !produto.fatorConversao) {
        return {
            quantidadeConvertida: quantidade,
            unidadeConvertida: produto?.unidade || 'UN',
            temConversao: false
        };
    }

    const quantidadeConvertida = quantidade * produto.fatorConversao;

    return {
        quantidadeConvertida: quantidadeConvertida,
        unidadeConvertida: produto.unidadeSecundaria,
        temConversao: true,
        fatorConversao: produto.fatorConversao,
        unidadeOriginal: produto.unidade
    };
};

// Função para formatar exibição de conversão
window.formatConversionDisplay = function(quantidade, produto) {
    const conversion = calculateUnitConversion(quantidade, produto);

    if (!conversion.temConversao) {
        return `${quantidade} ${conversion.unidadeConvertida}`;
    }

    return `${quantidade} ${conversion.unidadeOriginal} (${conversion.quantidadeConvertida.toFixed(3)} ${conversion.unidadeConvertida})`;
};

// Função para adicionar produtos de exemplo com conversão
window.addConversionExamples = function() {
    const exemplosProdutos = [
        {
            id: 'conv001',
            codigo: 'CABO001',
            descricao: 'Cabo de Rede Cat6 - Controle interno em PC, compra em KG',
            unidade: 'PC',
            unidadeSecundaria: 'KG',
            fatorConversao: 0.15, // 1 PC = 0.15 KG
            tipo: 'PA',
            status: 'ativo'
        },
        {
            id: 'conv002',
            codigo: 'PARAF001',
            descricao: 'Parafuso M6x20 - Controle interno em PC, compra em KG',
            unidade: 'PC',
            unidadeSecundaria: 'KG',
            fatorConversao: 0.025, // 1 PC = 0.025 KG
            tipo: 'PA',
            status: 'ativo'
        },
        {
            id: 'conv003',
            codigo: 'CHAPA001',
            descricao: 'Chapa de Aço - Controle interno em PC, compra em KG',
            unidade: 'PC',
            unidadeSecundaria: 'KG',
            fatorConversao: 12.5, // 1 PC = 12.5 KG
            tipo: 'PA',
            status: 'ativo'
        }
    ];

    // Adicionar aos produtos existentes se não existirem
    exemplosProdutos.forEach(exemplo => {
        if (!produtos.find(p => p.codigo === exemplo.codigo)) {
            produtos.push(exemplo);
        }
    });

    console.log('Produtos de exemplo com conversão adicionados:', exemplosProdutos.length);
    showNotification(`${exemplosProdutos.length} produtos de exemplo com conversão adicionados`, 'success');
};

// Função para demonstrar conversão em uma cotação
window.demonstrateConversion = function() {
    // Adicionar produtos de exemplo
    addConversionExamples();

    // Criar cotação de exemplo com conversão
    const cotacaoConversao = {
        id: 'conv_demo_' + Date.now(),
        numero: 'CT-CONV-' + String(Date.now()).slice(-4),
        dataCriacao: Timestamp.now(),
        dataLimite: Timestamp.fromDate(new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)),
        status: 'ABERTA',
        itens: [
            {
                codigo: 'CABO001',
                descricao: 'Cabo de Rede Cat6 - Controle interno em PC, compra em KG',
                unidade: 'PC',
                unidadeSecundaria: 'KG',
                fatorConversao: 0.15,
                quantidade: 100, // 100 PC = 15.000 KG
                valorUnitario: 25.00, // R$ 25,00 por KG
                ipi: 0,
                icms: 18,
                temConversao: true
                // Cálculo: 15.000 KG × R$ 25,00 = R$ 375,00 + ICMS
            },
            {
                codigo: 'PARAF001',
                descricao: 'Parafuso M6x20 - Controle interno em PC, compra em KG',
                unidade: 'PC',
                unidadeSecundaria: 'KG',
                fatorConversao: 0.025,
                quantidade: 500, // 500 PC = 12.500 KG
                valorUnitario: 45.00, // R$ 45,00 por KG
                ipi: 0,
                icms: 18,
                temConversao: true
                // Cálculo: 12.500 KG × R$ 45,00 = R$ 562,50 + ICMS
            }
        ],
        fornecedores: [],
        valorEstimado: 1106.25, // (375 + 562.5) × 1.18 (ICMS)
        observacoesFornecedores: 'Cotação de demonstração - Conversão de Unidades (PC para KG)',
        criadoPor: currentUser.nome || 'Sistema',
        historico: [{
            data: Timestamp.now(),
            acao: 'CRIACAO',
            usuario: currentUser.nome || 'Sistema',
            detalhes: 'Cotação de demonstração criada para mostrar conversão de unidades'
        }]
    };

    // Adicionar à lista
    cotacoes.unshift(cotacaoConversao);

    // Re-renderizar
    renderQuotations();
    updateStats();

    showNotification('Cotação de demonstração criada com conversão PC → KG', 'success');

    // Mostrar informações no console
    console.log('Cotação de demonstração criada:', cotacaoConversao);
    console.log('Conversões e Cálculos:');
    cotacaoConversao.itens.forEach(item => {
        const conversion = calculateUnitConversion(item.quantidade, item);
        const quantidadeCompra = conversion.quantidadeConvertida;
        const valorTotal = quantidadeCompra * item.valorUnitario;

        console.log(`${item.codigo}:`);
        console.log(`  - Quantidade Interna: ${item.quantidade} ${item.unidade}`);
        console.log(`  - Quantidade Compra: ${quantidadeCompra.toFixed(3)} ${conversion.unidadeConvertida}`);
        console.log(`  - Valor Unitário: R$ ${item.valorUnitario.toFixed(2)} por ${conversion.unidadeConvertida}`);
        console.log(`  - Cálculo: ${quantidadeCompra.toFixed(3)} × R$ ${item.valorUnitario.toFixed(2)} = R$ ${valorTotal.toFixed(2)}`);
        console.log('---');
    });
};
