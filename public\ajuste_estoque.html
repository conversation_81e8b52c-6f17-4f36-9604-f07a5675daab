<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajuste de Estoque</title>
    <script type="module" src="js/main.js"></script>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .header h1 {
            font-size: 2.8rem;
            margin: 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .form-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .form-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .form-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 25px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-weight: 600;
            position: relative;
        }

        .form-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #e74c3c, #f39c12);
            border-radius: 2px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-col {
            flex: 1;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: block;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        input, select, textarea {
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 100%;
            background: #f8f9fa;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.1);
            background: white;
            transform: translateY(-2px);
        }

        .info-box {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            padding: 15px;
            color: #2c3e50;
            font-weight: 500;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(149, 165, 166, 0.3);
            font-weight: 600;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-warning:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
            background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
        }

        .alert {
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            color: #d68910;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #a8dadc 100%);
            border: 2px solid #3498db;
            color: #0c5460;
        }

        .table-responsive {
            margin-top: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .items-table th,
        .items-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .items-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
            position: relative;
        }

        .items-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(135deg, #e74c3c, #f39c12);
        }

        .items-table tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .container {
                padding: 15px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .header {
                padding: 20px;
            }

            .form-container {
                padding: 20px;
            }

            .items-table th, .items-table td {
                padding: 10px 12px;
                font-size: 13px;
            }
        }

        /* Animações */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-container, .table-responsive {
            animation: slideIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Ajuste de Estoque</h1>
            <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
        </div>

        <!-- Alerta de implantação -->
        <div class="alert alert-warning">
            <strong>⚠️ Ferramenta de Implantação:</strong>
            Use esta funcionalidade para ajustar saldos durante a implantação do sistema.
            Registre sempre o motivo do ajuste para auditoria.
        </div>

        <!-- Seção de Importação Excel -->
        <div class="form-container" style="margin-bottom: 30px;">
            <h2 class="form-title">📊 Importação em Lote do Excel</h2>

            <!-- Instruções -->
            <div class="alert alert-info" style="background: #e8f4fd; border: 1px solid #bee5eb; color: #0c5460;">
                <strong>📋 Como usar:</strong>
                <ol style="margin: 10px 0 0 20px; line-height: 1.6;">
                    <li><strong>No Excel:</strong> Selecione duas colunas (Código do Produto e Quantidade)</li>
                    <li><strong>Copie:</strong> Pressione <kbd>Ctrl+C</kbd> para copiar</li>
                    <li><strong>Cole:</strong> Clique na área abaixo e pressione <kbd>Ctrl+V</kbd></li>
                    <li><strong>Valide:</strong> Sistema verificará automaticamente os dados</li>
                    <li><strong>Processe:</strong> Clique em "Processar Lote" para aplicar</li>
                </ol>
            </div>

            <!-- Área de Colagem -->
            <div class="form-row">
                <div class="form-col" style="flex: 1;">
                    <label>📋 Área de Colagem (Ctrl+V):</label>
                    <textarea
                        id="areaColagemExcel"
                        placeholder="Cole aqui os dados do Excel (Ctrl+V)

Exemplo de formato esperado:
PROD001	100
PROD002	250.5
PROD003	75

💡 Os dados devem estar separados por TAB (automaticamente quando copiados do Excel)"
                        style="width: 100%; height: 150px; padding: 15px; border: 2px dashed #28a745; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 14px; resize: vertical;"
                        oninput="processarDadosExcel()"
                        onpaste="setTimeout(processarDadosExcel, 100)">
                    </textarea>
                    <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                        💡 <strong>Dica:</strong> Os dados devem estar separados por TAB (automaticamente quando copiados do Excel)
                    </div>
                </div>
            </div>

            <!-- Configurações do Lote -->
            <div class="form-row" id="configLote" style="display: none;">
                <div class="form-col">
                    <label>Armazém para todos os itens:</label>
                    <select id="armazemLote" required>
                        <option value="">Selecione o armazém...</option>
                    </select>
                </div>
                <div class="form-col">
                    <label>Tipo de Ajuste:</label>
                    <select id="tipoAjusteLote" required>
                        <option value="ACERTO">Acerto (Definir saldo exato)</option>
                        <option value="ENTRADA">Entrada (Adicionar ao estoque)</option>
                        <option value="SAIDA">Saída (Reduzir do estoque)</option>
                    </select>
                </div>
            </div>

            <div class="form-row" id="motivoLote" style="display: none;">
                <div class="form-col" style="flex: 1;">
                    <label>Motivo do Ajuste em Lote:</label>
                    <textarea id="motivoAjusteLote" placeholder="Descreva o motivo do ajuste em lote..." style="width: 100%; height: 80px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                </div>
            </div>

            <!-- Botões de Ação -->
            <div class="form-row" id="acoesLote" style="display: none;">
                <div class="form-col">
                    <button type="button" onclick="validarLoteExcel()" class="btn-primary">
                        🔍 Validar Dados
                    </button>
                    <button type="button" onclick="processarLoteExcel()" class="btn-success" id="btnProcessarLote" disabled>
                        ⚡ Processar Lote
                    </button>
                    <button type="button" onclick="limparImportacao()" class="btn-secondary">
                        🗑️ Limpar
                    </button>
                </div>
            </div>
        </div>

        <!-- Preview dos Dados do Excel -->
        <div class="table-responsive" id="previewExcel" style="display: none; margin-bottom: 30px;">
            <h3 style="color: #495057; margin-bottom: 15px;">
                <i class="fas fa-eye"></i> Preview dos Dados Importados:
            </h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th>Código do Produto</th>
                        <th>Quantidade/Saldo</th>
                        <th>Produto Encontrado</th>
                        <th>Saldo Atual</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="corpoPreviewExcel">
                    <!-- Dados serão inseridos aqui -->
                </tbody>
            </table>
        </div>

        <!-- Relatório de Validação -->
        <div id="relatorioValidacaoExcel" style="display: none; margin-bottom: 30px;">
            <h3 style="color: #495057; margin-bottom: 15px;">
                <i class="fas fa-check-circle"></i> Relatório de Validação:
            </h3>
            <div id="conteudoRelatorioExcel">
                <!-- Relatório será inserido aqui -->
            </div>
        </div>

        <div class="form-container">
            <h2 class="form-title">Novo Ajuste de Estoque</h2>

            <form id="adjustmentForm" onsubmit="handleAdjustment(event)">
                <div class="form-row">
                    <div class="form-col">
                        <label>Produto:</label>
                        <select id="productSelect" onchange="loadProductInfo()" required>
                            <option value="">Selecione o produto...</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label>Armazém:</label>
                        <select id="warehouseSelect" onchange="loadCurrentBalance()" required>
                            <option value="">Selecione o armazém...</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <label>Informações do Produto:</label>
                        <div id="productInfo" class="info-box">Selecione um produto</div>
                    </div>
                    <div class="form-col">
                        <label>Saldo Atual:</label>
                        <div id="currentBalance" class="info-box">Selecione produto e armazém</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <label>Tipo de Ajuste:</label>
                        <select id="adjustmentType" onchange="toggleAdjustmentFields()" required>
                            <option value="">Selecione o tipo...</option>
                            <option value="ENTRADA">Entrada (Adicionar ao estoque)</option>
                            <option value="SAIDA">Saída (Reduzir do estoque)</option>
                            <option value="ACERTO">Acerto (Definir saldo exato)</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label id="quantityLabel">Quantidade:</label>
                        <input type="number" id="quantity" min="0.001" step="0.001" placeholder="0.000" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <label>Motivo do Ajuste:</label>
                        <select id="reasonSelect" onchange="toggleCustomReason()" required>
                            <option value="">Selecione o motivo...</option>
                            <option value="IMPLANTACAO">Implantação do sistema</option>
                            <option value="INVENTARIO">Ajuste de inventário</option>
                            <option value="CORRECAO_ERRO">Correção de erro</option>
                            <option value="PERDA">Perda de material</option>
                            <option value="AVARIA">Material avariado</option>
                            <option value="DEVOLUCAO">Devolução de cliente</option>
                            <option value="OUTRO">Outro motivo</option>
                        </select>
                    </div>
                    <div class="form-col" id="customReasonCol" style="display: none;">
                        <label>Motivo Personalizado:</label>
                        <input type="text" id="customReason" placeholder="Descreva o motivo...">
                    </div>
                </div>

                <div class="form-group">
                    <label>Observações Detalhadas:</label>
                    <textarea id="observations" rows="3" placeholder="Descreva detalhes do ajuste, documentos relacionados, etc." required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <label>Data do Ajuste:</label>
                        <input type="datetime-local" id="adjustmentDate" required>
                    </div>
                    <div class="form-col">
                        <label>Responsável:</label>
                        <input type="text" id="responsible" placeholder="Nome do responsável" required>
                    </div>
                </div>

                <!-- Resumo do ajuste -->
                <div id="adjustmentSummary" class="alert alert-info" style="display: none;">
                    <strong>📋 Resumo do Ajuste:</strong>
                    <div id="summaryContent"></div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <button type="submit" class="btn-warning">Realizar Ajuste</button>
                    </div>
                    <div class="form-col" style="text-align: right;">
                        <button type="button" class="btn-secondary" onclick="clearForm()">Limpar Formulário</button>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <h2 class="form-title">Histórico de Ajustes</h2>

            <!-- Filtros -->
            <div class="form-row" style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <div class="form-col">
                    <label>Filtrar por Produto:</label>
                    <input type="text" id="filterProduct" placeholder="Digite código ou descrição..." oninput="filterHistory()">
                </div>
                <div class="form-col">
                    <label>Período:</label>
                    <select id="filterPeriod" onchange="filterHistory()">
                        <option value="7">Últimos 7 dias</option>
                        <option value="30">Últimos 30 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="all">Todos os registros</option>
                    </select>
                </div>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Data/Hora</th>
                        <th>Produto</th>
                        <th>Armazém</th>
                        <th>Tipo</th>
                        <th>Quantidade</th>
                        <th>Saldo Anterior</th>
                        <th>Saldo Atual</th>
                        <th>Motivo</th>
                        <th>Responsável</th>
                        <th>Ação</th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    <tr>
                        <td colspan="10" style="text-align: center; color: #666; padding: 40px;">
                            <div style="font-size: 18px; margin-bottom: 10px;">📋</div>
                            <div>Nenhum ajuste realizado ainda</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            addDoc,
            getDocs,
            query,
            where,
            doc,
            updateDoc,
            Timestamp,
            orderBy,
            onSnapshot
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let armazens = [];
        let estoques = [];
        let ajustes = [];
        let currentUser = JSON.parse(localStorage.getItem('currentUser'));

        window.onload = async function() {
            if (!currentUser) {
                window.location.href = './login.html';
                return;
            }

            // Verificar se usuário tem permissão (nível 9 ou superior)
            if (currentUser.nivel < 9) {
                alert('Acesso negado. Esta funcionalidade requer nível 9 ou superior.');
                window.location.href = './index.html';
                return;
            }

            await loadInitialData();
            setupRealTimeListeners();
            setDefaultDateTime();
        };

        async function loadInitialData() {
            try {
                const [produtosSnap, armazensSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "estoques"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                populateSelects();
                loadHistory();

            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados iniciais.");
            }
        }

        function setupRealTimeListeners() {
            // Listener para estoques
            onSnapshot(collection(db, "estoques"), (snapshot) => {
                estoques = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                loadCurrentBalance();
            });

            // Listener para ajustes
            onSnapshot(collection(db, "ajustesEstoque"), (snapshot) => {
                loadHistory();
            });
        }

        function populateSelects() {
            // Popular select de produtos
            const productSelect = document.getElementById('productSelect');
            productSelect.innerHTML = '<option value="">Selecione o produto...</option>';

            produtos
                .filter(p => p.ativo !== false)
                .sort((a, b) => (a.codigo || '').localeCompare(b.codigo || ''))
                .forEach(produto => {
                    productSelect.innerHTML += `
                        <option value="${produto.id}">
                            ${produto.codigo} - ${produto.descricao}
                        </option>`;
                });

            // Popular select de armazéns
            const warehouseSelect = document.getElementById('warehouseSelect');
            warehouseSelect.innerHTML = '<option value="">Selecione o armazém...</option>';

            const armazemLote = document.getElementById('armazemLote');
            armazemLote.innerHTML = '<option value="">Selecione o armazém...</option>';

            armazens
                .filter(a => a.ativo !== false)
                .forEach(armazem => {
                    // Select individual
                    warehouseSelect.innerHTML += `
                        <option value="${armazem.id}">
                            ${armazem.codigo} - ${armazem.nome}
                        </option>`;

                    // Select do lote
                    armazemLote.innerHTML += `
                        <option value="${armazem.id}">
                            ${armazem.codigo} - ${armazem.nome}
                        </option>`;
                });
        }

        function setDefaultDateTime() {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000);
            document.getElementById('adjustmentDate').value = localDateTime.toISOString().slice(0, 16);
            document.getElementById('responsible').value = currentUser.nome;
        }

        window.loadProductInfo = function() {
            const produtoId = document.getElementById('productSelect').value;
            const productInfo = document.getElementById('productInfo');

            if (!produtoId) {
                productInfo.textContent = 'Selecione um produto';
                return;
            }

            const produto = produtos.find(p => p.id === produtoId);
            if (produto) {
                productInfo.innerHTML = `
                    <strong>Código:</strong> ${produto.codigo}<br>
                    <strong>Tipo:</strong> ${produto.tipo || 'N/A'}<br>
                    <strong>Unidade:</strong> ${produto.unidade || 'UN'}
                `;
                loadCurrentBalance();
                updateAdjustmentSummary();
            }
        };

        window.loadCurrentBalance = function() {
            const produtoId = document.getElementById('productSelect').value;
            const armazemId = document.getElementById('warehouseSelect').value;
            const balanceDiv = document.getElementById('currentBalance');

            if (!produtoId || !armazemId) {
                balanceDiv.textContent = 'Selecione produto e armazém';
                return;
            }

            const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
            const produto = produtos.find(p => p.id === produtoId);
            const armazem = armazens.find(a => a.id === armazemId);
            const saldo = estoque ? estoque.saldo : 0;

            balanceDiv.innerHTML = `
                <strong>Saldo:</strong> ${saldo.toFixed(3)} ${produto ? produto.unidade : 'UN'}<br>
                <strong>Armazém:</strong> ${armazem ? armazem.nome : 'N/A'}
            `;
            balanceDiv.style.color = saldo > 0 ? '#107e3e' : '#bb0000';

            updateAdjustmentSummary();
        };

        window.toggleAdjustmentFields = function() {
            const adjustmentType = document.getElementById('adjustmentType').value;
            const quantityLabel = document.getElementById('quantityLabel');

            switch(adjustmentType) {
                case 'ENTRADA':
                    quantityLabel.textContent = 'Quantidade a Adicionar:';
                    break;
                case 'SAIDA':
                    quantityLabel.textContent = 'Quantidade a Reduzir:';
                    break;
                case 'ACERTO':
                    quantityLabel.textContent = 'Saldo Final Desejado:';
                    break;
                default:
                    quantityLabel.textContent = 'Quantidade:';
            }

            updateAdjustmentSummary();
        };

        window.toggleCustomReason = function() {
            const reasonSelect = document.getElementById('reasonSelect').value;
            const customReasonCol = document.getElementById('customReasonCol');
            const customReason = document.getElementById('customReason');

            if (reasonSelect === 'OUTRO') {
                customReasonCol.style.display = 'block';
                customReason.required = true;
            } else {
                customReasonCol.style.display = 'none';
                customReason.required = false;
                customReason.value = '';
            }
        };

        function updateAdjustmentSummary() {
            const produtoId = document.getElementById('productSelect').value;
            const armazemId = document.getElementById('warehouseSelect').value;
            const adjustmentType = document.getElementById('adjustmentType').value;
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;

            const summaryDiv = document.getElementById('adjustmentSummary');
            const summaryContent = document.getElementById('summaryContent');

            if (!produtoId || !armazemId || !adjustmentType || quantity <= 0) {
                summaryDiv.style.display = 'none';
                return;
            }

            const produto = produtos.find(p => p.id === produtoId);
            const armazem = armazens.find(a => a.id === armazemId);
            const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
            const saldoAtual = estoque ? estoque.saldo : 0;

            let novoSaldo = 0;
            let operacao = '';

            switch(adjustmentType) {
                case 'ENTRADA':
                    novoSaldo = saldoAtual + quantity;
                    operacao = `Adicionar ${quantity.toFixed(3)} ${produto.unidade}`;
                    break;
                case 'SAIDA':
                    novoSaldo = saldoAtual - quantity;
                    operacao = `Reduzir ${quantity.toFixed(3)} ${produto.unidade}`;
                    break;
                case 'ACERTO':
                    novoSaldo = quantity;
                    operacao = `Definir saldo como ${quantity.toFixed(3)} ${produto.unidade}`;
                    break;
            }

            summaryContent.innerHTML = `
                <strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}<br>
                <strong>Armazém:</strong> ${armazem.codigo} - ${armazem.nome}<br>
                <strong>Operação:</strong> ${operacao}<br>
                <strong>Saldo Atual:</strong> ${saldoAtual.toFixed(3)} ${produto.unidade}<br>
                <strong>Novo Saldo:</strong> ${novoSaldo.toFixed(3)} ${produto.unidade}
                ${novoSaldo < 0 ? '<br><span style="color: #bb0000;">⚠️ Atenção: Saldo ficará negativo!</span>' : ''}
            `;

            summaryDiv.style.display = 'block';
        }

        // Variáveis para importação Excel
        let dadosExcelValidados = [];
        let estatisticasValidacao = {};

        // Função para processar dados colados do Excel
        window.processarDadosExcel = function() {
            const textarea = document.getElementById('areaColagemExcel');
            const dados = textarea.value.trim();

            if (!dados) {
                document.getElementById('configLote').style.display = 'none';
                document.getElementById('motivoLote').style.display = 'none';
                document.getElementById('acoesLote').style.display = 'none';
                document.getElementById('previewExcel').style.display = 'none';
                document.getElementById('relatorioValidacaoExcel').style.display = 'none';
                return;
            }

            try {
                // Processar dados do Excel (separados por TAB e quebra de linha)
                const linhas = dados.split('\n').filter(linha => linha.trim());
                const dadosProcessados = [];

                linhas.forEach((linha, index) => {
                    const colunas = linha.split('\t');
                    if (colunas.length >= 2) {
                        dadosProcessados.push({
                            linha: index + 1,
                            codigo: colunas[0].trim(),
                            quantidade: colunas[1].trim(),
                            original: linha
                        });
                    }
                });

                if (dadosProcessados.length === 0) {
                    showNotification('❌ Nenhum dado válido encontrado. Verifique o formato.', 'error');
                    return;
                }

                // Mostrar configurações do lote
                document.getElementById('configLote').style.display = 'flex';
                document.getElementById('motivoLote').style.display = 'flex';
                document.getElementById('acoesLote').style.display = 'flex';

                // Armazenar dados para validação posterior
                dadosExcelValidados = dadosProcessados;

                showNotification(`📊 ${dadosProcessados.length} linhas detectadas. Configure o lote e clique em "Validar Dados".`, 'info');

            } catch (error) {
                console.error('❌ Erro ao processar dados:', error);
                showNotification('❌ Erro ao processar dados: ' + error.message, 'error');
            }
        };

        // Função para validar dados do Excel
        window.validarLoteExcel = async function() {
            if (dadosExcelValidados.length === 0) {
                showNotification('❌ Nenhum dado para validar. Cole os dados primeiro.', 'error');
                return;
            }

            const armazemId = document.getElementById('armazemLote').value;
            const tipoAjuste = document.getElementById('tipoAjusteLote').value;
            const motivo = document.getElementById('motivoAjusteLote').value.trim();

            if (!armazemId) {
                showNotification('❌ Selecione o armazém para o lote.', 'error');
                return;
            }

            if (!motivo) {
                showNotification('❌ Digite o motivo do ajuste em lote.', 'error');
                return;
            }

            try {
                showNotification('🔍 Validando dados...', 'info');

                const resultadosValidacao = [];
                let validos = 0;
                let invalidos = 0;
                let erros = [];

                for (const item of dadosExcelValidados) {
                    const resultado = await validarItemExcel(item, armazemId);
                    resultadosValidacao.push(resultado);

                    if (resultado.valido) {
                        validos++;
                    } else {
                        invalidos++;
                        erros.push(`Linha ${item.linha}: ${resultado.erro}`);
                    }
                }

                // Mostrar preview
                mostrarPreviewExcel(resultadosValidacao);

                // Mostrar relatório
                mostrarRelatorioValidacaoExcel(validos, invalidos, erros);

                // Habilitar botão de processar se há itens válidos
                document.getElementById('btnProcessarLote').disabled = validos === 0;

                if (validos > 0) {
                    showNotification(`✅ Validação concluída: ${validos} válidos, ${invalidos} inválidos`, 'success');
                } else {
                    showNotification('❌ Nenhum item válido encontrado.', 'error');
                }

            } catch (error) {
                console.error('❌ Erro na validação:', error);
                showNotification('❌ Erro na validação: ' + error.message, 'error');
            }
        };

        // Função para validar item individual do Excel
        async function validarItemExcel(item, armazemId) {
            try {
                // 1. VALIDAR FORMATO DA QUANTIDADE
                const quantidade = parseFloat(item.quantidade.replace(',', '.'));

                if (isNaN(quantidade)) {
                    return {
                        ...item,
                        valido: false,
                        erro: 'Quantidade inválida (não é um número)',
                        quantidade: item.quantidade
                    };
                }

                if (quantidade < 0) {
                    return {
                        ...item,
                        valido: false,
                        erro: 'Quantidade deve ser positiva',
                        quantidade: quantidade
                    };
                }

                // 2. BUSCAR PRODUTO PELO CÓDIGO
                const produto = produtos.find(p =>
                    p.codigo && p.codigo.toLowerCase() === item.codigo.toLowerCase()
                );

                if (!produto) {
                    return {
                        ...item,
                        valido: false,
                        erro: 'Produto não encontrado',
                        quantidade: quantidade,
                        produto: null,
                        saldoAtual: 0
                    };
                }

                // 3. BUSCAR ESTOQUE ATUAL
                const estoque = estoques.find(e =>
                    e.produtoId === produto.id && e.armazemId === armazemId
                );

                const saldoAtual = estoque ? (estoque.saldo || 0) : 0;

                // 4. VALIDAR SE QUANTIDADE É VÁLIDA PARA O TIPO DE AJUSTE
                const tipoAjuste = document.getElementById('tipoAjusteLote').value;
                let erro = null;

                if (tipoAjuste === 'SAIDA' && quantidade > saldoAtual) {
                    erro = `Quantidade de saída (${quantidade}) maior que saldo atual (${saldoAtual})`;
                }

                return {
                    ...item,
                    valido: !erro,
                    erro: erro,
                    quantidade: quantidade,
                    produto: produto,
                    saldoAtual: saldoAtual,
                    estoqueId: estoque?.id || null
                };

            } catch (error) {
                return {
                    ...item,
                    valido: false,
                    erro: 'Erro na validação: ' + error.message,
                    quantidade: 0,
                    produto: null,
                    saldoAtual: 0
                };
            }
        }

        // Função para mostrar preview dos dados
        function mostrarPreviewExcel(resultados) {
            const tbody = document.getElementById('corpoPreviewExcel');
            tbody.innerHTML = '';

            resultados.forEach((resultado, index) => {
                const row = document.createElement('tr');
                const statusClass = resultado.valido ? 'status-valid' : 'status-invalid';
                const statusText = resultado.valido ? '✅ Válido' : '❌ Inválido';

                row.innerHTML = `
                    <td><strong>${index + 1}</strong></td>
                    <td style="font-family: monospace; font-weight: bold;">${resultado.codigo}</td>
                    <td style="text-align: center; font-weight: bold;">${resultado.quantidade}</td>
                    <td>
                        ${resultado.produto ?
                            `<strong>${resultado.produto.codigo}</strong><br><small>${resultado.produto.descricao}</small>` :
                            '<span style="color: #dc3545;">Não encontrado</span>'
                        }
                    </td>
                    <td style="text-align: center;">${resultado.saldoAtual.toFixed(3)}</td>
                    <td>
                        <span class="${statusClass}">${statusText}</span>
                        ${!resultado.valido ? `<br><small style="color: #dc3545;">${resultado.erro}</small>` : ''}
                    </td>
                `;

                if (!resultado.valido) {
                    row.style.backgroundColor = '#fff5f5';
                }

                tbody.appendChild(row);
            });

            document.getElementById('previewExcel').style.display = 'block';
        }

        // Função para mostrar relatório de validação
        function mostrarRelatorioValidacaoExcel(validos, invalidos, erros) {
            const container = document.getElementById('conteudoRelatorioExcel');

            let html = `
                <div class="report-card report-success">
                    <h4 style="color: #28a745; margin: 0 0 10px 0;">✅ Itens Válidos: ${validos}</h4>
                    <p style="margin: 0;">Estes itens serão processados com sucesso.</p>
                </div>
            `;

            if (invalidos > 0) {
                html += `
                    <div class="report-card report-error">
                        <h4 style="color: #dc3545; margin: 0 0 10px 0;">❌ Itens Inválidos: ${invalidos}</h4>
                        <p style="margin: 0 0 10px 0;">Os seguintes erros foram encontrados:</p>
                        <ul style="margin: 0; padding-left: 20px;">
                `;

                erros.forEach(erro => {
                    html += `<li style="margin-bottom: 5px;">${erro}</li>`;
                });

                html += `
                        </ul>
                    </div>
                `;
            }

            const total = validos + invalidos;
            const percentualSucesso = total > 0 ? ((validos / total) * 100).toFixed(1) : 0;

            html += `
                <div class="report-card" style="border-left: 4px solid #17a2b8; background: #f8fdff;">
                    <h4 style="color: #17a2b8; margin: 0 0 10px 0;">📊 Resumo da Validação</h4>
                    <p style="margin: 0;">
                        <strong>Total de itens:</strong> ${total}<br>
                        <strong>Taxa de sucesso:</strong> ${percentualSucesso}%<br>
                        <strong>Tipo de ajuste:</strong> ${document.getElementById('tipoAjusteLote').value}<br>
                        <strong>Armazém:</strong> ${armazens.find(a => a.id === document.getElementById('armazemLote').value)?.nome || 'N/A'}
                    </p>
                </div>
            `;

            container.innerHTML = html;
            document.getElementById('relatorioValidacaoExcel').style.display = 'block';
        }

        // Função para processar lote do Excel
        window.processarLoteExcel = async function() {
            if (!dadosExcelValidados || dadosExcelValidados.length === 0) {
                showNotification('❌ Nenhum dado para processar.', 'error');
                return;
            }

            const armazemId = document.getElementById('armazemLote').value;
            const tipoAjuste = document.getElementById('tipoAjusteLote').value;
            const motivo = document.getElementById('motivoAjusteLote').value.trim();

            if (!armazemId || !tipoAjuste || !motivo) {
                showNotification('❌ Preencha todos os campos obrigatórios.', 'error');
                return;
            }

            try {
                showNotification('⚡ Processando lote...', 'info');

                let processados = 0;
                let erros = 0;
                const resultados = [];

                // Revalidar dados antes de processar
                for (const item of dadosExcelValidados) {
                    const validacao = await validarItemExcel(item, armazemId);

                    if (validacao.valido) {
                        try {
                            await processarItemIndividual(validacao, armazemId, tipoAjuste, motivo);
                            processados++;
                            resultados.push(`✅ ${validacao.codigo}: Processado com sucesso`);
                        } catch (error) {
                            erros++;
                            resultados.push(`❌ ${validacao.codigo}: ${error.message}`);
                        }
                    } else {
                        erros++;
                        resultados.push(`❌ ${validacao.codigo}: ${validacao.erro}`);
                    }
                }

                // Recarregar dados
                await loadData();
                await loadAdjustmentHistory();

                // Mostrar resultado final
                const mensagem = `🎯 Processamento concluído: ${processados} sucessos, ${erros} erros`;
                showNotification(mensagem, processados > 0 ? 'success' : 'warning');

                // Log detalhado
                console.log('📊 Resultado do processamento em lote:');
                resultados.forEach(resultado => console.log(resultado));

                // Limpar formulário se tudo foi processado com sucesso
                if (erros === 0) {
                    limparImportacao();
                }

            } catch (error) {
                console.error('❌ Erro no processamento do lote:', error);
                showNotification('❌ Erro no processamento: ' + error.message, 'error');
            }
        };

        // Função para processar item individual
        async function processarItemIndividual(item, armazemId, tipoAjuste, motivo) {
            const produto = item.produto;
            const quantidade = item.quantidade;
            const saldoAtual = item.saldoAtual;

            // Calcular novo saldo baseado no tipo de ajuste
            let novoSaldo;
            let operacao;

            switch (tipoAjuste) {
                case 'ENTRADA':
                    novoSaldo = saldoAtual + quantidade;
                    operacao = `Entrada de ${quantidade}`;
                    break;
                case 'SAIDA':
                    novoSaldo = saldoAtual - quantidade;
                    operacao = `Saída de ${quantidade}`;
                    break;
                case 'ACERTO':
                    novoSaldo = quantidade;
                    operacao = `Acerto para ${quantidade}`;
                    break;
                default:
                    throw new Error('Tipo de ajuste inválido');
            }

            // Registrar movimento
            const movimento = {
                produtoId: produto.id,
                armazemId: armazemId,
                tipo: tipoAjuste,
                quantidade: quantidade,
                saldoAnterior: saldoAtual,
                saldoAtual: novoSaldo,
                motivo: `${motivo} (Lote Excel - ${operacao})`,
                responsavel: currentUser?.nome || 'Sistema',
                dataHora: Timestamp.now(),
                origem: 'IMPORTACAO_EXCEL'
            };

            await addDoc(collection(db, 'movimentacoes_estoque'), movimento);

            // Atualizar ou criar registro de estoque
            if (item.estoqueId) {
                // Atualizar estoque existente
                await updateDoc(doc(db, 'estoques', item.estoqueId), {
                    saldo: novoSaldo,
                    ultimaMovimentacao: Timestamp.now()
                });
            } else {
                // Criar novo registro de estoque
                await addDoc(collection(db, 'estoques'), {
                    produtoId: produto.id,
                    armazemId: armazemId,
                    saldo: novoSaldo,
                    saldoReservado: 0,
                    saldoEmpenhado: 0,
                    ultimaMovimentacao: Timestamp.now(),
                    criadoEm: Timestamp.now()
                });
            }

            console.log(`✅ Item processado: ${produto.codigo} - ${saldoAtual} → ${novoSaldo}`);
        }

        // Função para limpar importação
        window.limparImportacao = function() {
            document.getElementById('areaColagemExcel').value = '';
            document.getElementById('armazemLote').value = '';
            document.getElementById('tipoAjusteLote').value = 'ACERTO';
            document.getElementById('motivoAjusteLote').value = '';

            document.getElementById('configLote').style.display = 'none';
            document.getElementById('motivoLote').style.display = 'none';
            document.getElementById('acoesLote').style.display = 'none';
            document.getElementById('previewExcel').style.display = 'none';
            document.getElementById('relatorioValidacaoExcel').style.display = 'none';

            document.getElementById('btnProcessarLote').disabled = true;
            dadosExcelValidados = [];

            showNotification('🗑️ Dados de importação limpos', 'info');
        };

        // Adicionar estilos para os status
        const style = document.createElement('style');
        style.textContent = `
            .status-valid {
                background: #d4edda;
                color: #155724;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }
            .status-invalid {
                background: #f8d7da;
                color: #721c24;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }
            .report-card {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                padding: 20px;
                margin-bottom: 15px;
            }
            .report-success {
                border-left: 4px solid #28a745;
                background: #f8fff9;
            }
            .report-error {
                border-left: 4px solid #dc3545;
                background: #fff5f5;
            }
            kbd {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 2px 6px;
                font-family: monospace;
                font-size: 12px;
            }
        `;
        document.head.appendChild(style);

        // Função para carregar dados iniciais
        window.loadData = async function() {
            try {
                console.log('🔄 Carregando dados...');

                // Carregar produtos
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`📦 ${produtos.length} produtos carregados`);

                // Carregar armazéns
                const armazensSnap = await getDocs(collection(db, "armazens"));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`🏪 ${armazens.length} armazéns carregados`);

                // Carregar estoques
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`📊 ${estoques.length} registros de estoque carregados`);

                // Preencher selects
                populateProductSelect();
                populateWarehouseSelect();

                console.log('✅ Dados carregados com sucesso');

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados: ' + error.message, 'error');
            }
        };

        // Função para carregar histórico de ajustes
        window.loadAdjustmentHistory = async function() {
            try {
                console.log('🔄 Carregando histórico de ajustes...');
                await loadHistory();
                console.log('✅ Histórico carregado com sucesso');
            } catch (error) {
                console.error('❌ Erro ao carregar histórico:', error);
                showNotification('Erro ao carregar histórico: ' + error.message, 'error');
            }
        };

        // Função para popular select de produtos
        function populateProductSelect() {
            const productSelect = document.getElementById('productSelect');
            if (!productSelect) return;

            productSelect.innerHTML = '<option value="">Selecione um produto...</option>';

            produtos.forEach(produto => {
                const option = document.createElement('option');
                option.value = produto.id;
                option.textContent = `${produto.codigo} - ${produto.descricao}`;
                productSelect.appendChild(option);
            });
        }

        // Função para popular select de armazéns
        function populateWarehouseSelect() {
            const warehouseSelect = document.getElementById('warehouseSelect');
            if (!warehouseSelect) return;

            warehouseSelect.innerHTML = '<option value="">Selecione um armazém...</option>';

            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                warehouseSelect.appendChild(option);
            });
        }

        // Adicionar listeners para atualizar resumo
        document.addEventListener('DOMContentLoaded', function() {
            const quantity = document.getElementById('quantity');
            if (quantity) {
                quantity.addEventListener('input', updateAdjustmentSummary);
            }

            // Carregar dados iniciais
            loadData();
            loadAdjustmentHistory();
        });

        window.handleAdjustment = async function(event) {
            event.preventDefault();

            const produtoId = document.getElementById('productSelect').value;
            const armazemId = document.getElementById('warehouseSelect').value;
            const adjustmentType = document.getElementById('adjustmentType').value;
            const quantity = parseFloat(document.getElementById('quantity').value);
            const reasonSelect = document.getElementById('reasonSelect').value;
            const customReason = document.getElementById('customReason').value;
            const observations = document.getElementById('observations').value;
            const adjustmentDate = document.getElementById('adjustmentDate').value;
            const responsible = document.getElementById('responsible').value;

            // Validações
            if (!produtoId || !armazemId || !adjustmentType || !quantity || quantity <= 0) {
                alert('Preencha todos os campos obrigatórios.');
                return;
            }

            if (reasonSelect === 'OUTRO' && !customReason.trim()) {
                alert('Informe o motivo personalizado.');
                return;
            }

            if (!observations.trim()) {
                alert('Informe as observações detalhadas do ajuste.');
                return;
            }

            try {
                const produto = produtos.find(p => p.id === produtoId);
                const armazem = armazens.find(a => a.id === armazemId);
                const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
                const saldoAnterior = estoque ? estoque.saldo : 0;

                let novoSaldo = 0;
                let quantidadeMovimentacao = 0;
                let tipoMovimentacao = '';

                switch(adjustmentType) {
                    case 'ENTRADA':
                        novoSaldo = saldoAnterior + quantity;
                        quantidadeMovimentacao = quantity;
                        tipoMovimentacao = 'ENTRADA';
                        break;
                    case 'SAIDA':
                        novoSaldo = saldoAnterior - quantity;
                        quantidadeMovimentacao = quantity;
                        tipoMovimentacao = 'SAIDA';
                        break;
                    case 'ACERTO':
                        novoSaldo = quantity;
                        quantidadeMovimentacao = Math.abs(quantity - saldoAnterior);
                        tipoMovimentacao = quantity > saldoAnterior ? 'ENTRADA' : 'SAIDA';
                        break;
                }

                // Confirmar ajuste se saldo ficar negativo
                if (novoSaldo < 0) {
                    const confirm = window.confirm(
                        `ATENÇÃO: O saldo ficará negativo (${novoSaldo.toFixed(3)}).\n\n` +
                        `Deseja continuar com o ajuste?`
                    );
                    if (!confirm) return;
                }

                const motivo = reasonSelect === 'OUTRO' ? customReason : getReasonText(reasonSelect);

                // Registrar ajuste
                const ajuste = {
                    produtoId: produtoId,
                    armazemId: armazemId,
                    tipo: adjustmentType,
                    quantidade: quantity,
                    saldoAnterior: saldoAnterior,
                    saldoNovo: novoSaldo,
                    motivo: motivo,
                    observacoes: observations,
                    dataAjuste: Timestamp.fromDate(new Date(adjustmentDate)),
                    responsavel: responsible,
                    usuario: currentUser.nome,
                    dataHora: Timestamp.now()
                };

                await addDoc(collection(db, "ajustesEstoque"), ajuste);

                // Atualizar ou criar estoque
                if (estoque) {
                    await updateDoc(doc(db, "estoques", estoque.id), {
                        saldo: novoSaldo,
                        ultimaMovimentacao: Timestamp.now()
                    });
                } else {
                    await addDoc(collection(db, "estoques"), {
                        produtoId: produtoId,
                        armazemId: armazemId,
                        saldo: novoSaldo,
                        ultimaMovimentacao: Timestamp.now()
                    });
                }

                // Registrar movimentação de estoque
                const numeroDoc = `AJU-${Date.now()}`;
                await addDoc(collection(db, "movimentacoesEstoque"), {
                    produtoId: produtoId,
                    tipo: tipoMovimentacao,
                    quantidade: quantidadeMovimentacao,
                    unidade: produto.unidade,
                    tipoDocumento: 'AJUSTE_ESTOQUE',
                    numeroDocumento: numeroDoc,
                    observacoes: `Ajuste: ${motivo} - ${observations}`,
                    dataHora: Timestamp.fromDate(new Date(adjustmentDate)),
                    armazemId: armazemId,
                    responsavel: responsible
                });

                alert(`Ajuste realizado com sucesso!\n\nSaldo anterior: ${saldoAnterior.toFixed(3)}\nNovo saldo: ${novoSaldo.toFixed(3)}`);
                clearForm();

            } catch (error) {
                console.error("Erro ao realizar ajuste:", error);
                alert("Erro ao realizar ajuste: " + error.message);
            }
        };

        function getReasonText(reason) {
            const reasons = {
                'IMPLANTACAO': 'Implantação do sistema',
                'INVENTARIO': 'Ajuste de inventário',
                'CORRECAO_ERRO': 'Correção de erro',
                'PERDA': 'Perda de material',
                'AVARIA': 'Material avariado',
                'DEVOLUCAO': 'Devolução de cliente'
            };
            return reasons[reason] || reason;
        }

        window.clearForm = function() {
            document.getElementById('adjustmentForm').reset();
            document.getElementById('productInfo').textContent = 'Selecione um produto';
            document.getElementById('currentBalance').textContent = 'Selecione produto e armazém';
            document.getElementById('adjustmentSummary').style.display = 'none';
            document.getElementById('customReasonCol').style.display = 'none';
            setDefaultDateTime();
        };

        async function loadHistory() {
            try {
                const filterPeriod = document.getElementById('filterPeriod')?.value || '30';
                let queryConstraints = [orderBy("dataHora", "desc")];

                // Filtro de período
                if (filterPeriod !== 'all') {
                    const daysAgo = parseInt(filterPeriod);
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - daysAgo);
                    queryConstraints.push(where("dataHora", ">=", Timestamp.fromDate(startDate)));
                }

                const ajustesSnap = await getDocs(
                    query(collection(db, "ajustesEstoque"), ...queryConstraints)
                );

                ajustes = ajustesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                displayHistory();

            } catch (error) {
                console.error("Erro ao carregar histórico:", error);
                document.getElementById('historyTableBody').innerHTML =
                    '<tr><td colspan="10" style="text-align: center; color: #d32f2f;">Erro ao carregar histórico</td></tr>';
            }
        }

        function displayHistory() {
            const tableBody = document.getElementById('historyTableBody');
            const filterProduct = document.getElementById('filterProduct')?.value.toLowerCase() || '';

            let filteredAjustes = ajustes;

            // Filtrar por produto se especificado
            if (filterProduct) {
                filteredAjustes = ajustes.filter(ajuste => {
                    const produto = produtos.find(p => p.id === ajuste.produtoId);
                    if (!produto) return false;

                    const codigo = (produto.codigo || '').toLowerCase();
                    const descricao = (produto.descricao || '').toLowerCase();

                    return codigo.includes(filterProduct) || descricao.includes(filterProduct);
                });
            }

            if (filteredAjustes.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; color: #666; padding: 40px;">
                            <div style="font-size: 18px; margin-bottom: 10px;">📋</div>
                            <div>Nenhum ajuste encontrado</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tableBody.innerHTML = '';

            filteredAjustes.forEach(ajuste => {
                const produto = produtos.find(p => p.id === ajuste.produtoId);
                const armazem = armazens.find(a => a.id === ajuste.armazemId);

                const dataAjuste = ajuste.dataAjuste ?
                    new Date(ajuste.dataAjuste.seconds * 1000).toLocaleString('pt-BR') :
                    'N/A';

                const tipoClass = ajuste.tipo === 'ENTRADA' ? 'success' :
                                ajuste.tipo === 'SAIDA' ? 'danger' : 'warning';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${dataAjuste}</td>
                    <td>
                        <strong>${produto ? produto.codigo : 'N/A'}</strong><br>
                        <small>${produto ? produto.descricao : 'Produto não encontrado'}</small>
                    </td>
                    <td>${armazem ? `${armazem.codigo} - ${armazem.nome}` : 'N/A'}</td>
                    <td>
                        <span class="badge badge-${tipoClass}">${ajuste.tipo}</span>
                    </td>
                    <td style="text-align: right;">
                        ${ajuste.quantidade.toFixed(3)} ${produto ? produto.unidade : 'UN'}
                    </td>
                    <td style="text-align: right;">
                        ${ajuste.saldoAnterior.toFixed(3)} ${produto ? produto.unidade : 'UN'}
                    </td>
                    <td style="text-align: right; font-weight: bold;">
                        ${ajuste.saldoNovo.toFixed(3)} ${produto ? produto.unidade : 'UN'}
                    </td>
                    <td>
                        <strong>${ajuste.motivo}</strong><br>
                        <small>${ajuste.observacoes}</small>
                    </td>
                    <td>${ajuste.responsavel}</td>
                    <td>
                        <button class="btn-secondary" onclick="viewAdjustmentDetails('${ajuste.id}')"
                                style="padding: 5px 10px; font-size: 12px;">
                            Ver Detalhes
                        </button>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }

        window.filterHistory = function() {
            displayHistory();
        };

        window.viewAdjustmentDetails = function(ajusteId) {
            const ajuste = ajustes.find(a => a.id === ajusteId);
            if (!ajuste) return;

            const produto = produtos.find(p => p.id === ajuste.produtoId);
            const armazem = armazens.find(a => a.id === ajuste.armazemId);

            const dataAjuste = ajuste.dataAjuste ?
                new Date(ajuste.dataAjuste.seconds * 1000).toLocaleString('pt-BR') : 'N/A';
            const dataCriacao = ajuste.dataHora ?
                new Date(ajuste.dataHora.seconds * 1000).toLocaleString('pt-BR') : 'N/A';

            alert(`DETALHES DO AJUSTE\n\n` +
                  `Produto: ${produto ? produto.codigo : 'N/A'} - ${produto ? produto.descricao : 'N/A'}\n` +
                  `Armazém: ${armazem ? armazem.codigo : 'N/A'} - ${armazem ? armazem.nome : 'N/A'}\n` +
                  `Tipo: ${ajuste.tipo}\n` +
                  `Quantidade: ${ajuste.quantidade.toFixed(3)} ${produto ? produto.unidade : 'UN'}\n` +
                  `Saldo Anterior: ${ajuste.saldoAnterior.toFixed(3)} ${produto ? produto.unidade : 'UN'}\n` +
                  `Saldo Novo: ${ajuste.saldoNovo.toFixed(3)} ${produto ? produto.unidade : 'UN'}\n` +
                  `Motivo: ${ajuste.motivo}\n` +
                  `Observações: ${ajuste.observacoes}\n` +
                  `Data do Ajuste: ${dataAjuste}\n` +
                  `Responsável: ${ajuste.responsavel}\n` +
                  `Usuário do Sistema: ${ajuste.usuario}\n` +
                  `Data de Criação: ${dataCriacao}`);
        };

    </script>

    <style>
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        /* Estilos para notificações */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-weight: 600;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        }

        .notification.success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .notification.error {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .notification.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .notification.info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>

    <script>
        // Função para mostrar notificações
        window.showNotification = function(message, type = 'info', duration = 5000) {
            // Remover notificações existentes
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => {
                notification.remove();
            });

            // Criar nova notificação
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = message;

            // Adicionar ao DOM
            document.body.appendChild(notification);

            // Remover automaticamente após o tempo especificado
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOut 0.3s ease-out';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);

            // Permitir fechar clicando na notificação
            notification.addEventListener('click', () => {
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            });
        };
    </script>
</body>
</html>