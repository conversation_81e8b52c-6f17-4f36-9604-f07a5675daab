<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow de Aprovação Avançado - TOTVS Style</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .workflow-container {
            padding: 40px;
        }

        .alcada-config {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .alcada-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alcada-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .alcada-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .alcada-item:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }

        .alcada-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .alcada-nivel {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .alcada-valor {
            font-size: 1.1rem;
            color: #27ae60;
            font-weight: bold;
        }

        .workflow-visual {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-top: 30px;
            border: 2px solid #e9ecef;
        }

        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin: 40px 0;
        }

        .workflow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .step-pending {
            background: #95a5a6;
        }

        .step-current {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            animation: pulse 2s infinite;
        }

        .step-approved {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .step-rejected {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .step-line {
            position: absolute;
            top: 30px;
            left: 0;
            right: 0;
            height: 4px;
            background: #e9ecef;
            z-index: 1;
        }

        .step-line-progress {
            height: 100%;
            background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
            transition: width 0.5s ease;
        }

        .step-label {
            text-align: center;
            font-weight: bold;
            color: #2c3e50;
        }

        .step-user {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .approval-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-approve {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-reject {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-delegate {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .justification-box {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .justification-box textarea {
            width: 100%;
            min-height: 100px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            font-family: inherit;
            resize: vertical;
        }

        .alcada-rules {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .rule-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 0.95rem;
        }

        .rule-icon {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-route"></i> Workflow de Aprovação Avançado</h1>
            <p>Sistema de Alçadas Dinâmicas - Padrão TOTVS</p>
        </div>

        <div class="workflow-container">
            <!-- Configuração de Alçadas -->
            <div class="alcada-config">
                <div class="alcada-title">
                    <i class="fas fa-cogs"></i>
                    Configuração de Alçadas por Valor
                </div>
                
                <div class="alcada-grid" id="alcadaGrid">
                    <!-- Alçadas carregadas dinamicamente -->
                </div>
            </div>

            <!-- Visualização do Workflow -->
            <div class="workflow-visual">
                <h3><i class="fas fa-project-diagram"></i> Fluxo de Aprovação Atual</h3>
                
                <div class="workflow-steps" id="workflowSteps">
                    <!-- Steps carregados dinamicamente -->
                </div>

                <div class="approval-actions" id="approvalActions">
                    <!-- Ações carregadas dinamicamente -->
                </div>

                <div class="justification-box" id="justificationBox" style="display: none;">
                    <label><strong>Justificativa:</strong></label>
                    <textarea id="justificationText" placeholder="Digite a justificativa para sua decisão..."></textarea>
                </div>
            </div>

            <!-- Regras de Alçada -->
            <div class="alcada-rules">
                <h4><i class="fas fa-info-circle"></i> Regras de Alçada</h4>
                <div class="rule-item">
                    <i class="fas fa-check rule-icon"></i>
                    <span>Até R$ 5.000: Aprovação do Supervisor</span>
                </div>
                <div class="rule-item">
                    <i class="fas fa-check rule-icon"></i>
                    <span>R$ 5.001 a R$ 25.000: Supervisor + Gerente</span>
                </div>
                <div class="rule-item">
                    <i class="fas fa-check rule-icon"></i>
                    <span>R$ 25.001 a R$ 100.000: Supervisor + Gerente + Diretor</span>
                </div>
                <div class="rule-item">
                    <i class="fas fa-check rule-icon"></i>
                    <span>Acima de R$ 100.000: Aprovação da Diretoria</span>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // ===================================================================
        // WORKFLOW APROVAÇÃO AVANÇADO - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { collection, doc, updateDoc, addDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase já inicializado centralmente

        // Configuração de Alçadas (Padrão TOTVS)
        const alcadas = [
            { nivel: 1, cargo: 'Supervisor', valorMinimo: 0, valorMaximo: 5000, usuario: '<EMAIL>' },
            { nivel: 2, cargo: 'Gerente', valorMinimo: 5001, valorMaximo: 25000, usuario: '<EMAIL>' },
            { nivel: 3, cargo: 'Diretor', valorMinimo: 25001, valorMaximo: 100000, usuario: '<EMAIL>' },
            { nivel: 4, cargo: 'Diretoria', valorMinimo: 100001, valorMaximo: 999999999, usuario: '<EMAIL>' }
        ];

        // Dados do pedido atual (exemplo)
        const pedidoAtual = {
            id: 'PC001',
            numero: 'PC20240001',
            valor: 35000,
            solicitante: 'João Silva',
            status: 'AGUARDANDO_APROVACAO',
            nivelAtual: 2,
            aprovacoes: [
                { nivel: 1, usuario: '<EMAIL>', status: 'APROVADO', data: new Date(), justificativa: 'Aprovado conforme orçamento' }
            ]
        };

        function initializeWorkflow() {
            renderAlcadas();
            renderWorkflowSteps();
            renderApprovalActions();
        }

        function renderAlcadas() {
            const grid = document.getElementById('alcadaGrid');
            grid.innerHTML = '';

            alcadas.forEach(alcada => {
                const item = document.createElement('div');
                item.className = 'alcada-item';
                
                item.innerHTML = `
                    <div class="alcada-header">
                        <div class="alcada-nivel">Nível ${alcada.nivel}</div>
                        <div class="alcada-valor">R$ ${alcada.valorMinimo.toLocaleString()} - R$ ${alcada.valorMaximo.toLocaleString()}</div>
                    </div>
                    <div style="font-weight: bold; color: #2c3e50; margin-bottom: 5px;">${alcada.cargo}</div>
                    <div style="color: #7f8c8d; font-size: 0.9rem;">${alcada.usuario}</div>
                `;
                
                grid.appendChild(item);
            });
        }

        function renderWorkflowSteps() {
            const container = document.getElementById('workflowSteps');
            const niveisNecessarios = determineRequiredLevels(pedidoAtual.valor);
            
            container.innerHTML = '<div class="step-line"><div class="step-line-progress" style="width: 25%"></div></div>';

            niveisNecessarios.forEach((nivel, index) => {
                const alcada = alcadas.find(a => a.nivel === nivel);
                const aprovacao = pedidoAtual.aprovacoes.find(a => a.nivel === nivel);
                
                let status = 'pending';
                let icon = 'fas fa-clock';
                
                if (aprovacao) {
                    status = aprovacao.status === 'APROVADO' ? 'approved' : 'rejected';
                    icon = aprovacao.status === 'APROVADO' ? 'fas fa-check' : 'fas fa-times';
                } else if (nivel === pedidoAtual.nivelAtual) {
                    status = 'current';
                    icon = 'fas fa-user-clock';
                }

                const step = document.createElement('div');
                step.className = 'workflow-step';
                step.innerHTML = `
                    <div class="step-circle step-${status}">
                        <i class="${icon}"></i>
                    </div>
                    <div class="step-label">${alcada.cargo}</div>
                    <div class="step-user">${alcada.usuario}</div>
                `;
                
                container.appendChild(step);
            });
        }

        function renderApprovalActions() {
            const container = document.getElementById('approvalActions');
            const currentUser = JSON.parse(localStorage.getItem('currentUser')) || {};
            const currentLevel = alcadas.find(a => a.nivel === pedidoAtual.nivelAtual);
            
            if (currentLevel && currentLevel.usuario === currentUser.email) {
                container.innerHTML = `
                    <button class="btn btn-approve" onclick="approveDocument()">
                        <i class="fas fa-check"></i> Aprovar
                    </button>
                    <button class="btn btn-reject" onclick="rejectDocument()">
                        <i class="fas fa-times"></i> Rejeitar
                    </button>
                    <button class="btn btn-delegate" onclick="delegateApproval()">
                        <i class="fas fa-user-plus"></i> Delegar
                    </button>
                `;
            } else {
                container.innerHTML = `
                    <div style="text-align: center; color: #7f8c8d; font-style: italic;">
                        Aguardando aprovação de: ${currentLevel ? currentLevel.cargo : 'N/A'}
                    </div>
                `;
            }
        }

        function determineRequiredLevels(valor) {
            if (valor <= 5000) return [1];
            if (valor <= 25000) return [1, 2];
            if (valor <= 100000) return [1, 2, 3];
            return [1, 2, 3, 4];
        }

        window.approveDocument = function() {
            document.getElementById('justificationBox').style.display = 'block';
            // Implementar lógica de aprovação
        };

        window.rejectDocument = function() {
            document.getElementById('justificationBox').style.display = 'block';
            // Implementar lógica de rejeição
        };

        window.delegateApproval = function() {
            // Implementar lógica de delegação
            alert('Funcionalidade de delegação será implementada');
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', initializeWorkflow);
    </script>
</body>
</html>
