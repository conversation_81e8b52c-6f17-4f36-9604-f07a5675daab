<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção de Armazém Padrão por Tipo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
            --border-color: #bdc3c7;
            --text-color: #2c3e50;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        body {
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .form-group select,
        .form-group input {
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: var(--secondary-color);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            margin: 5px;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid var(--secondary-color);
            box-shadow: var(--shadow);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .results-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .results-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--danger-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .notification.info {
            background: var(--secondary-color);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-color);
            transition: width 0.3s;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-warehouse"></i> Correção de Armazém Padrão</h1>
            <p>Ferramenta para corrigir armazéns padrão dos produtos baseado no tipo</p>
        </div>

        <div class="content">
            <!-- Seção de Análise -->
            <div class="section">
                <h3><i class="fas fa-search"></i> Análise Atual</h3>
                <button class="btn btn-primary" onclick="analisarDados()">
                    <i class="fas fa-chart-bar"></i> Analisar Dados Atuais
                </button>
                
                <div id="statsContainer" style="display: none;">
                    <div class="stats-grid" id="statsGrid"></div>
                </div>
            </div>

            <!-- Seção de Configuração -->
            <div class="section">
                <h3><i class="fas fa-cog"></i> Configuração de Correção</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Armazém para Matéria Prima (MP)</label>
                        <select id="armazemMP">
                            <option value="">Carregando armazéns...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Armazém para Produto Acabado (PA)</label>
                        <select id="armazemPA">
                            <option value="">Carregando armazéns...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Armazém para Sub-Produto (SP)</label>
                        <select id="armazemSP">
                            <option value="">Carregando armazéns...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Armazém para Serviços (SV)</label>
                        <select id="armazemSV">
                            <option value="">Carregando armazéns...</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Seção de Execução -->
            <div class="section">
                <h3><i class="fas fa-play"></i> Execução</h3>
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-warning" onclick="simularCorrecao()">
                        <i class="fas fa-eye"></i> Simular Correção
                    </button>
                    <button class="btn btn-success" onclick="executarCorrecao()" id="btnExecutar" disabled>
                        <i class="fas fa-check"></i> Executar Correção
                    </button>
                    <button class="btn btn-danger" onclick="window.location.href='index.html'">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </button>
                </div>

                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText" style="text-align: center; margin-top: 10px;"></div>
            </div>

            <!-- Seção de Resultados -->
            <div class="section" id="resultsSection" style="display: none;">
                <h3><i class="fas fa-list"></i> Resultados</h3>
                <div id="resultsContainer"></div>
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>Carregando dados...</p>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc,
            query,
            where
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let armazens = [];
        let simulationResults = [];

        // Verificar autenticação
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser) {
            window.location.href = 'login.html';
        }

        // Carregar dados iniciais
        window.onload = async function() {
            await carregarDados();
        };

        async function carregarDados() {
            try {
                showLoading(true);
                
                const [produtosSnap, armazensSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`Carregados: ${produtos.length} produtos, ${armazens.length} armazéns`);
                
                preencherSelectsArmazens();
                showNotification('Dados carregados com sucesso!', 'success');
                
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        function preencherSelectsArmazens() {
            const selects = ['armazemPA', 'armazemSP', 'armazemSV'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">Selecione um armazém...</option>';

                armazens.forEach(armazem => {
                    select.innerHTML += `
                        <option value="${armazem.id}">
                            ${armazem.codigo} - ${armazem.nome}
                        </option>
                    `;
                });
            });

            // Configurar MP como ALM01 fixo
            const armazemMP = document.getElementById('armazemMP');
            const alm01 = armazens.find(a => a.codigo === 'ALM01');
            if (alm01) {
                armazemMP.innerHTML = `<option value="${alm01.id}" selected>ALM01 - ${alm01.nome} (Fixo para MP)</option>`;
            } else {
                armazemMP.innerHTML = '<option value="">⚠️ ALM01 não encontrado - Criar primeiro!</option>';
                showNotification('Armazém ALM01 não encontrado! Crie o armazém ALM01 primeiro.', 'warning');
            }
        }

        window.analisarDados = function() {
            const stats = {
                total: produtos.length,
                comArmazem: produtos.filter(p => p.armazemPadraoId).length,
                semArmazem: produtos.filter(p => !p.armazemPadraoId).length,
                porTipo: {}
            };

            // Contar por tipo
            const tipos = ['MP', 'PA', 'SP', 'SV', 'HR', 'TA'];
            tipos.forEach(tipo => {
                const produtosTipo = produtos.filter(p => p.tipo === tipo);
                stats.porTipo[tipo] = {
                    total: produtosTipo.length,
                    comArmazem: produtosTipo.filter(p => p.armazemPadraoId).length,
                    semArmazem: produtosTipo.filter(p => !p.armazemPadraoId).length,
                    comALM01: produtosTipo.filter(p => {
                        const armazem = armazens.find(a => a.id === p.armazemPadraoId);
                        return armazem && armazem.codigo === 'ALM01';
                    }).length
                };
            });

            exibirEstatisticas(stats);
        };

        function exibirEstatisticas(stats) {
            const container = document.getElementById('statsGrid');
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">Total de Produtos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--success-color);">${stats.comArmazem}</div>
                    <div class="stat-label">Com Armazém Padrão</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--danger-color);">${stats.semArmazem}</div>
                    <div class="stat-label">Sem Armazém Padrão</div>
                </div>
            `;

            // Adicionar estatísticas por tipo
            Object.entries(stats.porTipo).forEach(([tipo, dados]) => {
                if (dados.total > 0) {
                    const tipoNome = getTipoNome(tipo);
                    container.innerHTML += `
                        <div class="stat-card">
                            <div class="stat-number" style="color: ${dados.semArmazem > 0 ? 'var(--warning-color)' : 'var(--success-color)'};">
                                ${dados.semArmazem}
                            </div>
                            <div class="stat-label">${tipoNome} sem Armazém</div>
                        </div>
                    `;

                    if (tipo === 'MP') {
                        container.innerHTML += `
                            <div class="stat-card">
                                <div class="stat-number" style="color: ${dados.comALM01 === dados.total ? 'var(--success-color)' : 'var(--warning-color)'};">
                                    ${dados.comALM01}/${dados.total}
                                </div>
                                <div class="stat-label">MP com ALM01</div>
                            </div>
                        `;
                    }
                }
            });

            document.getElementById('statsContainer').style.display = 'block';
        }

        function getTipoNome(tipo) {
            const tipos = {
                'MP': 'Matéria Prima',
                'PA': 'Produto Acabado',
                'SP': 'Sub-Produto',
                'SV': 'Serviços',
                'HR': 'Hora Máquina',
                'TA': 'Taxas'
            };
            return tipos[tipo] || tipo;
        }

        window.simularCorrecao = function() {
            const alm01 = armazens.find(a => a.codigo === 'ALM01');
            if (!alm01) {
                showNotification('Armazém ALM01 não encontrado! Crie o armazém primeiro.', 'error');
                return;
            }

            const armazemPA = document.getElementById('armazemPA').value;
            const armazemSP = document.getElementById('armazemSP').value;
            const armazemSV = document.getElementById('armazemSV').value;

            simulationResults = [];

            produtos.forEach(produto => {
                let novoArmazemId = null;
                let acao = 'Manter';
                let motivo = '';

                if (produto.tipo === 'MP') {
                    // MP sempre vai para ALM01
                    if (produto.armazemPadraoId !== alm01.id) {
                        novoArmazemId = alm01.id;
                        acao = 'Alterar';
                        motivo = `MP deve usar ALM01. Atual: ${getArmazemNome(produto.armazemPadraoId)}`;
                    } else {
                        motivo = 'Já está correto (ALM01)';
                    }
                } else if (produto.tipo === 'PA' && armazemPA) {
                    if (produto.armazemPadraoId !== armazemPA) {
                        novoArmazemId = armazemPA;
                        acao = 'Alterar';
                        motivo = `PA configurado para ${getArmazemNome(armazemPA)}`;
                    }
                } else if (produto.tipo === 'SP' && armazemSP) {
                    if (produto.armazemPadraoId !== armazemSP) {
                        novoArmazemId = armazemSP;
                        acao = 'Alterar';
                        motivo = `SP configurado para ${getArmazemNome(armazemSP)}`;
                    }
                } else if (produto.tipo === 'SV' && armazemSV) {
                    if (produto.armazemPadraoId !== armazemSV) {
                        novoArmazemId = armazemSV;
                        acao = 'Alterar';
                        motivo = `SV configurado para ${getArmazemNome(armazemSV)}`;
                    }
                } else if (!produto.armazemPadraoId) {
                    acao = 'Pendente';
                    motivo = 'Sem armazém padrão e sem configuração';
                }

                if (acao !== 'Manter' || produto.tipo === 'MP') {
                    simulationResults.push({
                        produto,
                        acao,
                        armazemAtual: produto.armazemPadraoId,
                        novoArmazem: novoArmazemId,
                        motivo
                    });
                }
            });

            exibirResultadosSimulacao();
            document.getElementById('btnExecutar').disabled = false;
        };

        function getArmazemNome(armazemId) {
            if (!armazemId) return 'Nenhum';
            const armazem = armazens.find(a => a.id === armazemId);
            return armazem ? `${armazem.codigo} - ${armazem.nome}` : 'Não encontrado';
        }

        function exibirResultadosSimulacao() {
            const container = document.getElementById('resultsContainer');

            if (simulationResults.length === 0) {
                container.innerHTML = '<p>Nenhuma alteração necessária.</p>';
                document.getElementById('resultsSection').style.display = 'block';
                return;
            }

            const alteracoes = simulationResults.filter(r => r.acao === 'Alterar').length;
            const pendentes = simulationResults.filter(r => r.acao === 'Pendente').length;

            let html = `
                <div style="margin-bottom: 20px;">
                    <h4>Simulação de Correção</h4>
                    <p><strong>${simulationResults.length}</strong> produtos serão afetados:</p>
                    <ul>
                        <li><strong>${alteracoes}</strong> produtos serão alterados</li>
                        <li><strong>${pendentes}</strong> produtos ficam pendentes</li>
                    </ul>
                </div>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Tipo</th>
                            <th>Armazém Atual</th>
                            <th>Novo Armazém</th>
                            <th>Ação</th>
                            <th>Motivo</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            simulationResults.forEach(result => {
                const statusClass = result.acao === 'Alterar' ? 'status-warning' :
                                  result.acao === 'Pendente' ? 'status-error' : 'status-success';

                html += `
                    <tr>
                        <td>${result.produto.codigo}</td>
                        <td>${result.produto.descricao.substring(0, 50)}${result.produto.descricao.length > 50 ? '...' : ''}</td>
                        <td>${result.produto.tipo}</td>
                        <td>${getArmazemNome(result.armazemAtual)}</td>
                        <td>${result.novoArmazem ? getArmazemNome(result.novoArmazem) : '-'}</td>
                        <td><span class="status-badge ${statusClass}">${result.acao}</span></td>
                        <td>${result.motivo}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
            document.getElementById('resultsSection').style.display = 'block';
        }

        window.executarCorrecao = async function() {
            if (simulationResults.length === 0) {
                showNotification('Execute a simulação primeiro!', 'warning');
                return;
            }

            const alteracoes = simulationResults.filter(r => r.acao === 'Alterar');
            if (alteracoes.length === 0) {
                showNotification('Nenhuma alteração para executar!', 'info');
                return;
            }

            const confirmacao = confirm(`Confirma a correção de ${alteracoes.length} produtos?\n\nEsta ação não pode ser desfeita!`);
            if (!confirmacao) return;

            try {
                showProgress(true);
                let processados = 0;
                let erros = 0;

                for (const result of alteracoes) {
                    try {
                        await updateDoc(doc(db, "produtos", result.produto.id), {
                            armazemPadraoId: result.novoArmazem
                        });
                        processados++;
                    } catch (error) {
                        console.error(`Erro ao atualizar produto ${result.produto.codigo}:`, error);
                        erros++;
                    }

                    // Atualizar progresso
                    const progresso = ((processados + erros) / alteracoes.length) * 100;
                    updateProgress(progresso, `Processando: ${processados + erros}/${alteracoes.length}`);
                }

                showProgress(false);

                if (erros === 0) {
                    showNotification(`✅ Correção concluída! ${processados} produtos atualizados.`, 'success');
                } else {
                    showNotification(`⚠️ Correção parcial: ${processados} atualizados, ${erros} erros.`, 'warning');
                }

                // Recarregar dados
                await carregarDados();

                // Limpar resultados
                simulationResults = [];
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('btnExecutar').disabled = true;

            } catch (error) {
                showProgress(false);
                console.error('Erro na correção:', error);
                showNotification('Erro na correção: ' + error.message, 'error');
            }
        };

        // Funções utilitárias
        function showLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('active');
            } else {
                loading.classList.remove('active');
            }
        }

        function showProgress(show) {
            const progressBar = document.getElementById('progressBar');
            if (show) {
                progressBar.style.display = 'block';
            } else {
                progressBar.style.display = 'none';
            }
        }

        function updateProgress(percentage, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressFill.style.width = percentage + '%';
            progressText.textContent = text || '';
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }
    </script>
</body>
</html>
