<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório de Estrutura</title>
  <style>
    /* (Keeping the existing styles from the original document) */
    @media print {
      body {
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 20mm;
      }
      .no-print {
        display: none;
      }
    }

    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: 210mm;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #333;
    }

    .logo {
      max-width: 150px;
      height: auto;
    }

    .report-info {
      text-align: right;
    }

    .title {
      text-align: center;
      margin: 20px 0;
      font-size: 24px;
      font-weight: bold;
    }

    .product-select {
      width: 100%;
      padding: 8px;
      margin-bottom: 20px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .structure-tree {
      margin-top: 20px;
    }

    .component {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .level-0 { margin-left: 0; }
    .level-1 { margin-left: 20px; }
    .level-2 { margin-left: 40px; }
    .level-3 { margin-left: 60px; }
    .level-4 { margin-left: 80px; }

    .operations-list {
      margin-top: 10px;
      padding-left: 20px;
    }

    .operation-item {
      margin: 5px 0;
      font-size: 0.9em;
      color: #666;
    }

    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px;
    }

    button:hover {
      background-color: #45a049;
    }

    .back-button {
      background-color: #6c757d;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="no-print">
      <select id="productSelect" class="product-select">
        <option value="">Selecione o produto...</option>
      </select>
      <button onclick="generateReport()">Gerar Relatório</button>
      <button onclick="window.print()">Imprimir</button>
      <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
    </div>

    <div id="reportContent">
      <!-- Report content will be generated here -->
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      query, 
      where 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let operacoes = [];
    let recursos = [];

    window.onload = async function() {
      await loadData();
      updateProductSelect();
    };

    async function loadData() {
      try {
        const [produtosSnap, estruturasSnap, operacoesSnap, recursosSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "operacoes")),
          getDocs(collection(db, "recursos"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        operacoes = operacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        recursos = recursosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados. Por favor, recarregue a página.");
      }
    }

    function updateProductSelect() {
      const select = document.getElementById('productSelect');
      select.innerHTML = '<option value="">Selecione o produto...</option>';
      
      produtos
        .filter(p => p.tipo === 'PA' || p.tipo === 'SP')
        .forEach(produto => {
          select.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
        });
    }

    window.generateReport = async function() {
      const produtoId = document.getElementById('productSelect').value;
      if (!produtoId) {
        alert('Por favor, selecione um produto.');
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!estrutura) {
        alert('Este produto não possui estrutura cadastrada.');
        return;
      }

      const reportContent = document.getElementById('reportContent');
      reportContent.innerHTML = `
        <div class="header">
          <img src="https://www.naliteck.com.br/img/logo.png" alt="Logo" class="logo">
          <div class="report-info">
            <div>Data: ${new Date().toLocaleDateString()}</div>
            <div>Hora: ${new Date().toLocaleTimeString()}</div>
          </div>
        </div>
        <div class="title">Relatório de Estrutura de Produto</div>
        <div class="product-info">
          <h2>Produto: ${produto.codigo} - ${produto.descricao}</h2>
          <p>Tipo: ${produto.tipo}</p>
          <p>Unidade: ${produto.unidade}</p>
        </div>
        <div class="structure-tree">
          ${await generateStructureTree(produto, estrutura, 0)}
        </div>
      `;
    };

    async function generateStructureTree(produto, estrutura, level) {
      if (!estrutura) return '';

      let html = `
        <div class="component level-${level}">
          <strong>${produto.codigo} - ${produto.descricao}</strong>
          <div>Tipo: ${produto.tipo} | Unidade: ${produto.unidade}</div>
      `;

      if (estrutura.operacoes && estrutura.operacoes.length > 0) {
        html += '<div class="operations-list"><strong>Operações:</strong>';
        for (const op of estrutura.operacoes) {
          const operacao = operacoes.find(o => o.id === op.operacaoId);
          const recurso = recursos.find(r => r.id === op.recursoId);
          if (operacao && recurso) {
            html += `
              <div class="operation-item">
                ${op.sequencia}. ${operacao.operacao} - 
                Recurso: ${recurso.codigo} - ${recurso.maquina} |
                Tempo: ${op.tempo} min
              </div>
            `;
          }
        }
        html += '</div>';
      }

      if (estrutura.componentes && estrutura.componentes.length > 0) {
        for (const comp of estrutura.componentes) {
          const componenteProduto = produtos.find(p => p.id === comp.componentId);
          const componenteEstrutura = estruturas.find(e => e.produtoPaiId === comp.componentId);
          
          html += `
            <div class="component level-${level + 1}">
              <div>→ ${componenteProduto.codigo} - ${componenteProduto.descricao}</div>
              <div>Quantidade: ${comp.quantidade} ${comp.unidade}</div>
          `;

          if (componenteEstrutura) {
            html += await generateStructureTree(componenteProduto, componenteEstrutura, level + 1);
          }

          html += '</div>';
        }
      }

      html += '</div>';
      return html;
    }
  </script>
</body>
</html>