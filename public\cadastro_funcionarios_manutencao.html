<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cadastro Completo de Funcionários - Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header h1::before {
      content: '👷‍♀️';
      font-size: 32px;
    }

    .main-content {
      padding: 30px;
    }

    .form-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
      margin-bottom: 30px;
    }

    .form-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 25px;
      color: #2c3e50;
      border-bottom: 3px solid #ecf0f1;
      padding-bottom: 15px;
    }

    .form-section {
      margin-bottom: 25px;
    }

    .section-header {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 15px 20px;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .section-body {
      padding: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      margin-bottom: 8px;
      color: #2c3e50;
      font-weight: 600;
      font-size: 14px;
    }

    .required::after {
      content: "*";
      color: #e74c3c;
      margin-left: 4px;
    }

    input, select, textarea {
      padding: 12px;
      border: 2px solid #bdc3c7;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
      color: white;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 10px;
    }

    .tag {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .tag-remove {
      cursor: pointer;
      font-weight: bold;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: rgba(255,255,255,0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }

    .tag-remove:hover {
      background: rgba(255,255,255,0.3);
    }

    .input-with-button {
      display: flex;
      gap: 10px;
      align-items: end;
    }

    .input-with-button input,
    .input-with-button select {
      flex: 1;
    }

    .input-with-button button {
      flex-shrink: 0;
    }

    .form-actions {
      text-align: right;
      padding: 20px;
      border-top: 1px solid #e9ecef;
      background: #f8f9fa;
      margin-top: 30px;
    }

    .info-text {
      font-size: 12px;
      color: #7f8c8d;
      margin-top: 5px;
      font-style: italic;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 1000;
      display: none;
      animation: slideIn 0.3s ease;
    }

    .notification-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    }

    .notification-error {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    .notification-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid;
      font-weight: 500;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: #17a2b8;
    }

    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .input-with-button {
        flex-direction: column;
        align-items: stretch;
      }

      .form-actions {
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Cadastro Completo de Funcionários da Manutenção</h1>
      <div>
        <button class="btn btn-secondary" onclick="window.location.href='funcionarios_manutencao.html'">
          <i class="fas fa-list"></i> Lista Simples
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <div id="notification" class="notification"></div>

      <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Cadastro Completo de Funcionários:</strong>
        Sistema avançado de cadastro com dados pessoais, profissionais, especialidades e certificações.
        Use esta tela para registros detalhados ou a "Lista Simples" para cadastros rápidos.
      </div>

      <div class="form-container">
        <form id="funcionarioForm">
          <input type="hidden" id="editingId">

          <!-- Dados Pessoais -->
          <div class="form-section">
            <div class="section-header">
              <i class="fas fa-user"></i> Dados Pessoais
            </div>
            <div class="section-body">
              <div class="form-row">
                <div class="form-col">
                  <label for="nome" class="required">Nome Completo</label>
                  <input type="text" id="nome" name="nome" required>
                </div>
                <div class="form-col">
                  <label for="cpf" class="required">CPF</label>
                  <input type="text" id="cpf" name="cpf" required maxlength="14" placeholder="000.000.000-00">
                </div>
                <div class="form-col">
                  <label for="rg">RG</label>
                  <input type="text" id="rg" name="rg">
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-col">
                  <label for="telefone" class="required">Telefone</label>
                  <input type="tel" id="telefone" name="telefone" required placeholder="(00) 00000-0000">
                </div>
                <div class="form-col">
                  <label for="email">Email</label>
                  <input type="email" id="email" name="email" placeholder="<EMAIL>">
                </div>
                <div class="form-col">
                  <label for="dataNascimento">Data de Nascimento</label>
                  <input type="date" id="dataNascimento" name="dataNascimento">
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="endereco">Endereço Completo</label>
                  <textarea id="endereco" name="endereco" rows="2" placeholder="Rua, número, bairro, cidade, CEP"></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Dados Profissionais -->
          <div class="form-section">
            <div class="section-header">
              <i class="fas fa-briefcase"></i> Dados Profissionais
            </div>
            <div class="section-body">
              <div class="form-row">
                <div class="form-col">
                  <label for="matricula" class="required">Matrícula</label>
                  <input type="text" id="matricula" name="matricula" required>
                  <div class="info-text">Código único do funcionário no sistema</div>
                </div>
                <div class="form-col">
                  <label for="cargo" class="required">Cargo</label>
                  <select id="cargo" name="cargo" required>
                    <option value="">Selecione o cargo...</option>
                    <option value="TECNICO_ELETRICA">Técnico em Elétrica</option>
                    <option value="TECNICO_MECANICA">Técnico em Mecânica</option>
                    <option value="TECNICO_HIDRAULICA">Técnico em Hidráulica</option>
                    <option value="TECNICO_PNEUMATICA">Técnico em Pneumática</option>
                    <option value="TECNICO_ELETRONICA">Técnico em Eletrônica</option>
                    <option value="SOLDADOR">Soldador</option>
                    <option value="TORNEIRO">Torneiro Mecânico</option>
                    <option value="AUXILIAR">Auxiliar de Manutenção</option>
                    <option value="SUPERVISOR">Supervisor de Manutenção</option>
                    <option value="COORDENADOR">Coordenador de Manutenção</option>
                    <option value="GERENTE">Gerente de Manutenção</option>
                  </select>
                </div>
                <div class="form-col">
                  <label for="setor" class="required">Setor</label>
                  <select id="setor" name="setor" required>
                    <option value="">Selecione o setor...</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
                  <label for="turno" class="required">Turno de Trabalho</label>
                  <select id="turno" name="turno" required>
                    <option value="">Selecione o turno...</option>
                    <option value="MANHA">Manhã (06:00 - 14:00)</option>
                    <option value="TARDE">Tarde (14:00 - 22:00)</option>
                    <option value="NOITE">Noite (22:00 - 06:00)</option>
                    <option value="ADMINISTRATIVO">Administrativo (08:00 - 17:00)</option>
                    <option value="SOBREAVISO">Sobreaviso</option>
                  </select>
                </div>
                <div class="form-col">
                  <label for="custoHora">Custo por Hora (R$)</label>
                  <input type="number" id="custoHora" name="custoHora" min="0" step="0.01" placeholder="0,00">
                  <div class="info-text">Para cálculo de custos de manutenção</div>
                </div>
                <div class="form-col">
                  <label for="dataAdmissao">Data de Admissão</label>
                  <input type="date" id="dataAdmissao" name="dataAdmissao">
                </div>
              </div>
            </div>
          </div>

          <!-- Especialidades -->
          <div class="form-section">
            <div class="section-header">
              <i class="fas fa-tools"></i> Especialidades e Competências
            </div>
            <div class="section-body">
              <div class="form-row">
                <div class="form-col">
                  <label for="especialidadeInput">Adicionar Especialidade</label>
                  <div class="input-with-button">
                    <select id="especialidadeInput">
                      <option value="">Selecione uma especialidade...</option>
                      <option value="MANUTENCAO_ELETRICA">Manutenção Elétrica</option>
                      <option value="MANUTENCAO_MECANICA">Manutenção Mecânica</option>
                      <option value="MANUTENCAO_HIDRAULICA">Manutenção Hidráulica</option>
                      <option value="SOLDAGEM_MIG">Soldagem MIG</option>
                      <option value="SOLDAGEM_TIG">Soldagem TIG</option>
                      <option value="SOLDAGEM_ELETRODO">Soldagem Eletrodo</option>
                      <option value="TORNO_MECANICO">Torno Mecânico</option>
                      <option value="FRESADORA">Fresadora</option>
                      <option value="PNEUMATICA">Sistemas Pneumáticos</option>
                      <option value="ELETRONICA_INDUSTRIAL">Eletrônica Industrial</option>
                      <option value="CLP">Controladores Lógicos (CLP)</option>
                      <option value="IHM">Interface Homem-Máquina</option>
                      <option value="INVERSORES">Inversores de Frequência</option>
                      <option value="INSTRUMENTACAO">Instrumentação</option>
                    </select>
                    <button type="button" class="btn btn-primary" onclick="adicionarEspecialidade()">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                  <div id="especialidadesContainer" class="tags-container"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Certificações -->
          <div class="form-section">
            <div class="section-header">
              <i class="fas fa-certificate"></i> Certificações e Cursos
            </div>
            <div class="section-body">
              <div class="form-row">
                <div class="form-col">
                  <label for="certificacaoInput">Adicionar Certificação</label>
                  <div class="input-with-button">
                    <input type="text" id="certificacaoInput" placeholder="Ex: NR-10, NR-12, Soldagem AWS...">
                    <button type="button" class="btn btn-primary" onclick="adicionarCertificacao()">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                  <div id="certificacoesContainer" class="tags-container"></div>
                  <div class="info-text">Digite o nome da certificação e clique em adicionar</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Observações -->
          <div class="form-section">
            <div class="section-header">
              <i class="fas fa-comment"></i> Informações Adicionais
            </div>
            <div class="section-body">
              <div class="form-row">
                <div class="form-col">
                  <label for="observacoes">Observações</label>
                  <textarea id="observacoes" name="observacoes" rows="4" placeholder="Informações adicionais sobre o funcionário..."></textarea>
                </div>
              </div>
              <div class="form-row">
                <div class="form-col">
                  <label for="status" class="required">Status</label>
                  <select id="status" name="status" required>
                    <option value="ATIVO">Ativo</option>
                    <option value="INATIVO">Inativo</option>
                    <option value="AFASTADO">Afastado</option>
                    <option value="FERIAS">Férias</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="limparFormulario()">
              <i class="fas fa-times"></i> Limpar
            </button>
            <button type="submit" class="btn btn-success" id="submitButton">
              <i class="fas fa-save"></i> Salvar Funcionário
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, addDoc, getDocs, serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let setoresList = [];
    let especialidadesSelecionadas = [];
    let certificacoesSelecionadas = [];

    // Carregar setores
    async function loadSetores() {
      try {
        const setoresRef = collection(db, "setores");
        const querySnapshot = await getDocs(setoresRef);
        
        setoresList = querySnapshot.docs.map(doc => ({
          id: doc.id, ...doc.data()
        }));

        const select = document.getElementById('setor');
        select.innerHTML = '<option value="">Selecione o setor...</option>' +
          setoresList.map(setor => 
            `<option value="${setor.id}">${setor.nome}</option>`
          ).join('');
      } catch (error) {
        console.error("Erro ao carregar setores:", error);
        showNotification("Erro ao carregar setores", "error");
      }
    }

    // Funções para gerenciar especialidades
    window.adicionarEspecialidade = function() {
      const select = document.getElementById('especialidadeInput');
      const valor = select.value;
      
      if (valor && !especialidadesSelecionadas.includes(valor)) {
        especialidadesSelecionadas.push(valor);
        atualizarEspecialidadesView();
        select.value = '';
      } else if (especialidadesSelecionadas.includes(valor)) {
        showNotification("Especialidade já adicionada", "warning");
      }
    };

    // Funções para gerenciar certificações
    window.adicionarCertificacao = function() {
      const input = document.getElementById('certificacaoInput');
      const valor = input.value.trim();
      
      if (valor && !certificacoesSelecionadas.includes(valor)) {
        certificacoesSelecionadas.push(valor);
        atualizarCertificacoesView();
        input.value = '';
      } else if (certificacoesSelecionadas.includes(valor)) {
        showNotification("Certificação já adicionada", "warning");
      }
    };

    function atualizarEspecialidadesView() {
      const container = document.getElementById('especialidadesContainer');
      container.innerHTML = especialidadesSelecionadas.map(esp => `
        <div class="tag">
          ${getEspecialidadeLabel(esp)}
          <span class="tag-remove" onclick="removerEspecialidade('${esp}')">&times;</span>
        </div>
      `).join('');
    }

    function atualizarCertificacoesView() {
      const container = document.getElementById('certificacoesContainer');
      container.innerHTML = certificacoesSelecionadas.map(cert => `
        <div class="tag">
          ${cert}
          <span class="tag-remove" onclick="removerCertificacao('${cert}')">&times;</span>
        </div>
      `).join('');
    }

    window.removerEspecialidade = function(especialidade) {
      const index = especialidadesSelecionadas.indexOf(especialidade);
      if (index > -1) {
        especialidadesSelecionadas.splice(index, 1);
        atualizarEspecialidadesView();
      }
    };

    window.removerCertificacao = function(certificacao) {
      const index = certificacoesSelecionadas.indexOf(certificacao);
      if (index > -1) {
        certificacoesSelecionadas.splice(index, 1);
        atualizarCertificacoesView();
      }
    };

    function getEspecialidadeLabel(especialidade) {
      const especialidades = {
        'MANUTENCAO_ELETRICA': 'Manutenção Elétrica',
        'MANUTENCAO_MECANICA': 'Manutenção Mecânica',
        'MANUTENCAO_HIDRAULICA': 'Manutenção Hidráulica',
        'SOLDAGEM_MIG': 'Soldagem MIG',
        'SOLDAGEM_TIG': 'Soldagem TIG',
        'SOLDAGEM_ELETRODO': 'Soldagem Eletrodo',
        'TORNO_MECANICO': 'Torno Mecânico',
        'FRESADORA': 'Fresadora',
        'PNEUMATICA': 'Pneumática',
        'ELETRONICA_INDUSTRIAL': 'Eletrônica Industrial',
        'CLP': 'CLP',
        'IHM': 'IHM',
        'INVERSORES': 'Inversores',
        'INSTRUMENTACAO': 'Instrumentação'
      };
      return especialidades[especialidade] || especialidade;
    }

    // Salvar funcionário
    async function salvarFuncionario(event) {
      event.preventDefault();

      try {
        const formData = new FormData(event.target);

        const funcionarioData = {
          nome: formData.get('nome'),
          cpf: formData.get('cpf'),
          rg: formData.get('rg'),
          telefone: formData.get('telefone'),
          email: formData.get('email'),
          dataNascimento: formData.get('dataNascimento') ? new Date(formData.get('dataNascimento')) : null,
          endereco: formData.get('endereco'),
          matricula: formData.get('matricula'),
          cargo: formData.get('cargo'),
          setor: formData.get('setor'),
          turno: formData.get('turno'),
          custoHora: parseFloat(formData.get('custoHora')) || 0,
          dataAdmissao: formData.get('dataAdmissao') ? new Date(formData.get('dataAdmissao')) : null,
          especialidades: especialidadesSelecionadas,
          certificacoes: certificacoesSelecionadas,
          observacoes: formData.get('observacoes'),
          status: formData.get('status'),
          dataCadastro: serverTimestamp()
        };

        await addDoc(collection(db, "funcionariosManutencao"), funcionarioData);
        showNotification("Funcionário cadastrado com sucesso!", "success");
        limparFormulario();
      } catch (error) {
        console.error("Erro ao salvar funcionário:", error);
        showNotification("Erro ao salvar funcionário", "error");
      }
    }

    window.limparFormulario = function() {
      document.getElementById('funcionarioForm').reset();
      especialidadesSelecionadas = [];
      certificacoesSelecionadas = [];
      atualizarEspecialidadesView();
      atualizarCertificacoesView();
    };

    function showNotification(message, type) {
      const notification = document.getElementById('notification');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      notification.style.display = 'block';

      setTimeout(() => {
        notification.style.display = 'none';
      }, 3000);
    }

    // Máscaras para CPF e telefone
    function applyCPFMask(value) {
      return value
        .replace(/\D/g, '')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d{1,2})/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1');
    }

    function applyPhoneMask(value) {
      return value
        .replace(/\D/g, '')
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4,5})(\d{4})/, '$1-$2')
        .replace(/(-\d{4})\d+?$/, '$1');
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', () => {
      loadSetores();
      document.getElementById('funcionarioForm').addEventListener('submit', salvarFuncionario);

      // Aplicar máscaras
      document.getElementById('cpf').addEventListener('input', (e) => {
        e.target.value = applyCPFMask(e.target.value);
      });

      document.getElementById('telefone').addEventListener('input', (e) => {
        e.target.value = applyPhoneMask(e.target.value);
      });
    });
  </script>
</body>
</html> 