<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Solicitação de Compras</title>
    <style>
      :root {
        --primary-color: #0854a0;
        --primary-hover: #0a4d8c;
        --secondary-color: #f0f3f6;
        --border-color: #d4d4d4;
        --text-color: #333;
        --text-secondary: #666;
        --success-color: #107e3e;
        --success-hover: #0d6e36;
        --danger-color: #bb0000;
        --danger-hover: #a30000;
        --warning-color: #e9730c;
        --header-bg: #354a5f;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f7f7f7;
        color: var(--text-color);
      }

      .container {
        width: 95%;
        max-width: 1200px;
        margin: 30px auto;
        padding: 0;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .header {
        background-color: var(--header-bg);
        color: white;
        padding: 15px 20px;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .header h1 {
        font-size: 24px;
        font-weight: 500;
        margin: 0;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap; /* Added for responsiveness */
      }

      .form-col {
        flex: 1;
        min-width: 200px; /* Prevent fields from shrinking too much */
      }

      label {
        display: block;
        margin-bottom: 5px;
        color: var(--text-secondary);
        font-weight: 500;
        font-size: 14px;
      }

      input,
      select,
      textarea {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
      }

      textarea {
        resize: vertical;
        min-height: 80px;
      }

      button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: background-color 0.2s;
      }

      .btn-primary {
        background-color: var(--primary-color);
        color: white;
      }

      .btn-primary:hover {
        background-color: var(--primary-hover);
      }

      .btn-success {
        background-color: var(--success-color);
        color: white;
      }

      .btn-success:hover {
        background-color: var(--success-hover);
      }

      .btn-danger {
        background-color: var(--danger-color);
        color: white;
      }

      .btn-danger:hover {
        background-color: var(--danger-hover);
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background-color: #5a6268;
      }

      .requests-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }

      .requests-table th,
      .requests-table td {
        padding: 12px 15px;
        border: 1px solid var(--border-color);
        text-align: left;
      }

      .requests-table th {
        background-color: var(--secondary-color);
        font-weight: 600;
        color: var(--text-secondary);
      }

      .requests-table tr:hover {
        background-color: #f8f9fa;
      }

      .status-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .status-pendente {
        background-color: #f0ad4e;
        color: white;
      }

      .status-aprovada {
        background-color: #5bc0de;
        color: white;
      }

      .status-em_cotacao {
        background-color: #428bca;
        color: white;
      }

      .status-cotado {
        background-color: #5cb85c;
        color: white;
      }

      .status-finalizada {
        background-color: #337ab7;
        color: white;
      }

      .status-rejeitada {
        background-color: #d9534f;
        color: white;
      }

      .status-cancelada {
        background-color: #777;
        color: white;
      }

      .status-recebida {
        background-color: #4cae4c;
        color: white;
      }

      /* Estilos para prioridades */
      .status-normal {
        background-color: #5bc0de;
        color: white;
      }

      .status-urgente {
        background-color: #f0ad4e;
        color: white;
      }

      .status-critica {
        background-color: #d9534f;
        color: white;
      }

      /* Estilos para tipos */
      .tipo-normal {
        border-left: 4px solid #5bc0de;
      }

      .tipo-planejada {
        border-left: 4px solid #5cb85c;
      }

      .tipo-emergencial {
        border-left: 4px solid #d9534f;
      }

      /* Estilos para origem */
      .origem-manual {
        font-style: italic;
      }

      .origem-mrp {
        font-weight: bold;
      }

      .action-icon {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 0 2px;
        font-size: 16px;
      }

      .view-icon {
        background-color: #0854a0;
        color: white;
      }
      .print-icon {
        background-color: #6c757d;
        color: white;
      }
      .approve-icon {
        background-color: #28a745;
        color: white;
      }
      .reject-icon {
        background-color: #dc3545;
        color: white;
      }
      .edit-icon {
        background-color: #ffc107;
        color: black;
      }
      .quote-icon {
        background-color: #17a2b8;
        color: white;
      }

      .status-pendente {
        background-color: #ffc107;
        color: black;
      }
      .status-aprovada {
        background-color: #28a745;
        color: white;
      }
      .status-rejeitada {
        background-color: #dc3545;
        color: white;
      }
      .status-cotacao {
        background-color: #17a2b8;
        color: white;
      }

      .filter-row th {
        padding: 5px;
      }

      .filter-input {
        width: 100%;
        padding: 4px;
        border: 1px solid #ddd;
        border-radius: 4px;
        outline: none; /* Remove default focus */
      }

      .sort-header {
        cursor: pointer;
      }

      .sort-header:hover {
        background-color: #f0f0f0;
      }

      .status-container {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .status-indicator {
        font-size: 12px;
      }

      .status-indicator.critical {
        color: #dc3545;
      }

      .status-indicator.warning {
        color: #ffc107;
      }

      .status-indicator.attention {
        color: #17a2b8;
      }

      .status-indicator.normal {
        color: #28a745;
      }

      .status-pendente {
        background-color: #ffc107;
        color: #000;
      }
      .status-aprovada {
        background-color: #28a745;
        color: #fff;
      }
      .status-rejeitada {
        background-color: #dc3545;
        color: #fff;
      }
      .status-cotacao {
        background-color: #17a2b8;
        color: #fff;
      }

      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
      }

      .modal-content {
        background-color: white;
        margin: 1% auto;
        padding: 0;
        width: 95%;
        max-width: 1200px;
        border-radius: 8px;
        position: relative;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        max-height: 95vh;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--border-color);
        overflow-y: auto;
      }

      /* Personalização da barra de rolagem */
      .modal-content::-webkit-scrollbar {
        width: 10px;
      }

      .modal-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 5px;
      }

      .modal-content::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 5px;
      }

      .modal-content::-webkit-scrollbar-thumb:hover {
        background: #555;
      }

      .item-descricao {
        min-width: 300px;
        width: 100%;
      }

      .modal-body {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 20px;
      }

      .modal-footer {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 10px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid var(--border-color);
        gap: 10px;
      }

      .close-button {
        position: absolute;
        right: 10px;
        top: 10px;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-secondary);
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }

      .items-table th,
      .items-table td {
        padding: 8px;
        border: 1px solid var(--border-color);
        text-align: left;
      }

      .items-table th {
        background-color: var(--secondary-color);
        font-weight: 600;
        color: var(--text-secondary);
      }

      .history-section {
        margin-top: 20px;
        padding: 15px;
        background-color: var(--secondary-color);
        border-radius: 4px;
        border: 1px solid var(--border-color);
      }

      .history-item {
        padding: 10px;
        border-bottom: 1px solid var(--border-color);
      }

      .history-item:last-child {
        border-bottom: none;
      }

      .edit-warning {
        background-color: #fff3cd;
        color: #856404;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
        border: 1px solid #ffeeba;
      }

      .access-denied {
        text-align: center;
        padding: 50px 20px;
        background-color: #f8d7da;
        color: #721c24;
        border-radius: 8px;
        margin: 50px auto;
        max-width: 600px;
        border: 1px solid #f5c6cb;
      }

      .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
        display: none;
        border: 1px solid #f5c6cb;
      }

      .conversion-info {
        font-size: 12px;
        color: var(--primary-color);
        margin-top: 5px;
      }

      .required::after {
        content: "*";
        color: var(--danger-color);
        margin-left: 4px;
      }

      .action-buttons {
        display: flex;
        gap: 5px;
      }

      .action-buttons button {
        padding: 5px 10px;
        font-size: 12px;
      }

      .view-button {
        background-color: var(--primary-color);
        color: white;
      }

      .print-button {
        background-color: #6c757d;
        color: white;
      }

      .approve-button {
        background-color: var(--success-color);
        color: white;
      }

      .reject-button {
        background-color: var(--danger-color);
        color: white;
      }

      .edit-button {
        background-color: #ffc107;
        color: #000;
      }

      .create-quotation-button {
        background-color: #17a2b8;
        color: white;
      }
      #linkedItemsModal input[type="search"] {
        margin-bottom: 10px;
        padding: 5px;
      }
      .item-checkbox {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }

      /* Estilos para badges */
      .badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
      }

      .badge-info {
        background-color: #17a2b8;
        color: white;
      }

      .badge-secondary {
        background-color: #6c757d;
        color: white;
      }

      /* Estilos para tipos de solicitação */
      .tipo-normal {
        color: #0854a0;
      }

      .tipo-planejada {
        color: #107e3e;
      }

      .tipo-emergencial {
        color: #bb0000;
      }

      /* Estilos para solicitações do MRP */
      .mrp-request {
        background-color: rgba(23, 162, 184, 0.05);
      }

      .mrp-request:hover {
        background-color: rgba(23, 162, 184, 0.1);
      }

      /* Estilos para o modal de detalhes */
      .detail-section {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
      }

      .detail-section h3 {
        color: var(--primary-color);
        font-size: 16px;
        margin-bottom: 10px;
      }

      .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
      }

      .detail-item {
        margin-bottom: 10px;
      }

      .detail-label {
        font-weight: 500;
        color: var(--text-secondary);
        font-size: 12px;
      }

      .detail-value {
        font-size: 14px;
        color: var(--text-color);
      }

      /* Estilos para histórico */
      .history-item {
        padding: 10px;
        border-left: 3px solid var(--primary-color);
        margin-bottom: 10px;
        background-color: var(--secondary-color);
      }

      .history-date {
        font-size: 12px;
        color: var(--text-secondary);
      }

      .history-user {
        font-weight: 500;
        color: var(--primary-color);
      }

      .history-action {
        margin-top: 5px;
      }

      /* Estilos para informações do MRP */
      .mrp-info {
        background-color: rgba(23, 162, 184, 0.1);
        padding: 15px;
        border-radius: 4px;
        margin-top: 10px;
      }

      .mrp-info h4 {
        color: #17a2b8;
        font-size: 14px;
        margin-bottom: 10px;
      }

      .mrp-info ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .mrp-info li {
        font-size: 13px;
        margin-bottom: 5px;
      }

      /* Estilos para ações em lote */
      .batch-actions {
        margin-bottom: 20px;
        padding: 10px;
        background-color: var(--secondary-color);
        border-radius: 4px;
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .batch-actions select {
        max-width: 200px;
      }

      /* Ajustes para a tabela de itens dentro do modal de detalhes */
      #detailsModal .items-table td {
        white-space: normal; /* Permitir quebra de linha */
        word-break: break-word; /* Quebrar palavras longas */
      }

      #detailsModal .items-table td:nth-child(1) { /* Código */
        min-width: 80px;
      }

      #detailsModal .items-table td:nth-child(2) { /* Descrição */
        min-width: 200px;
      }

      #detailsModal .items-table td:nth-child(3) { /* Quantidade */
        min-width: 100px;
      }

      #detailsModal .items-table td:nth-child(4) { /* Unidade */
        min-width: 50px;
      }

      #detailsModal .items-table td:nth-child(5) { /* Valor Unit. */
        min-width: 100px;
      }

      #detailsModal .items-table td:nth-child(6) { /* Total */
        min-width: 100px;
      }

      .filter-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .filter-section h3 {
        margin-bottom: 15px;
        color: #2c3e50;
        font-size: 1.2em;
      }

      .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .filter-item {
        display: flex;
        flex-direction: column;
      }

      .filter-item label {
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
      }

      .filter-item input,
      .filter-item select {
        padding: 8px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
      }

      .filter-item input:focus,
      .filter-item select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
      }

      .filter-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }

      .btn-primary {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 5px;
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 5px;
      }

      .btn-primary:hover {
        background-color: #0056b3;
      }

      .btn-secondary:hover {
        background-color: #5a6268;
      }

      .btn-icon {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 5px;
      }

      .btn-icon:hover {
        color: #007bff;
      }

      .btn-icon:disabled {
        color: #ccc;
        cursor: not-allowed;
      }
    </style>
    <style>
      .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    display: none;
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  .notification-success {
    background-color: #4CAF50;
  }

  .notification-error {
    background-color: #f44336;
  }

  .notification-warning {
    background-color: #ff9800;
  }
</style>
<script>
function showNotification(message, type = 'success', duration = 3000) {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;
  document.body.appendChild(notification);

  // Show notification
  notification.style.display = 'block';

  // Hide after duration
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, duration);
}
</script>
  </head>
  <body>
    <div id="mainContainer" class="container">
      <div class="header">
        <h1>Solicitação de Compras</h1>
        <div>
          <button
            id="newRequestBtn"
            class="btn-primary"
            onclick="openNewRequestModal()"
          >
            Nova Solicitação
          </button>
          <button
            class="btn-secondary"
            onclick="exportSolicitacoes()"
          >
            Exportar Relatório
          </button>
          <button
            class="btn-secondary"
            onclick="window.location.href='index.html'"
          >
            Voltar
          </button>
        </div>
      </div>

      <div class="filter-section">
        <h3>Filtros</h3>
        <div class="filter-grid">
            <div class="filter-item">
                <label for="filtroNumero">Número</label>
                <input type="text" id="filtroNumero" placeholder="Número da SC">
            </div>
            <div class="filter-item">
                <label for="filtroStatus">Status</label>
                <select id="filtroStatus">
                    <option value="">Todos</option>
                    <option value="PENDENTE">Pendente</option>
                    <option value="APROVADA">Aprovada</option>
                    <option value="REJEITADA">Rejeitada</option>
                    <option value="EM_COTACAO">Em Cotação</option>
                    <option value="COTADO">Cotado</option>
                    <option value="CANCELADA">Cancelada</option>
                    <option value="RECEBIDA">Recebida</option>
                    <option value="FINALIZADA">Finalizada</option>
                </select>
            </div>
            <div class="filter-item">
                <label for="filtroSolicitante">Solicitante</label>
                <input type="text" id="filtroSolicitante" placeholder="Nome do solicitante">
            </div>
            <div class="filter-item">
                <label for="filtroDepartamento">Departamento</label>
                <select id="filtroDepartamento">
                    <option value="">Todos</option>
                    <option value="ENGENHARIA">Engenharia</option>
                    <option value="PRODUCAO">Produção</option>
                    <option value="MANUTENCAO">Manutenção</option>
                    <option value="QUALIDADE">Qualidade</option>
                    <option value="ADMINISTRATIVO">Administrativo</option>
                </select>
            </div>
            <div class="filter-item">
                <label for="filtroPrioridade">Prioridade</label>
                <select id="filtroPrioridade">
                    <option value="">Todas</option>
                    <option value="NORMAL">Normal</option>
                    <option value="URGENTE">Urgente</option>
                    <option value="CRITICA">Crítica</option>
                </select>
            </div>
            <div class="filter-item">
                <label for="filtroTipo">Tipo</label>
                <select id="filtroTipo">
                    <option value="">Todos</option>
                    <option value="NORMAL">Normal</option>
                    <option value="PLANEJADA">Planejada</option>
                    <option value="EMERGENCIAL">Emergencial</option>
                </select>
            </div>
            <div class="filter-item">
                <label for="filtroOrigem">Origem</label>
                <select id="filtroOrigem">
                    <option value="">Todas</option>
                    <option value="MANUAL">Manual</option>
                    <option value="MRP">MRP</option>
                </select>
            </div>
            <div class="filter-item">
                <label for="filtroDataInicial">Data Inicial</label>
                <input type="date" id="filtroDataInicial">
            </div>
            <div class="filter-item">
                <label for="filtroDataFinal">Data Final</label>
                <input type="date" id="filtroDataFinal">
            </div>
        </div>
        <div class="filter-actions">
            <button onclick="filtrarSolicitacoes()" class="btn-primary">
                <i class="fas fa-search"></i> Filtrar
            </button>
            <button onclick="limparFiltros()" class="btn-secondary">
                <i class="fas fa-eraser"></i> Limpar Filtros
            </button>
        </div>
    </div>

      <table class="requests-table">
        <thead>
          <tr>
            <th class="sort-header" onclick="sortTable('numero')">Número</th>
            <th class="sort-header" onclick="sortTable('data')">Data</th>
            <th class="sort-header" onclick="sortTable('solicitante')">
              Solicitante
            </th>
            <th class="sort-header" onclick="sortTable('departamento')">
              Departamento
            </th>
            <th class="sort-header" onclick="sortTable('centroCusto')">
              Centro de Custo
            </th>
            <th class="sort-header" onclick="sortTable('itens')">Itens</th>
            <th class="sort-header" onclick="sortTable('prioridade')">
              Prioridade
            </th>
            <th
              class="sort-header"
              onclick="sortTable('status')"
              style="width: 120px"
            >
              Status
            </th>
            <th style="width: 180px; text-align: center">Ações</th>
          </tr>
          <tr class="filter-row">
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="numero"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="data"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="solicitante"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="departamento"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="centroCusto"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="itens"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="prioridade"
              />
            </th>
            <th>
              <input
                type="text"
                class="filter-input"
                onkeyup="filterTable()"
                data-column="status"
              />
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody id="requestsTableBody"></tbody>
      </table>
    </div>

    <div id="accessDenied" class="access-denied" style="display: none">
      <h2>Acesso Negado</h2>
      <p>Você não possui permissão para acessar esta funcionalidade.</p>
      <p>
        Entre em contato com o administrador do sistema para solicitar acesso.
      </p>
      <button class="btn-secondary" onclick="window.location.href='index.html'">
        Voltar para o Menu
      </button>
    </div>

    <div id="requestModal" class="modal">
      <div class="modal-content">
          <span class="close-button" onclick="closeModal('requestModal')">×</span>
        <h2 id="modalTitle">Nova Solicitação de Compra</h2>
        <div id="displayRequestNumber" style="margin-bottom: 15px"></div>
          <div id="editWarning" class="edit-warning" style="display: none">
          Atenção: Esta solicitação já está em processo de cotação/aprovação. As
          alterações serão registradas no histórico e precisarão de nova
          aprovação.
          </div>
          <div id="errorMessage" class="error-message"></div>
          <form id="requestForm" onsubmit="handleRequest(event)">
            <input type="hidden" id="editingId" />
            <input type="hidden" id="originalStatus" />

              <div class="form-row">
                <div class="form-col">
              <label>Origem:</label>
              <select id="origem" required>
                <option value="MANUAL">Manual</option>
                <option value="MRP" disabled>MRP (Automático)</option>
              </select>
                </div>
                <div class="form-col">
              <label>Tipo:</label>
              <select id="tipoSolicitacao" required>
                <option value="NORMAL">Normal</option>
                <option value="PLANEJADA">Planejada</option>
                <option value="EMERGENCIAL">Emergencial</option>
                  </select>
                </div>
                <div class="form-col">
              <label>Prioridade:</label>
                  <select id="prioridade" required>
                    <option value="NORMAL">Normal</option>
                    <option value="URGENTE">Urgente</option>
                    <option value="CRITICA">Crítica</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
              <label>Solicitante:</label>
              <input type="text" id="solicitante" required />
                </div>
                <div class="form-col">
              <label>Departamento:</label>
              <select id="departamento" required>
                <option value="">Selecione...</option>
                <option value="ENGENHARIA">Engenharia</option>
                <option value="PRODUCAO">Produção</option>
                <option value="MANUTENCAO">Manutenção</option>
                <option value="QUALIDADE">Qualidade</option>
                <option value="ADMINISTRATIVO">Administrativo</option>
              </select>
            </div>
            <div class="form-col">
              <label>Centro de Custo:</label>
              <select id="centroCusto" required>
                <option value="">Selecione...</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
              <label>Fornecedor:</label>
              <select id="fornecedor" required>
                <option value="">Selecione...</option>
              </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-col">
              <label>Justificativa:</label>
              <textarea id="justificativa" rows="3" required placeholder="Descreva o motivo da solicitação..."></textarea>
              </div>
            </div>

              <div class="form-group">
            <label>Itens:</label>
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Código</th>
                    <th>Descrição</th>
                  <th>Quantidade (PC)</th>
                    <th>Unidade Interna</th>
                    <th>Ações</th>
                  <th>Quantidade (KG)</th>
                  </tr>
                </thead>
              <tbody id="itemsTableBody"></tbody>
              </table>
            <button type="button" class="btn-primary" onclick="addItem()">
              Adicionar Item
            </button>
            </div>

          <div class="form-group">
            <label>Motivo da Alteração:</label>
            <textarea
              id="motivoAlteracao"
              rows="2"
              style="display: none"
            ></textarea>
            </div>

          <div class="modal-footer">
            <button type="submit" id="submitButton" class="btn-primary">
              Criar Solicitação
              </button>
            <button
              type="button"
              id="deleteButton"
              class="btn-danger"
              style="display: none"
              onclick="deleteRequest()"
            >
              Excluir Solicitação
              </button>
          </div>
        </form>

        <div id="historySection" class="history-section" style="display: none">
          <h3>Histórico de Alterações</h3>
          <div id="historyContent"></div>
        </div>
      </div>
    </div>

    <!-- Modal para itens vinculados -->
    <div id="linkedItemsModal" class="modal">
      <div class="modal-content" style="max-width: 800px">
        <span class="close-button" onclick="closeModal('linkedItemsModal')"
          >×</span
        >
        <h2>Itens Vinculados ao Fornecedor</h2>
        <input
          type="search"
          id="searchLinkedItems"
          oninput="filterLinkedItems(this.value)"
          placeholder="Pesquisar por descrição"
        />
        <div style="margin-top: 20px">
          <table class="items-table">
            <thead>
              <tr>
                <th>Selecionar</th>
                <th>Código</th>
                <th>Descrição</th>
                <th>Unidade</th>
                <th>Quantidade</th>
              </tr>
            </thead>
            <tbody id="linkedItemsTableBody"></tbody>
          </table>
          <div style="margin-top: 15px; text-align: right">
            <button
              type="button"
              class="btn-primary"
              onclick="addSelectedItems()"
            >
              Adicionar Itens Selecionados
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Adicionar ao HTML, antes do fechamento do body -->
    <div id="detailsModal" class="modal">
      <div class="modal-content">
        <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px; border-bottom: 1px solid var(--border-color);">
          <h2>Detalhes da Solicitação <span id="detailsRequestNumber"></span></h2>
          <div style="display: flex; gap: 10px; align-items: center;">
            <button class="btn-secondary" onclick="closeModal('detailsModal')" style="padding: 5px 10px;">
              Voltar
            </button>
            <span class="close-button" onclick="closeModal('detailsModal')" style="cursor: pointer; font-size: 24px;">×</span>
          </div>
        </div>
        <div class="modal-body">
          <div class="detail-section">
            <h3>Informações Gerais</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Status</div>
                <div class="detail-value" id="detailsStatus"></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Origem</div>
                <div class="detail-value" id="detailsOrigem"></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Tipo</div>
                <div class="detail-value" id="detailsTipo"></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Data Criação</div>
                <div class="detail-value" id="detailsDataCriacao"></div>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3>Solicitante e Departamento</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Solicitante</div>
                <div class="detail-value" id="detailsSolicitante"></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Departamento</div>
                <div class="detail-value" id="detailsDepartamento"></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Centro de Custo</div>
                <div class="detail-value" id="detailsCentroCusto"></div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Fornecedor</div>
                <div class="detail-value" id="detailsFornecedor"></div>
              <div class="detail-item">
                <div class="detail-label">Prioridade</div>
                <div class="detail-value" id="detailsPrioridade"></div>
              </div>
            </div>
          </div>

          <div id="mrpInfoSection" class="detail-section" style="display: none;">
            <h3>Informações do MRP</h3>
            <div class="mrp-info">
              <h4>Análise de Necessidades</h4>
              <ul>
                <li>Data da Análise: <span id="detailsDataAnalise"></span></li>
                <li>Período Analisado: <span id="detailsPeriodoAnalise"></span></li>
                <li>Ordens de Produção: <span id="detailsOrdensProducao"></span></li>
              </ul>
            </div>
          </div>

          <div class="detail-section">
            <h3>Itens da Solicitação</h3>
            <table class="data-table">
              <thead>
                <tr>
                  <th>Código</th>
                  <th>Descrição</th>
                  <th>Quantidade</th>
                  <th>Unidade</th>
                  <th>Valor Unit.</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody id="detailsItensTable"></tbody>
            </table>
          </div>

          <div class="detail-section">
            <h3>Histórico</h3>
            <div id="detailsHistorico"></div>
          </div>
        </div>
        <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; padding: 15px 20px; border-top: 1px solid var(--border-color);">
          <button id="detailsQuoteButton" class="btn-primary" onclick="selectItemsForQuotationFromDetails()" style="display: none;">
            Gerar Cotação
          </button>
          <button id="detailsApproveButton" class="btn-success" onclick="approveRequestFromDetails()" style="display: none;">
            Aprovar Solicitação
          </button>
        </div>
      </div>
    </div>

    <script type="module">
      // ===================================================================
      // SOLICITAÇÃO COMPRAS - COM INTEGRIDADE DE DADOS
      // ===================================================================
      import { db } from './firebase-config.js';
      import {
        collection,
        addDoc,
        doc,
        getDoc,
        setDoc,
        runTransaction,
        getDocs,
        updateDoc,
        deleteDoc,
        Timestamp
      } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

      // Importar serviço de integridade
      import { DataIntegrityService } from './services/data-integrity-service.js';

      window.printSolicitacao = function(solicitacaoId) {
          window.open(`imprimir_solicitacao.html?solicitacaoId=${solicitacaoId}`, '_blank');
      };

      let produtos = [];
      let solicitacoes = [];
      let ordensProducao = [];
      let estoques = [];
      let fornecedores = [];
      let centrosCusto = [];
      let counterRef;
      let currentUser = null;
      let userPermissions = [];
      let lastQuotationCounter = null;

      window.onload = async function() {
          try {
              await new Promise(resolve => setTimeout(resolve, 1000));
              console.log("Verificando autenticação...");
              const userSession = localStorage.getItem('currentUser');
              if (!userSession) {
                  console.error("Sessão não encontrada");
                  window.location.href = 'login.html';
                  return;
              }
              console.log("Sessão encontrada:", userSession);

              if (!userSession) {
                  console.log("Nenhuma sessão encontrada");
                  alert("Usuário não está logado. Redirecionando para a página de login...");
                  window.location.href = 'login.html';
                  return;
              }

              await new Promise(resolve => setTimeout(resolve, 500));

              try {
                  currentUser = JSON.parse(userSession);
                  console.log("Dados do usuário:", currentUser);

                  if (!currentUser || !currentUser.nome) {
                      console.log("Dados do usuário inválidos");
                      localStorage.removeItem('currentUser');
                      window.location.href = 'login.html';
                      return;
                  }

                  console.log("Usuário autenticado:", currentUser.nome);
              } catch (e) {
                  console.error("Erro ao processar dados do usuário:", e);
                  localStorage.removeItem('currentUser');
                  window.location.href = 'login.html';
                  return;
              }

              document.getElementById('mainContainer').style.display = 'block';
              await loadInitialData();
              await loadRequests();
              await checkUserPermissions();

              counterRef = doc(db, "contadores", "solicitacoes");
              const counterDoc = await getDoc(counterRef);
              if (!counterDoc.exists()) {
                  await setDoc(counterRef, { valor: 0 });
              }

              const quotationCounterRef = doc(db, "contadores", "cotacoes");
              const quotationCounterDoc = await getDoc(quotationCounterRef);
              if (!quotationCounterDoc.exists()) {
                  await setDoc(quotationCounterRef, { valor: 0 });
              } else {
                  lastQuotationCounter = quotationCounterDoc.data().valor;
              }
          } catch (error) {
              console.error("Erro na inicialização:", error);
              alert("Erro ao carregar a aplicação");
          }
      };

      async function checkUserPermissions() {
          try {
              if (currentUser.nivel === 5 || currentUser.nivel === 9) {
                  document.getElementById('mainContainer').style.display = 'block';
                  return;
              }

              const permissionsDoc = await getDoc(doc(db, "permissoes", currentUser.id));
              if (permissionsDoc.exists()) {
                  userPermissions = permissionsDoc.data().permissoes || [];
                  if (userPermissions.includes('solicitacao_compras')) {
                      document.getElementById('mainContainer').style.display = 'block';
                      return;
                  }
              }

              document.getElementById('accessDenied').style.display = 'block';
          } catch (error) {
              console.error("Erro ao verificar permissões:", error);
              alert("Erro ao verificar permissões.");
          }
      }

      async function generateRequestNumber() {
          try {
              const counterRef = doc(db, "contadores", "solicitacoes");
              const counterDoc = await getDoc(counterRef);

              if (!counterDoc.exists()) {
                  await setDoc(counterRef, { valor: 1 });
                  return 'SC000001';
              }

              const newValue = counterDoc.data().valor + 1;
              await updateDoc(counterRef, { valor: newValue });

              return `SC${newValue.toString().padStart(6, '0')}`;
          } catch (error) {
              console.error("Erro ao gerar número da solicitação:", error);
              throw error;
          }
      }

      async function loadInitialData() {
          try {
              console.log("Iniciando carregamento de dados...");

                  const parametrosDoc = await getDoc(doc(db, "parametros", "sistema"));
              const parametros = parametrosDoc.exists() ? parametrosDoc.data() : {};
              const requireHomologacao = parametros.homologacaoFornecedor || false;

              const [produtosSnap, solicitacoesSnap, ordensSnap, estoquesSnap, fornecedoresSnap, centrosCustoSnap, vinculosSnap] = await Promise.all([
                  getDocs(collection(db, "produtos")),
                  getDocs(collection(db, "solicitacoesCompra")),
                  getDocs(collection(db, "ordensProducao")),
                  getDocs(collection(db, "estoques")),
                  getDocs(collection(db, "fornecedores")),
                  getDocs(collection(db, "centrosCusto")),
                  getDocs(collection(db, "produtos_fornecedores"))
              ]);

              produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
              window.produtosFornecedores = vinculosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
              solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
              ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
              estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
              fornecedores = fornecedoresSnap.docs
                      .map(doc => ({ id: doc.id, ...doc.data() }))
                  .filter(f => !requireHomologacao || f.statusHomologacao === 'Homologado');
              centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

              console.log("Dados carregados:", {
                  produtos: produtos.length,
                  fornecedores: fornecedores.length,
                  centrosCusto: centrosCusto.length
              });

              populateCentrosCusto();
          } catch (error) {
              console.error("Erro ao carregar dados:", error);
              alert("Erro ao carregar dados iniciais.");
          }
      }

      function checkLowStock() {
          try {
              const ordensAtivas = ordensProducao.filter(op =>
                  op.status === 'Pendente' || op.status === 'Em Produção'
              );
              alert('Atenção: Materiais com estoque baixo identificados. Por favor, verifique a aba de estoque.');
          } catch (error) {
              console.error("Erro ao verificar estoque baixo:", error);
          }
      }

      function populateCentrosCusto() {
          console.log("Populando centros de custo...");
          const select = document.getElementById('centroCusto');
          if (!select) {
              console.error("Elemento 'centroCusto' não encontrado");
              return;
          }

          if (!centrosCusto || !Array.isArray(centrosCusto)) {
              console.error("Dados de centros de custo inválidos:", centrosCusto);
              return;
          }

          // Log detalhado dos centros de custo
          console.log("Detalhes dos centros de custo:", centrosCusto.map(cc => ({
              id: cc.id,
              codigo: cc.codigo,
              descricao: cc.descricao,
              departamento: cc.departamento,
              nome: cc.nome
          })));

          select.innerHTML = '<option value="">Selecione...</option>';

          try {
              centrosCusto
                  .sort((a, b) => (a.codigo || '').localeCompare(b.codigo || ''))
                  .forEach(centro => {
                      if (centro && centro.id && centro.codigo) {
                          const descricao = centro.descricao || centro.nome || '';
                          select.innerHTML += `<option value="${centro.id}">${centro.codigo} - ${descricao}</option>`;
                      }
                  });
              console.log(`${centrosCusto.length} centros de custo carregados`);
          } catch (error) {
              console.error("Erro ao popular centros de custo:", error);
          }
      }

      async function loadRequests() {
          try {
              console.log("Iniciando carregamento das solicitações");
              const tableBody = document.getElementById('requestsTableBody');
              if (!tableBody) {
                  console.error("Elemento requestsTableBody não encontrado");
                  return;
              }
              tableBody.innerHTML = '';

              const [solicitacoesSnap, cotacoesSnap] = await Promise.all([
                  getDocs(collection(db, "solicitacoesCompra")),
                  getDocs(collection(db, "cotacoes"))
              ]);
              console.log("Solicitações encontradas:", solicitacoesSnap.size);

              const cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
              solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

              solicitacoes
                  .sort((a, b) => (b.dataCriacao?.seconds || 0) - (a.dataCriacao?.seconds || 0))
                  .forEach(solicitacao => {
                      if (!solicitacao) return;

                      const fornecedor = fornecedores.find(f => f.id === solicitacao.fornecedorId);
                      const centroCusto = centrosCusto.find(cc => cc.id === solicitacao.centroCustoId);
                      const hasCotacao = cotacoes.some(c => c.solicitacaoId === solicitacao.id);
                  const row = document.createElement('tr');
                  row.ondblclick = () => {
                          const requestNumber = row.cells[0].textContent;
                          const solicitacao = solicitacoes.find(s => s.numero === requestNumber);
                          if (solicitacao) {
                      viewRequest(solicitacao.id);
                          } else {
                              console.error('Solicitation not found for viewing', requestNumber);
                              alert('Erro ao encontrar a solicitação para visualizar.');
                          }
                  };

                  const isSystemGenerated = solicitacao.origem === 'MRP';
                  const solicitanteDisplay = isSystemGenerated ? 'Sistema' : (solicitacao.solicitante || 'N/A');
                  const departamentoDisplay = isSystemGenerated ? 'PRODUCAO' : (solicitacao.departamento || 'N/A');
                      const centroCustoDisplay = isSystemGenerated ? 'PROD' : (centroCusto ? centroCusto.codigo + ' - ' + centroCusto.descricao : 'N/A');

                  row.innerHTML = `
                      <td>${solicitacao.numero || 'N/A'}</td>
                      <td>${solicitacao.dataCriacao ? new Date(solicitacao.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}</td>
                      <td>${solicitanteDisplay}</td>
                      <td>${departamentoDisplay}</td>
                      <td>${centroCustoDisplay}</td>
                          <td>${solicitacao.itens?.length || 0} itens</td>
                          <td><span class="status-badge status-${(solicitacao.prioridade || 'NORMAL').toLowerCase()}">${solicitacao.prioridade || 'NORMAL'}</span></td>
                      <td>
                          <span class="status-badge status-${(solicitacao.status || '').toLowerCase()}">${solicitacao.status || 'N/A'}</span>
                      </td>
                      <td style="text-align: center;">
                          <div class="action-buttons" style="display: inline-flex; gap: 5px; justify-content: center;">
                              <button class="action-icon view-icon" title="Detalhes" onclick="viewRequest('${solicitacao.id}')">👁️</button>
                              <button class="action-icon print-icon" title="Imprimir" onclick="printSolicitacao('${solicitacao.id}')">🖨️</button>
                              ${
                                    solicitacao.status === 'PENDENTE' &&
                                    (currentUser.nivel >= 3 || userPermissions.includes('aprovar_solicitacoes'))
                                ? `
                                    <button class="action-icon approve-icon" title="Aprovar" onclick="approveRequest('${solicitacao.id}')">✓</button>
                                      <button class="action-icon reject-icon" title="Rejeitar" onclick="rejectRequest('${solicitacao.id}')">✕</button>
                              ` : ''
                            }
                              ${
                                solicitacao.status === 'APROVADA' &&
                                (currentUser.nivel >= 3 || userPermissions.includes('gerar_cotacoes')) &&
                                !hasCotacao
                                ? `<button class="action-icon quote-icon" title="Gerar Cotação" onclick="selectItemsForQuotation('${solicitacao.id}')">📝</button>`
                                : ''
                              }
                          </div>
                      </td>
                  `;
                      tableBody.appendChild(row);
                  });

              addButtonEventListeners();
          } catch (error) {
              console.error("Erro ao carregar solicitações:", error);
              alert("Erro ao carregar solicitações.");
          }
      }

      function addButtonEventListeners() {
          document.querySelectorAll('.view-icon').forEach(button => {
              button.addEventListener('click', () => {
                  const row = button.closest('tr');
                  const cells = row.getElementsByTagName('td');
                  const requestNumber = cells[0].textContent;
                  const solicitacao = solicitacoes.find(s => s.numero === requestNumber);
                  if (solicitacao) {
                      viewRequest(solicitacao.id);
                  } else {
                      console.error('Solicitation not found for viewing (icon click)', requestNumber);
                      alert('Erro ao encontrar a solicitação para visualizar.');
                  }
              });
          });

          document.querySelectorAll('.print-button').forEach(button => {
              button.addEventListener('click', () => {
                  const solicitacaoId = button.getAttribute('data-solicitacao-id');
                  window.printSolicitacao(solicitacaoId);
              });
          });

          document.querySelectorAll('.approve-button').forEach(button => {
              button.addEventListener('click', () => {
                  const solicitacaoId = button.getAttribute('data-solicitacao-id');
                  approveRequest(solicitacaoId);
              });
          });

          document.querySelectorAll('.reject-button').forEach(button => {
              button.addEventListener('click', () => {
                  const solicitacaoId = button.getAttribute('data-solicitacao-id');
                  rejectRequest(solicitacaoId);
              });
          });

          document.querySelectorAll('.edit-button').forEach(button => {
              button.addEventListener('click', () => {
                  const solicitacaoId = button.getAttribute('data-solicitacao-id');
                  editRequest(solicitacaoId);
              });
          });

          document.querySelectorAll('.quote-icon').forEach(button => {
              button.addEventListener('click', () => {
                  const row = button.closest('tr');
                  const cells = row.getElementsByTagName('td');
                  // Assuming the first cell (index 0) contains the request number
                  // Find the solicitation object based on the number
                  const requestNumber = cells[0].textContent;
                  const solicitacao = solicitacoes.find(s => s.numero === requestNumber);
                  if (solicitacao) {
                    selectItemsForQuotation(solicitacao.id);
                  } else {
                    console.error('Solicitation not found for quotation', requestNumber);
                    alert('Erro ao encontrar a solicitação para gerar cotação.');
                  }
              });
          });
      }

      window.openNewRequestModal = function() {
          try {
              const userSession = localStorage.getItem('currentUser');
              if (!userSession) {
                  alert('Usuário não está logado. Por favor, faça login novamente.');
                  window.location.href = 'login.html';
                  return;
              }

              currentUser = JSON.parse(userSession);
              if (!currentUser || !currentUser.nome) {
                  alert('Sessão inválida. Por favor, faça login novamente.');
                  localStorage.removeItem('currentUser');
                  window.location.href = 'login.html';
                  return;
              }

              if (currentUser.nivel < 2 && !userPermissions.includes('criar_solicitacoes')) {
                  alert('Você não tem permissão para criar solicitações de compra.');
                  return;
              }

              const form = document.getElementById('requestForm');
              const elements = {
                  modal: document.getElementById('requestModal'),
                  form: document.getElementById('requestForm'),
                  editWarning: document.getElementById('editWarning'),
                  motivoAlteracao: document.getElementById('motivoAlteracao'),
                  historySection: document.getElementById('historySection'),
                  submitButton: document.getElementById('submitButton'),
                  deleteButton: document.getElementById('deleteButton'),
                  errorMessage: document.getElementById('errorMessage'),
                  displayRequestNumber: document.getElementById('displayRequestNumber'),
                  itemsTableBody: document.getElementById('itemsTableBody'),
                  solicitante: document.getElementById('solicitante'),
                  departamento: document.getElementById('departamento'),
                  centroCusto: document.getElementById('centroCusto'),
                  prioridade: document.getElementById('prioridade'),
                  justificativa: document.getElementById('justificativa'),
                  editingId: document.getElementById('editingId'),
                  originalStatus: document.getElementById('originalStatus'),
                  modalTitle: document.getElementById('modalTitle')
              };

              if (!form || !elements.modal) {
                  console.error('Elementos do formulário não encontrados');
                  return;
              }

              form.reset();
              elements.itemsTableBody.innerHTML = '';
              elements.errorMessage.style.display = 'none';
              elements.solicitante.value = currentUser.nome;
              elements.editingId.value = '';
              elements.originalStatus.value = '';
              elements.modalTitle.textContent = 'Nova Solicitação de Compra';
              elements.submitButton.textContent = 'Criar Solicitação';
              elements.deleteButton.style.display = 'none';
              elements.editWarning.style.display = 'none';
              elements.motivoAlteracao.style.display = 'none';
              elements.historySection.style.display = 'none';

              populateCentrosCusto();
              populateFornecedores();
              elements.modal.style.display = 'block';
              addItem();
          } catch (error) {
              console.error('Erro ao abrir modal:', error);
              showErrorMessage('Erro ao abrir modal. Por favor, tente novamente.');
          }
      };

      window.closeModal = function(modalId) {
          document.getElementById(modalId).style.display = 'none';
      };

      function populateFornecedores() {
          try {
              const select = document.getElementById('fornecedor');
              if (!select) {
                  console.error("Elemento 'fornecedor' não encontrado");
                  return;
              }

              select.innerHTML = '<option value="">Selecione...</option>';

              if (!fornecedores || !Array.isArray(fornecedores)) {
                  console.error("Dados de fornecedores inválidos:", fornecedores);
                  return;
              }

              const fornecedoresHomologados = fornecedores.filter(f => f.statusHomologacao === 'Homologado');
              fornecedoresHomologados.forEach(fornecedor => {
                  if (fornecedor && fornecedor.id && fornecedor.razaoSocial) {
                      select.innerHTML += `<option value="${fornecedor.id}">${fornecedor.razaoSocial}</option>`;
                  }
              });

              if (fornecedoresHomologados.length === 0) {
                  console.warn('Nenhum fornecedor homologado disponível');
              }
          } catch (error) {
              console.error("Erro ao popular fornecedores:", error);
          }
      }

      function showErrorMessage(message) {
          const errorMessage = document.getElementById('errorMessage');
          if (errorMessage) {
              errorMessage.textContent = message;
              errorMessage.style.display = 'block';
          } else {
              console.error("Elemento 'errorMessage' não encontrado");
              alert(message);
          }
      }

      window.addItem = function() {
          const tableBody = document.getElementById('itemsTableBody');
          const row = document.createElement('tr');
          row.innerHTML = `
              <td>
                  <div style="display: flex; gap: 5px;">
                      <input type="text" class="item-codigo" placeholder="Código" style="width: 100px;"
                             onblur="searchProduct(this)" required>
                      <button type="button" onclick="openProductSearch(this)">🔍</button>
                  </div>
              </td>
              <td><input type="text" class="item-descricao" required readonly></td>
              <td><input type="number" class="item-quantidade" min="0.001" step="0.001" required
                         oninput="updateConversion(this.closest('tr'))"></td>
              <td><span class="item-unidade">PC</span></td>
              <td><button type="button" class="btn-danger" onclick="removeItem(this)">Remover</button></td>
              <td><span class="item-quantidade-kg">-</span></td>
          `;
          tableBody.appendChild(row);
          return row;
      };

      window.searchProduct = function(input) {
          const codigo = input.value.trim().toUpperCase();
          const row = input.closest('tr');
          const descricaoInput = row.querySelector('.item-descricao');
          const unidadeSpan = row.querySelector('.item-unidade');
          const quantidadeKgSpan = row.querySelector('.item-quantidade-kg');

          if (codigo) {
              const produto = produtos.find(p => p.codigo.toUpperCase() === codigo);
              if (produto) {
                  if (produto.tipo === 'PA' || produto.tipo === 'SP') {
                      alert('Não é possível solicitar compra de produtos do tipo PA ou SP.');
                      input.value = '';
                      descricaoInput.value = '';
                      unidadeSpan.textContent = 'PC';
                      quantidadeKgSpan.textContent = '-';
                      return;
                  }

                  descricaoInput.value = produto.descricao;
                  row.dataset.produtoId = produto.id;
                  row.dataset.unidadeInterna = produto.unidade;
                  row.dataset.unidadeCompra = produto.unidadeSecundaria || produto.unidade;
                  row.dataset.fatorConversao = produto.fatorConversao || 1;

                  unidadeSpan.textContent = produto.unidade;

                  // Se tiver ordem de produção selecionada, pegar quantidade do material necessário
                  const opId = document.getElementById('productionOrderSelect')?.value;
                  if (opId) {
                      const ordem = ordensProducao.find(op => op.id === opId);
                      if (ordem && ordem.materiaisNecessarios) {
                          const material = ordem.materiaisNecessarios.find(m => m.produtoId === produto.id);
                          if (material && material.necessidade > 0) {
                              row.querySelector('.item-quantidade').value = material.necessidade.toFixed(3);
                          }
                      }
                  }

                  updateConversion(row);
              } else {
                  alert('Produto não encontrado!');
                  input.value = '';
                  descricaoInput.value = '';
                  quantidadeKgSpan.textContent = '-';
              }
          }
      };

      window.updateConversion = function(row) {
          const quantidadeInput = row.querySelector('.item-quantidade');
          const quantidadeKgSpan = row.querySelector('.item-quantidade-kg');
          const fatorConversao = parseFloat(row.dataset.fatorConversao) || 1;
          const unidadeInterna = row.dataset.unidadeInterna;
          const unidadeCompra = row.dataset.unidadeCompra;
          const quantidadePC = parseFloat(quantidadeInput.value) || 0;

          if (quantidadePC > 0 && unidadeInterna === 'PC' && unidadeCompra === 'KG') {
              const quantidadeKG = quantidadePC * fatorConversao;
              quantidadeKgSpan.textContent = `${quantidadeKG.toFixed(3)} KG`;
          } else {
              quantidadeKgSpan.textContent = `${quantidadePC.toFixed(3)} ${unidadeCompra}`;
          }
      };

      window.removeItem = function(button) {
          button.closest('tr').remove();
      };

      window.viewRequest = async function(requestId) {
        try {
          if (!requestId) {
            console.error('Invalid or missing request ID for viewing');
            alert('Por favor, selecione uma solicitação para visualizar.');
            return;
          }
          console.log('Visualizando solicitação:', requestId);
          const request = solicitacoes.find(s => s.id === requestId);

          if (!request) {
            console.error('Solicitação não encontrada:', requestId);
            alert('Solicitação não encontrada');
            return;
          }

          // Helper function to safely set element content
          const setElementContent = (elementId, content) => {
            const element = document.getElementById(elementId);
            if (element) {
              element.textContent = content;
            } else {
              console.warn(`Element with id ${elementId} not found`);
            }
          };

          // Helper function to safely set element HTML
          const setElementHTML = (elementId, content) => {
            const element = document.getElementById(elementId);
            if (element) {
              element.innerHTML = content;
            } else {
              console.warn(`Element with id ${elementId} not found`);
            }
          };

          // Formatar datas com validação
          const formatDate = (timestamp) => {
            if (!timestamp) return '-';
            try {
              if (timestamp.seconds) {
                return new Date(timestamp.seconds * 1000).toLocaleDateString('pt-BR');
              }
              return new Date(timestamp).toLocaleDateString('pt-BR');
            } catch (e) {
              console.warn('Erro ao formatar data:', e);
              return '-';
            }
          };

          // Formatar status com validação
          const formatStatus = (status) => {
            if (!status) return 'PENDENTE';
            return status.toUpperCase();
          };

          // Formatar valores numéricos
          const formatNumber = (value) => {
            if (value === undefined || value === null) return '0';
            return Number(value).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
          };

          // Formatar período de análise do MRP
          const formatPeriodoAnalise = (mrpInfo) => {
            if (!mrpInfo || !mrpInfo.periodoAnalise) return 'N/A';
            const { inicio, fim } = mrpInfo.periodoAnalise;
            if (!inicio || !fim) return 'N/A';
            return `${formatDate(inicio)} a ${formatDate(fim)}`;
          };

          // Atualizar o conteúdo do modal de detalhes
          setElementContent('detailsRequestNumber', request.numero || 'N/A');
          setElementHTML('detailsStatus', `<span class="status-badge status-${formatStatus(request.status).toLowerCase()}">${formatStatus(request.status)}</span>`);
          setElementContent('detailsOrigem', request.origem || 'N/A');
          setElementContent('detailsTipo', request.tipo || 'N/A');
          setElementContent('detailsDataCriacao', formatDate(request.dataCriacao));
          setElementContent('detailsSolicitante', request.solicitante || 'N/A');
          setElementContent('detailsDepartamento', request.departamento || 'N/A');

          const centroCusto = centrosCusto.find(cc => cc.id === request.centroCustoId);
          setElementContent('detailsCentroCusto', centroCusto ? `${centroCusto.codigo} - ${centroCusto.descricao || centroCusto.nome}` : 'N/A');

          const fornecedor = fornecedores.find(f => f.id === request.fornecedorId);
          setElementContent('detailsFornecedor', fornecedor ? fornecedor.razaoSocial : 'N/A');

          setElementContent('detailsPrioridade', request.prioridade || 'NORMAL');

          // Atualizar informações do MRP se existirem
          const mrpInfoSection = document.getElementById('mrpInfoSection');
          if (mrpInfoSection) {
            if (request.mrpInfo) {
              mrpInfoSection.style.display = 'block';
              setElementContent('detailsDataAnalise', formatDate(request.mrpInfo.dataAnalise));
              setElementContent('detailsPeriodoAnalise', formatPeriodoAnalise(request.mrpInfo));
              setElementContent('detailsOrdensProducao', (request.mrpInfo.ordensProducao || []).join(', ') || 'N/A');
            } else {
              mrpInfoSection.style.display = 'none';
            }
          }

          // Atualizar tabela de itens
          const itemsTableBody = document.getElementById('detailsItensTable');
          if (itemsTableBody) {
            itemsTableBody.innerHTML = (request.itens || []).map(item => {
              // Add a check for missing quantity or unit data
              if (item.quantidadeCompra === undefined || item.unidadeCompra === undefined) {
                console.warn(`Item ${item.codigo} in solicitation ${request.numero} is missing quantidadeCompra or unidadeCompra.`);
                // Fallback to internal quantity/unit if purchase quantity/unit is missing
                item.quantidadeCompra = item.quantidadeInterna;
                item.unidadeCompra = item.unidadeInterna;
              }

              return `
              <tr>
                <td>${item.codigo || 'N/A'}</td>
                <td>${item.descricao || 'N/A'}</td>
                <td>${item.quantidadeCompra === undefined || item.quantidadeCompra === null ? formatNumber(item.quantidade || 0) : formatNumber(item.quantidadeCompra)}</td>
                <td>${item.unidadeCompra || item.unidade || 'UN'}</td>
                <td>${formatNumber(item.valorUnitario || 0)}</td>
                <td>${formatNumber((item.valorUnitario || 0) * (item.quantidadeCompra || item.quantidade || 0))}</td>
              </tr>
            `;
            }).join('');
          }

          // Atualizar histórico
          const historicoContent = document.getElementById('detailsHistorico');
          if (historicoContent) {
            if (request.historico && request.historico.length > 0) {
              historicoContent.innerHTML = request.historico.map(h => `
                <div class="history-item">
                  <div class="history-date">${formatDate(h.data)}</div>
                  <div class="history-user">${h.usuario || 'Sistema'}</div>
                  <div class="history-action">
                    <strong>Status:</strong> ${h.statusAnterior} → ${h.novoStatus}<br>
                    <strong>Motivo:</strong> ${h.motivo || 'N/A'}
                  </div>
                </div>
              `).join('');
            } else {
              historicoContent.innerHTML = '<div class="history-item">Nenhum histórico disponível</div>';
            }
          }

          // Exibir o modal
          const detailsModal = document.getElementById('detailsModal');
          if (detailsModal) {
            detailsModal.style.display = 'block';
          } else {
            console.error('Modal de detalhes não encontrado');
          }

          // Update button visibility based on request status and user permissions
          const approveButton = document.getElementById('detailsApproveButton');
          const quoteButton = document.getElementById('detailsQuoteButton');

          if (approveButton) {
            const canApprove = currentUser.nivel >= 3 || userPermissions.includes('aprovar_solicitacoes');
            const isPending = request.status === 'PENDENTE';
            approveButton.style.display = (canApprove && isPending) ? 'block' : 'none';
          }

          if (quoteButton) {
            const canQuote = currentUser.nivel >= 3 || userPermissions.includes('gerar_cotacoes');
            const isApproved = request.status === 'APROVADA';
            const hasQuotation = await checkIfRequestHasQuotation(requestId);
            quoteButton.style.display = (canQuote && isApproved && !hasQuotation) ? 'block' : 'none';
          }

          // Store request ID in the number element for reference
          const requestNumberElement = document.getElementById('detailsRequestNumber');
          if (requestNumberElement) {
            requestNumberElement.dataset.requestId = requestId;
          }

        } catch (error) {
          console.error('Erro ao visualizar solicitação:', error);
          alert('Erro ao visualizar solicitação: ' + error.message);
        }
      };

      window.editRequest = function(requestId) {
          const solicitacao = solicitacoes.find(s => s.id === requestId);
          if (solicitacao) {
              openRequestModal(solicitacao);
          }
      };

      function openRequestModal(solicitacao = null) {
          try {
              const elements = {
                  modal: document.getElementById('requestModal'),
                  form: document.getElementById('requestForm'),
                  editWarning: document.getElementById('editWarning'),
                  motivoAlteracao: document.getElementById('motivoAlteracao'),
                  historySection: document.getElementById('historySection'),
                  submitButton: document.getElementById('submitButton'),
                  deleteButton: document.getElementById('deleteButton'),
                  errorMessage: document.getElementById('errorMessage'),
                  displayRequestNumber: document.getElementById('displayRequestNumber'),
                  itemsTableBody: document.getElementById('itemsTableBody'),
                  solicitante: document.getElementById('solicitante'),
                  departamento: document.getElementById('departamento'),
                  centroCusto: document.getElementById('centroCusto'),
                  prioridade: document.getElementById('prioridade'),
                  justificativa: document.getElementById('justificativa'),
                  editingId: document.getElementById('editingId'),
                  originalStatus: document.getElementById('originalStatus'),
                  modalTitle: document.getElementById('modalTitle')
              };

              for (const [key, element] of Object.entries(elements)) {
                  if (!element) {
                      console.error(`Required element '${key}' not found`);
                      return;
                  }
              }

              elements.form.reset();
              elements.itemsTableBody.innerHTML = '';
              elements.errorMessage.style.display = 'none';
              elements.displayRequestNumber.style.display = 'block';

              if (solicitacao) {
                  elements.modalTitle.textContent = `Editar Solicitação`;
                  elements.displayRequestNumber.textContent = solicitacao.numero;
                  elements.editingId.value = solicitacao.id;
                  elements.originalStatus.value = solicitacao.status;
                  elements.solicitante.value = solicitacao.solicitante || '';
                  elements.departamento.value = solicitacao.departamento || '';
                  elements.centroCusto.value = solicitacao.centroCustoId || '';
                  elements.prioridade.value = solicitacao.prioridade || 'NORMAL';
                  elements.justificativa.value = solicitacao.justificativa || '';

                  solicitacao.itens.forEach(item => {
                      const row = addItem();
                      row.querySelector('.item-codigo').value = item.codigo;
                      row.querySelector('.item-descricao').value = item.descricao;
                      row.querySelector('.item-quantidade').value = item.quantidadeInterna;
                      const produto = produtos.find(p => p.codigo === item.codigo);
                      if (produto) {
                          row.dataset.produtoId = produto.id;
                          row.dataset.unidadeInterna = produto.unidade;
                          row.dataset.unidadeCompra = produto.unidadeSecundaria || produto.unidade;
                          row.dataset.fatorConversao = produto.fatorConversao || 1;
                          row.querySelector('.item-unidade').textContent = item.unidadeInterna;
                          updateConversion(row);
                      }
                  });

                  elements.editWarning.style.display = 'none';
                  elements.motivoAlteracao.style.display = 'none';
                  elements.motivoAlteracao.required = false;

                  const form = document.getElementById('requestForm');
                  const initialFormState = JSON.stringify(getFormData());

                  form.addEventListener('change', () => {
                      const currentFormState = JSON.stringify(getFormData());
                      if (solicitacao.status !== 'PENDENTE' && currentFormState !== initialFormState) {
                          elements.editWarning.style.display = 'block';
                          elements.motivoAlteracao.style.display = 'block';
                          elements.motivoAlteracao.required = true;
                      } else {
                          elements.editWarning.style.display = 'none';
                          elements.motivoAlteracao.style.display = 'none';
                          elements.motivoAlteracao.required = false;
                      }
                  });

                  function getFormData() {
                      return {
                          departamento: document.getElementById('departamento').value,
                          centroCusto: document.getElementById('centroCusto').value,
                          prioridade: document.getElementById('prioridade').value,
                          justificativa: document.getElementById('justificativa').value,
                          itens: Array.from(document.querySelectorAll('#itemsTableBody tr')).map(row => ({
                              codigo: row.querySelector('.item-codigo').value,
                              quantidade: row.querySelector('.item-quantidade').value
                          }))
                      };
                  }

                  if (solicitacao.historico && solicitacao.historico.length > 0) {
                      elements.historySection.style.display = 'block';
                      const historyContent = document.getElementById('historyContent');
                      historyContent.innerHTML = solicitacao.historico.map(h => `
                          <div class="history-item">
                              <strong>Data:</strong> ${new Date(h.data.seconds * 1000).toLocaleString()}<br>
                              <strong>Status Anterior:</strong> ${h.statusAnterior}<br>
                              <strong>Motivo:</strong> ${h.motivo}
                          </div>
                      `).join('');
                  } else {
                      elements.historySection.style.display = 'none';
                  }

                  const canEdit = currentUser.nivel >= 2 ||
                                 userPermissions.includes('editar_solicitacoes') ||
                                 solicitacao.solicitante === currentUser.nome;

                  if (!canEdit) {
                      document.querySelectorAll('#requestForm input, #requestForm select, #requestForm textarea').forEach(el => {
                          el.disabled = true;
                      });
                      elements.submitButton.style.display = 'none';
                      elements.deleteButton.style.display = 'none';
                  } else {
                      elements.submitButton.textContent = 'Atualizar Solicitação';
                      elements.deleteButton.style.display = 'block';
                      elements.deleteButton.dataset.requestId = solicitacao.id;
                  }
              } else {
                  elements.modalTitle.textContent = 'Nova Solicitação de Compra';
                  elements.displayRequestNumber.textContent = 'Será gerado automaticamente';
                  elements.editingId.value = '';
                  elements.originalStatus.value = '';
                  elements.solicitante.value = currentUser.nome;
                  elements.editWarning.style.display = 'none';
                  elements.motivoAlteracao.style.display = 'none';
                  elements.motivoAlteracao.required = false;
                  elements.historySection.style.display = 'none';
                  elements.submitButton.textContent = 'Criar Solicitação';
                  elements.deleteButton.style.display = 'none';
                  populateFornecedores();
                  populateCentrosCusto();
                  addItem();
              }

              elements.modal.style.display = 'block';
          } catch (error) {
              console.error('Erro ao abrir modal:', error);
              showErrorMessage('Erro ao abrir modal. Por favor, tente novamente.');
          }
      }

      // Função para duplicar solicitação
      window.duplicateRequest = async function(requestId) {
          try {
              const solicitacao = solicitacoes.find(s => s.id === requestId);
              if (!solicitacao) {
                  alert('Solicitação não encontrada.');
                  return;
              }

              openNewRequestModal();

              // Preencher dados da solicitação original
              document.getElementById('departamento').value = solicitacao.departamento || '';
              document.getElementById('centroCusto').value = solicitacao.centroCustoId || '';
              document.getElementById('fornecedor').value = solicitacao.fornecedorId || '';
              document.getElementById('prioridade').value = solicitacao.prioridade || 'NORMAL';
              document.getElementById('justificativa').value = solicitacao.justificativa || '';

              // Adicionar itens
              const itemsTableBody = document.getElementById('itemsTableBody');
              itemsTableBody.innerHTML = '';

              solicitacao.itens.forEach(item => {
                  const row = addItem();
                  row.querySelector('.item-codigo').value = item.codigo;
                  row.querySelector('.item-descricao').value = item.descricao;
                  row.querySelector('.item-quantidade').value = item.quantidadeInterna;

                  const produto = produtos.find(p => p.codigo === item.codigo);
                  if (produto) {
                      row.dataset.produtoId = produto.id;
                      row.dataset.unidadeInterna = produto.unidade;
                      row.dataset.unidadeCompra = produto.unidadeSecundaria || produto.unidade;
                      row.dataset.fatorConversao = produto.fatorConversao || 1;
                      row.querySelector('.item-unidade').textContent = item.unidadeInterna;
                      updateConversion(row);
                  }
              });

              document.getElementById('modalTitle').textContent = 'Duplicar Solicitação (Nova)';
              showNotification('Dados carregados para duplicação. Revise e salve.', 'info');
          } catch (error) {
              console.error('Erro ao duplicar solicitação:', error);
              showNotification('Erro ao duplicar solicitação.', 'error');
          }
      };

      // Função para exportar solicitações
      window.exportSolicitacoes = function() {
          try {
              const dados = solicitacoes.map(sol => {
                  const fornecedor = fornecedores.find(f => f.id === sol.fornecedorId);
                  const centroCusto = centrosCusto.find(cc => cc.id === sol.centroCustoId);

                  return {
                      'Número': sol.numero,
                      'Data': sol.dataCriacao ? new Date(sol.dataCriacao.seconds * 1000).toLocaleDateString() : '',
                      'Solicitante': sol.solicitante,
                      'Departamento': sol.departamento,
                      'Centro de Custo': centroCusto ? `${centroCusto.codigo} - ${centroCusto.descricao}` : '',
                      'Fornecedor': fornecedor ? fornecedor.razaoSocial : '',
                      'Prioridade': sol.prioridade,
                      'Status': sol.status,
                      'Tipo': sol.tipo,
                      'Origem': sol.origem,
                      'Valor Total': sol.valorTotal ? `R$ ${sol.valorTotal.toFixed(2)}` : '',
                      'Itens': sol.itens ? sol.itens.length : 0,
                      'Justificativa': sol.justificativa
                  };
              });

              exportToCSV(dados, 'solicitacoes_compra');
              showNotification('Relatório exportado com sucesso!', 'success');
          } catch (error) {
              console.error('Erro ao exportar:', error);
              showNotification('Erro ao exportar dados.', 'error');
          }
      };

      // Função auxiliar para exportar CSV
      function exportToCSV(data, filename) {
          if (!data || data.length === 0) return;

          const headers = Object.keys(data[0]);
          const csvContent = [
              headers.join(';'),
              ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(';'))
          ].join('\n');

          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
          link.click();
      }

      // Função para filtros avançados
      window.filtrarSolicitacoes = function() {
          try {
              const filtros = {
                  numero: document.getElementById('filtroNumero').value.toLowerCase(),
                  status: document.getElementById('filtroStatus').value,
                  solicitante: document.getElementById('filtroSolicitante').value.toLowerCase(),
                  departamento: document.getElementById('filtroDepartamento').value,
                  prioridade: document.getElementById('filtroPrioridade').value,
                  tipo: document.getElementById('filtroTipo').value,
                  origem: document.getElementById('filtroOrigem').value,
                  dataInicial: document.getElementById('filtroDataInicial').value,
                  dataFinal: document.getElementById('filtroDataFinal').value
              };

              const tableBody = document.getElementById('requestsTableBody');
              tableBody.innerHTML = '';

              let solicitacoesFiltradas = solicitacoes.filter(sol => {
                  // Filtro por número
                  if (filtros.numero && !sol.numero.toLowerCase().includes(filtros.numero)) {
                      return false;
                  }

                  // Filtro por status
                  if (filtros.status && sol.status !== filtros.status) {
                      return false;
                  }

                  // Filtro por solicitante
                  if (filtros.solicitante && !sol.solicitante.toLowerCase().includes(filtros.solicitante)) {
                      return false;
                  }

                  // Filtro por departamento
                  if (filtros.departamento && sol.departamento !== filtros.departamento) {
                      return false;
                  }

                  // Filtro por prioridade
                  if (filtros.prioridade && sol.prioridade !== filtros.prioridade) {
                      return false;
                  }

                  // Filtro por tipo
                  if (filtros.tipo && sol.tipo !== filtros.tipo) {
                      return false;
                  }

                  // Filtro por origem
                  if (filtros.origem && sol.origem !== filtros.origem) {
                      return false;
                  }

                  // Filtro por data
                  if (filtros.dataInicial || filtros.dataFinal) {
                      const dataDoc = sol.dataCriacao ? new Date(sol.dataCriacao.seconds * 1000) : null;
                      if (!dataDoc) return false;

                      if (filtros.dataInicial) {
                          const dataInicial = new Date(filtros.dataInicial);
                          if (dataDoc < dataInicial) return false;
                      }

                      if (filtros.dataFinal) {
                          const dataFinal = new Date(filtros.dataFinal);
                          dataFinal.setHours(23, 59, 59);
                          if (dataDoc > dataFinal) return false;
                      }
                  }

                  return true;
              });

              // Renderizar resultados filtrados
              solicitacoesFiltradas.forEach(solicitacao => {
                  const fornecedor = fornecedores.find(f => f.id === solicitacao.fornecedorId);
                  const centroCusto = centrosCusto.find(cc => cc.id === solicitacao.centroCustoId);

                  const row = document.createElement('tr');
                  row.ondblclick = () => viewRequest(solicitacao.id);

                  const isSystemGenerated = solicitacao.origem === 'MRP';
                  const solicitanteDisplay = isSystemGenerated ? 'Sistema' : (solicitacao.solicitante || 'N/A');
                  const departamentoDisplay = isSystemGenerated ? 'PRODUCAO' : (solicitacao.departamento || 'N/A');
                  const centroCustoDisplay = isSystemGenerated ? 'PROD' : (centroCusto ? centroCusto.codigo + ' - ' + centroCusto.descricao : 'N/A');

                  row.innerHTML = `
                      <td>${solicitacao.numero || 'N/A'}</td>
                      <td>${solicitacao.dataCriacao ? new Date(solicitacao.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}</td>
                      <td>${solicitanteDisplay}</td>
                      <td>${departamentoDisplay}</td>
                      <td>${centroCustoDisplay}</td>
                      <td>${solicitacao.itens?.length || 0} itens</td>
                      <td><span class="status-badge status-${(solicitacao.prioridade || 'NORMAL').toLowerCase()}">${solicitacao.prioridade || 'NORMAL'}</span></td>
                      <td>
                          <span class="status-badge status-${(solicitacao.status || '').toLowerCase()}">${solicitacao.status || 'N/A'}</span>
                      </td>
                      <td style="text-align: center;">
                          <div class="action-buttons" style="display: inline-flex; gap: 5px; justify-content: center;">
                              <button class="action-icon view-icon" title="Detalhes" onclick="viewRequest('${solicitacao.id}')">👁️</button>
                              <button class="action-icon print-icon" title="Imprimir" onclick="printSolicitacao('${solicitacao.id}')">🖨️</button>
                              <button class="action-icon edit-icon" title="Duplicar" onclick="duplicateRequest('${solicitacao.id}')">📋</button>
                              ${
                                    solicitacao.status === 'PENDENTE' &&
                                    (currentUser.nivel >= 3 || userPermissions.includes('aprovar_solicitacoes'))
                                ? `
                                    <button class="action-icon approve-icon" title="Aprovar" onclick="approveRequest('${solicitacao.id}')">✓</button>
                                      <button class="action-icon reject-icon" title="Rejeitar" onclick="rejectRequest('${solicitacao.id}')">✕</button>
                              ` : ''
                            }
                              ${
                                solicitacao.status === 'APROVADA' &&
                                (currentUser.nivel >= 3 || userPermissions.includes('gerar_cotacoes'))
                                ? `<button class="action-icon quote-icon" title="Gerar Cotação" onclick="selectItemsForQuotation('${solicitacao.id}')">📝</button>`
                                : ''
                              }
                          </div>
                      </td>
                  `;
                  tableBody.appendChild(row);
              });

              showNotification(`${solicitacoesFiltradas.length} solicitação(ões) encontrada(s).`, 'info');
          } catch (error) {
              console.error('Erro ao filtrar:', error);
              showNotification('Erro ao aplicar filtros.', 'error');
          }
      };

      window.limparFiltros = function() {
          document.getElementById('filtroNumero').value = '';
          document.getElementById('filtroStatus').value = '';
          document.getElementById('filtroSolicitante').value = '';
          document.getElementById('filtroDepartamento').value = '';
          document.getElementById('filtroPrioridade').value = '';
          document.getElementById('filtroTipo').value = '';
          document.getElementById('filtroOrigem').value = '';
          document.getElementById('filtroDataInicial').value = '';
          document.getElementById('filtroDataFinal').value = '';

          loadRequests(); // Recarregar todas as solicitações
      };

      window.handleRequest = async function(event, isSystemGenerated = false) {
          if (event) event.preventDefault();

          const editingId = document.getElementById('editingId').value;
          if (editingId) {
              const solicitacao = solicitacoes.find(s => s.id === editingId);
              if (solicitacao.origem === 'MRP' && !userPermissions.includes('editar_solicitacoes_mrp')) {
                  alert('Você não tem permissão para editar solicitações geradas pelo MRP.');
                  return;
              }

              const canEdit = currentUser.nivel >= 2 ||
                             userPermissions.includes('editar_solicitacoes') ||
                             solicitacao.solicitante === currentUser.nome;
              if (!canEdit) {
                  alert('Você não tem permissão para editar esta solicitação.');
                  return;
              }
          } else {
              if (currentUser.nivel < 2 && !userPermissions.includes('criar_solicitacoes')) {
                  alert('Você não tem permissão para criar solicitações de compra.');
                  return;
              }
          }

          const codigosItens = new Set();
          let hasDuplicates = false;
          let hasInvalidProductType = false;

          document.querySelectorAll('#itemsTableBody tr').forEach(row => {
              const codigo = row.querySelector('.item-codigo').value;
              if (codigosItens.has(codigo)) {
                  hasDuplicates = true;
              }
              codigosItens.add(codigo);

              const produto = produtos.find(p => p.codigo === codigo);
              if (produto && (produto.tipo === 'PA' || produto.tipo === 'SP')) {
                  hasInvalidProductType = true;
              }
          });

          const errorMessage = document.getElementById('errorMessage');

          if (hasDuplicates) {
              showErrorMessage('Não é permitido incluir o mesmo item mais de uma vez.');
              return;
          }

          if (hasInvalidProductType) {
              showErrorMessage('Não é possível solicitar compra de produtos do tipo PA ou SP.');
              return;
          }

          const centroCustoId = document.getElementById('centroCusto').value;
          if (!centroCustoId) {
              showErrorMessage('Selecione um centro de custo.');
              return;
          }

          const centroCusto = centrosCusto.find(cc => cc.id === centroCustoId);
          if (!centroCusto) {
              showErrorMessage('Centro de custo não encontrado.');
              return;
          }

          let valorTotalSolicitacao = 0;
          document.querySelectorAll('#itemsTableBody tr').forEach(row => {
              const quantidade = parseFloat(row.querySelector('.item-quantidade').value);
              const produto = produtos.find(p => p.codigo === row.querySelector('.item-codigo').value);
              if (produto && produto.precoMedio) {
                  valorTotalSolicitacao += quantidade * produto.precoMedio;
              }
          });

          const inicioMes = new Date();
          inicioMes.setDate(1);
          inicioMes.setHours(0, 0, 0, 0);

          const solicitacoesDoMes = solicitacoes.filter(s =>
              s.centroCustoId === centroCustoId &&
              s.dataCriacao.seconds >= inicioMes.getTime() / 1000 &&
              s.status !== 'REJEITADA'
          );

          const valorUtilizado = solicitacoesDoMes.reduce((total, s) => total + (s.valorTotal || 0), 0);
          const limiteDisponivel = (centroCusto.orcamentoMensal || 0) - valorUtilizado;

          if (valorTotalSolicitacao > limiteDisponivel) {
              if (!confirm(`Atenção: Esta solicitação excederá o orçamento mensal do centro de custo!\n\nOrçamento Mensal: R$ ${centroCusto.orcamentoMensal?.toFixed(2)}\nJá Utilizado: R$ ${valorUtilizado.toFixed(2)}\nDisponível: R$ ${limiteDisponivel.toFixed(2)}\nValor Solicitação: R$ ${valorTotalSolicitacao.toFixed(2)}\n\nDeseja continuar mesmo assim?`)) {
                  return;
              }
          }

          const fornecedorId = document.getElementById('fornecedor').value;
          if (!fornecedorId) {
              showErrorMessage('Selecione um fornecedor homologado.');
              return;
          }

          const items = [];
          document.querySelectorAll('#itemsTableBody tr').forEach(row => {
              const quantidadeInterna = parseFloat(row.querySelector('.item-quantidade').value);
              const fatorConversao = parseFloat(row.dataset.fatorConversao) || 1;
              const unidadeInterna = row.dataset.unidadeInterna;
              const unidadeCompra = row.dataset.unidadeCompra;
              const quantidadeCompra = unidadeInterna === 'PC' && unidadeCompra === 'KG'
                  ? quantidadeInterna * fatorConversao
                  : quantidadeInterna;

              items.push({
                  codigo: row.querySelector('.item-codigo').value,
                  descricao: row.querySelector('.item-descricao').value,
                  quantidadeInterna: quantidadeInterna,
                  quantidadeCompra: quantidadeCompra !== undefined && quantidadeCompra !== null ? quantidadeCompra : quantidadeInterna,
                  unidadeInterna: unidadeInterna,
                  unidadeCompra: unidadeCompra || unidadeInterna, // Default to internal unit if purchase unit is missing
                  fatorConversao: fatorConversao
              });
          });

          if (items.length === 0) {
              showErrorMessage('Adicione pelo menos um item à solicitação.');
              return;
          }

          try {
              const solicitacaoData = {
                  solicitante: isSystemGenerated ? 'Sistema' : document.getElementById('solicitante').value,
                  departamento: isSystemGenerated ? 'PRODUCAO' : document.getElementById('departamento').value,
                  centroCustoId: isSystemGenerated ? (centrosCusto.find(cc => cc.codigo === 'PROD')?.id || centroCustoId) : centroCustoId,
                  fornecedorId,
                  origem: isSystemGenerated ? 'MRP' : document.getElementById('origem').value,
                  tipo: isSystemGenerated ? 'PLANEJADA' : document.getElementById('tipoSolicitacao').value,
                  prioridade: document.getElementById('prioridade').value,
                  itens: items,
                  justificativa: document.getElementById('justificativa').value,
                  valorTotal: valorTotalSolicitacao,
                  dataCriacao: Timestamp.now()
              };

              if (isSystemGenerated) {
                  solicitacaoData.mrpInfo = {
                      dataAnalise: Timestamp.now(),
                      periodoAnalise: {
                          inicio: document.getElementById('startDate')?.value,
                          fim: document.getElementById('endDate')?.value
                      },
                      ordensProducao: Array.from(new Set(items.flatMap(item => item.ordensOrigem || [])))
                  };
              }

              // ===================================================================
              // VALIDAÇÃO DE INTEGRIDADE ANTES DE SALVAR
              // ===================================================================
              try {
                  const validation = await DataIntegrityService.validateSolicitacao(solicitacaoData);

                  if (!validation.valid) {
                      const errorMessage = 'Erros de validação encontrados:\n\n' + validation.errors.join('\n');
                      alert(errorMessage);
                      return;
                  }
              } catch (validationError) {
                  console.warn('Erro na validação (continuando):', validationError);
                  // Continua mesmo se a validação falhar para não quebrar o fluxo existente
              }

              if (editingId) {
                  const originalStatus = document.getElementById('originalStatus').value;
                  const motivoAlteracao = document.getElementById('motivoAlteracao').value;

                  if (originalStatus !== 'PENDENTE') {
                      solicitacaoData.status = 'PENDENTE';
                      const solicitacao = solicitacoes.find(s => s.id === editingId);
                      const historicoItem = {
                          data: Timestamp.now(),
                          statusAnterior: originalStatus,
                          novoStatus: 'PENDENTE',
                          motivo: motivoAlteracao,
                          usuario: currentUser.nome
                      };
                      solicitacaoData.historico = [
                          ...(solicitacao.historico || []),
                          historicoItem
                      ];
                  }

                  await updateDoc(doc(db, "solicitacoesCompra", editingId), solicitacaoData);
                  showNotification('Solicitação atualizada com sucesso!', 'success');
              } else {
                  solicitacaoData.numero = await generateRequestNumber();
                  solicitacaoData.status = 'PENDENTE';

                  await addDoc(collection(db, "solicitacoesCompra"), solicitacaoData);
                  showNotification('Solicitação criada com sucesso!', 'success');
              }

              closeModal('requestModal');
              await loadInitialData();
          } catch (error) {
              console.error("Erro ao salvar solicitação:", error);
              showNotification('Erro ao salvar solicitação: ' + error.message, 'error');
          }
      };

      window.approveRequest = async function(requestId) {
        if (currentUser.nivel < 3 && !userPermissions.includes('aprovar_solicitacoes')) {
          alert('Você não tem permissão para aprovar solicitações.');
              return;
        }

        if (confirm('Confirma a aprovação desta solicitação?')) {
          try {
            // Atualizar status da solicitação
            await updateDoc(doc(db, "solicitacoesCompra", requestId), {
            status: 'APROVADA',
              dataAprovacao: Timestamp.now(),
              aprovadoPor: currentUser.nome
          });

          // ===================================================================
          // CRIAÇÃO AUTOMÁTICA DE COTAÇÃO (INTEGRIDADE DE DADOS)
          // ===================================================================
          try {
              const result = await DataIntegrityService.createCotacaoFromSolicitacao(requestId);
              if (result.success) {
                  alert(`Solicitação aprovada e cotação ${result.numeroCotacao} criada automaticamente!`);
              }
          } catch (cotacaoError) {
              console.warn('Erro ao criar cotação automática:', cotacaoError);
              alert('Solicitação aprovada com sucesso! (Cotação deve ser criada manualmente)');
          }

          await loadInitialData();
            await loadRequests();
        } catch (error) {
            console.error("Erro ao aprovar solicitação:", error);
            showErrorMessage("Erro ao aprovar solicitação.");
          }
        }
      };

      window.rejectRequest = async function(requestId) {
        if (currentUser.nivel < 3 && !userPermissions.includes('aprovar_solicitacoes')) {
          alert('Você não tem permissão para rejeitar solicitações.');
          return;
        }

        const motivo = prompt('Informe o motivo da rejeição:');
        if (motivo) {
          try {
            await updateDoc(doc(db, "solicitacoesCompra", requestId), {
              status: 'REJEITADA',
              dataRejeicao: Timestamp.now(),
              rejeitadoPor: currentUser.nome,
              motivoRejeicao: motivo
            });
            await loadInitialData();
            await loadRequests();
            alert('Solicitação rejeitada com sucesso!');
          } catch (error) {
            console.error("Erro ao rejeitar solicitação:", error);
            showErrorMessage("Erro ao rejeitar solicitação.");
          }
        }
      };

      window.selectItemsForQuotation = async function(requestId) {
        try {
          if (!requestId) {
            throw new Error('ID da solicitação inválido ou ausente');
          }

            const solicitacao = solicitacoes.find(s => s.id === requestId);
            if (!solicitacao) {
            throw new Error('Solicitação não encontrada');
          }

          // Check for existing quotations
          const cotacoesSnap = await getDocs(collection(db, "cotacoes"));
          const hasQuotation = cotacoesSnap.docs.some(doc => doc.data().solicitacaoId === requestId);

          if (hasQuotation) {
            throw new Error('Esta solicitação já possui uma cotação.');
          }

          // Check permissions
          if (currentUser.nivel < 3 && !userPermissions.includes('gerar_cotacoes')) {
            throw new Error('Você não tem permissão para gerar cotações.');
          }

          // Check status
          if (solicitacao.status !== 'APROVADA') {
            throw new Error('Apenas solicitações aprovadas podem gerar cotações.');
          }

          // Process items with fallbacks
          const itemsForModal = solicitacao.itens.map(item => ({
            ...item,
            quantidadeCompra: item.quantidadeCompra ?? item.quantidadeInterna ?? item.quantidade,
            unidadeCompra: item.unidadeCompra ?? item.unidadeInterna ?? item.unidade
          }));

          // Create and show modal
          const modal = createSelectItemsModal({ ...solicitacao, itens: itemsForModal });
          modal.style.display = 'block';

        } catch (error) {
          console.error('Erro ao gerar cotação:', error);
          showNotification(error.message, 'error');
        }
      };

      function createSelectItemsModal(solicitacao) {
  // Remove modal antigo se existir
  const oldModal = document.getElementById('selectItemsModal');
  if (oldModal) oldModal.remove();
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.id = 'selectItemsModal';
      // ...
        document.body.appendChild(modal);

        const content = document.createElement('div');
        content.className = 'modal-content';

        const closeButton = document.createElement('span');
        closeButton.className = 'close-button';
        closeButton.textContent = '×';
        closeButton.onclick = () => modal.style.display = 'none';
        content.appendChild(closeButton);

        const title = document.createElement('h2');
        title.textContent = 'Selecione Itens para Cotação';
        content.appendChild(title);

        // Add center cost information
        const centerCostInfo = document.createElement('div');
        centerCostInfo.style.margin = '10px 0';
        centerCostInfo.style.padding = '10px';
        centerCostInfo.style.backgroundColor = '#f0f3f6';
        centerCostInfo.style.borderRadius = '4px';

        centerCostInfo.innerHTML = `
          <strong>Centro de Custo:</strong> ${centrosCusto.find(cc => cc.id === solicitacao.centroCustoId)?.descricao || 'Não especificado'}
        `;
        content.appendChild(centerCostInfo);

        const itemsContainer = document.createElement('div');
        itemsContainer.style.padding = '20px';
        itemsContainer.innerHTML = `
          <table class="items-table">
            <thead>
              <tr>
                <th>Selecionar</th>
                <th>Código</th>
                <th>Descrição</th>
                <th>Quantidade</th>
                <th>Unidade</th>
              </tr>
            </thead>
            <tbody id="selectQuotationItemsTableBody"></tbody>
          </table>
        `;
        content.appendChild(itemsContainer);

        const tableBody = itemsContainer.querySelector('#selectQuotationItemsTableBody');
        solicitacao.itens.forEach(item => {
          const quantidadeExibicao = item.quantidadeCompra ?? item.quantidadeInterna ?? item.quantidade;
          const unidadeExibicao = item.unidadeCompra ?? item.unidadeInterna ?? item.unidade;
          const isComplete = (quantidadeExibicao !== undefined && quantidadeExibicao !== null) && 
                             (unidadeExibicao !== undefined && unidadeExibicao !== null);

          const row = document.createElement('tr');
          row.innerHTML = `
            <td>
              <input 
                type="checkbox" 
                class="item-checkbox" 
                id="select-item-${item.codigo}" 
                value="${item.codigo}" 
                ${isComplete ? 'checked' : 'disabled'}
              >
            </td>
            <td>${item.codigo || 'N/A'}</td>
            <td>
              <label for="select-item-${item.codigo}">
                ${item.descricao || 'N/A'}
                ${!isComplete ? ' - <span style="color:red">Dados incompletos</span>' : ''}
              </label>
            </td>
            <td>${quantidadeExibicao !== undefined ? quantidadeExibicao : 'N/A'}</td>
            <td>${unidadeExibicao || 'UN'}</td>
          `;
          tableBody.appendChild(row);
        });

        const footer = document.createElement('div');
        footer.className = 'modal-footer';

        const generateButton = document.createElement('button');
        generateButton.className = 'btn-primary';
        generateButton.textContent = 'Gerar Cotação';
        generateButton.onclick = async () => {
          const selectedItems = Array.from(document.querySelectorAll('#selectQuotationItemsTableBody .item-checkbox:checked'))
            .map(checkbox => {
              const codigo = checkbox.value;
              return solicitacao.itens.find(item => item.codigo === codigo);
          });

          if (selectedItems.length === 0) {
            showNotification('Selecione pelo menos um item para gerar a cotação.', 'warning');
            return;
          }

          try {
            await createQuotation(solicitacao.id, selectedItems);
            modal.style.display = 'none';
          } catch (error) {
            console.error('Erro ao criar cotação:', error);
          }
        };
        footer.appendChild(generateButton);

        const cancelButton = document.createElement('button');
        cancelButton.className = 'btn-secondary';
        cancelButton.textContent = 'Cancelar';
        cancelButton.onclick = () => modal.style.display = 'none';
        footer.appendChild(cancelButton);

        content.appendChild(footer);
        modal.appendChild(content);
        document.body.appendChild(modal);
        return modal;
      }

      async function createQuotation(requestId, selectedItems) {
        try {
          if (!requestId) {
            throw new Error('ID da solicitação inválido ou ausente');
          }

          const solicitacao = solicitacoes.find(s => s.id === requestId);
          if (!solicitacao) {
            throw new Error('Solicitação não encontrada');
          }

          // Validação de campo obrigatório
          if (!solicitacao.centroCustoId) {
            throw new Error('A solicitação não possui um centro de custo associado');
          }

          // Verificar se já existe cotação
          const cotacoesSnap = await getDocs(collection(db, "cotacoes"));
          const hasQuotation = cotacoesSnap.docs.some(doc => doc.data().solicitacaoId === requestId);
          if (hasQuotation) {
            throw new Error('Esta solicitação já possui uma cotação');
          }

          // Validação estrita dos itens
          const itensParaCotacao = selectedItems.map(item => {
            if (!item.codigo || !item.descricao) {
              throw new Error(`Item está faltando código ou descrição`);
            }
            const finalQuantidade = item.quantidadeCompra ?? item.quantidadeInterna ?? item.quantidade;
            const finalUnidade = item.unidadeCompra ?? item.unidadeInterna ?? item.unidade;
            if (finalQuantidade === undefined || finalQuantidade === null) {
              throw new Error(`Quantidade inválida para item ${item.codigo}`);
            }
            if (!finalUnidade) {
              throw new Error(`Unidade inválida para item ${item.codigo}`);
            }
            return {
              codigo: item.codigo,
              descricao: item.descricao,
              quantidade: finalQuantidade,
              unidade: finalUnidade,
              produtoId: item.produtoId || null,
              fatorConversao: item.fatorConversao || 1
            };
          });

          // Remover campos undefined dos itens
          function removeUndefined(obj) {
            Object.keys(obj).forEach(key => {
              if (obj[key] === undefined) delete obj[key];
            });
            return obj;
          }
          const cleanItens = itensParaCotacao.map(removeUndefined);

          // Log de depuração
          console.log('Dados da cotação antes de enviar:', {
            cleanItens,
            solicitacaoId: requestId,
            centroCustoId: solicitacao.centroCustoId
          });

          const cotacaoData = {
            numero: await generateQuotationNumber(),
            solicitacaoId: requestId,
            centroCustoId: solicitacao.centroCustoId,
            itens: cleanItens,
            status: 'PENDENTE',
            dataCriacao: Timestamp.now(),
            criadoPor: currentUser.nome
          };

          await addDoc(collection(db, 'cotacoes'), cotacaoData);
          await updateDoc(doc(db, 'solicitacoesCompra', requestId), {
            status: 'EM_COTACAO',
            geradoPor: currentUser.nome
          });

          showNotification('Cotação gerada com sucesso!', 'success');
          await loadInitialData();
          await loadRequests();
          return true;
        } catch (error) {
          console.error('Erro detalhado ao gerar cotação:', {
            error,
            requestId,
            selectedItems
          });
          showNotification(`Erro detalhado: ${error.message}. Verifique os dados dos itens.`, 'error');
          throw error;
        }
      }

      async function generateQuotationNumber() {
        const date = new Date();
        const year = date.getFullYear().toString().substr(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const docRef = doc(collection(db, 'cotacoes')); // Cria referência para novo documento
        const docId = docRef.id; // Obtém ID automático
        const sequence = docId.slice(0, 6); // Usa primeiros 6 caracteres do ID
        const numero = `CT${year}${month}${sequence}`;
        console.log(`Número da cotação gerado: ${numero}`);
        return numero;
      }
      window.deleteRequest = async function() {
          const requestId = document.getElementById('deleteButton').dataset.requestId;
          if (!requestId) return;

          try {
            // Check for related quotes
            const quotesSnapshot = await getDocs(collection(db, "cotacoes"));
            const relatedQuotes = quotesSnapshot.docs.filter(doc => doc.data().solicitacaoId === requestId);

            if (relatedQuotes.length > 0) {
              if (confirm(`Esta solicitação possui ${relatedQuotes.length} cotação(ões). Deseja excluir as cotações e a solicitação?`)) {
                // Delete all related quotes first
                for (const quote of relatedQuotes) {
                  await deleteDoc(doc(db, "cotacoes", quote.id));
                }
              } else {
                return;
              }
            }

            // Then delete the request
            if (confirm('Confirma a exclusão desta solicitação? Esta ação não pode ser desfeita.')) {
              await deleteDoc(doc(db, "solictacoesCompra", requestId));
              alert('Solicitação e cotações relacionadas foram excluídas com sucesso!');
              closeModal('requestModal');
              await loadInitialData();
              await loadRequests();
            }
          } catch (error) {
            console.error("Erro ao excluir solicitação:", error);
            showErrorMessage("Erro ao excluir solicitação: " + error.message);
          }
        };

        window.updateLinkedItemsButton = function() {
          const fornecedorId = document.getElementById('fornecedor').value;
          const viewLinkedItemsBtn = document.getElementById('viewLinkedItemsBtn');
          viewLinkedItemsBtn.style.display = fornecedorId ? 'block' : 'none';
        };

        window.filterLinkedItems = function(searchText) {
          const rows = document.querySelectorAll('#linkedItemsTableBody tr');
          const search = searchText.toLowerCase();

          rows.forEach(row => {
            const descricao = row.children[2].textContent.toLowerCase();
            row.style.display = descricao.includes(search) ? '' : 'none';
          });
        };

        window.viewLinkedItems = function() {
          const fornecedorId = document.getElementById('fornecedor').value;
          const tableBody = document.getElementById('linkedItemsTableBody');
          tableBody.innerHTML = '';

          produtos.forEach(produto => {
            const vinculo = window.produtosFornecedores.find(v => v.produtoId === produto.id && v.fornecedorId === fornecedorId);
            if (vinculo) {
              const row = document.createElement('tr');
              row.innerHTML = `
                <td>
                  <input type="checkbox" class="item-select" data-codigo="${produto.codigo}">
                </td>
                <td>${produto.codigo}</td>
                <td>${produto.descricao}</td>
                <td>${produto.unidade}</td>
                <td>
                  <input type="number" class="item-qty" min="0.001" step="0.001" style="width: 100px;">
                </td>
              `;
              tableBody.appendChild(row);
            }
          });

          document.getElementById('linkedItemsModal').style.display = 'block';
        };

        window.addSelectedItems = function() {
          const selectedRows = document.querySelectorAll('#linkedItemsTableBody tr');
          let itemsAdded = 0;

          selectedRows.forEach(row => {
            const checkbox = row.querySelector('.item-select');
            const quantidade = row.querySelector('.item-qty').value;

            if (checkbox.checked && quantidade > 0) {
              const codigo = checkbox.dataset.codigo;
              const newRow = addItem();
              const codigoInput = newRow.querySelector('.item-codigo');
              codigoInput.value = codigo;
              searchProduct(codigoInput);
              newRow.querySelector('.item-quantidade').value = quantidade;
              updateConversion(newRow);
              itemsAdded++;
            }
          });

          if (itemsAdded > 0) {
            closeModal('linkedItemsModal');
          } else {
            alert('Selecione pelo menos um item e informe a quantidade.');
          }
        };

        window.openProductSearch = function(button) {
          const row = button.closest('tr');
          const codigoInput = row.querySelector('.item-codigo');
          const codigo = prompt('Digite o código do produto:');
          if (codigo) {
            codigoInput.value = codigo;
            searchProduct(codigoInput);
          }
        };

        function getStatusClass(solicitacao) {
          const now = Date.now() / 1000;
          const dataCriacao = solicitacao.dataCriacao.seconds;
          const prazo = solicitacao.prazoDias || 7;
          const dataLimite = dataCriacao + prazo * 86400;

          if (solicitacao.status === 'PENDENTE' && now > dataLimite) {
            return 'status-critical';
          } else if (solicitacao.status === 'PENDENTE' && now + 86400 > dataLimite) {
            return 'status-warning';
          } else {
            return 'status-normal';
          }
        }
        window.filterTable = function() {
            const rows = document.querySelectorAll('#requestsTableBody tr');
            const filters = {};

            document.querySelectorAll('.filter-input').forEach(input => {
              filters[input.dataset.column] = input.value.toLowerCase();
            });

            rows.forEach(row => {
              let show = true;
              const cells = row.getElementsByTagName('td');

              Object.keys(filters).forEach((column, index) => {
                  const cellText = cells[index].textContent.toLowerCase();
                  if (filters[column] && !cellText.includes(filters[column])) {
                    show = false;
                }
              });

              row.style.display = show ? '' : 'none';
            });
          };

          let currentSort = { column: '', direction: 'asc' };

          window.sortTable = function(column) {
            const tbody = document.getElementById('requestsTableBody');
            const rows = Array.from(tbody.getElementsByTagName('tr'));

            if (currentSort.column === column) {
              currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
              currentSort = { column, direction: 'asc' };
            }

            rows.sort((a, b) => {
              let aValue = a.cells[getColumnIndex(column)].textContent;
              let bValue = b.cells[getColumnIndex(column)].textContent;

              if (column === 'data') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
              }

              if (aValue < bValue) return currentSort.direction === 'asc' ? -1 : 1;
              if (aValue > bValue) return currentSort.direction === 'asc' ? 1 :-1;
              return 0;
            });

            rows.forEach(row => tbody.appendChild(row));
          };

          function getColumnIndex(column) {
            const columns = {
              'numero': 0,
              'data': 1,
              'solicitante': 2,
              'departamento': 3,
              'centroCusto': 4,
              'fornecedor': 5,
              'itens': 6,
              'prioridade': 7,
              'status': 8
            };
            return columns[column] || 0;
          }

          window.executeBatchAction = async function() {
            const action = document.getElementById('batchAction').value;
            if (!action) {
              showNotification('Selecione uma ação para executar.', 'warning');
              return;
            }

            const selectedRows = document.querySelectorAll('.request-checkbox:checked');
            if (selectedRows.length === 0) {
              showNotification('Selecione pelo menos uma solicitação.', 'warning');
              return;
            }

            const confirmation = confirm(`Deseja realmente ${action} as ${selectedRows.length} solicitações selecionadas?`);
            if (!confirmation) return;

            try {
              for (const row of selectedRows) {
                const solicitacaoId = row.dataset.id;
                const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);

                if (!solicitacao) continue;

                // Verificar permissões
                if (solicitacao.origem === 'MRP' && !userPermissions.includes('editar_solicitacoes_mrp')) {
                  showNotification(`Você não tem permissão para ${action} solicitações do MRP.`, 'error');
                  continue;
                }

                const novoStatus = action === 'aprovar' ? 'APROVADA' :
                                  action === 'rejeitar' ? 'REJEITADA' :
                                  'CANCELADA';

                const historicoItem = {
                  data: Timestamp.now(),
                  statusAnterior: solicitacao.status,
                  novoStatus: novoStatus,
                  motivo: `Ação em lote: ${action}`,
                  usuario: currentUser.nome
                };

                await updateDoc(doc(db, "solicitacoesCompra", solicitacaoId), {
                  status: novoStatus,
                  historico: [...(solicitacao.historico || []), historicoItem]
                });
              }

              showNotification('Ações executadas com sucesso!', 'success');
              await loadInitialData();
            } catch (error) {
              console.error('Erro ao executar ações em lote:', error);
              showNotification('Erro ao executar ações em lote: ' + error.message, 'error');
            }
          };

          // Add this function to handle approval from details modal
          window.approveRequestFromDetails = function() {
            const requestId = document.getElementById('detailsRequestNumber').dataset.requestId;
            if (requestId) {
              approveRequest(requestId);
              closeModal('detailsModal');
            }
          };

          // Add these functions to handle the buttons
          window.selectItemsForQuotationFromDetails = function() {
            const requestId = document.getElementById('detailsRequestNumber').dataset.requestId;
            if (requestId) {
              selectItemsForQuotation(requestId);
              closeModal('detailsModal');
            }
          };

          // Add helper function to check if request has quotation
          async function checkIfRequestHasQuotation(requestId) {
            try {
              const cotacoesSnap = await getDocs(collection(db, "cotacoes"));
              return cotacoesSnap.docs.some(doc => doc.data().solicitacaoId === requestId);
            } catch (error) {
              console.error("Erro ao verificar cotações:", error);
              return false;
            }
          }

        // Fix the 'populateFornecedores' function

    </script>
  </body>
</html>