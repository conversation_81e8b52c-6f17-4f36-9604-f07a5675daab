<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🩺 Diagnóstico do Sistema - FUNCIONANDO</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .diagnostic-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }
        .diagnostic-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .diagnostic-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .test-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
            border-color: #e74c3c;
        }
        .test-card.running {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .test-card.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .test-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        .test-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .test-result {
            font-weight: bold;
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .summary-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 5px solid #e74c3c;
        }
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .details-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .issue-item {
            background: white;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .issue-item.warning {
            border-left-color: #ffc107;
        }
        .issue-item.info {
            border-left-color: #17a2b8;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="diagnostic-header">
            <h1>🩺 Diagnóstico Universal do Sistema</h1>
            <p>Análise completa da integridade dos dados e funcionamento do sistema</p>
            <p><strong>Status:</strong> <span id="statusGeral">Aguardando diagnóstico</span></p>
        </div>

        <!-- Controles Principais -->
        <div class="diagnostic-section">
            <h3>🎛️ Controles de Diagnóstico</h3>
            <div style="text-align: center; margin: 20px 0;">
                <button onclick="executarDiagnosticoCompleto()" class="btn btn-primary">🩺 Diagnóstico Completo</button>
                <button onclick="diagnosticoRapido()" class="btn btn-success">⚡ Diagnóstico Rápido</button>
                <button onclick="verificarIntegridade()" class="btn btn-warning">🔍 Verificar Integridade</button>
                <button onclick="limparResultados()" class="btn btn-secondary">🗑️ Limpar Resultados</button>
            </div>
            
            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill">0%</div>
            </div>
        </div>

        <!-- Resumo dos Resultados -->
        <div class="summary-grid" id="summaryGrid" style="display: none;"></div>

        <!-- Testes Individuais -->
        <div class="test-grid" id="testGrid">
            <div class="test-card" onclick="testarConexao()">
                <div class="test-icon">🔗</div>
                <div class="test-title">Conexão com Banco</div>
                <div class="test-description">Verifica conectividade com Firebase</div>
                <div class="test-result" id="result-conexao"></div>
            </div>

            <div class="test-card" onclick="verificarProdutos()">
                <div class="test-icon">📦</div>
                <div class="test-title">Cadastro de Produtos</div>
                <div class="test-description">Verifica integridade dos produtos</div>
                <div class="test-result" id="result-produtos"></div>
            </div>

            <div class="test-card" onclick="verificarEstoques()">
                <div class="test-icon">🏪</div>
                <div class="test-title">Estoques</div>
                <div class="test-description">Verifica consistência dos estoques</div>
                <div class="test-result" id="result-estoques"></div>
            </div>

            <div class="test-card" onclick="verificarQualidade()">
                <div class="test-icon">✅</div>
                <div class="test-title">Estoque Qualidade</div>
                <div class="test-description">Verifica itens na qualidade</div>
                <div class="test-result" id="result-qualidade"></div>
            </div>

            <div class="test-card" onclick="buscarItem101133()">
                <div class="test-icon">🔍</div>
                <div class="test-title">Buscar Item 101133</div>
                <div class="test-description">Análise específica do motor WEG</div>
                <div class="test-result" id="result-item101133"></div>
            </div>

            <div class="test-card" onclick="verificarSaldosUndefined()">
                <div class="test-icon">❓</div>
                <div class="test-title">Saldos Undefined</div>
                <div class="test-description">Busca saldos com valores undefined</div>
                <div class="test-result" id="result-undefined"></div>
            </div>
        </div>

        <!-- Log de Atividades -->
        <div class="log-area" id="logArea"></div>

        <!-- Detalhes dos Problemas -->
        <div class="details-section" id="detailsSection" style="display: none;">
            <h3>📋 Detalhes dos Problemas Encontrados</h3>
            <div id="problemDetails"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            query,
            where,
            orderBy,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Tornar variáveis globais
        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.limit = limit;

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let diagnosticResults = {};
        let problemsFound = [];

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            progressFill.style.width = percentage + '%';
            progressFill.textContent = text || `${percentage}%`;
        }

        function setTestResult(testId, status, message) {
            const resultElement = document.getElementById(`result-${testId}`);
            const cardElement = resultElement.closest('.test-card');
            
            cardElement.className = `test-card ${status}`;
            resultElement.className = `test-result ${status}`;
            resultElement.textContent = message;
            
            diagnosticResults[testId] = { status, message };
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de diagnóstico universal carregado', 'info');
        };

        // Tornar funções globais
        window.diagnosticResults = diagnosticResults;
        window.problemsFound = problemsFound;
        window.currentUser = currentUser;
    </script>

    <script>
        // Funções globais para os testes
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');

            progressBar.style.display = 'block';
            progressFill.style.width = percentage + '%';
            progressFill.textContent = text || `${percentage}%`;
        }

        function setTestResult(testId, status, message) {
            const resultElement = document.getElementById(`result-${testId}`);
            const cardElement = resultElement.closest('.test-card');

            cardElement.className = `test-card ${status}`;
            resultElement.className = `test-result ${status}`;
            resultElement.textContent = message;

            window.diagnosticResults[testId] = { status, message };
        }

        window.executarDiagnosticoCompleto = async function() {
            log('🩺 Iniciando diagnóstico completo do sistema...', 'info');
            document.getElementById('statusGeral').textContent = 'Executando diagnóstico completo...';
            
            problemsFound = [];
            const tests = [
                'conexao', 'colecoes', 'produtos', 'estoques', 
                'qualidade', 'movimentacoes', 'solicitacoes', 
                'orfaos', 'duplicatas', 'inconsistencias'
            ];
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                const progress = Math.round(((i + 1) / tests.length) * 100);
                
                updateProgress(progress, `Testando ${test}...`);
                
                try {
                    switch (test) {
                        case 'conexao':
                            await testarConexaoBanco();
                            break;
                        case 'colecoes':
                            await testarColecoes();
                            break;
                        case 'produtos':
                            await testarProdutos();
                            break;
                        case 'estoques':
                            await testarEstoques();
                            break;
                        case 'qualidade':
                            await testarQualidade();
                            break;
                        case 'movimentacoes':
                            await testarMovimentacoes();
                            break;
                        case 'solicitacoes':
                            await testarSolicitacoes();
                            break;
                        case 'orfaos':
                            await testarOrfaos();
                            break;
                        case 'duplicatas':
                            await testarDuplicatas();
                            break;
                        case 'inconsistencias':
                            await testarInconsistencias();
                            break;
                    }
                    
                    // Pequeno delay para visualização
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    log(`❌ Erro no teste ${test}: ${error.message}`, 'error');
                    setTestResult(test, 'error', `Erro: ${error.message}`);
                }
            }
            
            updateProgress(100, 'Diagnóstico concluído');
            setTimeout(() => {
                document.getElementById('progressBar').style.display = 'none';
            }, 2000);
            
            // Mostrar resumo
            mostrarResumo();
            mostrarProblemas();
            
            log('✅ Diagnóstico completo finalizado', 'success');
            document.getElementById('statusGeral').textContent = 'Diagnóstico concluído';
        };

        window.diagnosticoRapido = async function() {
            log('⚡ Executando diagnóstico rápido...', 'info');
            
            try {
                await testarConexaoBanco();
                await testarColecoes();
                await testarProdutos();
                
                log('✅ Diagnóstico rápido concluído', 'success');
                mostrarResumo();
            } catch (error) {
                log(`❌ Erro no diagnóstico rápido: ${error.message}`, 'error');
            }
        };

        window.verificarIntegridade = async function() {
            log('🔍 Verificando integridade dos dados...', 'info');
            
            try {
                await testarOrfaos();
                await testarDuplicatas();
                await testarInconsistencias();
                
                log('✅ Verificação de integridade concluída', 'success');
                mostrarProblemas();
            } catch (error) {
                log(`❌ Erro na verificação de integridade: ${error.message}`, 'error');
            }
        };

        window.limparResultados = function() {
            // Limpar todos os resultados
            const testCards = document.querySelectorAll('.test-card');
            testCards.forEach(card => {
                card.className = 'test-card';
                const result = card.querySelector('.test-result');
                if (result) {
                    result.textContent = '';
                    result.className = 'test-result';
                }
            });

            // Limpar outras seções
            document.getElementById('summaryGrid').style.display = 'none';
            document.getElementById('detailsSection').style.display = 'none';
            document.getElementById('logArea').innerHTML = '';

            diagnosticResults = {};
            problemsFound = [];

            log('🗑️ Resultados limpos', 'info');
            document.getElementById('statusGeral').textContent = 'Aguardando diagnóstico';
        };

        // Mover funções para escopo global será feito no script separado
    </script>

    <script>
        // Funções de teste globais funcionais
        async function testarConexao() {
            log('🔗 Testando conexão com banco...', 'info');
            try {
                const testSnap = await window.getDocs(window.query(window.collection(window.db, "produtos"), window.limit(1)));
                setTestResult('conexao', 'success', 'Conexão OK');
                log('✅ Conexão com Firebase estabelecida', 'success');
            } catch (error) {
                setTestResult('conexao', 'error', `Erro: ${error.message}`);
                log(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        async function verificarProdutos() {
            log('📦 Verificando produtos...', 'info');
            try {
                const produtosSnap = await window.getDocs(window.collection(window.db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                produtos.forEach(produto => {
                    if (!produto.codigo) problemas++;
                    if (!produto.descricao) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setTestResult('produtos', status, `${produtos.length} produtos, ${problemas} problemas`);
                log(`📦 ${produtos.length} produtos verificados`, 'success');
            } catch (error) {
                setTestResult('produtos', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }

        async function verificarEstoques() {
            log('🏪 Verificando estoques...', 'info');
            try {
                const estoquesSnap = await window.getDocs(window.collection(window.db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                estoques.forEach(estoque => {
                    if (estoque.saldo === undefined || estoque.saldo === null) problemas++;
                    if (estoque.saldo < 0) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setTestResult('estoques', status, `${estoques.length} registros, ${problemas} problemas`);
                log(`🏪 ${estoques.length} estoques verificados`, 'success');
            } catch (error) {
                setTestResult('estoques', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }

        async function verificarQualidade() {
            log('✅ Verificando qualidade...', 'info');
            try {
                const qualidadeSnap = await window.getDocs(window.collection(window.db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                qualidade.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) problemas++;
                    if (!item.produtoId) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setTestResult('qualidade', status, `${qualidade.length} itens, ${problemas} problemas`);
                log(`✅ ${qualidade.length} itens verificados`, 'success');
            } catch (error) {
                setTestResult('qualidade', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }

        async function buscarItem101133() {
            log('🔍 Buscando item 101133...', 'info');
            try {
                // Buscar produto
                const produtosSnap = await window.getDocs(window.collection(window.db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produto = produtos.find(p => p.codigo === '101133');

                if (!produto) {
                    setTestResult('item101133', 'error', 'Produto não encontrado');
                    log('❌ Produto 101133 não encontrado', 'error');
                    return;
                }

                // Buscar na qualidade
                const qualidadeSnap = await window.getDocs(window.collection(window.db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const itemQualidade = qualidade.find(q => q.produtoId === produto.id);

                // Buscar no estoque
                const estoquesSnap = await window.getDocs(window.collection(window.db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const itemEstoque = estoques.find(e => e.produtoId === produto.id);

                let resultado = '';
                if (itemQualidade) {
                    resultado += `Qualidade: ${itemQualidade.saldo || 'undefined'} `;
                }
                if (itemEstoque) {
                    resultado += `Estoque: ${itemEstoque.saldo || 0}`;
                }

                setTestResult('item101133', 'success', resultado || 'Não encontrado');
                log(`✅ Item 101133 analisado: ${resultado}`, 'success');

            } catch (error) {
                setTestResult('item101133', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }

        async function verificarSaldosUndefined() {
            log('❓ Verificando saldos undefined...', 'info');
            try {
                const [estoquesSnap, qualidadeSnap] = await Promise.all([
                    window.getDocs(window.collection(window.db, "estoques")),
                    window.getDocs(window.collection(window.db, "estoqueQualidade"))
                ]);

                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let undefinedEstoque = 0;
                let undefinedQualidade = 0;

                estoques.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) undefinedEstoque++;
                });

                qualidade.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) undefinedQualidade++;
                });

                const total = undefinedEstoque + undefinedQualidade;
                const status = total === 0 ? 'success' : 'warning';
                setTestResult('undefined', status, `${total} saldos undefined`);
                log(`❓ ${total} saldos undefined encontrados`, total > 0 ? 'warning' : 'success');

            } catch (error) {
                setTestResult('undefined', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }

        // Função de diagnóstico completo simplificado
        async function executarDiagnosticoCompleto() {
            log('🩺 Iniciando diagnóstico completo...', 'info');
            document.getElementById('statusGeral').textContent = 'Executando diagnóstico...';

            await testarConexao();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarProdutos();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarEstoques();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarQualidade();
            await new Promise(resolve => setTimeout(resolve, 500));
            await buscarItem101133();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarSaldosUndefined();

            document.getElementById('statusGeral').textContent = 'Diagnóstico concluído';
            log('✅ Diagnóstico completo finalizado', 'success');
        }

        // Função de limpeza
        function limparResultados() {
            const testCards = document.querySelectorAll('.test-card');
            testCards.forEach(card => {
                card.className = 'test-card';
                const result = card.querySelector('.test-result');
                if (result) {
                    result.textContent = '';
                    result.className = 'test-result';
                }
            });

            document.getElementById('logArea').innerHTML = '';
            log('🗑️ Resultados limpos', 'info');
            document.getElementById('statusGeral').textContent = 'Aguardando diagnóstico';
        }

        // Todas as funções duplicadas foram removidas - usando apenas as versões funcionais acima
    </script>
</body>
</html>

        window.testarQualidade = async function() {
            log('✅ Testando estoque qualidade...', 'info');
            try {
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                qualidade.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) problemas++;
                    if (!item.produtoId) problemas++;
                });

                const status = problemas === 0 ? 'success' : problemas < 5 ? 'warning' : 'error';
                setTestResult('qualidade', status, `${qualidade.length} itens, ${problemas} problemas`);
                log(`✅ ${qualidade.length} itens na qualidade, ${problemas} problemas`, 'info');
            } catch (error) {
                setTestResult('qualidade', 'error', `Erro: ${error.message}`);
            }
        };

        window.testarMovimentacoes = async function() {
            log('🔄 Testando movimentações...', 'info');
            try {
                const movSnap = await getDocs(query(collection(db, "movimentacoesEstoque"), limit(100)));
                const movimentacoes = movSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                movimentacoes.forEach(mov => {
                    if (!mov.produtoId) problemas++;
                    if (!mov.tipo) problemas++;
                    if (mov.quantidade === undefined) problemas++;
                });

                const status = problemas === 0 ? 'success' : problemas < 5 ? 'warning' : 'error';
                setTestResult('movimentacoes', status, `${movimentacoes.length} movimentações, ${problemas} problemas`);
                log(`🔄 ${movimentacoes.length} movimentações verificadas, ${problemas} problemas`, 'info');
            } catch (error) {
                setTestResult('movimentacoes', 'error', `Erro: ${error.message}`);
            }
        };

        window.testarSolicitacoes = async function() {
            log('📋 Testando solicitações de compra...', 'info');
            try {
                const solSnap = await getDocs(query(collection(db, "solicitacoesCompra"), limit(50)));
                const solicitacoes = solSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                solicitacoes.forEach(sol => {
                    if (!sol.itens || !Array.isArray(sol.itens)) problemas++;
                    if (!sol.dataCriacao) problemas++;
                });

                const status = problemas === 0 ? 'success' : problemas < 5 ? 'warning' : 'error';
                setTestResult('solicitacoes', status, `${solicitacoes.length} solicitações, ${problemas} problemas`);
                log(`📋 ${solicitacoes.length} solicitações verificadas, ${problemas} problemas`, 'info');
            } catch (error) {
                setTestResult('solicitacoes', 'error', `Erro: ${error.message}`);
            }
        };

        window.testarOrfaos = async function() {
            log('🔍 Buscando registros órfãos...', 'info');
            try {
                const [produtosSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques"))
                ]);

                const produtos = produtosSnap.docs.map(doc => doc.id);
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let orfaos = 0;
                estoques.forEach(estoque => {
                    if (estoque.produtoId && !produtos.includes(estoque.produtoId)) {
                        orfaos++;
                    }
                });

                const status = orfaos === 0 ? 'success' : orfaos < 5 ? 'warning' : 'error';
                setTestResult('orfaos', status, `${orfaos} registros órfãos`);
                log(`🔍 ${orfaos} registros órfãos encontrados`, orfaos > 0 ? 'warning' : 'success');
            } catch (error) {
                setTestResult('orfaos', 'error', `Erro: ${error.message}`);
            }
        };

        window.testarDuplicatas = async function() {
            log('👥 Buscando duplicatas...', 'info');
            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const codigos = {};
                let duplicatas = 0;

                produtos.forEach(produto => {
                    if (produto.codigo) {
                        if (codigos[produto.codigo]) {
                            duplicatas++;
                        } else {
                            codigos[produto.codigo] = true;
                        }
                    }
                });

                const status = duplicatas === 0 ? 'success' : duplicatas < 3 ? 'warning' : 'error';
                setTestResult('duplicatas', status, `${duplicatas} duplicatas`);
                log(`👥 ${duplicatas} duplicatas encontradas`, duplicatas > 0 ? 'warning' : 'success');
            } catch (error) {
                setTestResult('duplicatas', 'error', `Erro: ${error.message}`);
            }
        };

        window.testarInconsistencias = async function() {
            log('⚠️ Buscando inconsistências...', 'info');
            try {
                const [produtosSnap, estoquesSnap, qualidadeSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "estoqueQualidade"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let inconsistencias = 0;

                // Verificar produtos sem código
                produtos.forEach(produto => {
                    if (!produto.codigo || produto.codigo.trim() === '') inconsistencias++;
                });

                // Verificar saldos negativos
                estoques.forEach(estoque => {
                    if (estoque.saldo < 0) inconsistencias++;
                });

                const status = inconsistencias === 0 ? 'success' : inconsistencias < 10 ? 'warning' : 'error';
                setTestResult('inconsistencias', status, `${inconsistencias} inconsistências`);
                log(`⚠️ ${inconsistencias} inconsistências encontradas`, inconsistencias > 0 ? 'warning' : 'success');
            } catch (error) {
                setTestResult('inconsistencias', 'error', `Erro: ${error.message}`);
            }
        };

        function mostrarResumo() {
            const summaryGrid = document.getElementById('summaryGrid');

            let totalTestes = Object.keys(diagnosticResults).length;
            let sucessos = Object.values(diagnosticResults).filter(r => r.status === 'success').length;
            let avisos = Object.values(diagnosticResults).filter(r => r.status === 'warning').length;
            let erros = Object.values(diagnosticResults).filter(r => r.status === 'error').length;

            summaryGrid.innerHTML = `
                <div class="summary-card">
                    <div class="summary-number">${totalTestes}</div>
                    <div>Testes Executados</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" style="color: #28a745;">${sucessos}</div>
                    <div>Sucessos</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" style="color: #ffc107;">${avisos}</div>
                    <div>Avisos</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" style="color: #dc3545;">${erros}</div>
                    <div>Erros</div>
                </div>
            `;

            summaryGrid.style.display = 'grid';
        }

        function mostrarProblemas() {
            if (problemsFound.length === 0) return;

            const detailsSection = document.getElementById('detailsSection');
            const problemDetails = document.getElementById('problemDetails');

            let html = '';
            problemsFound.forEach(problem => {
                html += `
                    <div class="issue-item ${problem.severity}">
                        <h4>${problem.title}</h4>
                        <p>${problem.description}</p>
                        ${problem.details ? `<pre>${problem.details}</pre>` : ''}
                    </div>
                `;
            });

            problemDetails.innerHTML = html;
            detailsSection.style.display = 'block';
        }
