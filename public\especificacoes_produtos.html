<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>📋 Especificações de Ensaios por Produto - Sistema de Qualidade</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f8f9fa;
      --border-color: #dee2e6;
      --text-color: #212529;
      --text-secondary: #6c757d;
      --success-color: #28a745;
      --success-hover: #218838;
      --danger-color: #dc3545;
      --danger-hover: #c82333;
      --warning-color: #ffc107;
      --info-color: #17a2b8;
      --header-bg: linear-gradient(135deg, #0854a0, #0a4d8c);
      --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --border-radius: 8px;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      color: var(--text-color);
      min-height: 100vh;
      margin: 0;
      padding: 10px;
      box-sizing: border-box;
    }

    * {
      box-sizing: border-box;
    }

    .container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 15px;
      position: relative;
      z-index: 1;
    }

    .header-icon {
      font-size: 32px;
      color: var(--warning-color);
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .header-subtitle {
      font-size: 14px;
      opacity: 0.9;
      margin-top: 5px;
    }

    .header-actions {
      position: relative;
      z-index: 1;
    }

    h2 {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    .spec-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .spec-table th,
    .spec-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .spec-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
    }

    .spec-table tr:hover {
      background-color: #f8f9fa;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .filters-section {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid var(--border-color);
    }

    .stats-display {
      background-color: white;
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid var(--border-color);
      font-size: 14px;
      color: var(--text-secondary);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .table-header h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .templates-section {
      margin-top: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid var(--border-color);
    }

    .templates-section h4 {
      margin-bottom: 15px;
      color: var(--primary-color);
    }

    .template-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .btn-template {
      background-color: #e9ecef;
      color: var(--text-color);
      border: 1px solid var(--border-color);
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }

    .btn-template:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-1px);
    }

    .priority-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .priority-normal {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .priority-importante {
      background-color: #fff3e0;
      color: #f57c00;
    }

    .priority-critica {
      background-color: #ffebee;
      color: #d32f2f;
    }

    .type-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      background-color: #f5f5f5;
      color: var(--text-secondary);
    }

    /* Novos estilos modernos */
    .main-content {
      padding: 20px;
    }

    .dashboard-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
    }

    .dashboard-card {
      background: white;
      border-radius: var(--border-radius);
      padding: 20px;
      box-shadow: var(--card-shadow);
      border-left: 4px solid var(--primary-color);
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .dashboard-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
    }

    .card-icon {
      font-size: 24px;
      color: var(--primary-color);
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
    }

    .card-value {
      font-size: 32px;
      font-weight: 700;
      color: var(--primary-color);
      margin: 10px 0;
    }

    .card-description {
      font-size: 14px;
      color: var(--text-secondary);
    }

    .search-section {
      background: white;
      border-radius: var(--border-radius);
      padding: 25px;
      margin-bottom: 25px;
      box-shadow: var(--card-shadow);
    }

    .search-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
    }

    .search-icon {
      font-size: 20px;
      color: var(--primary-color);
    }

    .search-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
    }

    .advanced-search {
      display: grid;
      grid-template-columns: 1fr 1fr auto;
      gap: 15px;
      align-items: end;
    }

    .input-group {
      position: relative;
    }

    .input-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 14px;
    }

    .input-group input,
    .input-group select {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 14px;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .input-group input:focus,
    .input-group select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: var(--border-radius);
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      text-decoration: none;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background: var(--primary-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(8, 84, 160, 0.3);
    }

    .btn-success {
      background: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background: var(--success-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .btn-danger {
      background: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background: var(--danger-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }

    .btn-secondary {
      background: var(--text-secondary);
      color: white;
    }

    .btn-secondary:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }

    .btn-outline {
      background: transparent;
      border: 2px solid var(--primary-color);
      color: var(--primary-color);
    }

    .btn-outline:hover {
      background: var(--primary-color);
      color: white;
    }

    .specifications-section {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .section-header {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 20px 25px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: between;
      align-items: center;
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .section-content {
      padding: 25px;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: var(--text-secondary);
    }

    .empty-state-icon {
      font-size: 64px;
      color: var(--border-color);
      margin-bottom: 20px;
    }

    .empty-state-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--text-color);
    }

    .empty-state-description {
      font-size: 16px;
      margin-bottom: 30px;
    }

    .modern-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--card-shadow);
    }

    .modern-table th {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      padding: 16px 20px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .modern-table td {
      padding: 16px 20px;
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .modern-table tr:hover {
      background-color: rgba(8, 84, 160, 0.05);
    }

    .modern-table tr:last-child td {
      border-bottom: none;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      backdrop-filter: blur(5px);
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: var(--border-radius);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      max-width: 700px;
      width: 95%;
      max-height: 85vh;
      overflow-y: auto;
    }

    .modal-header {
      background: var(--header-bg);
      color: white;
      padding: 20px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-title {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }

    .modal-close {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;
    }

    .modal-close:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .modal-body {
      padding: 25px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--text-color);
      font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 14px;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
    }

    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 15px;
    }

    .checkbox-group input[type="checkbox"] {
      width: auto;
      margin: 0;
    }

    .templates-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 25px;
    }

    .template-card {
      background: white;
      border: 2px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s;
    }

    .template-card:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .template-icon {
      font-size: 32px;
      margin-bottom: 10px;
      color: var(--primary-color);
    }

    .template-name {
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 5px;
    }

    .template-description {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: var(--border-radius);
      color: white;
      font-weight: 600;
      z-index: 1001;
      transform: translateX(400px);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      background: var(--success-color);
    }

    .notification.error {
      background: var(--danger-color);
    }

    .notification.warning {
      background: var(--warning-color);
      color: var(--text-color);
    }

    .notification.info {
      background: var(--info-color);
    }

    /* Responsividade */
    @media (max-width: 768px) {
      body {
        padding: 5px;
      }

      .main-content {
        padding: 15px;
      }

      .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .advanced-search {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .form-grid {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .templates-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
      }

      .modal-content {
        width: 98%;
        max-height: 95vh;
        top: 2.5vh;
        transform: translateX(-50%);
      }

      .header {
        padding: 20px 15px;
      }

      .header h1 {
        font-size: 22px;
      }

      .modern-table {
        font-size: 14px;
      }

      .modern-table th,
      .modern-table td {
        padding: 10px 8px;
      }
    }

    @media (max-width: 480px) {
      .dashboard-cards {
        grid-template-columns: 1fr;
      }

      .card-value {
        font-size: 24px;
      }

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .header-actions {
        width: 100%;
      }

      .btn {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Moderno -->
    <div class="header">
      <div class="header-content">
        <div class="header-icon">
          <i class="fas fa-clipboard-list"></i>
        </div>
        <div>
          <h1>Especificações de Ensaios por Produto</h1>
          <div class="header-subtitle">Sistema de Qualidade - Gestão de Critérios de Inspeção</div>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar ao Menu
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- Dashboard Cards -->
      <div class="dashboard-cards">
        <div class="dashboard-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-box"></i>
            </div>
            <h3 class="card-title">Total de Produtos</h3>
          </div>
          <div class="card-value" id="totalProducts">-</div>
          <div class="card-description">Produtos cadastrados no sistema</div>
        </div>

        <div class="dashboard-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-clipboard-check"></i>
            </div>
            <h3 class="card-title">Especificações Ativas</h3>
          </div>
          <div class="card-value" id="totalSpecs">-</div>
          <div class="card-description">Critérios de qualidade definidos</div>
        </div>

        <div class="dashboard-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="card-title">Critérios Críticos</h3>
          </div>
          <div class="card-value" id="criticalSpecs">-</div>
          <div class="card-description">Especificações de alta prioridade</div>
        </div>

        <div class="dashboard-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-camera"></i>
            </div>
            <h3 class="card-title">Com Evidência Fotográfica</h3>
          </div>
          <div class="card-value" id="photoSpecs">-</div>
          <div class="card-description">Critérios que requerem foto</div>
        </div>
      </div>

      <!-- Seção de Busca -->
      <div class="search-section">
        <div class="search-header">
          <div class="search-icon">
            <i class="fas fa-search"></i>
          </div>
          <h2 class="search-title">Buscar e Selecionar Produto</h2>
        </div>

        <div class="advanced-search">
          <div class="input-group">
            <label><i class="fas fa-search"></i> Buscar Produto</label>
            <input type="text" id="searchProduct" placeholder="Digite código ou descrição do produto..." onkeyup="filterProducts()">
          </div>

          <div class="input-group">
            <label><i class="fas fa-box"></i> Selecionar Produto</label>
            <select id="productSelect" onchange="loadProductSpecs()">
              <option value="">Escolha um produto...</option>
            </select>
          </div>

          <div class="input-group">
            <label><i class="fas fa-plus"></i> Ação</label>
            <button class="btn btn-success" onclick="showAddForm()" id="addSpecBtn" disabled>
              <i class="fas fa-plus"></i> Nova Especificação
            </button>
          </div>
        </div>
      </div>
      <!-- Seção de Especificações -->
      <div class="specifications-section" id="specsSection" style="display: none;">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-list-ul"></i>
            Especificações do Produto
          </h2>
          <button class="btn btn-success" onclick="showAddForm()">
            <i class="fas fa-plus"></i> Nova Especificação
          </button>
        </div>

        <div class="section-content">
          <div id="specsTableContainer">
            <!-- Tabela será inserida aqui -->
          </div>
        </div>
      </div>

      <!-- Templates Rápidos -->
      <div class="specifications-section" id="templatesSection" style="display: none;">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-magic"></i>
            Templates Rápidos
          </h2>
        </div>

        <div class="section-content">
          <div class="templates-grid">
            <div class="template-card" onclick="applyTemplate('dimensional')">
              <div class="template-icon">📏</div>
              <div class="template-name">Dimensional</div>
              <div class="template-description">Medições com tolerância em mm</div>
            </div>

            <div class="template-card" onclick="applyTemplate('visual')">
              <div class="template-icon">👁️</div>
              <div class="template-name">Inspeção Visual</div>
              <div class="template-description">Verificação visual com foto</div>
            </div>

            <div class="template-card" onclick="applyTemplate('peso')">
              <div class="template-icon">⚖️</div>
              <div class="template-name">Peso</div>
              <div class="template-description">Medição de peso em kg</div>
            </div>

            <div class="template-card" onclick="applyTemplate('dureza')">
              <div class="template-icon">🔨</div>
              <div class="template-name">Dureza</div>
              <div class="template-description">Teste de dureza HRC</div>
            </div>

            <div class="template-card" onclick="applyTemplate('cor')">
              <div class="template-icon">🎨</div>
              <div class="template-name">Cor</div>
              <div class="template-description">Verificação de cor padrão</div>
            </div>

            <div class="template-card" onclick="applyTemplate('temperatura')">
              <div class="template-icon">🌡️</div>
              <div class="template-name">Temperatura</div>
              <div class="template-description">Medição de temperatura</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para Adicionar/Editar Especificação -->
  <div id="specModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title" id="modalTitle">
          <i class="fas fa-plus"></i> Nova Especificação
        </h2>
        <button class="modal-close" onclick="closeModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <form id="specForm" onsubmit="saveSpecs(event)">
          <div class="form-grid">
            <div class="form-group">
              <label><i class="fas fa-bullseye"></i> Critério de Qualidade</label>
              <input type="text" id="criterio" placeholder="Ex.: Dimensão Externa, Peso Total, Dureza Superficial" required>
            </div>

            <div class="form-group">
              <label><i class="fas fa-ruler"></i> Tipo de Medição</label>
              <select id="tipoMedicao" required>
                <option value="">Selecione o tipo...</option>
                <option value="numerico">📊 Numérico (com tolerância)</option>
                <option value="visual">👁️ Visual (Aprovado/Rejeitado)</option>
                <option value="lista">📋 Lista de opções</option>
                <option value="texto">📝 Texto livre</option>
              </select>
            </div>

            <div class="form-group">
              <label><i class="fas fa-exclamation-triangle"></i> Prioridade</label>
              <select id="prioridade" required>
                <option value="NORMAL">🟢 Normal</option>
                <option value="IMPORTANTE">🟡 Importante</option>
                <option value="CRITICA">🔴 Crítica</option>
              </select>
            </div>
          </div>

          <!-- Campos Numéricos -->
          <div id="numericFields" style="display: none;">
            <div class="form-grid">
              <div class="form-group">
                <label><i class="fas fa-arrow-down"></i> Valor Mínimo</label>
                <input type="number" step="any" id="valorMinimo" placeholder="Ex.: 9.5">
              </div>

              <div class="form-group">
                <label><i class="fas fa-arrow-up"></i> Valor Máximo</label>
                <input type="number" step="any" id="valorMaximo" placeholder="Ex.: 10.5">
              </div>

              <div class="form-group">
                <label><i class="fas fa-ruler-combined"></i> Unidade de Medida</label>
                <input type="text" id="unidade" placeholder="Ex.: mm, kg, %, °C">
              </div>
            </div>
          </div>

          <!-- Campo Lista -->
          <div class="form-group" id="listOptions" style="display: none;">
            <label><i class="fas fa-list"></i> Opções Disponíveis (separadas por vírgula)</label>
            <input type="text" id="opcoes" placeholder="Ex.: Aprovado, Rejeitado, Condicional, Retrabalho">
          </div>

          <div class="form-group">
            <label><i class="fas fa-comment-alt"></i> Instruções para o Inspetor</label>
            <textarea id="specObservacoes" rows="4" placeholder="Descreva detalhadamente como realizar esta inspeção, equipamentos necessários, normas aplicáveis, etc."></textarea>
          </div>

          <div class="form-grid">
            <div class="checkbox-group">
              <input type="checkbox" id="obrigatorio" checked>
              <label for="obrigatorio">
                <i class="fas fa-exclamation-circle"></i> Critério obrigatório para aprovação
              </label>
            </div>

            <div class="checkbox-group">
              <input type="checkbox" id="requerFoto">
              <label for="requerFoto">
                <i class="fas fa-camera"></i> Requer evidência fotográfica
              </label>
            </div>
          </div>

          <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">
              <i class="fas fa-times"></i> Cancelar
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fas fa-save"></i> Salvar Especificação
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>


  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      doc,
      deleteDoc,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let especificacoes = [];
    let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Usuário Atual' };
    let currentProductId = null;

    window.onload = async () => {
      showLoading(true);
      await loadInitialData();
      populateProductSelect();
      updateDashboard();
      showLoading(false);
    };

    async function loadInitialData() {
      try {
        const [produtosSnap, specsSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "especificacoesInspecao"))
        ]);
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        especificacoes = specsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`✅ Carregados ${produtos.length} produtos e ${especificacoes.length} especificações`);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        showNotification("Erro ao carregar dados iniciais", "error");
      }
    }

    function updateDashboard() {
      // Atualizar cards do dashboard
      document.getElementById('totalProducts').textContent = produtos.length;
      document.getElementById('totalSpecs').textContent = especificacoes.length;

      const criticalSpecs = especificacoes.filter(spec => spec.prioridade === 'CRITICA').length;
      document.getElementById('criticalSpecs').textContent = criticalSpecs;

      const photoSpecs = especificacoes.filter(spec => spec.requerFoto === true).length;
      document.getElementById('photoSpecs').textContent = photoSpecs;
    }

    function populateProductSelect() {
      const select = document.getElementById('productSelect');
      select.innerHTML = '<option value="">Selecione o produto...</option>';
      produtos.forEach(produto => {
        select.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
      });
    }

    // Função para filtrar produtos na busca
    window.filterProducts = function() {
      const searchTerm = document.getElementById('searchProduct').value.toLowerCase();
      const select = document.getElementById('productSelect');

      select.innerHTML = '<option value="">Selecione o produto...</option>';

      const filteredProducts = produtos.filter(produto =>
        produto.codigo.toLowerCase().includes(searchTerm) ||
        produto.descricao.toLowerCase().includes(searchTerm)
      );

      filteredProducts.forEach(produto => {
        select.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
      });
    };

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Controle de campos baseado no tipo de medição
      const tipoMedicao = document.getElementById('tipoMedicao');
      if (tipoMedicao) {
        tipoMedicao.addEventListener('change', function() {
          const numericFields = document.getElementById('numericFields');
          const listOptions = document.getElementById('listOptions');

          // Ocultar todos os campos específicos
          numericFields.style.display = 'none';
          listOptions.style.display = 'none';

          // Mostrar campos baseado na seleção
          if (this.value === 'numerico') {
            numericFields.style.display = 'block';
          } else if (this.value === 'lista') {
            listOptions.style.display = 'block';
          }
        });
      }

      // Fechar modal ao clicar fora
      const modal = document.getElementById('specModal');
      if (modal) {
        modal.addEventListener('click', function(e) {
          if (e.target === modal) {
            closeModal();
          }
        });
      }

      // Atalhos de teclado
      document.addEventListener('keydown', function(e) {
        // ESC para fechar modal
        if (e.key === 'Escape') {
          closeModal();
        }

        // Ctrl+N para nova especificação
        if (e.ctrlKey && e.key === 'n' && currentProductId) {
          e.preventDefault();
          showAddForm();
        }
      });
    });

    window.loadProductSpecs = async function() {
      currentProductId = document.getElementById('productSelect').value;
      const specsSection = document.getElementById('specsSection');
      const templatesSection = document.getElementById('templatesSection');
      const addSpecBtn = document.getElementById('addSpecBtn');

      if (currentProductId) {
        const produto = produtos.find(p => p.id === currentProductId);
        const productSpecs = especificacoes.filter(spec => spec.produtoId === currentProductId);

        // Habilitar botão de adicionar
        addSpecBtn.disabled = false;

        // Mostrar seções
        specsSection.style.display = 'block';
        templatesSection.style.display = 'block';

        // Renderizar tabela de especificações
        renderSpecificationsTable(productSpecs, produto);

      } else {
        // Ocultar seções e desabilitar botão
        specsSection.style.display = 'none';
        templatesSection.style.display = 'none';
        addSpecBtn.disabled = true;
      }
    };

    function renderSpecificationsTable(productSpecs, produto) {
      const container = document.getElementById('specsTableContainer');

      if (productSpecs.length === 0) {
        container.innerHTML = `
          <div class="empty-state">
            <div class="empty-state-icon">
              <i class="fas fa-clipboard"></i>
            </div>
            <h3 class="empty-state-title">Nenhuma especificação cadastrada</h3>
            <p class="empty-state-description">
              Este produto ainda não possui critérios de qualidade definidos.<br>
              Comece adicionando uma nova especificação ou use um template rápido.
            </p>
            <button class="btn btn-success" onclick="showAddForm()">
              <i class="fas fa-plus"></i> Adicionar Primeira Especificação
            </button>
          </div>
        `;
        return;
      }

      // Criar tabela moderna
      let tableHTML = `
        <table class="modern-table">
          <thead>
            <tr>
              <th><i class="fas fa-bullseye"></i> Critério</th>
              <th><i class="fas fa-cogs"></i> Tipo</th>
              <th><i class="fas fa-ruler"></i> Parâmetros</th>
              <th><i class="fas fa-exclamation-triangle"></i> Prioridade</th>
              <th><i class="fas fa-camera"></i> Foto</th>
              <th><i class="fas fa-comment"></i> Observações</th>
              <th><i class="fas fa-tools"></i> Ações</th>
            </tr>
          </thead>
          <tbody>
      `;

      productSpecs.forEach(spec => {
        // Determinar parâmetros baseado no tipo
        let parametros = '';
        if (spec.tipoMedicao === 'numerico') {
          parametros = `${spec.valorMinimo || 'N/A'} - ${spec.valorMaximo || 'N/A'} ${spec.unidade || ''}`;
        } else if (spec.tipoMedicao === 'lista') {
          parametros = spec.opcoes || 'N/A';
        } else {
          parametros = spec.tipoMedicao || 'N/A';
        }

        // Ícone do tipo
        const typeIcons = {
          'numerico': '📊',
          'visual': '👁️',
          'lista': '📋',
          'texto': '📝'
        };

        // Cor da prioridade
        const priorityColors = {
          'NORMAL': '#28a745',
          'IMPORTANTE': '#ffc107',
          'CRITICA': '#dc3545'
        };

        tableHTML += `
          <tr>
            <td>
              <div style="display: flex; align-items: center; gap: 8px;">
                <strong>${spec.criterio}</strong>
                ${spec.obrigatorio ? '<span style="color: #dc3545; font-size: 12px;">⚠️ Obrigatório</span>' : ''}
              </div>
            </td>
            <td>
              <span style="display: flex; align-items: center; gap: 5px;">
                ${typeIcons[spec.tipoMedicao] || '❓'} ${spec.tipoMedicao || 'N/A'}
              </span>
            </td>
            <td><code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px;">${parametros}</code></td>
            <td>
              <span style="background: ${priorityColors[spec.prioridade] || '#6c757d'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                ${spec.prioridade || 'NORMAL'}
              </span>
            </td>
            <td>${spec.requerFoto ? '📸 Sim' : '❌ Não'}</td>
            <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${spec.observacoes || ''}">
              ${spec.observacoes || '-'}
            </td>
            <td>
              <div class="action-buttons">
                <button class="btn btn-secondary btn-sm" onclick="editSpec('${spec.id}')" title="Editar">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteSpec('${spec.id}')" title="Excluir">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        `;
      });

      tableHTML += `
          </tbody>
        </table>
      `;

      container.innerHTML = tableHTML;
    }

    window.saveSpecs = async function(event) {
      event.preventDefault();

      if (!currentProductId) {
        showNotification('Selecione um produto antes de adicionar especificações', 'warning');
        return;
      }

      const tipoMedicao = document.getElementById('tipoMedicao').value;

      // Validações específicas por tipo
      if (tipoMedicao === 'numerico') {
        const min = document.getElementById('valorMinimo').value;
        const max = document.getElementById('valorMaximo').value;
        if (!min || !max) {
          showNotification('Para medições numéricas, informe os valores mínimo e máximo', 'warning');
          return;
        }
        if (parseFloat(min) >= parseFloat(max)) {
          showNotification('O valor mínimo deve ser menor que o valor máximo', 'warning');
          return;
        }
      }

      if (tipoMedicao === 'lista') {
        const opcoes = document.getElementById('opcoes').value;
        if (!opcoes) {
          showNotification('Para listas de opções, informe as opções separadas por vírgula', 'warning');
          return;
        }
      }

      const specData = {
        produtoId: currentProductId,
        criterio: document.getElementById('criterio').value,
        tipoMedicao: tipoMedicao,
        prioridade: document.getElementById('prioridade').value,
        obrigatorio: document.getElementById('obrigatorio').checked,
        requerFoto: document.getElementById('requerFoto').checked,
        observacoes: document.getElementById('specObservacoes').value,
        dataCriacao: Timestamp.now(),
        criadoPor: currentUser.nome
      };

      // Adicionar campos específicos baseado no tipo
      if (tipoMedicao === 'numerico') {
        specData.valorMinimo = parseFloat(document.getElementById('valorMinimo').value);
        specData.valorMaximo = parseFloat(document.getElementById('valorMaximo').value);
        specData.unidade = document.getElementById('unidade').value;
      } else if (tipoMedicao === 'lista') {
        specData.opcoes = document.getElementById('opcoes').value;
      }

      try {
        const editId = document.getElementById('specForm').dataset.editId;

        if (editId) {
          // Editar especificação existente
          await updateDoc(doc(db, "especificacoesInspecao", editId), {
            ...specData,
            dataModificacao: Timestamp.now(),
            modificadoPor: currentUser.nome
          });
          showNotification('✅ Especificação atualizada com sucesso!', 'success');
        } else {
          // Criar nova especificação
          await addDoc(collection(db, "especificacoesInspecao"), specData);
          showNotification('✅ Especificação criada com sucesso!', 'success');
        }

        // Fechar modal e recarregar
        closeModal();
        await loadInitialData();
        updateDashboard();
        loadProductSpecs();

      } catch (error) {
        console.error("Erro ao salvar especificação:", error);
        showNotification('❌ Erro ao salvar especificação', 'error');
      }
    };

    window.deleteSpec = async function(specId) {
      if (confirm('⚠️ Confirma a exclusão desta especificação?\n\nEsta ação não pode ser desfeita.')) {
        try {
          await deleteDoc(doc(db, "especificacoesInspecao", specId));
          await loadInitialData();
          loadProductSpecs();
          showNotification('✅ Especificação excluída com sucesso!', 'success');
        } catch (error) {
          console.error("Erro ao excluir especificação:", error);
          showNotification('❌ Erro ao excluir especificação.', 'error');
        }
      }
    };

    // Função para mostrar modal de adição
    window.showAddForm = function() {
      if (!currentProductId) {
        showNotification('Selecione um produto primeiro', 'warning');
        return;
      }

      document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus"></i> Nova Especificação';
      document.getElementById('specForm').reset();
      document.getElementById('numericFields').style.display = 'none';
      document.getElementById('listOptions').style.display = 'none';
      document.getElementById('specModal').style.display = 'block';
      document.getElementById('criterio').focus();
    };

    // Função para fechar modal
    window.closeModal = function() {
      document.getElementById('specModal').style.display = 'none';
      document.getElementById('specForm').reset();
      document.getElementById('numericFields').style.display = 'none';
      document.getElementById('listOptions').style.display = 'none';
    };

    // Função para editar especificação
    window.editSpec = function(specId) {
      const spec = especificacoes.find(s => s.id === specId);
      if (!spec) return;

      // Preencher formulário
      document.getElementById('criterio').value = spec.criterio || '';
      document.getElementById('tipoMedicao').value = spec.tipoMedicao || '';
      document.getElementById('prioridade').value = spec.prioridade || 'NORMAL';
      document.getElementById('obrigatorio').checked = spec.obrigatorio !== false;
      document.getElementById('requerFoto').checked = spec.requerFoto === true;
      document.getElementById('specObservacoes').value = spec.observacoes || '';

      // Campos específicos
      if (spec.tipoMedicao === 'numerico') {
        document.getElementById('valorMinimo').value = spec.valorMinimo || '';
        document.getElementById('valorMaximo').value = spec.valorMaximo || '';
        document.getElementById('unidade').value = spec.unidade || '';
        document.getElementById('numericFields').style.display = 'flex';
      } else if (spec.tipoMedicao === 'lista') {
        document.getElementById('opcoes').value = spec.opcoes || '';
        document.getElementById('listOptions').style.display = 'block';
      }

      // Configurar modal para edição
      document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit"></i> Editar Especificação';
      document.getElementById('specForm').dataset.editId = specId;
      document.getElementById('specModal').style.display = 'block';
    };

    // Função para aplicar templates
    window.applyTemplate = function(templateType) {
      showAddForm();

      const templates = {
        dimensional: {
          criterio: 'Dimensão',
          tipoMedicao: 'numerico',
          prioridade: 'IMPORTANTE',
          unidade: 'mm',
          obrigatorio: true,
          requerFoto: false,
          observacoes: 'Verificar dimensões conforme desenho técnico'
        },
        visual: {
          criterio: 'Inspeção Visual',
          tipoMedicao: 'lista',
          prioridade: 'NORMAL',
          opcoes: 'Aprovado, Rejeitado, Condicional',
          obrigatorio: true,
          requerFoto: true,
          observacoes: 'Verificar defeitos visuais, riscos, manchas'
        },
        peso: {
          criterio: 'Peso',
          tipoMedicao: 'numerico',
          prioridade: 'NORMAL',
          unidade: 'kg',
          obrigatorio: false,
          requerFoto: false,
          observacoes: 'Pesar em balança calibrada'
        },
        dureza: {
          criterio: 'Dureza',
          tipoMedicao: 'numerico',
          prioridade: 'CRITICA',
          unidade: 'HRC',
          obrigatorio: true,
          requerFoto: false,
          observacoes: 'Teste de dureza conforme norma'
        },
        cor: {
          criterio: 'Cor',
          tipoMedicao: 'lista',
          prioridade: 'NORMAL',
          opcoes: 'Conforme, Não Conforme',
          obrigatorio: false,
          requerFoto: true,
          observacoes: 'Comparar com padrão de cor'
        }
      };

      const template = templates[templateType];
      if (template) {
        document.getElementById('criterio').value = template.criterio;
        document.getElementById('tipoMedicao').value = template.tipoMedicao;
        document.getElementById('prioridade').value = template.prioridade;
        document.getElementById('obrigatorio').checked = template.obrigatorio;
        document.getElementById('requerFoto').checked = template.requerFoto;
        document.getElementById('specObservacoes').value = template.observacoes;

        // Disparar evento change para mostrar campos específicos
        document.getElementById('tipoMedicao').dispatchEvent(new Event('change'));

        if (template.unidade) {
          document.getElementById('unidade').value = template.unidade;
        }
        if (template.opcoes) {
          document.getElementById('opcoes').value = template.opcoes;
        }
      }
    };

    // Função para mostrar notificações modernas
    function showNotification(message, type = 'info') {
      // Remover notificação existente
      const existingNotification = document.querySelector('.notification');
      if (existingNotification) {
        existingNotification.remove();
      }

      // Criar nova notificação
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;

      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      };

      notification.innerHTML = `
        <i class="${icons[type] || icons.info}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);

      // Mostrar com animação
      setTimeout(() => notification.classList.add('show'), 100);

      // Remover após 4 segundos
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
      }, 4000);
    }

    // Função para mostrar/ocultar loading
    function showLoading(show) {
      const loadingElements = document.querySelectorAll('.loading');
      loadingElements.forEach(el => {
        el.style.display = show ? 'inline-block' : 'none';
      });

      // Desabilitar botões durante loading
      const buttons = document.querySelectorAll('button');
      buttons.forEach(btn => {
        btn.disabled = show;
      });
    }

    // Função para aplicar templates com interface melhorada
    window.applyTemplate = function(templateType) {
      if (!currentProductId) {
        showNotification('Selecione um produto primeiro', 'warning');
        return;
      }

      const templates = {
        dimensional: {
          criterio: 'Dimensão Externa',
          tipoMedicao: 'numerico',
          prioridade: 'IMPORTANTE',
          valorMinimo: 9.5,
          valorMaximo: 10.5,
          unidade: 'mm',
          obrigatorio: true,
          requerFoto: false,
          observacoes: 'Verificar dimensões conforme desenho técnico usando paquímetro calibrado'
        },
        visual: {
          criterio: 'Inspeção Visual',
          tipoMedicao: 'lista',
          prioridade: 'NORMAL',
          opcoes: 'Aprovado, Rejeitado, Condicional',
          obrigatorio: true,
          requerFoto: true,
          observacoes: 'Verificar defeitos visuais: riscos, manchas, deformações, acabamento superficial'
        },
        peso: {
          criterio: 'Peso Total',
          tipoMedicao: 'numerico',
          prioridade: 'NORMAL',
          valorMinimo: 0.95,
          valorMaximo: 1.05,
          unidade: 'kg',
          obrigatorio: false,
          requerFoto: false,
          observacoes: 'Pesar em balança calibrada, considerar tolerância de ±5%'
        },
        dureza: {
          criterio: 'Dureza Superficial',
          tipoMedicao: 'numerico',
          prioridade: 'CRITICA',
          valorMinimo: 58,
          valorMaximo: 62,
          unidade: 'HRC',
          obrigatorio: true,
          requerFoto: false,
          observacoes: 'Teste de dureza Rockwell C conforme norma ASTM E18'
        },
        cor: {
          criterio: 'Verificação de Cor',
          tipoMedicao: 'lista',
          prioridade: 'NORMAL',
          opcoes: 'Conforme Padrão, Não Conforme, Verificar Lote',
          obrigatorio: false,
          requerFoto: true,
          observacoes: 'Comparar com padrão de cor aprovado, verificar uniformidade'
        },
        temperatura: {
          criterio: 'Temperatura de Operação',
          tipoMedicao: 'numerico',
          prioridade: 'IMPORTANTE',
          valorMinimo: -10,
          valorMaximo: 85,
          unidade: '°C',
          obrigatorio: true,
          requerFoto: false,
          observacoes: 'Teste de temperatura conforme especificação técnica'
        }
      };

      const template = templates[templateType];
      if (!template) {
        showNotification('Template não encontrado', 'error');
        return;
      }

      // Mostrar modal e preencher campos
      showAddForm();

      setTimeout(() => {
        document.getElementById('criterio').value = template.criterio;
        document.getElementById('tipoMedicao').value = template.tipoMedicao;
        document.getElementById('prioridade').value = template.prioridade;
        document.getElementById('obrigatorio').checked = template.obrigatorio;
        document.getElementById('requerFoto').checked = template.requerFoto;
        document.getElementById('specObservacoes').value = template.observacoes;

        // Disparar evento change para mostrar campos específicos
        document.getElementById('tipoMedicao').dispatchEvent(new Event('change'));

        // Preencher campos específicos
        if (template.valorMinimo !== undefined) {
          document.getElementById('valorMinimo').value = template.valorMinimo;
        }
        if (template.valorMaximo !== undefined) {
          document.getElementById('valorMaximo').value = template.valorMaximo;
        }
        if (template.unidade) {
          document.getElementById('unidade').value = template.unidade;
        }
        if (template.opcoes) {
          document.getElementById('opcoes').value = template.opcoes;
        }

        showNotification(`Template "${template.criterio}" aplicado com sucesso!`, 'success');
      }, 100);
    };
  </script>
</body>
</html>