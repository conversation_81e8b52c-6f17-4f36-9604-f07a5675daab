<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plano de Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
    }

    .form-container {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 60px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    .section-header {
      font-size: 16px;
      font-weight: 500;
      color: var(--primary-color);
      margin: 20px 0 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .table-container {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
      margin-top: 20px;
    }

    .search-container {
      margin-bottom: 20px;
    }

    .search-container input {
      width: 300px;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .plans-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .plans-table th,
    .plans-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .plans-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
      cursor: pointer;
    }

    .plans-table tr:hover {
      background-color: #f8f9fa;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
    }

    .edit-btn, .delete-btn, .execute-btn {
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      cursor: pointer;
      border: none;
      transition: background-color 0.2s;
    }

    .edit-btn {
      background-color: #ffc107;
      color: #000;
    }

    .edit-btn:hover {
      background-color: #e0a800;
    }

    .delete-btn {
      background-color: var(--danger-color);
      color: white;
    }

    .delete-btn:hover {
      background-color: var(--danger-hover);
    }

    .execute-btn {
      background-color: var(--success-color);
      color: white;
    }

    .execute-btn:hover {
      background-color: var(--success-hover);
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }

    .required::after {
      content: "*";
      color: var(--danger-color);
      margin-left: 4px;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 4px;
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      display: none;
      align-items: center;
      gap: 10px;
      transition: opacity 0.3s ease;
    }

    .notification-success { background-color: var(--success-color); }
    .notification-error { background-color: var(--danger-color); }
    .notification-icon { font-weight: bold; font-size: 18px; }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-planned {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .status-in-progress {
      background-color: #fff3e0;
      color: #f57c00;
    }

    .status-completed {
      background-color: #e8f5e9;
      color: #2e7d32;
    }

    .status-cancelled {
      background-color: #ffebee;
      color: #c62828;
    }

    .frequency-select {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .frequency-select input[type="number"] {
      width: 100px;
    }

    @media print {
      .no-print {
        display: none;
      }
      .container {
        margin: 0;
        box-shadow: none;
      }
    }

    @media (max-width: 768px) {
      .container {
        width: 100%;
        margin: 0;
        border-radius: 0;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .search-container input {
        width: 100%;
      }

      .plans-table {
        font-size: 12px;
      }

      .plans-table th,
      .plans-table td {
        padding: 8px;
      }

      .action-buttons {
        flex-direction: column;
        gap: 3px;
      }

      .edit-btn, .delete-btn, .execute-btn {
        width: 100%;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Plano de Manutenção</h1>
      <div>
        <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
      </div>
    </div>

    <div id="notification" class="notification"></div>

    <div class="form-container">
      <h2 class="form-title">Criar Novo Plano de Manutenção</h2>
      <form id="maintenancePlanForm">
        <input type="hidden" id="editingId">

        <!-- Seleção do Equipamento -->
        <div class="section-header">Equipamento</div>
        <div class="form-row">
          <div class="form-col">
            <label for="equipamento" class="required">Equipamento</label>
            <select id="equipamento" name="equipamento" required>
              <option value="">Selecione um equipamento...</option>
            </select>
          </div>
        </div>

        <!-- Detalhes do Plano -->
        <div class="section-header">Detalhes do Plano</div>
        <div class="form-row">
          <div class="form-col">
            <label for="tipoManutencao" class="required">Tipo de Manutenção</label>
            <select id="tipoManutencao" name="tipoManutencao" required>
              <option value="PREVENTIVA">Preventiva</option>
              <option value="CORRETIVA">Corretiva</option>
              <option value="PREDITIVA">Preditiva</option>
              <option value="CONDICIONAL">Condicional</option>
            </select>
          </div>
          <div class="form-col">
            <label for="prioridade" class="required">Prioridade</label>
            <select id="prioridade" name="prioridade" required>
              <option value="ALTA">Alta</option>
              <option value="MEDIA">Média</option>
              <option value="BAIXA">Baixa</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="descricao" class="required">Descrição</label>
            <textarea id="descricao" name="descricao" required></textarea>
          </div>
        </div>

        <!-- Frequência e Duração -->
        <div class="section-header">Frequência e Duração</div>
        <div class="form-row">
          <div class="form-col">
            <label for="frequencia" class="required">Frequência</label>
            <div class="frequency-select">
              <input type="number" id="frequenciaValor" name="frequenciaValor" min="1" required aria-label="Valor da frequência" placeholder="Valor">
              <select id="frequenciaUnidade" name="frequenciaUnidade" required aria-label="Unidade da frequência" title="Selecione a unidade de frequência">
                <option value="DIAS">Dias</option>
                <option value="SEMANAS">Semanas</option>
                <option value="MESES">Meses</option>
                <option value="HORAS">Horas de Operação</option>
              </select>
            </div>
          </div>
          <div class="form-col">
            <label for="duracaoEstimada" class="required">Duração Estimada (horas)</label>
            <input type="number" id="duracaoEstimada" name="duracaoEstimada" min="0.5" step="0.5" required>
          </div>
        </div>

        <!-- Recursos Necessários -->
        <div class="section-header">Recursos Necessários</div>
        <div class="form-row">
          <div class="form-col">
            <label for="fornecedor">Fornecedor Responsável</label>
            <select id="fornecedor" name="fornecedor">
              <option value="">Selecione um fornecedor...</option>
            </select>
          </div>
          <div class="form-col">
            <label for="responsavel">Responsável Interno</label>
            <input type="text" id="responsavel" name="responsavel">
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="pecasNecessarias">Peças Necessárias</label>
            <select id="pecasNecessarias" name="pecasNecessarias" multiple>
              <option value="">Selecione as peças...</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="ferramentas">Ferramentas Necessárias</label>
            <textarea id="ferramentas" name="ferramentas" rows="2"></textarea>
          </div>
        </div>

        <!-- Procedimentos -->
        <div class="section-header">Procedimentos</div>
        <div class="form-row">
          <div class="form-col">
            <label for="procedimentos" class="required">Procedimentos de Manutenção</label>
            <textarea id="procedimentos" name="procedimentos" rows="4" required></textarea>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="observacoes">Observações</label>
            <textarea id="observacoes" name="observacoes" rows="2"></textarea>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-secondary" onclick="cancelEdit()">Cancelar</button>
          <button type="submit" class="btn-success" id="submitButton">Criar Plano</button>
        </div>
      </form>
    </div>

    <div class="table-container">
      <h2 class="form-title">Planos de Manutenção</h2>
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="Pesquisar por equipamento, tipo de manutenção...">
      </div>
      <table class="plans-table">
        <thead>
          <tr>
            <th onclick="sortTable('equipamento')">Equipamento</th>
            <th onclick="sortTable('tipoManutencao')">Tipo</th>
            <th onclick="sortTable('frequencia')">Frequência</th>
            <th onclick="sortTable('duracaoEstimada')">Duração (h)</th>
            <th onclick="sortTable('prioridade')">Prioridade</th>
            <th onclick="sortTable('proximaExecucao')">Próxima Execução</th>
            <th onclick="sortTable('status')">Status</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="plansTableBody">
        </tbody>
      </table>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let planos = [];
    let equipamentos = [];
    let fornecedores = [];
    let pecas = [];
    let sortDirection = 'asc';
    let currentSortColumn = '';

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }

      const currentUser = JSON.parse(userSession);
      if (currentUser.nivel < 9) {
        showNotification('Acesso restrito. Apenas administradores podem gerenciar planos de manutenção.', 'error');
        window.location.href = 'index.html';
        return;
      }

      await loadEquipamentos();
      await loadFornecedores();
      await loadPecas();
      await loadMaintenancePlans();
      document.getElementById('searchInput').addEventListener('input', filterTable);
    };

    async function loadEquipamentos() {
      try {
        const equipamentosSnapshot = await getDocs(collection(db, "recursos"));
        equipamentos = equipamentosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        const equipamentoSelect = document.getElementById('equipamento');
        equipamentoSelect.innerHTML = '<option value="">Selecione um equipamento...</option>';

        equipamentos
          .filter(e => e.tipoEquipamento) // Filtra apenas equipamentos
          .sort((a, b) => a.codigo.localeCompare(b.codigo))
          .forEach(equip => {
            equipamentoSelect.innerHTML += `
              <option value="${equip.id}">
                ${equip.codigo} - ${equip.maquina}
              </option>`;
          });
      } catch (error) {
        console.error("Erro ao carregar equipamentos:", error);
        showNotification("Erro ao carregar equipamentos.", "error");
      }
    }

    async function loadFornecedores() {
      try {
        const fornecedoresSnapshot = await getDocs(collection(db, "fornecedores"));
        fornecedores = fornecedoresSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            nome: data.nomeFantasia || data.razaoSocial || 'Fornecedor sem nome'
          };
        });

        const fornecedorSelect = document.getElementById('fornecedor');
        fornecedorSelect.innerHTML = '<option value="">Selecione um fornecedor...</option>';

        fornecedores
          .filter(f => f.ativo !== false)
          .sort((a, b) => a.nome.localeCompare(b.nome))
          .forEach(fornecedor => {
            fornecedorSelect.innerHTML += `
              <option value="${fornecedor.id}">${fornecedor.nome}</option>`;
          });
      } catch (error) {
        console.error("Erro ao carregar fornecedores:", error);
        showNotification("Erro ao carregar fornecedores.", "error");
      }
    }

    async function loadPecas() {
      try {
        const pecasSnapshot = await getDocs(collection(db, "materiais"));
        pecas = pecasSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        const pecasSelect = document.getElementById('pecasNecessarias');
        pecasSelect.innerHTML = '<option value="">Selecione as peças...</option>';

        pecas
          .sort((a, b) => a.codigo.localeCompare(b.codigo))
          .forEach(peca => {
            pecasSelect.innerHTML += `
              <option value="${peca.id}">
                ${peca.codigo} - ${peca.descricao}
              </option>`;
          });
      } catch (error) {
        console.error("Erro ao carregar peças:", error);
        showNotification("Erro ao carregar peças.", "error");
      }
    }

    async function loadMaintenancePlans() {
      try {
        const planosSnapshot = await getDocs(collection(db, "planosManutencao"));
        planos = planosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        displayPlans();
      } catch (error) {
        console.error("Erro ao carregar planos de manutenção:", error);
        showNotification("Erro ao carregar planos de manutenção.", "error");
      }
    }

    function displayPlans() {
      const tableBody = document.getElementById('plansTableBody');
      tableBody.innerHTML = '';

      planos.forEach(plano => {
        const equipamento = equipamentos.find(e => e.id === plano.equipamentoId);
        const proximaExecucao = calcularProximaExecucao(plano);

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${equipamento ? `${equipamento.codigo} - ${equipamento.maquina}` : '-'}</td>
          <td>${getTipoManutencaoLabel(plano.tipoManutencao)}</td>
          <td>${formatarFrequencia(plano.frequenciaValor, plano.frequenciaUnidade)}</td>
          <td>${plano.duracaoEstimada}</td>
          <td>${getPrioridadeLabel(plano.prioridade)}</td>
          <td>${proximaExecucao ? proximaExecucao.toLocaleDateString() : '-'}</td>
          <td><span class="status-badge status-${plano.status.toLowerCase()}">${getStatusLabel(plano.status)}</span></td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editPlan('${plano.id}')">Editar</button>
            <button class="delete-btn" onclick="deletePlan('${plano.id}')">Excluir</button>
            <button class="execute-btn" onclick="executePlan('${plano.id}')">Executar</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function calcularProximaExecucao(plano) {
      if (!plano.ultimaExecucao) return new Date();

      const dataUltima = new Date(plano.ultimaExecucao);
      let diasAdicionar = 0;

      switch (plano.frequenciaUnidade) {
        case 'DIAS':
          diasAdicionar = plano.frequenciaValor;
          break;
        case 'SEMANAS':
          diasAdicionar = plano.frequenciaValor * 7;
          break;
        case 'MESES':
          diasAdicionar = plano.frequenciaValor * 30;
          break;
        case 'HORAS':
          // Para horas de operação, precisaríamos implementar um cálculo baseado nas horas de operação do equipamento
          return null;
      }

      return new Date(dataUltima.setDate(dataUltima.getDate() + diasAdicionar));
    }

    function formatarFrequencia(valor, unidade) {
      const unidades = {
        'DIAS': 'dias',
        'SEMANAS': 'semanas',
        'MESES': 'meses',
        'HORAS': 'horas de operação'
      };
      return `${valor} ${unidades[unidade]}`;
    }

    function getTipoManutencaoLabel(tipo) {
      const tipos = {
        'PREVENTIVA': 'Preventiva',
        'CORRETIVA': 'Corretiva',
        'PREDITIVA': 'Preditiva',
        'CONDICIONAL': 'Condicional'
      };
      return tipos[tipo] || tipo;
    }

    function getPrioridadeLabel(prioridade) {
      const prioridades = {
        'ALTA': 'Alta',
        'MEDIA': 'Média',
        'BAIXA': 'Baixa'
      };
      return prioridades[prioridade] || prioridade;
    }

    function getStatusLabel(status) {
      const statusLabels = {
        'PLANNED': 'Planejado',
        'IN_PROGRESS': 'Em Andamento',
        'COMPLETED': 'Concluído',
        'CANCELLED': 'Cancelado'
      };
      return statusLabels[status] || status;
    }

    window.sortTable = function(sortBy) {
      if (sortBy === currentSortColumn) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        currentSortColumn = sortBy;
        sortDirection = 'asc';
      }

      planos.sort((a, b) => {
        let valueA, valueB;

        switch (sortBy) {
          case 'equipamento':
            const equipA = equipamentos.find(e => e.id === a.equipamentoId);
            const equipB = equipamentos.find(e => e.id === b.equipamentoId);
            valueA = equipA ? `${equipA.codigo} - ${equipA.maquina}` : '';
            valueB = equipB ? `${equipB.codigo} - ${equipB.maquina}` : '';
            break;
          case 'tipoManutencao':
            valueA = getTipoManutencaoLabel(a.tipoManutencao);
            valueB = getTipoManutencaoLabel(b.tipoManutencao);
            break;
          case 'frequencia':
            valueA = `${a.frequenciaValor} ${a.frequenciaUnidade}`;
            valueB = `${b.frequenciaValor} ${b.frequenciaUnidade}`;
            break;
          case 'duracaoEstimada':
            valueA = a.duracaoEstimada;
            valueB = b.duracaoEstimada;
            break;
          case 'prioridade':
            valueA = getPrioridadeLabel(a.prioridade);
            valueB = getPrioridadeLabel(b.prioridade);
            break;
          case 'proximaExecucao':
            valueA = calcularProximaExecucao(a);
            valueB = calcularProximaExecucao(b);
            break;
          case 'status':
            valueA = getStatusLabel(a.status);
            valueB = getStatusLabel(b.status);
            break;
          default:
            valueA = a[sortBy];
            valueB = b[sortBy];
        }

        if (typeof valueA === 'string' && typeof valueB === 'string') {
          return sortDirection === 'asc' 
            ? valueA.localeCompare(valueB)
            : valueB.localeCompare(valueA);
        } else {
          return sortDirection === 'asc'
            ? valueA - valueB
            : valueB - valueA;
        }
      });

      displayPlans();
    };

    window.editPlan = function(planId) {
      const plano = planos.find(p => p.id === planId);
      if (plano) {
        document.getElementById('editingId').value = planId;
        document.getElementById('equipamento').value = plano.equipamentoId;
        document.getElementById('tipoManutencao').value = plano.tipoManutencao;
        document.getElementById('prioridade').value = plano.prioridade;
        document.getElementById('descricao').value = plano.descricao;
        document.getElementById('frequenciaValor').value = plano.frequenciaValor;
        document.getElementById('frequenciaUnidade').value = plano.frequenciaUnidade;
        document.getElementById('duracaoEstimada').value = plano.duracaoEstimada;
        document.getElementById('fornecedor').value = plano.fornecedorId || '';
        document.getElementById('responsavel').value = plano.responsavel || '';
        document.getElementById('pecasNecessarias').value = plano.pecasNecessarias || [];
        document.getElementById('ferramentas').value = plano.ferramentas || '';
        document.getElementById('procedimentos').value = plano.procedimentos;
        document.getElementById('observacoes').value = plano.observacoes || '';

        document.getElementById('submitButton').textContent = 'Atualizar Plano';
      }
    };

    window.cancelEdit = function() {
      document.getElementById('maintenancePlanForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('submitButton').textContent = 'Criar Plano';
    };

    window.deletePlan = async function(planId) {
      if (confirm('Tem certeza que deseja excluir este plano de manutenção?')) {
        try {
          await deleteDoc(doc(db, "planosManutencao", planId));
          showNotification('Plano de manutenção excluído com sucesso!', 'success');
          await loadMaintenancePlans();
        } catch (error) {
          console.error("Erro ao excluir plano:", error);
          showNotification("Erro ao excluir plano de manutenção.", "error");
        }
      }
    };

    window.executePlan = function(planId) {
      // TODO: Implementar a execução do plano de manutenção
      alert('Funcionalidade de execução de plano será implementada em breve.');
    };

    document.getElementById('maintenancePlanForm').addEventListener('submit', async function(event) {
      event.preventDefault();

      const formData = {
        equipamentoId: document.getElementById('equipamento').value,
        tipoManutencao: document.getElementById('tipoManutencao').value,
        prioridade: document.getElementById('prioridade').value,
        descricao: document.getElementById('descricao').value,
        frequenciaValor: parseInt(document.getElementById('frequenciaValor').value),
        frequenciaUnidade: document.getElementById('frequenciaUnidade').value,
        duracaoEstimada: parseFloat(document.getElementById('duracaoEstimada').value),
        fornecedorId: document.getElementById('fornecedor').value || null,
        responsavel: document.getElementById('responsavel').value,
        pecasNecessarias: Array.from(document.getElementById('pecasNecessarias').selectedOptions).map(option => option.value),
        ferramentas: document.getElementById('ferramentas').value,
        procedimentos: document.getElementById('procedimentos').value,
        observacoes: document.getElementById('observacoes').value,
        status: 'PLANNED',
        dataCriacao: new Date(),
        ultimaExecucao: null
      };

      const editingId = document.getElementById('editingId').value;

      try {
        if (editingId) {
          await updateDoc(doc(db, "planosManutencao", editingId), formData);
          showNotification("Plano de manutenção atualizado com sucesso!", "success");
        } else {
          await addDoc(collection(db, "planosManutencao"), formData);
          showNotification("Plano de manutenção criado com sucesso!", "success");
        }

        await loadMaintenancePlans();
        event.target.reset();
        cancelEdit();
      } catch (error) {
        console.error("Erro ao salvar plano:", error);
        showNotification("Erro ao salvar plano de manutenção: " + error.message, "error");
      }
    });

    function filterTable() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const filteredPlans = planos.filter(plano => {
        const equipamento = equipamentos.find(e => e.id === plano.equipamentoId);
        return (equipamento && (
          equipamento.codigo.toLowerCase().includes(searchTerm) ||
          equipamento.maquina.toLowerCase().includes(searchTerm)
        )) ||
        getTipoManutencaoLabel(plano.tipoManutencao).toLowerCase().includes(searchTerm) ||
        plano.descricao.toLowerCase().includes(searchTerm);
      });
      displayFilteredPlans(filteredPlans);
    }

    function displayFilteredPlans(filteredPlans) {
      const tableBody = document.getElementById('plansTableBody');
      tableBody.innerHTML = '';

      filteredPlans.forEach(plano => {
        const equipamento = equipamentos.find(e => e.id === plano.equipamentoId);
        const proximaExecucao = calcularProximaExecucao(plano);

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${equipamento ? `${equipamento.codigo} - ${equipamento.maquina}` : '-'}</td>
          <td>${getTipoManutencaoLabel(plano.tipoManutencao)}</td>
          <td>${formatarFrequencia(plano.frequenciaValor, plano.frequenciaUnidade)}</td>
          <td>${plano.duracaoEstimada}</td>
          <td>${getPrioridadeLabel(plano.prioridade)}</td>
          <td>${proximaExecucao ? proximaExecucao.toLocaleDateString() : '-'}</td>
          <td><span class="status-badge status-${plano.status.toLowerCase()}">${getStatusLabel(plano.status)}</span></td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editPlan('${plano.id}')">Editar</button>
            <button class="delete-btn" onclick="deletePlan('${plano.id}')">Excluir</button>
            <button class="execute-btn" onclick="executePlan('${plano.id}')">Executar</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function showNotification(message, type = 'success', duration = 3000) {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.className = '';
      notification.classList.add('notification', `notification-${type}`);
      notification.style.display = 'block';

      let icon = '';
      if (type === 'success') {
        icon = '✓';
      } else if (type === 'error') {
        icon = '✗';
      }

      notification.innerHTML = `<span class="notification-icon">${icon}</span> ${message}`;

      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.style.display = 'none', 300);
      }, duration);
    }
  </script>
</body>
</html> 