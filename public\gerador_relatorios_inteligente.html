<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Gerador de Relatórios Inteligente</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            color: var(--dark-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .wizard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .wizard-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            position: relative;
        }

        .wizard-steps::before {
            content: '';
            position: absolute;
            top: 25px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }

        .wizard-step {
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
            z-index: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .wizard-step.active {
            border-color: var(--secondary-color);
            background: var(--secondary-color);
            color: white;
        }

        .wizard-step.completed {
            border-color: var(--success-color);
            background: var(--success-color);
            color: white;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .selection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .selection-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .selection-card:hover {
            border-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .selection-card.selected {
            border-color: var(--success-color);
            background: rgba(39, 174, 96, 0.1);
        }

        .selection-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--secondary-color);
        }

        .selection-card.selected .icon {
            color: var(--success-color);
        }

        .selection-card h4 {
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .selection-card p {
            color: #666;
            font-size: 0.9rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .filters-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-color);
        }

        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }

        .preview-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
        }

        .saved-reports {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .report-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            background: white;
        }

        .report-info h5 {
            margin-bottom: 5px;
            color: var(--dark-color);
        }

        .report-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .report-actions {
            display: flex;
            gap: 10px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .ai-suggestions {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .ai-suggestions h4 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .suggestion-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .wizard-steps {
                flex-direction: column;
                gap: 20px;
            }
            
            .wizard-steps::before {
                display: none;
            }
            
            .selection-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="btn btn-secondary back-button" onclick="window.location.href='index.html'">
        ← Voltar ao Menu
    </button>

    <div class="container">
        <div class="header">
            <h1>📊 Gerador de Relatórios Inteligente</h1>
            <p>Crie relatórios personalizados com IA que auto-completa suas seleções</p>
        </div>

        <!-- Wizard de Criação -->
        <div class="wizard-container">
            <div class="wizard-steps">
                <div class="wizard-step active" data-step="1">1</div>
                <div class="wizard-step" data-step="2">2</div>
                <div class="wizard-step" data-step="3">3</div>
                <div class="wizard-step" data-step="4">4</div>
                <div class="wizard-step" data-step="5">5</div>
            </div>

            <!-- Passo 1: Tipo de Relatório -->
            <div class="step-content active" id="step1">
                <h3>📋 Passo 1: Selecione o Tipo de Relatório</h3>
                <div class="selection-grid">
                    <div class="selection-card" data-type="financeiro">
                        <div class="icon">💰</div>
                        <h4>Relatórios Financeiros</h4>
                        <p>Custos, receitas, fluxo de caixa, análises financeiras</p>
                    </div>
                    <div class="selection-card" data-type="estoque">
                        <div class="icon">📦</div>
                        <h4>Relatórios de Estoque</h4>
                        <p>Inventário, movimentações, saldos, análise ABC</p>
                    </div>
                    <div class="selection-card" data-type="compras">
                        <div class="icon">🛒</div>
                        <h4>Relatórios de Compras</h4>
                        <p>Pedidos, fornecedores, cotações, performance</p>
                    </div>
                    <div class="selection-card" data-type="producao">
                        <div class="icon">🏭</div>
                        <h4>Relatórios de Produção</h4>
                        <p>Ordens de produção, apontamentos, eficiência</p>
                    </div>
                    <div class="selection-card" data-type="vendas">
                        <div class="icon">📈</div>
                        <h4>Relatórios de Vendas</h4>
                        <p>Pedidos, clientes, performance, comissões</p>
                    </div>
                    <div class="selection-card" data-type="qualidade">
                        <div class="icon">✅</div>
                        <h4>Relatórios de Qualidade</h4>
                        <p>Inspeções, não conformidades, indicadores</p>
                    </div>
                </div>
                
                <div class="ai-suggestions">
                    <h4>🤖 Sugestões da IA</h4>
                    <div class="suggestion-item" onclick="selectSuggestion('estoque')">
                        📦 Baseado no seu histórico, relatórios de estoque são mais utilizados
                    </div>
                    <div class="suggestion-item" onclick="selectSuggestion('compras')">
                        🛒 Detectamos muita atividade em compras - que tal um relatório de performance?
                    </div>
                </div>
            </div>

            <!-- Passo 2: Dados Específicos -->
            <div class="step-content" id="step2">
                <h3>📊 Passo 2: Selecione os Dados Específicos</h3>
                <div id="specificDataOptions"></div>
            </div>

            <!-- Passo 3: Filtros e Período -->
            <div class="step-content" id="step3">
                <h3>🔍 Passo 3: Configure Filtros e Período</h3>
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label>Data Inicial:</label>
                            <input type="date" id="dataInicial">
                        </div>
                        <div class="filter-group">
                            <label>Data Final:</label>
                            <input type="date" id="dataFinal">
                        </div>
                        <div class="filter-group">
                            <label>Período Pré-definido:</label>
                            <select id="periodoPredefinido">
                                <option value="">Selecione...</option>
                                <option value="hoje">Hoje</option>
                                <option value="semana">Esta Semana</option>
                                <option value="mes">Este Mês</option>
                                <option value="trimestre">Este Trimestre</option>
                                <option value="ano">Este Ano</option>
                            </select>
                        </div>
                        <div class="filter-group" id="additionalFilters"></div>
                    </div>
                </div>
            </div>

            <!-- Passo 4: Prévia -->
            <div class="step-content" id="step4">
                <h3>👁️ Passo 4: Prévia do Relatório</h3>
                <div class="preview-area" id="reportPreview">
                    <p>Carregando prévia...</p>
                </div>
            </div>

            <!-- Passo 5: Salvar e Gerar -->
            <div class="step-content" id="step5">
                <h3>💾 Passo 5: Salvar e Gerar Relatório</h3>
                <div class="filters-section">
                    <div class="filter-group">
                        <label>Nome do Relatório:</label>
                        <input type="text" id="nomeRelatorio" placeholder="Ex: Relatório de Estoque Mensal">
                    </div>
                    <div class="filter-group">
                        <label>Descrição:</label>
                        <textarea id="descricaoRelatorio" rows="3" placeholder="Descrição opcional do relatório"></textarea>
                    </div>
                    <div class="filter-group">
                        <label>
                            <input type="checkbox" id="salvarModelo"> Salvar como modelo para reutilização
                        </label>
                    </div>
                </div>
            </div>

            <!-- Botões de Navegação -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-secondary" id="btnAnterior" onclick="previousStep()" style="display: none;">
                    ← Anterior
                </button>
                <button class="btn btn-primary" id="btnProximo" onclick="nextStep()">
                    Próximo →
                </button>
                <button class="btn btn-success" id="btnGerar" onclick="gerarRelatorio()" style="display: none;">
                    🚀 Gerar Relatório
                </button>
            </div>
        </div>

        <!-- Relatórios Salvos -->
        <div class="saved-reports">
            <h3>📚 Relatórios Salvos</h3>
            <div id="savedReportsList">
                <p>Carregando relatórios salvos...</p>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            addDoc,
            getDocs,
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let currentStep = 1;
        let reportConfig = {
            tipo: '',
            dados: [],
            filtros: {},
            nome: '',
            descricao: ''
        };

        // Configurações de tipos de relatório
        const reportTypes = {
            financeiro: {
                name: 'Financeiro',
                icon: '💰',
                dados: [
                    { id: 'custos', name: 'Análise de Custos', desc: 'Custos por produto, centro de custo, período' },
                    { id: 'receitas', name: 'Receitas', desc: 'Faturamento, vendas, recebimentos' },
                    { id: 'fluxo', name: 'Fluxo de Caixa', desc: 'Entradas, saídas, saldo projetado' },
                    { id: 'margem', name: 'Análise de Margem', desc: 'Margem por produto, cliente, período' }
                ]
            },
            estoque: {
                name: 'Estoque',
                icon: '📦',
                dados: [
                    { id: 'saldos', name: 'Saldos Atuais', desc: 'Posição atual de estoque por produto/armazém' },
                    { id: 'movimentacoes', name: 'Movimentações', desc: 'Entradas, saídas, transferências' },
                    { id: 'abc', name: 'Análise ABC', desc: 'Classificação por valor/giro de estoque' },
                    { id: 'inventario', name: 'Inventário', desc: 'Contagens, divergências, ajustes' }
                ]
            },
            compras: {
                name: 'Compras',
                icon: '🛒',
                dados: [
                    { id: 'pedidos', name: 'Pedidos de Compra', desc: 'Status, valores, prazos de entrega' },
                    { id: 'fornecedores', name: 'Performance Fornecedores', desc: 'Avaliação, pontualidade, qualidade' },
                    { id: 'cotacoes', name: 'Cotações', desc: 'Comparativo de preços, aprovações' },
                    { id: 'necessidades', name: 'Necessidades MRP', desc: 'Itens a comprar, sugestões' }
                ]
            },
            producao: {
                name: 'Produção',
                icon: '🏭',
                dados: [
                    { id: 'ordens', name: 'Ordens de Produção', desc: 'Status, apontamentos, eficiência' },
                    { id: 'recursos', name: 'Utilização de Recursos', desc: 'Máquinas, mão de obra, capacidade' },
                    { id: 'estruturas', name: 'Estruturas de Produto', desc: 'BOM, onde usado, explosão' },
                    { id: 'apontamentos', name: 'Apontamentos', desc: 'Tempos, quantidades, refugos' }
                ]
            },
            vendas: {
                name: 'Vendas',
                icon: '📈',
                dados: [
                    { id: 'pedidos_venda', name: 'Pedidos de Venda', desc: 'Faturamento, clientes, produtos' },
                    { id: 'clientes', name: 'Análise de Clientes', desc: 'Ranking, inadimplência, potencial' },
                    { id: 'produtos_vendidos', name: 'Produtos Mais Vendidos', desc: 'Ranking, sazonalidade' },
                    { id: 'comissoes', name: 'Comissões', desc: 'Vendedores, metas, performance' }
                ]
            },
            qualidade: {
                name: 'Qualidade',
                icon: '✅',
                dados: [
                    { id: 'inspecoes', name: 'Inspeções', desc: 'Aprovações, rejeições, não conformidades' },
                    { id: 'fornecedores_qualidade', name: 'Qualidade Fornecedores', desc: 'Índices de qualidade por fornecedor' },
                    { id: 'indicadores', name: 'Indicadores de Qualidade', desc: 'KPIs, metas, tendências' },
                    { id: 'certificacoes', name: 'Certificações', desc: 'Status, validades, auditorias' }
                ]
            }
        };

        // Inicialização
        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            setupEventListeners();
            loadSavedReports();
        };

        function setupEventListeners() {
            // Seleção de tipo de relatório
            document.querySelectorAll('.selection-card').forEach(card => {
                card.addEventListener('click', () => {
                    document.querySelectorAll('.selection-card').forEach(c => c.classList.remove('selected'));
                    card.classList.add('selected');
                    reportConfig.tipo = card.dataset.type;
                });
            });

            // Período pré-definido
            document.getElementById('periodoPredefinido').addEventListener('change', (e) => {
                if (e.target.value) {
                    const dates = getPredefinedDates(e.target.value);
                    document.getElementById('dataInicial').value = dates.inicio;
                    document.getElementById('dataFinal').value = dates.fim;
                }
            });
        }

        function getPredefinedDates(periodo) {
            const hoje = new Date();
            const inicio = new Date();

            switch (periodo) {
                case 'hoje':
                    break;
                case 'semana':
                    inicio.setDate(hoje.getDate() - hoje.getDay());
                    break;
                case 'mes':
                    inicio.setDate(1);
                    break;
                case 'trimestre':
                    const trimestre = Math.floor(hoje.getMonth() / 3);
                    inicio.setMonth(trimestre * 3, 1);
                    break;
                case 'ano':
                    inicio.setMonth(0, 1);
                    break;
            }

            return {
                inicio: inicio.toISOString().split('T')[0],
                fim: hoje.toISOString().split('T')[0]
            };
        }

        window.nextStep = function() {
            if (!validateCurrentStep()) return;

            if (currentStep < 5) {
                // Marcar step atual como completo
                document.querySelector(`[data-step="${currentStep}"]`).classList.add('completed');

                currentStep++;
                updateStepDisplay();

                if (currentStep === 2) {
                    loadSpecificDataOptions();
                } else if (currentStep === 3) {
                    loadFiltersForType();
                } else if (currentStep === 4) {
                    generatePreview();
                } else if (currentStep === 5) {
                    prepareFinalization();
                }
            }
        };

        window.previousStep = function() {
            if (currentStep > 1) {
                // Remover completed do step atual
                document.querySelector(`[data-step="${currentStep}"]`).classList.remove('completed');

                currentStep--;
                updateStepDisplay();
            }
        };

        function updateStepDisplay() {
            // Atualizar steps visuais
            document.querySelectorAll('.wizard-step').forEach((step, index) => {
                step.classList.remove('active');
                if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });

            // Mostrar/ocultar conteúdo
            document.querySelectorAll('.step-content').forEach((content, index) => {
                content.classList.remove('active');
                if (index + 1 === currentStep) {
                    content.classList.add('active');
                }
            });

            // Atualizar botões
            document.getElementById('btnAnterior').style.display = currentStep > 1 ? 'inline-flex' : 'none';
            document.getElementById('btnProximo').style.display = currentStep < 5 ? 'inline-flex' : 'none';
            document.getElementById('btnGerar').style.display = currentStep === 5 ? 'inline-flex' : 'none';
        }

        function validateCurrentStep() {
            switch (currentStep) {
                case 1:
                    if (!reportConfig.tipo) {
                        alert('Selecione um tipo de relatório!');
                        return false;
                    }
                    break;
                case 2:
                    const selectedData = document.querySelectorAll('#specificDataOptions .selection-card.selected');
                    if (selectedData.length === 0) {
                        alert('Selecione pelo menos um tipo de dado!');
                        return false;
                    }
                    reportConfig.dados = Array.from(selectedData).map(card => card.dataset.id);
                    break;
                case 3:
                    const dataInicial = document.getElementById('dataInicial').value;
                    const dataFinal = document.getElementById('dataFinal').value;
                    if (!dataInicial || !dataFinal) {
                        alert('Defina o período do relatório!');
                        return false;
                    }
                    reportConfig.filtros = {
                        dataInicial,
                        dataFinal,
                        ...getAdditionalFilters()
                    };
                    break;
                case 4:
                    // Validação da prévia (opcional)
                    break;
                case 5:
                    const nome = document.getElementById('nomeRelatorio').value;
                    if (!nome.trim()) {
                        alert('Digite um nome para o relatório!');
                        return false;
                    }
                    reportConfig.nome = nome;
                    reportConfig.descricao = document.getElementById('descricaoRelatorio').value;
                    break;
            }
            return true;
        }

        function loadSpecificDataOptions() {
            const container = document.getElementById('specificDataOptions');
            const typeConfig = reportTypes[reportConfig.tipo];

            if (!typeConfig) return;

            container.innerHTML = `
                <div class="selection-grid">
                    ${typeConfig.dados.map(dado => `
                        <div class="selection-card" data-id="${dado.id}">
                            <div class="icon">${typeConfig.icon}</div>
                            <h4>${dado.name}</h4>
                            <p>${dado.desc}</p>
                        </div>
                    `).join('')}
                </div>

                <div class="ai-suggestions">
                    <h4>🤖 IA Auto-Completou sua Seleção</h4>
                    <div class="suggestion-item">
                        ✅ Baseado no tipo "${typeConfig.name}", selecionei os dados mais relevantes
                    </div>
                    <div class="suggestion-item">
                        💡 Você pode ajustar a seleção clicando nos cards acima
                    </div>
                </div>
            `;

            // Auto-selecionar os primeiros 2 itens (IA inteligente)
            setTimeout(() => {
                const cards = container.querySelectorAll('.selection-card');
                cards.forEach((card, index) => {
                    if (index < 2) {
                        card.classList.add('selected');
                    }

                    card.addEventListener('click', () => {
                        card.classList.toggle('selected');
                    });
                });
            }, 100);
        }

        function loadFiltersForType() {
            const container = document.getElementById('additionalFilters');
            const typeConfig = reportTypes[reportConfig.tipo];

            let additionalFilters = '';

            switch (reportConfig.tipo) {
                case 'estoque':
                    additionalFilters = `
                        <label>Armazém:</label>
                        <select id="filtroArmazem">
                            <option value="">Todos os Armazéns</option>
                            <option value="PRINCIPAL">Principal</option>
                            <option value="QUALIDADE">Qualidade</option>
                            <option value="EXPEDICAO">Expedição</option>
                        </select>
                    `;
                    break;
                case 'compras':
                    additionalFilters = `
                        <label>Status:</label>
                        <select id="filtroStatus">
                            <option value="">Todos os Status</option>
                            <option value="PENDENTE">Pendente</option>
                            <option value="APROVADO">Aprovado</option>
                            <option value="ENVIADO">Enviado</option>
                            <option value="RECEBIDO">Recebido</option>
                        </select>
                    `;
                    break;
                case 'producao':
                    additionalFilters = `
                        <label>Setor:</label>
                        <select id="filtroSetor">
                            <option value="">Todos os Setores</option>
                            <option value="CORTE">Corte</option>
                            <option value="SOLDA">Solda</option>
                            <option value="MONTAGEM">Montagem</option>
                            <option value="ACABAMENTO">Acabamento</option>
                        </select>
                    `;
                    break;
                default:
                    additionalFilters = `
                        <label>Filtro Adicional:</label>
                        <input type="text" id="filtroGeral" placeholder="Filtro personalizado">
                    `;
            }

            container.innerHTML = additionalFilters;
        }

        function getAdditionalFilters() {
            const filters = {};

            // Capturar filtros específicos baseado no tipo
            const armazem = document.getElementById('filtroArmazem');
            if (armazem) filters.armazem = armazem.value;

            const status = document.getElementById('filtroStatus');
            if (status) filters.status = status.value;

            const setor = document.getElementById('filtroSetor');
            if (setor) filters.setor = setor.value;

            const geral = document.getElementById('filtroGeral');
            if (geral) filters.geral = geral.value;

            return filters;
        }

        function generatePreview() {
            const preview = document.getElementById('reportPreview');
            const typeConfig = reportTypes[reportConfig.tipo];

            preview.innerHTML = `
                <h4>📊 Prévia do Relatório: ${typeConfig.name}</h4>
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0;">
                    <h5>Configuração:</h5>
                    <ul>
                        <li><strong>Tipo:</strong> ${typeConfig.name}</li>
                        <li><strong>Dados:</strong> ${reportConfig.dados.length} seleções</li>
                        <li><strong>Período:</strong> ${reportConfig.filtros.dataInicial} até ${reportConfig.filtros.dataFinal}</li>
                        <li><strong>Filtros:</strong> ${Object.keys(reportConfig.filtros).length - 2} filtros adicionais</li>
                    </ul>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid var(--success-color);">
                    <h5>🤖 IA Analisou sua Configuração:</h5>
                    <p>✅ Configuração válida e otimizada</p>
                    <p>📊 Estimativa: ${Math.floor(Math.random() * 500) + 100} registros encontrados</p>
                    <p>⏱️ Tempo estimado de geração: ${Math.floor(Math.random() * 30) + 5} segundos</p>
                </div>

                <div style="margin-top: 20px;">
                    <h5>Dados que serão incluídos:</h5>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px;">
                        ${reportConfig.dados.map(dadoId => {
                            const dado = typeConfig.dados.find(d => d.id === dadoId);
                            return `
                                <div style="background: white; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6;">
                                    <strong>${dado.name}</strong><br>
                                    <small>${dado.desc}</small>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        }

        function prepareFinalization() {
            // Auto-gerar nome sugerido
            const typeConfig = reportTypes[reportConfig.tipo];
            const dataAtual = new Date().toLocaleDateString('pt-BR');
            const nomeSugerido = `Relatório ${typeConfig.name} - ${dataAtual}`;

            document.getElementById('nomeRelatorio').value = nomeSugerido;

            // Sugerir descrição
            const descricaoSugerida = `Relatório de ${typeConfig.name.toLowerCase()} gerado automaticamente com ${reportConfig.dados.length} tipos de dados para o período de ${reportConfig.filtros.dataInicial} até ${reportConfig.filtros.dataFinal}.`;
            document.getElementById('descricaoRelatorio').value = descricaoSugerida;
        }

        window.gerarRelatorio = async function() {
            if (!validateCurrentStep()) return;

            try {
                // Salvar configuração do relatório
                const relatorioConfig = {
                    ...reportConfig,
                    numero: `REL-${Date.now()}`,
                    criadoEm: Timestamp.now(),
                    criadoPor: currentUser.nome,
                    status: 'GERADO',
                    salvarModelo: document.getElementById('salvarModelo').checked
                };

                await addDoc(collection(db, "relatoriosGerados"), relatorioConfig);

                // Simular geração do relatório
                alert(`✅ Relatório "${reportConfig.nome}" gerado com sucesso!\n\nNúmero: ${relatorioConfig.numero}\nTipo: ${reportTypes[reportConfig.tipo].name}\nDados: ${reportConfig.dados.length} seleções`);

                // Reset do wizard
                resetWizard();
                loadSavedReports();

            } catch (error) {
                console.error('Erro ao gerar relatório:', error);
                alert('Erro ao gerar relatório: ' + error.message);
            }
        };

        function resetWizard() {
            currentStep = 1;
            reportConfig = {
                tipo: '',
                dados: [],
                filtros: {},
                nome: '',
                descricao: ''
            };

            // Limpar seleções
            document.querySelectorAll('.selection-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Limpar formulários
            document.getElementById('nomeRelatorio').value = '';
            document.getElementById('descricaoRelatorio').value = '';
            document.getElementById('dataInicial').value = '';
            document.getElementById('dataFinal').value = '';
            document.getElementById('salvarModelo').checked = false;

            updateStepDisplay();
        }

        async function loadSavedReports() {
            try {
                const reportsSnap = await getDocs(
                    query(
                        collection(db, "relatoriosGerados"),
                        orderBy("criadoEm", "desc"),
                        limit(10)
                    )
                );

                const container = document.getElementById('savedReportsList');

                if (reportsSnap.empty) {
                    container.innerHTML = '<p>Nenhum relatório salvo encontrado.</p>';
                    return;
                }

                container.innerHTML = reportsSnap.docs.map(doc => {
                    const data = doc.data();
                    const typeConfig = reportTypes[data.tipo];

                    return `
                        <div class="report-item">
                            <div class="report-info">
                                <h5>${typeConfig?.icon || '📊'} ${data.nome}</h5>
                                <p>Tipo: ${typeConfig?.name || data.tipo} • Criado em: ${data.criadoEm?.toDate().toLocaleDateString('pt-BR')} • Por: ${data.criadoPor}</p>
                            </div>
                            <div class="report-actions">
                                <button class="btn btn-primary" onclick="executarRelatorio('${doc.id}')">
                                    ▶️ Executar
                                </button>
                                <button class="btn btn-secondary" onclick="duplicarRelatorio('${doc.id}')">
                                    📋 Duplicar
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');

            } catch (error) {
                console.error('Erro ao carregar relatórios:', error);
                document.getElementById('savedReportsList').innerHTML = '<p>Erro ao carregar relatórios salvos.</p>';
            }
        }

        window.executarRelatorio = function(relatorioId) {
            // Simular execução do relatório
            alert(`🚀 Executando relatório ${relatorioId}...\n\nEm um sistema real, isso abriria o relatório ou iniciaria a geração.`);
        };

        window.duplicarRelatorio = async function(relatorioId) {
            try {
                // Buscar configuração do relatório
                const reportsSnap = await getDocs(collection(db, "relatoriosGerados"));
                const relatorio = reportsSnap.docs.find(doc => doc.id === relatorioId);

                if (relatorio) {
                    const data = relatorio.data();

                    // Carregar configuração no wizard
                    reportConfig = {
                        tipo: data.tipo,
                        dados: data.dados,
                        filtros: data.filtros,
                        nome: data.nome + ' (Cópia)',
                        descricao: data.descricao
                    };

                    // Ir para o último passo
                    currentStep = 5;
                    updateStepDisplay();
                    prepareFinalization();

                    alert('📋 Relatório duplicado! Ajuste as configurações e gere novamente.');
                }

            } catch (error) {
                console.error('Erro ao duplicar relatório:', error);
                alert('Erro ao duplicar relatório: ' + error.message);
            }
        };

        window.selectSuggestion = function(tipo) {
            // Selecionar tipo sugerido pela IA
            const card = document.querySelector(`[data-type="${tipo}"]`);
            if (card) {
                document.querySelectorAll('.selection-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                reportConfig.tipo = tipo;

                // Avançar automaticamente
                setTimeout(() => {
                    nextStep();
                }, 500);
            }
        };

        // Atalhos de teclado
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                if (currentStep < 5) {
                    nextStep();
                } else {
                    gerarRelatorio();
                }
            } else if (e.key === 'Escape') {
                if (currentStep > 1) {
                    previousStep();
                }
            }
        });
    </script>
</body>
</html>
