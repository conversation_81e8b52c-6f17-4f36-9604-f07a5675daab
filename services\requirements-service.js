// Serviço centralizado para necessidades e empenhos
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  runTransaction, 
  addDoc, 
  updateDoc, 
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class RequirementsService {
  
  /**
   * Calcula necessidades de compra baseado em OPs ativas
   * @param {Array} ordensProducao - Ordens de produção ativas
   * @param {Array} estoques - Estoques atuais
   * @param {Array} produtos - Catálogo de produtos
   * @returns {Promise<Array>} Lista de necessidades calculadas
   */
  static async calculateRequirements(ordensProducao, estoques, produtos) {
    try {
      const necessidadesMap = new Map();
      
      // Processar cada ordem de produção
      for (const op of ordensProducao) {
        if (!op.materiaisNecessarios || op.status === 'FINALIZADA') continue;
        
        for (const material of op.materiaisNecessarios) {
          const produto = produtos.find(p => p.id === material.produtoId);
          if (!produto) continue;
          
          // Calcular necessidade total considerando quantidade da OP
          const necessidadeTotal = material.quantidade * op.quantidade;
          
          // Buscar estoque atual
          const estoque = estoques.find(e => e.produtoId === material.produtoId);
          const saldoAtual = estoque?.saldo || 0;
          const saldoReservado = estoque?.saldoReservado || 0;
          const saldoDisponivel = saldoAtual - saldoReservado;
          
          // Calcular necessidade líquida
          const necessidadeLiquida = Math.max(0, necessidadeTotal - saldoDisponivel);
          
          // Aplicar políticas de estoque
          const necessidadeAjustada = this.applyStockPolicies(
            necessidadeLiquida, 
            produto, 
            saldoAtual
          );
          
          // Criar ou atualizar necessidade
          if (!necessidadesMap.has(produto.id)) {
            necessidadesMap.set(produto.id, {
              produtoId: produto.id,
              produto: {
                id: produto.id,
                codigo: produto.codigo,
                descricao: produto.descricao,
                unidade: produto.unidade,
                grupo: produto.grupo || 'Sem Grupo',
                familia: produto.familia || 'Sem Família',
                tipo: produto.tipo,
                leadTime: produto.leadTime || 0,
                pontoPedido: produto.pontoPedido || 0,
                estoqueMinimo: produto.estoqueMinimo || 0,
                loteCompra: produto.loteCompra || 1
              },
              quantidadeNecessaria: 0,
              saldoAtual,
              saldoReservado,
              saldoDisponivel,
              necessidadeLiquida: 0,
              necessidadeAjustada: 0,
              ordens: new Set(),
              consumoPorOP: new Map(),
              criticidade: this.calculateCriticality(produto, saldoAtual),
              dataCalculo: Timestamp.now()
            });
          }
          
          const necessidade = necessidadesMap.get(produto.id);
          necessidade.quantidadeNecessaria += necessidadeTotal;
          necessidade.necessidadeLiquida += necessidadeLiquida;
          necessidade.necessidadeAjustada += necessidadeAjustada;
          necessidade.ordens.add(op.numero);
          necessidade.consumoPorOP.set(op.numero, necessidadeTotal);
        }
      }
      
      // Converter Map para Array e filtrar apenas necessidades > 0
      const necessidades = Array.from(necessidadesMap.values())
        .filter(n => n.necessidadeAjustada > 0)
        .sort((a, b) => {
          // Ordenar por criticidade e depois por quantidade
          const critOrder = { 'CRITICA': 0, 'ALTA': 1, 'MEDIA': 2, 'BAIXA': 3 };
          const critA = critOrder[a.criticidade] || 4;
          const critB = critOrder[b.criticidade] || 4;
          
          if (critA !== critB) return critA - critB;
          return b.necessidadeAjustada - a.necessidadeAjustada;
        });
      
      // Salvar necessidades calculadas
      await this.saveCalculatedRequirements(necessidades);
      
      return necessidades;
      
    } catch (error) {
      console.error('Erro ao calcular necessidades:', error);
      throw new Error(`Falha no cálculo de necessidades: ${error.message}`);
    }
  }
  
  /**
   * Aplica políticas de estoque (lote mínimo, estoque de segurança, etc.)
   */
  static applyStockPolicies(necessidadeLiquida, produto, saldoAtual) {
    let necessidadeAjustada = necessidadeLiquida;
    
    // Verificar estoque mínimo
    if (saldoAtual < produto.estoqueMinimo) {
      necessidadeAjustada = Math.max(
        necessidadeAjustada, 
        produto.estoqueMinimo - saldoAtual
      );
    }
    
    // Aplicar lote de compra
    if (produto.loteCompra > 1 && necessidadeAjustada > 0) {
      necessidadeAjustada = Math.ceil(necessidadeAjustada / produto.loteCompra) * produto.loteCompra;
    }
    
    // Verificar ponto de pedido
    if (produto.pontoPedido > 0 && saldoAtual <= produto.pontoPedido) {
      necessidadeAjustada = Math.max(necessidadeAjustada, produto.loteCompra || 1);
    }
    
    return necessidadeAjustada;
  }
  
  /**
   * Calcula criticidade baseada em estoque e lead time
   */
  static calculateCriticality(produto, saldoAtual) {
    const estoqueMinimo = produto.estoqueMinimo || 0;
    const pontoPedido = produto.pontoPedido || 0;
    const leadTime = produto.leadTime || 0;
    
    // Sem estoque
    if (saldoAtual <= 0) return 'CRITICA';
    
    // Abaixo do estoque mínimo
    if (saldoAtual < estoqueMinimo) return 'ALTA';
    
    // Abaixo do ponto de pedido
    if (saldoAtual <= pontoPedido) return 'MEDIA';
    
    // Lead time longo e estoque baixo
    if (leadTime > 30 && saldoAtual < (estoqueMinimo * 2)) return 'MEDIA';
    
    return 'BAIXA';
  }
  
  /**
   * Salva necessidades calculadas no Firestore
   */
  static async saveCalculatedRequirements(necessidades) {
    try {
      const batch = [];
      
      for (const necessidade of necessidades) {
        const necessidadeRef = doc(db, 'necessidadesCompra', necessidade.produtoId);
        
        // Converter Sets e Maps para arrays para serialização
        const necessidadeData = {
          ...necessidade,
          ordens: Array.from(necessidade.ordens),
          consumoPorOP: Object.fromEntries(necessidade.consumoPorOP),
          ultimaAtualizacao: Timestamp.now()
        };
        
        batch.push(updateDoc(necessidadeRef, necessidadeData));
      }
      
      await Promise.all(batch);
      console.log(`${necessidades.length} necessidades salvas com sucesso`);
      
    } catch (error) {
      console.error('Erro ao salvar necessidades:', error);
      throw error;
    }
  }
  
  /**
   * Calcula e atualiza empenhos de material
   */
  static async calculateReservations(ordensProducao, estoques) {
    try {
      const empenhosMap = new Map();
      
      // Calcular empenhos por produto/armazém
      for (const op of ordensProducao) {
        if (!op.materiaisNecessarios || op.status === 'FINALIZADA') continue;
        
        for (const material of op.materiaisNecessarios) {
          const key = `${material.produtoId}_${op.armazemProducaoId}`;
          const quantidadeEmpenho = material.quantidade * op.quantidade;
          
          if (!empenhosMap.has(key)) {
            empenhosMap.set(key, {
              produtoId: material.produtoId,
              armazemId: op.armazemProducaoId,
              quantidadeEmpenhada: 0,
              ordens: []
            });
          }
          
          const empenho = empenhosMap.get(key);
          empenho.quantidadeEmpenhada += quantidadeEmpenho;
          empenho.ordens.push({
            opId: op.id,
            opNumero: op.numero,
            quantidade: quantidadeEmpenho
          });
        }
      }
      
      // Atualizar estoques com novos empenhos
      const updates = [];
      
      for (const [key, empenho] of empenhosMap) {
        const estoque = estoques.find(e => 
          e.produtoId === empenho.produtoId && 
          e.armazemId === empenho.armazemId
        );
        
        if (estoque) {
          updates.push(
            updateDoc(doc(db, "estoques", estoque.id), {
              saldoReservado: empenho.quantidadeEmpenhada,
              ultimaAtualizacaoEmpenho: Timestamp.now(),
              ordensEmpenho: empenho.ordens
            })
          );
        }
      }
      
      await Promise.all(updates);
      console.log(`${updates.length} empenhos atualizados`);
      
      return empenhosMap;
      
    } catch (error) {
      console.error('Erro ao calcular empenhos:', error);
      throw new Error(`Falha no cálculo de empenhos: ${error.message}`);
    }
  }
  
  /**
   * Gera solicitações de compra baseadas nas necessidades
   */
  static async generatePurchaseRequests(necessidades, agrupamento = 'familia') {
    try {
      const solicitacoes = [];
      const grupos = this.groupRequirements(necessidades, agrupamento);
      
      for (const [chave, itens] of Object.entries(grupos)) {
        if (itens.length === 0) continue;
        
        const numeroSolicitacao = await this.generateRequestNumber();
        
        const solicitacao = {
          numero: numeroSolicitacao,
          dataCriacao: Timestamp.now(),
          status: 'PENDENTE',
          tipo: 'PLANEJADA',
          origem: 'MRP',
          agrupamento: chave,
          solicitante: 'Sistema MRP',
          departamento: 'PRODUCAO',
          prioridade: this.determinePriority(itens),
          itens: itens.map(item => ({
            produtoId: item.produtoId,
            codigo: item.produto.codigo,
            descricao: item.produto.descricao,
            quantidade: item.necessidadeAjustada,
            unidade: item.produto.unidade,
            ordensOrigem: Array.from(item.ordens)
          })),
          valorEstimado: this.calculateEstimatedValue(itens),
          justificativa: this.generateJustification(itens, chave),
          mrpInfo: {
            dataAnalise: Timestamp.now(),
            ordensProducao: [...new Set(itens.flatMap(i => Array.from(i.ordens)))]
          }
        };
        
        const docRef = await addDoc(collection(db, "solicitacoesCompra"), solicitacao);
        solicitacao.id = docRef.id;
        solicitacoes.push(solicitacao);
      }
      
      console.log(`${solicitacoes.length} solicitações geradas`);
      return solicitacoes;
      
    } catch (error) {
      console.error('Erro ao gerar solicitações:', error);
      throw new Error(`Falha na geração de solicitações: ${error.message}`);
    }
  }
  
  /**
   * Agrupa necessidades por critério
   */
  static groupRequirements(necessidades, criterio) {
    const grupos = {};
    
    necessidades.forEach(necessidade => {
      let chave;
      
      switch (criterio) {
        case 'familia':
          chave = necessidade.produto.familia || 'Sem Família';
          break;
        case 'grupo':
          chave = necessidade.produto.grupo || 'Sem Grupo';
          break;
        case 'criticidade':
          chave = necessidade.criticidade;
          break;
        default:
          chave = 'Geral';
      }
      
      if (!grupos[chave]) grupos[chave] = [];
      grupos[chave].push(necessidade);
    });
    
    return grupos;
  }
  
  /**
   * Determina prioridade baseada na criticidade dos itens
   */
  static determinePriority(itens) {
    const criticidades = itens.map(i => i.criticidade);
    
    if (criticidades.includes('CRITICA')) return 'URGENTE';
    if (criticidades.includes('ALTA')) return 'ALTA';
    if (criticidades.includes('MEDIA')) return 'NORMAL';
    return 'BAIXA';
  }
  
  /**
   * Calcula valor estimado da solicitação
   */
  static calculateEstimatedValue(itens) {
    return itens.reduce((total, item) => {
      const precoEstimado = item.produto.precoUltimaCompra || 
                           item.produto.precoMedio || 
                           0;
      return total + (item.necessidadeAjustada * precoEstimado);
    }, 0);
  }
  
  /**
   * Gera justificativa automática
   */
  static generateJustification(itens, agrupamento) {
    const ordensUnicas = [...new Set(itens.flatMap(i => Array.from(i.ordens)))];
    
    return `Solicitação gerada automaticamente pelo MRP para ${agrupamento}.
    
Itens necessários: ${itens.length}
Ordens de Produção relacionadas: ${ordensUnicas.join(', ')}
Criticidade máxima: ${this.determinePriority(itens)}

Detalhamento:
${itens.map(i => `- ${i.produto.codigo}: ${i.necessidadeAjustada} ${i.produto.unidade}`).join('\n')}`;
  }
  
  /**
   * Gera número sequencial para solicitação
   */
  static async generateRequestNumber() {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Buscar último número
    const lastRequest = await getDocs(
      query(
        collection(db, "solicitacoesCompra"),
        where("numero", ">=", `SC${year}${month}`),
        where("numero", "<", `SC${year}${month}9999`),
        orderBy("numero", "desc")
      )
    );
    
    let nextNumber = 1;
    if (!lastRequest.empty) {
      const lastNumber = lastRequest.docs[0].data().numero;
      const lastSequence = parseInt(lastNumber.slice(-4));
      nextNumber = lastSequence + 1;
    }
    
    return `SC${year}${month}${String(nextNumber).padStart(4, '0')}`;
  }
}

// Exportar para uso global
window.RequirementsService = RequirementsService;
