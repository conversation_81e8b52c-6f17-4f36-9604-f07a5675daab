<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitações de Compra - Sistema ERP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .main-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.pendentes {
            border-left-color: #f39c12;
        }

        .stat-card.aprovadas {
            border-left-color: #27ae60;
        }

        .stat-card.em-cotacao {
            border-left-color: #3498db;
        }

        .stat-card.finalizadas {
            border-left-color: #2ecc71;
        }

        .stat-card.rejeitadas {
            border-left-color: #e74c3c;
        }

        .stat-card.total {
            border-left-color: #9b59b6;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .filters {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-bar {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 18px;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-aprovada {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-rejeitada {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-excluida {
            background: #6c757d;
            color: #ffffff;
            border: 1px solid #5a6268;
            text-decoration: line-through;
        }

        .status-cotacao {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .status-finalizada {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Estilos para links de rastreabilidade */
        .rastreabilidade-links {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }

        .link-rastreabilidade {
            display: inline-block;
            background: #f8f9fa;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            text-decoration: none;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .link-rastreabilidade:hover {
            background: #e9ecef;
            color: #212529;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .priority {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .priority-alta {
            background: #ffebee;
            color: #c62828;
        }

        .priority-media {
            background: #fff3e0;
            color: #ef6c00;
        }

        .priority-baixa {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 1200px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease;
            max-height: 90vh;
            overflow-y: auto;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close:hover {
            transform: scale(1.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            opacity: 1;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .items-table th,
        .items-table td {
            padding: 12px;
            border: 1px solid #e9ecef;
            text-align: left;
        }

        .items-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 25px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            background: white;
            color: #495057;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            border-color: #3498db;
            color: #3498db;
        }

        .pagination button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Estilos para modal de edição melhorado */
        #newRequestModal .modal-content {
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 12px;
        }

        #newRequestModal .form-section {
            transition: all 0.3s ease;
        }

        #newRequestModal .form-section:hover {
            background: #fafbfc;
        }

        #newRequestModal .section-header {
            font-size: 14px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        #newRequestModal .section-header:hover {
            background: #e9ecef !important;
        }

        #newRequestModal .form-control {
            transition: all 0.3s ease;
            font-size: 14px;
        }

        #newRequestModal .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            transform: translateY(-1px);
        }

        #newRequestModal .btn {
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 12px;
        }

        #newRequestModal .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        #newRequestModal .btn-success:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%) !important;
        }

        /* Animação para seções */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #newRequestModal .form-section {
            animation: slideInUp 0.4s ease-out;
        }

        #newRequestModal .form-section:nth-child(2) { animation-delay: 0.1s; }
        #newRequestModal .form-section:nth-child(3) { animation-delay: 0.2s; }
        #newRequestModal .form-section:nth-child(4) { animation-delay: 0.3s; }
        #newRequestModal .form-section:nth-child(5) { animation-delay: 0.4s; }

        /* Melhorias nos campos de formulário */
        #newRequestModal select option {
            padding: 8px;
        }

        #newRequestModal textarea {
            font-family: inherit;
        }

        /* Indicadores visuais para campos obrigatórios */
        #newRequestModal .form-control:required:invalid {
            border-color: #dc3545;
        }

        #newRequestModal .form-control:required:valid {
            border-color: #28a745;
        }

        /* Estilo especial para o container de itens vazio */
        #newRequestModal #itemsContainer .empty-state {
            transition: all 0.3s ease;
        }

        #newRequestModal #itemsContainer .empty-state:hover {
            background: #f1f3f4 !important;
            border-color: #28a745 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-file-alt"></i> Solicitações de Compra</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="openNewRequestModal()">
                    <i class="fas fa-plus"></i> Nova Solicitação
                </button>
                <a href="cadastro_centro_custo.html" class="btn btn-warning" target="_blank" title="Gerenciar Centros de Custo">
                    <i class="fas fa-building"></i> Centros de Custo
                </a>
                <a href="cadastro_setores.html" class="btn btn-warning" target="_blank" title="Gerenciar Setores">
                    <i class="fas fa-sitemap"></i> Setores
                </a>
                <button class="btn btn-info" onclick="exportReport()">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <button class="btn btn-warning" onclick="showUrgentRequests()">
                    <i class="fas fa-exclamation-triangle"></i> Urgentes
                </button>
                <a href="index.html" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card pendentes">
                    <div class="stat-number" id="statPendentes">0</div>
                    <div class="stat-label">🟡 Pendentes</div>
                </div>
                <div class="stat-card aprovadas">
                    <div class="stat-number" id="statAprovadas">0</div>
                    <div class="stat-label">🟢 Aprovadas</div>
                </div>
                <div class="stat-card em-cotacao">
                    <div class="stat-number" id="statEmCotacao">0</div>
                    <div class="stat-label">📋 Em Cotação</div>
                </div>
                <div class="stat-card finalizadas">
                    <div class="stat-number" id="statFinalizadas">0</div>
                    <div class="stat-label">✅ Finalizadas</div>
                </div>
                <div class="stat-card rejeitadas">
                    <div class="stat-number" id="statRejeitadas">0</div>
                    <div class="stat-label">❌ Rejeitadas</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-number" id="statTotal">0</div>
                    <div class="stat-label">📊 Total</div>
                </div>
            </div>

            <!-- Barra de Pesquisa -->
            <div class="search-bar">
                <input type="text" class="search-input" id="searchInput" placeholder="Pesquisar por número, solicitante, produto...">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- Ações Rápidas -->
            <div class="quick-actions">
                <button class="btn btn-primary" onclick="showAllRequests()">
                    <i class="fas fa-list"></i> Todas
                </button>
                <button class="btn btn-warning" onclick="showPendingRequests()">
                    <i class="fas fa-clock"></i> Pendentes
                </button>
                <button class="btn btn-success" onclick="showApprovedRequests()">
                    <i class="fas fa-check"></i> Aprovadas
                </button>
                <button class="btn btn-info" onclick="showQuotationRequests()">
                    <i class="fas fa-file-invoice"></i> Em Cotação
                </button>
                <button class="btn btn-success" onclick="showFinalizedRequests()">
                    <i class="fas fa-check-circle"></i> Finalizadas
                </button>
                <button class="btn btn-secondary" onclick="showMyRequests()">
                    <i class="fas fa-user"></i> Minhas Solicitações
                </button>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <h3><i class="fas fa-filter"></i> Filtros Avançados</h3>
                <div class="filter-row">
                    <div class="form-group">
                        <label>Data Início</label>
                        <input type="date" class="form-control" id="dataInicio">
                    </div>
                    <div class="form-group">
                        <label>Data Fim</label>
                        <input type="date" class="form-control" id="dataFim">
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <select class="form-control" id="statusFilter">
                            <option value="">Todos</option>
                            <option value="PENDENTE">Pendente</option>
                            <option value="APROVADA">Aprovada</option>
                            <option value="REJEITADA">Rejeitada</option>
                            <option value="EM_COTACAO">Em Cotação</option>
                            <option value="COTADO">Cotado</option>
                            <option value="FINALIZADA">Finalizada</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tipo</label>
                        <select class="form-control" id="typeFilter">
                            <option value="">Todos</option>
                            <option value="NORMAL">Normal</option>
                            <option value="PLANEJADA">Planejada</option>
                            <option value="EMERGENCIAL">Emergencial</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Origem</label>
                        <select class="form-control" id="originFilter">
                            <option value="">Todas</option>
                            <option value="MANUAL">Manual</option>
                            <option value="MRP">MRP (Automático)</option>
                            <option value="ESTOQUE_MINIMO">Estoque Mínimo</option>
                            <option value="MANUTENCAO">Manutenção</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Prioridade</label>
                        <select class="form-control" id="priorityFilter">
                            <option value="">Todas</option>
                            <option value="NORMAL">Normal</option>
                            <option value="URGENTE">Urgente</option>
                            <option value="CRITICA">Crítica</option>
                        </select>
                    </div>
                </div>
                <div class="filter-row">
                    <div class="form-group">
                        <label>Departamento</label>
                        <select class="form-control" id="departmentFilter">
                            <option value="">Todos</option>
                            <option value="ENGENHARIA">Engenharia</option>
                            <option value="PRODUCAO">Produção</option>
                            <option value="MANUTENCAO">Manutenção</option>
                            <option value="QUALIDADE">Qualidade</option>
                            <option value="ADMINISTRATIVO">Administrativo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Solicitante</label>
                        <input type="text" class="form-control" id="requesterFilter" placeholder="Nome do solicitante">
                    </div>
                    <div class="form-group">
                        <label>Exibição</label>
                        <select class="form-control" id="displayFilter">
                            <option value="active">🟢 Apenas Ativas (Pendente/Aprovada)</option>
                            <option value="quoted">📋 Em Cotação</option>
                            <option value="finalized">✅ Finalizadas (Pedidos)</option>
                            <option value="excluded">❌ Excluídas</option>
                            <option value="all">📊 Todas (exceto excluídas)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Ordenação</label>
                        <select class="form-control" id="sortOrder">
                            <option value="date_desc">📅 Data (Mais Recente)</option>
                            <option value="date_asc">📅 Data (Mais Antiga)</option>
                            <option value="number_desc">🔢 Número (Decrescente)</option>
                            <option value="number_asc">🔢 Número (Crescente)</option>
                            <option value="priority_desc">⚡ Prioridade (Alta → Baixa)</option>
                            <option value="priority_asc">⚡ Prioridade (Baixa → Alta)</option>
                            <option value="value_desc">💰 Valor (Maior → Menor)</option>
                            <option value="value_asc">💰 Valor (Menor → Maior)</option>
                        </select>
                    </div>
                </div>
                <div style="display: flex; gap: 15px; justify-content: flex-end;">
                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-filter"></i> Aplicar Filtros
                    </button>
                    <button class="btn btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times"></i> Limpar
                    </button>
                </div>
            </div>

            <!-- Tabela de Solicitações -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Tipo</th>
                            <th>Origem</th>
                            <th>Solicitante</th>
                            <th>Departamento</th>
                            <th>Itens</th>
                            <th>Valor Total</th>
                            <th>Prioridade</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="requestsTableBody">
                        <!-- Dados carregados dinamicamente -->
                    </tbody>
                </table>
            </div>

            <!-- Paginação -->
            <div class="pagination" id="pagination">
                <!-- Controles de paginação serão inseridos aqui -->
            </div>
        </div>
    </div>

    <!-- Modal Nova Solicitação -->
    <div id="newRequestModal" class="modal">
        <div class="modal-content" style="max-width: 1200px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 8px 8px 0 0;">
                <h2><i class="fas fa-plus"></i> Nova Solicitação de Compra</h2>
                <span class="close" onclick="closeModal('newRequestModal')" style="color: white; font-size: 28px;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 0;">
                <form id="newRequestForm">
                    <input type="hidden" id="editingRequestId" value="">

                    <!-- Seção: Informações Básicas -->
                    <div class="form-section" style="margin: 0; border-radius: 0; border: none; border-bottom: 1px solid #dee2e6;">
                        <div class="section-header" style="background: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #dee2e6;">
                            <i class="fas fa-info-circle" style="color: #28a745;"></i>
                            <span style="font-weight: 600; color: #495057;">Informações Básicas</span>
                        </div>
                        <div class="section-body" style="padding: 20px;">
                            <div class="form-row">
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">Solicitante</label>
                                    <input type="text" class="form-control" id="solicitante" readonly style="background: #f8f9fa; font-weight: bold; border: 2px solid #e9ecef;">
                                </div>
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">Tipo <span style="color: #dc3545;">*</span></label>
                                    <select class="form-control" id="tipo" required style="border: 2px solid #e9ecef;">
                                        <option value="">Selecione...</option>
                                        <option value="NORMAL">📋 Normal</option>
                                        <option value="PLANEJADA">📅 Planejada</option>
                                        <option value="EMERGENCIAL">🚨 Emergencial</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">Origem <span style="color: #dc3545;">*</span></label>
                                    <select class="form-control" id="origem" required style="border: 2px solid #e9ecef;">
                                        <option value="">Selecione...</option>
                                        <option value="MANUAL">👤 Manual</option>
                                        <option value="MRP">🤖 MRP (Automático)</option>
                                        <option value="ESTOQUE_MINIMO">📦 Estoque Mínimo</option>
                                        <option value="MANUTENCAO">🔧 Manutenção</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">Departamento/Setor <span style="color: #dc3545;">*</span></label>
                                    <select class="form-control" id="departamento" required style="border: 2px solid #e9ecef;">
                                        <option value="">Selecione...</option>
                                        <!-- Carregado dinamicamente dos setores cadastrados -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">Prioridade <span style="color: #dc3545;">*</span></label>
                                    <select class="form-control" id="prioridade" required style="border: 2px solid #e9ecef;">
                                        <option value="">Selecione...</option>
                                        <option value="NORMAL">🟢 Normal</option>
                                        <option value="URGENTE">🟡 Urgente</option>
                                        <option value="CRITICA">🔴 Crítica</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">Centro de Custo <span style="color: #dc3545;">*</span></label>
                                    <select class="form-control" id="centroCusto" required style="border: 2px solid #e9ecef;">
                                        <option value="">Selecione um centro de custo...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Seção: Prazos e Datas -->
                    <div class="form-section" style="margin: 0; border-radius: 0; border: none; border-bottom: 1px solid #dee2e6;">
                        <div class="section-header" style="background: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #dee2e6;">
                            <i class="fas fa-calendar-alt" style="color: #17a2b8;"></i>
                            <span style="font-weight: 600; color: #495057;">Prazos e Datas</span>
                        </div>
                        <div class="section-body" style="padding: 20px;">
                            <div class="form-row">
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">📅 Data Necessidade</label>
                                    <input type="date" class="form-control" id="dataNecessidade" style="border: 2px solid #e9ecef;">
                                </div>
                                <div class="form-group">
                                    <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">⏰ Data Limite Aprovação</label>
                                    <input type="date" class="form-control" id="dataLimiteAprovacao" style="border: 2px solid #e9ecef;">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Seção: Justificativa e Observações -->
                    <div class="form-section" style="margin: 0; border-radius: 0; border: none; border-bottom: 1px solid #dee2e6;">
                        <div class="section-header" style="background: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #dee2e6;">
                            <i class="fas fa-comment-alt" style="color: #ffc107;"></i>
                            <span style="font-weight: 600; color: #495057;">Justificativa e Observações</span>
                        </div>
                        <div class="section-body" style="padding: 20px;">
                            <div class="form-group">
                                <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">📝 Justificativa <span style="color: #dc3545;">*</span></label>
                                <textarea class="form-control" id="justificativa" rows="3" placeholder="Descreva a necessidade da compra..." required style="border: 2px solid #e9ecef; resize: vertical;"></textarea>
                            </div>

                            <div class="form-group">
                                <label style="color: #6c757d; font-weight: 600; font-size: 12px; text-transform: uppercase;">💬 Observações</label>
                                <textarea class="form-control" id="observacoes" rows="2" placeholder="Informações adicionais..." style="border: 2px solid #e9ecef; resize: vertical;"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Seção: Itens da Solicitação -->
                    <div class="form-section" style="margin: 0; border-radius: 0; border: none; border-bottom: 1px solid #dee2e6;">
                        <div class="section-header" style="background: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <i class="fas fa-list" style="color: #6f42c1;"></i>
                                <span style="font-weight: 600; color: #495057;">Itens da Solicitação</span>
                            </div>
                            <button type="button" class="btn btn-success btn-sm" onclick="addItem()" style="border-radius: 20px; padding: 8px 16px;">
                                <i class="fas fa-plus"></i> Adicionar Item
                            </button>
                        </div>
                        <div class="section-body" style="padding: 20px;">
                            <div id="itemsContainer">
                                <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
                                    <i class="fas fa-box-open" style="font-size: 48px; color: #6c757d; margin-bottom: 15px;"></i>
                                    <p style="color: #6c757d; margin: 0; font-size: 16px;">
                                        Nenhum item adicionado
                                    </p>
                                    <p style="color: #6c757d; margin: 5px 0 0 0; font-size: 14px;">
                                        Use o botão "Adicionar Item" para incluir produtos
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Seção: Ações -->
                    <div class="form-section" style="margin: 0; border-radius: 0 0 8px 8px; border: none; background: #f8f9fa;">
                        <div class="section-body" style="padding: 20px; display: flex; gap: 15px; justify-content: flex-end;">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('newRequestModal')" style="padding: 12px 24px; border-radius: 25px; font-weight: 600;">
                                <i class="fas fa-times"></i> Cancelar
                            </button>
                            <button type="submit" class="btn btn-success" style="padding: 12px 24px; border-radius: 25px; font-weight: 600; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none;">
                                <i class="fas fa-save"></i> Salvar Solicitação
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Detalhes -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-eye"></i> Detalhes da Solicitação</h2>
                <span class="close" onclick="closeModal('detailsModal')">&times;</span>
            </div>
            <div class="modal-body" id="detailsModalBody">
                <!-- Conteúdo carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Modal Busca de Produtos -->
    <div id="productSearchModal" class="modal">
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h2><i class="fas fa-search"></i> Buscar Produto</h2>
                <span class="close" onclick="closeModal('productSearchModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="search-bar" style="margin-bottom: 20px;">
                    <input type="text" class="search-input" id="productSearchInput" placeholder="Pesquisar por código ou descrição...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Unidade</th>
                                <th>Valor Unit.</th>
                                <th>Estoque</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody id="productsSearchTableBody">
                            <!-- Produtos carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Exclusão -->
    <div id="deleteModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h2><i class="fas fa-exclamation-triangle"></i> Confirmar Exclusão</h2>
                <span class="close" onclick="closeModal('deleteModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>Atenção!</strong> Esta ação não pode ser desfeita. A solicitação será marcada como excluída.
                </div>

                <div class="form-group">
                    <label>Motivo da Exclusão *</label>
                    <select class="form-control" id="deleteReason" required>
                        <option value="">Selecione o motivo...</option>
                        <option value="DUPLICADA">Solicitação Duplicada</option>
                        <option value="CANCELADA">Cancelada pelo Solicitante</option>
                        <option value="ERRO_CADASTRO">Erro no Cadastro</option>
                        <option value="NAO_APROVADA">Não Aprovada pela Diretoria</option>
                        <option value="SUBSTITUIDA">Substituída por Outra</option>
                        <option value="ORCAMENTO_INDISPONIVEL">Orçamento Indisponível</option>
                        <option value="OUTRO">Outro Motivo</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Observações</label>
                    <textarea class="form-control" id="deleteObservations" rows="3" placeholder="Descreva detalhes sobre a exclusão..."></textarea>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <button class="btn btn-secondary" onclick="closeModal('deleteModal')">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> Confirmar Exclusão
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script type="module">
        // ===================================================================
        // SOLICITAÇÃO COMPRAS MELHORADA - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import {
            collection,
            addDoc,
            getDocs,
            getDoc,
            setDoc,
            doc,
            updateDoc,
            deleteDoc,
            query,
            where,
            orderBy,
            limit,
            startAfter,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase já inicializado centralmente

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', id: 'sistema', uid: 'sistema' };
        
        // Garantir que currentUser tem todos os campos necessários
        if (currentUser && !currentUser.id && !currentUser.uid) {
            currentUser.id = 'sistema';
            currentUser.uid = 'sistema';
        }
        let solicitacoes = [];
        let produtos = [];
        let centrosCusto = [];
        let setores = [];
        let estoques = [];
        let currentPage = 1;
        let itemsPerPage = 20;
        let totalItems = 0;
        let currentItemRow = null; // Para controlar qual linha está sendo editada

        // Verificar autenticação
        if (!currentUser) {
            window.location.href = 'login.html';
        }

        // Funções de notificação
        function showNotification(message, type = 'success', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type}`;
            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                    notification.style.opacity = '1';
                }, 300);
            }, duration);
        }

        // Definir funções globais primeiro
        window.applyFilters = async function() {
            const filters = {
                dataInicio: document.getElementById('dataInicio').value,
                dataFim: document.getElementById('dataFim').value,
                status: document.getElementById('statusFilter').value,
                priority: document.getElementById('priorityFilter').value,
                department: document.getElementById('departmentFilter').value,
                requester: document.getElementById('requesterFilter').value.toLowerCase(),
                display: document.getElementById('displayFilter').value
            };

            // Primeiro, carregar todas as solicitações
            const solicitacoesSnap = await getDocs(
                query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))
            );
            let filteredRequests = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            // Aplicar filtro de exibição primeiro
            filteredRequests = filterByDisplay(filteredRequests, filters.display);

            if (filters.dataInicio) {
                const startDate = new Date(filters.dataInicio);
                filteredRequests = filteredRequests.filter(req => {
                    const reqDate = req.dataCriacao.toDate ? req.dataCriacao.toDate() : new Date(req.dataCriacao);
                    return reqDate >= startDate;
                });
            }

            if (filters.dataFim) {
                const endDate = new Date(filters.dataFim);
                filteredRequests = filteredRequests.filter(req => {
                    const reqDate = req.dataCriacao.toDate ? req.dataCriacao.toDate() : new Date(req.dataCriacao);
                    return reqDate <= endDate;
                });
            }

            if (filters.status) {
                filteredRequests = filteredRequests.filter(req => req.status === filters.status);
            }

            if (filters.priority) {
                filteredRequests = filteredRequests.filter(req => req.prioridade === filters.priority);
            }

            if (filters.department) {
                filteredRequests = filteredRequests.filter(req => req.departamento === filters.department);
            }

            if (filters.requester) {
                filteredRequests = filteredRequests.filter(req =>
                    (req.solicitante || currentUser.nome).toLowerCase().includes(filters.requester)
                );
            }

            // Aplicar ordenação baseada na seleção do usuário
            const sortOrder = document.getElementById('sortOrder')?.value || 'date_desc';
            filteredRequests = applySorting(filteredRequests, sortOrder);

            solicitacoes = filteredRequests;
            currentPage = 1;
            renderRequests();
            updatePagination();
            updateStats();
        };

        window.clearFilters = function() {
            document.getElementById('dataInicio').value = '';
            document.getElementById('dataFim').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('priorityFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('requesterFilter').value = '';
            document.getElementById('displayFilter').value = 'active';
            loadRequests();
        };

        // Outras funções globais necessárias
        window.toggleSelectAll = function() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('#requestsTableBody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        };

        window.openModal = function(modalId) {
            document.getElementById(modalId).style.display = 'block';
        };

        window.closeModal = function(modalId) {
            document.getElementById(modalId).style.display = 'none';
        };

        window.openNewRequestModal = function() {
            // Limpar formulário
            document.getElementById('newRequestForm').reset();
            document.getElementById('editingRequestId').value = '';
            document.getElementById('itemsContainer').innerHTML = `
                <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;" class="empty-state">
                    <i class="fas fa-box-open" style="font-size: 48px; color: #6c757d; margin-bottom: 15px;"></i>
                    <p style="color: #6c757d; margin: 0; font-size: 16px;">
                        Nenhum item adicionado
                    </p>
                    <p style="color: #6c757d; margin: 5px 0 0 0; font-size: 14px;">
                        Use o botão "Adicionar Item" para incluir produtos
                    </p>
                </div>
            `;

            // Definir título para nova solicitação
            document.querySelector('#newRequestModal .modal-header h2').innerHTML =
                '<i class="fas fa-plus"></i> Nova Solicitação de Compra';

            // Preencher solicitante
            document.getElementById('solicitante').value = currentUser.nome || 'Sistema';

            // Limpar array de itens
            requestItems = [];

            // Abrir modal
            openModal('newRequestModal');
        };

        window.showActiveRequests = function() {
            document.getElementById('displayFilter').value = 'active';
            loadRequests();
        };

        window.showQuotationRequests = function() {
            document.getElementById('displayFilter').value = 'quoted';
            loadRequests();
        };

        window.showFinalizedRequests = function() {
            document.getElementById('displayFilter').value = 'finalized';
            loadRequests();
        };

        window.showMyRequests = function() {
            document.getElementById('requesterFilter').value = currentUser.nome;
            applyFilters();
        };

        window.showUrgentRequests = function() {
            document.getElementById('priorityFilter').value = 'URGENTE';
            applyFilters();
        };

        // Carregar dados iniciais
        window.onload = async function() {
            await loadInitialData();
            await loadRequests();
            updateStats();

            // Adicionar listener para filtro de exibição
            const displayFilter = document.getElementById('displayFilter');
            if (displayFilter) {
                displayFilter.addEventListener('change', async function() {
                    await loadRequests();
                    updateStats();
                });
            }

            // Adicionar listener para ordenação
            const sortOrder = document.getElementById('sortOrder');
            if (sortOrder) {
                sortOrder.addEventListener('change', async function() {
                    await loadRequests();
                    updateStats();
                });
            }
        };

        async function loadInitialData() {
            try {
                // Carregar produtos
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar centros de custo (usando sua estrutura existente)
                const centrosCustoSnap = await getDocs(collection(db, "centrosCusto"));
                centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar setores (usando seu cadastro existente)
                const setoresSnap = await getDocs(collection(db, "setores"));
                setores = setoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar estoques
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', {
                    produtos: produtos.length,
                    centrosCusto: centrosCusto.length,
                    setores: setores.length,
                    estoques: estoques.length
                });

                // Carregar dados nos selects
                loadCostCenters();
                loadDepartments();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados iniciais', 'error');
            }
        }

        function loadCostCenters() {
            const select = document.getElementById('centroCusto');
            select.innerHTML = '<option value="">Selecione um centro de custo...</option>';

            // Filtrar apenas centros de custo ativos e ordenar por código
            centrosCusto
                .filter(cc => cc.status === 'Ativo')
                .sort((a, b) => a.codigo.localeCompare(b.codigo))
                .forEach(cc => {
                    const option = document.createElement('option');
                    option.value = cc.id;
                    option.textContent = `${cc.codigo} - ${cc.descricao}`;
                    select.appendChild(option);
                });

            console.log(`${centrosCusto.filter(cc => cc.status === 'Ativo').length} centros de custo ativos carregados`);
        }

        function loadDepartments() {
            const select = document.getElementById('departamento');
            const filterSelect = document.getElementById('departmentFilter');

            // Limpar selects
            select.innerHTML = '<option value="">Selecione...</option>';
            if (filterSelect) {
                filterSelect.innerHTML = '<option value="">Todos</option>';
            }

            // Se temos setores cadastrados, usar eles
            if (setores.length > 0) {
                setores
                    .filter(setor => setor.status === 'Ativo')
                    .sort((a, b) => a.nome.localeCompare(b.nome))
                    .forEach(setor => {
                        const option = document.createElement('option');
                        option.value = setor.id;
                        option.textContent = setor.nome;
                        select.appendChild(option);

                        if (filterSelect) {
                            const filterOption = document.createElement('option');
                            filterOption.value = setor.id;
                            filterOption.textContent = setor.nome;
                            filterSelect.appendChild(filterOption);
                        }
                    });

                console.log(`${setores.filter(s => s.status === 'Ativo').length} setores ativos carregados`);
            } else {
                // Fallback para departamentos fixos se não houver setores
                const departamentosFixos = [
                    'ENGENHARIA',
                    'PRODUCAO',
                    'MANUTENCAO',
                    'QUALIDADE',
                    'ADMINISTRATIVO'
                ];

                departamentosFixos.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept;
                    option.textContent = dept.charAt(0) + dept.slice(1).toLowerCase();
                    select.appendChild(option);

                    if (filterSelect) {
                        const filterOption = document.createElement('option');
                        filterOption.value = dept;
                        filterOption.textContent = dept.charAt(0) + dept.slice(1).toLowerCase();
                        filterSelect.appendChild(filterOption);
                    }
                });

                console.log('Usando departamentos fixos (setores não encontrados)');
            }
        }

        async function loadRequests() {
            try {
                const solicitacoesSnap = await getDocs(
                    query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))
                );
                const allSolicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Aplicar filtro de exibição
                const displayFilter = document.getElementById('displayFilter')?.value || 'active';
                let filteredSolicitacoes = filterByDisplay(allSolicitacoes, displayFilter);

                // Aplicar ordenação baseada na seleção do usuário
                const sortOrder = document.getElementById('sortOrder')?.value || 'date_desc';
                filteredSolicitacoes = applySorting(filteredSolicitacoes, sortOrder);

                solicitacoes = filteredSolicitacoes;

                renderRequests();
                updatePagination();
            } catch (error) {
                console.error('Erro ao carregar solicitações:', error);
                showNotification('Erro ao carregar solicitações', 'error');
            }
        }

        function filterByDisplay(requests, displayFilter) {
            switch (displayFilter) {
                case 'active':
                    // Apenas ativas (pendentes e aprovadas)
                    return requests.filter(r =>
                        r.status === 'PENDENTE' ||
                        r.status === 'APROVADA'
                    );
                case 'quoted':
                    // Apenas em cotação
                    return requests.filter(r =>
                        r.status === 'EM_COTACAO' ||
                        r.status === 'COTADO'
                    );
                case 'finalized':
                    // Apenas finalizadas (que viraram pedidos)
                    return requests.filter(r => r.status === 'FINALIZADA');
                case 'excluded':
                    // Apenas excluídas
                    return requests.filter(r => r.status === 'EXCLUIDA');
                case 'all':
                    // Todas exceto excluídas
                    return requests.filter(r => r.status !== 'EXCLUIDA');
                default:
                    // Por padrão, mostrar apenas ativas
                    return requests.filter(r =>
                        r.status === 'PENDENTE' ||
                        r.status === 'APROVADA'
                    );
            }
        }

        function renderRequests() {
            const tbody = document.getElementById('requestsTableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageRequests = solicitacoes.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            pageRequests.forEach(request => {
                const row = document.createElement('tr');

                const statusClass = getStatusClass(request.status);
                const priorityClass = getPriorityClass(request.prioridade);

                // Criar links de rastreabilidade
                const rastreabilidadeLinks = [];
                if (request.cotacaoId || request.cotacaoNumero) {
                    rastreabilidadeLinks.push(`
                        <a href="cotacoes_melhorada.html?cotacao=${request.cotacaoId}"
                           class="link-rastreabilidade" title="Ver Cotação">
                            📋 CT-${request.cotacaoNumero || 'N/A'}
                        </a>
                    `);
                }
                if (request.pedidoId || request.pedidoNumero) {
                    rastreabilidadeLinks.push(`
                        <a href="pedidos_compra.html?pedido=${request.pedidoId}"
                           class="link-rastreabilidade" title="Ver Pedido">
                            📦 PC-${request.pedidoNumero || 'N/A'}
                        </a>
                    `);
                }

                row.innerHTML = `
                    <td><input type="checkbox" value="${request.id}"></td>
                    <td>
                        <strong>${request.numero || 'N/A'}</strong>
                        ${rastreabilidadeLinks.length > 0 ? `
                            <br><small class="rastreabilidade-links">
                                ${rastreabilidadeLinks.join(' → ')}
                            </small>
                        ` : ''}
                    </td>
                    <td>${formatDate(request.dataCriacao)}</td>
                    <td>${getTipoText(request.tipo)}</td>
                    <td>${getOrigemText(request.origem)}</td>
                    <td>${request.solicitante || currentUser.nome}</td>
                    <td>${request.departamento || 'N/A'}</td>
                    <td>${request.itens ? request.itens.length : 0} itens</td>
                    <td>R$ ${formatCurrency(request.valorTotal || 0)}</td>
                    <td><span class="priority ${priorityClass}">${request.prioridade || 'NORMAL'}</span></td>
                    <td>
                        <span class="status ${statusClass}">${getStatusText(request.status)}</span>
                        ${request.status === 'EXCLUIDA' ? `
                            <br><small style="color: #6c757d;">
                                Motivo: ${request.motivoExclusao || 'N/A'}
                                <br>Excluída em: ${request.dataExclusao ? new Date(request.dataExclusao.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A'}
                            </small>
                        ` : ''}
                        ${request.status === 'FINALIZADA' ? `
                            <br><small style="color: #27ae60;">
                                ✅ Convertida em pedido de compra
                            </small>
                        ` : ''}
                    </td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-info btn-sm" onclick="viewRequest('${request.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editRequest('${request.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${request.status === 'PENDENTE' ? `
                                <button class="btn btn-success btn-sm" onclick="approveRequest('${request.id}')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="rejectRequest('${request.id}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                            ${request.status === 'APROVADA' ? `
                                <button class="btn btn-primary btn-sm" onclick="createQuotation('${request.id}')">
                                    <i class="fas fa-file-invoice"></i>
                                </button>
                            ` : ''}
                            ${request.status !== 'EXCLUIDA' ? `
                                <button class="btn btn-danger btn-sm" onclick="openDeleteModal('${request.id}')" title="Excluir">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        function updateStats() {
            const pendentes = solicitacoes.filter(s => s.status === 'PENDENTE').length;
            const aprovadas = solicitacoes.filter(s => s.status === 'APROVADA').length;
            const emCotacao = solicitacoes.filter(s => s.status === 'EM_COTACAO').length;
            const finalizadas = solicitacoes.filter(s => s.status === 'FINALIZADA').length;
            const rejeitadas = solicitacoes.filter(s => s.status === 'REJEITADA').length;
            const total = solicitacoes.length;

            document.getElementById('statPendentes').textContent = pendentes;
            document.getElementById('statAprovadas').textContent = aprovadas;
            document.getElementById('statEmCotacao').textContent = emCotacao;
            document.getElementById('statFinalizadas').textContent = finalizadas;
            document.getElementById('statRejeitadas').textContent = rejeitadas;
            document.getElementById('statTotal').textContent = total;
        }

        function updatePagination() {
            totalItems = solicitacoes.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const pagination = document.getElementById('pagination');

            pagination.innerHTML = '';

            // Botão anterior
            const prevBtn = document.createElement('button');
            prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => changePage(currentPage - 1);
            pagination.appendChild(prevBtn);

            // Números das páginas
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const pageBtn = document.createElement('button');
                    pageBtn.textContent = i;
                    pageBtn.className = i === currentPage ? 'active' : '';
                    pageBtn.onclick = () => changePage(i);
                    pagination.appendChild(pageBtn);
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    const dots = document.createElement('span');
                    dots.textContent = '...';
                    pagination.appendChild(dots);
                }
            }

            // Botão próximo
            const nextBtn = document.createElement('button');
            nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => changePage(currentPage + 1);
            pagination.appendChild(nextBtn);
        }

        function changePage(page) {
            currentPage = page;
            renderRequests();
            updatePagination();
        }

        // Funções auxiliares
        function getStatusClass(status) {
            switch (status) {
                case 'PENDENTE': return 'status-pendente';
                case 'APROVADA': return 'status-aprovada';
                case 'REJEITADA': return 'status-rejeitada';
                case 'EM_COTACAO': return 'status-cotacao';
                case 'COTADO': return 'status-cotacao';
                case 'FINALIZADA': return 'status-finalizada';
                case 'EXCLUIDA': return 'status-excluida';
                default: return 'status-pendente';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'PENDENTE': return 'Pendente';
                case 'APROVADA': return 'Aprovada';
                case 'REJEITADA': return 'Rejeitada';
                case 'EM_COTACAO': return 'Em Cotação';
                case 'COTADO': return 'Cotado';
                case 'FINALIZADA': return 'Finalizada';
                case 'EXCLUIDA': return 'Excluída';
                default: return 'Pendente';
            }
        }

        function getPriorityClass(priority) {
            switch (priority) {
                case 'ALTA': case 'CRITICA': return 'priority-alta';
                case 'MEDIA': case 'NORMAL': return 'priority-media';
                case 'BAIXA': return 'priority-baixa';
                default: return 'priority-media';
            }
        }

        function getTipoText(tipo) {
            switch (tipo) {
                case 'NORMAL': return '📋 Normal';
                case 'PLANEJADA': return '📅 Planejada';
                case 'EMERGENCIAL': return '🚨 Emergencial';
                default: return '📋 Normal';
            }
        }

        function getOrigemText(origem) {
            switch (origem) {
                case 'MANUAL': return '👤 Manual';
                case 'MRP': return '🤖 MRP';
                case 'ESTOQUE_MINIMO': return '📦 Estoque Mín.';
                case 'MANUTENCAO': return '🔧 Manutenção';
                default: return '👤 Manual';
            }
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleDateString('pt-BR');
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        // Função para coletar itens do formulário (usado na edição)
        function collectItemsFromForm() {
            const itemRows = document.querySelectorAll('#itemsContainer .item-row');
            const items = [];

            itemRows.forEach(row => {
                const item = {
                    produtoId: row.querySelector('input[name="produtoId"]').value,
                    produtoDescricao: row.querySelector('input[name="produtoDescricao"]').value,
                    quantidade: parseFloat(row.querySelector('input[name="quantidade"]').value) || 1,
                    unidade: row.querySelector('input[name="unidade"]').value,
                    valorUnitario: parseFloat(row.querySelector('input[name="valorUnitario"]').value) || 0,
                    observacoesItem: row.querySelector('textarea[name="observacoesItem"]').value
                };

                if (item.produtoId && item.quantidade > 0) {
                    items.push(item);
                }
            });

            return items;
        }

        // Função para adicionar item ao formulário (usado na edição)
        function addItemToForm(item, index) {
            const itemsContainer = document.getElementById('itemsContainer');

            // Buscar informações completas do produto se necessário
            let produtoDescricao = item.produtoDescricao || item.descricao || '';
            let unidade = item.unidade || '';
            let valorUnitario = item.valorUnitario || 0;

            // Se temos o produtoId, buscar dados completos do produto
            if (item.produtoId && produtos && produtos.length > 0) {
                const produto = produtos.find(p => p.id === item.produtoId);
                if (produto) {
                    produtoDescricao = `${produto.codigo || ''} - ${produto.descricao || ''}`.trim();
                    unidade = produto.unidade || unidade;
                    // Manter o valor unitário do item se existir, senão usar do produto
                    if (!valorUnitario && produto.valorUnitario) {
                        valorUnitario = produto.valorUnitario;
                    }
                }
            }

            const itemDiv = document.createElement('div');
            itemDiv.className = 'item-row';
            itemDiv.innerHTML = `
                <div class="item-header">
                    <h5>Item ${index + 1}</h5>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">
                        <i class="fas fa-trash"></i> Remover
                    </button>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Produto</label>
                        <input type="text" class="form-control" name="produtoDescricao" value="${produtoDescricao}" readonly>
                        <input type="hidden" name="produtoId" value="${item.produtoId || ''}">
                    </div>
                    <div class="form-group">
                        <label>Quantidade</label>
                        <input type="number" class="form-control" name="quantidade" value="${item.quantidade || 1}" min="1" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>Unidade</label>
                        <input type="text" class="form-control" name="unidade" value="${unidade}" readonly>
                    </div>
                    <div class="form-group">
                        <label>Valor Unit. (R$)</label>
                        <input type="number" class="form-control" name="valorUnitario" value="${valorUnitario}" step="0.01" readonly>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Observações do Item</label>
                        <textarea class="form-control" name="observacoesItem" rows="2">${item.observacoesItem || ''}</textarea>
                    </div>
                </div>
            `;

            itemsContainer.appendChild(itemDiv);
        }

        // Função para aplicar ordenação
        function applySorting(requests, sortOrder) {
            return requests.sort((a, b) => {
                switch (sortOrder) {
                    case 'date_desc':
                        // Data decrescente (mais recente primeiro) - PADRÃO
                        const dateA_desc = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const dateB_desc = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return dateB_desc - dateA_desc;

                    case 'date_asc':
                        // Data crescente (mais antiga primeiro)
                        const dateA_asc = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const dateB_asc = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return dateA_asc - dateB_asc;

                    case 'number_desc':
                        // Número decrescente
                        const numA_desc = parseInt(a.numero?.replace(/\D/g, '') || '0');
                        const numB_desc = parseInt(b.numero?.replace(/\D/g, '') || '0');
                        return numB_desc - numA_desc;

                    case 'number_asc':
                        // Número crescente
                        const numA_asc = parseInt(a.numero?.replace(/\D/g, '') || '0');
                        const numB_asc = parseInt(b.numero?.replace(/\D/g, '') || '0');
                        return numA_asc - numB_asc;

                    case 'priority_desc':
                        // Prioridade decrescente (CRITICA > ALTA > URGENTE > NORMAL > BAIXA)
                        const priorityOrder = { 'CRITICA': 5, 'ALTA': 4, 'URGENTE': 3, 'NORMAL': 2, 'BAIXA': 1 };
                        const prioA_desc = priorityOrder[a.prioridade] || 2;
                        const prioB_desc = priorityOrder[b.prioridade] || 2;
                        return prioB_desc - prioA_desc;

                    case 'priority_asc':
                        // Prioridade crescente (BAIXA > NORMAL > URGENTE > ALTA > CRITICA)
                        const priorityOrderAsc = { 'CRITICA': 5, 'ALTA': 4, 'URGENTE': 3, 'NORMAL': 2, 'BAIXA': 1 };
                        const prioA_asc = priorityOrderAsc[a.prioridade] || 2;
                        const prioB_asc = priorityOrderAsc[b.prioridade] || 2;
                        return prioA_asc - prioB_asc;

                    case 'value_desc':
                        // Valor decrescente (maior primeiro)
                        const valueA_desc = a.valorTotal || 0;
                        const valueB_desc = b.valorTotal || 0;
                        return valueB_desc - valueA_desc;

                    case 'value_asc':
                        // Valor crescente (menor primeiro)
                        const valueA_asc = a.valorTotal || 0;
                        const valueB_asc = b.valorTotal || 0;
                        return valueA_asc - valueB_asc;

                    default:
                        // Padrão: data decrescente
                        const dateA_default = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const dateB_default = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return dateB_default - dateA_default;
                }
            });
        }

        // Funções de modal - removida duplicata

        window.closeModal = function(modalId) {
            document.getElementById(modalId).style.display = 'none';
        };

        // Variável para armazenar o ID da solicitação a ser excluída
        let requestToDelete = null;

        window.openDeleteModal = function(requestId) {
            requestToDelete = requestId;

            // Limpar campos
            document.getElementById('deleteReason').value = '';
            document.getElementById('deleteObservations').value = '';

            document.getElementById('deleteModal').style.display = 'block';
        };

        window.confirmDelete = async function() {
            const reason = document.getElementById('deleteReason').value;
            const observations = document.getElementById('deleteObservations').value;

            if (!reason) {
                showNotification('Selecione o motivo da exclusão', 'warning');
                return;
            }

            if (!requestToDelete) {
                showNotification('Erro: Solicitação não identificada', 'error');
                return;
            }

            try {
                // Preparar dados para atualização
                const updateData = {
                    status: 'EXCLUIDA',
                    motivoExclusao: reason,
                    observacoesExclusao: observations,
                    dataExclusao: new Date(),
                    usuarioExclusao: currentUser.nome,
                    uidExclusao: currentUser.id || currentUser.uid || 'sistema'
                };

                // Remover campos undefined
                Object.keys(updateData).forEach(key => {
                    if (updateData[key] === undefined) {
                        delete updateData[key];
                    }
                });

                // Atualizar status da solicitação para EXCLUIDA
                await updateDoc(doc(db, "solicitacoesCompra", requestToDelete), updateData);

                showNotification('Solicitação excluída com sucesso', 'success');
                closeModal('deleteModal');

                // Recarregar lista
                await loadRequests();
                updateStats();

                requestToDelete = null;

            } catch (error) {
                console.error('Erro ao excluir solicitação:', error);
                showNotification('Erro ao excluir solicitação: ' + error.message, 'error');
            }
        };

        // Funções de ação
        window.showAllRequests = function() {
            currentPage = 1;
            renderRequests();
            updatePagination();
        };

        window.showPendingRequests = function() {
            document.getElementById('statusFilter').value = 'PENDENTE';
            applyFilters();
        };

        window.showApprovedRequests = function() {
            document.getElementById('statusFilter').value = 'APROVADA';
            applyFilters();
        };

        window.showQuotationRequests = function() {
            document.getElementById('displayFilter').value = 'quoted';
            loadRequests();
        };

        window.showFinalizedRequests = function() {
            document.getElementById('displayFilter').value = 'finalized';
            loadRequests();
        };

        window.showMyRequests = function() {
            document.getElementById('requesterFilter').value = currentUser.nome;
            applyFilters();
        };

        // Função para criar cotação a partir de solicitação
        window.createQuotation = async function(requestId) {
            try {
                const request = solicitacoes.find(r => r.id === requestId);
                if (!request) {
                    showNotification('Solicitação não encontrada', 'error');
                    return;
                }

                if (request.status !== 'APROVADA') {
                    showNotification('Apenas solicitações aprovadas podem gerar cotações', 'warning');
                    return;
                }

                if (!request.itens || request.itens.length === 0) {
                    showNotification('Solicitação não possui itens para cotação', 'warning');
                    return;
                }

                // Gerar número da cotação
                const counterRef = doc(db, "contadores", "cotacoes");
                const counterDoc = await getDoc(counterRef);
                let nextNumber = 1;

                if (counterDoc.exists()) {
                    nextNumber = counterDoc.data().valor + 1;
                    await updateDoc(counterRef, { valor: nextNumber });
                } else {
                    await setDoc(counterRef, { valor: nextNumber });
                }

                const numeroCotacao = String(nextNumber).padStart(6, '0');

                // Criar dados da cotação
                const cotacaoData = {
                    numero: numeroCotacao,
                    solicitacaoId: requestId,
                    centroCustoId: request.centroCustoId,
                    itens: request.itens.map(item => ({
                        produtoId: item.produtoId,
                        codigo: item.codigo,
                        descricao: item.descricao,
                        quantidade: item.quantidade,
                        unidade: item.unidade,
                        valorUnitario: item.valorUnitario || 0
                    })),
                    status: 'ABERTA',
                    dataCriacao: new Date(),
                    criadoPor: currentUser.nome,
                    fornecedores: [] // Será preenchido posteriormente
                };

                // Validar e limpar dados da cotação
                Object.keys(cotacaoData).forEach(key => {
                    if (cotacaoData[key] === undefined) {
                        delete cotacaoData[key];
                    }
                });

                // Salvar cotação
                const cotacaoRef = await addDoc(collection(db, 'cotacoes'), cotacaoData);

                // Preparar dados para atualização da solicitação
                const updateSolicitacaoData = {
                    status: 'EM_COTACAO',
                    cotacaoId: cotacaoRef.id,
                    cotacaoNumero: numeroCotacao,
                    geradoPor: currentUser.nome,
                    dataGeracao: new Date(),
                    ultimaAtualizacao: new Date()
                };

                // Remover campos undefined
                Object.keys(updateSolicitacaoData).forEach(key => {
                    if (updateSolicitacaoData[key] === undefined) {
                        delete updateSolicitacaoData[key];
                    }
                });

                // Atualizar status da solicitação com link reverso
                await updateDoc(doc(db, 'solicitacoesCompra', requestId), updateSolicitacaoData);

                showNotification(`Cotação ${numeroCotacao} gerada com sucesso!`, 'success');

                // Recarregar dados
                await loadRequests();
                updateStats();

            } catch (error) {
                console.error('Erro ao gerar cotação:', error);
                showNotification('Erro ao gerar cotação: ' + error.message, 'error');
            }
        };

        window.showUrgentRequests = function() {
            document.getElementById('priorityFilter').value = 'ALTA';
            applyFilters();
        };

        window.applyFilters = async function() {
            const filters = {
                dataInicio: document.getElementById('dataInicio').value,
                dataFim: document.getElementById('dataFim').value,
                status: document.getElementById('statusFilter').value,
                priority: document.getElementById('priorityFilter').value,
                department: document.getElementById('departmentFilter').value,
                requester: document.getElementById('requesterFilter').value.toLowerCase(),
                display: document.getElementById('displayFilter').value
            };

            // Primeiro, carregar todas as solicitações
            const solicitacoesSnap = await getDocs(
                query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))
            );
            let filteredRequests = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            // Aplicar filtro de exibição primeiro
            filteredRequests = filterByDisplay(filteredRequests, filters.display);

            if (filters.dataInicio) {
                const startDate = new Date(filters.dataInicio);
                filteredRequests = filteredRequests.filter(req => {
                    const reqDate = req.dataCriacao.toDate ? req.dataCriacao.toDate() : new Date(req.dataCriacao);
                    return reqDate >= startDate;
                });
            }

            if (filters.dataFim) {
                const endDate = new Date(filters.dataFim);
                filteredRequests = filteredRequests.filter(req => {
                    const reqDate = req.dataCriacao.toDate ? req.dataCriacao.toDate() : new Date(req.dataCriacao);
                    return reqDate <= endDate;
                });
            }

            if (filters.status) {
                filteredRequests = filteredRequests.filter(req => req.status === filters.status);
            }

            if (filters.priority) {
                filteredRequests = filteredRequests.filter(req => req.prioridade === filters.priority);
            }

            if (filters.department) {
                filteredRequests = filteredRequests.filter(req => req.departamento === filters.department);
            }

            if (filters.requester) {
                filteredRequests = filteredRequests.filter(req =>
                    (req.solicitante || currentUser.nome).toLowerCase().includes(filters.requester)
                );
            }

            // Aplicar ordenação baseada na seleção do usuário
            const sortOrder = document.getElementById('sortOrder')?.value || 'date_desc';
            filteredRequests = applySorting(filteredRequests, sortOrder);

            solicitacoes = filteredRequests;
            currentPage = 1;
            renderRequests();
            updatePagination();
            updateStats();
        };

        window.clearFilters = function() {
            document.getElementById('dataInicio').value = '';
            document.getElementById('dataFim').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('priorityFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('requesterFilter').value = '';
            document.getElementById('displayFilter').value = 'active';
            loadRequests();
        };

        // Função para editar solicitação
        window.editRequest = async function(id) {
            const request = solicitacoes.find(r => r.id === id);
            if (!request) {
                showNotification('Solicitação não encontrada', 'error');
                return;
            }

            // Verificar se pode editar
            if (request.status !== 'PENDENTE' && request.status !== 'APROVADA') {
                showNotification('Apenas solicitações pendentes ou aprovadas podem ser editadas', 'warning');
                return;
            }

            // Garantir que os produtos estejam carregados
            if (!produtos || produtos.length === 0) {
                try {
                    const produtosSnap = await getDocs(collection(db, "produtos"));
                    produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                } catch (error) {
                    console.error('Erro ao carregar produtos:', error);
                    showNotification('Erro ao carregar dados dos produtos', 'error');
                    return;
                }
            }

            // Preencher formulário com dados da solicitação
            document.getElementById('tipo').value = request.tipo || 'NORMAL';
            document.getElementById('origem').value = request.origem || 'MANUAL';
            document.getElementById('departamento').value = request.departamento || '';
            document.getElementById('prioridade').value = request.prioridade || 'NORMAL';
            document.getElementById('centroCusto').value = request.centroCustoId || '';
            document.getElementById('dataNecessidade').value = request.dataNecessidade ?
                (request.dataNecessidade.toDate ? request.dataNecessidade.toDate().toISOString().split('T')[0] : '') : '';
            document.getElementById('dataLimiteAprovacao').value = request.dataLimiteAprovacao ?
                (request.dataLimiteAprovacao.toDate ? request.dataLimiteAprovacao.toDate().toISOString().split('T')[0] : '') : '';
            document.getElementById('justificativa').value = request.justificativa || '';
            document.getElementById('observacoes').value = request.observacoes || '';

            // Carregar itens
            const itemsContainer = document.getElementById('itemsContainer');
            itemsContainer.innerHTML = '';

            if (request.itens && request.itens.length > 0) {
                request.itens.forEach((item, index) => {
                    addItemToForm(item, index);
                });
            } else {
                itemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;" class="empty-state">
                        <i class="fas fa-box-open" style="font-size: 48px; color: #6c757d; margin-bottom: 15px;"></i>
                        <p style="color: #6c757d; margin: 0; font-size: 16px;">Nenhum item adicionado</p>
                        <p style="color: #6c757d; margin: 5px 0 0 0; font-size: 14px;">Use o botão "Adicionar Item" para incluir produtos</p>
                    </div>
                `;
            }

            // Marcar como edição
            document.getElementById('editingRequestId').value = id;
            document.querySelector('#newRequestModal .modal-header h2').innerHTML =
                '<i class="fas fa-edit"></i> Editar Solicitação de Compra';

            // Abrir modal
            openModal('newRequestModal');
        };

        // Função para aprovar solicitação
        window.approveRequest = async function(id) {
            if (!confirm('Tem certeza que deseja aprovar esta solicitação?')) {
                return;
            }

            try {
                await updateDoc(doc(db, "solicitacoesCompra", id), {
                    status: 'APROVADA',
                    dataAprovacao: Timestamp.now(),
                    aprovadoPor: currentUser.nome || 'Sistema'
                });

                showNotification('Solicitação aprovada com sucesso!', 'success');
                await loadRequests();
                updateStats();
            } catch (error) {
                console.error('Erro ao aprovar solicitação:', error);
                showNotification('Erro ao aprovar solicitação: ' + error.message, 'error');
            }
        };

        // Função para rejeitar solicitação
        window.rejectRequest = async function(id) {
            const motivo = prompt('Informe o motivo da rejeição:');
            if (!motivo) {
                return;
            }

            try {
                await updateDoc(doc(db, "solicitacoesCompra", id), {
                    status: 'REJEITADA',
                    dataRejeicao: Timestamp.now(),
                    rejeitadoPor: currentUser.nome || 'Sistema',
                    motivoRejeicao: motivo
                });

                showNotification('Solicitação rejeitada com sucesso!', 'success');
                await loadRequests();
                updateStats();
            } catch (error) {
                console.error('Erro ao rejeitar solicitação:', error);
                showNotification('Erro ao rejeitar solicitação: ' + error.message, 'error');
            }
        };

        window.viewRequest = function(id) {
            const request = solicitacoes.find(r => r.id === id);
            if (!request) return;

            const modalBody = document.getElementById('detailsModalBody');
            modalBody.innerHTML = `
                <div class="form-row">
                    <div class="form-group">
                        <label>Número:</label>
                        <div>${request.numero || 'N/A'}</div>
                    </div>
                    <div class="form-group">
                        <label>Data:</label>
                        <div>${formatDate(request.dataCriacao)}</div>
                    </div>
                    <div class="form-group">
                        <label>Solicitante:</label>
                        <div>${request.solicitante || currentUser.nome}</div>
                    </div>
                    <div class="form-group">
                        <label>Departamento:</label>
                        <div>${request.departamento || 'N/A'}</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Prioridade:</label>
                        <div><span class="priority ${getPriorityClass(request.prioridade)}">${request.prioridade || 'MEDIA'}</span></div>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <div><span class="status ${getStatusClass(request.status)}">${getStatusText(request.status)}</span></div>
                    </div>
                    <div class="form-group">
                        <label>Valor Total:</label>
                        <div>R$ ${formatCurrency(request.valorTotal || 0)}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Justificativa:</label>
                    <div>${request.justificativa || 'N/A'}</div>
                </div>
                ${request.observacoes ? `
                    <div class="form-group">
                        <label>Observações:</label>
                        <div>${request.observacoes}</div>
                    </div>
                ` : ''}
                <h4>Itens da Solicitação</h4>
                <div class="table-container">
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Quantidade</th>
                                <th>Unidade</th>
                                <th>Valor Unit.</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${request.itens ? request.itens.map(item => `
                                <tr>
                                    <td>${item.codigo || 'N/A'}</td>
                                    <td>${item.descricao || 'N/A'}</td>
                                    <td>${item.quantidade || 0}</td>
                                    <td>${item.unidade || 'UN'}</td>
                                    <td>R$ ${formatCurrency(item.valorUnitario || 0)}</td>
                                    <td>R$ ${formatCurrency((item.quantidade || 0) * (item.valorUnitario || 0))}</td>
                                </tr>
                            `).join('') : '<tr><td colspan="6">Nenhum item encontrado</td></tr>'}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('detailsModal').style.display = 'block';
        };

        // Array para armazenar itens da solicitação
        let requestItems = [];

        window.addItem = function() {
            // Abrir modal de busca de produtos
            loadProductsForSearch();
            document.getElementById('productSearchModal').style.display = 'block';
        };

        function loadProductsForSearch() {
            const tbody = document.getElementById('productsSearchTableBody');
            tbody.innerHTML = '';

            if (produtos.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #6c757d;">Nenhum produto encontrado</td></tr>';
                return;
            }

            produtos.forEach(produto => {
                const estoque = estoques.find(e => e.produtoId === produto.id);
                const saldoEstoque = estoque ? estoque.saldo : 0;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${produto.codigo || 'N/A'}</strong></td>
                    <td>${produto.descricao || 'N/A'}</td>
                    <td>${produto.unidade || 'UN'}</td>
                    <td>R$ ${formatCurrency(produto.valorUnitario || 0)}</td>
                    <td>${saldoEstoque}</td>
                    <td>
                        <button class="btn btn-success btn-sm" onclick="selectProduct('${produto.id}')">
                            <i class="fas fa-plus"></i> Adicionar
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        window.selectProduct = function(produtoId) {
            const produto = produtos.find(p => p.id === produtoId);
            if (!produto) return;

            // Verificar se produto já foi adicionado
            const existingItem = requestItems.find(item => item.produtoId === produtoId);
            if (existingItem) {
                showNotification('Produto já adicionado à solicitação!', 'warning');
                return;
            }

            // Adicionar produto à lista de itens
            const newItem = {
                produtoId: produto.id,
                codigo: produto.codigo,
                descricao: produto.descricao,
                unidade: produto.unidade,
                quantidade: 1,
                valorUnitario: produto.valorUnitario || 0,
                observacoes: ''
            };

            requestItems.push(newItem);
            renderRequestItems();
            closeModal('productSearchModal');
            showNotification('Produto adicionado com sucesso!', 'success');
        };

        function renderRequestItems() {
            const container = document.getElementById('itemsContainer');

            if (requestItems.length === 0) {
                container.innerHTML =
                    '<p style="color: #6c757d; text-align: center; padding: 20px;">Nenhum item adicionado. Use o botão "Adicionar Item" para incluir produtos.</p>';
                return;
            }

            let html = '<div class="table-container"><table class="table"><thead><tr>';
            html += '<th>Código</th><th>Descrição</th><th>Quantidade</th><th>Unidade</th><th>Valor Unit.</th><th>Total</th><th>Observações</th><th>Ações</th>';
            html += '</tr></thead><tbody>';

            let valorTotal = 0;

            requestItems.forEach((item, index) => {
                const total = (item.quantidade || 0) * (item.valorUnitario || 0);
                valorTotal += total;

                html += `<tr>
                    <td><strong>${item.codigo}</strong></td>
                    <td>${item.descricao}</td>
                    <td>
                        <input type="number" class="form-control" value="${item.quantidade}" min="1" step="0.01"
                               onchange="updateItemQuantity(${index}, this.value)" style="width: 100px;">
                    </td>
                    <td>${item.unidade}</td>
                    <td>R$ ${formatCurrency(item.valorUnitario)}</td>
                    <td><strong>R$ ${formatCurrency(total)}</strong></td>
                    <td>
                        <input type="text" class="form-control" value="${item.observacoes || ''}"
                               onchange="updateItemObservations(${index}, this.value)" placeholder="Observações...">
                    </td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="removeItem(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table></div>';

            html += `<div style="text-align: right; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">`;
            html += `<strong style="font-size: 18px;">Total Estimado: R$ ${formatCurrency(valorTotal)}</strong>`;
            html += `</div>`;

            container.innerHTML = html;
        }

        window.updateItemQuantity = function(index, quantity) {
            if (requestItems[index]) {
                requestItems[index].quantidade = parseFloat(quantity) || 1;
                renderRequestItems();
            }
        };

        window.updateItemObservations = function(index, observations) {
            if (requestItems[index]) {
                requestItems[index].observacoes = observations;
            }
        };

        window.removeItem = function(index) {
            if (confirm('Tem certeza que deseja remover este item?')) {
                requestItems.splice(index, 1);
                renderRequestItems();
                showNotification('Item removido com sucesso!', 'success');
            }
        };

        // Função de busca de produtos
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('productSearchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filterProducts(searchTerm);
                });
            }
        });

        function filterProducts(searchTerm) {
            const tbody = document.getElementById('productsSearchTableBody');
            const rows = tbody.querySelectorAll('tr');

            rows.forEach(row => {
                const codigo = row.cells[0]?.textContent.toLowerCase() || '';
                const descricao = row.cells[1]?.textContent.toLowerCase() || '';

                if (codigo.includes(searchTerm) || descricao.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Handler do formulário de nova solicitação
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('newRequestForm');
            if (form) {
                form.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    // Verificar se é edição
                    const editingId = document.getElementById('editingRequestId').value;
                    const isEditing = editingId !== '';

                    // Validar se há itens (para edição, usar itens do formulário)
                    let itemsToValidate = requestItems;
                    if (isEditing) {
                        // Coletar itens do formulário para edição
                        itemsToValidate = collectItemsFromForm();
                    }

                    if (itemsToValidate.length === 0) {
                        showNotification('Adicione pelo menos um item à solicitação!', 'warning');
                        return;
                    }

                    try {
                        // Capturar dados do formulário
                        const formData = {
                            solicitante: currentUser.nome,
                            solicitanteId: currentUser.id || currentUser.uid || 'sistema',
                            tipo: document.getElementById('tipo').value,
                            origem: document.getElementById('origem').value,
                            departamento: document.getElementById('departamento').value,
                            prioridade: document.getElementById('prioridade').value,
                            dataNecessidade: document.getElementById('dataNecessidade').value ?
                                Timestamp.fromDate(new Date(document.getElementById('dataNecessidade').value)) : null,
                            dataLimiteAprovacao: document.getElementById('dataLimiteAprovacao').value ?
                                Timestamp.fromDate(new Date(document.getElementById('dataLimiteAprovacao').value)) : null,
                            centroCustoId: document.getElementById('centroCusto').value,
                            justificativa: document.getElementById('justificativa').value,
                            observacoes: document.getElementById('observacoes').value,
                            itens: isEditing ? itemsToValidate : requestItems,
                            ultimaAtualizacao: Timestamp.now()
                        };

                        // Adicionar campos específicos para criação
                        if (!isEditing) {
                            formData.status = 'PENDENTE';
                            formData.dataCriacao = Timestamp.now();
                        }

                        // Buscar dados do centro de custo (usando sua estrutura existente)
                        const centroCusto = centrosCusto.find(cc => cc.id === formData.centroCustoId);
                        if (centroCusto) {
                            formData.centroCusto = {
                                codigo: centroCusto.codigo,
                                descricao: centroCusto.descricao,
                                departamento: centroCusto.departamento
                            };
                        }

                        // Buscar dados do setor/departamento
                        const setor = setores.find(s => s.id === formData.departamento);
                        if (setor) {
                            formData.departamentoNome = setor.nome;
                            formData.departamentoId = setor.id;
                        } else {
                            // Fallback para departamentos fixos
                            formData.departamentoNome = formData.departamento;
                        }

                        // Gerar número da solicitação apenas para criação
                        if (!isEditing) {
                            formData.numero = await generateRequestNumber();
                        }

                        // Validar campos obrigatórios para evitar undefined
                        if (!formData.solicitanteId) {
                            throw new Error('ID do solicitante é obrigatório');
                        }
                        if (!formData.solicitante) {
                            throw new Error('Nome do solicitante é obrigatório');
                        }

                        // Remover campos undefined
                        Object.keys(formData).forEach(key => {
                            if (formData[key] === undefined) {
                                delete formData[key];
                            }
                        });

                        console.log('Salvando solicitação:', formData);

                        // Salvar no Firebase
                        if (isEditing) {
                            await updateDoc(doc(db, "solicitacoesCompra", editingId), formData);
                            showNotification('Solicitação atualizada com sucesso!', 'success');
                        } else {
                            const docRef = await addDoc(collection(db, "solicitacoesCompra"), formData);
                            showNotification('Solicitação criada com sucesso!', 'success');
                        }

                        // Fechar modal e limpar dados
                        closeModal('newRequestModal');
                        requestItems = [];
                        form.reset();
                        document.getElementById('editingRequestId').value = '';
                        document.getElementById('itemsContainer').innerHTML = '<p style="color: #6c757d; text-align: center; padding: 20px;">Nenhum item adicionado. Use o botão "Adicionar Item" para incluir produtos.</p>';
                        document.querySelector('#newRequestModal .modal-header h2').innerHTML = '<i class="fas fa-plus"></i> Nova Solicitação de Compra';

                        // Recarregar lista
                        await loadRequests();
                        updateStats();

                    } catch (error) {
                        console.error('Erro ao salvar solicitação:', error);
                        showNotification('Erro ao salvar solicitação: ' + error.message, 'error');
                    }
                });
            }
        });

        async function generateRequestNumber() {
            try {
                const hoje = new Date();
                const ano = hoje.getFullYear().toString().slice(-2); // Últimos 2 dígitos do ano
                const mes = (hoje.getMonth() + 1).toString().padStart(2, '0'); // Mês com 2 dígitos
                const anoMes = ano + mes; // Formato AAMM

                // Usar a coleção contadores para gerar numeração sequencial
                const counterRef = doc(db, "contadores", "solicitacoesCompra");
                const counterDoc = await getDoc(counterRef);

                let nextNumber = 1;

                if (counterDoc.exists()) {
                    nextNumber = counterDoc.data().valor + 1;
                } else {
                    // Criar contador se não existir
                    await setDoc(counterRef, {
                        valor: 1,
                        ultimaAtualizacao: Timestamp.now(),
                        descricao: "Contador para numeração de solicitações de compra"
                    });
                }

                // Atualizar contador
                await updateDoc(counterRef, {
                    valor: nextNumber,
                    ultimaAtualizacao: Timestamp.now()
                });

                // Retornar número no formato SC-AAMM-XXXX
                return `SC-${anoMes}-${nextNumber.toString().padStart(4, '0')}`;

            } catch (error) {
                console.error('Erro ao gerar número da solicitação:', error);
                // Fallback para método anterior em caso de erro
                const hoje = new Date();
                const ano = hoje.getFullYear().toString().slice(-2);
                const mes = (hoje.getMonth() + 1).toString().padStart(2, '0');
                const anoMes = ano + mes;
                const timestamp = Date.now().toString().slice(-4);
                return `SC-${anoMes}-${timestamp}`;
            }
        }

        window.exportReport = function() {
            showNotification('Funcionalidade de exportação será implementada', 'info');
        };

        window.toggleSelectAll = function() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('#requestsTableBody input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        };

        // Pesquisa em tempo real
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();

            if (searchTerm === '') {
                loadRequests();
                return;
            }

            const filtered = solicitacoes.filter(request =>
                (request.numero || '').toLowerCase().includes(searchTerm) ||
                (request.solicitante || currentUser.nome).toLowerCase().includes(searchTerm) ||
                (request.departamento || '').toLowerCase().includes(searchTerm) ||
                (request.justificativa || '').toLowerCase().includes(searchTerm)
            );

            solicitacoes = filtered;
            currentPage = 1;
            renderRequests();
            updatePagination();
        });

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        };
    </script>
</body>
</html>
