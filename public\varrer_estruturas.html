<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação de Estruturas</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --error-color: #e74c3c;
            --info-color: #3498db;
            --light-bg: #ecf0f1;
            --white: #ffffff;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #bdc3c7;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 30px;
        }

        .summary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid rgba(255,255,255,0.8);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .summary-item {
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .summary-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .summary-item:hover::before {
            left: 100%;
        }

        .summary-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: var(--error-color);
            border-color: var(--error-color);
        }

        .warning {
            background: linear-gradient(135deg, #fff8e1, #ffecb3);
            color: #e65100;
            border-color: var(--warning-color);
        }

        .success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: var(--success-color);
            border-color: var(--success-color);
        }

        .info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: var(--info-color);
            border-color: var(--info-color);
        }
        .filter-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid rgba(255,255,255,0.8);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: 1fr 200px;
            gap: 20px;
            align-items: end;
        }

        .filter-inputs {
            display: grid;
            grid-template-columns: 1fr 200px;
            gap: 15px;
        }

        .filter-section input,
        .filter-section select {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: var(--transition);
            background: var(--white);
            width: 100%;
        }

        .filter-section input:focus,
        .filter-section select:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }

        .filter-section input::placeholder {
            color: var(--text-light);
        }

        .btn-group {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-hover);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: var(--white);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: var(--white);
        }

        .table-container {
            background: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-top: 30px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, var(--primary-color), #34495e);
            color: var(--white);
            padding: 20px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
        }

        td {
            padding: 16px;
            border-bottom: 1px solid #ecf0f1;
            transition: var(--transition);
            vertical-align: top;
        }

        tr:hover {
            background: linear-gradient(135deg, #f8f9fa, #ecf0f1);
            transform: scale(1.01);
        }

        tr:nth-child(even) {
            background: rgba(52, 152, 219, 0.02);
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .status-badge:hover::before {
            left: 100%;
        }

        .status-error {
            background: linear-gradient(135deg, var(--error-color), #c0392b);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .status-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .status-success {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .status-info {
            background: linear-gradient(135deg, var(--info-color), #2980b9);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .problem-item {
            margin: 8px 0;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9rem;
            border-left: 4px solid;
        }

        .problem-item.error {
            background: rgba(231, 76, 60, 0.1);
            border-left-color: var(--error-color);
            color: var(--error-color);
        }

        .problem-item.warning {
            background: rgba(243, 156, 18, 0.1);
            border-left-color: var(--warning-color);
            color: #e67e22;
        }

        .problem-item.info {
            background: rgba(52, 152, 219, 0.1);
            border-left-color: var(--info-color);
            color: var(--info-color);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .content {
                padding: 20px;
            }

            .filter-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .filter-inputs {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }

            .summary-grid {
                grid-template-columns: 1fr;
            }

            table {
                font-size: 0.9rem;
            }

            th, td {
                padding: 12px 8px;
            }
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
            font-size: 1.2rem;
            color: var(--text-light);
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--secondary-color);
            border-radius: 50%;
            margin-left: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        .fade-in {
            animation: slideUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Verificação de Estruturas</h1>
            <p class="subtitle">Análise completa das estruturas de produtos PA e SP</p>
        </div>

        <div class="content">
            <div class="filter-section">
                <div class="filter-grid">
                    <div class="filter-inputs">
                        <input type="text" id="searchInput" placeholder="🔍 Buscar por código ou descrição...">
                        <select id="tipoFilter">
                            <option value="">📦 Todos os tipos</option>
                            <option value="PA">🏭 Produto Acabado</option>
                            <option value="SP">⚙️ Semi-Produto</option>
                            <option value="MP">🧱 Matéria Prima</option>
                        </select>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="verificarEstruturas()">
                            🔍 Verificar
                        </button>
                        <button class="btn btn-secondary" onclick="exportarRelatorio()">
                            📊 Exportar
                        </button>
                    </div>
                </div>
            </div>

            <div class="summary" id="summarySection">
                <div class="loading">Carregando dados...</div>
            </div>

            <div class="table-container">
                <table id="resultsTable">
                    <thead>
                        <tr>
                            <th>📋 Código</th>
                            <th>📝 Descrição</th>
                            <th>🏷️ Tipo</th>
                            <th>📊 Status</th>
                            <th>⚠️ Problemas Encontrados</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                        <!-- Resultados serão preenchidos aqui -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let estruturas = [];
        let problemas = [];

        window.onload = async function() {
            await loadData();
        };

        async function loadData() {
            try {
                const [produtosSnap, estruturasSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturas"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log("Dados carregados:", {
                    produtos: produtos.length,
                    estruturas: estruturas.length
                });
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados. Por favor, recarregue a página.");
            }
        }

        window.verificarEstruturas = function() {
            // Mostrar loading
            const summarySection = document.getElementById('summarySection');
            const resultsBody = document.getElementById('resultsBody');

            summarySection.innerHTML = '<div class="loading">🔍 Analisando estruturas...</div>';
            resultsBody.innerHTML = '<tr><td colspan="5" class="loading">Processando dados...</td></tr>';

            // Simular delay para mostrar loading
            setTimeout(() => {
                problemas = [];
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const tipoFilter = document.getElementById('tipoFilter').value;

            // Verificar produtos PA e SP
            produtos
                .filter(p => (p.tipo === 'PA' || p.tipo === 'SP') && 
                            (!tipoFilter || p.tipo === tipoFilter) &&
                            (!searchTerm || 
                             p.codigo.toLowerCase().includes(searchTerm) || 
                             p.descricao.toLowerCase().includes(searchTerm)))
                .forEach(produto => {
                    const estrutura = estruturas.find(e => e.produtoPaiId === produto.id);
                    const problemasProduto = [];

                    // Verificar se tem estrutura
                    if (!estrutura) {
                        problemasProduto.push({
                            tipo: 'error',
                            mensagem: 'Produto não possui estrutura cadastrada'
                        });
                    } else {
                        // Verificar componentes da estrutura
                        if (!estrutura.componentes || estrutura.componentes.length === 0) {
                            problemasProduto.push({
                                tipo: 'error',
                                mensagem: 'Estrutura não possui componentes'
                            });
                        } else {
                            estrutura.componentes.forEach(componente => {
                                const produtoComponente = produtos.find(p => p.id === componente.componentId);
                                if (!produtoComponente) {
                                    problemasProduto.push({
                                        tipo: 'error',
                                        mensagem: `Componente ${componente.componentId} não encontrado no cadastro de produtos`
                                    });
                                } else if (produtoComponente.tipo === 'PA' || produtoComponente.tipo === 'SP') {
                                    // Verificar se o componente PA/SP tem sua própria estrutura
                                    const subEstrutura = estruturas.find(e => e.produtoPaiId === componente.componentId);
                                    if (!subEstrutura) {
                                        problemasProduto.push({
                                            tipo: 'warning',
                                            mensagem: `Componente ${produtoComponente.codigo} (${produtoComponente.descricao}) não possui estrutura cadastrada`
                                        });
                                    }
                                }
                            });
                        }
                    }

                    // Verificar onde o produto é utilizado como componente
                    const estruturasQueUsamProduto = estruturas.filter(e => 
                        e.componentes && e.componentes.some(c => c.componentId === produto.id)
                    );

                    if (estruturasQueUsamProduto.length > 0) {
                        const produtosPai = estruturasQueUsamProduto.map(e => {
                            const produtoPai = produtos.find(p => p.id === e.produtoPaiId);
                            return produtoPai ? `${produtoPai.codigo} (${produtoPai.descricao})` : 'Produto não encontrado';
                        });

                        problemasProduto.push({
                            tipo: 'info',
                            mensagem: `Este produto é utilizado como componente em: ${produtosPai.join(', ')}`
                        });
                    }

                    if (problemasProduto.length > 0) {
                        problemas.push({
                            produto: produto,
                            problemas: problemasProduto
                        });
                    }
                });

                exibirResultados();
            }, 500); // Delay para mostrar loading
        };

        function exibirResultados() {
            const summarySection = document.getElementById('summarySection');
            const resultsBody = document.getElementById('resultsBody');

            // Limpar resultados anteriores
            summarySection.innerHTML = '';
            resultsBody.innerHTML = '';

            // Criar resumo
            const totalProdutos = problemas.length;
            const erros = problemas.filter(p => p.problemas.some(prob => prob.tipo === 'error')).length;
            const warnings = problemas.filter(p => p.problemas.some(prob => prob.tipo === 'warning')).length;
            const infos = problemas.filter(p => p.problemas.some(prob => prob.tipo === 'info')).length;

            summarySection.innerHTML = `
                <h3 style="margin-bottom: 20px; color: var(--primary-color); font-size: 1.4rem;">
                    📊 Resumo da Verificação
                </h3>
                <div class="summary-grid">
                    <div class="summary-item ${totalProdutos > 0 ? (erros > 0 ? 'error' : 'warning') : 'success'}">
                        <div style="font-size: 2rem; margin-bottom: 8px;">📦</div>
                        <div style="font-size: 1.8rem; font-weight: bold; margin-bottom: 5px;">${totalProdutos}</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Produtos Analisados</div>
                    </div>
                    <div class="summary-item ${erros > 0 ? 'error' : 'success'}">
                        <div style="font-size: 2rem; margin-bottom: 8px;">${erros > 0 ? '❌' : '✅'}</div>
                        <div style="font-size: 1.8rem; font-weight: bold; margin-bottom: 5px;">${erros}</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Erros Críticos</div>
                    </div>
                    <div class="summary-item ${warnings > 0 ? 'warning' : 'success'}">
                        <div style="font-size: 2rem; margin-bottom: 8px;">${warnings > 0 ? '⚠️' : '✅'}</div>
                        <div style="font-size: 1.8rem; font-weight: bold; margin-bottom: 5px;">${warnings}</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Avisos</div>
                    </div>
                    <div class="summary-item info">
                        <div style="font-size: 2rem; margin-bottom: 8px;">ℹ️</div>
                        <div style="font-size: 1.8rem; font-weight: bold; margin-bottom: 5px;">${infos}</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Informações</div>
                    </div>
                </div>
            `;

            // Preencher tabela
            if (problemas.length === 0) {
                resultsBody.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 40px; color: var(--success-color); font-size: 1.2rem;">
                            <div style="font-size: 3rem; margin-bottom: 15px;">🎉</div>
                            <div style="font-weight: 600; margin-bottom: 10px;">Parabéns!</div>
                            <div>Nenhum problema encontrado nas estruturas verificadas.</div>
                        </td>
                    </tr>
                `;
            } else {
                problemas.forEach((problema, index) => {
                    const row = document.createElement('tr');
                    row.style.animationDelay = `${index * 0.1}s`;
                    row.style.animation = 'slideUp 0.5s ease-out forwards';

                    // Determinar ícone do tipo de produto
                    const tipoIcon = {
                        'PA': '🏭',
                        'SP': '⚙️',
                        'MP': '🧱'
                    }[problema.produto.tipo] || '📦';

                    row.innerHTML = `
                        <td style="font-weight: 600; color: var(--primary-color);">
                            ${tipoIcon} ${problema.produto.codigo}
                        </td>
                        <td style="max-width: 300px; word-wrap: break-word;">
                            ${problema.produto.descricao}
                        </td>
                        <td>
                            <span style="padding: 4px 8px; background: var(--light-bg); border-radius: 6px; font-size: 0.85rem; font-weight: 600;">
                                ${problema.produto.tipo}
                            </span>
                        </td>
                        <td>
                            ${problema.problemas.some(p => p.tipo === 'error') ?
                                '<span class="status-badge status-error">❌ Erro</span>' :
                                problema.problemas.some(p => p.tipo === 'warning') ?
                                '<span class="status-badge status-warning">⚠️ Aviso</span>' :
                                '<span class="status-badge status-info">ℹ️ Info</span>'}
                        </td>
                        <td>
                            ${problema.problemas.map(p => {
                                const icon = {
                                    'error': '❌',
                                    'warning': '⚠️',
                                    'info': 'ℹ️'
                                }[p.tipo] || '•';

                                return `<div class="problem-item ${p.tipo}">
                                    <strong>${icon}</strong> ${p.mensagem}
                                </div>`;
                            }).join('')}
                        </td>
                    `;
                    resultsBody.appendChild(row);
                });
            }
        }

        window.exportarRelatorio = function() {
            if (problemas.length === 0) {
                // Criar notificação personalizada
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, var(--warning-color), #e67e22);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    box-shadow: var(--shadow-hover);
                    z-index: 1000;
                    font-weight: 600;
                    animation: slideIn 0.3s ease-out;
                `;
                notification.innerHTML = '⚠️ Execute a verificação primeiro para exportar dados!';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease-out';
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
                return;
            }

            // Feedback visual durante exportação
            const btnExport = event.target;
            const originalText = btnExport.innerHTML;
            btnExport.innerHTML = '📊 Exportando...';
            btnExport.disabled = true;

            setTimeout(() => {
                let csv = 'Código,Descrição,Tipo,Status,Problemas\n';
                problemas.forEach(problema => {
                    const status = problema.problemas.some(p => p.tipo === 'error') ? 'Erro' :
                                 problema.problemas.some(p => p.tipo === 'warning') ? 'Aviso' : 'Info';
                    const problemasStr = problema.problemas.map(p => p.mensagem).join('; ');
                    csv += `"${problema.produto.codigo}","${problema.produto.descricao}","${problema.produto.tipo}","${status}","${problemasStr}"\n`;
                });

                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `verificacao_estruturas_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Sucesso
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, var(--success-color), #229954);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    box-shadow: var(--shadow-hover);
                    z-index: 1000;
                    font-weight: 600;
                    animation: slideIn 0.3s ease-out;
                `;
                notification.innerHTML = '✅ Relatório exportado com sucesso!';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease-out';
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);

                // Restaurar botão
                btnExport.innerHTML = originalText;
                btnExport.disabled = false;
            }, 1000);
        };
    </script>
</body>
</html> 