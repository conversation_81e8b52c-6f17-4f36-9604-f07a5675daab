<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>📦 Movimentações de Estoque</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    /* ========================================
       🎨 CSS PADRONIZADO - ESTOQUES
       Baseado em: gestao_compras_integrada.html
       ======================================== */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .tabs {
      display: flex;
      border-bottom: 3px solid #ecf0f1;
      margin-bottom: 30px;
      overflow-x: auto;
    }

    .tab {
      padding: 15px 25px;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-weight: 600;
      color: #7f8c8d;
      border-radius: 8px 8px 0 0;
      margin-right: 5px;
      transition: all 0.3s ease;
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tab.active {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      transform: translateY(-2px);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .filters {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .form-control {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
    }

    .table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
    }

    .table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid;
      font-weight: 500;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: #17a2b8;
    }

    .alert-warning {
      background: #fff3cd;
      color: #856404;
      border-left-color: #ffc107;
    }

    .alert-success {
      background: #d4edda;
      color: #155724;
      border-left-color: #28a745;
    }

    /* Estilos específicos para skeleton loading */
    .loading-skeleton {
      padding: 20px;
      text-align: center;
    }

    .skeleton-line {
      height: 20px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin: 10px 0;
      border-radius: 4px;
    }

    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }

    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .toast.success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    }

    .toast.error {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    /* Melhorias visuais */
    .movement-type {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.85em;
      font-weight: 500;
    }

    .type-entrada { background: #e8f5e8; color: #2e7d32; }
    .type-saida { background: #ffebee; color: #c62828; }

    /* Responsividade */
    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .table-container {
        overflow-x: auto;
      }

      .tabs {
        flex-wrap: wrap;
      }

      .filter-row {
        grid-template-columns: 1fr;
      }
    }
  </style>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js" defer></script>
  <script type="module">
    import { db } from './firebase-config.js';
    import {
      collection,
      getDocs,
      query,
      where,
      orderBy,
      limit
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estoques = [];
    let armazens = [];

    // Inicialização
    document.addEventListener('DOMContentLoaded', async function() {
      try {
        await loadInitialData();
        setupInterface();
      } catch (error) {
        console.error('Erro na inicialização:', error);
        showError('Erro ao carregar sistema: ' + error.message);
      }
    });

    async function loadInitialData() {
      try {
        showLoading('Carregando dados...');

        const [produtosSnap, estoquesSnap, armazensSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estoques")),
          getDocs(collection(db, "armazens"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log('Dados carregados:', { produtos: produtos.length, estoques: estoques.length, armazens: armazens.length });

        loadStock();
        hideLoading();
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        showError('Erro ao carregar dados: ' + error.message);
        hideLoading();
      }
    }

    function loadStock() {
      const tbody = document.getElementById('stockTableBody');
      if (!tbody) return;

      tbody.innerHTML = '';

      // Processar dados de estoque
      const stockData = estoques.map(estoque => {
        const produto = produtos.find(p => p.id === estoque.produtoId);
        const armazem = armazens.find(a => a.id === estoque.armazemId);

        return {
          ...estoque,
          codigo: produto?.codigo || '-',
          descricao: produto?.descricao || '-',
          tipo: produto?.tipo || '-',
          unidade: produto?.unidade || '-',
          armazem: armazem?.codigo || '-'
        };
      }); // Mostrar todos os itens (incluindo saldo negativo)

      if (stockData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 20px;">Nenhum produto encontrado no estoque</td></tr>';
        return;
      }

      stockData.forEach(item => {
        const saldoTotal = item.saldo || 0;
        const saldoReservado = item.saldoReservado || 0;
        const saldoDisponivel = Math.max(0, saldoTotal - saldoReservado);

        // Definir cores baseadas na disponibilidade
        const corSaldoReservado = saldoReservado > 0 ? 'color: #dc3545; font-weight: bold;' : '';
        const corSaldoDisponivel = saldoDisponivel <= 0 ? 'color: #dc3545; font-weight: bold;' :
                                   saldoDisponivel < (saldoTotal * 0.2) ? 'color: #ffc107; font-weight: bold;' :
                                   'color: #28a745; font-weight: bold;';

        // Destacar saldo total negativo
        const corSaldoTotal = saldoTotal < 0 ? 'color: #dc3545; font-weight: bold; background-color: #f8d7da;' : 'font-weight: bold;';

        // Destacar linha inteira se saldo negativo
        const rowStyle = saldoTotal < 0 ? 'background-color: #f8d7da; border-left: 4px solid #dc3545;' : '';

        const row = document.createElement('tr');
        row.style.cssText = rowStyle;
        row.innerHTML = `
          <td>${item.codigo}</td>
          <td>${item.descricao}</td>
          <td>${item.tipo}</td>
          <td>${item.unidade}</td>
          <td>${item.armazem}</td>
          <td style="text-align: right; ${corSaldoTotal}">${formatNumber(saldoTotal)}</td>
          <td style="text-align: right; ${corSaldoReservado}">${formatNumber(saldoReservado)}</td>
          <td style="text-align: right; ${corSaldoDisponivel}">${formatNumber(saldoDisponivel)}</td>
          <td style="text-align: right">${formatCurrency(item.precoUnitario || 0)}</td>
          <td style="text-align: right">${formatCurrency(saldoTotal * (item.precoUnitario || 0))}</td>
          <td>${formatDate(item.ultimaMovimentacao)}</td>
        `;
        tbody.appendChild(row);
      });
    }

    function setupInterface() {
      // Configurar busca
      const searchInput = document.getElementById('searchInput');
      if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
      }

      // Configurar botão de busca
      const btnBuscar = document.getElementById('btnBuscarSaldos');
      if (btnBuscar) {
        btnBuscar.onclick = filterStock;
      }
    }

    function handleSearch(event) {
      const searchTerm = event.target.value.toLowerCase().trim();
      filterStock(searchTerm);
    }

    function filterStock(searchTerm = '') {
      const tbody = document.getElementById('stockTableBody');
      if (!tbody) return;

      const searchText = searchTerm || document.getElementById('searchInput')?.value?.toLowerCase() || '';

      tbody.innerHTML = '';

      let filteredData = estoques.map(estoque => {
        const produto = produtos.find(p => p.id === estoque.produtoId);
        const armazem = armazens.find(a => a.id === estoque.armazemId);

        return {
          ...estoque,
          codigo: produto?.codigo || '-',
          descricao: produto?.descricao || '-',
          tipo: produto?.tipo || '-',
          unidade: produto?.unidade || '-',
          armazem: armazem?.codigo || '-'
        };
      }); // Mostrar todos os itens (incluindo saldo negativo)

      // Aplicar filtro de busca
      if (searchText) {
        filteredData = filteredData.filter(item =>
          item.codigo.toLowerCase().includes(searchText) ||
          item.descricao.toLowerCase().includes(searchText)
        );
      }

      if (filteredData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 20px;">Nenhum resultado encontrado</td></tr>';
        return;
      }

      filteredData.forEach(item => {
        const saldoTotal = item.saldo || 0;
        const saldoReservado = item.saldoReservado || 0;
        const saldoDisponivel = Math.max(0, saldoTotal - saldoReservado);

        // Definir cores baseadas na disponibilidade
        const corSaldoReservado = saldoReservado > 0 ? 'color: #dc3545; font-weight: bold;' : '';
        const corSaldoDisponivel = saldoDisponivel <= 0 ? 'color: #dc3545; font-weight: bold;' :
                                   saldoDisponivel < (saldoTotal * 0.2) ? 'color: #ffc107; font-weight: bold;' :
                                   'color: #28a745; font-weight: bold;';

        // Destacar saldo total negativo
        const corSaldoTotal = saldoTotal < 0 ? 'color: #dc3545; font-weight: bold; background-color: #f8d7da;' : 'font-weight: bold;';

        // Destacar linha inteira se saldo negativo
        const rowStyle = saldoTotal < 0 ? 'background-color: #f8d7da; border-left: 4px solid #dc3545;' : '';

        const row = document.createElement('tr');
        row.style.cssText = rowStyle;
        row.innerHTML = `
          <td>${item.codigo}</td>
          <td>${item.descricao}</td>
          <td>${item.tipo}</td>
          <td>${item.unidade}</td>
          <td>${item.armazem}</td>
          <td style="text-align: right; ${corSaldoTotal}">${formatNumber(saldoTotal)}</td>
          <td style="text-align: right; ${corSaldoReservado}">${formatNumber(saldoReservado)}</td>
          <td style="text-align: right; ${corSaldoDisponivel}">${formatNumber(saldoDisponivel)}</td>
          <td style="text-align: right">${formatCurrency(item.precoUnitario || 0)}</td>
          <td style="text-align: right">${formatCurrency(saldoTotal * (item.precoUnitario || 0))}</td>
          <td>${formatDate(item.ultimaMovimentacao)}</td>
        `;
        tbody.appendChild(row);
      });
    }

    // Funções utilitárias
    function formatNumber(value) {
      return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
      }).format(value);
    }

    function formatCurrency(value) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    }

    function formatDate(timestamp) {
      if (!timestamp) return '';

      try {
        let date;
        if (typeof timestamp.toDate === 'function') {
          date = timestamp.toDate();
        } else if (typeof timestamp.seconds === 'number') {
          date = new Date(timestamp.seconds * 1000);
        } else if (timestamp instanceof Date) {
          date = timestamp;
        } else {
          return '';
        }
        return new Intl.DateTimeFormat('pt-BR').format(date);
      } catch (e) {
        return '';
      }
    }

    function showLoading(message) {
      const tbody = document.getElementById('stockTableBody');
      if (tbody) {
        tbody.innerHTML = `<tr><td colspan="9" style="text-align: center; padding: 20px;">${message}</td></tr>`;
      }
    }

    function hideLoading() {
      // Loading será substituído pelos dados
    }

    function showError(message) {
      console.error(message);
      const tbody = document.getElementById('stockTableBody');
      if (tbody) {
        tbody.innerHTML = `<tr><td colspan="9" style="text-align: center; padding: 20px; color: red;">${message}</td></tr>`;
      }
    }

    // Funções globais para compatibilidade
    window.switchTab = function(tab) {
      document.querySelectorAll('.tab-content').forEach(content =>
        content.classList.remove('active')
      );
      document.querySelectorAll('.btn').forEach(button =>
        button.classList.remove('active')
      );

      const tabContent = document.getElementById(`${tab}Tab`);
      const tabButton = document.querySelector(`button[onclick="switchTab('${tab}')"]`);

      if (tabContent) tabContent.classList.add('active');
      if (tabButton) tabButton.classList.add('active');
    };

    window.filterStock = filterStock;
  </script>
</head>
<body>
<div class="container">
  <div class="header">
    <h1>
      <i class="fas fa-boxes"></i>
      Movimentações de Estoque
    </h1>
    <div class="header-actions">
      <button class="btn btn-warning" onclick="window.location.href='index.html'">
        <i class="fas fa-home"></i>
        Voltar
      </button>
    </div>
  </div>

  <div class="main-content">

    <!-- Filtros -->
    <div class="filters">
      <h3>
        <i class="fas fa-filter"></i>
        Filtros Avançados
      </h3>
      <div class="filter-row">
        <div class="form-group">
          <label>Buscar:</label>
          <input type="text" id="searchInput" class="form-control" placeholder="Buscar por código ou descrição...">
        </div>
        <div class="form-group">
          <label for="typeFilter">Filtrar por Tipo:</label>
          <select id="typeFilter" class="form-control" title="Filtrar por tipo">
            <option value="">Todos os tipos</option>
            <option value="MP">📦 Matéria Prima</option>
            <option value="SP">⚙️ Semi-Produto</option>
            <option value="PA">📋 Produto Acabado</option>
          </select>
        </div>
        <div class="form-group">
          <label for="warehouseFilter">Filtrar por Armazém:</label>
          <select id="warehouseFilter" class="form-control" title="Filtrar por armazém">
            <option value="">Todos os armazéns</option>
          </select>
        </div>
        <div class="form-group">
          <label>&nbsp;</label>
          <button class="btn btn-primary" id="btnBuscarSaldos" type="button">
            <i class="fas fa-search"></i>
            Buscar
          </button>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="tabs">
      <button class="tab active" onclick="switchTab('saldos')">
        <i class="fas fa-warehouse"></i>
        Saldos em Estoque
      </button>
      <button class="tab" onclick="switchTab('movimentacoes')">
        <i class="fas fa-exchange-alt"></i>
        Movimentações
      </button>
    </div>

    <!-- Tab Content: Saldos -->
    <div id="saldosTab" class="tab-content active">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Tipo</th>
              <th>Unidade</th>
              <th>Armazém</th>
              <th style="text-align: right;">Saldo Total</th>
              <th style="text-align: right;">Saldo Reservado</th>
              <th style="text-align: right;">Saldo Disponível</th>
              <th style="text-align: right;">Custo Unitário</th>
              <th style="text-align: right;">Valor Total</th>
              <th>Última Movimentação</th>
            </tr>
          </thead>
          <tbody id="stockTableBody">
            <tr>
              <td colspan="11" style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin"></i>
                Carregando dados...
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Tab Content: Movimentações -->
    <div id="movimentacoesTab" class="tab-content">
      <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Movimentações de Estoque:</strong> Use esta seção para registrar entradas e saídas de materiais.
      </div>

      <form id="movementForm" onsubmit="handleMovement(event)">
        <div class="filter-row">
          <div class="form-group">
            <label for="movementType">Tipo de Movimentação:</label>
            <select id="movementType" class="form-control" required onchange="updateWarehouseOptions()">
              <option value="entrada">📥 Entrada</option>
              <option value="saida">📤 Saída</option>
            </select>
          </div>

          <div class="form-group">
            <label for="productSelect">Produto:</label>
            <select id="productSelect" class="form-control" required onchange="updateProductInfo()">
              <option value="">Selecione um produto...</option>
            </select>
          </div>
        </div>

        <div class="filter-row">
          <div class="form-group">
            <label for="warehouseOrigin">Armazém de Origem:</label>
            <select id="warehouseOrigin" class="form-control" required>
              <option value="">Selecione o armazém...</option>
            </select>
          </div>
          <div class="form-group">
            <label for="warehouseDestiny">Armazém de Destino:</label>
            <select id="warehouseDestiny" class="form-control" required>
              <option value="">Selecione o armazém...</option>
            </select>
          </div>
        </div>

        <div class="filter-row">
          <div class="form-group">
            <label for="movementUnit">Unidade de Movimentação:</label>
            <select id="movementUnit" class="form-control" required onchange="updateCalculations()">
              <option value="principal">📏 Unidade Principal</option>
              <option value="secundaria">📐 Unidade Secundária</option>
            </select>
          </div>
          <div class="form-group">
            <label for="quantity">Quantidade:</label>
            <input type="number" id="quantity" class="form-control" min="0.001" step="0.001" required oninput="updateCalculations()">
          </div>
          <div class="form-group">
            <label for="valorUnitario">Valor Unitário (R$):</label>
            <input type="number" id="valorUnitario" class="form-control" min="0" step="0.01" required oninput="updateCalculations()">
          </div>
        </div>

      <div class="form-group">
        <label for="documentType">Tipo de Documento:</label>
        <select id="documentType" required onchange="updateTESOptions()">
          <option value="COMPRA">Nota Fiscal de Compra</option>
          <option value="VENDA">Nota Fiscal de Venda</option>
          <option value="PRODUCAO">Ordem de Produção</option>
          <option value="CONSUMO">Requisição de Material</option>
          <option value="AJUSTE">Ajuste de Inventário</option>
        </select>
      </div>

      <div class="form-group">
        <label for="tes">TES - Tipo de Entrada/Saída:</label>
        <select id="tes" required>
          <option value="">Selecione o TES...</option>
        </select>
      </div>

      <div class="form-group">
        <label for="documentNumber">Número do Documento:</label>
        <input type="text" id="documentNumber" required>
      </div>

      <div class="form-group">
        <label for="observations">Observações:</label>
        <textarea id="observations" rows="3"></textarea>
      </div>

      <div class="form-row">
        <div class="form-col">
          <label>Estoque Atual:</label>
          <div id="currentStock" class="status-message info">-</div>
        </div>
        <div class="form-col">
          <label>Saldo Futuro:</label>
          <div id="futureStock" class="status-message info">-</div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-col">
          <label>Valor Total:</label>
          <div id="totalValue" class="status-message info">-</div>
        </div>
      </div>

      <button type="submit" class="btn btn-success">Confirmar Movimentação</button>
    </form>
  </div>
</div>

<div id="movementModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h2 id="modalTitle">Nova Movimentação</h2>
      <span class="close-button" onclick="closeModal()">×</span>
    </div>

    <div class="modal-body">
      <form id="movementForm" onsubmit="handleMovement(event)">
        <input type="hidden" id="movementType">

        <div class="form-group">
          <label for="productSelect">Produto:</label>
          <select id="productSelect" required onchange="updateCalculations()">
            <option value="">Selecione o produto...</option>
          </select>
        </div>

        <div class="form-group">
          <label for="movementUnit">Unidade:</label>
          <select id="movementUnit" onchange="updateCalculations()">
            <option value="principal">Unidade Principal</option>
            <option value="secundaria">Unidade Secundária</option>
          </select>
        </div>

        <div class="form-group">
          <label for="warehouseOrigin">Armazém de Origem:</label>
          <select id="warehouseOrigin" required>
            <option value="">Selecione o armazém de origem...</option>
          </select>
        </div>

        <div class="form-group">
          <label for="warehouseDestiny">Armazém de Destino:</label>
          <select id="warehouseDestiny" required>
            <option value="">Selecione o armazém de destino...</option>
          </select>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="quantity">Quantidade:</label>
            <input type="number" id="quantity" min="0.001" step="0.001" required oninput="updateCalculations()">
          </div>
          <div class="form-col">
            <label for="valorUnitario">Valor Unitário (R$):</label>
            <input type="number" id="valorUnitario" min="0" step="0.01" required oninput="updateCalculations()">
          </div>
          <div class="form-col">
            <label for="documentType">Tipo de Documento:</label>
            <select id="documentType" required onchange="updateTESOptions()">
              <option value="COMPRA">Nota Fiscal de Compra</option>
              <option value="VENDA">Nota Fiscal de Venda</option>
              <option value="PRODUCAO">Ordem de Produção</option>
              <option value="CONSUMO">Requisição de Material</option>
              <option value="AJUSTE">Ajuste de Inventário</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="tes">TES - Tipo de Entrada/Saída:</label>
          <select id="tes" required>
            <option value="">Selecione o TES...</option>
          </select>
        </div>

        <div class="form-group">
          <label for="documentNumber">Número do Documento:</label>
          <input type="text" id="documentNumber" required>
        </div>

        <div class="form-group">
          <label for="observations">Observações:</label>
          <textarea id="observations" rows="3"></textarea>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label>Estoque Atual:</label>
            <div id="currentStock" class="status-message info">-</div>
          </div>
          <div class="form-col">
            <label>Saldo Futuro:</label>
            <div id="futureStock" class="status-message info">-</div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label>Valor Total:</label>
            <div id="totalValue" class="status-message info">-</div>
          </div>
        </div>

        <button type="submit" class="btn btn-success">Confirmar Movimentação</button>
      </form>
    </div>
  </div>
</div>

</body>
</html>