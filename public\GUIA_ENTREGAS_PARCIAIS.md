# 📦 GUIA PRÁTICO - ENTREGAS PARCIAIS

## 🎯 **COMO PROCESSAR ENTREGAS PARCIAIS CORRETAMENTE**

### **📋 CONCEITO IMPORTANTE:**
> **Cada Nota Fiscal = Um Recebimento Separado**
> 
> Mesmo que seja do mesmo pedido, cada nota fiscal deve ser processada individualmente no sistema.

---

## 🔄 **FLUXO COMPLETO DE ENTREGAS PARCIAIS**

### **📊 EXEMPLO PRÁTICO:**

#### **🛒 PEDIDO ORIGINAL:**
```
PC-2024001 - Fornecedor ABC Ltda
┌─────────────────────────────────────────┐
│ Item: Parafuso M6x20                    │
│ Quantidade: 1000 unidades               │
│ Valor Unitário: R$ 1,50                │
│ Total: R$ 1.500,00                     │
└─────────────────────────────────────────┘
```

#### **📦 PRIMEIRA ENTREGA:**
```
NF: 12345 - Data: 15/01/2024
┌─────────────────────────────────────────┐
│ Item: Parafuso M6x20                    │
│ Quantidade Entregue: 400 unidades      │
│ Valor da NF: R$ 600,00                 │
│ Saldo Pendente: 600 unidades           │
└─────────────────────────────────────────┘
```

#### **📦 SEGUNDA ENTREGA:**
```
NF: 12389 - Data: 22/01/2024
┌─────────────────────────────────────────┐
│ Item: Parafuso M6x20                    │
│ Quantidade Entregue: 350 unidades      │
│ Valor da NF: R$ 525,00                 │
│ Saldo Pendente: 250 unidades           │
└─────────────────────────────────────────┘
```

#### **📦 TERCEIRA ENTREGA (FINAL):**
```
NF: 12401 - Data: 30/01/2024
┌─────────────────────────────────────────┐
│ Item: Parafuso M6x20                    │
│ Quantidade Entregue: 250 unidades      │
│ Valor da NF: R$ 375,00                 │
│ Saldo Pendente: 0 unidades             │
│ STATUS: PEDIDO COMPLETO                 │
└─────────────────────────────────────────┘
```

---

## 📝 **PASSO A PASSO NO SISTEMA**

### **🔍 PRIMEIRA ENTREGA:**

#### **1️⃣ SELEÇÃO DO PEDIDO:**
```
1. Acesse: recebimento_materiais_avancado.html
2. Selecione: PC-2024001 - Fornecedor ABC Ltda
3. Sistema carrega automaticamente:
   ✅ Informações do fornecedor
   ✅ Status de prazo
   ✅ Resumo do pedido
```

#### **2️⃣ RESUMO DO PEDIDO (PRIMEIRA VEZ):**
```
┌─────────────────────────────────────────┐
│ 📊 RESUMO DO PEDIDO                     │
├─────────────────────────────────────────┤
│ Total de Itens: 1                      │
│ Valor Total: R$ 1.500,00               │
│ Valor Recebido: R$ 0,00                │
│ Valor Pendente: R$ 1.500,00            │
│ Progresso: 0%                          │
│ Status: [PENDENTE]                      │
└─────────────────────────────────────────┘
```

#### **3️⃣ DADOS DA PRIMEIRA NF:**
```
┌─────────────────────────────────────────┐
│ 📋 DADOS DO RECEBIMENTO                 │
├─────────────────────────────────────────┤
│ Número da NF: [12345]                  │
│ Data da NF: [15/01/2024]               │
│ Valor Total da NF: [600,00]            │
│ Armazém: [Estoque Principal]           │
│ Observações: [Primeira entrega]        │
└─────────────────────────────────────────┘
```

#### **4️⃣ TABELA DE ITENS:**
```
┌─────┬─────────────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│Cód. │ Descrição   │Ped. │Rec. │Sald.│Rec. │Pr.Or│Pr.NF│Dest.│Stat.│
├─────┼─────────────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤
│PAR01│Parafuso M6  │1000 │  0  │1000 │[400]│ 1,50│ 1,50│EST. │ OK  │
└─────┴─────────────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘
```

#### **5️⃣ PROCESSAR PRIMEIRA ENTREGA:**
```
✅ Clique: "Processar Recebimento"
✅ Sistema registra: 400 unidades recebidas
✅ Atualiza pedido: Saldo pendente = 600 unidades
✅ Cria movimentação de estoque
✅ Registra histórico de entrega
```

---

### **🔍 SEGUNDA ENTREGA:**

#### **1️⃣ SELEÇÃO DO MESMO PEDIDO:**
```
1. Selecione NOVAMENTE: PC-2024001 - Fornecedor ABC Ltda
2. Sistema detecta entrega anterior:
   ⚠️ ALERTA: "Entrega Parcial Detectada"
   📊 Mostra progresso atual
```

#### **2️⃣ RESUMO ATUALIZADO:**
```
┌─────────────────────────────────────────┐
│ 📊 RESUMO DO PEDIDO                     │
├─────────────────────────────────────────┤
│ Total de Itens: 1                      │
│ Valor Total: R$ 1.500,00               │
│ Valor Recebido: R$ 600,00              │
│ Valor Pendente: R$ 900,00              │
│ Progresso: 40%                         │
│ Status: [PARCIAL]                      │
└─────────────────────────────────────────┘
```

#### **3️⃣ ALERTA DE ENTREGA PARCIAL:**
```
┌─────────────────────────────────────────┐
│ ℹ️ ENTREGA PARCIAL DETECTADA            │
├─────────────────────────────────────────┤
│ Este pedido já teve entregas anteriores │
│ Recebido: 400 de 1000 itens totais     │
│ Importante: Processe cada NF separada   │
└─────────────────────────────────────────┘
```

#### **4️⃣ TABELA ATUALIZADA:**
```
┌─────┬─────────────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│Cód. │ Descrição   │Ped. │Rec. │Sald.│Rec. │Pr.Or│Pr.NF│Dest.│Stat.│
├─────┼─────────────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤
│PAR01│Parafuso M6  │1000 │ 400 │ 600 │[350]│ 1,50│ 1,50│EST. │ OK  │
└─────┴─────────────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘
```

#### **5️⃣ DADOS DA SEGUNDA NF:**
```
┌─────────────────────────────────────────┐
│ 📋 DADOS DO RECEBIMENTO                 │
├─────────────────────────────────────────┤
│ Número da NF: [12389]                  │
│ Data da NF: [22/01/2024]               │
│ Valor Total da NF: [525,00]            │
│ Armazém: [Estoque Principal]           │
│ Observações: [Segunda entrega]         │
└─────────────────────────────────────────┘
```

---

### **🔍 HISTÓRICO DE ENTREGAS:**

#### **📜 APÓS SEGUNDA ENTREGA:**
```
┌─────────────────────────────────────────┐
│ 📜 HISTÓRICO DE ENTREGAS PARCIAIS       │
├─────────────────────────────────────────┤
│ NF: 12345 - 15/01/2024                 │
│ 1 item recebido            R$ 600,00   │
├─────────────────────────────────────────┤
│ NF: 12389 - 22/01/2024                 │
│ 1 item recebido            R$ 525,00   │
└─────────────────────────────────────────┘
```

---

## ⚠️ **PONTOS IMPORTANTES**

### **🔑 REGRAS FUNDAMENTAIS:**

#### **1️⃣ UMA NF = UM RECEBIMENTO:**
```
❌ ERRADO: Tentar processar múltiplas NFs juntas
✅ CORRETO: Cada NF é um recebimento separado
```

#### **2️⃣ MESMO PEDIDO, MÚLTIPLOS RECEBIMENTOS:**
```
❌ ERRADO: Criar novo pedido para cada entrega
✅ CORRETO: Usar o mesmo pedido para todas as entregas
```

#### **3️⃣ CONTROLE DE SALDO:**
```
✅ Sistema calcula automaticamente:
   - Quantidade já recebida
   - Saldo pendente
   - Progresso do pedido
```

#### **4️⃣ VALIDAÇÕES AUTOMÁTICAS:**
```
✅ Não permite receber mais que o pedido
✅ Valida preços conforme tolerância
✅ Controla quantidades por tolerância
✅ Registra histórico completo
```

---

## 🎯 **CENÁRIOS ESPECIAIS**

### **📦 ENTREGA COM EXCESSO:**
```
Pedido: 1000 unidades
Já Recebido: 750 unidades
Saldo: 250 unidades
Entrega Atual: 300 unidades (50 a mais)

Sistema:
⚠️ Alerta de excesso (20%)
🔒 Pode exigir autorização (se configurado)
✅ Permite dentro da tolerância
```

### **💰 ENTREGA COM PREÇO DIFERENTE:**
```
Preço Original: R$ 1,50
Preço da NF: R$ 1,60 (6,7% mais caro)

Sistema:
⚠️ Alerta de divergência de preço
🔒 Pode bloquear (se configurado)
✅ Permite dentro da tolerância
```

### **📅 ENTREGA EM ATRASO:**
```
Data Prevista: 20/01/2024
Data Atual: 25/01/2024

Sistema:
🚨 Pedido aparece em vermelho na lista
⚠️ Status: "5 DIAS EM ATRASO"
📊 Prioridade na lista de pedidos
```

---

## ✅ **CHECKLIST DE ENTREGA PARCIAL**

### **📋 ANTES DE PROCESSAR:**
- [ ] Conferir se é o pedido correto
- [ ] Verificar dados do fornecedor
- [ ] Conferir número da nota fiscal
- [ ] Validar data da nota fiscal
- [ ] Conferir valor total da NF

### **📋 DURANTE O PROCESSAMENTO:**
- [ ] Verificar quantidades por item
- [ ] Validar preços unitários
- [ ] Conferir destino dos materiais
- [ ] Observar alertas do sistema
- [ ] Verificar resumo da entrega atual

### **📋 APÓS PROCESSAR:**
- [ ] Conferir atualização do saldo
- [ ] Verificar histórico de entregas
- [ ] Validar movimentação de estoque
- [ ] Confirmar status do pedido

---

## 🚀 **BENEFÍCIOS DO SISTEMA**

### **✅ CONTROLE TOTAL:**
- **Rastreabilidade** completa de cada entrega
- **Histórico** detalhado por nota fiscal
- **Progresso** visual do pedido
- **Validações** automáticas

### **✅ FACILIDADE DE USO:**
- **Interface** intuitiva
- **Alertas** claros e informativos
- **Cálculos** automáticos
- **Resumos** em tempo real

### **✅ SEGURANÇA:**
- **Validações** de quantidade e preço
- **Controle** de tolerâncias
- **Autorização** para exceções
- **Auditoria** completa

**Com este sistema, o controle de entregas parciais fica simples, seguro e totalmente rastreável!** 🎉✅📦
