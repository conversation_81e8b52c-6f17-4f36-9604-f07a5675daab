// Adicione este código no início do seu arquivo JavaScript (pode ser cotacoes.js ou diretamente no <script> do cotacoes.html)

// Função para adicionar o botão de aglutinação
function adicionarBotaoAglutinacao() {
  // Procurar pelo elemento onde queremos adicionar o botão (pode ser a barra de filtros ou outro local visível)
  const containerAlvo = document.querySelector('.filter-buttons') || 
                        document.querySelector('.actions-container') || 
                        document.querySelector('.header');
  
  if (!containerAlvo) {
    console.error('Não foi possível encontrar um container para o botão de aglutinação');
    return;
  }
  
  // Verificar se o botão já existe para evitar duplicação
  if (document.getElementById('btnAglutinar')) {
    return;
  }
  
  // Criar o botão
  const botaoAglutinar = document.createElement('button');
  botaoAglutinar.id = 'btnAglutinar';
  botaoAglutinar.className = 'btn-primary';
  botaoAglutinar.style.backgroundColor = '#28a745';
  botaoAglutinar.style.marginRight = '10px';
  botaoAglutinar.innerHTML = '<i class="fas fa-object-group"></i> Aglutinar';
  botaoAglutinar.onclick = mostrarModalAglutinacao;
  
  // Adicionar o botão ao container
  containerAlvo.prepend(botaoAglutinar);
  
  console.log('Botão de aglutinação adicionado com sucesso');
}

// Função para mostrar o modal de aglutinação
function mostrarModalAglutinacao() {
  // Verificar se há cotações selecionadas
  const cotacoesSelecionadas = obterCotacoesSelecionadas();
  
  if (cotacoesSelecionadas.length < 2) {
    alert('Selecione pelo menos duas cotações para aglutinar.');
    return;
  }
  
  // Verificar status das cotações
  const statusInvalidos = cotacoesSelecionadas.filter(c => 
    ['FECHADA', 'CANCELADO', 'AGLUTINADO'].includes(c.status)
  );
  
  if (statusInvalidos.length > 0) {
    alert('Não é possível aglutinar cotações com status: FECHADA, CANCELADO ou AGLUTINADO.');
    return;
  }
  
  // Criar o modal
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.id = 'modalAglutinacao';
  modal.style.display = 'block';
  modal.style.position = 'fixed';
  modal.style.zIndex = '1000';
  modal.style.left = '0';
  modal.style.top = '0';
  modal.style.width = '100%';
  modal.style.height = '100%';
  modal.style.overflow = 'auto';
  modal.style.backgroundColor = 'rgba(0,0,0,0.4)';
  
  // Conteúdo do modal
  modal.innerHTML = `
    <div style="background-color: white; margin: 10% auto; padding: 20px; border-radius: 5px; width: 80%; max-width: 600px;">
      <h2>Aglutinar Cotações</h2>
      <p>Você está prestes a aglutinar ${cotacoesSelecionadas.length} cotações.</p>
      
      <div style="margin: 15px 0;">
        <label for="nomeAglutinacao">Nome da cotação aglutinada:</label>
        <input type="text" id="nomeAglutinacao" value="AGLUT-${new Date().toISOString().slice(0,10)}" 
               style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      
      <div style="margin-top: 20px; text-align: right;">
        <button onclick="document.getElementById('modalAglutinacao').remove()" 
                style="padding: 8px 15px; margin-right: 10px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Cancelar
        </button>
        <button onclick="executarAglutinacao()" 
                style="padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Confirmar
        </button>
      </div>
    </div>
  `;
  
  // Adicionar o modal ao body
  document.body.appendChild(modal);
}

// Função para obter as cotações selecionadas
function obterCotacoesSelecionadas() {
  // Obter os checkboxes selecionados
  const checkboxesSelecionados = document.querySelectorAll('.selectCotacao:checked');
  
  // Mapear para obter os IDs das cotações
  const idsSelecionados = Array.from(checkboxesSelecionados).map(cb => cb.value);
  
  // Filtrar as cotações pelo ID
  return cotacoes.filter(c => idsSelecionados.includes(c.id));
}

// Função para executar a aglutinação
async function executarAglutinacao() {
  try {
    const cotacoesSelecionadas = obterCotacoesSelecionadas();
    const nomeAglutinacao = document.getElementById('nomeAglutinacao').value.trim();
    
    if (!nomeAglutinacao) {
      alert('Informe um nome para a cotação aglutinada.');
      return;
    }
    
    // Criar nova cotação aglutinada
    const novaCotacao = {
      numero: nomeAglutinacao,
      status: 'ABERTA',
      cotacoesAglutinadas: cotacoesSelecionadas.map(c => c.id),
      itens: [],
      fornecedores: [],
      dataCriacao: new Date(),
      criadoPor: currentUser?.nome || 'Sistema',
      alteracoes: [{
        data: new Date(),
        usuario: currentUser?.nome || 'Sistema',
        tipo: 'CRIACAO',
        descricao: `Cotação aglutinada criada a partir de ${cotacoesSelecionadas.length} cotações`
      }]
    };
    
    // Consolidar itens
    const itensMap = new Map();
    
    cotacoesSelecionadas.forEach(cotacao => {
      // Adicionar itens
      if (cotacao.itens && Array.isArray(cotacao.itens)) {
        cotacao.itens.forEach(item => {
          const key = item.codigo || item.produtoId || JSON.stringify(item);
          
          if (!itensMap.has(key)) {
            itensMap.set(key, { ...item });
          } else {
            // Se o item já existe, somar as quantidades
            const itemExistente = itensMap.get(key);
            itemExistente.quantidade = (parseFloat(itemExistente.quantidade) || 0) + 
                                      (parseFloat(item.quantidade) || 0);
          }
        });
      }
      
      // Adicionar fornecedores
      if (cotacao.fornecedores && Array.isArray(cotacao.fornecedores)) {
        novaCotacao.fornecedores = [...new Set([...novaCotacao.fornecedores, ...cotacao.fornecedores])];
      }
    });
    
    // Converter o Map de itens para array
    novaCotacao.itens = Array.from(itensMap.values());
    
    // Fechar o modal
    document.getElementById('modalAglutinacao').remove();
    
    // Mostrar loading
    alert('Processando aglutinação...');
    
    // Salvar nova cotação aglutinada
    const docRef = await addDoc(collection(db, 'cotacoes'), novaCotacao);
    
    // Atualizar status das cotações originais
    for (const cotacao of cotacoesSelecionadas) {
      await updateDoc(doc(db, 'cotacoes', cotacao.id), { 
        status: 'AGLUTINADO', 
        cotacaoAglutinadora: docRef.id,
        alteracoes: [...(cotacao.alteracoes || []), {
          data: new Date(),
          usuario: currentUser?.nome || 'Sistema',
          tipo: 'AGLUTINACAO',
          descricao: `Cotação aglutinada em ${nomeAglutinacao}`
        }]
      });
    }
    
    // Mostrar sucesso
    alert('Cotações aglutinadas com sucesso!');
    
    // Recarregar dados
    await loadInitialData();
    await loadQuotations();
    
  } catch (e) {
    console.error('Erro ao aglutinar:', e);
    alert('Erro ao aglutinar: ' + (e.message || e));
  }
}

// Função para adicionar checkboxes na tabela
function adicionarCheckboxesNaTabela() {
  const tabela = document.querySelector('.quotations-table');
  if (!tabela) return;
  
  // Adicionar checkbox no cabeçalho
  const cabecalho = tabela.querySelector('thead tr');
  if (cabecalho) {
    // Verificar se já existe o checkbox no cabeçalho
    if (!cabecalho.querySelector('.select-all-header')) {
      // Criar nova célula para o cabeçalho
      const th = document.createElement('th');
      th.className = 'select-all-header';
      th.style.width = '40px';
      th.innerHTML = '<input type="checkbox" onclick="selecionarTodasCotacoes(this)">';
      
      // Adicionar no início do cabeçalho
      cabecalho.insertBefore(th, cabecalho.firstChild);
    }
  }
  
  // Adicionar checkbox em cada linha
  const linhas = tabela.querySelectorAll('tbody tr');
  linhas.forEach(linha => {
    // Verificar se já tem checkbox
    if (!linha.querySelector('.selectCotacao')) {
      // Extrair ID da cotação
      const cotacaoId = linha.getAttribute('ondblclick')?.match(/viewQuotation\('([^']+)'\)/)?.[1];
      if (cotacaoId) {
        // Criar nova célula para o checkbox
        const td = document.createElement('td');
        td.style.textAlign = 'center';
        td.innerHTML = `<input type="checkbox" class="selectCotacao" value="${cotacaoId}">`;
        
        // Adicionar no início da linha
        linha.insertBefore(td, linha.firstChild);
      }
    }
  });
}

// Função para selecionar todas as cotações
function selecionarTodasCotacoes(checkbox) {
  document.querySelectorAll('.selectCotacao').forEach(cb => {
    cb.checked = checkbox.checked;
  });
}

// Adicionar inicialização ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
  // Tentar adicionar o botão e os checkboxes após um pequeno delay
  setTimeout(() => {
    adicionarBotaoAglutinacao();
    adicionarCheckboxesNaTabela();
  }, 1000);
});

// Interceptar a função loadQuotations para adicionar checkboxes após carregar os dados
const originalLoadQuotations = window.loadQuotations;
if (originalLoadQuotations) {
  window.loadQuotations = async function() {
    // Chamar a função original
    await originalLoadQuotations();
    
    // Adicionar checkboxes e botão
    setTimeout(() => {
      adicionarCheckboxesNaTabela();
      adicionarBotaoAglutinacao();
    }, 500);
  };
}

// Interceptar a função aplicarFiltrosCotacoes para adicionar checkboxes após filtrar
const originalAplicarFiltrosCotacoes = window.aplicarFiltrosCotacoes;
if (originalAplicarFiltrosCotacoes) {
  window.aplicarFiltrosCotacoes = function() {
    // Chamar a função original
    originalAplicarFiltrosCotacoes();
    
    // Adicionar checkboxes e botão
    setTimeout(() => {
      adicionarCheckboxesNaTabela();
      adicionarBotaoAglutinacao();
    }, 500);
  };
}