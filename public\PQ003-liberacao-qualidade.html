<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ003 - Liberação de Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #27ae60;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #27ae60;
            margin-right: 10px;
            width: 20px;
        }

        .liberation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .liberation-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .liberation-card:hover {
            transform: translateY(-5px);
        }

        .liberation-card.pendente { border-left-color: #f39c12; }
        .liberation-card.aprovado { border-left-color: #27ae60; }
        .liberation-card.rejeitado { border-left-color: #e74c3c; }
        .liberation-card.liberado { border-left-color: #3498db; }

        .liberation-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .liberation-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            text-transform: uppercase;
            margin-top: 5px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .status-liberado {
            background: #cce5ff;
            color: #004085;
        }

        .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .priority-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .priority-alta { background: #e74c3c; }
        .priority-media { background: #f39c12; }
        .priority-baixa { background: #27ae60; }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .bulk-actions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .bulk-actions.active {
            display: block;
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .liberation-grid {
                grid-template-columns: 1fr;
            }
            
            .filters {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .btn-action {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ PQ003 - Liberação de Qualidade</h1>
            <p>Aprovação final para uso dos materiais</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Liberar materiais aprovados na inspeção para uso no estoque principal</li>
                    <li><i class="fas fa-check-double"></i><strong>Processo:</strong> Revisão final e transferência do armazém de qualidade para estoque</li>
                    <li><i class="fas fa-shipping-fast"></i><strong>Resultado:</strong> Materiais disponíveis para produção e vendas</li>
                    <li><i class="fas fa-shield-alt"></i><strong>Controle:</strong> Rastreabilidade completa do processo de liberação</li>
                </ul>
            </div>

            <!-- Cards de Liberação -->
            <div class="liberation-grid">
                <div class="liberation-card pendente">
                    <h4><i class="fas fa-clock"></i> Aguardando Liberação</h4>
                    <div class="liberation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="pendenteCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="pendenteValor">R$ 0</div>
                            <div class="stat-label">Valor</div>
                        </div>
                    </div>
                    <button class="btn btn-warning btn-full" onclick="filtrarStatus('PENDENTE')">
                        <i class="fas fa-eye"></i> Ver Pendentes
                    </button>
                </div>

                <div class="liberation-card aprovado">
                    <h4><i class="fas fa-check-circle"></i> Aprovados</h4>
                    <div class="liberation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="aprovadoCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="aprovadoValor">R$ 0</div>
                            <div class="stat-label">Valor</div>
                        </div>
                    </div>
                    <button class="btn btn-success btn-full" onclick="filtrarStatus('APROVADO')">
                        <i class="fas fa-eye"></i> Ver Aprovados
                    </button>
                </div>

                <div class="liberation-card liberado">
                    <h4><i class="fas fa-shipping-fast"></i> Liberados</h4>
                    <div class="liberation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="liberadoCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="liberadoValor">R$ 0</div>
                            <div class="stat-label">Valor</div>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" onclick="filtrarStatus('LIBERADO')">
                        <i class="fas fa-eye"></i> Ver Liberados
                    </button>
                </div>

                <div class="liberation-card rejeitado">
                    <h4><i class="fas fa-times-circle"></i> Rejeitados</h4>
                    <div class="liberation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="rejeitadoCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="rejeitadoValor">R$ 0</div>
                            <div class="stat-label">Valor</div>
                        </div>
                    </div>
                    <button class="btn btn-danger btn-full" onclick="filtrarStatus('REJEITADO')">
                        <i class="fas fa-eye"></i> Ver Rejeitados
                    </button>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter">
                        <option value="">Todos os Status</option>
                        <option value="PENDENTE">Aguardando Liberação</option>
                        <option value="APROVADO">Aprovado</option>
                        <option value="LIBERADO">Liberado</option>
                        <option value="REJEITADO">Rejeitado</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="prioridadeFilter">Prioridade</label>
                    <select id="prioridadeFilter">
                        <option value="">Todas as Prioridades</option>
                        <option value="ALTA">Alta</option>
                        <option value="MEDIA">Média</option>
                        <option value="BAIXA">Baixa</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dataInicio">Data Início</label>
                    <input type="date" id="dataInicio">
                </div>
                <div class="filter-group">
                    <label for="dataFim">Data Fim</label>
                    <input type="date" id="dataFim">
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações em Lote -->
            <div class="bulk-actions" id="bulkActions">
                <strong>Ações em Lote:</strong>
                <button class="btn btn-success" onclick="liberarSelecionados()">
                    <i class="fas fa-check"></i> Liberar Selecionados
                </button>
                <button class="btn btn-danger" onclick="rejeitarSelecionados()">
                    <i class="fas fa-times"></i> Rejeitar Selecionados
                </button>
                <button class="btn btn-secondary" onclick="limparSelecao()">
                    <i class="fas fa-times"></i> Limpar Seleção
                </button>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="liberacaoRapida()">
                    <i class="fas fa-bolt"></i> Liberação Rápida
                </button>
                <button class="btn btn-primary" onclick="relatorioLiberacao()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-warning" onclick="exportarLiberacoes()">
                    <i class="fas fa-file-excel"></i> Exportar
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando liberações...</p>
            </div>

            <!-- Tabela de Liberações -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Código</th>
                            <th>Produto</th>
                            <th>Lote</th>
                            <th>Quantidade</th>
                            <th>Prioridade</th>
                            <th>Data Inspeção</th>
                            <th>Status</th>
                            <th>Responsável</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-check-circle"></i>
                <h3>Nenhuma liberação encontrada</h3>
                <p>Não há itens aguardando liberação no momento.</p>
                <button class="btn btn-primary" onclick="sincronizarInspecoes()" style="margin-top: 20px;">
                    <i class="fas fa-sync"></i> Sincronizar Inspeções
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy, updateDoc, doc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let liberacoes = [];
        let selectedItems = new Set();

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);
                
                // Carregar liberações (itens aprovados na inspeção)
                const liberacoesSnap = await getDocs(collection(db, "liberacoesQualidade"));
                liberacoes = liberacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ003 - Dados carregados:', {
                    liberacoes: liberacoes.length
                });

                updateStats();
                renderTable();
                showLoading(false);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ003:', error);
                showLoading(false);
                showError('Erro ao carregar dados de liberação');
            }
        }

        function updateStats() {
            const stats = {
                pendente: { count: 0, valor: 0 },
                aprovado: { count: 0, valor: 0 },
                liberado: { count: 0, valor: 0 },
                rejeitado: { count: 0, valor: 0 }
            };

            liberacoes.forEach(item => {
                const status = item.status?.toLowerCase() || 'pendente';
                const valor = (item.quantidade || 0) * (item.valorUnitario || 0);
                
                if (stats[status]) {
                    stats[status].count++;
                    stats[status].valor += valor;
                }
            });

            // Atualizar contadores
            Object.keys(stats).forEach(status => {
                const countEl = document.getElementById(`${status}Count`);
                const valorEl = document.getElementById(`${status}Valor`);
                
                if (countEl) countEl.textContent = stats[status].count;
                if (valorEl) valorEl.textContent = `R$ ${stats[status].valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
            });
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (liberacoes.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = liberacoes.map(item => {
                const dataInspecao = item.dataInspecao ? 
                    new Date(item.dataInspecao.seconds * 1000).toLocaleDateString() : 'N/A';
                
                const prioridade = item.prioridade || 'MEDIA';
                const priorityClass = `priority-${prioridade.toLowerCase()}`;

                return `
                    <tr>
                        <td class="checkbox-column">
                            <input type="checkbox" value="${item.id}" onchange="toggleItemSelection('${item.id}')">
                        </td>
                        <td><strong>${item.codigo || 'N/A'}</strong></td>
                        <td>${item.produtoNome || 'N/A'}</td>
                        <td>${item.lote || 'N/A'}</td>
                        <td>${item.quantidade || 0} ${item.unidade || ''}</td>
                        <td>
                            <span class="priority-indicator ${priorityClass}"></span>
                            ${prioridade}
                        </td>
                        <td>${dataInspecao}</td>
                        <td><span class="status-badge status-${item.status?.toLowerCase() || 'pendente'}">${item.status || 'PENDENTE'}</span></td>
                        <td>${item.responsavel || 'N/A'}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarItem('${item.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${item.status === 'APROVADO' ? `
                                    <button class="btn btn-success btn-action" onclick="liberarItem('${item.id}')" title="Liberar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-danger btn-action" onclick="rejeitarItem('${item.id}')" title="Rejeitar">
                                        <i class="fas fa-times"></i>
                                    </button>
                                ` : ''}
                                ${item.status === 'LIBERADO' ? `
                                    <button class="btn btn-warning btn-action" onclick="rastrearItem('${item.id}')" title="Rastrear">
                                        <i class="fas fa-search"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        function toggleItemSelection(itemId) {
            if (selectedItems.has(itemId)) {
                selectedItems.delete(itemId);
            } else {
                selectedItems.add(itemId);
            }
            
            document.getElementById('bulkActions').classList.toggle('active', selectedItems.size > 0);
        }

        // Funções globais
        window.filtrarStatus = function(status) {
            document.getElementById('statusFilter').value = status;
            aplicarFiltros();
        };

        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar filtros
        };

        window.toggleSelectAll = function() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedItems.add(cb.value);
                } else {
                    selectedItems.delete(cb.value);
                }
            });
            
            document.getElementById('bulkActions').classList.toggle('active', selectedItems.size > 0);
        };

        window.liberacaoRapida = function() {
            alert('⚡ Funcionalidade em desenvolvimento: Liberação Rápida');
        };

        window.relatorioLiberacao = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório de Liberação');
        };

        window.exportarLiberacoes = function() {
            alert('📁 Funcionalidade em desenvolvimento: Exportar Liberações');
        };

        window.sincronizarInspecoes = function() {
            alert('🔄 Funcionalidade em desenvolvimento: Sincronizar Inspeções');
        };

        window.liberarSelecionados = function() {
            if (selectedItems.size === 0) {
                alert('⚠️ Selecione pelo menos um item para liberar');
                return;
            }
            alert(`✅ Funcionalidade em desenvolvimento: Liberar ${selectedItems.size} itens selecionados`);
        };

        window.rejeitarSelecionados = function() {
            if (selectedItems.size === 0) {
                alert('⚠️ Selecione pelo menos um item para rejeitar');
                return;
            }
            alert(`❌ Funcionalidade em desenvolvimento: Rejeitar ${selectedItems.size} itens selecionados`);
        };

        window.limparSelecao = function() {
            selectedItems.clear();
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
            document.getElementById('bulkActions').classList.remove('active');
        };

        window.visualizarItem = function(id) {
            alert('👁️ Funcionalidade em desenvolvimento: Visualizar Item ' + id);
        };

        window.liberarItem = function(id) {
            alert('✅ Funcionalidade em desenvolvimento: Liberar Item ' + id);
        };

        window.rejeitarItem = function(id) {
            alert('❌ Funcionalidade em desenvolvimento: Rejeitar Item ' + id);
        };

        window.rastrearItem = function(id) {
            alert('🔍 Funcionalidade em desenvolvimento: Rastrear Item ' + id);
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
