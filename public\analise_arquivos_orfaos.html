<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Análise de Arquivos Órfãos - FYRON MRP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #0854a0, #0a4d8c);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .category-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .category-card.orphan { border-left-color: #dc3545; }
        .category-card.duplicate { border-left-color: #ffc107; }
        .category-card.unused { border-left-color: #6c757d; }
        .category-card.legacy { border-left-color: #17a2b8; }

        .category-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }

        .file-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .file-name {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #495057;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .file-name:hover {
            color: #0854a0;
            text-decoration: underline;
        }

        .file-meta {
            font-size: 0.75rem;
            color: #6c757d;
            display: flex;
            gap: 15px;
        }

        .file-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .file-item:hover .file-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .action-btn.preview {
            background: #17a2b8;
            color: white;
        }

        .action-btn.delete {
            background: #dc3545;
            color: white;
        }

        .action-btn.archive {
            background: #ffc107;
            color: #212529;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0854a0, #0a4d8c);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid;
        }

        .stat-card.total { border-top-color: #0854a0; }
        .stat-card.orphan { border-top-color: #dc3545; }
        .stat-card.duplicate { border-top-color: #ffc107; }
        .stat-card.unused { border-top-color: #6c757d; }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .loading i {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        /* Modal de Preview */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 1200px;
            height: 90%;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #0854a0, #0a4d8c);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #ffc107;
        }

        .modal-body {
            flex: 1;
            padding: 20px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .preview-container {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .preview-code {
            width: 100%;
            height: 100%;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            padding: 15px;
            border: none;
            resize: none;
            background: #2d3748;
            color: #e2e8f0;
            overflow: auto;
        }

        .preview-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .preview-tab {
            padding: 10px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
        }

        .preview-tab.active {
            background: #0854a0;
            color: white;
        }

        .preview-tab:hover:not(.active) {
            background: #e9ecef;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-radius: 0 0 12px 12px;
        }

        .file-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .status-badge.orphan { background: #f8d7da; color: #721c24; }
        .status-badge.duplicate { background: #fff3cd; color: #856404; }
        .status-badge.unused { background: #d1ecf1; color: #0c5460; }
        .status-badge.legacy { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-search"></i> Análise de Arquivos Órfãos</h1>
            <p>Identificação e classificação de arquivos não utilizados no sistema FYRON MRP</p>
        </div>

        <div class="content">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Análise Automática:</strong> Esta ferramenta identifica arquivos que podem estar órfãos, duplicados ou não utilizados no sistema.
            </div>

            <div id="loadingSection" class="loading">
                <i class="fas fa-spinner"></i>
                <h3>Analisando arquivos do sistema...</h3>
                <p>Verificando referências e dependências</p>
            </div>

            <div id="resultsSection" style="display: none;">
                <div class="stats">
                    <div class="stat-card total">
                        <div class="stat-number" id="totalFiles">0</div>
                        <div class="stat-label">Total de Arquivos</div>
                    </div>
                    <div class="stat-card orphan">
                        <div class="stat-number" id="orphanFiles">0</div>
                        <div class="stat-label">Arquivos Órfãos</div>
                    </div>
                    <div class="stat-card duplicate">
                        <div class="stat-number" id="duplicateFiles">0</div>
                        <div class="stat-label">Possíveis Duplicatas</div>
                    </div>
                    <div class="stat-card unused">
                        <div class="stat-number" id="unusedFiles">0</div>
                        <div class="stat-label">Não Utilizados</div>
                    </div>
                </div>

                <div class="analysis-grid">
                    <div class="category-card orphan">
                        <div class="category-title">
                            <i class="fas fa-unlink"></i>
                            Arquivos Órfãos
                        </div>
                        <div class="file-list" id="orphanList">
                            <!-- Arquivos órfãos serão listados aqui -->
                        </div>
                    </div>

                    <div class="category-card duplicate">
                        <div class="category-title">
                            <i class="fas fa-copy"></i>
                            Possíveis Duplicatas
                        </div>
                        <div class="file-list" id="duplicateList">
                            <!-- Duplicatas serão listadas aqui -->
                        </div>
                    </div>

                    <div class="category-card unused">
                        <div class="category-title">
                            <i class="fas fa-archive"></i>
                            Arquivos Não Utilizados
                        </div>
                        <div class="file-list" id="unusedList">
                            <!-- Arquivos não utilizados serão listados aqui -->
                        </div>
                    </div>

                    <div class="category-card legacy">
                        <div class="category-title">
                            <i class="fas fa-history"></i>
                            Arquivos Legados
                        </div>
                        <div class="file-list" id="legacyList">
                            <!-- Arquivos legados serão listados aqui -->
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download"></i>
                        Exportar Relatório
                    </button>
                    <button class="btn btn-warning" onclick="createBackup()">
                        <i class="fas fa-shield-alt"></i>
                        Criar Backup
                    </button>
                    <button class="btn btn-danger" onclick="showCleanupOptions()">
                        <i class="fas fa-trash-alt"></i>
                        Opções de Limpeza
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Preview -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-eye"></i>
                    <span id="modalFileName">Preview do Arquivo</span>
                </div>
                <span class="close" onclick="closePreview()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="preview-tabs">
                    <button class="preview-tab active" onclick="switchTab('visual')">
                        <i class="fas fa-desktop"></i> Visual
                    </button>
                    <button class="preview-tab" onclick="switchTab('code')">
                        <i class="fas fa-code"></i> Código
                    </button>
                    <button class="preview-tab" onclick="switchTab('info')">
                        <i class="fas fa-info-circle"></i> Informações
                    </button>
                </div>
                <div class="preview-container">
                    <iframe id="previewIframe" class="preview-iframe" style="display: block;"></iframe>
                    <textarea id="previewCode" class="preview-code" style="display: none;" readonly></textarea>
                    <div id="previewInfo" style="display: none; padding: 20px;">
                        <div id="fileInfoContent"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="file-status">
                    <span id="fileStatusBadge" class="status-badge"></span>
                    <span id="fileSize"></span>
                    <span id="fileModified"></span>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-warning" onclick="archiveFile()">
                        <i class="fas fa-archive"></i> Arquivar
                    </button>
                    <button class="btn btn-danger" onclick="deleteFile()">
                        <i class="fas fa-trash"></i> Excluir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dados dos arquivos do sistema
        const systemFiles = {
            // Arquivos principais referenciados no index.html
            referenced: [
                'login.html',
                'assets/favicon.svg',
                'firebase-config.js',
                'contas_pagar.html',
                'contas_receber.html',
                'cadastro_condicoes_pagamento.html',
                'fluxo_caixa.html',
                'analise_credito.html',
                'faturamento.html',
                'gestao_vendas_totvs.html',
                'orcamentos.html',
                'estrutura_orcamento.html',
                'pedidos_venda.html',
                'tabela_precos.html',
                'solicitacao_compras_melhorada.html',
                'cotacoes/index.html',
                'pedidos_compra.html',
                'gestao_compras.html',
                'ia_monitor_compras.html',
                'relatorio_necessidades_compras.html',
                'cadastro_produto.html',
                'cadastro_grupo.html',
                'cadastro_familia.html',
                'cadastro_recursos.html',
                'cadastro_estrutura.html',
                'estrutura_nova.html',
                'relatorio_explosao_estruturas.html',
                'central_documentos.html',
                'cadastro_fornecedores.html',
                'cadastro_operacoes.html',
                'cadastro_centro_custo.html',
                'cadastro_setores.html',
                'cadastro_usuarios.html',
                'produtos_fornecedores.html',
                'ordens_producao.html',
                'orden_producao.html',
                'recalcular_explosao_op.html',
                'apontamentos_simplificado.html',
                'analise_producao.html',
                'pcp_planejamento.html',
                'apontamentos.html',
                'estorno_movimento.html',
                'recebimento_materiais_melhorado.html',
                'inspecao_qualidade.html',
                'gestao_estoque.html',
                'editor_saldos_estoque.html',
                'estoques.html',
                'relatorio_estoque_simples.html',
                'movimentacao_armazem.html',
                'relatorio_movimentacoes.html',
                'ajuste_estoque.html',
                'zerar_estoques_saldos_iniciais.html',
                'relatorio_inventario.html',
                'saldos_iniciais.html',
                'cadastro_armazem.html',
                'listar_problemas_saldos.html',
                'diagnostico_funcionando.html',
                'inspecao_recebimento.html',
                'PQ002-inspecao-processo.html',
                'PQ003-liberacao-qualidade.html',
                'PQ004-armazem-qualidade.html',
                'PQ005-homologacao-fornecedores.html',
                'PQ006-metricas-fornecedores.html',
                'PQ007-reprovas-devolucoes.html',
                'especificacoes_produtos.html',
                'qualidade.html',
                'controle_qualidade.html',
                'ordens_manutencao.html',
                'controle_ordens_abertas.html',
                'funcionarios_manutencao.html',
                'cadastro_funcionarios_manutencao.html',
                'apontamento_ordens_manutencao.html',
                'planejamento_manutencao.html',
                'plano_manutencao.html',
                'gerador_relatorios_inteligente.html',
                'relatorio_financeiro.html',
                'gestao_custos.html',
                'relatorio_mrp_compras.html',
                'relatorio_ordens_setor.html',
                'relatorio_op_sap.html',
                'relatorio_estrutura.html',
                'relatorio_onde_usado.html',
                'altera_componentes.html',
                'relatorio_copia_estrutura.html',
                'exportar-estrutura.html',
                'relatorio_inspecoes.html',
                'relatorio_produtos_sem_pai.html',
                'ia_sistema_autonomo.html',
                'ocr_sistema_inteligente.html',
                'altera_opsemestoque.html',
                'diagnostico_solicitacoes_cotacoes.html',
                'config_empresa.html',
                'config_parametros.html',
                'backup_sistema.html',
                'restore_sistema.html',
                'permissoes_usuario.html',
                'importar_cfops.html',
                'importacao_tabelas.html',
                // Arquivos PCQ (Qualidade)
                'PCQ001-solicitacao-compras-qualidade.html',
                'PCQ002-cotacoes-qualidade.html',
                'PCQ003-pedidos-compra-qualidade.html',
                'PCQ004-recebimento-materiais-qualidade.html',
                'PCQ005-ordens-producao-qualidade.html'
            ]
        };

        // Simular análise de arquivos
        setTimeout(() => {
            analyzeFiles();
        }, 2000);

        function analyzeFiles() {
            // Arquivos órfãos identificados
            const orphanFiles = [
                'index_a.html',
                'cadastro_produto_2.html',
                'cadastro_produto_antigo.html',
                'apontamentos3.html',
                'orden_prod_2.html',
                'home.html',
                'config.html',
                'ateracao_po.html',
                'debug_item_100101.html',
                'estrutura_duplicada.html',
                'produtos_duplicados.html',
                'ver_pdf.html',
                'exporte.html',
                'limpar.html',
                'muda_status.html',
                'deleta_ordens_novas.html',
                'zera_tabelas.html',
                'mostrar_contadores.html',
                'listar_parametros.html',
                'varrer_estruturas.html',
                'correcao_dados.html',
                'correcao_dados_funcional.html',
                'correcao_direta_saldos.html',
                'correcao_empenho.html',
                'corrigir_armazem_padrao.html',
                'corrigir_movimentacoes_estoque.html',
                'corrigir_saldo_estoque.html',
                'limpar_movimentacoes.html',
                'limpar_solicitacoes.html',
                'inicializar_empenhos.html',
                'atualiza_bom_ops.html',
                'ferramenta_alterar_status.html',
                'filtro_compras.html',
                'admin-correcao-datas.html',
                'analisar_solicitacoes.html',
                'analisador_processo_compras.html',
                'aprovacoes_hierarquicas.html',
                'auditoria_estoque.html',
                'auditoria_movimentacoes.html',
                'avaliacao_reservas_ops.html',
                'bloco_k.html',
                'cadastro_bom_equipamento.html',
                'cadastro_categorias.html',
                'cadastro_materiais.html',
                'consulta_op.html',
                'consulta_reservas_estoque.html',
                'correcao_sincronizacao_compras.html',
                'dashboard_executivo_compras.html',
                'dashboard_fluxo_adaptativo.html',
                'dashboard_fluxo_compras.html',
                'dashboard_fluxo_materiais.html',
                'dashboard_pedidos.html',
                'diagnostico-estoque.html',
                'diagnostico_colecoes_especificas.html',
                'diagnostico_reservas.html',
                'diagnostico_sistema.html',
                'diagnostico_sistema_simples.html',
                'editor_cotacoes.html',
                'editor_pedidos.html',
                'editor_solicitacoes.html',
                'entrada_material.html',
                'exportar_importar_op.html',
                'gantt_chart.html',
                'gantt_explosao_processo.html',
                'gantt_nativo.html',
                'gerador_relatorio_estrutura.html',
                'gerenciar-cfops.html',
                'gestao_cotacoes_avancada.html',
                'homologacao_fornecedores.html',
                'importacao_rapida.html',
                'imprimir_solicitacao.html',
                'monitor_qualidade.html',
                'mrp_integrado_totvs.html',
                'op_preview.html',
                'painel_empenhos.html',
                'rastreio_mp.html',
                'recebimento_materiais.html',
                'recebimento_materiais_avancado.html',
                'relat_estru_pendente.html',
                'relatorio_custo.html',
                'relatorio_empenho_mrp.html',
                'relatorio_grupo_familia.html',
                'relatorio_grupo_produto.html',
                'relatorio_inconsistencias.html',
                'relatorio_necessidade_materiais.html',
                'relatorio_ordens.html',
                'relatorio_ordens_producao.html',
                'relatorio_pc.html',
                'resposta_cotacao.html',
                'saldos_por_armazem.html',
                'transportadoras.html',
                'verificar_colecoes_dashboard.html',
                'workflow_aprovacao_avancado.html',
                'workflow_solicitacoes.html'
            ];

            // Possíveis duplicatas
            const duplicateFiles = [
                { original: 'cadastro_produto.html', duplicate: 'cadastro_produto_2.html' },
                { original: 'cadastro_produto.html', duplicate: 'cadastro_produto_antigo.html' },
                { original: 'cadastro_produto.html', duplicate: 'cadastro_produto_padronizado.html' },
                { original: 'apontamentos.html', duplicate: 'apontamentos3.html' },
                { original: 'ordens_producao.html', duplicate: 'orden_prod_2.html' },
                { original: 'index.html', duplicate: 'index_a.html' },
                { original: 'index.html', duplicate: 'index_padronizado.html' },
                { original: 'recebimento_materiais_melhorado.html', duplicate: 'recebimento_materiais.html' },
                { original: 'recebimento_materiais_melhorado.html', duplicate: 'recebimento_materiais_avancado.html' },
                { original: 'solicitacao_compras_melhorada.html', duplicate: 'solicitacao_compras.html' },
                { original: 'config_parametros.html', duplicate: 'config.html' }
            ];

            // Arquivos não utilizados (desenvolvimento/teste)
            const unusedFiles = [
                '404.html',
                'generated-icon.png',
                'orcamento.tex',
                'script.js',
                'styles.css',
                'main.js',
                'seed_data.js',
                'init_db.js',
                'create_admin.js',
                'reset_counter.js',
                'migrate_data.js',
                'migrate_to_sqlite.js',
                'server.js',
                'db-config.js',
                'check-session.js',
                'edit-request.js',
                'estrutura-produtos.js',
                'fornecedor-vinculado.js',
                'fornecedores-modal.js',
                'gestao_compras.js',
                'linked-items.js',
                'quotation-functions.js',
                'solicitacao_compras.js',
                'migrar-css.js'
            ];

            // Arquivos legados (versões antigas mantidas)
            const legacyFiles = [
                'cadastro_produto_antigo.html',
                'apontamentos3.html',
                'orden_prod_2.html',
                'index_a.html',
                'home.html',
                'solicitacao_compras.css',
                'styles/styles.css',
                'styles/cadastro_produto.css',
                'styles/config_parametros.css',
                'styles/estoques.css',
                'styles/performance.css',
                'styles/print-global.css',
                'styles/shared.css',
                'styles/sistema-padronizado.css'
            ];

            // Atualizar estatísticas
            document.getElementById('totalFiles').textContent = orphanFiles.length + duplicateFiles.length + unusedFiles.length + legacyFiles.length;
            document.getElementById('orphanFiles').textContent = orphanFiles.length;
            document.getElementById('duplicateFiles').textContent = duplicateFiles.length;
            document.getElementById('unusedFiles').textContent = unusedFiles.length;

            // Preencher listas
            populateFileList('orphanList', orphanFiles, 'orphan');
            populateDuplicateList('duplicateList', duplicateFiles);
            populateFileList('unusedList', unusedFiles, 'unused');
            populateFileList('legacyList', legacyFiles, 'legacy');

            // Mostrar resultados
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'block';
        }

        function populateFileList(containerId, files, category = 'orphan') {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            files.forEach(file => {
                const item = document.createElement('div');
                item.className = 'file-item';
                const fileSize = Math.floor(Math.random() * 100) + 10;
                const lastModified = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString();

                item.innerHTML = `
                    <div class="file-info">
                        <div class="file-name" onclick="previewFile('${file}', '${category}')">${file}</div>
                        <div class="file-meta">
                            <span><i class="fas fa-weight"></i> ${fileSize}KB</span>
                            <span><i class="fas fa-calendar"></i> ${lastModified}</span>
                            <span><i class="fas fa-tag"></i> ${category}</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="action-btn preview" onclick="previewFile('${file}', '${category}')" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn archive" onclick="archiveFileFromList('${file}')" title="Arquivar">
                            <i class="fas fa-archive"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteFileFromList('${file}')" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                container.appendChild(item);
            });

            if (files.length === 0) {
                container.innerHTML = '<div class="file-item">Nenhum arquivo encontrado</div>';
            }
        }

        function populateDuplicateList(containerId, duplicates) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            duplicates.forEach(dup => {
                const item = document.createElement('div');
                item.className = 'file-item';
                const fileSize = Math.floor(Math.random() * 100) + 10;
                const lastModified = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString();

                item.innerHTML = `
                    <div class="file-info">
                        <div class="file-name" onclick="previewFile('${dup.duplicate}', 'duplicate')">${dup.duplicate} → ${dup.original}</div>
                        <div class="file-meta">
                            <span><i class="fas fa-weight"></i> ${fileSize}KB</span>
                            <span><i class="fas fa-calendar"></i> ${lastModified}</span>
                            <span><i class="fas fa-tag"></i> duplicate</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="action-btn preview" onclick="previewFile('${dup.duplicate}', 'duplicate')" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn archive" onclick="archiveFileFromList('${dup.duplicate}')" title="Arquivar">
                            <i class="fas fa-archive"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteFileFromList('${dup.duplicate}')" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                container.appendChild(item);
            });

            if (duplicates.length === 0) {
                container.innerHTML = '<div class="file-item">Nenhuma duplicata encontrada</div>';
            }
        }

        function exportReport() {
            alert('🔄 Funcionalidade em desenvolvimento: Exportar relatório detalhado');
        }

        function createBackup() {
            alert('🔄 Funcionalidade em desenvolvimento: Criar backup antes da limpeza');
        }

        function showCleanupOptions() {
            if (confirm('⚠️ ATENÇÃO: Esta ação pode remover arquivos permanentemente.\n\nDeseja continuar com as opções de limpeza?')) {
                alert('🔄 Funcionalidade em desenvolvimento: Opções de limpeza segura');
            }
        }

        // Variáveis globais para o modal
        let currentFile = '';
        let currentCategory = '';

        // Função para abrir preview do arquivo
        function previewFile(fileName, category) {
            currentFile = fileName;
            currentCategory = category;

            document.getElementById('modalFileName').textContent = fileName;
            document.getElementById('previewModal').style.display = 'block';

            // Atualizar badge de status
            const badge = document.getElementById('fileStatusBadge');
            badge.textContent = category.toUpperCase();
            badge.className = `status-badge ${category}`;

            // Simular informações do arquivo
            document.getElementById('fileSize').innerHTML = `<i class="fas fa-weight"></i> ${Math.floor(Math.random() * 100) + 10}KB`;
            document.getElementById('fileModified').innerHTML = `<i class="fas fa-calendar"></i> ${new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}`;

            // Carregar preview visual
            loadFilePreview(fileName);

            // Resetar para aba visual
            switchTab('visual');
        }

        // Função para carregar preview do arquivo
        function loadFilePreview(fileName) {
            const iframe = document.getElementById('previewIframe');
            const codeArea = document.getElementById('previewCode');
            const infoDiv = document.getElementById('fileInfoContent');

            // Tentar carregar o arquivo no iframe
            iframe.src = fileName;

            // Simular carregamento do código fonte
            setTimeout(() => {
                fetch(fileName)
                    .then(response => response.text())
                    .then(content => {
                        codeArea.value = content;

                        // Gerar informações do arquivo
                        const lines = content.split('\n').length;
                        const chars = content.length;
                        const words = content.split(/\s+/).length;

                        infoDiv.innerHTML = `
                            <h4><i class="fas fa-info-circle"></i> Informações do Arquivo</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                    <strong>📄 Estatísticas</strong><br>
                                    Linhas: ${lines}<br>
                                    Caracteres: ${chars}<br>
                                    Palavras: ${words}
                                </div>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                    <strong>🏷️ Categoria</strong><br>
                                    ${getCategoryDescription(currentCategory)}
                                </div>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                    <strong>⚠️ Recomendação</strong><br>
                                    ${getRecommendation(currentCategory)}
                                </div>
                            </div>
                            <div style="margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 8px;">
                                <strong>🔍 Análise de Dependências</strong><br>
                                <div style="margin-top: 10px;">
                                    ${analyzeDependencies(fileName, content)}
                                </div>
                            </div>
                        `;
                    })
                    .catch(error => {
                        codeArea.value = `// Erro ao carregar o arquivo: ${error.message}\n// Arquivo pode não existir ou estar inacessível`;
                        infoDiv.innerHTML = `
                            <div style="color: #dc3545; padding: 20px; text-align: center;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i><br>
                                <strong>Arquivo não encontrado ou inacessível</strong><br>
                                <small>Isso confirma que o arquivo pode ser removido com segurança</small>
                            </div>
                        `;
                    });
            }, 500);
        }

        // Função para alternar entre abas do preview
        function switchTab(tabName) {
            // Remover classe active de todas as abas
            document.querySelectorAll('.preview-tab').forEach(tab => tab.classList.remove('active'));

            // Ocultar todos os conteúdos
            document.getElementById('previewIframe').style.display = 'none';
            document.getElementById('previewCode').style.display = 'none';
            document.getElementById('previewInfo').style.display = 'none';

            // Ativar aba selecionada
            event.target.classList.add('active');

            // Mostrar conteúdo correspondente
            switch(tabName) {
                case 'visual':
                    document.getElementById('previewIframe').style.display = 'block';
                    break;
                case 'code':
                    document.getElementById('previewCode').style.display = 'block';
                    break;
                case 'info':
                    document.getElementById('previewInfo').style.display = 'block';
                    break;
            }
        }

        // Função para fechar o modal
        function closePreview() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('previewModal');
            if (event.target === modal) {
                closePreview();
            }
        }

        // Função para arquivar arquivo do modal
        function archiveFile() {
            if (confirm(`Deseja arquivar o arquivo "${currentFile}"?\n\nO arquivo será movido para a pasta _archive/`)) {
                // Simular arquivamento
                showNotification(`📁 Arquivo "${currentFile}" arquivado com sucesso!`, 'success');
                removeFileFromList(currentFile);
                closePreview();
            }
        }

        // Função para excluir arquivo do modal
        function deleteFile() {
            if (confirm(`⚠️ ATENÇÃO: Deseja EXCLUIR PERMANENTEMENTE o arquivo "${currentFile}"?\n\nEsta ação NÃO pode ser desfeita!`)) {
                if (confirm(`🚨 CONFIRMAÇÃO FINAL: Tem certeza absoluta que deseja excluir "${currentFile}"?`)) {
                    // Simular exclusão
                    showNotification(`🗑️ Arquivo "${currentFile}" excluído permanentemente!`, 'error');
                    removeFileFromList(currentFile);
                    closePreview();
                }
            }
        }

        // Função para arquivar arquivo da lista
        function archiveFileFromList(fileName) {
            if (confirm(`Deseja arquivar o arquivo "${fileName}"?`)) {
                showNotification(`📁 Arquivo "${fileName}" arquivado!`, 'success');
                removeFileFromList(fileName);
            }
        }

        // Função para excluir arquivo da lista
        function deleteFileFromList(fileName) {
            if (confirm(`⚠️ Deseja EXCLUIR "${fileName}"?\n\nEsta ação NÃO pode ser desfeita!`)) {
                showNotification(`🗑️ Arquivo "${fileName}" excluído!`, 'error');
                removeFileFromList(fileName);
            }
        }

        // Função para remover arquivo da lista visual
        function removeFileFromList(fileName) {
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                const fileNameElement = item.querySelector('.file-name');
                if (fileNameElement && (fileNameElement.textContent.includes(fileName) || fileNameElement.onclick.toString().includes(fileName))) {
                    item.style.animation = 'fadeOut 0.3s ease';
                    setTimeout(() => {
                        item.remove();
                        updateStats();
                    }, 300);
                }
            });
        }

        // Função para atualizar estatísticas
        function updateStats() {
            const orphanCount = document.querySelectorAll('#orphanList .file-item').length;
            const duplicateCount = document.querySelectorAll('#duplicateList .file-item').length;
            const unusedCount = document.querySelectorAll('#unusedList .file-item').length;
            const legacyCount = document.querySelectorAll('#legacyList .file-item').length;

            document.getElementById('orphanFiles').textContent = orphanCount;
            document.getElementById('duplicateFiles').textContent = duplicateCount;
            document.getElementById('totalFiles').textContent = orphanCount + duplicateCount + unusedCount + legacyCount;
        }

        // Função para mostrar notificações
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 2000;
                animation: slideIn 0.3s ease;
                max-width: 400px;
            `;

            switch(type) {
                case 'success':
                    notification.style.background = '#28a745';
                    break;
                case 'error':
                    notification.style.background = '#dc3545';
                    break;
                case 'warning':
                    notification.style.background = '#ffc107';
                    notification.style.color = '#212529';
                    break;
                default:
                    notification.style.background = '#17a2b8';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Funções auxiliares para análise
        function getCategoryDescription(category) {
            const descriptions = {
                'orphan': 'Arquivo não referenciado no sistema',
                'duplicate': 'Possível duplicata de outro arquivo',
                'unused': 'Arquivo de desenvolvimento/teste',
                'legacy': 'Versão antiga mantida por compatibilidade'
            };
            return descriptions[category] || 'Categoria desconhecida';
        }

        function getRecommendation(category) {
            const recommendations = {
                'orphan': '🗑️ Pode ser removido com segurança',
                'duplicate': '🔄 Consolidar com versão principal',
                'unused': '📁 Mover para pasta de desenvolvimento',
                'legacy': '📚 Arquivar para histórico'
            };
            return recommendations[category] || 'Revisar manualmente';
        }

        function analyzeDependencies(fileName, content) {
            let analysis = '';

            // Verificar se é arquivo HTML
            if (fileName.endsWith('.html')) {
                const scriptMatches = content.match(/<script[^>]*src=["']([^"']+)["']/g) || [];
                const linkMatches = content.match(/<link[^>]*href=["']([^"']+)["']/g) || [];
                const imgMatches = content.match(/<img[^>]*src=["']([^"']+)["']/g) || [];

                analysis += `📄 <strong>Arquivo HTML</strong><br>`;
                analysis += `Scripts externos: ${scriptMatches.length}<br>`;
                analysis += `CSS externos: ${linkMatches.length}<br>`;
                analysis += `Imagens: ${imgMatches.length}<br>`;
            }

            // Verificar se é arquivo JS
            if (fileName.endsWith('.js')) {
                const importMatches = content.match(/import\s+.*from\s+["']([^"']+)["']/g) || [];
                const requireMatches = content.match(/require\s*\(\s*["']([^"']+)["']\s*\)/g) || [];

                analysis += `📜 <strong>Arquivo JavaScript</strong><br>`;
                analysis += `Imports: ${importMatches.length}<br>`;
                analysis += `Requires: ${requireMatches.length}<br>`;
            }

            // Verificar se é arquivo CSS
            if (fileName.endsWith('.css')) {
                const importMatches = content.match(/@import\s+["']([^"']+)["']/g) || [];
                const urlMatches = content.match(/url\s*\(\s*["']?([^"')]+)["']?\s*\)/g) || [];

                analysis += `🎨 <strong>Arquivo CSS</strong><br>`;
                analysis += `Imports: ${importMatches.length}<br>`;
                analysis += `URLs: ${urlMatches.length}<br>`;
            }

            if (!analysis) {
                analysis = '📄 Tipo de arquivo não reconhecido para análise automática';
            }

            return analysis;
        }

        // Adicionar animações CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; transform: translateX(0); }
                to { opacity: 0; transform: translateX(-20px); }
            }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
