<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Verificação de Coleções para Dashboard</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .verification-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .collection-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .collection-found {
            border-left: 4px solid #28a745;
        }
        .collection-not-found {
            border-left: 4px solid #dc3545;
        }
        .collection-empty {
            border-left: 4px solid #ffc107;
        }
        .data-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .btn-verify {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-fix {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .summary-number {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .found { color: #28a745; }
        .not-found { color: #dc3545; }
        .empty { color: #ffc107; }
    </style>
</head>
<body>
    <div class="verification-container">
        <h1>🔍 Verificação de Coleções para Dashboard</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="verificarTodasColecoes()" class="btn-verify">🔍 Verificar Todas as Coleções</button>
            <button onclick="gerarDashboardCorrigido()" class="btn-fix" id="btnGerar" disabled>🔧 Gerar Dashboard Corrigido</button>
        </div>

        <div id="summarySection"></div>
        <div id="collectionsResults"></div>
        <div id="recommendationsSection"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let collectionsData = {};

        // Coleções que o dashboard precisa verificar
        const colecoesPrevistas = [
            { nome: 'solicitacoesCompra', descricao: 'Solicitações de Compra', essencial: true },
            { nome: 'cotacoes', descricao: 'Cotações', essencial: false },
            { nome: 'pedidosCompra', descricao: 'Pedidos de Compra', essencial: false },
            { nome: 'recebimentosDetalhes', descricao: 'Detalhes de Recebimentos', essencial: true },
            { nome: 'recebimento_materiais', descricao: 'Recebimento de Materiais (alternativo)', essencial: false },
            { nome: 'estoqueQualidade', descricao: 'Estoque em Qualidade', essencial: false },
            { nome: 'estoques', descricao: 'Estoques', essencial: true },
            { nome: 'movimentacoesEstoque', descricao: 'Movimentações de Estoque', essencial: true },
            { nome: 'ordensProducao', descricao: 'Ordens de Produção', essencial: true },
            { nome: 'ordens_producao', descricao: 'Ordens de Produção (alternativo)', essencial: false },
            { nome: 'produtos', descricao: 'Produtos', essencial: true },
            { nome: 'armazens', descricao: 'Armazéns', essencial: true },
            { nome: 'fornecedores', descricao: 'Fornecedores', essencial: false },
            { nome: 'usuarios', descricao: 'Usuários', essencial: false }
        ];

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
        };

        window.verificarTodasColecoes = async function() {
            const container = document.getElementById('collectionsResults');
            container.innerHTML = '<p>🔄 Verificando coleções...</p>';

            collectionsData = {};
            let html = '';

            for (const colecao of colecoesPrevistas) {
                try {
                    console.log(`Verificando coleção: ${colecao.nome}`);
                    
                    const snapshot = await getDocs(collection(db, colecao.nome));
                    const documentos = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    
                    collectionsData[colecao.nome] = {
                        existe: true,
                        quantidade: documentos.length,
                        documentos: documentos.slice(0, 3), // Primeiros 3 para preview
                        essencial: colecao.essencial
                    };

                    const cardClass = documentos.length > 0 ? 'collection-found' : 'collection-empty';
                    const statusIcon = documentos.length > 0 ? '✅' : '⚠️';
                    const statusText = documentos.length > 0 ? 'Encontrada com dados' : 'Encontrada mas vazia';

                    html += `
                        <div class="collection-card ${cardClass}">
                            <h3>${statusIcon} ${colecao.nome}</h3>
                            <p><strong>Descrição:</strong> ${colecao.descricao}</p>
                            <p><strong>Status:</strong> ${statusText}</p>
                            <p><strong>Quantidade de documentos:</strong> ${documentos.length}</p>
                            <p><strong>Essencial:</strong> ${colecao.essencial ? 'Sim' : 'Não'}</p>
                    `;

                    if (documentos.length > 0) {
                        html += `
                            <h4>Preview dos dados:</h4>
                            <div class="data-preview">
                        `;
                        
                        documentos.slice(0, 2).forEach((doc, index) => {
                            html += `<strong>Documento ${index + 1}:</strong><br>`;
                            Object.keys(doc).slice(0, 5).forEach(key => {
                                let value = doc[key];
                                if (typeof value === 'object' && value !== null) {
                                    if (value.seconds) {
                                        value = new Date(value.seconds * 1000).toLocaleString();
                                    } else {
                                        value = JSON.stringify(value).substring(0, 50) + '...';
                                    }
                                }
                                html += `${key}: ${value}<br>`;
                            });
                            html += '<br>';
                        });
                        
                        html += '</div>';
                    }

                    html += '</div>';

                } catch (error) {
                    console.error(`Erro ao verificar ${colecao.nome}:`, error);
                    
                    collectionsData[colecao.nome] = {
                        existe: false,
                        erro: error.message,
                        essencial: colecao.essencial
                    };

                    html += `
                        <div class="collection-card collection-not-found">
                            <h3>❌ ${colecao.nome}</h3>
                            <p><strong>Descrição:</strong> ${colecao.descricao}</p>
                            <p><strong>Status:</strong> Não encontrada</p>
                            <p><strong>Erro:</strong> ${error.message}</p>
                            <p><strong>Essencial:</strong> ${colecao.essencial ? 'Sim' : 'Não'}</p>
                        </div>
                    `;
                }
            }

            container.innerHTML = html;
            mostrarResumo();
            gerarRecomendacoes();
            document.getElementById('btnGerar').disabled = false;
        };

        function mostrarResumo() {
            const summary = document.getElementById('summarySection');
            
            const encontradas = Object.values(collectionsData).filter(c => c.existe).length;
            const comDados = Object.values(collectionsData).filter(c => c.existe && c.quantidade > 0).length;
            const essenciaisFaltando = Object.entries(collectionsData)
                .filter(([nome, dados]) => dados.essencial && (!dados.existe || dados.quantidade === 0)).length;

            summary.innerHTML = `
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="summary-number found">${encontradas}</div>
                        <div>Coleções Encontradas</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number found">${comDados}</div>
                        <div>Com Dados</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number ${essenciaisFaltando > 0 ? 'not-found' : 'found'}">${essenciaisFaltando}</div>
                        <div>Essenciais Faltando</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number">${colecoesPrevistas.length}</div>
                        <div>Total Verificadas</div>
                    </div>
                </div>
            `;
        }

        function gerarRecomendacoes() {
            const container = document.getElementById('recommendationsSection');
            
            const essenciaisComDados = Object.entries(collectionsData)
                .filter(([nome, dados]) => dados.essencial && dados.existe && dados.quantidade > 0);
            
            const essenciaisFaltando = Object.entries(collectionsData)
                .filter(([nome, dados]) => dados.essencial && (!dados.existe || dados.quantidade === 0));

            let html = `
                <div class="collection-card">
                    <h3>💡 Recomendações para o Dashboard</h3>
            `;

            if (essenciaisComDados.length >= 3) {
                html += `
                    <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 10px 0;">
                        <h4>✅ Dashboard Viável!</h4>
                        <p>Você tem dados suficientes nas coleções essenciais:</p>
                        <ul>
                `;
                
                essenciaisComDados.forEach(([nome, dados]) => {
                    html += `<li><strong>${nome}:</strong> ${dados.quantidade} registros</li>`;
                });
                
                html += `
                        </ul>
                        <p>O dashboard pode ser gerado com essas coleções.</p>
                    </div>
                `;
            } else {
                html += `
                    <div style="background: #f8d7da; padding: 15px; border-radius: 6px; margin: 10px 0;">
                        <h4>⚠️ Dados Insuficientes</h4>
                        <p>Para um dashboard completo, você precisa de dados nas seguintes coleções essenciais:</p>
                        <ul>
                `;
                
                essenciaisFaltando.forEach(([nome, dados]) => {
                    const colecao = colecoesPrevistas.find(c => c.nome === nome);
                    html += `<li><strong>${nome}:</strong> ${colecao.descricao} - ${dados.existe ? 'Vazia' : 'Não encontrada'}</li>`;
                });
                
                html += `
                        </ul>
                    </div>
                `;
            }

            // Sugestões de coleções alternativas
            const alternativas = [];
            if (!collectionsData['recebimentosDetalhes']?.existe || collectionsData['recebimentosDetalhes']?.quantidade === 0) {
                if (collectionsData['recebimento_materiais']?.existe && collectionsData['recebimento_materiais']?.quantidade > 0) {
                    alternativas.push('Use "recebimento_materiais" em vez de "recebimentosDetalhes"');
                }
            }
            
            if (!collectionsData['ordensProducao']?.existe || collectionsData['ordensProducao']?.quantidade === 0) {
                if (collectionsData['ordens_producao']?.existe && collectionsData['ordens_producao']?.quantidade > 0) {
                    alternativas.push('Use "ordens_producao" em vez de "ordensProducao"');
                }
            }

            if (alternativas.length > 0) {
                html += `
                    <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 10px 0;">
                        <h4>🔄 Coleções Alternativas Disponíveis</h4>
                        <ul>
                `;
                alternativas.forEach(alt => {
                    html += `<li>${alt}</li>`;
                });
                html += `
                        </ul>
                    </div>
                `;
            }

            html += '</div>';
            container.innerHTML = html;
        }

        window.gerarDashboardCorrigido = function() {
            // Identificar quais coleções usar
            const colecoesParaUsar = {};
            
            // Mapear coleções disponíveis
            if (collectionsData['recebimentosDetalhes']?.existe && collectionsData['recebimentosDetalhes']?.quantidade > 0) {
                colecoesParaUsar.recebimentos = 'recebimentosDetalhes';
            } else if (collectionsData['recebimento_materiais']?.existe && collectionsData['recebimento_materiais']?.quantidade > 0) {
                colecoesParaUsar.recebimentos = 'recebimento_materiais';
            }

            if (collectionsData['ordensProducao']?.existe && collectionsData['ordensProducao']?.quantidade > 0) {
                colecoesParaUsar.ordens = 'ordensProducao';
            } else if (collectionsData['ordens_producao']?.existe && collectionsData['ordens_producao']?.quantidade > 0) {
                colecoesParaUsar.ordens = 'ordens_producao';
            }

            // Outras coleções essenciais
            ['estoques', 'movimentacoesEstoque', 'produtos', 'armazens'].forEach(col => {
                if (collectionsData[col]?.existe && collectionsData[col]?.quantidade > 0) {
                    colecoesParaUsar[col] = col;
                }
            });

            // Coleções opcionais
            ['solicitacoesCompra', 'cotacoes', 'pedidosCompra', 'estoqueQualidade', 'fornecedores'].forEach(col => {
                if (collectionsData[col]?.existe && collectionsData[col]?.quantidade > 0) {
                    colecoesParaUsar[col] = col;
                }
            });

            const config = {
                colecoesEncontradas: colecoesParaUsar,
                totalColecoes: Object.keys(colecoesParaUsar).length,
                dataVerificacao: new Date().toISOString()
            };

            // Salvar configuração no localStorage para o dashboard usar
            localStorage.setItem('dashboardConfig', JSON.stringify(config));

            alert(`✅ Configuração salva!\n\nColeções disponíveis: ${Object.keys(colecoesParaUsar).length}\n\nAgora você pode abrir o dashboard corrigido que será criado.`);
            
            // Aqui você pode redirecionar para um dashboard corrigido ou gerar um novo arquivo
            console.log('Configuração do dashboard:', config);
        };
    </script>
</body>
</html>
