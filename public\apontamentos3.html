<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Apontamento de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }
    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
      padding: 20px;
    }
    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
    }
    .header h1 {
      font-size: 24px;
      margin: 0;
    }
    .search-bar {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 25px;
    }
    .form-col {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #495057;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-col label {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-col label::before {
      content: '•';
      color: #0854a0;
      font-weight: bold;
      font-size: 16px;
    }

    input, select, textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      background-color: white;
      margin-bottom: 8px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #0854a0;
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-1px);
    }

    textarea {
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      background: var(--primary-color);
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      font-weight: 500;
    }
    button:hover {
      background: var(--primary-hover);
    }
    .back-button {
      background-color: var(--header-bg);
      color: white;
      border: none;
    }
    .back-button:hover {
      background-color: #2a3b4d;
    }
    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      font-size: 13px;
    }
    .orders-table th, .orders-table td {
      padding: 10px 8px;
      border: 1px solid var(--border-color);
      text-align: left;
    }
    .orders-table th {
      background: var(--secondary-color);
      font-weight: 600;
      color: var(--primary-color);
    }
    .orders-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .orders-table tr:hover {
      background-color: #e6f2ff;
    }
    .status-badge {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-pendente { background: #fff4cc; color: #8c6c00; border: 1px solid #ffd43b; }
    .status-em-producao { background: #e5f2f9; color: #0854a0; border: 1px solid #0854a0; }
    .status-concluida { background: #e8f3e8; color: #107e3e; border: 1px solid #107e3e; }
    .status-cancelada { background: #ffeaea; color: #bb0000; border: 1px solid #bb0000; }
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(5px);
      animation: modalFadeIn 0.3s ease;
    }

    @keyframes modalFadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .modal-content {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 900px;
      border-radius: 16px;
      position: relative;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      animation: modalSlideIn 0.4s ease;
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      flex-direction: column;
    }
    .modal-header {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
      color: white;
      padding: 25px 30px;
      border-radius: 16px 16px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .modal-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }

    .modal-header h2 {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;
      z-index: 1;
    }

    .modal-header h2::before {
      content: '🏭';
      font-size: 28px;
      opacity: 0.9;
    }
    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: 30px;
      max-height: calc(85vh - 150px);
      background: white;
      position: relative;
    }

    /* Seção de informações da ordem */
    #orderInfo {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 25px;
      border-left: 4px solid #0854a0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    #orderInfo h3 {
      color: #0854a0;
      margin: 0 0 15px 0;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    #orderInfo h3::before {
      content: '📋';
      font-size: 20px;
    }

    .order-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }

    .order-detail-item {
      background: white;
      padding: 12px 15px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .order-detail-label {
      font-size: 12px;
      color: #6c757d;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
    }

    .order-detail-value {
      font-size: 16px;
      color: #212529;
      font-weight: 600;
    }
    .modal-footer {
      padding: 25px 30px;
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      text-align: right;
      border-radius: 0 0 16px 16px;
    }

    .modal-footer button {
      background: linear-gradient(135deg, #107e3e, #0d6e36);
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(16, 126, 62, 0.3);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .modal-footer button:hover {
      background: linear-gradient(135deg, #0d6e36, #0a5a2e);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(16, 126, 62, 0.4);
    }

    .modal-footer button:disabled {
      background: linear-gradient(135deg, #6c757d, #5a6268);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .close-button {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      position: relative;
      z-index: 2;
    }

    .close-button:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: rotate(90deg) scale(1.1);
    }
    .materials-list {
      max-height: 400px;
      overflow-y: auto;
      margin: 25px 0;
      padding: 0;
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .materials-list h4 {
      background: linear-gradient(135deg, #6f42c1, #5a32a3);
      color: white;
      margin: 0;
      padding: 15px 20px;
      border-radius: 12px 12px 0 0;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .materials-list h4::before {
      content: '📦';
      font-size: 18px;
    }

    .materials-container {
      padding: 15px;
    }

    .material-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background: white;
      margin-bottom: 10px;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .material-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .material-item:last-child {
      margin-bottom: 0;
    }

    .material-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .material-code {
      font-weight: 600;
      color: #0854a0;
      font-size: 14px;
    }

    .material-description {
      color: #495057;
      font-size: 13px;
      line-height: 1.4;
    }

    .material-status {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      font-weight: 600;
    }

    .status-ok {
      color: #107e3e;
    }

    .status-warning {
      color: #e9730c;
    }

    .status-error {
      color: #bb0000;
    }

    .material-quantities {
      text-align: right;
      font-size: 12px;
      color: #6c757d;
      line-height: 1.4;
    }

    .generate-stock-btn {
      background: linear-gradient(135deg, #e9730c, #d66a0b);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .generate-stock-btn:hover {
      background: linear-gradient(135deg, #d66a0b, #c4600a);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(233, 115, 12, 0.3);
    }
    .material-status {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-ok { background: #e8f3e8; color: #107e3e; border: 1px solid #107e3e; }
    .status-warning { background: #fff4cc; color: #8c6c00; border: 1px solid #ffd43b; }
    .status-error { background: #ffeaea; color: #bb0000; border: 1px solid #bb0000; }
    .progress-bar {
      width: 100%;
      height: 16px;
      background-color: var(--secondary-color);
      border-radius: 8px;
      overflow: hidden;
      margin-top: 5px;
      border: 1px solid var(--border-color);
    }
    .progress-fill {
      height: 100%;
      background-color: var(--success-color);
      transition: width 0.3s ease;
    }
    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* Estilos para notificações */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 2000;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      background: linear-gradient(135deg, #107e3e, #0d6e36);
    }

    .notification.warning {
      background: linear-gradient(135deg, #e9730c, #d66a0b);
    }

    .notification.error {
      background: linear-gradient(135deg, #bb0000, #a30000);
    }

    .notification.info {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
    }

    .metric-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .metric-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
  </style>

    <script type="module" src="js/main.js"></script>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Apontamento de Produção</h1>
      <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
    </div>

    <!-- Dashboard de Métricas -->
    <div class="dashboard-metrics" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
      <div class="metric-card" style="background: linear-gradient(135deg, #0854a0, #0a4d8c); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="totalOPs">0</div>
        <div style="font-size: 12px; opacity: 0.9;">OPs Ativas</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #107e3e, #0d6e36); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="emProducao">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Em Produção</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #e9730c, #d66a0b); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="materiaisFalta">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Materiais em Falta</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #bb0000, #a30000); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="opsAtrasadas">0</div>
        <div style="font-size: 12px; opacity: 0.9;">OPs Atrasadas</div>
      </div>
    </div>

    <div class="search-bar">
      <div class="form-row">
        <div class="form-col">
          <input type="text" id="searchInput" placeholder="Buscar por número da ordem ou produto..." oninput="filterOrders()">
        </div>
        <div class="form-col">
          <select id="statusFilter" onchange="filterOrders()">
            <option value="">Todos os status</option>
            <option value="Pendente">Pendente</option>
            <option value="Em Produção">Em Produção</option>
          </select>
        </div>
        <div class="form-col">
          <button onclick="atualizarDashboard()" style="background: var(--success-color);">
            <i class="fas fa-sync-alt"></i> Atualizar Métricas
          </button>
        </div>
      </div>
    </div>

    <table class="orders-table">
      <thead>
        <tr>
          <th>Ordem</th>
          <th>Produto</th>
          <th>Quantidade</th>
          <th>Produzido</th>
          <th>Status</th>
          <th>Data Entrega</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="ordersTableBody">
      </tbody>
    </table>
  </div>

  <div id="appointmentModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Apontamento de Produção</h2>
        <span class="close-button" onclick="closeModal()">×</span>
      </div>

      <div class="modal-body">
        <div id="orderInfo"></div>

        <div id="materialsList" class="materials-list">
          <h4>Materiais Necessários</h4>
          <div class="materials-container"></div>
        </div>

        <form id="appointmentForm" onsubmit="submitAppointment(event)">
          <div class="form-row">
            <div class="form-col">
              <label for="quantity">Quantidade Produzida</label>
              <input type="number" id="quantity" min="0.001" step="0.001" required placeholder="Digite a quantidade produzida">
            </div>
            <div class="form-col">
              <label for="scrap">Quantidade de Refugo</label>
              <input type="number" id="scrap" min="0" step="0.001" value="0" placeholder="Digite a quantidade de refugo">
            </div>
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="observations">Observações</label>
              <textarea id="observations" rows="3" placeholder="Digite observações sobre a produção (opcional)"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="submit" form="appointmentForm" id="submitButton">
          <i class="fas fa-check"></i> Confirmar Apontamento
        </button>
      </div>
    </div>
  </div>

  <!-- Área oculta para impressão -->
  <div id="printArea" style="display:none"></div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { doc, getDoc, collection, onSnapshot, updateDoc, Timestamp, addDoc, writeBatch } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let ordensProducao = [];
    let estoques = [];
    let armazens = [];
    let estruturas = [];
    let operacoes = [];
    let recursos = [];
    let currentOrder = null;
    let permitirProducaoSemEstoque = false;
    let permitirGerarEstoqueAutomatico = false;

    window.onload = async function() {
      // Verificar se o usuário está logado
      const currentUser = JSON.parse(localStorage.getItem('currentUser'));
      if (!currentUser) {
        window.location.href = 'login.html';
        return;
      }

      await loadSystemParameters();
      setupRealTimeListeners();
    };

    async function loadSystemParameters() {
      try {
        const docSnap = await getDoc(doc(db, "parametros", "sistema"));
        if (docSnap.exists()) {
          permitirProducaoSemEstoque = docSnap.data().permitirProducaoSemEstoque || false;
          permitirGerarEstoqueAutomatico = docSnap.data().permitirGerarEstoqueAutomatico || false;
        }
      } catch (error) {
        console.error("Erro ao carregar parâmetros do sistema:", error);
      }
    }

    async function setupRealTimeListeners() {
      try {
        const promises = [
          new Promise(resolve => onSnapshot(collection(db, "produtos"), snap => { produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); })),
          new Promise(resolve => onSnapshot(collection(db, "ordensProducao"), snap => { ordensProducao = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); loadOrders(); resolve(); })),
          new Promise(resolve => onSnapshot(collection(db, "estoques"), snap => { estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); })),
          new Promise(resolve => onSnapshot(collection(db, "armazens"), snap => { armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); })),
          new Promise(resolve => onSnapshot(collection(db, "estruturas"), snap => { estruturas = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); })),
          new Promise(resolve => onSnapshot(collection(db, "operacoes"), snap => { operacoes = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); })),
          new Promise(resolve => onSnapshot(collection(db, "recursos"), snap => { recursos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); }))
        ];
        await Promise.all(promises);
      } catch (error) {
        console.error("Erro ao configurar listeners:", error);
        alert("Erro ao carregar dados: " + error.message);
      }
    }

    async function loadOrders() {
      const tableBody = document.getElementById('ordersTableBody');
      tableBody.innerHTML = '';

      const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');

      // Atualizar métricas do dashboard
      atualizarDashboard();

      ordensAtivas
        .sort((a, b) => {
          // Primeiro ordena por nível
          if (a.nivel !== b.nivel) return a.nivel - b.nivel;
          // Depois ordena por data de entrega, tratando casos onde dataEntrega pode estar ausente
          const dateA = a.dataEntrega?.seconds ? new Date(a.dataEntrega.seconds * 1000) : new Date(0);
          const dateB = b.dataEntrega?.seconds ? new Date(b.dataEntrega.seconds * 1000) : new Date(0);
          return dateB - dateA;
        })
        .forEach(ordem => {
          const produto = produtos.find(p => p.id === ordem.produtoId);
          if (!produto) return;

          const row = document.createElement('tr');
          const progress = ordem.quantidadeProduzida ? 
            (ordem.quantidadeProduzida / ordem.quantidade * 100).toFixed(1) : 0;

          // Trata a exibição da data de entrega para casos onde pode estar ausente
          const dataEntrega = ordem.dataEntrega?.seconds 
            ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString() 
            : 'Não definida';

          row.innerHTML = `
            <td>${ordem.numero}</td>
            <td>${produto.codigo} - ${produto.descricao}</td>
            <td>${ordem.quantidade} ${produto.unidade}</td>
            <td>
              ${ordem.quantidadeProduzida || 0} ${produto.unidade}
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress}%"></div>
              </div>
            </td>
            <td><span class="status-badge status-${ordem.status.toLowerCase()}">${ordem.status}</span></td>
            <td>${dataEntrega}</td>
            <td>
              <button onclick="openAppointmentModal('${ordem.id}')">Apontar</button>
              <button onclick="printOrderReport('${ordem.id}')">Imprimir OP</button>
              ${(ordem.status !== 'Em Produção' && ordem.status !== 'Concluída' && ordem.status !== 'Cancelada') ? `<button onclick="marcarEnviadaParaFabrica('${ordem.id}')">Enviar p/ Fábrica</button>` : ''}
            </td>
          `;
          tableBody.appendChild(row);
        });
    }

    window.filterOrders = function() {
      const searchText = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;

      const rows = document.getElementById('ordersTableBody').getElementsByTagName('tr');

      for (const row of rows) {
        const numero = row.cells[0].textContent.toLowerCase();
        const produto = row.cells[1].textContent.toLowerCase();
        const status = row.cells[4].textContent;

        const matchesSearch = numero.includes(searchText) || produto.includes(searchText);
        const matchesStatus = !statusFilter || status === statusFilter;

        row.style.display = matchesSearch && matchesStatus ? '' : 'none';
      }
    };

    window.openAppointmentModal = async function(orderId) {
      // Atualiza os estoques antes de abrir o modal
      await new Promise(resolve => onSnapshot(collection(db, "estoques"), snap => { estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); }));
      currentOrder = ordensProducao.find(op => op.id === orderId);
      if (!currentOrder) return;

      const produto = produtos.find(p => p.id === currentOrder.produtoId);
      let materialsHtml = '';
      let canProduce = true;

      // Verifica se o armazém de produção da OP é do tipo PRODUCAO
      const armazemProducao = armazens.find(a => a.id === currentOrder.armazemProducaoId);
      if (!armazemProducao || armazemProducao.tipo !== 'PRODUCAO') {
        alert('A ordem de produção deve estar associada a um armazém do tipo Produção.');
        return;
      }

      if (Array.isArray(currentOrder.materiaisNecessarios) && currentOrder.materiaisNecessarios.length > 0) {
        for (const material of currentOrder.materiaisNecessarios) {
          const materialProduto = produtos.find(p => p.id === material.produtoId);
          const armazemProducaoMateriais = armazens.find(a => a.id === currentOrder.armazemProducaoId && a.tipo === 'PRODUCAO');
          if (!armazemProducaoMateriais) {
            canProduce = false;
            materialsHtml += `
              <div class="material-item">
                <div class="material-info">
                  <div class="material-code">${materialProduto.codigo}</div>
                  <div class="material-description">${materialProduto.descricao}</div>
                </div>
                <div class="material-quantities">
                  <div>Necessário: ${material.quantidade.toFixed(3)} ${materialProduto.unidade}</div>
                  <div>Disponível: 0 ${materialProduto.unidade}</div>
                  <div>Armazém: Não encontrado</div>
                </div>
                <div class="material-status status-error">
                  <i class="fas fa-times-circle"></i> 0%
                </div>
              </div>`;
            continue;
          }

          const estoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === armazemProducaoMateriais.id) || { saldo: 0, saldoReservado: 0 };
          const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);

          const quantidadeRestante = material.quantidade - (currentOrder.quantidadeProduzida || 0) * (material.quantidade / currentOrder.quantidade);
          const disponibilidade = saldoDisponivel >= quantidadeRestante ? 100 : (saldoDisponivel / quantidadeRestante * 100).toFixed(1);
          let statusClass = 'status-ok';

          if (!permitirProducaoSemEstoque && saldoDisponivel < quantidadeRestante) {
            statusClass = 'status-error';
            canProduce = false;
          } else if (saldoDisponivel < quantidadeRestante * 1.2) {
            statusClass = 'status-warning';
          }

          const statusIcon = statusClass === 'status-ok' ? 'check-circle' :
                            statusClass === 'status-warning' ? 'exclamation-triangle' : 'times-circle';

          materialsHtml += `
            <div class="material-item">
              <div class="material-info">
                <div class="material-code">${materialProduto.codigo}</div>
                <div class="material-description">${materialProduto.descricao}</div>
              </div>
              <div class="material-quantities">
                <div>Necessário: ${quantidadeRestante.toFixed(3)} ${materialProduto.unidade}</div>
                <div>Disponível: ${saldoDisponivel.toFixed(3)} ${materialProduto.unidade}</div>
                <div>Armazém: ${armazemProducaoMateriais.codigo}</div>
              </div>
              <div class="material-status ${statusClass}">
                <i class="fas fa-${statusIcon}"></i> ${disponibilidade}%
              </div>
              ${(saldoDisponivel < quantidadeRestante && permitirGerarEstoqueAutomatico) ?
                `<button class='generate-stock-btn' onclick='ajustarETransferirMaterial("${material.produtoId}", ${quantidadeRestante - saldoDisponivel})'>
                  <i class="fas fa-magic"></i> Gerar Estoque
                </button>` : ''}
            </div>`;
        }
      } else {
        materialsHtml += '<p>Sem materiais necessários registrados.</p>';
      }

      document.getElementById('orderInfo').innerHTML = `
        <h3>Ordem de Produção: ${currentOrder.numero}</h3>
        <div class="order-details">
          <div class="order-detail-item">
            <div class="order-detail-label">Produto</div>
            <div class="order-detail-value">${produto.codigo} - ${produto.descricao}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Quantidade Total</div>
            <div class="order-detail-value">${currentOrder.quantidade} ${produto.unidade}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Já Produzido</div>
            <div class="order-detail-value">${currentOrder.quantidadeProduzida || 0} ${produto.unidade}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Armazém de Produção</div>
            <div class="order-detail-value">${armazemProducao.codigo} - ${armazemProducao.nome}</div>
          </div>
        </div>
      `;

      document.querySelector('#materialsList .materials-container').innerHTML = materialsHtml;

      document.getElementById('submitButton').disabled = !canProduce;
      if (!canProduce && !permitirProducaoSemEstoque) {
        alert('Não há material suficiente no armazém de produção para realizar o apontamento. Transfira os materiais necessários do armazém tipo Almoxarifado para o armazém tipo Produção usando o módulo de movimentação.');
      }

      document.getElementById('appointmentModal').style.display = 'block';
    };

    window.closeModal = function() {
      document.getElementById('appointmentModal').style.display = 'none';
      document.getElementById('appointmentForm').reset();
      currentOrder = null;
    };

    async function updateInventory(produtoId, armazemId, quantidade, tipo) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldo = tipo === 'entrada' ? estoque.saldo + quantidade : estoque.saldo - quantidade;
        if (novoSaldo < 0 && !permitirProducaoSemEstoque) throw new Error(`Saldo insuficiente no armazém ${armazemId}.`);
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldo = novoSaldo;
      } else if (tipo === 'entrada') {
        const novoEstoque = { produtoId, armazemId, saldo: quantidade, ultimaMovimentacao: Timestamp.now() };
        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        estoques.push({ ...novoEstoque, id: docRef.id });
      }
    }

    async function updateInventoryReservation(produtoId, armazemId, quantidade) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldoReservado = (estoque.saldoReservado || 0) + quantidade;
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldoReservado: Math.max(0, novoSaldoReservado),
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldoReservado = Math.max(0, novoSaldoReservado);
      }
    }

    window.submitAppointment = async function(event) {
      event.preventDefault();
      const submitButton = document.getElementById('submitButton');
      submitButton.disabled = true;

      try {
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser) {
          throw new Error('Usuário não está logado');
        }

        const quantidade = parseFloat(document.getElementById('quantity').value);
        const refugo = parseFloat(document.getElementById('scrap').value) || 0;
        const observacoes = document.getElementById('observations').value;

        if (quantidade <= 0) {
          throw new Error('A quantidade produzida deve ser maior que zero');
        }

        if (quantidade + (currentOrder.quantidadeProduzida || 0) > currentOrder.quantidade) {
          throw new Error('A quantidade total produzida não pode exceder a quantidade da ordem');
        }

        // Atualizar ordem de produção
        const orderRef = doc(db, "ordensProducao", currentOrder.id);
        const novaQuantidadeProduzida = (currentOrder.quantidadeProduzida || 0) + quantidade;
        const novoRefugo = (currentOrder.refugo || 0) + refugo;

        // Determinar novo status
        let novoStatus = currentOrder.status;
        if (novaQuantidadeProduzida >= currentOrder.quantidade) {
          novoStatus = 'Concluída';
        } else if (novoStatus === 'Pendente') {
          novoStatus = 'Em Produção';
        }

        // Baixar materiais do estoque
        const batch = writeBatch(db);

        if (currentOrder.materiaisNecessarios) {
          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * quantidade;
            const armazemId = material.armazemId || currentOrder.armazemProducaoId;

            // Encontrar o estoque correspondente
            const estoque = estoques.find(e => 
              e.produtoId === material.produtoId && 
              e.armazemId === armazemId
            );

            if (estoque) {
              const saldoAtual = estoque.saldo;
              const saldoReservado = estoque.saldoReservado || 0;

              if (saldoAtual - saldoReservado < quantidadeNecessaria && !permitirProducaoSemEstoque) {
                throw new Error(`Saldo insuficiente para o material ${material.codigo}`);
              }

              batch.update(doc(db, "estoques", estoque.id), {
                saldo: saldoAtual - quantidadeNecessaria,
                ultimaMovimentacao: Timestamp.now()
              });
            } else if (!permitirProducaoSemEstoque) {
              throw new Error(`Estoque não encontrado para o material ${material.codigo}`);
            }
          }
        }

        // Atualizar ordem
        batch.update(orderRef, {
          quantidadeProduzida: novaQuantidadeProduzida,
          refugo: novoRefugo,
          status: novoStatus,
          ultimaAtualizacao: Timestamp.now()
        });

        // Registrar apontamento
        const apontamentoRef = collection(db, "apontamentos");
        const novoApontamento = {
          ordemId: currentOrder.id,
          numeroOrdem: currentOrder.numero,
          produtoId: currentOrder.produtoId,
          quantidade: quantidade,
          refugo: refugo,
          observacoes: observacoes,
          usuario: currentUser.email,
          nomeUsuario: currentUser.nome,
          dataHora: Timestamp.now()
        };

        // Primeiro executar o batch para atualizar estoque e ordem
        await batch.commit();

        // Depois adicionar o apontamento
        await addDoc(apontamentoRef, novoApontamento);

        // Registrar movimentações de estoque
        // 1. Consumo de materiais
        if (!currentOrder.materiaisNecessarios || currentOrder.materiaisNecessarios.length === 0) {
          alert('Atenção: Esta ordem de produção não possui materiais necessários cadastrados. Nenhuma movimentação de consumo será registrada!');
        } else {
          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * quantidade;
            const produtoMaterial = produtos.find(p => p.id === material.produtoId);
            await addDoc(collection(db, "movimentacoesEstoque"), {
              produtoId: material.produtoId,
              tipo: 'SAIDA',
              quantidade: quantidadeNecessaria,
              unidade: produtoMaterial?.unidade || '',
              tipoDocumento: 'PRODUCAO',
              numeroDocumento: currentOrder.numero,
              observacoes: `Consumo para OP ${currentOrder.numero}`,
              dataHora: Timestamp.now(),
              armazemId: material.armazemId || currentOrder.armazemProducaoId
            });
          }
        }
        // 2. Entrada do produto acabado
        const produtoAcabado = produtos.find(p => p.id === currentOrder.produtoId);
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId: currentOrder.produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidade,
          unidade: produtoAcabado?.unidade || '',
          tipoDocumento: 'PRODUCAO',
          numeroDocumento: currentOrder.numero,
          observacoes: `Produção OP ${currentOrder.numero}`,
          dataHora: Timestamp.now(),
          armazemId: currentOrder.armazemProducaoId
        });
        // Atualizar saldo do estoque do produto acabado no armazem de produção
        let estoqueProd = estoques.find(e => e.produtoId === currentOrder.produtoId && e.armazemId === currentOrder.armazemProducaoId);
        if (estoqueProd) {
          await updateDoc(doc(db, "estoques", estoqueProd.id), {
            saldo: (estoqueProd.saldo || 0) + quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
          estoqueProd.saldo += quantidade;
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId: currentOrder.produtoId,
            armazemId: currentOrder.armazemProducaoId,
            saldo: quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId: currentOrder.produtoId, armazemId: currentOrder.armazemProducaoId, saldo: quantidade });
        }

        mostrarNotificacao('✅ Apontamento registrado com sucesso!', 'success');
        closeModal();

      } catch (error) {
        console.error("Erro ao registrar apontamento:", error);
        alert(error.message || 'Erro ao registrar apontamento');
      } finally {
        submitButton.disabled = false;
      }
    };

    window.onclick = function(event) {
      const modal = document.getElementById('appointmentModal');
      if (event.target === modal) {
        closeModal();
      }
    };

    // Adicionar função global para ajuste e transferência automática
    window.ajustarETransferirMaterial = async function(produtoId, quantidadeNecessaria) {
      try {
        const ALM01 = armazens.find(a => a.codigo === 'ALM01');
        const PROD1 = armazens.find(a => a.id === currentOrder.armazemProducaoId);
        if (!ALM01) {
          alert('Armazém ALM01 não encontrado!');
          return;
        }
        if (!PROD1) {
          alert('Armazém de produção não encontrado!');
          return;
        }
        // 1. Ajuste de inventário (entrada) no ALM01
        const produto = produtos.find(p => p.id === produtoId);
        let estoqueAlm01 = estoques.find(e => e.produtoId === produtoId && e.armazemId === ALM01.id);
        let novoSaldo = (estoqueAlm01?.saldo || 0) + quantidadeNecessaria;
        if (estoqueAlm01) {
          await updateDoc(doc(db, "estoques", estoqueAlm01.id), {
            saldo: novoSaldo,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId,
            armazemId: ALM01.id,
            saldo: quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId, armazemId: ALM01.id, saldo: quantidadeNecessaria });
        }
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'AJUSTE',
          numeroDocumento: `AJU-${Date.now()}`,
          observacoes: `Ajuste automático para suprir OP ${currentOrder.numero}`,
          dataHora: Timestamp.now(),
          armazemId: ALM01.id
        });
        // 2. Transferência ALM01 -> PROD1
        // Atualizar saldo ALM01
        estoqueAlm01 = estoques.find(e => e.produtoId === produtoId && e.armazemId === ALM01.id);
        if (estoqueAlm01) {
          await updateDoc(doc(db, "estoques", estoqueAlm01.id), {
            saldo: estoqueAlm01.saldo - quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
        }
        // Atualizar saldo PROD1
        let estoqueProd1 = estoques.find(e => e.produtoId === produtoId && e.armazemId === PROD1.id);
        if (estoqueProd1) {
          await updateDoc(doc(db, "estoques", estoqueProd1.id), {
            saldo: (estoqueProd1.saldo || 0) + quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId,
            armazemId: PROD1.id,
            saldo: quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId, armazemId: PROD1.id, saldo: quantidadeNecessaria });
        }
        // Registrar movimentações
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'SAIDA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: `TRF-${Date.now()}`,
          observacoes: `Transferência automática para OP ${currentOrder.numero} - Armazém ${PROD1.codigo}`,
          dataHora: Timestamp.now(),
          armazemId: ALM01.id
        });
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: `TRF-${Date.now()}`,
          observacoes: `Transferência automática para OP ${currentOrder.numero} - Do armazém ${ALM01.codigo}`,
          dataHora: Timestamp.now(),
          armazemId: PROD1.id
        });
        mostrarNotificacao('✅ Ajuste e transferência realizados com sucesso!', 'success');
        await setupRealTimeListeners(); // Atualiza os dados na tela
        openAppointmentModal(currentOrder.id); // Reabre o modal atualizado
      } catch (error) {
        console.error(error);
        alert('Erro ao ajustar e transferir material: ' + error.message);
      }
    };

    // Adicionar a função global para impressão:
    window.printOrderReport = function(orderId) {
      const ordem = ordensProducao.find(op => op.id === orderId);
      if (!ordem) return alert('Ordem não encontrada!');
      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);
      const printArea = document.getElementById('printArea');
      let roteiroHtml = '';
      if (estrutura && Array.isArray(estrutura.operacoes) && estrutura.operacoes.length > 0) {
        roteiroHtml = `
          <div class='section' style='margin-bottom:15px;'>
            <div class='section-title' style='background:#f0f0f0;padding:3px 8px;font-weight:bold;border:1px solid #ccc;font-size:11px;'>ROTEIRO DE PRODUÇÃO</div>
            <table style='width:100%;border-collapse:collapse;margin-top:3px;font-size:11px;'>
              <thead>
                <tr>
                  <th style='border:1px solid #ccc;padding:4px;'>Seq.</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Operação</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Recurso</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Tempo (min)</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Descrição</th>
                </tr>
              </thead>
              <tbody>
                ${estrutura.operacoes.sort((a, b) => a.sequencia - b.sequencia).map(op => {
                  const operacao = operacoes.find(o => o.id === op.operacaoId) || {};
                  const recurso = recursos.find(r => r.id === op.recursoId) || {};
                  return `<tr>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.sequencia}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${operacao.operacao || ''}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${recurso.codigo || ''} - ${recurso.maquina || ''}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.tempo}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.descricao || ''}</td>
                  </tr>`;
                }).join('')}
              </tbody>
            </table>
          </div>`;
      }
      printArea.innerHTML = `
        <div class='container' style='max-width:210mm;margin:0 auto;font-family:Arial,sans-serif;font-size:12px;'>
          <div class='order-page' style='background:white;padding:15px;margin-bottom:20px;box-shadow:0 2px 8px rgba(0,0,0,0.1);'>
            <div class='header' style='display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:10px;border-bottom:1px solid #000;padding-bottom:5px;'>
              <img src='https://www.naliteck.com.br/img/logo.png' alt='Logo' style='width:100px;height:auto;'>
              <div class='order-title' style='text-align:center;flex-grow:1;margin:0 10px;'>
                <h1 style='margin:0;font-size:18px;'>ORDEM DE PRODUÇÃO</h1>
                <h2 style='margin:3px 0;font-size:16px;'>${ordem.numero}</h2>
              </div>
              <div style='text-align:right;font-size:10px;'>
                <strong>Data: </strong>${new Date().toLocaleDateString()}<br>
                <strong>Hora: </strong>${new Date().toLocaleTimeString()}
              </div>
            </div>
            <div class='order-info' style='display:grid;grid-template-columns:repeat(4,1fr);gap:5px;margin-bottom:15px;border:1px solid #ccc;padding:5px;'>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Produto:</strong><span style='font-size:12px;'>${produto.codigo || ''} - ${produto.descricao || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Tipo:</strong><span style='font-size:12px;'>${produto.tipo || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Quantidade:</strong><span style='font-size:12px;'>${ordem.quantidade} ${produto.unidade || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Status:</strong><span class='status-badge status-${ordem.status.toLowerCase()}' style='font-size:10px;font-weight:bold;'>${ordem.status}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Data de Criação:</strong><span style='font-size:12px;'>${ordem.dataCriacao ? new Date(ordem.dataCriacao.seconds * 1000).toLocaleDateString() : ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Data de Entrega:</strong><span style='font-size:12px;'>${ordem.dataEntrega ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString() : ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Prioridade:</strong><span style='font-size:12px;'>${ordem.prioridade || 'Normal'}</span></div>
            </div>
            ${(Array.isArray(ordem.materiaisNecessarios) && ordem.materiaisNecessarios.length > 0) ? `
            <div class='section' style='margin-bottom:15px;'>
              <div class='section-title' style='background:#f0f0f0;padding:3px 8px;font-weight:bold;border:1px solid #ccc;font-size:11px;'>LISTA DE MATERIAIS</div>
              <table style='width:100%;border-collapse:collapse;margin-top:3px;font-size:11px;'>
                <thead>
                  <tr>
                    <th style='border:1px solid #ccc;padding:4px;'>Código</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Descrição</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Tipo</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Quantidade</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Unidade</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Disponível</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Necessidade</th>
                  </tr>
                </thead>
                <tbody>
                  ${ordem.materiaisNecessarios.map(material => {
                    const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
                    const estoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === (material.armazemId || ordem.armazemProducaoId)) || { saldo: 0, saldoReservado: 0 };
                    const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
                    return `<tr>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.codigo || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.descricao || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.tipo || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${material.quantidade}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.unidade || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${saldoDisponivel !== undefined ? saldoDisponivel : 0}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${material.necessidade !== undefined ? material.necessidade : material.quantidade}</td>
                    </tr>`;
                  }).join('')}
                </tbody>
              </table>
            </div>` : ''}
            ${roteiroHtml}
            <div class='signatures' style='margin-top:20px;display:flex;justify-content:space-between;'>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Produção</div></div>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Qualidade</div></div>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Supervisor</div></div>
            </div>
          </div>
        </div>
      `;
      // Abrir nova janela e imprimir
      const printWindow = window.open('', '_blank');
      printWindow.document.write(`<!DOCTYPE html><html><head><title>Impressão OP</title><meta charset='UTF-8'><style>@page{size:A4 portrait;margin:15mm;}@media print{.no-print{display:none;}}</style></head><body>${printArea.innerHTML}<script>window.onload=function(){window.print();}<' + '/script></body></html>`);
      printWindow.document.close();
    };

    // Função para marcar como enviada para fábrica
    window.marcarEnviadaParaFabrica = async function(orderId) {
      try {
        const ordemRef = doc(db, "ordensProducao", orderId);
        await updateDoc(ordemRef, {
          status: 'Em Produção',
          dataEnvioFabrica: Timestamp.now()
        });
        mostrarNotificacao('🏭 OP enviada para produção com sucesso!', 'success');
        await setupRealTimeListeners();
      } catch (error) {
        console.error('Erro ao marcar como enviada para fábrica:', error);
        alert('Erro ao marcar como enviada para fábrica.');
      }
    };

    // Função para atualizar dashboard de métricas
    window.atualizarDashboard = function() {
      try {
        const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');
        const ordensEmProducao = ordensAtivas.filter(op => op.status === 'Em Produção');

        // Calcular OPs atrasadas (data de entrega passou)
        const hoje = new Date();
        const opsAtrasadas = ordensAtivas.filter(op => {
          if (!op.dataEntrega?.seconds) return false;
          const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
          return dataEntrega < hoje;
        });

        // Calcular materiais em falta
        let materiaisEmFalta = 0;
        ordensAtivas.forEach(ordem => {
          if (ordem.materiaisNecessarios) {
            ordem.materiaisNecessarios.forEach(material => {
              const estoque = estoques.find(e =>
                e.produtoId === material.produtoId &&
                e.armazemId === (material.armazemId || ordem.armazemProducaoId)
              ) || { saldo: 0, saldoReservado: 0 };

              const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
              const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

              if (saldoDisponivel < quantidadeRestante) {
                materiaisEmFalta++;
              }
            });
          }
        });

        // Atualizar elementos do dashboard
        document.getElementById('totalOPs').textContent = ordensAtivas.length;
        document.getElementById('emProducao').textContent = ordensEmProducao.length;
        document.getElementById('materiaisFalta').textContent = materiaisEmFalta;
        document.getElementById('opsAtrasadas').textContent = opsAtrasadas.length;

        console.log('Dashboard atualizado:', {
          totalOPs: ordensAtivas.length,
          emProducao: ordensEmProducao.length,
          materiaisFalta: materiaisEmFalta,
          opsAtrasadas: opsAtrasadas.length
        });

      } catch (error) {
        console.error('Erro ao atualizar dashboard:', error);
      }
    };

    // Sistema de notificações
    function mostrarNotificacao(mensagem, tipo = 'info', duracao = 5000) {
      // Remover notificações existentes
      const existingNotifications = document.querySelectorAll('.notification');
      existingNotifications.forEach(n => n.remove());

      // Criar nova notificação
      const notification = document.createElement('div');
      notification.className = `notification ${tipo}`;
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="fas fa-${getIconForType(tipo)}"></i>
          <span>${mensagem}</span>
          <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
        </div>
      `;

      document.body.appendChild(notification);

      // Mostrar notificação
      setTimeout(() => notification.classList.add('show'), 100);

      // Remover automaticamente
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
      }, duracao);
    }

    function getIconForType(tipo) {
      switch (tipo) {
        case 'success': return 'check-circle';
        case 'warning': return 'exclamation-triangle';
        case 'error': return 'times-circle';
        case 'info':
        default: return 'info-circle';
      }
    }

    // Verificar alertas automaticamente
    function verificarAlertas() {
      const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');

      // Verificar OPs atrasadas
      const hoje = new Date();
      const opsAtrasadas = ordensAtivas.filter(op => {
        if (!op.dataEntrega?.seconds) return false;
        const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
        return dataEntrega < hoje;
      });

      if (opsAtrasadas.length > 0) {
        mostrarNotificacao(`⚠️ ${opsAtrasadas.length} ordem(ns) de produção atrasada(s)!`, 'warning', 8000);
      }

      // Verificar materiais em falta críticos
      let materiaisCriticos = 0;
      ordensAtivas.forEach(ordem => {
        if (ordem.materiaisNecessarios) {
          ordem.materiaisNecessarios.forEach(material => {
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || ordem.armazemProducaoId)
            ) || { saldo: 0, saldoReservado: 0 };

            const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
            const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

            if (saldoDisponivel <= 0 && quantidadeRestante > 0) {
              materiaisCriticos++;
            }
          });
        }
      });

      if (materiaisCriticos > 0) {
        mostrarNotificacao(`🚨 ${materiaisCriticos} material(is) em falta crítica!`, 'error', 10000);
      }
    }

    // Executar verificação de alertas a cada 5 minutos
    setInterval(verificarAlertas, 5 * 60 * 1000);
  </script>
</body>
</html>