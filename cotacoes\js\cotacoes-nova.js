// ===== COTAÇÕES - NOVA COTAÇÃO =====

let selectedCreationMethod = 'manual';

// ===== ABRIR MODAL DE NOVA COTAÇÃO =====
window.openNewQuotationModal = function() {
    // Resetar formulário
    resetNewQuotationForm();
    
    // Carregar dados necessários
    loadSolicitacoesForSelection();
    loadTemplatesForSelection();
    
    // Gerar número automático
    generateQuotationNumber();
    
    // Definir data limite padrão (15 dias)
    const defaultDeadline = new Date();
    defaultDeadline.setDate(defaultDeadline.getDate() + 15);
    document.getElementById('newQuotationDeadline').value = defaultDeadline.toISOString().split('T')[0];
    
    openModal('newQuotationModal');
};

function resetNewQuotationForm() {
    selectedCreationMethod = 'manual';
    
    // Resetar método de criação
    document.querySelectorAll('.method-card').forEach(card => {
        card.classList.remove('active');
    });
    document.querySelector('.method-card').classList.add('active');
    
    // Mostrar formulário manual
    document.querySelectorAll('.creation-form').forEach(form => {
        form.classList.remove('active');
    });
    document.getElementById('manualForm').classList.add('active');
    
    // Limpar campos
    document.getElementById('newQuotationNumber').value = '';
    document.getElementById('newQuotationDeadline').value = '';
    document.getElementById('newDeliveryTerm').value = '';
    document.getElementById('newPaymentTerms').value = '';
    document.getElementById('newDeliveryLocation').value = '';
    document.getElementById('newGeneralNotes').value = '';
    document.getElementById('selectedSolicitacao').value = '';
    document.getElementById('selectedTemplate').value = '';
    
    // Ocultar previews
    document.getElementById('solicitacaoPreview').style.display = 'none';
    document.getElementById('templatePreview').style.display = 'none';
}

// ===== SELEÇÃO DO MÉTODO DE CRIAÇÃO =====
window.selectCreationMethod = function(method) {
    selectedCreationMethod = method;
    
    // Atualizar cards visuais
    document.querySelectorAll('.method-card').forEach(card => {
        card.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    // Mostrar formulário correspondente
    document.querySelectorAll('.creation-form').forEach(form => {
        form.classList.remove('active');
    });
    
    const formId = method === 'manual' ? 'manualForm' : 
                   method === 'solicitacao' ? 'solicitacaoForm' : 'templateForm';
    document.getElementById(formId).classList.add('active');
};

// ===== GERAÇÃO DE NÚMERO =====
function generateQuotationNumber() {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const sequence = String(cotacoes.length + 1).padStart(4, '0');
    
    const number = `CT${year}${month}${sequence}`;
    document.getElementById('newQuotationNumber').value = number;
}

// ===== CARREGAMENTO DE SOLICITAÇÕES =====
function loadSolicitacoesForSelection() {
    const select = document.getElementById('selectedSolicitacao');
    select.innerHTML = '<option value="">Selecione uma solicitação...</option>';
    
    // Filtrar solicitações aprovadas que ainda não têm cotação
    const availableSolicitacoes = solicitacoes.filter(s => {
        const hasQuotation = cotacoes.some(c => c.solicitacaoId === s.id);
        return s.status === 'APROVADA' && !hasQuotation;
    });
    
    availableSolicitacoes
        .sort((a, b) => (b.numero || 0) - (a.numero || 0))
        .forEach(solicitacao => {
            const option = document.createElement('option');
            option.value = solicitacao.id;
            option.textContent = `SC-${solicitacao.numero || 'N/A'} - ${solicitacao.itens?.length || 0} itens`;
            select.appendChild(option);
        });
}

window.loadSolicitacaoPreview = function() {
    const solicitacaoId = document.getElementById('selectedSolicitacao').value;
    const preview = document.getElementById('solicitacaoPreview');
    
    if (!solicitacaoId) {
        preview.style.display = 'none';
        return;
    }
    
    const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
    if (!solicitacao || !solicitacao.itens) {
        preview.style.display = 'none';
        return;
    }
    
    const tbody = document.getElementById('solicitacaoItemsPreview');
    tbody.innerHTML = '';
    
    solicitacao.itens.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.codigo || 'N/A'}</td>
            <td>${item.descricao || 'N/A'}</td>
            <td>${item.unidade || 'UN'}</td>
            <td>${item.quantidade || 0}</td>
            <td>
                <input type="checkbox" checked onchange="toggleSolicitacaoItem(${index}, this.checked)">
            </td>
        `;
        tbody.appendChild(row);
    });
    
    preview.style.display = 'block';
};

window.toggleSolicitacaoItem = function(index, include) {
    // Implementar lógica para incluir/excluir itens
    console.log(`Item ${index} ${include ? 'incluído' : 'excluído'}`);
};

// ===== CARREGAMENTO DE TEMPLATES =====
function loadTemplatesForSelection() {
    const select = document.getElementById('selectedTemplate');
    select.innerHTML = '<option value="">Selecione uma cotação...</option>';
    
    // Usar cotações existentes como templates
    cotacoes
        .sort((a, b) => (b.numero || 0) - (a.numero || 0))
        .forEach(cotacao => {
            const option = document.createElement('option');
            option.value = cotacao.id;
            option.textContent = `${cotacao.numero || 'N/A'} - ${cotacao.itens?.length || 0} itens`;
            select.appendChild(option);
        });
}

window.loadTemplatePreview = function() {
    const templateId = document.getElementById('selectedTemplate').value;
    const preview = document.getElementById('templatePreview');
    
    if (!templateId) {
        preview.style.display = 'none';
        return;
    }
    
    const template = cotacoes.find(c => c.id === templateId);
    if (!template) {
        preview.style.display = 'none';
        return;
    }
    
    // Preencher informações do template
    document.getElementById('templateNumber').textContent = template.numero || 'N/A';
    document.getElementById('templateDate').textContent = formatDate(template.dataCriacao);
    document.getElementById('templateStatus').textContent = getStatusText(template.status);
    document.getElementById('templateItemsCount').textContent = template.itens?.length || 0;
    document.getElementById('templateSuppliersCount').textContent = template.fornecedores?.length || 0;
    document.getElementById('templateValue').textContent = `R$ ${formatCurrency(template.valorEstimado || 0)}`;
    
    preview.style.display = 'block';
};

// ===== CRIAÇÃO DA COTAÇÃO =====
window.createNewQuotation = async function() {
    try {
        let newQuotationData;
        
        switch (selectedCreationMethod) {
            case 'manual':
                newQuotationData = createManualQuotation();
                break;
            case 'solicitacao':
                newQuotationData = createFromSolicitacao();
                break;
            case 'template':
                newQuotationData = createFromTemplate();
                break;
            default:
                throw new Error('Método de criação inválido');
        }
        
        if (!newQuotationData) {
            return;
        }
        
        // Adicionar dados comuns
        newQuotationData.dataCriacao = Timestamp.now();
        newQuotationData.ultimaAtualizacao = Timestamp.now();
        newQuotationData.criadoPor = currentUser.nome;
        newQuotationData.status = 'ABERTA';
        newQuotationData.historico = [{
            data: Timestamp.now(),
            acao: 'CRIACAO',
            usuario: currentUser.nome,
            detalhes: `Cotação criada via ${selectedCreationMethod}`
        }];
        
        // Salvar no Firebase
        const docRef = await addDoc(collection(db, "cotacoes"), newQuotationData);
        
        showNotification('Cotação criada com sucesso!', 'success');
        closeModal('newQuotationModal');
        
        // Recarregar lista e abrir para edição
        await loadQuotations();
        editQuotation(docRef.id);
        
    } catch (error) {
        console.error('Erro ao criar cotação:', error);
        showNotification('Erro ao criar cotação: ' + error.message, 'error');
    }
};

function createManualQuotation() {
    const numero = document.getElementById('newQuotationNumber').value;
    const deadline = document.getElementById('newQuotationDeadline').value;
    
    if (!numero.trim()) {
        showNotification('Número da cotação é obrigatório', 'warning');
        return null;
    }
    
    return {
        numero: numero.trim(),
        dataLimite: deadline ? Timestamp.fromDate(new Date(deadline)) : null,
        prazoEntrega: parseInt(document.getElementById('newDeliveryTerm').value) || null,
        condicoesPagamento: document.getElementById('newPaymentTerms').value,
        localEntrega: document.getElementById('newDeliveryLocation').value,
        observacoesFornecedores: document.getElementById('newGeneralNotes').value,
        itens: [],
        fornecedores: [],
        valorEstimado: 0
    };
}

function createFromSolicitacao() {
    const solicitacaoId = document.getElementById('selectedSolicitacao').value;
    
    if (!solicitacaoId) {
        showNotification('Selecione uma solicitação de compras', 'warning');
        return null;
    }
    
    const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
    if (!solicitacao) {
        showNotification('Solicitação não encontrada', 'error');
        return null;
    }
    
    // Filtrar itens selecionados
    const selectedItems = [];
    const checkboxes = document.querySelectorAll('#solicitacaoItemsPreview input[type="checkbox"]');
    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked && solicitacao.itens[index]) {
            selectedItems.push({
                ...solicitacao.itens[index],
                valorUnitario: 0,
                ipi: 0,
                icms: 0
            });
        }
    });
    
    if (selectedItems.length === 0) {
        showNotification('Selecione pelo menos um item da solicitação', 'warning');
        return null;
    }
    
    return {
        numero: document.getElementById('newQuotationNumber').value,
        solicitacaoId: solicitacaoId,
        dataLimite: null,
        itens: selectedItems,
        fornecedores: [],
        valorEstimado: 0,
        observacoesFornecedores: `Cotação criada a partir da Solicitação SC-${solicitacao.numero}`
    };
}

function createFromTemplate() {
    const templateId = document.getElementById('selectedTemplate').value;
    
    if (!templateId) {
        showNotification('Selecione uma cotação template', 'warning');
        return null;
    }
    
    const template = cotacoes.find(c => c.id === templateId);
    if (!template) {
        showNotification('Template não encontrado', 'error');
        return null;
    }
    
    const copySuppliers = document.getElementById('copySuppliers').checked;
    const copyConditions = document.getElementById('copyConditions').checked;
    
    const newQuotation = {
        numero: document.getElementById('newQuotationNumber').value,
        itens: template.itens ? [...template.itens] : [],
        fornecedores: copySuppliers ? [...(template.fornecedores || [])] : [],
        valorEstimado: template.valorEstimado || 0
    };
    
    if (copyConditions) {
        newQuotation.prazoEntrega = template.prazoEntrega;
        newQuotation.condicoesPagamento = template.condicoesPagamento;
        newQuotation.tipoFrete = template.tipoFrete;
        newQuotation.localEntrega = template.localEntrega;
        newQuotation.garantia = template.garantia;
    }
    
    newQuotation.observacoesFornecedores = `Cotação criada a partir do template ${template.numero}`;
    
    return newQuotation;
}
