// ===== COTAÇÕES - NOVA COTAÇÃO =====

let selectedCreationMethod = 'manual';

// ===== ABRIR MODAL DE NOVA COTAÇÃO =====
window.openNewQuotationModal = async function() {
    try {
        // ✅ VALIDAÇÃO RIGOROSA DE PERMISSÕES
        if (currentUser.nivel < 2 && currentUser.id !== 'sistema') {
            // TODO: Registrar tentativa de acesso negado
            console.log('❌ Tentativa de criação negada:', currentUser.id);
            showNotification('❌ Você não tem permissão para criar cotações', 'error');
            return;
        }

        // ✅ VERIFICAR SE USUÁRIO ESTÁ AUTENTICADO
        if (!currentUser || !currentUser.nome) {
            showNotification('❌ Usuário não autenticado', 'error');
            return;
        }

        // Resetar formulário
        resetNewQuotationForm();

        // Carregar dados necessários
        loadSolicitacoesForSelection();
        loadTemplatesForSelection();

        // ✅ Gerar número automático (agora assíncrono)
        await generateQuotationNumber();

        // Definir data limite padrão (15 dias)
        const defaultDeadline = new Date();
        defaultDeadline.setDate(defaultDeadline.getDate() + 15);
        document.getElementById('newQuotationDeadline').value = defaultDeadline.toISOString().split('T')[0];

        // TODO: Registrar abertura do modal
        console.log('📝 Modal de nova cotação aberto por:', currentUser.nome);

        openModal('newQuotationModal');

    } catch (error) {
        console.error('❌ Erro ao abrir modal:', error);
        showNotification('❌ Erro ao abrir formulário: ' + error.message, 'error');
    }
};

function resetNewQuotationForm() {
    selectedCreationMethod = 'manual';
    
    // Resetar método de criação
    document.querySelectorAll('.method-card').forEach(card => {
        card.classList.remove('active');
    });
    document.querySelector('.method-card').classList.add('active');
    
    // Mostrar formulário manual
    document.querySelectorAll('.creation-form').forEach(form => {
        form.classList.remove('active');
    });
    document.getElementById('manualForm').classList.add('active');
    
    // Limpar campos
    document.getElementById('newQuotationNumber').value = '';
    document.getElementById('newQuotationDeadline').value = '';
    document.getElementById('newDeliveryTerm').value = '';
    document.getElementById('newPaymentTerms').value = '';
    document.getElementById('newDeliveryLocation').value = '';
    document.getElementById('newGeneralNotes').value = '';
    document.getElementById('selectedSolicitacao').value = '';
    document.getElementById('selectedTemplate').value = '';
    
    // Ocultar previews
    document.getElementById('solicitacaoPreview').style.display = 'none';
    document.getElementById('templatePreview').style.display = 'none';
}

// ===== SELEÇÃO DO MÉTODO DE CRIAÇÃO =====
window.selectCreationMethod = function(method) {
    selectedCreationMethod = method;
    
    // Atualizar cards visuais
    document.querySelectorAll('.method-card').forEach(card => {
        card.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    // Mostrar formulário correspondente
    document.querySelectorAll('.creation-form').forEach(form => {
        form.classList.remove('active');
    });
    
    const formId = method === 'manual' ? 'manualForm' : 
                   method === 'solicitacao' ? 'solicitacaoForm' : 'templateForm';
    document.getElementById(formId).classList.add('active');
};

// ===== GERAÇÃO DE NÚMERO =====
async function generateQuotationNumber() {
    try {
        // ✅ USAR CONTADOR CENTRALIZADO PARA CONSISTÊNCIA
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2); // Últimos 2 dígitos do ano
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0'); // Mês com 2 dígitos
        const anoMes = ano + mes; // Formato AAMM

        // Usar a coleção contadores para gerar numeração sequencial
        const counterRef = doc(db, "contadores", "cotacoes");
        const counterDoc = await getDoc(counterRef);

        let nextNumber = 1;

        if (counterDoc.exists()) {
            const counterData = counterDoc.data();

            // ✅ VERIFICAR SE MUDOU O MÊS/ANO (RESET AUTOMÁTICO)
            if (counterData.anoMes === anoMes) {
                // Mesmo mês, incrementar sequência
                nextNumber = counterData.valor + 1;
            } else {
                // Novo mês, resetar sequência
                nextNumber = 1;
            }
        } else {
            // Criar contador se não existir
            await setDoc(counterRef, {
                valor: 1,
                anoMes: anoMes,
                ultimaAtualizacao: Timestamp.now(),
                descricao: "Contador para numeração de cotações"
            });
        }

        // Atualizar contador
        await updateDoc(counterRef, {
            valor: nextNumber,
            anoMes: anoMes,
            ultimaAtualizacao: Timestamp.now()
        });

        // ✅ RETORNAR NÚMERO NO FORMATO PADRÃO: CT-AAMM-XXXX
        const numeroFormatado = `CT-${anoMes}-${nextNumber.toString().padStart(4, '0')}`;

        // TODO: Log da geração
        console.log('📄 Número de cotação gerado:', numeroFormatado);

        document.getElementById('newQuotationNumber').value = numeroFormatado;
        return numeroFormatado;

    } catch (error) {
        console.error('❌ Erro ao gerar número da cotação:', error);

        // ✅ FALLBACK SEGURO EM CASO DE ERRO
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2);
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0');
        const anoMes = ano + mes;
        const timestamp = Date.now().toString().slice(-4);
        const numeroFallback = `CT-${anoMes}-${timestamp}`;

        console.warn('⚠️ Usando numeração fallback:', numeroFallback);
        document.getElementById('newQuotationNumber').value = numeroFallback;
        return numeroFallback;
    }
}

// ===== CARREGAMENTO DE SOLICITAÇÕES =====
function loadSolicitacoesForSelection() {
    const select = document.getElementById('selectedSolicitacao');
    select.innerHTML = '<option value="">Selecione uma solicitação...</option>';
    
    // Filtrar solicitações aprovadas que ainda não têm cotação
    const availableSolicitacoes = solicitacoes.filter(s => {
        const hasQuotation = cotacoes.some(c => c.solicitacaoId === s.id);
        return s.status === 'APROVADA' && !hasQuotation;
    });
    
    availableSolicitacoes
        .sort((a, b) => (b.numero || 0) - (a.numero || 0))
        .forEach(solicitacao => {
            const option = document.createElement('option');
            option.value = solicitacao.id;
            option.textContent = `${solicitacao.numero || 'N/A'} - ${solicitacao.itens?.length || 0} itens`;
            select.appendChild(option);
        });
}

window.loadSolicitacaoPreview = function() {
    const solicitacaoId = document.getElementById('selectedSolicitacao').value;
    const preview = document.getElementById('solicitacaoPreview');
    
    if (!solicitacaoId) {
        preview.style.display = 'none';
        return;
    }
    
    const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
    if (!solicitacao || !solicitacao.itens) {
        preview.style.display = 'none';
        return;
    }
    
    const tbody = document.getElementById('solicitacaoItemsPreview');
    tbody.innerHTML = '';
    
    solicitacao.itens.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.codigo || 'N/A'}</td>
            <td>${item.descricao || 'N/A'}</td>
            <td>${item.unidade || 'UN'}</td>
            <td>${item.quantidade || 0}</td>
            <td>
                <input type="checkbox" checked onchange="toggleSolicitacaoItem(${index}, this.checked)">
            </td>
        `;
        tbody.appendChild(row);
    });
    
    preview.style.display = 'block';
};

window.toggleSolicitacaoItem = function(index, include) {
    // Implementar lógica para incluir/excluir itens
    console.log(`Item ${index} ${include ? 'incluído' : 'excluído'}`);
};

// ===== CARREGAMENTO DE TEMPLATES =====
function loadTemplatesForSelection() {
    const select = document.getElementById('selectedTemplate');
    select.innerHTML = '<option value="">Selecione uma cotação...</option>';
    
    // Usar cotações existentes como templates
    cotacoes
        .sort((a, b) => (b.numero || 0) - (a.numero || 0))
        .forEach(cotacao => {
            const option = document.createElement('option');
            option.value = cotacao.id;
            option.textContent = `${cotacao.numero || 'N/A'} - ${cotacao.itens?.length || 0} itens`;
            select.appendChild(option);
        });
}

window.loadTemplatePreview = function() {
    const templateId = document.getElementById('selectedTemplate').value;
    const preview = document.getElementById('templatePreview');
    
    if (!templateId) {
        preview.style.display = 'none';
        return;
    }
    
    const template = cotacoes.find(c => c.id === templateId);
    if (!template) {
        preview.style.display = 'none';
        return;
    }
    
    // Preencher informações do template
    document.getElementById('templateNumber').textContent = template.numero || 'N/A';
    document.getElementById('templateDate').textContent = formatDate(template.dataCriacao);
    document.getElementById('templateStatus').textContent = getStatusText(template.status);
    document.getElementById('templateItemsCount').textContent = template.itens?.length || 0;
    document.getElementById('templateSuppliersCount').textContent = template.fornecedores?.length || 0;
    document.getElementById('templateValue').textContent = `R$ ${formatCurrency(template.valorEstimado || 0)}`;
    
    preview.style.display = 'block';
};

// ===== CRIAÇÃO DA COTAÇÃO =====
window.createNewQuotation = async function() {
    try {
        // ✅ VALIDAÇÃO RIGOROSA DE PERMISSÕES
        if (currentUser.nivel < 2 && currentUser.id !== 'sistema') {
            console.log('❌ Tentativa de criação negada:', currentUser.id);
            showNotification('❌ Você não tem permissão para criar cotações', 'error');
            return;
        }

        // ✅ VERIFICAR AUTENTICAÇÃO
        if (!currentUser || !currentUser.nome) {
            showNotification('❌ Usuário não autenticado', 'error');
            return;
        }

        // ✅ VALIDAR MÉTODO DE CRIAÇÃO
        if (!selectedCreationMethod || !['manual', 'solicitacao', 'template'].includes(selectedCreationMethod)) {
            showNotification('❌ Método de criação inválido', 'error');
            return;
        }

        let newQuotationData;

        switch (selectedCreationMethod) {
            case 'manual':
                newQuotationData = createManualQuotation();
                break;
            case 'solicitacao':
                newQuotationData = createFromSolicitacao();
                break;
            case 'template':
                newQuotationData = createFromTemplate();
                break;
            default:
                throw new Error('Método de criação inválido');
        }

        if (!newQuotationData) {
            return;
        }

        // ✅ VALIDAÇÕES ADICIONAIS DE SEGURANÇA
        if (!newQuotationData.numero || newQuotationData.numero.trim().length < 3) {
            showNotification('❌ Número da cotação deve ter pelo menos 3 caracteres', 'error');
            return;
        }

        // ✅ VERIFICAR SE NÚMERO JÁ EXISTE
        const numeroExistente = cotacoes.find(c => c.numero === newQuotationData.numero.trim());
        if (numeroExistente) {
            showNotification('❌ Já existe uma cotação com este número', 'error');
            return;
        }

        // ✅ ADICIONAR DADOS SEGUROS
        newQuotationData.dataCriacao = Timestamp.now();
        newQuotationData.ultimaAtualizacao = Timestamp.now();
        newQuotationData.criadoPor = currentUser.nome;
        newQuotationData.criadoPorId = currentUser.id;
        newQuotationData.status = 'ABERTA';
        newQuotationData.historico = [{
            data: Timestamp.now(),
            acao: 'CRIACAO',
            usuario: currentUser.nome,
            usuarioId: currentUser.id,
            detalhes: `Cotação criada via ${selectedCreationMethod}`
        }];

        // ✅ SALVAR NO FIREBASE COM VALIDAÇÃO
        const docRef = await addDoc(collection(db, "cotacoes"), newQuotationData);

        // TODO: Registrar auditoria da criação
        console.log('✅ Cotação criada:', docRef.id, 'por:', currentUser.nome);

        showNotification('✅ Cotação criada com sucesso!', 'success');
        closeModal('newQuotationModal');

        // Recarregar lista (forçar reload) e abrir para edição
        await loadQuotations(true);

        // Mostrar feedback visual adicional
        showSuccessMessage("Cotação criada com sucesso!");

        editQuotation(docRef.id);

    } catch (error) {
        console.error('❌ Erro ao criar cotação:', error);

        // TODO: Registrar erro na auditoria
        console.log('❌ Erro na criação:', error.message);

        showNotification('❌ Erro ao criar cotação: ' + error.message, 'error');
    }
};

function createManualQuotation() {
    // ✅ VALIDAÇÕES RIGOROSAS DE ENTRADA
    const numero = document.getElementById('newQuotationNumber').value;
    const deadline = document.getElementById('newQuotationDeadline').value;

    if (!numero || !numero.trim()) {
        showNotification('❌ Número da cotação é obrigatório', 'warning');
        return null;
    }

    // ✅ VALIDAR FORMATO DO NÚMERO
    const numeroLimpo = numero.trim();
    if (numeroLimpo.length < 3) {
        showNotification('❌ Número da cotação deve ter pelo menos 3 caracteres', 'warning');
        return null;
    }

    // ✅ VALIDAR DATA LIMITE
    let dataLimiteValidada = null;
    if (deadline) {
        const dataLimite = new Date(deadline);
        const hoje = new Date();
        hoje.setHours(0, 0, 0, 0);

        if (dataLimite < hoje) {
            showNotification('❌ Data limite não pode ser anterior a hoje', 'warning');
            return null;
        }

        dataLimiteValidada = Timestamp.fromDate(dataLimite);
    }

    // ✅ VALIDAR PRAZO DE ENTREGA
    const prazoEntregaInput = document.getElementById('newDeliveryTerm').value;
    let prazoEntrega = null;
    if (prazoEntregaInput) {
        const prazo = parseInt(prazoEntregaInput);
        if (isNaN(prazo) || prazo < 0 || prazo > 365) {
            showNotification('❌ Prazo de entrega deve ser entre 0 e 365 dias', 'warning');
            return null;
        }
        prazoEntrega = prazo;
    }

    // ✅ SANITIZAR CAMPOS DE TEXTO
    const condicoesPagamento = document.getElementById('newPaymentTerms').value?.trim() || '';
    const localEntrega = document.getElementById('newDeliveryLocation').value?.trim() || '';
    const observacoes = document.getElementById('newGeneralNotes').value?.trim() || '';

    return {
        numero: numeroLimpo,
        dataLimite: dataLimiteValidada,
        prazoEntrega: prazoEntrega,
        condicoesPagamento: condicoesPagamento,
        localEntrega: localEntrega,
        observacoesFornecedores: observacoes,
        itens: [],
        fornecedores: [],
        valorEstimado: 0
    };
}

function createFromSolicitacao() {
    // ✅ VALIDAÇÕES RIGOROSAS
    const solicitacaoId = document.getElementById('selectedSolicitacao').value;

    if (!solicitacaoId || typeof solicitacaoId !== 'string') {
        showNotification('❌ Selecione uma solicitação de compras válida', 'warning');
        return null;
    }

    const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
    if (!solicitacao) {
        showNotification('❌ Solicitação não encontrada', 'error');
        return null;
    }

    // ✅ VALIDAR STATUS DA SOLICITAÇÃO
    if (solicitacao.status !== 'APROVADA') {
        showNotification('❌ Apenas solicitações aprovadas podem gerar cotações', 'error');
        return null;
    }

    // ✅ VERIFICAR SE JÁ EXISTE COTAÇÃO PARA ESTA SOLICITAÇÃO
    const cotacaoExistente = cotacoes.find(c => c.solicitacaoId === solicitacaoId);
    if (cotacaoExistente) {
        showNotification('❌ Já existe uma cotação para esta solicitação', 'warning');
        return null;
    }

    // ✅ VALIDAR ITENS DA SOLICITAÇÃO
    if (!solicitacao.itens || solicitacao.itens.length === 0) {
        showNotification('❌ A solicitação não possui itens válidos', 'error');
        return null;
    }

    // ✅ FILTRAR ITENS SELECIONADOS COM VALIDAÇÃO
    const selectedItems = [];
    const checkboxes = document.querySelectorAll('#solicitacaoItemsPreview input[type="checkbox"]');
    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked && solicitacao.itens[index]) {
            const item = solicitacao.itens[index];

            // Validar dados do item
            if (!item.codigo || !item.descricao || !item.quantidade || item.quantidade <= 0) {
                console.warn('Item inválido ignorado:', item);
                return;
            }

            selectedItems.push({
                ...item,
                valorUnitario: 0,
                ipi: 0,
                icms: 0,
                origem: 'solicitacao'
            });
        }
    });

    if (selectedItems.length === 0) {
        showNotification('❌ Selecione pelo menos um item válido da solicitação', 'warning');
        return null;
    }

    // ✅ VALIDAR NÚMERO DA COTAÇÃO
    const numero = document.getElementById('newQuotationNumber').value?.trim();
    if (!numero || numero.length < 3) {
        showNotification('❌ Número da cotação é obrigatório', 'warning');
        return null;
    }

    return {
        numero: numero,
        solicitacaoId: solicitacaoId,
        dataLimite: null,
        itens: selectedItems,
        fornecedores: [],
        valorEstimado: 0,
        observacoesFornecedores: `Cotação criada a partir da Solicitação ${solicitacao.numero || 'N/A'} por ${currentUser.nome}`
    };
}

function createFromTemplate() {
    const templateId = document.getElementById('selectedTemplate').value;
    
    if (!templateId) {
        showNotification('Selecione uma cotação template', 'warning');
        return null;
    }
    
    const template = cotacoes.find(c => c.id === templateId);
    if (!template) {
        showNotification('Template não encontrado', 'error');
        return null;
    }
    
    const copySuppliers = document.getElementById('copySuppliers').checked;
    const copyConditions = document.getElementById('copyConditions').checked;
    
    const newQuotation = {
        numero: document.getElementById('newQuotationNumber').value,
        itens: template.itens ? [...template.itens] : [],
        fornecedores: copySuppliers ? [...(template.fornecedores || [])] : [],
        valorEstimado: template.valorEstimado || 0
    };
    
    if (copyConditions) {
        newQuotation.prazoEntrega = template.prazoEntrega;
        newQuotation.condicoesPagamento = template.condicoesPagamento;
        newQuotation.tipoFrete = template.tipoFrete;
        newQuotation.localEntrega = template.localEntrega;
        newQuotation.garantia = template.garantia;
    }
    
    newQuotation.observacoesFornecedores = `Cotação criada a partir do template ${template.numero}`;
    
    return newQuotation;
}
