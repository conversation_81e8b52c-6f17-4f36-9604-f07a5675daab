<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Produtos Sem Pai</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .report-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .report-title {
            font-size: 24px;
            color: var(--primary-color);
        }
        .report-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .report-table th, .report-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .report-table th {
            background-color: var(--primary-color);
            color: white;
        }
        .report-table tr:hover {
            background-color: #f5f5f5;
        }
        .product-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .type-PA { background-color: #e3f2fd; color: #1976d2; }
        .type-SP { background-color: #fff3e0; color: #f57c00; }
        .type-MP { background-color: #e8f5e9; color: #388e3c; }
        .type-SV { background-color: #fce4ec; color: #c2185b; }
        .type-MO { background-color: #f3e5f5; color: #7b1fa2; }
        .type-HR { background-color: #e0f7fa; color: #0097a7; }
        .no-results {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .export-btn {
            background-color: var(--success-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .export-btn:hover {
            background-color: var(--success-color-dark);
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">Relatório de Produtos Sem Pai</h1>
            <button class="export-btn" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Exportar para Excel
            </button>
        </div>

        <div class="report-filters">
            <select id="tipoFilter" onchange="filterProducts()">
                <option value="">Todos os Tipos</option>
                <option value="PA">Produto Acabado</option>
                <option value="SP">Semi-Produto</option>
                <option value="MP">Matéria Prima</option>
                <option value="SV">Serviço</option>
                <option value="MO">Mão de Obra</option>
                <option value="HR">Hora</option>
            </select>
        </div>

        <table class="report-table">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Descrição</th>
                    <th>Tipo</th>
                    <th>Unidade</th>
                    <th>Grupo</th>
                    <th>Família</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="productsTableBody">
                <!-- Produtos serão inseridos aqui via JavaScript -->
            </tbody>
        </table>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let estruturas = [];

        window.onload = async function() {
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

            await loadData();
            displayProducts();
        };

        async function loadData() {
            try {
                const [produtosSnap, estruturasSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturas"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados.");
            }
        }

        function displayProducts(filteredProducts = null) {
            const tbody = document.getElementById('productsTableBody');
            tbody.innerHTML = '';

            const productsToDisplay = filteredProducts || produtos;
            const produtosSemPai = productsToDisplay.filter(produto => {
                // Verifica se o produto não é usado como componente em nenhuma estrutura
                return !estruturas.some(estrutura => 
                    estrutura.componentes.some(comp => comp.componentId === produto.id)
                );
            });

            if (produtosSemPai.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="no-results">
                            Nenhum produto encontrado sem estrutura pai.
                        </td>
                    </tr>
                `;
                return;
            }

            produtosSemPai.forEach(produto => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${produto.codigo}</td>
                    <td>${produto.descricao}</td>
                    <td><span class="product-type type-${produto.tipo}">${produto.tipo}</span></td>
                    <td>${produto.unidade}</td>
                    <td>${produto.grupo || '-'}</td>
                    <td>${produto.familia || '-'}</td>
                    <td>${produto.status || 'Ativo'}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        window.filterProducts = function() {
            const tipoFilter = document.getElementById('tipoFilter').value;
            const filteredProducts = tipoFilter 
                ? produtos.filter(p => p.tipo === tipoFilter)
                : produtos;
            displayProducts(filteredProducts);
        };

        window.exportToExcel = function() {
            const tipoFilter = document.getElementById('tipoFilter').value;
            const filteredProducts = tipoFilter 
                ? produtos.filter(p => p.tipo === tipoFilter)
                : produtos;

            const produtosSemPai = filteredProducts.filter(produto => {
                return !estruturas.some(estrutura => 
                    estrutura.componentes.some(comp => comp.componentId === produto.id)
                );
            });

            // Criar conteúdo do Excel
            let csvContent = "Código,Descrição,Tipo,Unidade,Grupo,Família,Status\n";

            produtosSemPai.forEach(produto => {
                const row = [
                    produto.codigo,
                    produto.descricao,
                    produto.tipo,
                    produto.unidade,
                    produto.grupo || '-',
                    produto.familia || '-',
                    produto.status || 'Ativo'
                ].map(field => `"${field}"`).join(',');
                csvContent += row + "\n";
            });

            // Criar e baixar arquivo
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement("a");
            const url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", "produtos_sem_pai.csv");
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };
    </script>
</body>
</html> 