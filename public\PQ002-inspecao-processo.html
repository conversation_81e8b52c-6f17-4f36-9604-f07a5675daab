<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ002 - Inspeção no Processo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #8e44ad;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #8e44ad;
            margin-right: 10px;
            width: 20px;
        }

        .process-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .process-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .process-card:hover {
            transform: translateY(-5px);
        }

        .process-card.producao { border-left-color: #3498db; }
        .process-card.montagem { border-left-color: #e67e22; }
        .process-card.acabamento { border-left-color: #27ae60; }
        .process-card.embalagem { border-left-color: #9b59b6; }

        .process-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .process-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            text-transform: uppercase;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-aguardando {
            background: #fff3cd;
            color: #856404;
        }

        .status-inspecao {
            background: #cce5ff;
            color: #004085;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .programacao-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .programacao-info h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .inspecao-details {
            display: grid;
            gap: 20px;
        }

        .detail-section {
            padding: 15px;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
        }

        .detail-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .detail-grid div {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .timeline {
            display: grid;
            gap: 15px;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .timeline-item i {
            color: #3498db;
            font-size: 1.2em;
            margin-top: 2px;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .process-grid {
                grid-template-columns: 1fr;
            }
            
            .filters {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .btn-action {
                width: 100%;
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .form-grid, .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 PQ002 - Inspeção no Processo</h1>
            <p>Controle de qualidade durante a produção</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Garantir qualidade durante todas as etapas do processo produtivo</li>
                    <li><i class="fas fa-cogs"></i><strong>Processo:</strong> Inspeções programadas e aleatórias em pontos críticos da produção</li>
                    <li><i class="fas fa-check-circle"></i><strong>Resultado:</strong> Identificação precoce de desvios e correções imediatas</li>
                    <li><i class="fas fa-chart-line"></i><strong>Benefício:</strong> Redução de retrabalho e melhoria contínua da qualidade</li>
                </ul>
            </div>

            <!-- Cards dos Processos -->
            <div class="process-grid">
                <div class="process-card producao">
                    <h4>🔧 Produção</h4>
                    <div class="process-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="producaoAguardando">0</div>
                            <div class="stat-label">Aguardando</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="producaoInspecao">0</div>
                            <div class="stat-label">Em Inspeção</div>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" onclick="abrirProcesso('producao')">
                        <i class="fas fa-eye"></i> Ver Inspeções
                    </button>
                </div>

                <div class="process-card montagem">
                    <h4>🔩 Montagem</h4>
                    <div class="process-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="montagemAguardando">0</div>
                            <div class="stat-label">Aguardando</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="montagemInspecao">0</div>
                            <div class="stat-label">Em Inspeção</div>
                        </div>
                    </div>
                    <button class="btn btn-warning btn-full" onclick="abrirProcesso('montagem')">
                        <i class="fas fa-eye"></i> Ver Inspeções
                    </button>
                </div>

                <div class="process-card acabamento">
                    <h4>✨ Acabamento</h4>
                    <div class="process-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="acabamentoAguardando">0</div>
                            <div class="stat-label">Aguardando</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="acabamentoInspecao">0</div>
                            <div class="stat-label">Em Inspeção</div>
                        </div>
                    </div>
                    <button class="btn btn-success btn-full" onclick="abrirProcesso('acabamento')">
                        <i class="fas fa-eye"></i> Ver Inspeções
                    </button>
                </div>

                <div class="process-card embalagem">
                    <h4>📦 Embalagem</h4>
                    <div class="process-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="embalagemAguardando">0</div>
                            <div class="stat-label">Aguardando</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="embalagemInspecao">0</div>
                            <div class="stat-label">Em Inspeção</div>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" onclick="abrirProcesso('embalagem')">
                        <i class="fas fa-eye"></i> Ver Inspeções
                    </button>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="processoFilter">Processo</label>
                    <select id="processoFilter">
                        <option value="">Todos os Processos</option>
                        <option value="producao">Produção</option>
                        <option value="montagem">Montagem</option>
                        <option value="acabamento">Acabamento</option>
                        <option value="embalagem">Embalagem</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter">
                        <option value="">Todos os Status</option>
                        <option value="AGUARDANDO">Aguardando</option>
                        <option value="EM_INSPECAO">Em Inspeção</option>
                        <option value="APROVADO">Aprovado</option>
                        <option value="REJEITADO">Rejeitado</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="ordemProducao">Ordem de Produção</label>
                    <input type="text" id="ordemProducao" placeholder="Número da OP">
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="novaInspecaoProcesso()">
                    <i class="fas fa-plus"></i> Nova Inspeção
                </button>
                <button class="btn btn-primary" onclick="programarInspecoes()">
                    <i class="fas fa-calendar"></i> Programar Inspeções
                </button>
                <button class="btn btn-warning" onclick="relatorioProcesso()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando inspeções de processo...</p>
            </div>

            <!-- Tabela de Inspeções -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>OP</th>
                            <th>Produto</th>
                            <th>Processo</th>
                            <th>Etapa</th>
                            <th>Quantidade</th>
                            <th>Data Programada</th>
                            <th>Status</th>
                            <th>Inspetor</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-cogs"></i>
                <h3>Nenhuma inspeção de processo encontrada</h3>
                <p>Não há inspeções de processo programadas no momento.</p>
                <button class="btn btn-primary" onclick="novaInspecaoProcesso()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Programar Primeira Inspeção
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let inspecoesProcesso = [];
        let ordensProducao = [];

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);
                
                // Carregar inspeções de processo e ordens de produção
                const [inspecoesSnap, ordensSnap] = await Promise.all([
                    getDocs(collection(db, "inspecoesProcesso")),
                    getDocs(collection(db, "ordensProducao"))
                ]);

                inspecoesProcesso = inspecoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ002 - Dados carregados:', {
                    inspecoes: inspecoesProcesso.length,
                    ordens: ordensProducao.length
                });

                updateProcessStats();
                renderTable();
                showLoading(false);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ002:', error);
                showLoading(false);
                showError('Erro ao carregar dados de inspeção de processo');
            }
        }

        function updateProcessStats() {
            const processos = ['producao', 'montagem', 'acabamento', 'embalagem'];
            
            processos.forEach(processo => {
                const aguardando = inspecoesProcesso.filter(i => 
                    i.processo === processo && i.status === 'AGUARDANDO'
                ).length;
                
                const inspecao = inspecoesProcesso.filter(i => 
                    i.processo === processo && i.status === 'EM_INSPECAO'
                ).length;

                document.getElementById(`${processo}Aguardando`).textContent = aguardando;
                document.getElementById(`${processo}Inspecao`).textContent = inspecao;
            });
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (inspecoesProcesso.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = inspecoesProcesso.map(inspecao => {
                const ordem = ordensProducao.find(o => o.id === inspecao.ordemProducaoId);
                const dataProgramada = inspecao.dataProgramada ? 
                    new Date(inspecao.dataProgramada.seconds * 1000).toLocaleDateString() : 'N/A';

                return `
                    <tr>
                        <td><strong>${ordem?.numero || 'N/A'}</strong></td>
                        <td>${inspecao.produtoNome || 'N/A'}</td>
                        <td>${inspecao.processo || 'N/A'}</td>
                        <td>${inspecao.etapa || 'N/A'}</td>
                        <td>${inspecao.quantidade || 0} ${inspecao.unidade || ''}</td>
                        <td>${dataProgramada}</td>
                        <td><span class="status-badge status-${inspecao.status?.toLowerCase() || 'aguardando'}">${inspecao.status || 'AGUARDANDO'}</span></td>
                        <td>${inspecao.inspetor || 'N/A'}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarInspecao('${inspecao.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${inspecao.status === 'AGUARDANDO' ? `
                                    <button class="btn btn-warning btn-action" onclick="iniciarInspecao('${inspecao.id}')" title="Iniciar">
                                        <i class="fas fa-play"></i>
                                    </button>
                                ` : ''}
                                ${inspecao.status === 'EM_INSPECAO' ? `
                                    <button class="btn btn-success btn-action" onclick="finalizarInspecao('${inspecao.id}')" title="Finalizar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Funções globais
        window.abrirProcesso = function(processo) {
            document.getElementById('processoFilter').value = processo;
            aplicarFiltros();
        };

        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar filtros
        };

        window.novaInspecaoProcesso = function() {
            mostrarModalNovaInspecaoProcesso();
        };

        // Mostrar modal de nova inspeção de processo
        function mostrarModalNovaInspecaoProcesso() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus"></i> Nova Inspeção de Processo</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="formNovaInspecaoProcesso">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="numeroOP">
                                        <i class="fas fa-hashtag"></i>
                                        Número da OP
                                    </label>
                                    <input type="text" id="numeroOP" placeholder="Ex: OP-123456" required>
                                </div>
                                <div class="form-group">
                                    <label for="processoInspecao">
                                        <i class="fas fa-cogs"></i>
                                        Processo
                                    </label>
                                    <select id="processoInspecao" required>
                                        <option value="">Selecione o processo</option>
                                        <option value="producao">Produção</option>
                                        <option value="montagem">Montagem</option>
                                        <option value="acabamento">Acabamento</option>
                                        <option value="embalagem">Embalagem</option>
                                        <option value="soldagem">Soldagem</option>
                                        <option value="pintura">Pintura</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="produtoInspecao">
                                        <i class="fas fa-box"></i>
                                        Produto
                                    </label>
                                    <input type="text" id="produtoInspecao" placeholder="Código ou nome do produto" required>
                                </div>
                                <div class="form-group">
                                    <label for="quantidadeInspecao">
                                        <i class="fas fa-calculator"></i>
                                        Quantidade
                                    </label>
                                    <input type="number" id="quantidadeInspecao" min="1" required>
                                </div>
                                <div class="form-group">
                                    <label for="tipoInspecaoProcesso">
                                        <i class="fas fa-search"></i>
                                        Tipo de Inspeção
                                    </label>
                                    <select id="tipoInspecaoProcesso" required>
                                        <option value="">Selecione o tipo</option>
                                        <option value="PRIMEIRA_PECA">Primeira Peça</option>
                                        <option value="DURANTE_PROCESSO">Durante Processo</option>
                                        <option value="FINAL_PROCESSO">Final do Processo</option>
                                        <option value="AMOSTRAGEM">Por Amostragem</option>
                                        <option value="COMPLETA">Inspeção Completa</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="inspetorProcesso">
                                        <i class="fas fa-user"></i>
                                        Inspetor Responsável
                                    </label>
                                    <select id="inspetorProcesso" required>
                                        <option value="">Selecione o inspetor</option>
                                        <option value="inspetor1">João Silva</option>
                                        <option value="inspetor2">Maria Santos</option>
                                        <option value="inspetor3">Pedro Costa</option>
                                        <option value="inspetor4">Ana Oliveira</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group" style="margin-top: 20px;">
                                <label for="observacoesProcesso">
                                    <i class="fas fa-comment"></i>
                                    Observações
                                </label>
                                <textarea id="observacoesProcesso" rows="3" placeholder="Observações sobre a inspeção..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button class="btn btn-success" onclick="criarInspecaoProcesso()">
                            <i class="fas fa-check"></i> Criar Inspeção
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Criar inspeção de processo
        window.criarInspecaoProcesso = async function() {
            const form = document.getElementById('formNovaInspecaoProcesso');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            try {
                const novaInspecao = {
                    numero: 'INS-PROC-' + Date.now(),
                    numeroOP: document.getElementById('numeroOP').value,
                    processo: document.getElementById('processoInspecao').value,
                    produto: document.getElementById('produtoInspecao').value,
                    quantidade: parseInt(document.getElementById('quantidadeInspecao').value),
                    tipo: document.getElementById('tipoInspecaoProcesso').value,
                    inspetorResponsavel: document.getElementById('inspetorProcesso').value,
                    observacoes: document.getElementById('observacoesProcesso').value,
                    status: 'AGUARDANDO',
                    dataCriacao: Timestamp.now(),
                    usuarioCriacao: 'usuario_atual'
                };

                await addDoc(collection(db, "inspecoesProcesso"), novaInspecao);

                showAlert('success', '✅ Inspeção de processo criada com sucesso!');
                fecharModal();
                loadData(); // Recarregar dados

            } catch (error) {
                console.error('❌ Erro ao criar inspeção:', error);
                showAlert('danger', 'Erro ao criar inspeção de processo');
            }
        };

        window.programarInspecoes = function() {
            mostrarModalProgramacao();
        };

        // Mostrar modal de programação
        function mostrarModalProgramacao() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-calendar"></i> Programar Inspeções</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="programacao-info">
                            <h4>📋 Configurações de Programação</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="frequenciaInspecao">
                                        <i class="fas fa-clock"></i>
                                        Frequência
                                    </label>
                                    <select id="frequenciaInspecao">
                                        <option value="DIARIA">Diária</option>
                                        <option value="SEMANAL">Semanal</option>
                                        <option value="QUINZENAL">Quinzenal</option>
                                        <option value="MENSAL">Mensal</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="processosProgramacao">
                                        <i class="fas fa-cogs"></i>
                                        Processos
                                    </label>
                                    <select id="processosProgramacao" multiple>
                                        <option value="producao">Produção</option>
                                        <option value="montagem">Montagem</option>
                                        <option value="acabamento">Acabamento</option>
                                        <option value="embalagem">Embalagem</option>
                                    </select>
                                </div>
                            </div>
                            <p><i class="fas fa-info-circle"></i> As inspeções serão criadas automaticamente conforme a programação.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                        <button class="btn btn-primary" onclick="salvarProgramacao()">
                            <i class="fas fa-save"></i> Salvar Programação
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Salvar programação
        window.salvarProgramacao = function() {
            showAlert('success', '📅 Programação de inspeções salva com sucesso!');
            fecharModal();
        };

        window.relatorioProcesso = function() {
            const dados = inspecoes.map(inspecao => ({
                'Número': inspecao.numero,
                'OP': inspecao.numeroOP,
                'Processo': inspecao.processo,
                'Produto': inspecao.produto,
                'Quantidade': inspecao.quantidade,
                'Tipo': inspecao.tipo,
                'Status': inspecao.status,
                'Inspetor': inspecao.inspetorResponsavel,
                'Data Criação': inspecao.dataCriacao ? new Date(inspecao.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'
            }));

            if (dados.length === 0) {
                showAlert('warning', 'Não há dados para exportar');
                return;
            }

            // Converter para CSV
            const headers = Object.keys(dados[0]);
            const csvContent = [
                headers.join(','),
                ...dados.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\\n');

            // Download do arquivo
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `inspecoes_processo_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showAlert('success', '📊 Relatório de processo exportado com sucesso!');
        };

        window.visualizarInspecao = function(id) {
            const inspecao = inspecoes.find(i => i.id === id);
            if (!inspecao) {
                showAlert('danger', 'Inspeção não encontrada');
                return;
            }

            mostrarModalVisualizacao(inspecao);
        };

        window.iniciarInspecao = async function(id) {
            if (!confirm('Deseja iniciar esta inspeção?')) return;

            try {
                await updateDoc(doc(db, "inspecoesProcesso", id), {
                    status: 'EM_INSPECAO',
                    dataInicio: Timestamp.now(),
                    usuarioInicio: 'usuario_atual'
                });

                showAlert('success', '▶️ Inspeção iniciada com sucesso!');
                loadData();

            } catch (error) {
                console.error('❌ Erro ao iniciar inspeção:', error);
                showAlert('danger', 'Erro ao iniciar inspeção');
            }
        };

        window.finalizarInspecao = async function(id) {
            const resultado = prompt('Resultado da inspeção (APROVADO/REJEITADO):');
            if (!resultado || !['APROVADO', 'REJEITADO'].includes(resultado.toUpperCase())) {
                showAlert('warning', 'Resultado inválido. Use APROVADO ou REJEITADO.');
                return;
            }

            try {
                await updateDoc(doc(db, "inspecoesProcesso", id), {
                    status: resultado.toUpperCase(),
                    dataFinalizacao: Timestamp.now(),
                    usuarioFinalizacao: 'usuario_atual'
                });

                showAlert('success', `✅ Inspeção finalizada como ${resultado.toUpperCase()}!`);
                loadData();

            } catch (error) {
                console.error('❌ Erro ao finalizar inspeção:', error);
                showAlert('danger', 'Erro ao finalizar inspeção');
            }
        };

        // Mostrar modal de visualização
        function mostrarModalVisualizacao(inspecao) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-eye"></i> Visualizar Inspeção ${inspecao.numero}</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="inspecao-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-info-circle"></i> Dados Gerais</h4>
                                <div class="detail-grid">
                                    <div><strong>Número:</strong> ${inspecao.numero}</div>
                                    <div><strong>Status:</strong> <span class="status-badge status-${inspecao.status?.toLowerCase()}">${inspecao.status}</span></div>
                                    <div><strong>OP:</strong> ${inspecao.numeroOP}</div>
                                    <div><strong>Processo:</strong> ${inspecao.processo}</div>
                                    <div><strong>Produto:</strong> ${inspecao.produto}</div>
                                    <div><strong>Quantidade:</strong> ${inspecao.quantidade}</div>
                                    <div><strong>Tipo:</strong> ${inspecao.tipo}</div>
                                    <div><strong>Inspetor:</strong> ${inspecao.inspetorResponsavel}</div>
                                </div>
                            </div>

                            ${inspecao.observacoes ? `
                                <div class="detail-section">
                                    <h4><i class="fas fa-comment"></i> Observações</h4>
                                    <p>${inspecao.observacoes}</p>
                                </div>
                            ` : ''}

                            <div class="detail-section">
                                <h4><i class="fas fa-clock"></i> Histórico</h4>
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <i class="fas fa-plus-circle"></i>
                                        <div>
                                            <strong>Criação:</strong> ${inspecao.dataCriacao ? new Date(inspecao.dataCriacao.seconds * 1000).toLocaleString() : 'N/A'}
                                            <br><small>Por: ${inspecao.usuarioCriacao || 'N/A'}</small>
                                        </div>
                                    </div>
                                    ${inspecao.dataInicio ? `
                                        <div class="timeline-item">
                                            <i class="fas fa-play-circle"></i>
                                            <div>
                                                <strong>Início:</strong> ${new Date(inspecao.dataInicio.seconds * 1000).toLocaleString()}
                                                <br><small>Por: ${inspecao.usuarioInicio || 'N/A'}</small>
                                            </div>
                                        </div>
                                    ` : ''}
                                    ${inspecao.dataFinalizacao ? `
                                        <div class="timeline-item">
                                            <i class="fas fa-check-circle"></i>
                                            <div>
                                                <strong>Finalização:</strong> ${new Date(inspecao.dataFinalizacao.seconds * 1000).toLocaleString()}
                                                <br><small>Por: ${inspecao.usuarioFinalizacao || 'N/A'}</small>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                        ${inspecao.status === 'AGUARDANDO' ? `
                            <button class="btn btn-warning" onclick="fecharModal(); iniciarInspecao('${inspecao.id}');">
                                <i class="fas fa-play"></i> Iniciar Inspeção
                            </button>
                        ` : ''}
                        ${inspecao.status === 'EM_INSPECAO' ? `
                            <button class="btn btn-success" onclick="fecharModal(); finalizarInspecao('${inspecao.id}');">
                                <i class="fas fa-check"></i> Finalizar Inspeção
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Fechar modal
        window.fecharModal = function() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        };

        // Função para mostrar alertas
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer') || document.body;
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${message}
            `;

            alertContainer.appendChild(alert);
            alert.style.display = 'block';

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
