<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Executivo - Compras TOTVS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-info {
            text-align: right;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .dashboard-content {
            padding: 30px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .kpi-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 100%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .kpi-card.revenue {
            border-left-color: #27ae60;
        }

        .kpi-card.orders {
            border-left-color: #3498db;
        }

        .kpi-card.suppliers {
            border-left-color: #f39c12;
        }

        .kpi-card.savings {
            border-left-color: #9b59b6;
        }

        .kpi-card.performance {
            border-left-color: #e74c3c;
        }

        .kpi-card.leadtime {
            border-left-color: #1abc9c;
        }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .kpi-title {
            font-size: 0.95rem;
            color: #7f8c8d;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .kpi-icon {
            font-size: 2rem;
            opacity: 0.7;
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .kpi-subtitle {
            font-size: 0.9rem;
            color: #95a5a6;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .trend-up {
            color: #27ae60;
        }

        .trend-down {
            color: #e74c3c;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.2rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tables-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th {
            background: #f8f9fa;
            color: #2c3e50;
            font-weight: 600;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .supplier-rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .star {
            color: #f39c12;
        }

        .filters-bar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 0.9rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-icon {
            color: #f39c12;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-chart-line"></i>
                Dashboard Executivo - Compras
            </h1>
            <div class="header-info">
                <div><strong>Período:</strong> Janeiro 2024</div>
                <div><strong>Última atualização:</strong> <span id="lastUpdate"></span></div>
            </div>
        </div>

        <div class="dashboard-content">
            <!-- Filtros -->
            <div class="filters-bar">
                <div class="filter-group">
                    <label>Período</label>
                    <select id="periodoFiltro">
                        <option value="mes">Este Mês</option>
                        <option value="trimestre">Trimestre</option>
                        <option value="ano">Ano</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Centro de Custo</label>
                    <select id="centroCustoFiltro">
                        <option value="">Todos</option>
                        <option value="PROD">Produção</option>
                        <option value="MANUT">Manutenção</option>
                        <option value="ADM">Administrativo</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Categoria</label>
                    <select id="categoriaFiltro">
                        <option value="">Todas</option>
                        <option value="MP">Matéria Prima</option>
                        <option value="COMP">Componentes</option>
                        <option value="SERV">Serviços</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="aplicarFiltros()">
                    <i class="fas fa-filter"></i> Aplicar
                </button>
            </div>

            <!-- Alertas -->
            <div class="alert">
                <i class="fas fa-exclamation-triangle alert-icon"></i>
                <div>
                    <strong>Atenção:</strong> 5 pedidos com prazo de entrega vencido. 
                    <a href="#" style="color: #3498db;">Ver detalhes</a>
                </div>
            </div>

            <!-- KPIs -->
            <div class="kpi-grid">
                <div class="kpi-card revenue">
                    <div class="kpi-header">
                        <div class="kpi-title">Volume de Compras</div>
                        <i class="fas fa-dollar-sign kpi-icon" style="color: #27ae60;"></i>
                    </div>
                    <div class="kpi-value">R$ 2.847.320</div>
                    <div class="kpi-subtitle">
                        <i class="fas fa-arrow-up trend-up"></i>
                        +12.5% vs mês anterior
                    </div>
                </div>

                <div class="kpi-card orders">
                    <div class="kpi-header">
                        <div class="kpi-title">Pedidos Processados</div>
                        <i class="fas fa-shopping-cart kpi-icon" style="color: #3498db;"></i>
                    </div>
                    <div class="kpi-value">247</div>
                    <div class="kpi-subtitle">
                        <i class="fas fa-arrow-up trend-up"></i>
                        +8.2% vs mês anterior
                    </div>
                </div>

                <div class="kpi-card suppliers">
                    <div class="kpi-header">
                        <div class="kpi-title">Fornecedores Ativos</div>
                        <i class="fas fa-truck kpi-icon" style="color: #f39c12;"></i>
                    </div>
                    <div class="kpi-value">89</div>
                    <div class="kpi-subtitle">
                        <i class="fas fa-arrow-down trend-down"></i>
                        -2.1% vs mês anterior
                    </div>
                </div>

                <div class="kpi-card savings">
                    <div class="kpi-header">
                        <div class="kpi-title">Economia Gerada</div>
                        <i class="fas fa-piggy-bank kpi-icon" style="color: #9b59b6;"></i>
                    </div>
                    <div class="kpi-value">R$ 142.850</div>
                    <div class="kpi-subtitle">
                        <i class="fas fa-arrow-up trend-up"></i>
                        5.02% de economia
                    </div>
                </div>

                <div class="kpi-card performance">
                    <div class="kpi-header">
                        <div class="kpi-title">Taxa de Aprovação</div>
                        <i class="fas fa-check-circle kpi-icon" style="color: #e74c3c;"></i>
                    </div>
                    <div class="kpi-value">94.2%</div>
                    <div class="kpi-subtitle">
                        <i class="fas fa-arrow-up trend-up"></i>
                        +1.8% vs mês anterior
                    </div>
                </div>

                <div class="kpi-card leadtime">
                    <div class="kpi-header">
                        <div class="kpi-title">Lead Time Médio</div>
                        <i class="fas fa-clock kpi-icon" style="color: #1abc9c;"></i>
                    </div>
                    <div class="kpi-value">12.4</div>
                    <div class="kpi-subtitle">
                        <i class="fas fa-arrow-down trend-up"></i>
                        -0.8 dias vs mês anterior
                    </div>
                </div>
            </div>

            <!-- Gráficos -->
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        Evolução de Compras (Últimos 12 Meses)
                    </div>
                    <canvas id="comprasChart" width="400" height="200"></canvas>
                </div>

                <div class="chart-container">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        Compras por Categoria
                    </div>
                    <canvas id="categoriaChart" width="300" height="300"></canvas>
                </div>
            </div>

            <!-- Tabelas -->
            <div class="tables-grid">
                <div class="table-container">
                    <div class="chart-title">
                        <i class="fas fa-list"></i>
                        Pedidos Pendentes de Aprovação
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Pedido</th>
                                <th>Fornecedor</th>
                                <th>Valor</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>PC20240015</td>
                                <td>Suprimentos SA</td>
                                <td>R$ 25.430</td>
                                <td><span class="status-badge status-pending">Pendente</span></td>
                            </tr>
                            <tr>
                                <td>PC20240016</td>
                                <td>Industrial Ltda</td>
                                <td>R$ 18.750</td>
                                <td><span class="status-badge status-approved">Aprovado</span></td>
                            </tr>
                            <tr>
                                <td>PC20240017</td>
                                <td>Materiais Brasil</td>
                                <td>R$ 32.100</td>
                                <td><span class="status-badge status-pending">Pendente</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="table-container">
                    <div class="chart-title">
                        <i class="fas fa-star"></i>
                        Top Fornecedores (Performance)
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Fornecedor</th>
                                <th>Avaliação</th>
                                <th>Pedidos</th>
                                <th>Pontualidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Suprimentos SA</td>
                                <td>
                                    <div class="supplier-rating">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        4.8
                                    </div>
                                </td>
                                <td>45</td>
                                <td>96%</td>
                            </tr>
                            <tr>
                                <td>Industrial Ltda</td>
                                <td>
                                    <div class="supplier-rating">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star"></i>
                                        4.2
                                    </div>
                                </td>
                                <td>32</td>
                                <td>89%</td>
                            </tr>
                            <tr>
                                <td>Materiais Brasil</td>
                                <td>
                                    <div class="supplier-rating">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star"></i>
                                        4.0
                                    </div>
                                </td>
                                <td>28</td>
                                <td>92%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Atualizar timestamp
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString('pt-BR');

        // Gráfico de Evolução de Compras
        const comprasCtx = document.getElementById('comprasChart').getContext('2d');
        new Chart(comprasCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                datasets: [{
                    label: 'Volume de Compras (R$ mil)',
                    data: [2100, 2300, 2150, 2400, 2600, 2450, 2700, 2550, 2800, 2650, 2900, 2847],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Gráfico de Categorias
        const categoriaCtx = document.getElementById('categoriaChart').getContext('2d');
        new Chart(categoriaCtx, {
            type: 'doughnut',
            data: {
                labels: ['Matéria Prima', 'Componentes', 'Serviços', 'Embalagens', 'Outros'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        '#3498db',
                        '#27ae60',
                        '#f39c12',
                        '#e74c3c',
                        '#9b59b6'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        function aplicarFiltros() {
            alert('Filtros aplicados! Dashboard atualizado com os novos critérios.');
        }
    </script>
</body>
</html>
