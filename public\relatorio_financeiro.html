
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório Financeiro</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      font-size: 10px;
      color: #000;
    }
    .container {
      width: 210mm;
      height: 297mm;
      margin: 0 auto;
      border: 2px solid #000;
      padding: 10mm;
      border-radius: 8px;
      box-sizing: border-box;
      position: relative;
      page-break-after: always;
    }
    .box {
      border: 1px solid #000;
      border-radius: 8px;
      padding: 3mm;
      margin-bottom: 3mm;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
    }
    .header-left img {
      height: 15mm;
    }
    .header-right {
      text-align: right;
    }
    .header-right h1 {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
      text-transform: uppercase;
    }
    .summary-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 3mm;
    }
    .summary-item {
      flex: 1;
      text-align: center;
      padding: 2mm;
    }
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 3mm;
    }
    .table th, .table td {
      border: 1px solid #000;
      padding: 2mm;
      text-align: left;
    }
    .table th {
      background-color: #f0f0f0;
    }
    .total-row {
      font-weight: bold;
      background-color: #f0f0f0;
    }
    @media print {
      body {
        margin: 0;
      }
      .container {
        border: none;
      }
      .no-print {
        display: none;
      }
    }
  </style>
</head>
<body>
  <div class="no-print" style="text-align: center; margin: 10px;">
    <button onclick="window.print()">Imprimir</button>
    <button onclick="window.location.href='index.html'">Voltar</button>
  </div>

  <div id="report-container"></div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, query, where, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function loadData() {
      const now = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(now.getDate() - 30);

      const [contasPagar, contasReceber] = await Promise.all([
        getDocs(collection(db, "contasAPagar")),
        getDocs(collection(db, "contasAReceber"))
      ]);

      return {
        pagar: contasPagar.docs.map(doc => ({ id: doc.id, ...doc.data() })),
        receber: contasReceber.docs.map(doc => ({ id: doc.id, ...doc.data() }))
      };
    }

    function formatCurrency(value) {
      return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
    }

    function formatDate(timestamp) {
      if (!timestamp) return 'N/A';
      return new Date(timestamp.seconds * 1000).toLocaleDateString();
    }

    async function generateReport() {
      const data = await loadData();
      const container = document.getElementById('report-container');
      
      // Calcular totais
      const totalPagar = data.pagar.reduce((sum, conta) => sum + conta.valorTotal, 0);
      const totalReceber = data.receber.reduce((sum, conta) => sum + conta.valorTotal, 0);
      const saldo = totalReceber - totalPagar;

      const reportHtml = `
        <div class="container">
          <div class="box header">
            <div class="header-left">
              <img id="company-logo" src="logo-naliteck.png" alt="Logo">
            </div>
            <div class="header-right">
              <h1>Relatório Financeiro</h1>
              <p>Data: ${new Date().toLocaleDateString()}</p>
            </div>
          </div>

          <div class="box">
            <div class="summary-box">
              <div class="summary-item">
                <h3>Total a Pagar</h3>
                <p style="color: red">${formatCurrency(totalPagar)}</p>
              </div>
              <div class="summary-item">
                <h3>Total a Receber</h3>
                <p style="color: green">${formatCurrency(totalReceber)}</p>
              </div>
              <div class="summary-item">
                <h3>Saldo</h3>
                <p style="color: ${saldo >= 0 ? 'green' : 'red'}">${formatCurrency(saldo)}</p>
              </div>
              <div class="summary-item">
                <h3>Por Centro de Custo</h3>
                <select id="centroCustoFilter" onchange="filterByCentroCusto()">
                  <option value="">Todos</option>
                </select>
              </div>
            </div>
          </div>

          <div class="box">
            <h3>Contas a Pagar</h3>
            <table class="table">
              <thead>
                <tr>
                  <th>Vencimento</th>
                  <th>Documento</th>
                  <th>Fornecedor</th>
                  <th>Valor</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                ${data.pagar.map(conta => `
                  <tr>
                    <td>${formatDate(conta.dataVencimento)}</td>
                    <td>${conta.numeroDocumento}</td>
                    <td>${conta.fornecedorNome || 'N/A'}</td>
                    <td>${formatCurrency(conta.valorTotal)}</td>
                    <td>${conta.status}</td>
                  </tr>
                `).join('')}
                <tr class="total-row">
                  <td colspan="3">Total</td>
                  <td>${formatCurrency(totalPagar)}</td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="box">
            <h3>Contas a Receber</h3>
            <table class="table">
              <thead>
                <tr>
                  <th>Vencimento</th>
                  <th>Documento</th>
                  <th>Cliente</th>
                  <th>Valor</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                ${data.receber.map(conta => `
                  <tr>
                    <td>${formatDate(conta.dataVencimento)}</td>
                    <td>${conta.numeroDocumento}</td>
                    <td>${conta.clienteNome || 'N/A'}</td>
                    <td>${formatCurrency(conta.valorTotal)}</td>
                    <td>${conta.status}</td>
                  </tr>
                `).join('')}
                <tr class="total-row">
                  <td colspan="3">Total</td>
                  <td>${formatCurrency(totalReceber)}</td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      `;

      container.innerHTML = reportHtml;
    }

    document.addEventListener('DOMContentLoaded', generateReport);
  </script>
</body>
</html>
