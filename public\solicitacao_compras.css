:root {
  --primary-color: #0854a0;
  --primary-hover: #0a4d8c;
  --secondary-color: #f0f3f6;
  --border-color: #d4d4d4;
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #107e3e;
  --success-hover: #0d6e36;
  --danger-color: #bb0000;
  --danger-hover: #a30000;
  --warning-color: #e9730c;
  --header-bg: #354a5f;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f7f7f7;
  color: var(--text-color);
}

.container {
  width: 95%;
  max-width: 1500px;
  margin: 30px auto;
  padding: 0;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  background-color: var(--header-bg);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  padding: 0 5px;
}

.form-col {
  flex: 1;
  min-width: 200px;
  margin-bottom: 8px;
}

label {
  display: block;
  margin-bottom: 8px;
  color: #4a5568;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.2px;
}

input,
select,
textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: #fff;
  color: #2d3748;
  height: 40px;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
  transition: all 0.2s ease;
}

/* Estilização dos selects */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%234a5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 32px;
}

/* Estilo para o modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
  backdrop-filter: blur(3px);
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex !important;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 95%;
  max-width: 900px; /* Aumentado de 750px para 900px (+20%) */
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: modalFadeIn 0.3s ease-out;
  margin: 0 auto;
  padding: 0;
  border: none;
  display: flex;
  flex-direction: column;

  /* Estilo da barra de rolagem personalizada */
  scrollbar-width: thin;
  scrollbar-color: #a0aec0 #f7fafc;

  /* Melhorias visuais */
  transition: all 0.3s ease;
}

/* Melhorias no cabeçalho do modal */
.modal-header {
  padding: 24px 32px;
  border-bottom: 1px solid #edf2f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8fafc;
  border-radius: 12px 12px 0 0;
  flex-shrink: 0;
  position: relative;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #2d3748;
  font-weight: 600;
  flex: 1;
}

/* Melhorias no corpo do modal */
.modal-body {
  padding: 32px;
  background-color: #fff;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* Melhorias no rodapé do modal */
.modal-footer {
  padding: 24px 32px;
  border-top: 1px solid #edf2f7;
  background-color: #f8fafc;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

/* Ajustes para telas menores */
@media (max-width: 768px) {
  .modal {
    padding: 10px;
    align-items: flex-start;
  }

  .modal-content {
    width: 100%;
    max-width: none;
    margin: 20px auto;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }

  .modal-header h2 {
    font-size: 1.25rem;
  }
}

/* Estilo da barra de rolagem para WebKit (Chrome, Safari) */
.modal-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* Estilo para o botão de fechar */
.close-button {
  font-size: 24px;
  font-weight: bold;
  color: #718096;
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1;
  background: none;
  border: none;
  padding: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  margin-left: 16px;
}

.close-button:hover {
  color: #4a5568;
  background-color: #f0f4f8;
  transform: scale(1.1);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #edf2f7;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  border-radius: 10px 10px 0 0;
}

.modal-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 20px;
  font-weight: 600;
}

.modal-body {
  padding: 24px;
}

/* Estilo para os campos obrigatórios */
.required-field::after {
  content: ' *';
  color: #e53e3e;
}

/* Estilo para campos de busca */
.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: #fff;
  color: #2d3748;
  height: 40px;
  padding-right: 36px;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
  outline: none;
}

/* Estilo para os resultados da busca */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: none;
}

.search-result-item {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f8fafc;
}

.search-result-code {
  font-weight: 600;
  color: #2d3748;
  margin-right: 10px;
  font-family: 'Roboto Mono', monospace;
}

.search-result-desc {
  color: #64748b;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* Estilo para campos com ícone */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 12px;
  color: #64748b;
  font-size: 14px;
  pointer-events: none;
}

.input-with-icon input {
  padding-left: 30px;
}

/* Estilo para o valor total */
.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  padding: 8px 0;
}

/* Estilo para mensagens de ajuda */
.help-text {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
  line-height: 1.4;
}

/* Melhorias para os botões */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.btn i {
  font-size: 14px;
  margin-right: 0;
  width: 16px;
  text-align: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: #f8fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

/* Ajustes para responsividade */
@media (max-width: 768px) {
  .form-col {
    min-width: 100% !important;
  }

  .modal-content {
    width: 95%;
    margin: 10px auto;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}

/* Botões do formulário */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding: 20px 24px;
  border-top: 1px solid #edf2f7;
  background: #f8fafc;
  border-radius: 0 0 10px 10px;
  position: sticky;
  bottom: 0;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #f0f4f8;
  color: #4a5568;
  border: 1px solid #d2dce8;
}

.btn-secondary:hover {
  background-color: #e2e8f0;
  transform: translateY(-1px);
}

/* Melhorias para o número da solicitação */
.request-number {
  font-size: 15px;
  color: #4a5568;
  margin-bottom: 20px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  display: inline-block;
  border-left: 3px solid var(--primary-color);
}

/* Melhorias para os campos do formulário */
.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #edf2f7;
}

/* Mensagem de erro */
.error-message {
  color: #e53e3e;
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
  display: none;
}

/* Aviso de edição */
.edit-warning {
  background-color: #fffaf0;
  border-left: 4px solid #dd6b20;
  color: #9c4221;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

textarea {
  resize: vertical;
  min-height: 80px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

#submitButton {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

#submitButton:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.requests-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.requests-table th,
.requests-table td {
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  text-align: left;
}

.requests-table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-secondary);
  text-align: center;
}

.requests-table tr:hover {
  background-color: #f8f9fa;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.action-icon {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 0 2px;
  font-size: 16px;
}

.view-icon {
  background-color: #0854a0;
  color: white;
}

.print-icon {
  background-color: #6c757d;
  color: white;
}

.approve-icon {
  background-color: #28a745;
  color: white;
}

.reject-icon {
  background-color: #dc3545;
  color: white;
}

.edit-icon {
  background-color: #ffc107;
  color: black;
}

.quote-icon {
  background-color: #17a2b8;
  color: white;
}

.status-pendente {
  background-color: #ffc107;
  color: black;
}

.status-aprovada {
  background-color: #28a745;
  color: white;
}

.status-rejeitada {
  background-color: #dc3545;
  color: white;
}

.status-cotacao {
  background-color: #17a2b8;
  color: white;
}

.filter-row th {
  padding: 5px;
}

.filter-input {
  width: 100%;
  padding: 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.sort-header {
  cursor: pointer;
}

.sort-header:hover {
  background-color: #f0f0f0;
}

.status-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 12px;
}

.status-indicator.critical {
  color: #dc3545;
}

.status-indicator.warning {
  color: #ffc107;
}

.status-indicator.attention {
  color: #17a2b8;
}

.status-indicator.normal {
  color: #28a745;
}

.status-pendente {
  background-color: #ffc107;
  color: #000;
}
.status-aprovada {
  background-color: #28a745;
  color: #fff;
}
.status-rejeitada {
  background-color: #dc3545;
  color: #fff;
}
.status-cotacao {
  background-color: #17a2b8;
  color: #fff;
}

/* Configurações específicas para modais grandes (detalhes) */
.modal.large-modal .modal-content {
  max-width: 1440px; /* 20% maior que 1200px */
  width: 98%;
}

.modal.large-modal .modal-body {
  padding: 24px;
}

/* Personalização da barra de rolagem */
.modal-content::-webkit-scrollbar {
  width: 10px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.item-descricao {
  min-width: 300px;
  width: 100%;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.modal-footer {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--border-color);
  gap: 10px;
}

.close-button {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.items-table th,
.items-table td {
  padding: 8px;
  border: 1px solid var(--border-color);
  text-align: left;
}

.items-table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-secondary);
}

.history-section {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--secondary-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.history-item {
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
}

.history-item:last-child {
  border-bottom: none;
}

.edit-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  border: 1px solid #ffeeba;
}

.access-denied {
  text-align: center;
  padding: 50px 20px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 8px;
  margin: 50px auto;
  max-width: 600px;
  border: 1px solid #f5c6cb;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: none;
  border: 1px solid #f5c6cb;
}

.conversion-info {
  font-size: 12px;
  color: var(--primary-color);
  margin-top: 5px;
}

.required::after {
  content: "*";
  color: var(--danger-color);
  margin-left: 4px;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.action-buttons button {
  padding: 5px 10px;
  font-size: 12px;
}

.view-button {
  background-color: var(--primary-color);
  color: white;
}

.print-button {
  background-color: #6c757d;
  color: white;
}

.approve-button {
  background-color: var(--success-color);
  color: white;
}

.reject-button {
  background-color: var(--danger-color);
  color: white;
}

.edit-button {
  background-color: #ffc107;
  color: #000;
}

.create-quotation-button {
  background-color: #17a2b8;
  color: white;
}
#linkedItemsModal input[type="search"] {
  margin-bottom: 10px;
  padding: 5px;
}
.item-checkbox {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

/* Estilos para badges */
.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.badge-info {
  background-color: #17a2b8;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

/* Estilos para tipos de solicitação */
.tipo-normal {
  color: #0854a0;
}

.tipo-planejada {
  color: #107e3e;
}

.tipo-emergencial {
  color: #bb0000;
}

/* Estilos para solicitações do MRP */
.mrp-request {
  background-color: rgba(23, 162, 184, 0.05);
}

.mrp-request:hover {
  background-color: rgba(23, 162, 184, 0.1);
}

/* Estilos para o modal de detalhes */
.detail-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.detail-section h3 {
  color: var(--primary-color);
  font-size: 16px;
  margin-bottom: 10px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 12px;
}

.detail-value {
  font-size: 14px;
  color: var(--text-color);
}

/* Estilos para histórico */
.history-item {
  padding: 10px;
  border-left: 3px solid var(--primary-color);
  margin-bottom: 10px;
  background-color: var(--secondary-color);
}

.history-date {
  font-size: 12px;
  color: var(--text-secondary);
}

.history-user {
  font-weight: 500;
  color: var(--primary-color);
}

.history-action {
  margin-top: 5px;
}

/* Estilos para informações do MRP */
.mrp-info {
  background-color: rgba(23, 162, 184, 0.1);
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

.mrp-info h4 {
  color: #17a2b8;
  font-size: 14px;
  margin-bottom: 10px;
}

.mrp-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mrp-info li {
  font-size: 13px;
  margin-bottom: 5px;
}

/* Estilos específicos para campos de itens */
.items-table td input,
.items-table td textarea,
.items-table td select {
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 8px;
  font-size: 13px;
  box-sizing: border-box;
}

.items-table td input:focus,
.items-table td textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
  outline: none;
}

.items-table .item-codigo {
  min-width: 120px;
  max-width: 120px;
}

.items-table .item-descricao {
  min-width: 200px;
  background-color: #f8fafc;
}

.items-table .item-quantidade {
  min-width: 100px;
  max-width: 100px;
  text-align: right;
}

.items-table .item-unidade {
  min-width: 60px;
  max-width: 60px;
  background-color: #f8fafc;
  text-align: center;
  padding: 8px 4px;
}

.items-table .item-unidade-interna {
  min-width: 80px;
  max-width: 80px;
  background-color: #f0f4f8;
  text-align: center;
  padding: 8px 4px;
}

.items-table .item-unidade-compra {
  min-width: 100px;
  max-width: 100px;
}

.items-table .item-quantidade-compra {
  min-width: 120px;
  max-width: 120px;
}

.items-table .item-quantidade-compra-input {
  text-align: right;
}

.items-table .item-observacoes {
  min-width: 150px;
  resize: vertical;
  min-height: 40px;
  padding: 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 13px;
}

/* Estilos para ações em lote */
.batch-actions {
  margin-bottom: 20px;
  padding: 10px;
  background-color: var(--secondary-color);
  border-radius: 4px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.batch-actions select {
  max-width: 200px;
}

/* Ajustes para a tabela de itens dentro do modal de detalhes */
#detailsModal .items-table td {
  white-space: normal; /* Permitir quebra de linha */
  word-break: break-word; /* Quebrar palavras longas */
}

#detailsModal .items-table td:nth-child(1) { /* Código */
  min-width: 80px;
}

#detailsModal .items-table td:nth-child(2) { /* Descrição */
  min-width: 200px;
}

#detailsModal .items-table td:nth-child(3) { /* Quantidade */
  min-width: 100px;
}

#detailsModal .items-table td:nth-child(4) { /* Unidade */
  min-width: 50px;
}

#detailsModal .items-table td:nth-child(5) { /* Valor Unit. */
  min-width: 100px;
}

#detailsModal .items-table td:nth-child(6) { /* Total */
  min-width: 100px;
}

/* Configurações específicas para modal de fornecedores */
.supplier-modal .modal-content {
  max-width: 900px; /* 20% maior que 750px */
  width: 90%;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-button {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-secondary);
  cursor: pointer;
  border: none;
  background: none;
}

.close-button:hover {
  color: var(--text-color);
}

.supplier-select-group {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.supplier-select-group select {
  flex: 1;
}

.selected-suppliers-list {
  margin-top: 20px;
}

.selected-suppliers-list .selected-supplier {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--secondary-color);
  border-radius: 4px;
  margin-bottom: 8px;
}

.selected-suppliers-list .selected-supplier:last-child {
  margin-bottom: 0;
}

.supplier-modal-header h2 {
  margin: 0;
  color: var(--primary-color);
}

.supplier-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 10px;
}

.supplier-row {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.supplier-row select {
  flex: 1;
}

.supplier-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}