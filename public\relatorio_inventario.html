
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Inventário</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #0854a0;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --header-bg: #354a5f;
        }

        * { box-sizing: border-box; margin: 0; padding: 0; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 500;
            color: var(--text-color);
        }

        .filter-group select,
        .filter-group input {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .inventory-table th,
        .inventory-table td {
            padding: 12px;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        .inventory-table th {
            background-color: var(--secondary-color);
            font-weight: 600;
        }

        .inventory-table tr:hover {
            background-color: #f8f9fa;
        }

        .summary {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--secondary-color);
            border-radius: 4px;
        }

        .numeric {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Relatório de Inventário</h1>
            <div>
                <button class="btn btn-primary" onclick="exportToExcel()">Exportar Excel</button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label>Grupo</label>
                <select id="grupoFilter" onchange="filterInventory()"></select>
            </div>
            <div class="filter-group">
                <label>Família</label>
                <select id="familiaFilter" onchange="filterInventory()"></select>
            </div>
            <div class="filter-group">
                <label>Tipo</label>
                <select id="tipoFilter" onchange="filterInventory()">
                    <option value="">Todos</option>
                    <option value="MP">Matéria Prima</option>
                    <option value="PA">Produto Acabado</option>
                    <option value="PI">Produto Intermediário</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Armazém</label>
                <select id="armazemFilter" onchange="filterInventory()"></select>
            </div>
            <div class="filter-group">
                <label>Buscar</label>
                <input type="text" id="searchInput" onkeyup="filterInventory()" placeholder="Código ou descrição...">
            </div>
        </div>

        <div class="buttons">
            <button class="btn btn-primary" onclick="printReport()">Imprimir</button>
            <button class="btn btn-primary" onclick="showSummary()">Resumo</button>
        </div>

        <table class="inventory-table">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Descrição</th>
                    <th>Tipo</th>
                    <th>Grupo</th>
                    <th>Família</th>
                    <th>Armazém</th>
                    <th>Unidade</th>
                    <th class="numeric">Saldo</th>
                    <th class="numeric">Custo Unit.</th>
                    <th class="numeric">Valor Total</th>
                    <th>Última Movimentação</th>
                </tr>
            </thead>
            <tbody id="inventoryTableBody"></tbody>
        </table>

        <div id="summary" class="summary" style="display: none;">
            <h3>Resumo do Inventário</h3>
            <p>Total de Itens: <span id="totalItems">0</span></p>
            <p>Valor Total: R$ <span id="totalValue">0.00</span></p>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let estoques = [];
        let grupos = [];
        let familias = [];
        let armazens = [];
        let movimentacoes = [];

        window.onload = async function() {
            await loadData();
            setupFilters();
            await loadInventory();
        };

        async function loadData() {
            try {
                const [produtosSnap, estoquesSnap, gruposSnap, familiasSnap, armazensSnap, movimentacoesSnap] = 
                    await Promise.all([
                        getDocs(collection(db, "produtos")),
                        getDocs(collection(db, "estoques")),
                        getDocs(collection(db, "grupos")),
                        getDocs(collection(db, "familias")),
                        getDocs(collection(db, "armazens")),
                        getDocs(collection(db, "movimentacoesEstoque"))
                    ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                alert("Erro ao carregar dados do inventário");
            }
        }

        function setupFilters() {
            const grupoSelect = document.getElementById('grupoFilter');
            const familiaSelect = document.getElementById('familiaFilter');
            const armazemSelect = document.getElementById('armazemFilter');

            grupoSelect.innerHTML = '<option value="">Todos os Grupos</option>';
            grupos.forEach(grupo => {
                grupoSelect.innerHTML += `<option value="${grupo.id}">${grupo.codigoGrupo} - ${grupo.nomeGrupo}</option>`;
            });

            familiaSelect.innerHTML = '<option value="">Todas as Famílias</option>';
            familias.forEach(familia => {
                familiaSelect.innerHTML += `<option value="${familia.id}">${familia.codigoFamilia} - ${familia.nomeFamilia}</option>`;
            });

            armazemSelect.innerHTML = '<option value="">Todos os Armazéns</option>';
            armazens.forEach(armazem => {
                armazemSelect.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.descricao}</option>`;
            });
        }

        window.filterInventory = function() {
            const grupoId = document.getElementById('grupoFilter').value;
            const familiaId = document.getElementById('familiaFilter').value;
            const tipo = document.getElementById('tipoFilter').value;
            const armazemId = document.getElementById('armazemFilter').value;
            const searchText = document.getElementById('searchInput').value.toLowerCase();

            const filteredProdutos = produtos.filter(produto => {
                const matchesGrupo = !grupoId || produto.grupo === grupoId;
                const matchesFamilia = !familiaId || produto.familia === familiaId;
                const matchesTipo = !tipo || produto.tipo === tipo;
                const matchesSearch = !searchText || 
                    produto.codigo.toLowerCase().includes(searchText) || 
                    produto.descricao.toLowerCase().includes(searchText);

                return matchesGrupo && matchesFamilia && matchesTipo && matchesSearch;
            });

            renderInventory(filteredProdutos, armazemId);
        };

        function renderInventory(filteredProdutos, armazemId) {
            const tableBody = document.getElementById('inventoryTableBody');
            tableBody.innerHTML = '';
            let totalValue = 0;
            let totalItems = 0;

            filteredProdutos.forEach(produto => {
                const produtoEstoques = estoques.filter(e => 
                    e.produtoId === produto.id && 
                    (!armazemId || e.armazemId === armazemId)
                );

                if (produtoEstoques.length === 0) {
                    const row = createInventoryRow(produto, null, null);
                    tableBody.appendChild(row);
                    totalItems++;
                } else {
                    produtoEstoques.forEach(estoque => {
                        const row = createInventoryRow(produto, estoque, armazemId);
                        tableBody.appendChild(row);
                        totalValue += (estoque.saldo || 0) * (produto.custo || 0);
                        totalItems++;
                    });
                }
            });

            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalValue').textContent = totalValue.toFixed(2);
        }

        function createInventoryRow(produto, estoque, armazemId) {
            const row = document.createElement('tr');
            const grupo = grupos.find(g => g.id === produto.grupo);
            const familia = familias.find(f => f.id === produto.familia);
            const armazem = armazens.find(a => a.id === (estoque?.armazemId || armazemId));
            const ultimaMovimentacao = movimentacoes
                .filter(m => m.produtoId === produto.id)
                .sort((a, b) => b.dataHora.seconds - a.dataHora.seconds)[0];

            const saldo = estoque?.saldo || 0;
            const custoUnitario = produto.custo || 0;
            const valorTotal = saldo * custoUnitario;

            row.innerHTML = `
                <td>${produto.codigo}</td>
                <td>${produto.descricao}</td>
                <td>${produto.tipo}</td>
                <td>${grupo ? `${grupo.codigoGrupo} - ${grupo.nomeGrupo}` : '-'}</td>
                <td>${familia ? `${familia.codigoFamilia} - ${familia.nomeFamilia}` : '-'}</td>
                <td>${armazem ? `${armazem.codigo} - ${armazem.descricao}` : '-'}</td>
                <td>${produto.unidade}</td>
                <td class="numeric">${saldo.toFixed(3)}</td>
                <td class="numeric">R$ ${custoUnitario.toFixed(2)}</td>
                <td class="numeric">R$ ${valorTotal.toFixed(2)}</td>
                <td>${ultimaMovimentacao ? 
                    new Date(ultimaMovimentacao.dataHora.seconds * 1000).toLocaleString() : 
                    '-'}</td>
            `;

            return row;
        }

        window.showSummary = function() {
            const summary = document.getElementById('summary');
            summary.style.display = summary.style.display === 'none' ? 'block' : 'none';
        };

        window.exportToExcel = function() {
            const rows = [];
            const headers = [
                'Código', 'Descrição', 'Tipo', 'Grupo', 'Família', 'Armazém',
                'Unidade', 'Saldo', 'Custo Unitário', 'Valor Total', 'Última Movimentação'
            ];
            rows.push(headers);

            document.querySelectorAll('#inventoryTableBody tr').forEach(tr => {
                const row = [];
                tr.querySelectorAll('td').forEach(td => row.push(td.textContent));
                rows.push(row);
            });

            const ws = XLSX.utils.aoa_to_sheet(rows);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Inventário");

            const fileName = `inventario_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
        };

        window.printReport = function() {
            window.print();
        };

        await loadInventory();
    </script>
</body>
</html>
