/**
 * CONFIGURAÇÃO DA IA - SISTEMA NALITECK
 * Configurações e parâmetros para o sistema de IA de monitoramento
 */

export const IAConfig = {
    // ===== CONFIGURAÇÕES GERAIS =====
    general: {
        monitoringInterval: 30000, // 30 segundos
        analysisInterval: 1800000, // 30 minutos
        dataRetentionDays: 180, // 6 meses
        maxAlertsHistory: 100,
        maxRecommendations: 10
    },

    // ===== THRESHOLDS DE ALERTAS =====
    alertThresholds: {
        // Atrasos
        delayDays: {
            warning: 2,
            critical: 5
        },
        
        // Variação de preços
        priceVariation: {
            warning: 15, // %
            critical: 30  // %
        },
        
        // Taxa de erro
        errorRate: {
            warning: 3,  // %
            critical: 8  // %
        },
        
        // Eficiência mínima
        efficiency: {
            warning: 85, // %
            critical: 75 // %
        },
        
        // Tempo de processo
        processTime: {
            warning: 10, // dias
            critical: 15 // dias
        },
        
        // Performance de fornecedor
        supplierPerformance: {
            warning: 80, // %
            critical: 60 // %
        }
    },

    // ===== CONFIGURAÇÕES DE MODELOS IA =====
    models: {
        // Modelo de previsão de atrasos
        delayPrediction: {
            enabled: true,
            confidence: 0.7, // 70%
            lookAheadDays: 7,
            factors: [
                'supplierHistory',
                'orderValue',
                'deliveryTime',
                'seasonality',
                'marketConditions'
            ]
        },
        
        // Modelo de detecção de anomalias de preço
        priceAnomaly: {
            enabled: true,
            sensitivity: 2.0, // desvios padrão
            minSamples: 5,
            factors: [
                'historicalPrices',
                'marketTrends',
                'supplierVariation',
                'volumeDiscount'
            ]
        },
        
        // Modelo de otimização de processo
        processOptimization: {
            enabled: true,
            optimizationGoals: [
                'reduceTime',
                'increaseEfficiency',
                'reduceErrors',
                'improveSatisfaction'
            ],
            constraints: [
                'budgetLimits',
                'resourceAvailability',
                'complianceRequirements'
            ]
        },
        
        // Modelo de previsão de demanda
        demandPrediction: {
            enabled: true,
            forecastHorizon: 30, // dias
            seasonalityFactors: true,
            trendAnalysis: true,
            externalFactors: [
                'economicIndicators',
                'industryTrends',
                'companyGrowth'
            ]
        }
    },

    // ===== CONFIGURAÇÕES DE NOTIFICAÇÕES =====
    notifications: {
        // Canais de notificação
        channels: {
            email: {
                enabled: true,
                recipients: ['<EMAIL>', '<EMAIL>'],
                templates: {
                    critical: 'critical-alert-template',
                    warning: 'warning-alert-template',
                    info: 'info-alert-template'
                }
            },
            
            dashboard: {
                enabled: true,
                realTime: true,
                autoRefresh: 30000
            },
            
            mobile: {
                enabled: false, // Para implementação futura
                pushNotifications: true
            }
        },
        
        // Frequência de notificações
        frequency: {
            critical: 'immediate',
            warning: 'hourly',
            info: 'daily'
        },
        
        // Filtros de notificação
        filters: {
            duplicateSupression: true,
            minimumSeverity: 'warning',
            businessHoursOnly: false
        }
    },

    // ===== CONFIGURAÇÕES DE RELATÓRIOS =====
    reports: {
        // Relatórios automáticos
        automated: {
            daily: {
                enabled: true,
                time: '08:00',
                recipients: ['<EMAIL>'],
                content: ['alerts', 'metrics', 'predictions']
            },
            
            weekly: {
                enabled: true,
                day: 'monday',
                time: '09:00',
                recipients: ['<EMAIL>'],
                content: ['summary', 'trends', 'recommendations']
            },
            
            monthly: {
                enabled: true,
                day: 1,
                time: '10:00',
                recipients: ['<EMAIL>'],
                content: ['executive', 'roi', 'strategic']
            }
        },
        
        // Formatos de relatório
        formats: {
            pdf: true,
            excel: true,
            json: true,
            csv: false
        }
    },

    // ===== CONFIGURAÇÕES DE APRENDIZADO =====
    learning: {
        // Aprendizado contínuo
        continuousLearning: {
            enabled: true,
            updateFrequency: 'weekly',
            feedbackIntegration: true,
            modelRetraining: true
        },
        
        // Fontes de dados para aprendizado
        dataSources: [
            'solicitacoesCompra',
            'cotacoes',
            'pedidosCompra',
            'recebimentoMateriais',
            'movimentacoesEstoque',
            'avaliacoesFornecedores',
            'parametrosSistema'
        ],
        
        // Métricas de qualidade do modelo
        qualityMetrics: {
            accuracy: 0.85,
            precision: 0.80,
            recall: 0.75,
            f1Score: 0.77
        }
    },

    // ===== CONFIGURAÇÕES DE SEGURANÇA =====
    security: {
        // Controle de acesso
        accessControl: {
            requireAuthentication: true,
            roleBasedAccess: true,
            auditLog: true
        },
        
        // Privacidade de dados
        dataPrivacy: {
            anonymization: true,
            encryption: true,
            retentionPolicy: true
        },
        
        // Compliance
        compliance: {
            gdpr: true,
            lgpd: true,
            auditTrail: true
        }
    },

    // ===== CONFIGURAÇÕES DE PERFORMANCE =====
    performance: {
        // Otimizações
        optimization: {
            caching: true,
            batchProcessing: true,
            parallelProcessing: true,
            memoryManagement: true
        },
        
        // Limites de recursos
        resourceLimits: {
            maxMemoryUsage: '512MB',
            maxCpuUsage: '50%',
            maxConcurrentAnalysis: 5
        },
        
        // Monitoramento de performance
        monitoring: {
            responseTime: true,
            throughput: true,
            errorRate: true,
            resourceUsage: true
        }
    },

    // ===== CONFIGURAÇÕES DE INTEGRAÇÃO =====
    integration: {
        // APIs externas
        externalAPIs: {
            economicData: {
                enabled: false,
                provider: 'banco-central',
                apiKey: null
            },
            
            marketData: {
                enabled: false,
                provider: 'market-api',
                apiKey: null
            }
        },
        
        // Webhooks
        webhooks: {
            enabled: true,
            endpoints: [
                {
                    url: '/api/webhooks/alerts',
                    events: ['critical-alert', 'process-complete']
                }
            ]
        },
        
        // Exportação de dados
        dataExport: {
            formats: ['json', 'csv', 'xml'],
            scheduling: true,
            compression: true
        }
    }
};

// ===== CONFIGURAÇÕES ESPECÍFICAS POR AMBIENTE =====
export const EnvironmentConfig = {
    development: {
        ...IAConfig,
        general: {
            ...IAConfig.general,
            monitoringInterval: 10000, // 10 segundos para desenvolvimento
            analysisInterval: 300000   // 5 minutos para desenvolvimento
        },
        notifications: {
            ...IAConfig.notifications,
            channels: {
                ...IAConfig.notifications.channels,
                email: {
                    ...IAConfig.notifications.channels.email,
                    enabled: false // Desabilitar email em desenvolvimento
                }
            }
        }
    },
    
    production: {
        ...IAConfig,
        performance: {
            ...IAConfig.performance,
            resourceLimits: {
                maxMemoryUsage: '1GB',
                maxCpuUsage: '70%',
                maxConcurrentAnalysis: 10
            }
        }
    },
    
    testing: {
        ...IAConfig,
        general: {
            ...IAConfig.general,
            dataRetentionDays: 7, // Apenas 7 dias para testes
            maxAlertsHistory: 20
        }
    }
};

// ===== FUNÇÃO PARA OBTER CONFIGURAÇÃO =====
export function getIAConfig(environment = 'production') {
    return EnvironmentConfig[environment] || IAConfig;
}

// ===== VALIDAÇÃO DE CONFIGURAÇÃO =====
export function validateConfig(config) {
    const required = [
        'general.monitoringInterval',
        'alertThresholds.delayDays',
        'models.delayPrediction.enabled'
    ];
    
    for (const path of required) {
        const value = path.split('.').reduce((obj, key) => obj?.[key], config);
        if (value === undefined || value === null) {
            throw new Error(`Configuração obrigatória não encontrada: ${path}`);
        }
    }
    
    return true;
}

export default IAConfig;
