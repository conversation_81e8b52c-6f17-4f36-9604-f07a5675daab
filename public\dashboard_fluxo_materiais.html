<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard - Fluxo de Materiais</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .dashboard-container {
            max-width: 1800px;
            margin: 20px auto;
            padding: 20px;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .filters-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-3px);
        }
        .stat-number {
            font-size: 2.2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }
        .flow-matrix {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1400px;
        }
        .matrix-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 13px;
        }
        .matrix-table td {
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
            position: relative;
            min-width: 100px;
            height: 70px;
            vertical-align: middle;
        }
        .product-header {
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            font-weight: bold;
            text-align: left;
            padding: 12px 15px;
            position: sticky;
            left: 0;
            z-index: 5;
            min-width: 250px;
            max-width: 250px;
            border-right: 2px solid #007bff;
        }
        .stage-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 100px;
            font-weight: bold;
            color: #495057;
        }
        
        /* Status Colors - Fluxo de Compras */
        .status-solicitado { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
            color: #856404; 
            border-left: 4px solid #ffc107;
        }
        .status-cotacao { 
            background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%); 
            color: #004085; 
            border-left: 4px solid #007bff;
        }
        .status-pedido { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); 
            color: #155724; 
            border-left: 4px solid #28a745;
        }
        .status-qualidade { 
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%); 
            color: #6c5ce7; 
            border-left: 4px solid #e17055;
        }
        .status-estoque { 
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); 
            color: white; 
            border-left: 4px solid #0984e3;
        }
        .status-parado { 
            background: linear-gradient(135deg, #ff7675 0%, #e84393 100%); 
            color: white; 
            border-left: 4px solid #e84393;
            animation: pulse-red 2s infinite;
        }
        
        @keyframes pulse-red {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .material-cell {
            border-radius: 8px;
            margin: 3px;
            padding: 8px;
            font-size: 11px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 55px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        .material-cell:hover {
            transform: scale(1.05);
            z-index: 100;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .quantity-badge {
            font-size: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 2px 6px;
            margin-top: 3px;
        }
        .days-badge {
            font-size: 9px;
            background: rgba(255,255,255,0.8);
            color: #333;
            border-radius: 8px;
            padding: 1px 4px;
            margin-top: 2px;
        }
        .btn-refresh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        .btn-refresh:hover {
            transform: translateY(-2px);
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-size: 18px;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        .legend-color {
            width: 25px;
            height: 25px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .alert-section {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 2px solid #e53e3e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
        }
        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 900px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 32px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        .close:hover {
            color: #333;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .timeline-date {
            min-width: 120px;
            font-weight: bold;
            color: #6c757d;
            font-size: 12px;
        }
        .timeline-content {
            flex: 1;
            margin-left: 20px;
        }
        .timeline-stage {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>📊 Dashboard de Fluxo de Compras</h1>
            <p>Acompanhe onde seus materiais estão parados no processo de compra</p>
            <p><strong>Fluxo:</strong> Solicitação → Cotação → Pedido → Qualidade → Estoque</p>
        </div>

        <!-- Filtros -->
        <div class="filters-section">
            <h3>🎛️ Filtros e Controles</h3>

            <!-- Indicador de Configurações -->
            <div id="configIndicator" style="background: #e7f3ff; padding: 12px; border-radius: 6px; margin-bottom: 15px; font-size: 13px; border-left: 4px solid #007bff;">
                ⚙️ <strong>Configurações do Sistema:</strong>
                <span id="configStatus">Carregando...</span>
            </div>

            <div class="filters-grid">
                <div>
                    <label><strong>Período:</strong></label>
                    <select id="periodoFiltro">
                        <option value="15">Últimos 15 dias</option>
                        <option value="30" selected>Últimos 30 dias</option>
                        <option value="60">Últimos 60 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="180">Últimos 6 meses</option>
                    </select>
                </div>
                <div>
                    <label><strong>Produto:</strong></label>
                    <input type="text" id="produtoFiltro" placeholder="Filtrar por código/nome...">
                </div>
                <div>
                    <label><strong>Status:</strong></label>
                    <select id="statusFiltro">
                        <option value="all">Todos os status</option>
                        <option value="parados">Apenas Parados (>7 dias)</option>
                        <option value="solicitado">Solicitados</option>
                        <option value="cotacao">Em Cotação</option>
                        <option value="pedido">Pedidos Feitos</option>
                        <option value="qualidade">Em Qualidade</option>
                    </select>
                </div>
                <div>
                    <label><strong>Fornecedor:</strong></label>
                    <input type="text" id="fornecedorFiltro" placeholder="Filtrar por fornecedor...">
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="carregarDashboard()" class="btn-refresh">🔄 Atualizar Dashboard</button>
                <button onclick="exportarRelatorio()" class="btn btn-secondary">📊 Exportar Relatório</button>
                <button onclick="mostrarAlertas()" class="btn btn-warning">⚠️ Materiais Parados</button>
            </div>
        </div>

        <!-- Estatísticas -->
        <div id="statsSection">
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <!-- Alertas de Materiais Parados -->
        <div id="alertsSection" style="display: none;"></div>

        <!-- Legenda -->
        <div class="legend" id="legendaStatus">
            <div class="legend-item">
                <div class="legend-color status-solicitado"></div>
                <span>Solicitado</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-cotacao"></div>
                <span>Em Cotação</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-pedido"></div>
                <span>Pedido Feito</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-qualidade"></div>
                <span>Em Qualidade</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-estoque"></div>
                <span>Em Estoque</span>
            </div>
            <div class="legend-item">
                <div class="legend-color status-parado"></div>
                <span>Parado (>7 dias)</span>
            </div>
        </div>

        <!-- Matriz de Fluxo -->
        <div class="flow-matrix">
            <h3>🗺️ Mapa de Fluxo - Onde estão seus materiais?</h3>
            <div id="matrixContainer" class="loading">
                <p>Clique em "🔄 Atualizar Dashboard" para carregar os dados...</p>
            </div>
        </div>
    </div>

    <!-- Modal para detalhes -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="fecharModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let dashboardData = {};
        let materiaisFluxo = [];

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
        };

        window.carregarDashboard = async function() {
            const container = document.getElementById('matrixContainer');
            container.innerHTML = '<div class="loading"><p>🔄 Carregando dados do fluxo de compras...</p></div>';

            try {
                await carregarDadosCompras();
                processarFluxoCompras();
                mostrarEstatisticas();
                renderizarMatrizFluxo();

            } catch (error) {
                console.error('Erro ao carregar dashboard:', error);
                container.innerHTML = `<div style="color: #e53e3e; text-align: center; padding: 20px;">❌ Erro: ${error.message}</div>`;
            }
        };

        // Variável para armazenar configurações do sistema
        let systemConfig = {
            controleQualidade: false,
            armazemQualidade: false,
            inspecaoRecebimento: 'manual'
        };

        async function carregarConfiguracoesDoSistema() {
            console.log('⚙️ Carregando configurações do sistema...');

            try {
                // Tentar carregar configurações do localStorage ou Firebase
                const configSnap = await getDocs(collection(db, "configuracoes"));
                if (!configSnap.empty) {
                    const configDoc = configSnap.docs[0];
                    const config = configDoc.data();

                    systemConfig = {
                        controleQualidade: config.controleQualidade || false,
                        armazemQualidade: config.armazemQualidade || false,
                        inspecaoRecebimento: config.inspecaoRecebimento || 'manual'
                    };

                    console.log('⚙️ Configurações carregadas:', systemConfig);
                } else {
                    console.log('⚙️ Usando configurações padrão (qualidade desativada)');
                }
            } catch (error) {
                console.log('⚠️ Erro ao carregar configurações, usando padrão:', error);
            }

            // Atualizar indicador visual
            atualizarIndicadorConfiguracoes();
        }

        function atualizarIndicadorConfiguracoes() {
            const configStatus = document.getElementById('configStatus');
            const configIndicator = document.getElementById('configIndicator');

            let statusText = '';
            let backgroundColor = '';
            let borderColor = '';

            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                statusText = `🟢 Módulo de Qualidade ATIVO | Inspeção: ${systemConfig.inspecaoRecebimento.toUpperCase()}`;
                backgroundColor = '#d4edda';
                borderColor = '#28a745';
            } else if (systemConfig.controleQualidade && !systemConfig.armazemQualidade) {
                statusText = '🟡 Controle de Qualidade ATIVO mas Armazém de Qualidade INATIVO';
                backgroundColor = '#fff3cd';
                borderColor = '#ffc107';
            } else {
                statusText = '🔴 Módulo de Qualidade DESATIVADO - Materiais vão direto para estoque';
                backgroundColor = '#f8d7da';
                borderColor = '#dc3545';
            }

            configStatus.textContent = statusText;
            configIndicator.style.backgroundColor = backgroundColor;
            configIndicator.style.borderLeftColor = borderColor;
        }

        async function carregarDadosCompras() {
            console.log('🔄 Iniciando VARREDURA COMPLETA do sistema de compras...');

            // Primeiro carregar configurações
            await carregarConfiguracoesDoSistema();

            // Carregar dados de TODAS as coleções do sistema
            const [
                solicitacoesSnap,
                cotacoesSnap,
                cotacoesAglutinadas,
                pedidosSnap,
                estoquesSnap,
                estoqueQualidadeSnap,
                movimentacoesSnap,
                transferenciasSnap,
                recebimentosSnap,
                produtosSnap,
                armazensSnap,
                fornecedoresSnap
            ] = await Promise.all([
                getDocs(query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"), limit(300))),
                getDocs(query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"), limit(200))),
                getDocs(query(collection(db, "cotacoesAglutinadas"), orderBy("dataCriacao", "desc"), limit(100))),
                getDocs(query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"), limit(200))),
                getDocs(collection(db, "estoques")),
                getDocs(collection(db, "estoqueQualidade")),
                getDocs(query(collection(db, "movimentacoesEstoque"), orderBy("dataHora", "desc"), limit(500))),
                getDocs(query(collection(db, "transferenciasArmazem"), orderBy("dataTransferencia", "desc"), limit(100))),
                getDocs(query(collection(db, "recebimentosDetalhes"), orderBy("dataRecebimento", "desc"), limit(200))),
                getDocs(collection(db, "produtos")),
                getDocs(collection(db, "armazens")),
                getDocs(collection(db, "fornecedores"))
            ]);

            dashboardData = {
                solicitacoes: solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                cotacoes: cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                cotacoesAglutinadas: cotacoesAglutinadas.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                pedidos: pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                estoques: estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                estoqueQualidade: estoqueQualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                movimentacoes: movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                transferencias: transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                recebimentos: recebimentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                produtos: produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                armazens: armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                fornecedores: fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
            };

            console.log('📊 VARREDURA COMPLETA - Dados carregados:', {
                '🛒 Solicitações': dashboardData.solicitacoes.length,
                '💰 Cotações': dashboardData.cotacoes.length,
                '🔗 Cotações Aglutinadas': dashboardData.cotacoesAglutinadas.length,
                '📋 Pedidos': dashboardData.pedidos.length,
                '📦 Estoques': dashboardData.estoques.length,
                '🔍 Estoque Qualidade': dashboardData.estoqueQualidade.length,
                '📊 Movimentações': dashboardData.movimentacoes.length,
                '🔄 Transferências': dashboardData.transferencias.length,
                '📥 Recebimentos': dashboardData.recebimentos.length,
                '🏷️ Produtos': dashboardData.produtos.length,
                '🏪 Armazéns': dashboardData.armazens.length,
                '🏢 Fornecedores': dashboardData.fornecedores.length
            });

            // Debug específico para produto 106142
            const produto106142 = dashboardData.produtos.find(p => p.codigo === '106142' || p.id === '106142');
            console.log('🔍 PRODUTO 106142 - Produto cadastrado:', produto106142);

            const estoque106142 = dashboardData.estoques.filter(e => e.produtoId === '106142' || e.codigo === '106142');
            console.log('🔍 PRODUTO 106142 - Estoques encontrados:', estoque106142);

            const solicitacoes106142 = dashboardData.solicitacoes.filter(s =>
                s.itens && s.itens.some(i => i.codigo === '106142' || i.produtoId === '106142')
            );
            console.log('🔍 PRODUTO 106142 - Solicitações encontradas:', solicitacoes106142);

            const movimentacoes106142 = dashboardData.movimentacoes.filter(m =>
                m.produtoId === '106142' || m.codigo === '106142'
            );
            console.log('🔍 PRODUTO 106142 - Movimentações encontradas:', movimentacoes106142);
        }

        function processarFluxoCompras() {
            console.log('🔄 Iniciando ANÁLISE COMPLETA do fluxo de materiais...');
            materiaisFluxo = [];
            const materiaisMap = new Map();

            // Função para verificar se produto precisa de inspeção
            function produtoPrecisaInspecao(produto) {
                if (!systemConfig.controleQualidade || !systemConfig.armazemQualidade) {
                    return false; // Módulo de qualidade desativado
                }

                switch (systemConfig.inspecaoRecebimento) {
                    case 'todos':
                        return true; // Todos os produtos vão para qualidade
                    case 'criticos':
                        return produto.critico === true; // Apenas produtos críticos
                    case 'manual':
                        return produto.inspecaoRecebimento === 'sim'; // Configuração individual do produto
                    default:
                        return false;
                }
            }

            // Função para determinar o STATUS REAL de um material
            function determinarStatusReal(produtoId, codigo) {
                console.log(`🔍 Analisando status real do produto: ${codigo || produtoId}`);
                console.log(`⚙️ Configurações de qualidade:`, systemConfig);

                const produto = dashboardData.produtos.find(p => p.id === produtoId || p.codigo === codigo);

                // 1. Verificar se está em ESTOQUE (status final)
                const estoqueAtual = dashboardData.estoques.find(e =>
                    e.produtoId === produtoId && e.saldo > 0
                );
                if (estoqueAtual) {
                    console.log(`✅ ${codigo} - DISPONÍVEL EM ESTOQUE: ${estoqueAtual.saldo}`);
                    return {
                        status: 'ESTOQUE',
                        etapa: 'Disponível',
                        localizacao: estoqueAtual.armazemId,
                        detalhes: `Saldo: ${estoqueAtual.saldo}`,
                        prioridade: 'BAIXA'
                    };
                }

                // 2. Verificar se está em QUALIDADE
                const qualidade = dashboardData.estoqueQualidade.find(q =>
                    q.produtoId === produtoId || q.codigo === codigo
                );
                if (qualidade) {
                    console.log(`🔍 ${codigo} - EM QUALIDADE: ${qualidade.status || 'PENDENTE'}`);
                    return {
                        status: 'QUALIDADE',
                        etapa: systemConfig.controleQualidade ? 'Aguardando Inspeção' : 'Em Qualidade (Config Desativada)',
                        localizacao: 'QUALIDADE',
                        detalhes: `Status: ${qualidade.status || 'PENDENTE'} | Módulo: ${systemConfig.controleQualidade ? 'ATIVO' : 'INATIVO'}`,
                        prioridade: 'ALTA'
                    };
                }

                // 3. Verificar RECEBIMENTOS recentes
                const recebimentoRecente = dashboardData.recebimentos.find(r =>
                    r.produtoId === produtoId || r.codigo === codigo
                );
                if (recebimentoRecente) {
                    console.log(`📦 ${codigo} - RECEBIDO RECENTEMENTE`);

                    // Determinar destino baseado nas configurações de qualidade
                    if (produto && produtoPrecisaInspecao(produto)) {
                        return {
                            status: 'QUALIDADE',
                            etapa: 'Aguardando Inspeção',
                            localizacao: 'QUALIDADE',
                            detalhes: `Recebido - Direcionado para qualidade (${systemConfig.inspecaoRecebimento})`,
                            prioridade: 'ALTA'
                        };
                    } else {
                        return {
                            status: 'RECEBIDO',
                            etapa: 'Recebido',
                            localizacao: produto && produto.armazemPadraoId || 'ESTOQUE',
                            detalhes: `Recebido - Direcionado para estoque (qualidade: ${systemConfig.controleQualidade ? 'ativa' : 'inativa'})`,
                            prioridade: 'MÉDIA'
                        };
                    }
                }

                // 4. Verificar PEDIDOS em andamento
                const pedidoAndamento = dashboardData.pedidos.find(p =>
                    p.status !== 'RECEBIDO' && p.itens &&
                    p.itens.some(i => i.produtoId === produtoId || i.codigo === codigo)
                );
                if (pedidoAndamento) {
                    console.log(`📋 ${codigo} - PEDIDO EM ANDAMENTO: ${pedidoAndamento.status}`);
                    return {
                        status: 'PEDIDO',
                        etapa: 'Aguardando Entrega',
                        localizacao: 'PEDIDO',
                        detalhes: `Status: ${pedidoAndamento.status}`,
                        prioridade: 'ALTA'
                    };
                }

                // 5. Verificar COTAÇÕES abertas
                const cotacaoAberta = dashboardData.cotacoes.find(c =>
                    c.status !== 'FECHADA' && c.itens &&
                    c.itens.some(i => i.produtoId === produtoId || i.codigo === codigo)
                );
                if (cotacaoAberta) {
                    console.log(`💰 ${codigo} - COTAÇÃO ABERTA: ${cotacaoAberta.status}`);
                    return {
                        status: 'COTACAO',
                        etapa: 'Em Cotação',
                        localizacao: 'COTACAO',
                        detalhes: `Status: ${cotacaoAberta.status}`,
                        prioridade: 'MÉDIA'
                    };
                }

                // 6. Verificar SOLICITAÇÕES pendentes
                const solicitacaoPendente = dashboardData.solicitacoes.find(s =>
                    s.status !== 'FINALIZADA' && s.itens &&
                    s.itens.some(i => i.produtoId === produtoId || i.codigo === codigo)
                );
                if (solicitacaoPendente) {
                    console.log(`🛒 ${codigo} - SOLICITAÇÃO PENDENTE: ${solicitacaoPendente.status}`);
                    return {
                        status: 'SOLICITADO',
                        etapa: 'Solicitado',
                        localizacao: 'SOLICITACAO',
                        detalhes: `Status: ${solicitacaoPendente.status}`,
                        prioridade: 'ALTA'
                    };
                }

                // 7. Se não encontrou nada, produto sem movimentação
                console.log(`❌ ${codigo} - SEM MOVIMENTAÇÃO RECENTE`);
                return {
                    status: 'SEM_ESTOQUE',
                    etapa: 'Sem Estoque',
                    localizacao: 'FALTA',
                    detalhes: 'Sem movimentação recente',
                    prioridade: 'CRÍTICA'
                };
            }

            // Aplicar filtros
            const periodo = parseInt(document.getElementById('periodoFiltro').value);
            const dataLimite = new Date();
            dataLimite.setDate(dataLimite.getDate() - periodo);
            const produtoFiltro = document.getElementById('produtoFiltro').value.toLowerCase();
            const statusFiltro = document.getElementById('statusFiltro').value;
            const fornecedorFiltro = document.getElementById('fornecedorFiltro').value.toLowerCase();

            console.log('🔍 ANÁLISE COM FILTROS:', { periodo, produtoFiltro, statusFiltro, fornecedorFiltro });

            // NOVA ABORDAGEM: Analisar todos os produtos e determinar status real
            console.log('📊 Iniciando análise de TODOS os produtos cadastrados...');

            dashboardData.produtos.forEach(produto => {
                // Aplicar filtro de produto se especificado
                if (produtoFiltro) {
                    const textoFiltro = `${produto.codigo} ${produto.descricao}`.toLowerCase();
                    if (!textoFiltro.includes(produtoFiltro)) return;
                }

                // Determinar o status real atual do produto
                const statusReal = determinarStatusReal(produto.id, produto.codigo);

                // Aplicar filtro de status se especificado
                if (statusFiltro && statusReal.status !== statusFiltro) return;

                // Calcular dias parado baseado na última movimentação
                const agora = new Date();
                let ultimaMovimentacao = new Date();
                let diasParado = 0;

                // Buscar última movimentação do produto
                const movimentacaoRecente = dashboardData.movimentacoes
                    .filter(m => m.produtoId === produto.id)
                    .sort((a, b) => {
                        const dateA = a.dataHora && a.dataHora.seconds ? new Date(a.dataHora.seconds * 1000) : new Date(0);
                        const dateB = b.dataHora && b.dataHora.seconds ? new Date(b.dataHora.seconds * 1000) : new Date(0);
                        return dateB - dateA;
                    })[0];

                if (movimentacaoRecente) {
                    ultimaMovimentacao = movimentacaoRecente.dataHora && movimentacaoRecente.dataHora.seconds ?
                        new Date(movimentacaoRecente.dataHora.seconds * 1000) : new Date();
                    diasParado = Math.floor((agora - ultimaMovimentacao) / (1000 * 60 * 60 * 24));
                }

                // Criar entrada no mapa de materiais
                const chave = `${produto.id}_REAL_STATUS`;
                const materialData = {
                    produtoId: produto.id,
                    codigo: produto.codigo,
                    descricao: produto.descricao,
                    quantidade: 0, // Será preenchido conforme o status
                    unidade: produto.unidade || 'UN',
                    status: statusReal.status,
                    etapa: statusReal.etapa,
                    localizacao: statusReal.localizacao,
                    dataInicio: ultimaMovimentacao,
                    ultimaAtualizacao: ultimaMovimentacao,
                    diasParado: diasParado,
                    parado: diasParado > 7,
                    prioridade: statusReal.prioridade,
                    solicitante: 'Sistema',
                    observacoes: statusReal.detalhes,
                    historico: [{
                        etapa: statusReal.status,
                        data: ultimaMovimentacao,
                        observacao: statusReal.detalhes
                    }]
                };

                // Preencher quantidade baseada no status
                if (statusReal.status === 'ESTOQUE') {
                    const estoque = dashboardData.estoques.find(e => e.produtoId === produto.id);
                    materialData.quantidade = estoque && estoque.saldo || 0;
                } else if (statusReal.status === 'QUALIDADE') {
                    const qualidade = dashboardData.estoqueQualidade.find(q => q.produtoId === produto.id);
                    materialData.quantidade = qualidade && (qualidade.quantidade || qualidade.saldo) || 0;
                }

                materiaisMap.set(chave, materialData);

                // Debug para produto 106142
                if (produto.codigo === '106142') {
                    console.log('🎯 PRODUTO 106142 - ANÁLISE COMPLETA:', {
                        produto: produto,
                        statusReal: statusReal,
                        materialData: materialData,
                        diasParado: diasParado
                    });
                }
            });

            // Converter mapa para array final
            materiaisFluxo = Array.from(materiaisMap.values());

            console.log('📊 ANÁLISE COMPLETA FINALIZADA:', {
                totalMateriais: materiaisFluxo.length,
                porStatus: {
                    ESTOQUE: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length,
                    QUALIDADE: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                    PEDIDO: materiaisFluxo.filter(m => m.status === 'PEDIDO').length,
                    COTACAO: materiaisFluxo.filter(m => m.status === 'COTACAO').length,
                    SOLICITADO: materiaisFluxo.filter(m => m.status === 'SOLICITADO').length,
                    SEM_ESTOQUE: materiaisFluxo.filter(m => m.status === 'SEM_ESTOQUE').length
                }
            });

            // Debug final para produto 106142
            const produto106142Final = materiaisFluxo.filter(m => m.codigo === '106142');
            console.log('🎯 PRODUTO 106142 - RESULTADO FINAL:', produto106142Final);

            // Aplicar filtros finais se necessário
            if (statusFiltro && statusFiltro !== 'all') {
                materiaisFluxo = materiaisFluxo.filter(m => {
                    switch (statusFiltro) {
                        case 'parados': return m.parado;
                        case 'estoque': return m.status === 'ESTOQUE';
                        case 'qualidade': return m.status === 'QUALIDADE';
                        case 'pedido': return m.status === 'PEDIDO';
                        case 'cotacao': return m.status === 'COTACAO';
                        case 'solicitado': return m.status === 'SOLICITADO';
                        case 'sem_estoque': return m.status === 'SEM_ESTOQUE';
                        default: return true;
                    }
                });
            }

            console.log('✅ VARREDURA COMPLETA FINALIZADA:', {
                totalFinal: materiaisFluxo.length,
                produto106142: materiaisFluxo.filter(m => m.codigo === '106142').length > 0 ? 'ENCONTRADO' : 'NÃO ENCONTRADO'
            });
        }

        // FUNÇÃO ADICIONAL: Processar solicitações ativas (se necessário)
        function processarSolicitacoesAtivas() {
            console.log('📋 Processando solicitações ativas para complementar dados...');
            // Esta função pode ser usada para adicionar solicitações específicas se necessário
            // Por enquanto, a análise principal já cobre todos os casos
        }

        function mostrarEstatisticas() {
            const stats = {
                total: materiaisFluxo.length,
                solicitados: materiaisFluxo.filter(m => m.status === 'SOLICITADO').length,
                cotacao: materiaisFluxo.filter(m => m.status === 'COTACAO').length,
                pedidos: materiaisFluxo.filter(m => m.status === 'PEDIDO').length,
                qualidade: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                estoque: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length,
                parados: materiaisFluxo.filter(m => m.parado).length,
                tempoMedio: calcularTempoMedio()
            };

            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total}</div>
                    <div>Total de Materiais</div>
                </div>
                <div class="stat-card" style="border-left-color: #ffc107;">
                    <div class="stat-number" style="color: #ffc107;">${stats.solicitados}</div>
                    <div>Solicitados</div>
                </div>
                <div class="stat-card" style="border-left-color: #007bff;">
                    <div class="stat-number" style="color: #007bff;">${stats.cotacao}</div>
                    <div>Em Cotação</div>
                </div>
                <div class="stat-card" style="border-left-color: #28a745;">
                    <div class="stat-number" style="color: #28a745;">${stats.pedidos}</div>
                    <div>Pedidos Feitos</div>
                </div>
                <div class="stat-card" style="border-left-color: #fd7e14;">
                    <div class="stat-number" style="color: #fd7e14;">${stats.qualidade}</div>
                    <div>Em Qualidade</div>
                </div>
                <div class="stat-card" style="border-left-color: #0984e3;">
                    <div class="stat-number" style="color: #0984e3;">${stats.estoque}</div>
                    <div>Em Estoque</div>
                </div>
                <div class="stat-card" style="border-left-color: #e84393;">
                    <div class="stat-number" style="color: #e84393;">${stats.parados}</div>
                    <div>Parados</div>
                </div>
                <div class="stat-card" style="border-left-color: #6c757d;">
                    <div class="stat-number" style="color: #6c757d;">${stats.tempoMedio}</div>
                    <div>Tempo Médio (dias)</div>
                </div>
            `;
        }

        function calcularTempoMedio() {
            if (materiaisFluxo.length === 0) return 0;
            const totalDias = materiaisFluxo.reduce((sum, m) => sum + (m.diasParado || 0), 0);
            return Math.round(totalDias / materiaisFluxo.length);
        }

        function renderizarMatrizFluxo() {
            const container = document.getElementById('matrixContainer');

            console.log('🎨 RENDERIZANDO MATRIZ - Total de materiais:', materiaisFluxo.length);

            if (materiaisFluxo.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">Nenhum material encontrado no período selecionado com os filtros aplicados.</div>';
                return;
            }

            // Debug específico para produto 106142
            const material106142 = materiaisFluxo.filter(m => m.codigo === '106142' || m.produtoId === '106142');
            console.log('🎨 PRODUTO 106142 - Materiais para renderização:', material106142);

            // Agrupar materiais por produto
            const produtosMap = new Map();
            materiaisFluxo.forEach(material => {
                const chave = material.produtoId;
                if (!produtosMap.has(chave)) {
                    produtosMap.set(chave, {
                        produtoId: chave,
                        codigo: material.codigo,
                        descricao: material.descricao,
                        materiais: []
                    });
                }
                produtosMap.get(chave).materiais.push(material);

                // Debug para produto 106142
                if (material.codigo === '106142' || material.produtoId === '106142') {
                    console.log('🎨 PRODUTO 106142 - Adicionado ao mapa de produtos:', {
                        chave: chave,
                        produto: produtosMap.get(chave)
                    });
                }
            });

            console.log('🎨 PRODUTOS AGRUPADOS - Total:', produtosMap.size);
            console.log('🎨 PRODUTOS AGRUPADOS - Lista:', Array.from(produtosMap.keys()));

            // Definir colunas do fluxo
            const colunas = [
                { id: 'SOLICITACAO', nome: 'Solicitação', tipo: 'etapa' },
                { id: 'COTACAO', nome: 'Cotação', tipo: 'etapa' },
                { id: 'PEDIDO', nome: 'Pedido', tipo: 'etapa' },
                { id: 'QUALIDADE', nome: 'Qualidade', tipo: 'etapa' },
                { id: 'ESTOQUE', nome: 'Estoque', tipo: 'etapa' }
            ];

            // Construir tabela
            let html = `
                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th class="product-header">Produto</th>
            `;

            colunas.forEach(col => {
                html += `<th class="stage-header">${col.nome}</th>`;
            });

            html += `
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Renderizar linhas dos produtos
            Array.from(produtosMap.values()).forEach(produto => {
                // Debug para produto 106142
                if (produto.codigo === '106142' || produto.produtoId === '106142') {
                    console.log('🎨 PRODUTO 106142 - Renderizando linha:', produto);
                }

                html += `
                    <tr>
                        <td class="product-header">
                            <strong>${produto.codigo}</strong><br>
                            <small>${produto.descricao}</small><br>
                            <small style="color: #6c757d;">Total: ${produto.materiais.length} solicitação(ões)</small>
                        </td>
                `;

                colunas.forEach(coluna => {
                    const materiaisNaColuna = produto.materiais.filter(material => {
                        return material.localizacao === coluna.id;
                    });

                    // Debug para produto 106142
                    if (produto.codigo === '106142' || produto.produtoId === '106142') {
                        console.log(`🎨 PRODUTO 106142 - Coluna ${coluna.nome}:`, {
                            coluna: coluna,
                            materiaisNaColuna: materiaisNaColuna,
                            todosMateriais: produto.materiais
                        });
                    }

                    html += '<td>';

                    if (materiaisNaColuna.length > 0) {
                        materiaisNaColuna.forEach(material => {
                            let statusClass = `status-${material.status.toLowerCase()}`;
                            if (material.parado) {
                                statusClass = 'status-parado';
                            }

                            // Debug para produto 106142
                            if (produto.codigo === '106142' || produto.produtoId === '106142') {
                                console.log(`🎨 PRODUTO 106142 - Criando célula:`, {
                                    material: material,
                                    statusClass: statusClass,
                                    coluna: coluna.nome
                                });
                            }

                            html += `
                                <div class="material-cell ${statusClass}"
                                     onclick="mostrarDetalhes('${material.produtoId}', '${material.solicitacaoId}')"
                                     title="${material.etapa} - ${material.quantidade} ${material.unidade} - ${material.diasParado} dias">
                                    <div>${material.etapa}</div>
                                    <div class="quantity-badge">${material.quantidade} ${material.unidade}</div>
                                    <div class="days-badge">${material.diasParado}d</div>
                                </div>
                            `;
                        });
                    }

                    html += '</td>';
                });

                html += '</tr>';
            });

            html += `
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        window.mostrarDetalhes = function(produtoId, solicitacaoId) {
            const material = materiaisFluxo.find(m =>
                m.produtoId === produtoId && m.solicitacaoId === solicitacaoId
            );

            if (!material) {
                alert('Material não encontrado');
                return;
            }

            const modal = document.getElementById('detailModal');
            const modalContent = document.getElementById('modalContent');

            const armazem = dashboardData.armazens && dashboardData.armazens.find(a => a.id === material.armazemId);

            let html = `
                <h3>📦 Detalhes do Material</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 25px 0;">
                    <div>
                        <h4>📋 Informações do Produto</h4>
                        <p><strong>Código:</strong> ${material.codigo}</p>
                        <p><strong>Descrição:</strong> ${material.descricao}</p>
                        <p><strong>Quantidade:</strong> ${material.quantidade} ${material.unidade}</p>
                        <p><strong>Status Atual:</strong> <span style="color: ${material.parado ? '#e84393' : '#28a745'};">${material.etapa}</span></p>
                        <p><strong>Dias no Status:</strong> <span style="color: ${material.parado ? '#e84393' : '#6c757d'};">${material.diasParado} dias ${material.parado ? '⚠️' : ''}</span></p>
                        <p><strong>Prioridade:</strong> ${material.prioridade || 'NORMAL'}</p>
                    </div>
                    <div>
                        <h4>🔍 Rastreamento</h4>
                        <p><strong>Solicitação:</strong> ${material.solicitacaoId}</p>
                        <p><strong>Solicitante:</strong> ${material.solicitante || 'N/A'}</p>
                        <p><strong>Cotação:</strong> ${material.cotacaoId || 'N/A'}</p>
                        <p><strong>Pedido:</strong> ${material.pedidoId || 'N/A'}</p>
                        <p><strong>Nº Pedido:</strong> ${material.numeroPedido || 'N/A'}</p>
                        <p><strong>Fornecedor:</strong> ${material.fornecedor || 'N/A'}</p>
                        <p><strong>Valor:</strong> ${material.valorPedido || material.valorCotado || 'N/A'}</p>
                        <p><strong>Prazo Entrega:</strong> ${material.prazoEntrega || 'N/A'}</p>
                    </div>
                </div>

                ${material.armazemId ? `
                <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4>📍 Localização Atual</h4>
                    <p><strong>Armazém:</strong> ${armazem && armazem.codigo || material.armazemId} - ${armazem && armazem.nome || 'Nome não encontrado'}</p>
                    <p><strong>Saldo Atual:</strong> ${material.saldoAtual || 'N/A'}</p>
                </div>
                ` : ''}

                ${material.observacoes ? `
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4>📝 Observações</h4>
                    <p>${material.observacoes}</p>
                </div>
                ` : ''}

                <h4>📅 Histórico Completo</h4>
                <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px;">
            `;

            material.historico.forEach(hist => {
                const data = hist.data ? new Date(hist.data.seconds * 1000).toLocaleString() : 'N/A';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-date">${data}</div>
                        <div class="timeline-content">
                            <div class="timeline-stage">${hist.etapa}</div>
                            <div>${hist.observacao}</div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            modalContent.innerHTML = html;
            modal.style.display = 'block';
        };

        window.fecharModal = function() {
            document.getElementById('detailModal').style.display = 'none';
        };

        window.mostrarAlertas = function() {
            const alertsSection = document.getElementById('alertsSection');
            const materiaisParados = materiaisFluxo.filter(m => m.parado);

            if (materiaisParados.length === 0) {
                alertsSection.innerHTML = `
                    <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>✅ Nenhum Material Parado!</h3>
                        <p>Todos os materiais estão dentro do prazo esperado.</p>
                    </div>
                `;
            } else {
                let html = `
                    <div class="alert-section">
                        <h3>⚠️ Materiais Parados - Ação Necessária!</h3>
                        <p><strong>${materiaisParados.length} materiais</strong> estão parados há mais de 7 dias (3 dias para qualidade).</p>
                        <div style="max-height: 400px; overflow-y: auto; margin: 15px 0;">
                `;

                materiaisParados.forEach(material => {
                    html += `
                        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #e84393;">
                            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 15px; align-items: center;">
                                <div>
                                    <strong>${material.codigo}</strong><br>
                                    <small>${material.descricao}</small>
                                </div>
                                <div>
                                    <strong>Status:</strong> ${material.etapa}<br>
                                    <strong>Parado:</strong> <span style="color: #e84393;">${material.diasParado} dias</span>
                                </div>
                                <div>
                                    <strong>Qtd:</strong> ${material.quantidade} ${material.unidade}<br>
                                    <strong>Fornecedor:</strong> ${material.fornecedor || 'N/A'}
                                </div>
                                <div>
                                    <button onclick="mostrarDetalhes('${material.produtoId}', '${material.solicitacaoId}')"
                                            style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                                        Ver Detalhes
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div></div>';
                alertsSection.innerHTML = html;
            }

            alertsSection.style.display = alertsSection.style.display === 'none' ? 'block' : 'none';
        };

        window.exportarRelatorio = function() {
            const dados = {
                dataExportacao: new Date().toISOString(),
                usuario: currentUser.nome,
                filtros: {
                    periodo: document.getElementById('periodoFiltro').value + ' dias',
                    produto: document.getElementById('produtoFiltro').value,
                    status: document.getElementById('statusFiltro').value,
                    fornecedor: document.getElementById('fornecedorFiltro').value
                },
                estatisticas: {
                    totalMateriais: materiaisFluxo.length,
                    materiaisParados: materiaisFluxo.filter(m => m.parado).length,
                    tempoMedio: calcularTempoMedio(),
                    porStatus: {
                        solicitados: materiaisFluxo.filter(m => m.status === 'SOLICITADO').length,
                        cotacao: materiaisFluxo.filter(m => m.status === 'COTACAO').length,
                        pedidos: materiaisFluxo.filter(m => m.status === 'PEDIDO').length,
                        qualidade: materiaisFluxo.filter(m => m.status === 'QUALIDADE').length,
                        estoque: materiaisFluxo.filter(m => m.status === 'ESTOQUE').length
                    }
                },
                materiais: materiaisFluxo.map(m => ({
                    codigo: m.codigo,
                    descricao: m.descricao,
                    quantidade: m.quantidade,
                    unidade: m.unidade,
                    status: m.status,
                    etapa: m.etapa,
                    diasParado: m.diasParado,
                    parado: m.parado,
                    fornecedor: m.fornecedor,
                    valorPedido: m.valorPedido,
                    prazoEntrega: m.prazoEntrega,
                    solicitacaoId: m.solicitacaoId,
                    pedidoId: m.pedidoId,
                    ultimaAtualizacao: m.ultimaAtualizacao
                }))
            };

            const blob = new Blob([JSON.stringify(dados, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `relatorio_fluxo_compras_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('detailModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        };

        // Função para buscar produto específico
        window.buscarProduto = function(codigo) {
            console.log(`🔍 BUSCANDO PRODUTO: ${codigo}`);

            const produto = dashboardData.produtos.find(p => p.codigo === codigo || p.id === codigo);
            console.log(`📦 Produto ${codigo}:`, produto);

            const estoques = dashboardData.estoques.filter(e => e.produtoId === codigo || e.codigo === codigo);
            console.log(`📊 Estoques para ${codigo}:`, estoques);

            const estoqueQualidade = dashboardData.estoqueQualidade.filter(e => e.produtoId === codigo || e.codigo === codigo);
            console.log(`🧪 Estoque Qualidade para ${codigo}:`, estoqueQualidade);

            const solicitacoes = dashboardData.solicitacoes.filter(s =>
                s.itens && s.itens.some(i => i.codigo === codigo || i.produtoId === codigo)
            );
            console.log(`📋 Solicitações para ${codigo}:`, solicitacoes);

            const movimentacoes = dashboardData.movimentacoes.filter(m =>
                m.produtoId === codigo || m.codigo === codigo
            );
            console.log(`🔄 Movimentações para ${codigo}:`, movimentacoes);

            const materiais = materiaisFluxo.filter(m => m.codigo === codigo || m.produtoId === codigo);
            console.log(`🎯 Materiais no fluxo para ${codigo}:`, materiais);

            // Se não encontrou no fluxo mas tem dados, forçar criação
            if (materiais.length === 0 && produto) {
                console.log(`⚠️ FORÇANDO CRIAÇÃO para produto ${codigo}`);

                // Verificar movimentações de entrada
                const entradasRecentes = movimentacoes.filter(m =>
                    (m.tipo === 'ENTRADA' || m.tipoMovimentacao === 'ENTRADA' || m.quantidade > 0) &&
                    m.dataHora && m.dataHora.seconds &&
                    new Date(m.dataHora.seconds * 1000) >= new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // últimos 90 dias
                );

                if (entradasRecentes.length > 0) {
                    console.log(`🔧 Criando entrada forçada baseada em movimentações:`, entradasRecentes);

                    entradasRecentes.forEach((mov, index) => {
                        const chaveForced = `${codigo}_FORCED_${mov.id || index}`;
                        const dataMovimentacao = new Date(mov.dataHora.seconds * 1000);
                        const agora = new Date();
                        const diasParado = Math.floor((agora - dataMovimentacao) / (1000 * 60 * 60 * 24));

                        // Determinar status baseado no armazém
                        let status = 'ESTOQUE';
                        let etapa = 'Em Estoque';
                        let localizacao = mov.armazemDestino || mov.armazemId || 'ESTOQUE';

                        const armazem = dashboardData.armazens && dashboardData.armazens.find(a => a.id === localizacao);
                        if (armazem && (armazem.nome && armazem.nome.toLowerCase().includes('qualidade') || armazem.codigo && armazem.codigo.toLowerCase().includes('qual'))) {
                            status = 'QUALIDADE';
                            etapa = 'Em Qualidade';
                            localizacao = 'QUALIDADE';
                        }

                        const materialForced = {
                            produtoId: codigo,
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            quantidade: Math.abs(mov.quantidade),
                            unidade: produto.unidade || 'UN',
                            status: status,
                            etapa: etapa,
                            localizacao: localizacao,
                            movimentacaoId: mov.id,
                            armazemId: mov.armazemDestino || mov.armazemId,
                            dataInicio: mov.dataHora,
                            ultimaAtualizacao: mov.dataHora,
                            diasParado: diasParado,
                            parado: status === 'QUALIDADE' ? diasParado > 3 : diasParado > 7,
                            prioridade: 'NORMAL',
                            solicitante: 'Entrada Forçada',
                            observacoes: `Material forçado - ${mov.observacoes || 'Recebimento'}`,
                            historico: [{
                                etapa: status,
                                data: mov.dataHora,
                                observacao: `${etapa} - Entrada forçada - Qtd: ${Math.abs(mov.quantidade)}`
                            }]
                        };

                        materiaisFluxo.push(materialForced);
                        console.log(`✅ Material ${codigo} adicionado forçadamente:`, materialForced);
                    });

                    // Atualizar estatísticas e tabela
                    mostrarEstatisticas();
                    renderizarMatrizFluxo();
                }
            }

            return { produto, estoques, estoqueQualidade, solicitacoes, movimentacoes, materiais };
        };

        // Função específica para produto 106142
        window.debug106142 = function() {
            return buscarProduto('106142');
        };

        // Função para simular ativação/desativação do módulo de qualidade
        window.toggleQualidade = function(ativar = null) {
            if (ativar === null) {
                // Toggle automático
                systemConfig.controleQualidade = !systemConfig.controleQualidade;
                systemConfig.armazemQualidade = systemConfig.controleQualidade;
            } else {
                // Definir explicitamente
                systemConfig.controleQualidade = ativar;
                systemConfig.armazemQualidade = ativar;
            }

            if (!systemConfig.inspecaoRecebimento) {
                systemConfig.inspecaoRecebimento = 'manual';
            }

            console.log('⚙️ CONFIGURAÇÕES ALTERADAS:', systemConfig);

            // Atualizar indicador
            atualizarIndicadorConfiguracoes();

            // Reprocessar dados
            processarFluxoCompras();
            mostrarEstatisticas();
            renderizarMatrizFluxo();

            console.log('✅ Dashboard atualizado com novas configurações!');

            return systemConfig;
        };

        // Função para definir tipo de inspeção
        window.setInspecao = function(tipo) {
            if (['todos', 'criticos', 'manual'].includes(tipo)) {
                systemConfig.inspecaoRecebimento = tipo;
                console.log(`⚙️ Inspeção alterada para: ${tipo}`);

                atualizarIndicadorConfiguracoes();
                processarFluxoCompras();
                mostrarEstatisticas();
                renderizarMatrizFluxo();

                return systemConfig;
            } else {
                console.log('❌ Tipo de inspeção inválido. Use: todos, criticos, manual');
            }
        };
    </script>
</body>
</html>
