<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestão de Estoque</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #fd7e14;
      --primary-hover: #e55a00;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #28a745;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --header-bg: #fd7e14;
    }
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }
    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
      padding: 20px;
    }
    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
    }
    .header h1 {
      font-size: 24px;
      margin: 0;
    }
    .nav-buttons {
      display: flex;
      gap: 10px;
    }
    .nav-btn {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }
    .nav-btn:hover {
      background: linear-gradient(135deg, #0a4d8c, #354a5f);
      transform: translateY(-2px);
    }
    .nav-btn.success {
      background: linear-gradient(135deg, #28a745, #20c997);
    }
    .nav-btn.danger {
      background: linear-gradient(135deg, #dc3545, #c82333);
    }
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 25px;
      margin-bottom: 30px;
    }
    .tool-card {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      border: 1px solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;
    }
    .tool-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .tool-card h3 {
      color: var(--primary-color);
      margin-bottom: 15px;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .tool-card p {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.6;
    }
    .tool-btn {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      width: 100%;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .tool-btn:hover {
      background: linear-gradient(135deg, var(--primary-hover), #d66a0b);
      transform: translateY(-2px);
    }
    .tool-btn.danger {
      background: linear-gradient(135deg, var(--danger-color), #c82333);
    }
    .tool-btn.success {
      background: linear-gradient(135deg, var(--success-color), #20c997);
    }
    .search-section {
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      padding: 25px;
      margin-bottom: 25px;
      border: 1px solid rgba(0,0,0,0.05);
    }
    .search-section h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .search-form {
      display: flex;
      gap: 15px;
      align-items: end;
    }
    .search-form input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      font-family: monospace;
    }
    .search-form input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
    }
    .search-form button {
      background: var(--primary-color);
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
    }
    .results-container {
      background: white;
      border-radius: 12px;
      padding: 25px;
      margin-top: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      border: 1px solid rgba(0,0,0,0.05);
      display: none;
    }
    .results-container h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(5px);
    }
    .modal-content {
      background: white;
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 1200px;
      border-radius: 16px;
      position: relative;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      display: flex;
      flex-direction: column;
    }
    .modal-header {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      padding: 25px 30px;
      border-radius: 16px 16px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .modal-header h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: 30px;
      max-height: calc(85vh - 150px);
    }
    .modal-footer {
      padding: 25px 30px;
      background: #f8f9fa;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      text-align: right;
      border-radius: 0 0 16px 16px;
    }
    .close-button {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .close-button:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: rotate(90deg) scale(1.1);
    }
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 1000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    }
    .notification.show {
      opacity: 1;
      transform: translateX(0);
    }
    .notification.success { background: #28a745; }
    .notification.error { background: #dc3545; }
    .notification.warning { background: #ffc107; color: #212529; }
    .notification.info { background: #17a2b8; }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
    }
    .stat-card {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      border: 1px solid rgba(0,0,0,0.05);
    }
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      color: var(--primary-color);
      margin-bottom: 5px;
    }
    .stat-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📦 Gestão de Estoque</h1>
      <div class="nav-buttons">
        <a href="apontamentos_simplificado.html" class="nav-btn">
          <i class="fas fa-industry"></i> Apontamentos
        </a>
        <a href="analise_producao.html" class="nav-btn success">
          <i class="fas fa-chart-line"></i> Análise de Produção
        </a>
        <button onclick="forcarAtualizacao()" class="nav-btn danger">
          <i class="fas fa-sync-alt"></i> Atualizar
        </button>
      </div>
    </div>

    <!-- Estatísticas de Estoque -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number" id="totalProdutos">0</div>
        <div class="stat-label">Produtos</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="produtosComEstoque">0</div>
        <div class="stat-label">Com Estoque</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="produtosZerados">0</div>
        <div class="stat-label">Zerados</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="produtosNegativos">0</div>
        <div class="stat-label">Negativos</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="totalArmazens">0</div>
        <div class="stat-label">Armazéns</div>
      </div>
    </div>

    <!-- Seção de Busca -->
    <div class="search-section">
      <h3><i class="fas fa-search"></i> Buscar Produto por Código</h3>
      <div class="search-form">
        <input type="text" id="codigoBusca" placeholder="Digite o código críptico (ex: iN38b9FRypjedRX5IhDb)" />
        <button onclick="buscarProduto()">
          <i class="fas fa-search"></i> Buscar
        </button>
      </div>
    </div>

    <!-- Ferramentas de Gestão -->
    <div class="tools-grid">
      <div class="tool-card">
        <h3><i class="fas fa-code"></i> Decodificador de Produtos</h3>
        <p>Identifica produtos através de códigos crípticos do Firebase, mostrando informações completas e status do estoque.</p>
        <button class="tool-btn" onclick="abrirDecodificador()">
          <i class="fas fa-search"></i> Abrir Decodificador
        </button>
      </div>

      <div class="tool-card">
        <h3><i class="fas fa-exclamation-triangle"></i> Análise de Problemas</h3>
        <p>Detecta automaticamente estoques negativos, inconsistências de reservas e outros problemas críticos.</p>
        <button class="tool-btn danger" onclick="analisarProblemas()">
          <i class="fas fa-bug"></i> Analisar Problemas
        </button>
      </div>

      <div class="tool-card">
        <h3><i class="fas fa-tools"></i> Correção de Estoques</h3>
        <p>Ferramenta para corrigir saldos incorretos, ajustar reservas e resolver inconsistências de estoque.</p>
        <button class="tool-btn success" onclick="abrirCorrecaoEstoques()">
          <i class="fas fa-wrench"></i> Corrigir Estoques
        </button>
      </div>

      <div class="tool-card">
        <h3><i class="fas fa-exchange-alt"></i> Gestão de Transferências</h3>
        <p>Gerencia transferências entre armazéns, identifica oportunidades e resolve bloqueios de produção.</p>
        <button class="tool-btn" onclick="gerenciarTransferencias()">
          <i class="fas fa-truck"></i> Transferências
        </button>
      </div>

      <div class="tool-card">
        <h3><i class="fas fa-chart-bar"></i> Relatórios de Estoque</h3>
        <p>Gera relatórios detalhados de movimentação, posição de estoque e análises de consumo.</p>
        <button class="tool-btn" onclick="gerarRelatorios()">
          <i class="fas fa-file-alt"></i> Gerar Relatórios
        </button>
      </div>

      <div class="tool-card">
        <h3><i class="fas fa-history"></i> Auditoria de Movimentos</h3>
        <p>Rastreia histórico de movimentações, identifica discrepâncias e mantém trilha de auditoria.</p>
        <button class="tool-btn" onclick="auditarMovimentos()">
          <i class="fas fa-search-plus"></i> Auditar
        </button>
      </div>
    </div>

    <!-- Container de Resultados -->
    <div class="results-container" id="resultsContainer">
      <h3><i class="fas fa-list"></i> Resultados</h3>
      <div id="resultsContent">
        <!-- Resultados serão inseridos aqui -->
      </div>
    </div>
  </div>

  <!-- Modal para Ferramentas -->
  <div id="modalFerramentas" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>🔧 Ferramenta de Gestão</h2>
        <span class="close-button" onclick="fecharModal()">&times;</span>
      </div>
      <div class="modal-body" id="conteudoModal">
        <!-- Conteúdo da ferramenta será inserido aqui -->
      </div>
      <div class="modal-footer">
        <button onclick="executarAcao()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; margin-right: 10px;">
          ✅ Executar
        </button>
        <button onclick="fecharModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px;">
          Fechar
        </button>
      </div>
    </div>
  </div>

  <script type="module">
    // Importar configurações do Firebase
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.1/firebase-app.js';
    import { getFirestore, collection, getDocs } from 'https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore.js';
    import { firebaseConfig } from './firebase-config.js';

    // Inicializar Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Variáveis globais
    let produtos = [];
    let estoques = [];
    let armazens = [];

    // Função para mostrar notificação
    function mostrarNotificacao(mensagem, tipo = 'info', duracao = 3000) {
      const notification = document.createElement('div');
      notification.className = `notification ${tipo}`;
      notification.textContent = mensagem;
      document.body.appendChild(notification);

      setTimeout(() => notification.classList.add('show'), 100);
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, duracao);
    }

    // Função para forçar atualização
    window.forcarAtualizacao = function() {
      mostrarNotificacao('🔄 Atualizando dados de estoque...', 'info', 2000);
      carregarDados();
    };

    // Carregar dados do Firebase
    async function carregarDados() {
      try {
        console.log('🔄 Carregando dados de estoque...');
        
        const [produtosSnapshot, estoquesSnapshot, armazensSnapshot] = await Promise.all([
          getDocs(collection(db, 'produtos')),
          getDocs(collection(db, 'estoques')),
          getDocs(collection(db, 'armazens'))
        ]);

        produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`✅ Dados carregados: ${produtos.length} produtos, ${estoques.length} estoques`);
        atualizarEstatisticas();
        
      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
        mostrarNotificacao('❌ Erro ao carregar dados', 'error', 5000);
      }
    }

    // Atualizar estatísticas
    function atualizarEstatisticas() {
      const produtosComEstoque = estoques.filter(e => e.saldo > 0).length;
      const produtosZerados = estoques.filter(e => e.saldo === 0).length;
      const produtosNegativos = estoques.filter(e => e.saldo < 0).length;

      document.getElementById('totalProdutos').textContent = produtos.length;
      document.getElementById('produtosComEstoque').textContent = produtosComEstoque;
      document.getElementById('produtosZerados').textContent = produtosZerados;
      document.getElementById('produtosNegativos').textContent = produtosNegativos;
      document.getElementById('totalArmazens').textContent = armazens.length;
    }

    // Buscar produto
    window.buscarProduto = function() {
      const codigo = document.getElementById('codigoBusca').value.trim();
      if (!codigo) {
        mostrarNotificacao('Digite um código para buscar', 'warning', 2000);
        return;
      }
      
      mostrarNotificacao(`🔍 Buscando produto: ${codigo}`, 'info', 2000);
      // Implementar busca
    };

    // Funções das ferramentas (placeholders)
    window.abrirDecodificador = function() {
      mostrarNotificacao('🔍 Abrindo decodificador...', 'info', 2000);
    };

    window.analisarProblemas = function() {
      mostrarNotificacao('🔍 Analisando problemas de estoque...', 'info', 2000);
    };

    window.abrirCorrecaoEstoques = function() {
      mostrarNotificacao('🔧 Abrindo correção de estoques...', 'info', 2000);
    };

    window.gerenciarTransferencias = function() {
      mostrarNotificacao('🚚 Abrindo gestão de transferências...', 'info', 2000);
    };

    window.gerarRelatorios = function() {
      mostrarNotificacao('📊 Gerando relatórios...', 'info', 2000);
    };

    window.auditarMovimentos = function() {
      mostrarNotificacao('🔍 Iniciando auditoria...', 'info', 2000);
    };

    // Funções do modal
    window.fecharModal = function() {
      document.getElementById('modalFerramentas').style.display = 'none';
    };

    window.executarAcao = function() {
      mostrarNotificacao('✅ Ação executada!', 'success', 2000);
    };

    // Inicializar aplicação
    document.addEventListener('DOMContentLoaded', function() {
      carregarDados();
    });
  </script>
</body>
</html>
