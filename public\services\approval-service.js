/**
 * SERVIÇO DE CONTROLE DE APROVAÇÕES - WIZAR ERP
 * Implementa workflow rigoroso de aprovações com validação de hierarquia
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc, 
    getDocs,
    updateDoc,
    addDoc,
    query,
    where,
    orderBy,
    runTransaction,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

import { ValidationService } from './validation-service.js';
import { AuthService } from './auth-service.js';

export class ApprovalService {
    
    // Configurações de aprovação por tipo de documento
    static APPROVAL_CONFIGS = {
        SOLICITACAO_COMPRA: {
            levels: [
                { minValue: 0, maxValue: 1000, requiredLevel: 3, description: 'Supervisor' },
                { minValue: 1000.01, maxValue: 5000, requiredLevel: 5, description: '<PERSON>ere<PERSON>' },
                { minValue: 5000.01, maxValue: 25000, requiredLevel: 7, description: 'Diretor' },
                { minValue: 25000.01, maxValue: Infinity, requiredLevel: 9, description: 'Presidente' }
            ],
            requiresSequential: true,
            maxDaysToApprove: 5
        },
        PEDIDO_COMPRA: {
            levels: [
                { minValue: 0, maxValue: 2000, requiredLevel: 4, description: 'Coordenador' },
                { minValue: 2000.01, maxValue: 10000, requiredLevel: 6, description: 'Gerente Geral' },
                { minValue: 10000.01, maxValue: 50000, requiredLevel: 8, description: 'Diretor Executivo' },
                { minValue: 50000.01, maxValue: Infinity, requiredLevel: 9, description: 'Presidente' }
            ],
            requiresSequential: true,
            maxDaysToApprove: 3
        },
        ORCAMENTO: {
            levels: [
                { minValue: 0, maxValue: 500, requiredLevel: 2, description: 'Analista Sênior' },
                { minValue: 500.01, maxValue: 3000, requiredLevel: 4, description: 'Coordenador' },
                { minValue: 3000.01, maxValue: 15000, requiredLevel: 6, description: 'Gerente' },
                { minValue: 15000.01, maxValue: Infinity, requiredLevel: 8, description: 'Diretor' }
            ],
            requiresSequential: false,
            maxDaysToApprove: 7
        }
    };
    
    /**
     * 🔍 DETERMINAR NÍVEIS DE APROVAÇÃO NECESSÁRIOS
     */
    static determineRequiredApprovals(documentType, value) {
        const config = this.APPROVAL_CONFIGS[documentType];
        
        if (!config) {
            throw new Error(`Tipo de documento não configurado: ${documentType}`);
        }
        
        const requiredLevels = [];
        
        for (const level of config.levels) {
            if (value >= level.minValue && value <= level.maxValue) {
                requiredLevels.push({
                    level: level.requiredLevel,
                    description: level.description,
                    minValue: level.minValue,
                    maxValue: level.maxValue
                });
                
                // Se requer aprovação sequencial, incluir todos os níveis anteriores
                if (config.requiresSequential) {
                    for (const prevLevel of config.levels) {
                        if (prevLevel.requiredLevel < level.requiredLevel && 
                            !requiredLevels.some(rl => rl.level === prevLevel.requiredLevel)) {
                            requiredLevels.push({
                                level: prevLevel.requiredLevel,
                                description: prevLevel.description,
                                minValue: prevLevel.minValue,
                                maxValue: prevLevel.maxValue
                            });
                        }
                    }
                }
                break;
            }
        }
        
        // Ordenar por nível crescente
        requiredLevels.sort((a, b) => a.level - b.level);
        
        return {
            levels: requiredLevels,
            requiresSequential: config.requiresSequential,
            maxDaysToApprove: config.maxDaysToApprove
        };
    }
    
    /**
     * 📝 INICIAR PROCESSO DE APROVAÇÃO
     */
    static async initiateApproval(documentId, documentType, value, requestedBy) {
        try {
            // Validar entrada
            if (!documentId || !documentType || !value || !requestedBy) {
                throw new Error('Parâmetros obrigatórios não fornecidos');
            }
            
            // Determinar aprovações necessárias
            const approvalConfig = this.determineRequiredApprovals(documentType, value);
            
            // Criar registro de aprovação
            const approvalData = {
                documentId,
                documentType,
                value,
                requestedBy,
                requestedAt: Timestamp.now(),
                status: 'PENDING',
                requiredLevels: approvalConfig.levels,
                requiresSequential: approvalConfig.requiresSequential,
                maxDaysToApprove: approvalConfig.maxDaysToApprove,
                expiresAt: Timestamp.fromDate(new Date(Date.now() + (approvalConfig.maxDaysToApprove * 24 * 60 * 60 * 1000))),
                approvals: [],
                rejections: [],
                currentLevel: approvalConfig.requiresSequential ? approvalConfig.levels[0]?.level : null,
                createdAt: Timestamp.now(),
                updatedAt: Timestamp.now()
            };
            
            const approvalRef = await addDoc(collection(db, "aprovacoes"), approvalData);
            
            // Registrar auditoria
            await this.logApprovalEvent('APPROVAL_INITIATED', {
                approvalId: approvalRef.id,
                documentId,
                documentType,
                value,
                requestedBy,
                requiredLevels: approvalConfig.levels
            });
            
            return {
                success: true,
                approvalId: approvalRef.id,
                requiredLevels: approvalConfig.levels,
                requiresSequential: approvalConfig.requiresSequential
            };
            
        } catch (error) {
            console.error('Erro ao iniciar aprovação:', error);
            throw error;
        }
    }
    
    /**
     * ✅ PROCESSAR APROVAÇÃO
     */
    static async processApproval(approvalId, userId, action, comments = '') {
        return await runTransaction(db, async (transaction) => {
            try {
                // Validar ação
                if (!['APPROVE', 'REJECT'].includes(action)) {
                    throw new Error('Ação inválida. Use APPROVE ou REJECT');
                }
                
                // Buscar registro de aprovação
                const approvalRef = doc(db, "aprovacoes", approvalId);
                const approvalDoc = await transaction.get(approvalRef);
                
                if (!approvalDoc.exists()) {
                    throw new Error('Registro de aprovação não encontrado');
                }
                
                const approvalData = approvalDoc.data();
                
                // Verificar se ainda está pendente
                if (approvalData.status !== 'PENDING') {
                    throw new Error(`Aprovação já foi ${approvalData.status.toLowerCase()}`);
                }
                
                // Verificar se não expirou
                if (approvalData.expiresAt.toDate() < new Date()) {
                    throw new Error('Prazo para aprovação expirado');
                }
                
                // Buscar dados do usuário
                const userRef = doc(db, "usuarios", userId);
                const userDoc = await transaction.get(userRef);
                
                if (!userDoc.exists()) {
                    throw new Error('Usuário não encontrado');
                }
                
                const userData = userDoc.data();
                
                // Verificar se usuário tem nível adequado
                const requiredLevel = approvalData.requiresSequential 
                    ? approvalData.currentLevel 
                    : Math.min(...approvalData.requiredLevels.map(l => l.level));
                
                if (userData.nivel < requiredLevel) {
                    throw new Error(`Nível insuficiente. Necessário: ${requiredLevel}, Atual: ${userData.nivel}`);
                }
                
                // Verificar se usuário já aprovou/rejeitou
                const existingApproval = approvalData.approvals.find(a => a.userId === userId);
                const existingRejection = approvalData.rejections.find(r => r.userId === userId);
                
                if (existingApproval || existingRejection) {
                    throw new Error('Usuário já processou esta aprovação');
                }
                
                // Verificar se é o solicitante (não pode aprovar própria solicitação)
                if (approvalData.requestedBy === userId) {
                    throw new Error('Solicitante não pode aprovar própria solicitação');
                }
                
                const actionData = {
                    userId,
                    userName: userData.nome,
                    userLevel: userData.nivel,
                    action,
                    comments: ValidationService.sanitizeString(comments),
                    timestamp: Timestamp.now(),
                    ip: await AuthService.getUserIP()
                };
                
                let newStatus = approvalData.status;
                let newCurrentLevel = approvalData.currentLevel;
                
                if (action === 'REJECT') {
                    // Rejeição - processo encerrado
                    newStatus = 'REJECTED';
                    newCurrentLevel = null;
                    
                    transaction.update(approvalRef, {
                        status: newStatus,
                        currentLevel: newCurrentLevel,
                        rejections: [...approvalData.rejections, actionData],
                        rejectedAt: Timestamp.now(),
                        rejectedBy: userId,
                        updatedAt: Timestamp.now()
                    });
                    
                } else {
                    // Aprovação
                    const newApprovals = [...approvalData.approvals, actionData];
                    
                    if (approvalData.requiresSequential) {
                        // Aprovação sequencial - verificar próximo nível
                        const currentLevelIndex = approvalData.requiredLevels.findIndex(l => l.level === approvalData.currentLevel);
                        const nextLevelIndex = currentLevelIndex + 1;
                        
                        if (nextLevelIndex < approvalData.requiredLevels.length) {
                            // Ainda há níveis pendentes
                            newCurrentLevel = approvalData.requiredLevels[nextLevelIndex].level;
                        } else {
                            // Todos os níveis aprovados
                            newStatus = 'APPROVED';
                            newCurrentLevel = null;
                        }
                    } else {
                        // Aprovação não sequencial - verificar se todos os níveis foram cobertos
                        const approvedLevels = new Set(newApprovals.map(a => a.userLevel));
                        const requiredLevels = new Set(approvalData.requiredLevels.map(l => l.level));
                        
                        const allLevelsCovered = [...requiredLevels].every(level => 
                            [...approvedLevels].some(approvedLevel => approvedLevel >= level)
                        );
                        
                        if (allLevelsCovered) {
                            newStatus = 'APPROVED';
                        }
                    }
                    
                    const updateData = {
                        status: newStatus,
                        currentLevel: newCurrentLevel,
                        approvals: newApprovals,
                        updatedAt: Timestamp.now()
                    };
                    
                    if (newStatus === 'APPROVED') {
                        updateData.approvedAt = Timestamp.now();
                    }
                    
                    transaction.update(approvalRef, updateData);
                }
                
                // Registrar auditoria
                await this.logApprovalEvent('APPROVAL_PROCESSED', {
                    approvalId,
                    userId,
                    action,
                    comments,
                    newStatus,
                    documentId: approvalData.documentId,
                    documentType: approvalData.documentType
                });
                
                return {
                    success: true,
                    status: newStatus,
                    currentLevel: newCurrentLevel,
                    isComplete: ['APPROVED', 'REJECTED'].includes(newStatus)
                };
                
            } catch (error) {
                console.error('Erro ao processar aprovação:', error);
                throw error;
            }
        });
    }

    /**
     * 📋 CONSULTAR STATUS DE APROVAÇÃO
     */
    static async getApprovalStatus(documentId, documentType) {
        try {
            const approvalQuery = query(
                collection(db, "aprovacoes"),
                where("documentId", "==", documentId),
                where("documentType", "==", documentType),
                orderBy("createdAt", "desc")
            );

            const approvalSnapshot = await getDocs(approvalQuery);

            if (approvalSnapshot.empty) {
                return {
                    exists: false,
                    status: 'NOT_INITIATED',
                    message: 'Processo de aprovação não iniciado'
                };
            }

            const approvalData = approvalSnapshot.docs[0].data();
            const approvalId = approvalSnapshot.docs[0].id;

            // Verificar se expirou
            const isExpired = approvalData.expiresAt.toDate() < new Date();

            if (isExpired && approvalData.status === 'PENDING') {
                // Marcar como expirado
                await updateDoc(doc(db, "aprovacoes", approvalId), {
                    status: 'EXPIRED',
                    expiredAt: Timestamp.now(),
                    updatedAt: Timestamp.now()
                });

                approvalData.status = 'EXPIRED';
            }

            return {
                exists: true,
                approvalId,
                status: approvalData.status,
                currentLevel: approvalData.currentLevel,
                requiredLevels: approvalData.requiredLevels,
                approvals: approvalData.approvals || [],
                rejections: approvalData.rejections || [],
                requiresSequential: approvalData.requiresSequential,
                expiresAt: approvalData.expiresAt.toDate(),
                isExpired,
                canBeApproved: approvalData.status === 'PENDING' && !isExpired
            };

        } catch (error) {
            console.error('Erro ao consultar status de aprovação:', error);
            throw error;
        }
    }

    /**
     * 👥 LISTAR APROVAÇÕES PENDENTES PARA USUÁRIO
     */
    static async getPendingApprovalsForUser(userId) {
        try {
            const userDoc = await getDoc(doc(db, "usuarios", userId));

            if (!userDoc.exists()) {
                throw new Error('Usuário não encontrado');
            }

            const userData = userDoc.data();
            const userLevel = userData.nivel;

            // Buscar aprovações pendentes onde o usuário pode aprovar
            const pendingQuery = query(
                collection(db, "aprovacoes"),
                where("status", "==", "PENDING"),
                where("expiresAt", ">", Timestamp.now())
            );

            const pendingSnapshot = await getDocs(pendingQuery);
            const userPendingApprovals = [];

            for (const doc of pendingSnapshot.docs) {
                const approvalData = doc.data();

                // Verificar se usuário não é o solicitante
                if (approvalData.requestedBy === userId) {
                    continue;
                }

                // Verificar se usuário já aprovou/rejeitou
                const alreadyProcessed = approvalData.approvals?.some(a => a.userId === userId) ||
                                       approvalData.rejections?.some(r => r.userId === userId);

                if (alreadyProcessed) {
                    continue;
                }

                // Verificar se usuário tem nível adequado
                let canApprove = false;

                if (approvalData.requiresSequential) {
                    // Aprovação sequencial - verificar nível atual
                    canApprove = userLevel >= approvalData.currentLevel;
                } else {
                    // Aprovação não sequencial - verificar se pode cobrir algum nível necessário
                    canApprove = approvalData.requiredLevels.some(level => userLevel >= level.level);
                }

                if (canApprove) {
                    userPendingApprovals.push({
                        approvalId: doc.id,
                        documentId: approvalData.documentId,
                        documentType: approvalData.documentType,
                        value: approvalData.value,
                        requestedBy: approvalData.requestedBy,
                        requestedAt: approvalData.requestedAt.toDate(),
                        expiresAt: approvalData.expiresAt.toDate(),
                        currentLevel: approvalData.currentLevel,
                        requiredLevels: approvalData.requiredLevels,
                        requiresSequential: approvalData.requiresSequential
                    });
                }
            }

            // Ordenar por data de expiração (mais urgentes primeiro)
            userPendingApprovals.sort((a, b) => a.expiresAt - b.expiresAt);

            return userPendingApprovals;

        } catch (error) {
            console.error('Erro ao listar aprovações pendentes:', error);
            throw error;
        }
    }

    /**
     * 🔄 CANCELAR PROCESSO DE APROVAÇÃO
     */
    static async cancelApproval(approvalId, userId, reason) {
        try {
            const approvalRef = doc(db, "aprovacoes", approvalId);
            const approvalDoc = await getDoc(approvalRef);

            if (!approvalDoc.exists()) {
                throw new Error('Registro de aprovação não encontrado');
            }

            const approvalData = approvalDoc.data();

            // Verificar se usuário pode cancelar (deve ser o solicitante ou admin)
            const userDoc = await getDoc(doc(db, "usuarios", userId));
            const userData = userDoc.data();

            if (approvalData.requestedBy !== userId && userData.nivel < 9) {
                throw new Error('Apenas o solicitante ou administrador pode cancelar');
            }

            // Verificar se ainda pode ser cancelado
            if (!['PENDING'].includes(approvalData.status)) {
                throw new Error(`Não é possível cancelar aprovação com status: ${approvalData.status}`);
            }

            await updateDoc(approvalRef, {
                status: 'CANCELLED',
                cancelledAt: Timestamp.now(),
                cancelledBy: userId,
                cancellationReason: ValidationService.sanitizeString(reason),
                updatedAt: Timestamp.now()
            });

            // Registrar auditoria
            await this.logApprovalEvent('APPROVAL_CANCELLED', {
                approvalId,
                userId,
                reason,
                documentId: approvalData.documentId,
                documentType: approvalData.documentType
            });

            return { success: true };

        } catch (error) {
            console.error('Erro ao cancelar aprovação:', error);
            throw error;
        }
    }

    /**
     * 📊 RELATÓRIO DE APROVAÇÕES
     */
    static async getApprovalReport(filters = {}) {
        try {
            let approvalQuery = collection(db, "aprovacoes");

            // Aplicar filtros
            if (filters.documentType) {
                approvalQuery = query(approvalQuery, where("documentType", "==", filters.documentType));
            }

            if (filters.status) {
                approvalQuery = query(approvalQuery, where("status", "==", filters.status));
            }

            if (filters.startDate) {
                approvalQuery = query(approvalQuery, where("createdAt", ">=", Timestamp.fromDate(filters.startDate)));
            }

            if (filters.endDate) {
                approvalQuery = query(approvalQuery, where("createdAt", "<=", Timestamp.fromDate(filters.endDate)));
            }

            approvalQuery = query(approvalQuery, orderBy("createdAt", "desc"));

            const approvalSnapshot = await getDocs(approvalQuery);
            const approvals = [];

            for (const doc of approvalSnapshot.docs) {
                const data = doc.data();
                approvals.push({
                    id: doc.id,
                    ...data,
                    createdAt: data.createdAt.toDate(),
                    updatedAt: data.updatedAt.toDate(),
                    expiresAt: data.expiresAt.toDate(),
                    approvedAt: data.approvedAt?.toDate(),
                    rejectedAt: data.rejectedAt?.toDate(),
                    cancelledAt: data.cancelledAt?.toDate(),
                    expiredAt: data.expiredAt?.toDate()
                });
            }

            // Calcular estatísticas
            const stats = {
                total: approvals.length,
                pending: approvals.filter(a => a.status === 'PENDING').length,
                approved: approvals.filter(a => a.status === 'APPROVED').length,
                rejected: approvals.filter(a => a.status === 'REJECTED').length,
                cancelled: approvals.filter(a => a.status === 'CANCELLED').length,
                expired: approvals.filter(a => a.status === 'EXPIRED').length,
                averageApprovalTime: this.calculateAverageApprovalTime(approvals.filter(a => a.status === 'APPROVED'))
            };

            return {
                approvals,
                stats
            };

        } catch (error) {
            console.error('Erro ao gerar relatório de aprovações:', error);
            throw error;
        }
    }

    /**
     * ⏱️ CALCULAR TEMPO MÉDIO DE APROVAÇÃO
     */
    static calculateAverageApprovalTime(approvedApprovals) {
        if (approvedApprovals.length === 0) return 0;

        const totalTime = approvedApprovals.reduce((sum, approval) => {
            const createdTime = approval.createdAt.getTime();
            const approvedTime = approval.approvedAt.getTime();
            return sum + (approvedTime - createdTime);
        }, 0);

        return Math.round(totalTime / approvedApprovals.length / (1000 * 60 * 60)); // em horas
    }

    /**
     * 📝 LOG DE EVENTOS DE APROVAÇÃO
     */
    static async logApprovalEvent(eventType, data) {
        try {
            await addDoc(collection(db, "approvalLogs"), {
                eventType,
                data,
                timestamp: Timestamp.now(),
                ip: await AuthService.getUserIP(),
                userAgent: navigator.userAgent
            });
        } catch (error) {
            console.error('Erro ao registrar evento de aprovação:', error);
        }
    }
}
