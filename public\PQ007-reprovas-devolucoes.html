<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ007 - Reprovas e Devoluções</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #34495e;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #34495e;
            margin-right: 10px;
            width: 20px;
        }

        .rejection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .rejection-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .rejection-card:hover {
            transform: translateY(-5px);
        }

        .rejection-card.nova { border-left-color: #e74c3c; }
        .rejection-card.analise { border-left-color: #f39c12; }
        .rejection-card.devolucao { border-left-color: #3498db; }
        .rejection-card.finalizada { border-left-color: #95a5a6; }

        .rejection-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .rejection-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            text-transform: uppercase;
            margin-top: 5px;
        }

        .workflow-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .workflow-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .workflow-step {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
        }

        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #bdc3c7;
        }

        .workflow-step:last-child::after {
            display: none;
        }

        .workflow-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #34495e;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5em;
        }

        .workflow-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .workflow-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-nova {
            background: #f8d7da;
            color: #721c24;
        }

        .status-analise {
            background: #fff3cd;
            color: #856404;
        }

        .status-devolucao {
            background: #cce5ff;
            color: #004085;
        }

        .status-finalizada {
            background: #e2e3e5;
            color: #383d41;
        }

        .severity-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .severity-critica { background: #e74c3c; }
        .severity-alta { background: #f39c12; }
        .severity-media { background: #3498db; }
        .severity-baixa { background: #27ae60; }

        .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .rejection-grid {
                grid-template-columns: 1fr;
            }

            .workflow-steps {
                grid-template-columns: 1fr;
            }

            .workflow-step::after {
                content: '↓';
                right: 50%;
                top: auto;
                bottom: -15px;
                transform: translateX(50%);
            }

            .workflow-step:last-child::after {
                display: none;
            }

            .filters {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ PQ007 - Reprovas e Devoluções</h1>
            <p>Gestão de não conformidades e devoluções</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Gerenciar não conformidades, reprovas e processo de devolução</li>
                    <li><i class="fas fa-exclamation-triangle"></i><strong>Identificação:</strong> Registro e classificação de problemas de qualidade</li>
                    <li><i class="fas fa-undo"></i><strong>Devolução:</strong> Processo estruturado de retorno ao fornecedor</li>
                    <li><i class="fas fa-chart-line"></i><strong>Melhoria:</strong> Análise de causas e ações preventivas</li>
                </ul>
            </div>

            <!-- Cards de Reprovas -->
            <div class="rejection-grid">
                <div class="rejection-card nova">
                    <h4><i class="fas fa-exclamation-circle"></i> Novas Reprovas</h4>
                    <div class="rejection-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="novaCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="novaValor">R$ 0</div>
                            <div class="stat-label">Valor</div>
                        </div>
                    </div>
                    <button class="btn btn-danger btn-full" onclick="filtrarStatus('NOVA')">
                        <i class="fas fa-eye"></i> Ver Novas
                    </button>
                </div>

                <div class="rejection-card analise">
                    <h4><i class="fas fa-search"></i> Em Análise</h4>
                    <div class="rejection-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="analiseCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="analiseDias">0</div>
                            <div class="stat-label">Dias Médios</div>
                        </div>
                    </div>
                    <button class="btn btn-warning btn-full" onclick="filtrarStatus('ANALISE')">
                        <i class="fas fa-eye"></i> Ver Análise
                    </button>
                </div>

                <div class="rejection-card devolucao">
                    <h4><i class="fas fa-truck"></i> Em Devolução</h4>
                    <div class="rejection-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="devolucaoCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="devolucaoTransporte">0</div>
                            <div class="stat-label">Em Transporte</div>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" onclick="filtrarStatus('DEVOLUCAO')">
                        <i class="fas fa-eye"></i> Ver Devoluções
                    </button>
                </div>

                <div class="rejection-card finalizada">
                    <h4><i class="fas fa-check-circle"></i> Finalizadas</h4>
                    <div class="rejection-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="finalizadaCount">0</div>
                            <div class="stat-label">Itens</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="finalizadaResolucao">0</div>
                            <div class="stat-label">Dias Resolução</div>
                        </div>
                    </div>
                    <button class="btn btn-secondary btn-full" onclick="filtrarStatus('FINALIZADA')">
                        <i class="fas fa-eye"></i> Ver Finalizadas
                    </button>
                </div>
            </div>

            <!-- Fluxo do Processo -->
            <div class="workflow-section">
                <h3><i class="fas fa-sitemap"></i> Fluxo do Processo de Reprova/Devolução</h3>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="workflow-title">1. Identificação</div>
                        <div class="workflow-description">Detecção da não conformidade durante inspeção</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="workflow-title">2. Registro</div>
                        <div class="workflow-description">Documentação detalhada do problema encontrado</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="workflow-title">3. Análise</div>
                        <div class="workflow-description">Investigação de causas e definição de ações</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="workflow-title">4. Comunicação</div>
                        <div class="workflow-description">Notificação ao fornecedor sobre a não conformidade</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="workflow-title">5. Devolução</div>
                        <div class="workflow-description">Processo de retorno do material ao fornecedor</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="workflow-title">6. Fechamento</div>
                        <div class="workflow-description">Finalização com ações preventivas definidas</div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter">
                        <option value="">Todos os Status</option>
                        <option value="NOVA">Nova Reprova</option>
                        <option value="ANALISE">Em Análise</option>
                        <option value="DEVOLUCAO">Em Devolução</option>
                        <option value="FINALIZADA">Finalizada</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="severidadeFilter">Severidade</label>
                    <select id="severidadeFilter">
                        <option value="">Todas as Severidades</option>
                        <option value="CRITICA">Crítica</option>
                        <option value="ALTA">Alta</option>
                        <option value="MEDIA">Média</option>
                        <option value="BAIXA">Baixa</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="fornecedorFilter">Fornecedor</label>
                    <select id="fornecedorFilter">
                        <option value="">Todos os Fornecedores</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dataInicio">Data Início</label>
                    <input type="date" id="dataInicio">
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-danger" onclick="novaReprova()">
                    <i class="fas fa-plus"></i> Nova Reprova
                </button>
                <button class="btn btn-warning" onclick="analiseLote()">
                    <i class="fas fa-clipboard-list"></i> Análise em Lote
                </button>
                <button class="btn btn-primary" onclick="programarDevolucao()">
                    <i class="fas fa-truck"></i> Programar Devolução
                </button>
                <button class="btn btn-success" onclick="relatorioReprovas()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando reprovas e devoluções...</p>
            </div>

            <!-- Tabela de Reprovas -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Produto</th>
                            <th>Lote</th>
                            <th>Fornecedor</th>
                            <th>Motivo</th>
                            <th>Severidade</th>
                            <th>Data Reprova</th>
                            <th>Status</th>
                            <th>Responsável</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-check-circle"></i>
                <h3>Nenhuma reprova encontrada</h3>
                <p>Não há reprovas ou devoluções registradas no momento.</p>
                <button class="btn btn-primary" onclick="sincronizarInspecoes()" style="margin-top: 20px;">
                    <i class="fas fa-sync"></i> Sincronizar Inspeções
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let reprovas = [];
        let fornecedores = [];

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);

                // Carregar reprovas e fornecedores
                const [reprovasSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "reprovasDevolucoes")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                reprovas = reprovasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ007 - Dados carregados:', {
                    reprovas: reprovas.length,
                    fornecedores: fornecedores.length
                });

                populateFornecedorFilter();
                updateStats();
                renderTable();
                showLoading(false);

            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ007:', error);
                showLoading(false);
                showError('Erro ao carregar dados de reprovas e devoluções');
            }
        }

        function populateFornecedorFilter() {
            const select = document.getElementById('fornecedorFilter');
            select.innerHTML = '<option value="">Todos os Fornecedores</option>';

            fornecedores.forEach(fornecedor => {
                const option = document.createElement('option');
                option.value = fornecedor.id;
                option.textContent = fornecedor.razaoSocial || fornecedor.nome;
                select.appendChild(option);
            });
        }

        function updateStats() {
            const stats = {
                nova: { count: 0, valor: 0 },
                analise: { count: 0, dias: 0 },
                devolucao: { count: 0, transporte: 0 },
                finalizada: { count: 0, resolucao: 0 }
            };

            const today = new Date();

            reprovas.forEach(item => {
                const status = item.status?.toLowerCase() || 'nova';
                const valor = (item.quantidade || 0) * (item.valorUnitario || 0);

                if (stats[status]) {
                    stats[status].count++;

                    if (status === 'nova') {
                        stats[status].valor += valor;
                    }

                    if (status === 'analise' && item.dataInicio) {
                        const dataInicio = new Date(item.dataInicio.seconds * 1000);
                        const dias = Math.floor((today - dataInicio) / (1000 * 60 * 60 * 24));
                        stats[status].dias += dias;
                    }

                    if (status === 'devolucao' && item.emTransporte) {
                        stats[status].transporte++;
                    }

                    if (status === 'finalizada' && item.dataInicio && item.dataFechamento) {
                        const dataInicio = new Date(item.dataInicio.seconds * 1000);
                        const dataFechamento = new Date(item.dataFechamento.seconds * 1000);
                        const dias = Math.floor((dataFechamento - dataInicio) / (1000 * 60 * 60 * 24));
                        stats[status].resolucao += dias;
                    }
                }
            });

            // Calcular médias
            if (stats.analise.count > 0) {
                stats.analise.dias = Math.round(stats.analise.dias / stats.analise.count);
            }
            if (stats.finalizada.count > 0) {
                stats.finalizada.resolucao = Math.round(stats.finalizada.resolucao / stats.finalizada.count);
            }

            // Atualizar interface
            document.getElementById('novaCount').textContent = stats.nova.count;
            document.getElementById('novaValor').textContent = `R$ ${stats.nova.valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
            document.getElementById('analiseCount').textContent = stats.analise.count;
            document.getElementById('analiseDias').textContent = stats.analise.dias;
            document.getElementById('devolucaoCount').textContent = stats.devolucao.count;
            document.getElementById('devolucaoTransporte').textContent = stats.devolucao.transporte;
            document.getElementById('finalizadaCount').textContent = stats.finalizada.count;
            document.getElementById('finalizadaResolucao').textContent = stats.finalizada.resolucao;
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (reprovas.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = reprovas.map(item => {
                const fornecedor = fornecedores.find(f => f.id === item.fornecedorId);
                const dataReprova = item.dataReprova ?
                    new Date(item.dataReprova.seconds * 1000).toLocaleDateString() : 'N/A';

                const severidade = item.severidade || 'MEDIA';
                const severityClass = `severity-${severidade.toLowerCase()}`;

                return `
                    <tr>
                        <td><strong>${item.codigo || 'N/A'}</strong></td>
                        <td>${item.produtoNome || 'N/A'}</td>
                        <td>${item.lote || 'N/A'}</td>
                        <td>${fornecedor?.razaoSocial || 'N/A'}</td>
                        <td>${item.motivo || 'N/A'}</td>
                        <td>
                            <span class="severity-indicator ${severityClass}"></span>
                            ${severidade}
                        </td>
                        <td>${dataReprova}</td>
                        <td><span class="status-badge status-${item.status?.toLowerCase() || 'nova'}">${item.status || 'NOVA'}</span></td>
                        <td>${item.responsavel || 'N/A'}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarReprova('${item.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${item.status === 'NOVA' ? `
                                    <button class="btn btn-warning btn-action" onclick="iniciarAnalise('${item.id}')" title="Iniciar Análise">
                                        <i class="fas fa-play"></i>
                                    </button>
                                ` : ''}
                                ${item.status === 'ANALISE' ? `
                                    <button class="btn btn-primary btn-action" onclick="programarDevolucaoItem('${item.id}')" title="Programar Devolução">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                ` : ''}
                                ${item.status === 'DEVOLUCAO' ? `
                                    <button class="btn btn-success btn-action" onclick="finalizarReprova('${item.id}')" title="Finalizar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Funções globais
        window.filtrarStatus = function(status) {
            document.getElementById('statusFilter').value = status;
            aplicarFiltros();
        };

        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar filtros
        };

        window.novaReprova = function() {
            alert('❌ Funcionalidade em desenvolvimento: Nova Reprova');
        };

        window.analiseLote = function() {
            alert('📋 Funcionalidade em desenvolvimento: Análise em Lote');
        };

        window.programarDevolucao = function() {
            alert('🚚 Funcionalidade em desenvolvimento: Programar Devolução');
        };

        window.relatorioReprovas = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório de Reprovas');
        };

        window.sincronizarInspecoes = function() {
            alert('🔄 Funcionalidade em desenvolvimento: Sincronizar Inspeções');
        };

        window.visualizarReprova = function(id) {
            alert('👁️ Funcionalidade em desenvolvimento: Visualizar Reprova ' + id);
        };

        window.iniciarAnalise = function(id) {
            alert('▶️ Funcionalidade em desenvolvimento: Iniciar Análise ' + id);
        };

        window.programarDevolucaoItem = function(id) {
            alert('🚚 Funcionalidade em desenvolvimento: Programar Devolução ' + id);
        };

        window.finalizarReprova = function(id) {
            alert('✅ Funcionalidade em desenvolvimento: Finalizar Reprova ' + id);
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>