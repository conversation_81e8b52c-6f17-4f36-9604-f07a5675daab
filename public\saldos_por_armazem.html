<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Saldos por Armazém</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 0;
        }

        .form-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .form-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 25px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-weight: 600;
            position: relative;
        }

        .form-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #e74c3c, #f39c12);
            border-radius: 2px;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-right: 10px;
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(149, 165, 166, 0.3);
            font-weight: 600;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
            font-weight: 600;
            margin-left: 10px;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .table-responsive {
            margin-top: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .items-table th,
        .items-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .items-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
            position: relative;
        }

        .items-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(135deg, #e74c3c, #f39c12);
        }

        .items-table tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }

        .loading i {
            font-size: 3rem;
            animation: spin 1s linear infinite;
            color: #3498db;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 12px;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .header {
                padding: 20px;
            }

            .form-container {
                padding: 20px;
            }

            .items-table th, .items-table td {
                padding: 10px 12px;
                font-size: 13px;
            }

            .btn-success {
                padding: 12px 20px;
                font-size: 14px;
            }

            .filters {
                flex-direction: column;
                align-items: stretch;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .badge {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Animações */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-container, .table-responsive {
            animation: slideIn 0.6s ease-out;
        }

        /* Scrollbar personalizada */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 10px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2980b9, #3498db);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-warehouse"></i> Saldos por Armazém</h1>
            <p>Visualização completa dos estoques por armazém</p>
        </div>

        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-number" id="totalProdutos">0</div>
                <div class="stat-label">Produtos com Estoque</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalArmazens">0</div>
                <div class="stat-label">Armazéns Ativos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRegistros">0</div>
                <div class="stat-label">Total de Registros</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="saldoBaixo">0</div>
                <div class="stat-label">Saldos Baixos (&lt;10)</div>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label>Filtrar por Produto:</label>
                <input type="text" id="filterProduct" placeholder="Digite código ou descrição...">
            </div>
            <div class="filter-group">
                <label>Filtrar por Armazém:</label>
                <select id="filterWarehouse">
                    <option value="">Todos os Armazéns</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Tipo de Produto:</label>
                <select id="filterType">
                    <option value="">Todos os Tipos</option>
                </select>
            </div>
        </div>

        <div class="form-container">
            <div style="margin-bottom: 20px;">
                <button class="btn-success" onclick="loadBalanceTable()">
                    <i class="fas fa-chart-bar"></i>
                    Carregar Saldos
                </button>
                <button class="btn-secondary" onclick="clearBalanceTable()">
                    <i class="fas fa-trash"></i>
                    Limpar Tabela
                </button>
                <button class="btn-info" onclick="exportToCSV()">
                    <i class="fas fa-download"></i>
                    Exportar CSV
                </button>
                <span style="margin-left: 15px; color: #666; font-size: 14px;">
                    💡 Clique em "Carregar Saldos" para visualizar os estoques
                </span>
            </div>
            <div class="table-responsive">
                <table class="items-table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-box"></i> Produto</th>
                            <th><i class="fas fa-tag"></i> Tipo</th>
                            <th><i class="fas fa-ruler"></i> Unidade</th>
                            <th><i class="fas fa-warehouse"></i> Armazém</th>
                            <th><i class="fas fa-calculator"></i> Saldo</th>
                        </tr>
                    </thead>
                    <tbody id="balanceTableBody">
                        <tr>
                            <td colspan="5" style="text-align: center; color: #666; padding: 40px;">
                                <div style="font-size: 18px; margin-bottom: 10px;">📊</div>
                                <div>Clique em "Carregar Saldos" para visualizar os estoques por armazém</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let produtos = [];
        let armazens = [];
        let estoques = [];
        let dadosFiltrados = [];
        let balanceTableLoaded = false;

        // Funções de loading
        function showLoading(message = "Carregando...") {
            const existingLoader = document.getElementById('globalLoader');
            if (existingLoader) existingLoader.remove();

            const loader = document.createElement('div');
            loader.id = 'globalLoader';
            loader.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;
            
            loader.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 15px;"></div>
                    <div style="color: #2c3e50; font-weight: 600;">${message}</div>
                </div>
            `;

            document.body.appendChild(loader);
        }

        function hideLoading() {
            const loader = document.getElementById('globalLoader');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => {
                    if (loader.parentNode) {
                        loader.parentNode.removeChild(loader);
                    }
                }, 300);
            }
        }

        window.onload = async function() {
            await loadInitialData();
            setupFilters();
        };

        async function loadInitialData() {
            try {
                showLoading("Carregando dados iniciais...");

                const [produtosSnap, armazensSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "estoques"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`Carregados: ${produtos.length} produtos, ${armazens.length} armazéns, ${estoques.length} estoques`);
                
                hideLoading();
                populateFilters();
                
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                hideLoading();
                alert("Erro ao carregar dados: " + error.message);
            }
        }

        function populateFilters() {
            // Popular filtro de armazéns
            const warehouseFilter = document.getElementById('filterWarehouse');
            warehouseFilter.innerHTML = '<option value="">Todos os Armazéns</option>';

            armazens
                .filter(a => a.ativo !== false)
                .sort((a, b) => (a.codigo || '').localeCompare(b.codigo || ''))
                .forEach(armazem => {
                    warehouseFilter.innerHTML += `
                        <option value="${armazem.id}">
                            ${armazem.codigo} - ${armazem.nome}
                        </option>`;
                });

            // Popular filtro de tipos
            const typeFilter = document.getElementById('filterType');
            const tipos = [...new Set(produtos.map(p => p.tipo).filter(Boolean))].sort();

            typeFilter.innerHTML = '<option value="">Todos os Tipos</option>';
            tipos.forEach(tipo => {
                typeFilter.innerHTML += `<option value="${tipo}">${tipo}</option>`;
            });
        }

        function setupFilters() {
            document.getElementById('filterProduct').addEventListener('input', applyFilters);
            document.getElementById('filterWarehouse').addEventListener('change', applyFilters);
            document.getElementById('filterType').addEventListener('change', applyFilters);
        }

        function applyFilters() {
            if (!balanceTableLoaded) return;

            const productFilter = document.getElementById('filterProduct').value.toLowerCase();
            const warehouseFilter = document.getElementById('filterWarehouse').value;
            const typeFilter = document.getElementById('filterType').value;

            dadosFiltrados = dadosFiltrados.filter(item => {
                const matchProduct = !productFilter ||
                    item.produto.codigo.toLowerCase().includes(productFilter) ||
                    item.produto.descricao.toLowerCase().includes(productFilter);

                const matchWarehouse = !warehouseFilter || item.estoque.armazemId === warehouseFilter;
                const matchType = !typeFilter || item.produto.tipo === typeFilter;

                return matchProduct && matchWarehouse && matchType;
            });

            renderTable();
            updateStats();
        }

        window.loadBalanceTable = function() {
            const tableBody = document.getElementById('balanceTableBody');

            if (!balanceTableLoaded) {
                showLoading("Carregando saldos de estoque...");
            }

            try {
                if (!estoques.length || !produtos.length || !armazens.length) {
                    hideLoading();
                    tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #d32f2f;"><i class="fas fa-exclamation-triangle"></i> Dados não carregados. Recarregue a página.</td></tr>';
                    return;
                }

                // Filtrar apenas estoques com saldo positivo
                const estoquesPositivos = estoques.filter(estoque => estoque.saldo > 0);

                if (estoquesPositivos.length === 0) {
                    hideLoading();
                    tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #666;"><i class="fas fa-info-circle"></i> Nenhum produto com saldo em estoque</td></tr>';
                    return;
                }

                // Criar mapas para acesso rápido
                const produtosMap = new Map(produtos.map(p => [p.id, p]));
                const armazensMap = new Map(armazens.map(a => [a.id, a]));

                // Preparar dados combinados
                dadosFiltrados = estoquesPositivos
                    .map(estoque => {
                        const produto = produtosMap.get(estoque.produtoId);
                        const armazem = armazensMap.get(estoque.armazemId);

                        if (!produto) return null;

                        return {
                            produto,
                            armazem,
                            estoque
                        };
                    })
                    .filter(Boolean)
                    .sort((a, b) => (a.produto.codigo || '').localeCompare(b.produto.codigo || ''));

                renderTable();
                updateStats();
                balanceTableLoaded = true;
                hideLoading();

            } catch (error) {
                console.error("Erro ao carregar saldos:", error);
                hideLoading();
                tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #d32f2f;"><i class="fas fa-exclamation-triangle"></i> Erro ao carregar saldos</td></tr>';
            }
        };

        function renderTable() {
            const tableBody = document.getElementById('balanceTableBody');

            if (dadosFiltrados.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #666;"><i class="fas fa-info-circle"></i> Nenhum resultado encontrado com os filtros aplicados</td></tr>';
                return;
            }

            // Agrupar por produto
            const produtoGroups = {};
            dadosFiltrados.forEach(item => {
                if (!produtoGroups[item.produto.id]) {
                    produtoGroups[item.produto.id] = [];
                }
                produtoGroups[item.produto.id].push(item);
            });

            const fragment = document.createDocumentFragment();

            Object.values(produtoGroups).forEach(group => {
                group.forEach((item, index) => {
                    const row = document.createElement('tr');

                    // Destacar linha se for armazém de produção
                    if (item.armazem && item.armazem.tipo === 'PRODUCAO') {
                        row.style.backgroundColor = '#e8f5e8';
                    }

                    row.innerHTML = `
                        <td>${index === 0 ? `<strong>${item.produto.codigo || 'SEM CÓDIGO'}</strong><br><small>${item.produto.descricao || 'Sem descrição'}</small>` : ''}</td>
                        <td>${index === 0 ? `<span class="badge">${item.produto.tipo || 'N/A'}</span>` : ''}</td>
                        <td>${index === 0 ? `${item.produto.unidade || 'UN'}` : ''}</td>
                        <td><i class="fas fa-warehouse"></i> ${item.armazem ? `${item.armazem.codigo} - ${item.armazem.nome}` : 'Armazém não encontrado'}</td>
                        <td style="text-align: right; font-weight: bold; ${item.estoque.saldo < 10 ? 'color: #d32f2f;' : 'color: #107e3e;'}">
                            ${item.estoque.saldo.toFixed(3)}
                        </td>
                    `;

                    fragment.appendChild(row);
                });
            });

            tableBody.innerHTML = '';
            tableBody.appendChild(fragment);
        }

        function updateStats() {
            const totalProdutos = new Set(dadosFiltrados.map(item => item.produto.id)).size;
            const totalArmazens = new Set(dadosFiltrados.map(item => item.estoque.armazemId)).size;
            const totalRegistros = dadosFiltrados.length;
            const saldoBaixo = dadosFiltrados.filter(item => item.estoque.saldo < 10).length;

            document.getElementById('totalProdutos').textContent = totalProdutos;
            document.getElementById('totalArmazens').textContent = totalArmazens;
            document.getElementById('totalRegistros').textContent = totalRegistros;
            document.getElementById('saldoBaixo').textContent = saldoBaixo;
        }

        window.clearBalanceTable = function() {
            const tableBody = document.getElementById('balanceTableBody');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" style="text-align: center; color: #666; padding: 40px;">
                        <div style="font-size: 18px; margin-bottom: 10px;">📊</div>
                        <div>Clique em "Carregar Saldos" para visualizar os estoques por armazém</div>
                    </td>
                </tr>
            `;

            // Resetar estatísticas
            document.getElementById('totalProdutos').textContent = '0';
            document.getElementById('totalArmazens').textContent = '0';
            document.getElementById('totalRegistros').textContent = '0';
            document.getElementById('saldoBaixo').textContent = '0';

            balanceTableLoaded = false;
            dadosFiltrados = [];
        };

        window.exportToCSV = function() {
            if (!dadosFiltrados.length) {
                alert('Não há dados para exportar. Carregue os saldos primeiro.');
                return;
            }

            const headers = ['Código', 'Descrição', 'Tipo', 'Unidade', 'Armazém Código', 'Armazém Nome', 'Saldo'];

            const csvContent = [
                headers.join(','),
                ...dadosFiltrados.map(item => [
                    `"${item.produto.codigo || ''}"`,
                    `"${item.produto.descricao || ''}"`,
                    `"${item.produto.tipo || ''}"`,
                    `"${item.produto.unidade || ''}"`,
                    `"${item.armazem ? item.armazem.codigo : ''}"`,
                    `"${item.armazem ? item.armazem.nome : ''}"`,
                    item.estoque.saldo.toFixed(3)
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `saldos_por_armazem_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };

    </script>
</body>
</html>
