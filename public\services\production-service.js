// Serviço centralizado para gestão de produção
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  runTransaction, 
  addDoc, 
  updateDoc, 
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class ProductionService {
  
  /**
   * Cria ordem de produção com explosão automática de BOM
   */
  static async createProductionOrder(orderData) {
    try {
      const result = await runTransaction(db, async (transaction) => {
        // 1. Criar ordem principal
        const orderRef = doc(collection(db, "ordensProducao"));
        const mainOrder = {
          numero: await this.generateOrderNumber(),
          produtoId: orderData.produtoId,
          quantidade: orderData.quantidade,
          dataEntrega: orderData.dataEntrega,
          status: 'PENDENTE',
          nivel: 0,
          prioridade: orderData.prioridade || 'NORMAL',
          centroCustoId: orderData.centroCustoId,
          armazemProducaoId: orderData.armazemProducaoId,
          dataCriacao: Timestamp.now(),
          criadoPor: orderData.usuario
        };
        
        transaction.set(orderRef, mainOrder);
        
        // 2. Explodir BOM e calcular necessidades
        const bomExplosion = await this.explodeBOM(
          orderData.produtoId, 
          orderData.quantidade,
          orderData.armazemProducaoId
        );
        
        // 3. Calcular empenhos
        const empenhos = await this.calculateReservations(bomExplosion);
        
        // 4. Atualizar ordem com materiais necessários
        transaction.update(orderRef, {
          materiaisNecessarios: bomExplosion.materiais,
          ordensFilhas: bomExplosion.ordensFilhas,
          empenhos: empenhos
        });
        
        // 5. Criar ordens filhas para SPs
        for (const ordemFilha of bomExplosion.ordensFilhas) {
          const filhaRef = doc(collection(db, "ordensProducao"));
          transaction.set(filhaRef, {
            ...ordemFilha,
            numero: await this.generateOrderNumber(),
            ordemPaiId: orderRef.id,
            dataCriacao: Timestamp.now()
          });
        }
        
        // 6. Atualizar empenhos nos estoques
        for (const empenho of empenhos) {
          const estoqueRef = doc(db, "estoques", empenho.estoqueId);
          transaction.update(estoqueRef, {
            saldoReservado: empenho.novoSaldoReservado,
            ultimaMovimentacao: Timestamp.now()
          });
        }
        
        return {
          orderId: orderRef.id,
          orderNumber: mainOrder.numero,
          materialsCount: bomExplosion.materiais.length,
          childOrdersCount: bomExplosion.ordensFilhas.length
        };
      });
      
      console.log('Ordem de produção criada:', result);
      return result;
      
    } catch (error) {
      console.error('Erro ao criar ordem de produção:', error);
      throw new Error(`Falha na criação da OP: ${error.message}`);
    }
  }
  
  /**
   * Explode BOM recursivamente
   */
  static async explodeBOM(produtoId, quantidade, armazemId, nivel = 0) {
    if (nivel > 10) throw new Error('BOM muito profunda - possível ciclo');
    
    // Buscar estrutura do produto
    const estruturaQuery = query(
      collection(db, "estruturas"),
      where("produtoPaiId", "==", produtoId)
    );
    const estruturaSnap = await getDocs(estruturaQuery);
    
    if (estruturaSnap.empty) {
      return { materiais: [], ordensFilhas: [] };
    }
    
    const estrutura = estruturaSnap.docs[0].data();
    const materiais = [];
    const ordensFilhas = [];
    
    // Buscar produtos e estoques
    const produtosSnap = await getDocs(collection(db, "produtos"));
    const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    const estoquesSnap = await getDocs(collection(db, "estoques"));
    const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    for (const componente of estrutura.componentes) {
      const produto = produtos.find(p => p.id === componente.componentId);
      const quantidadeNecessaria = quantidade * componente.quantidade;
      
      if (produto.tipo === 'SP' || produto.tipo === 'PA') {
        // Produto semi-acabado - criar ordem filha
        const subExplosion = await this.explodeBOM(
          componente.componentId,
          quantidadeNecessaria,
          armazemId,
          nivel + 1
        );
        
        ordensFilhas.push({
          produtoId: componente.componentId,
          quantidade: quantidadeNecessaria,
          nivel: nivel + 1,
          status: 'PENDENTE',
          armazemProducaoId: armazemId,
          materiaisNecessarios: subExplosion.materiais
        });
        
        // Adicionar ordens filhas recursivas
        ordensFilhas.push(...subExplosion.ordensFilhas);
        
      } else {
        // Matéria-prima - calcular necessidade
        const estoque = estoques.find(e => 
          e.produtoId === componente.componentId && 
          e.armazemId === armazemId
        );
        
        const saldoDisponivel = estoque ? 
          (estoque.saldo - (estoque.saldoReservado || 0)) : 0;
        
        const necessidade = Math.max(0, quantidadeNecessaria - saldoDisponivel);
        
        materiais.push({
          produtoId: componente.componentId,
          codigo: produto.codigo,
          descricao: produto.descricao,
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          saldoEstoque: estoque?.saldo || 0,
          saldoReservado: estoque?.saldoReservado || 0,
          saldoDisponivel,
          necessidade,
          tipo: produto.tipo,
          nivel
        });
      }
    }
    
    return { materiais, ordensFilhas };
  }
  
  /**
   * Calcula reservas de material
   */
  static async calculateReservations(bomExplosion) {
    const empenhos = [];
    
    for (const material of bomExplosion.materiais) {
      if (material.saldoDisponivel >= material.quantidade) {
        // Pode empenhar totalmente
        empenhos.push({
          produtoId: material.produtoId,
          estoqueId: material.estoqueId,
          quantidadeEmpenhada: material.quantidade,
          novoSaldoReservado: material.saldoReservado + material.quantidade
        });
      } else if (material.saldoDisponivel > 0) {
        // Empenho parcial
        empenhos.push({
          produtoId: material.produtoId,
          estoqueId: material.estoqueId,
          quantidadeEmpenhada: material.saldoDisponivel,
          novoSaldoReservado: material.saldoReservado + material.saldoDisponivel
        });
      }
    }
    
    return empenhos;
  }
  
  /**
   * Registra apontamento de produção
   */
  static async recordProduction(appointmentData) {
    try {
      const result = await runTransaction(db, async (transaction) => {
        // 1. Buscar ordem de produção
        const orderRef = doc(db, "ordensProducao", appointmentData.orderId);
        const orderDoc = await transaction.get(orderRef);
        
        if (!orderDoc.exists()) {
          throw new Error('Ordem de produção não encontrada');
        }
        
        const order = orderDoc.data();
        
        // 2. Validar quantidade
        const novaQuantidadeProduzida = (order.quantidadeProduzida || 0) + appointmentData.quantidade;
        if (novaQuantidadeProduzida > order.quantidade) {
          throw new Error('Quantidade produzida excede o planejado');
        }
        
        // 3. Consumir materiais
        const consumos = [];
        if (order.materiaisNecessarios) {
          for (const material of order.materiaisNecessarios) {
            const quantidadeConsumida = (material.quantidade / order.quantidade) * appointmentData.quantidade;
            
            // Buscar estoque
            const estoqueQuery = query(
              collection(db, "estoques"),
              where("produtoId", "==", material.produtoId),
              where("armazemId", "==", order.armazemProducaoId)
            );
            const estoqueSnap = await getDocs(estoqueQuery);
            
            if (!estoqueSnap.empty) {
              const estoqueDoc = estoqueSnap.docs[0];
              const estoque = estoqueDoc.data();
              
              // Atualizar estoque
              transaction.update(estoqueDoc.ref, {
                saldo: estoque.saldo - quantidadeConsumida,
                saldoReservado: Math.max(0, (estoque.saldoReservado || 0) - quantidadeConsumida),
                ultimaMovimentacao: Timestamp.now()
              });
              
              // Registrar movimentação de consumo
              const movConsumoRef = doc(collection(db, "movimentacoesEstoque"));
              transaction.set(movConsumoRef, {
                produtoId: material.produtoId,
                armazemId: order.armazemProducaoId,
                tipo: 'SAIDA',
                quantidade: quantidadeConsumida,
                tipoDocumento: 'PRODUCAO',
                numeroDocumento: order.numero,
                observacoes: `Consumo OP ${order.numero}`,
                dataHora: Timestamp.now(),
                apontamentoId: appointmentData.id
              });
              
              consumos.push({
                produtoId: material.produtoId,
                quantidade: quantidadeConsumida
              });
            }
          }
        }
        
        // 4. Dar entrada do produto acabado
        const produtoAcabadoQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", order.produtoId),
          where("armazemId", "==", order.armazemProducaoId)
        );
        const produtoAcabadoSnap = await getDocs(produtoAcabadoQuery);
        
        if (!produtoAcabadoSnap.empty) {
          const estoqueDoc = produtoAcabadoSnap.docs[0];
          const estoque = estoqueDoc.data();
          
          transaction.update(estoqueDoc.ref, {
            saldo: estoque.saldo + appointmentData.quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          // Criar novo estoque
          const novoEstoqueRef = doc(collection(db, "estoques"));
          transaction.set(novoEstoqueRef, {
            produtoId: order.produtoId,
            armazemId: order.armazemProducaoId,
            saldo: appointmentData.quantidade,
            saldoReservado: 0,
            ultimaMovimentacao: Timestamp.now()
          });
        }
        
        // 5. Registrar movimentação de produção
        const movProducaoRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movProducaoRef, {
          produtoId: order.produtoId,
          armazemId: order.armazemProducaoId,
          tipo: 'ENTRADA',
          quantidade: appointmentData.quantidade,
          tipoDocumento: 'PRODUCAO',
          numeroDocumento: order.numero,
          observacoes: `Produção OP ${order.numero}`,
          dataHora: Timestamp.now(),
          apontamentoId: appointmentData.id
        });
        
        // 6. Atualizar ordem de produção
        const novoStatus = novaQuantidadeProduzida >= order.quantidade ? 'FINALIZADA' : 'EM_PRODUCAO';
        
        transaction.update(orderRef, {
          quantidadeProduzida: novaQuantidadeProduzida,
          status: novoStatus,
          ultimoApontamento: Timestamp.now(),
          refugo: (order.refugo || 0) + (appointmentData.refugo || 0)
        });
        
        // 7. Registrar apontamento
        const apontamentoRef = doc(collection(db, "apontamentos"));
        transaction.set(apontamentoRef, {
          ordemProducaoId: appointmentData.orderId,
          quantidade: appointmentData.quantidade,
          refugo: appointmentData.refugo || 0,
          observacoes: appointmentData.observacoes,
          operadorId: appointmentData.operadorId,
          dataHora: Timestamp.now(),
          consumos
        });
        
        return {
          apontamentoId: apontamentoRef.id,
          quantidadeProduzida: novaQuantidadeProduzida,
          status: novoStatus,
          consumos: consumos.length
        };
      });
      
      console.log('Apontamento registrado:', result);
      return result;
      
    } catch (error) {
      console.error('Erro no apontamento:', error);
      throw new Error(`Falha no apontamento: ${error.message}`);
    }
  }
  
  /**
   * Gera número sequencial para ordem
   */
  static async generateOrderNumber() {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Buscar último número
    const lastOrder = await getDocs(
      query(
        collection(db, "ordensProducao"),
        where("numero", ">=", `OP${year}${month}`),
        where("numero", "<", `OP${year}${month}9999`),
        orderBy("numero", "desc")
      )
    );
    
    let nextNumber = 1;
    if (!lastOrder.empty) {
      const lastNumber = lastOrder.docs[0].data().numero;
      const lastSequence = parseInt(lastNumber.slice(-4));
      nextNumber = lastSequence + 1;
    }
    
    return `OP${year}${month}${String(nextNumber).padStart(4, '0')}`;
  }
}

// Exportar para uso global
window.ProductionService = ProductionService;
