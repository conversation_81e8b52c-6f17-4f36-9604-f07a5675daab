<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Categorias - Manutenção</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            min-height: 80vh;
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .panel {
            padding: 20px;
            overflow-y: auto;
        }

        .left-panel {
            width: 30%;
            border-right: 1px solid var(--border-color);
        }

        .right-panel {
            width: 70%;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--primary-color);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 10px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: var(--danger-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .category-list {
            list-style: none;
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .category-item {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            background-color: #fff;
            transition: background-color 0.2s;
        }

        .category-item:last-child {
            border-bottom: none;
        }

        .category-item:hover {
            background-color: var(--secondary-color);
        }

        .category-item.selected {
            background-color: var(--primary-color);
            color: white;
        }

        .category-item.selected:hover {
            background-color: var(--primary-hover);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        .data-table th {
            background-color: var(--secondary-color);
            font-weight: 600;
            color: var(--text-secondary);
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        .add-item-form {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 20px;
        }

        .add-item-form .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .add-item-form button {
            flex-shrink: 0;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
            align-items: center;
            gap: 10px;
            transition: opacity 0.3s ease;
        }

        .notification-success { background-color: var(--success-color); }
        .notification-error { background-color: var(--danger-color); }
        .notification-icon { font-weight: bold; font-size: 18px; }

        @media (max-width: 768px) {
            .container { flex-direction: column; }
            .left-panel, .right-panel {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }
            .add-item-form {
                flex-direction: column;
                align-items: stretch;
            }
            .add-item-form button { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Cadastro de Categorias</h1>
        <div>
            <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <div class="container">
        <div class="panel left-panel">
            <h2 class="panel-title">Tipos de Categoria</h2>
            <ul id="categoryTypeList" class="category-list">
                <li class="category-item" data-category-type="linha">Linha</li>
                <li class="category-item" data-category-type="secao">Seção</li>
                <li class="category-item" data-category-type="area">Área</li>
                <li class="category-item" data-category-type="local">Local</li>
                <li class="category-item" data-category-type="tipo">Tipo</li>
            </ul>
        </div>

        <div class="panel right-panel">
            <h2 class="panel-title" id="categoryContentTitle">Selecione um Tipo de Categoria</h2>
            <div id="categoryContent" style="display: none;">
                <div class="add-item-form">
                    <div class="form-group">
                        <label for="categoryName">Nome da Categoria</label>
                        <input type="text" id="categoryName" placeholder="Nome">
                    </div>
                    <button class="btn btn-primary" onclick="addItem()"><i class="fas fa-plus"></i> Adicionar</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="categoryTableBody">
                        <!-- Itens da categoria serão exibidos aqui -->
                    </tbody>
                </table>
            </div>
            <div id="noCategorySelectedMessage">
                <p>Selecione um tipo de categoria na lista ao lado para visualizar e gerenciar os itens.</p>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, doc, setDoc, deleteDoc, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentCategoryType = null;

        window.onload = async function() {
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

            const currentUser = JSON.parse(userSession);
            if (currentUser.nivel < 7) {
                showNotification('Acesso restrito. Nível de permissão insuficiente.', 'error');
                window.location.href = 'index.html';
                return;
            }

            setupCategoryTypeSelection();
        };

        function setupCategoryTypeSelection() {
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('click', () => selectCategoryType(item.dataset.categoryType));
            });
        }

        async function selectCategoryType(categoryType) {
            currentCategoryType = categoryType;

            const categoryContentTitle = document.getElementById('categoryContentTitle');
            categoryContentTitle.textContent = `Itens de ${categoryType.charAt(0).toUpperCase() + categoryType.slice(1)}`;

            document.querySelectorAll('.category-item').forEach(item => item.classList.remove('selected'));
            document.querySelector(`.category-item[data-category-type='${categoryType}']`).classList.add('selected');

            document.getElementById('noCategorySelectedMessage').style.display = 'none';
            document.getElementById('categoryContent').style.display = 'block';

            await loadCategoryItems(currentCategoryType);
        }

        async function loadCategoryItems(categoryType) {
            try {
                const q = query(collection(db, "categorias"), where("tipo", "==", categoryType));
                const querySnapshot = await getDocs(q);
                const items = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                displayCategoryItems(items);
            } catch (error) {
                console.error("Erro ao carregar itens da categoria:", error);
                showNotification(`Erro ao carregar itens de ${categoryType}.`, "error");
                displayCategoryItems([]);
            }
        }

        function displayCategoryItems(items) {
            const categoryTableBody = document.getElementById('categoryTableBody');
            categoryTableBody.innerHTML = '';

            items.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.nome || '-'}</td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="removeItem('${item.id}', '${item.nome}')">Remover</button>
                    </td>
                `;
                categoryTableBody.appendChild(row);
            });
        }

        window.addItem = async function() {
            if (!currentCategoryType) {
                showNotification('Selecione um tipo de categoria primeiro.', 'warning');
                return;
            }

            const categoryNameInput = document.getElementById('categoryName');
            const itemName = categoryNameInput.value.trim();

            if (!itemName) {
                showNotification('Informe o nome do item.', 'warning');
                return;
            }

            try {
                const existingQuery = query(collection(db, "categorias"), where("tipo", "==", currentCategoryType), where("nome", "==", itemName));
                const existingSnapshot = await getDocs(existingQuery);

                if (!existingSnapshot.empty) {
                    showNotification(`O item '${itemName}' já existe para ${currentCategoryType}.`, 'warning');
                    return;
                }

                const newItemRef = doc(collection(db, "categorias"));
                await setDoc(newItemRef, {
                    tipo: currentCategoryType,
                    nome: itemName
                });

                showNotification(`Item '${itemName}' adicionado com sucesso à ${currentCategoryType}s!`, 'success');
                await loadCategoryItems(currentCategoryType);
                categoryNameInput.value = '';

            } catch (error) {
                console.error("Erro ao adicionar item da categoria:", error);
                showNotification("Erro ao adicionar item da categoria. Por favor, tente novamente.", "error");
            }
        };

        window.removeItem = async function(itemId, itemName) {
            if (confirm(`Tem certeza que deseja remover o item '${itemName}'?`)) {
                try {
                    await deleteDoc(doc(db, "categorias", itemId));
                    showNotification(`Item '${itemName}' removido com sucesso!`, 'success');
                    await loadCategoryItems(currentCategoryType);
                } catch (error) {
                    console.error("Erro ao remover item da categoria:", error);
                    showNotification("Erro ao remover item da categoria. Por favor, tente novamente.", "error");
                }
            }
        };

        function showNotification(message, type = 'success', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = '';
            notification.classList.add('notification', `notification-${type}`);
            notification.style.display = 'block';

            let icon = '';
            if (type === 'success') {
                icon = '✓';
            } else if (type === 'error') {
                icon = '✗';
            }

            notification.innerHTML = `<span class="notification-icon">${icon}</span> ${message}`;

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.style.display = 'none', 300);
            }, duration);
        }
    </script>
</body>
</html> 