<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Backup e Importação de Ordens</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.4/xlsx.full.min.js"></script>
</head>
<body>
  <div class="container">
    <h1>Backup e Importação de Ordens de Produção</h1>
    <button onclick="exportToExcel()">Exportar para Excel</button>
    <input type="file" id="excelFile" accept=".xlsx, .xls" onchange="importFromExcel(event)">
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, addDoc, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    window.exportToExcel = async function() {
      const ordensSnap = await getDocs(collection(db, "ordensProducao"));
      const ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      const data = ordens.map(op => ({
        "Número da OP": op.numero,
        "Produto ID": op.produtoId,
        "Quantidade": op.quantidade,
        "Status": op.status,
        "Data de Criação": new Date(op.dataCriacao.seconds * 1000).toLocaleString(),
        "Pedido ID": op.pedidoId || "N/A",
        "Produto Pai ID": op.produtoPaiId || "N/A",
        "Materiais Necessários": op.materiaisNecessarios 
          ? JSON.stringify(op.materiaisNecessarios) 
          : "N/A"
      }));

      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Ordens de Produção");
      XLSX.writeFile(workbook, "backup_ordens_producao.xlsx");
      alert('Backup exportado com sucesso!');
    };

    window.importFromExcel = function(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = async function(e) {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        for (const row of jsonData) {
          const op = {
            numero: row["Número da OP"],
            produtoId: row["Produto ID"],
            quantidade: row["Quantidade"],
            status: row["Status"],
            dataCriacao: Timestamp.fromDate(new Date(row["Data de Criação"])),
            pedidoId: row["Pedido ID"] === "N/A" ? null : row["Pedido ID"],
            produtoPaiId: row["Produto Pai ID"] === "N/A" ? null : row["Produto Pai ID"],
            materiaisNecessarios: row["Materiais Necessários"] === "N/A" 
              ? null 
              : JSON.parse(row["Materiais Necessários"])
          };

          if (!op.numero || !op.produtoId || !op.quantidade || !op.status) {
            console.error('Dados inválidos na linha:', row);
            continue;
          }

          try {
            await addDoc(collection(db, "ordensProducao"), op);
          } catch (error) {
            console.error('Erro ao importar OP:', op.numero, error);
          }
        }

        alert('Importação concluída!');
      };

      reader.readAsArrayBuffer(file);
    };
  </script>
</body>
</html>