<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Iniciali<PERSON> - Wizhar ERP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #3498db;
        }

        .step h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .step p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            height: 100%;
            transition: width 0.3s ease;
            width: 0%;
        }

        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            display: none;
        }

        .log.show {
            display: block;
        }

        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checklist li:last-child {
            border-bottom: none;
        }

        .checklist .icon {
            width: 20px;
            text-align: center;
        }

        .checklist .pending {
            color: #f39c12;
        }

        .checklist .success {
            color: #27ae60;
        }

        .checklist .error {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> Inicializar Sistema de Empenhos</h1>
            <p>Configure o sistema de empenhos para controle avançado de materiais</p>
        </div>

        <div class="content">
            <div class="step">
                <h3><i class="fas fa-info-circle"></i> O que são Empenhos?</h3>
                <p>
                    <strong>Empenhos</strong> são o próximo nível após as reservas. Quando uma OP inicia produção, 
                    as reservas são transferidas para empenhos, garantindo controle total sobre materiais em uso.
                </p>
                <p>
                    <strong>Fluxo:</strong> Reserva → Empenho → Consumo → Baixa no Estoque
                </p>
            </div>

            <div class="step">
                <h3><i class="fas fa-database"></i> Inicialização Necessária</h3>
                <p>
                    Para ativar o sistema de empenhos, precisamos adicionar o campo <code>saldoEmpenhado</code> 
                    em todos os registros de estoque existentes.
                </p>
                
                <div class="status status-info">
                    <i class="fas fa-info-circle"></i>
                    Esta operação é segura e não afeta os dados existentes.
                </div>

                <button class="btn" onclick="verificarSistema()" id="btnVerificar">
                    <i class="fas fa-search"></i>
                    🔍 Verificar Sistema
                </button>
            </div>

            <div class="step" id="stepStatus" style="display: none;">
                <h3><i class="fas fa-clipboard-check"></i> Status do Sistema</h3>
                <ul class="checklist" id="checklist">
                    <!-- Será preenchido dinamicamente -->
                </ul>
            </div>

            <div class="step" id="stepInicializar" style="display: none;">
                <h3><i class="fas fa-rocket"></i> Inicializar Sistema</h3>
                <p>Clique no botão abaixo para inicializar o sistema de empenhos:</p>
                
                <button class="btn btn-success" onclick="inicializarSistema()" id="btnInicializar">
                    <i class="fas fa-play"></i>
                    🚀 Inicializar Empenhos
                </button>
                
                <div class="progress" id="progress" style="display: none;">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                
                <div id="progressText" style="text-align: center; margin-top: 10px; font-weight: 600;"></div>
            </div>

            <div class="step" id="stepConcluido" style="display: none;">
                <h3><i class="fas fa-check-circle"></i> Sistema Inicializado!</h3>
                <div class="status status-success">
                    <i class="fas fa-check"></i>
                    Sistema de empenhos inicializado com sucesso!
                </div>
                
                <p>Agora você pode:</p>
                <ul>
                    <li>✅ Usar o botão "Iniciar Produção" nas OPs</li>
                    <li>✅ Acompanhar empenhos no Painel de Empenhos</li>
                    <li>✅ Ter controle total sobre materiais em produção</li>
                </ul>

                <button class="btn btn-warning" onclick="abrirPainelEmpenhos()">
                    <i class="fas fa-external-link-alt"></i>
                    📊 Abrir Painel de Empenhos
                </button>
            </div>

            <div class="log" id="log"></div>
        </div>
    </div>

    <script type="module">
        // Importar Firebase e serviços
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            updateDoc,
            Timestamp
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        
        // ===================================================================
        // SERVIÇO DE EMPENHOS - INLINE (versão simplificada)
        // ===================================================================
        class EmpenhoService {
            static async inicializarCampoEmpenho() {
                console.log('🔧 Inicializando campo saldoEmpenhado nos estoques...');

                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                let atualizados = 0;

                for (const estoqueDoc of estoquesSnapshot.docs) {
                    const estoque = estoqueDoc.data();

                    if (estoque.saldoEmpenhado === undefined) {
                        await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                            saldoEmpenhado: 0
                        });
                        atualizados++;
                    }
                }

                console.log(`✅ ${atualizados} estoques atualizados com campo saldoEmpenhado`);
                return atualizados;
            }
        }

        // Variáveis globais
        let estoques = [];
        let empenhos = [];

        // Função para log
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const icon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            }[type] || 'ℹ️';
            
            logElement.innerHTML += `[${timestamp}] ${icon} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            logElement.classList.add('show');
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Verificar sistema
        window.verificarSistema = async function() {
            const btn = document.getElementById('btnVerificar');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando...';
            
            log('🔍 Iniciando verificação do sistema...');

            try {
                // Carregar dados
                const [estoquesSnapshot, empenhosSnapshot] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "empenhos"))
                ]);

                estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                empenhos = empenhosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                log(`📊 Encontrados ${estoques.length} registros de estoque`);
                log(`⚡ Encontrados ${empenhos.length} registros de empenho`);

                // Verificar status
                const estoquesComEmpenho = estoques.filter(e => e.saldoEmpenhado !== undefined);
                const estoquesSemEmpenho = estoques.filter(e => e.saldoEmpenhado === undefined);

                log(`✅ ${estoquesComEmpenho.length} estoques já têm campo saldoEmpenhado`);
                log(`⚠️ ${estoquesSemEmpenho.length} estoques precisam ser atualizados`);

                // Mostrar checklist
                mostrarChecklist(estoquesComEmpenho.length, estoquesSemEmpenho.length);

                // Mostrar próximo passo
                if (estoquesSemEmpenho.length > 0) {
                    document.getElementById('stepInicializar').style.display = 'block';
                } else {
                    document.getElementById('stepConcluido').style.display = 'block';
                }

            } catch (error) {
                log(`❌ Erro na verificação: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-search"></i> 🔍 Verificar Sistema';
            }
        };

        // Mostrar checklist
        function mostrarChecklist(comEmpenho, semEmpenho) {
            const checklist = document.getElementById('checklist');
            const stepStatus = document.getElementById('stepStatus');
            
            checklist.innerHTML = `
                <li>
                    <span class="icon ${comEmpenho > 0 ? 'success' : 'pending'}">
                        <i class="fas ${comEmpenho > 0 ? 'fa-check' : 'fa-clock'}"></i>
                    </span>
                    <span>Estoques com campo saldoEmpenhado: <strong>${comEmpenho}</strong></span>
                </li>
                <li>
                    <span class="icon ${semEmpenho === 0 ? 'success' : 'pending'}">
                        <i class="fas ${semEmpenho === 0 ? 'fa-check' : 'fa-exclamation-triangle'}"></i>
                    </span>
                    <span>Estoques que precisam ser atualizados: <strong>${semEmpenho}</strong></span>
                </li>
                <li>
                    <span class="icon ${empenhos.length > 0 ? 'success' : 'pending'}">
                        <i class="fas ${empenhos.length > 0 ? 'fa-check' : 'fa-info'}"></i>
                    </span>
                    <span>Empenhos registrados: <strong>${empenhos.length}</strong></span>
                </li>
                <li>
                    <span class="icon ${semEmpenho === 0 ? 'success' : 'pending'}">
                        <i class="fas ${semEmpenho === 0 ? 'fa-check' : 'fa-cog'}"></i>
                    </span>
                    <span>Sistema pronto: <strong>${semEmpenho === 0 ? 'SIM' : 'NÃO'}</strong></span>
                </li>
            `;
            
            stepStatus.style.display = 'block';
        }

        // Inicializar sistema
        window.inicializarSistema = async function() {
            const btn = document.getElementById('btnInicializar');
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Inicializando...';
            progress.style.display = 'block';

            log('🚀 Iniciando inicialização do sistema de empenhos...');

            try {
                // Usar o serviço de empenhos
                const resultado = await EmpenhoService.inicializarCampoEmpenho();

                // Simular progresso
                for (let i = 0; i <= 100; i += 10) {
                    progressBar.style.width = i + '%';
                    progressText.textContent = `Processando... ${i}%`;
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                log(`✅ Sistema inicializado com sucesso!`);
                log(`📊 ${resultado} registros de estoque atualizados`);

                // Mostrar conclusão
                document.getElementById('stepInicializar').style.display = 'none';
                document.getElementById('stepConcluido').style.display = 'block';

                // Atualizar checklist
                await verificarSistema();

            } catch (error) {
                log(`❌ Erro na inicialização: ${error.message}`, 'error');

                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-play"></i> 🚀 Inicializar Empenhos';
                progress.style.display = 'none';
            }
        };

        // Abrir painel de empenhos
        window.abrirPainelEmpenhos = function() {
            window.open('painel_empenhos.html', '_blank');
        };

        // Carregar automaticamente ao abrir
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 Inicializador de Empenhos carregado');
            log('💡 Clique em "Verificar Sistema" para começar');
        });
    </script>
</body>
</html>
