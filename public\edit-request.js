// Função para editar solicitação
window.editRequest = (requestId) => {
  const solicitacoes = [] // Declare solicitacoes variable
  const solicitacao = solicitacoes.find((s) => s.id === requestId)
  if (solicitacao) {
    openRequestModal(solicitacao)
  } else {
    showNotification("Solicitação não encontrada", "error")
  }
}

function openRequestModal(solicitacao = null) {
  try {
    const elements = {
      modal: document.getElementById("requestModal"),
      form: document.getElementById("requestForm"),
      editWarning: document.getElementById("editWarning"),
      motivoAlteracao: document.getElementById("motivoAlteracao"),
      historySection: document.getElementById("historySection"),
      submitButton: document.getElementById("submitButton"),
      deleteButton: document.getElementById("deleteButton"),
      errorMessage: document.getElementById("errorMessage"),
      displayRequestNumber: document.getElementById("displayRequestNumber"),
      itemsTableBody: document.getElementById("itemsTableBody"),
      solicitante: document.getElementById("solicitante"),
      departamento: document.getElementById("departamento"),
      centroCusto: document.getElementById("centroCusto"),
      fornecedor: document.getElementById("fornecedor"),
      prioridade: document.getElementById("prioridade"),
      justificativa: document.getElementById("justificativa"),
      editingId: document.getElementById("editingId"),
      originalStatus: document.getElementById("originalStatus"),
      modalTitle: document.getElementById("modalTitle"),
    }

    for (const [key, element] of Object.entries(elements)) {
      if (!element) {
        console.error(`Required element '${key}' not found`)
        return
      }
    }

    elements.form.reset()
    elements.itemsTableBody.innerHTML = ""
    elements.errorMessage.style.display = "none"
    elements.displayRequestNumber.style.display = "block"

    // Carregar fornecedores e centros de custo
    const fornecedores = [] // Declare fornecedores variable
    populateFornecedores()
    const centrosCusto = [] // Declare centrosCusto variable
    populateCentrosCusto()

    if (solicitacao) {
      elements.modalTitle.textContent = `Editar Solicitação`
      elements.displayRequestNumber.textContent = solicitacao.numero
      elements.editingId.value = solicitacao.id
      elements.originalStatus.value = solicitacao.status
      elements.solicitante.value = solicitacao.solicitante || ""
      elements.departamento.value = solicitacao.departamento || ""
      elements.centroCusto.value = solicitacao.centroCustoId || ""
      elements.fornecedor.value = solicitacao.fornecedorId || ""
      elements.prioridade.value = solicitacao.prioridade || "NORMAL"
      elements.justificativa.value = solicitacao.justificativa || ""

      // Verificar se o fornecedor existe no select
      if (solicitacao.fornecedorId && elements.fornecedor.value === "") {
        // Fornecedor não está na lista (talvez não esteja mais homologado)
        const fornecedor = fornecedores.find((f) => f.id === solicitacao.fornecedorId)
        if (fornecedor) {
          // Adicionar o fornecedor ao select mesmo que não esteja homologado
          const option = document.createElement("option")
          option.value = fornecedor.id
          option.textContent = `${fornecedor.razaoSocial} (${fornecedor.statusHomologacao || "Não homologado"})`
          elements.fornecedor.appendChild(option)
          elements.fornecedor.value = fornecedor.id
        }
      }

      // Carregar itens da solicitação
      const produtos = [] // Declare produtos variable
      if (solicitacao.itens && Array.isArray(solicitacao.itens)) {
        solicitacao.itens.forEach((item) => {
          const row = addItem()
          row.querySelector(".item-codigo").value = item.codigo
          row.querySelector(".item-descricao").value = item.descricao
          row.querySelector(".item-quantidade").value = item.quantidadeInterna || item.quantidade

          const produto = produtos.find((p) => p.codigo === item.codigo)
          if (produto) {
            row.dataset.produtoId = produto.id
            row.dataset.unidadeInterna = produto.unidade
            row.dataset.unidadeCompra = produto.unidadeSecundaria || produto.unidade
            row.dataset.fatorConversao = produto.fatorConversao || 1
            row.querySelector(".item-unidade").textContent = item.unidadeInterna || produto.unidade
            updateConversion(row) // Declare updateConversion function
          }
        })
      }

      // Configurar avisos e histórico
      elements.editWarning.style.display = "none"
      elements.motivoAlteracao.style.display = "none"
      elements.motivoAlteracao.required = false

      const form = document.getElementById("requestForm")
      const initialFormState = JSON.stringify(getFormData())

      form.addEventListener("change", () => {
        const currentFormState = JSON.stringify(getFormData())
        if (solicitacao.status !== "PENDENTE" && currentFormState !== initialFormState) {
          elements.editWarning.style.display = "block"
          elements.motivoAlteracao.style.display = "block"
          elements.motivoAlteracao.required = true
        } else {
          elements.editWarning.style.display = "none"
          elements.motivoAlteracao.style.display = "none"
          elements.motivoAlteracao.required = false
        }
      })

      function getFormData() {
        return {
          departamento: document.getElementById("departamento").value,
          centroCusto: document.getElementById("centroCusto").value,
          fornecedor: document.getElementById("fornecedor").value,
          prioridade: document.getElementById("prioridade").value,
          justificativa: document.getElementById("justificativa").value,
          itens: Array.from(document.querySelectorAll("#itemsTableBody tr")).map((row) => ({
            codigo: row.querySelector(".item-codigo").value,
            quantidade: row.querySelector(".item-quantidade").value,
          })),
        }
      }

      // Mostrar histórico se existir
      if (solicitacao.historico && solicitacao.historico.length > 0) {
        elements.historySection.style.display = "block"
        const historyContent = document.getElementById("historyContent")
        historyContent.innerHTML = solicitacao.historico
          .map(
            (h) => `
          <div class="history-item">
            <strong>Data:</strong> ${new Date(h.data.seconds * 1000).toLocaleString()}<br>
            <strong>Usuário:</strong> ${h.usuario || "Sistema"}<br>
            <strong>Status Anterior:</strong> ${h.statusAnterior}<br>
            <strong>Novo Status:</strong> ${h.novoStatus}<br>
            <strong>Motivo:</strong> ${h.motivo || "N/A"}
          </div>
        `,
          )
          .join("")
      } else {
        elements.historySection.style.display = "none"
      }

      // Verificar permissões de edição
      const currentUser = {} // Declare currentUser variable
      const userPermissions = [] // Declare userPermissions variable
      const canEdit =
        currentUser.nivel >= 2 ||
        userPermissions.includes("editar_solicitacoes") ||
        solicitacao.solicitante === currentUser.nome

      if (!canEdit) {
        document.querySelectorAll("#requestForm input, #requestForm select, #requestForm textarea").forEach((el) => {
          el.disabled = true
        })
        elements.submitButton.style.display = "none"
        elements.deleteButton.style.display = "none"
      } else {
        elements.submitButton.textContent = "Atualizar Solicitação"
        elements.deleteButton.style.display = "block"
        elements.deleteButton.dataset.requestId = solicitacao.id
      }
    } else {
      // Nova solicitação
      elements.modalTitle.textContent = "Nova Solicitação de Compra"
      elements.displayRequestNumber.textContent = "Será gerado automaticamente"
      elements.editingId.value = ""
      elements.originalStatus.value = ""
      elements.solicitante.value = currentUser.nome
      elements.editWarning.style.display = "none"
      elements.motivoAlteracao.style.display = "none"
      elements.motivoAlteracao.required = false
      elements.historySection.style.display = "none"
      elements.submitButton.textContent = "Criar Solicitação"
      elements.deleteButton.style.display = "none"
      addItem()
    }

    elements.modal.style.display = "block"
  } catch (error) {
    console.error("Erro ao abrir modal:", error)
    showErrorMessage("Erro ao abrir modal. Por favor, tente novamente.") // Declare showErrorMessage function
  }
}

function populateFornecedores() {
  // Implementation for populateFornecedores
}

function populateCentrosCusto() {
  // Implementation for populateCentrosCusto
}

function addItem() {
  // Implementation for addItem
}

function updateConversion(row) {
  // Implementation for updateConversion
}

function showNotification(message, type) {
  // Implementation for showNotification
}

function showErrorMessage(message) {
  // Implementation for showErrorMessage
}
