rules {
  match /databases/{database}/documents {
    // Regra para a coleção de cotações
    match /cotacoes/{cotacaoId} {
      // Permite leitura apenas para usuários autenticados
      allow read: if request.auth != null;
      
      // Permite escrita apenas se:
      // 1. O usuário estiver autenticado
      // 2. Para criação de nova cotação, verifica se não existe outra cotação para a mesma solicitação
      allow create: if request.auth != null && 
                   (!exists(/databases/$(database)/documents/cotacoes/$(cotacaoId)) &&
                   !exists(
                     query(collection(getFirestore(), 'cotacoes')
                       .where('solicitacaoId', '==', request.resource.data.solicitacaoId)
                     ).get()
                   ));
      
      // Permite atualização apenas para o criador da cotação
      allow update: if request.auth != null && 
                   resource.data.criadoPor == request.auth.token.name;
      
      // Permite exclusão apenas para o criador da cotação
      allow delete: if request.auth != null && 
                   resource.data.criadoPor == request.auth.token.name;
    }
    
    // Regra para a coleção de solicitações de compra
    match /solicitacoesCompra/{solicitacaoId} {
      // Permite leitura apenas para usuários autenticados
      allow read: if request.auth != null;
      
      // Permite atualização do status para 'EM COTAÇÃO' apenas se não houver cotação existente
      allow update: if request.auth != null && 
                   (request.resource.data.status == 'EM COTAÇÃO' && 
                    !exists(
                      query(collection(getFirestore(), 'cotacoes')
                        .where('solicitacaoId', '==', solicitacaoId)
                      ).get()
                    ));
      
      // Outras permissões para solicitações de compra
      allow create: if request.auth != null;
      allow delete: if false; // Não permite exclusão de solicitações
    }
  }
}
