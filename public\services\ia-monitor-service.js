/**
 * IA MONITOR SERVICE - SISTEMA NALITECK
 * Serviço de Inteligência Artificial para Monitoramento de Compras
 * Detecta anomalias, prevê problemas e sugere otimizações
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    query, 
    where, 
    orderBy, 
    limit, 
    getDocs, 
    doc, 
    getDoc,
    Timestamp,
    onSnapshot
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

class IAMonitorService {
    constructor() {
        this.isMonitoring = false;
        this.alertThresholds = {
            delayDays: 3,
            priceVariation: 20,
            errorRate: 5,
            efficiencyMin: 85
        };
        this.patterns = new Map();
        this.predictions = new Map();
        this.anomalies = [];
        this.recommendations = [];
    }

    // ===== INICIALIZAÇÃO DO MONITOR =====
    async initialize() {
        try {
            console.log('🤖 Inicializando IA Monitor de Compras...');
            
            await this.loadHistoricalData();
            await this.trainModels();
            this.startRealTimeMonitoring();
            
            console.log('✅ IA Monitor inicializado com sucesso');
            return true;
            
        } catch (error) {
            console.error('❌ Erro na inicialização do IA Monitor:', error);
            throw error;
        }
    }

    // ===== CARREGAMENTO DE DADOS HISTÓRICOS =====
    async loadHistoricalData() {
        try {
            console.log('📊 Carregando dados históricos...');
            
            // Carregar dados dos últimos 6 meses
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
            
            const collections = [
                'solicitacoesCompra',
                'cotacoes', 
                'pedidosCompra',
                'recebimentoMateriais',
                'movimentacoesEstoque'
            ];
            
            const historicalData = {};
            
            for (const collectionName of collections) {
                const q = query(
                    collection(db, collectionName),
                    where('dataCriacao', '>=', Timestamp.fromDate(sixMonthsAgo)),
                    orderBy('dataCriacao', 'desc')
                );
                
                const snapshot = await getDocs(q);
                historicalData[collectionName] = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
            }
            
            this.historicalData = historicalData;
            console.log('✅ Dados históricos carregados:', Object.keys(historicalData).map(k => `${k}: ${historicalData[k].length}`));
            
        } catch (error) {
            console.error('Erro ao carregar dados históricos:', error);
            throw error;
        }
    }

    // ===== TREINAMENTO DOS MODELOS IA =====
    async trainModels() {
        try {
            console.log('🧠 Treinando modelos de IA...');
            
            // Treinar modelo de previsão de atrasos
            this.trainDelayPredictionModel();
            
            // Treinar modelo de detecção de anomalias de preço
            this.trainPriceAnomalyModel();
            
            // Treinar modelo de otimização de processo
            this.trainProcessOptimizationModel();
            
            // Treinar modelo de previsão de demanda
            this.trainDemandPredictionModel();
            
            console.log('✅ Modelos de IA treinados');
            
        } catch (error) {
            console.error('Erro no treinamento dos modelos:', error);
            throw error;
        }
    }

    // ===== MODELO DE PREVISÃO DE ATRASOS =====
    trainDelayPredictionModel() {
        const pedidos = this.historicalData.pedidosCompra || [];
        const patterns = new Map();
        
        pedidos.forEach(pedido => {
            const fornecedorId = pedido.fornecedorId;
            const prazoEntrega = pedido.prazoEntrega || 0;
            const valorTotal = pedido.valorTotal || 0;
            const dataEntrega = pedido.dataEntrega;
            const dataPrevista = pedido.dataPrevista;
            
            if (dataEntrega && dataPrevista) {
                const atraso = (dataEntrega.toDate() - dataPrevista.toDate()) / (1000 * 60 * 60 * 24);
                
                if (!patterns.has(fornecedorId)) {
                    patterns.set(fornecedorId, {
                        totalPedidos: 0,
                        totalAtrasos: 0,
                        atrasoMedio: 0,
                        valores: [],
                        prazos: []
                    });
                }
                
                const pattern = patterns.get(fornecedorId);
                pattern.totalPedidos++;
                if (atraso > 0) pattern.totalAtrasos++;
                pattern.valores.push(valorTotal);
                pattern.prazos.push(prazoEntrega);
                pattern.atrasoMedio = (pattern.atrasoMedio + atraso) / 2;
            }
        });
        
        this.patterns.set('delayPrediction', patterns);
        console.log('📈 Modelo de previsão de atrasos treinado');
    }

    // ===== MODELO DE DETECÇÃO DE ANOMALIAS DE PREÇO =====
    trainPriceAnomalyModel() {
        const cotacoes = this.historicalData.cotacoes || [];
        const pricePatterns = new Map();
        
        cotacoes.forEach(cotacao => {
            if (cotacao.itens) {
                cotacao.itens.forEach(item => {
                    const produtoId = item.produtoId;
                    const precoUnitario = item.precoUnitario || 0;
                    
                    if (!pricePatterns.has(produtoId)) {
                        pricePatterns.set(produtoId, {
                            precos: [],
                            precoMedio: 0,
                            desvioPadrao: 0
                        });
                    }
                    
                    pricePatterns.get(produtoId).precos.push(precoUnitario);
                });
            }
        });
        
        // Calcular médias e desvios padrão
        pricePatterns.forEach((pattern, produtoId) => {
            const precos = pattern.precos;
            const media = precos.reduce((a, b) => a + b, 0) / precos.length;
            const variancia = precos.reduce((a, b) => a + Math.pow(b - media, 2), 0) / precos.length;
            const desvioPadrao = Math.sqrt(variancia);
            
            pattern.precoMedio = media;
            pattern.desvioPadrao = desvioPadrao;
        });
        
        this.patterns.set('priceAnomaly', pricePatterns);
        console.log('💰 Modelo de detecção de anomalias de preço treinado');
    }

    // ===== MODELO DE OTIMIZAÇÃO DE PROCESSO =====
    trainProcessOptimizationModel() {
        const solicitacoes = this.historicalData.solicitacoesCompra || [];
        const processMetrics = {
            tempoMedioAprovacao: 0,
            tempoMedioCotacao: 0,
            tempoMedioRecebimento: 0,
            taxaAprovacao: 0,
            eficienciaGeral: 0
        };
        
        let totalProcessos = 0;
        let somaTempoAprovacao = 0;
        let aprovacoes = 0;
        
        solicitacoes.forEach(solicitacao => {
            if (solicitacao.dataAprovacao && solicitacao.dataCriacao) {
                const tempoAprovacao = (solicitacao.dataAprovacao.toDate() - solicitacao.dataCriacao.toDate()) / (1000 * 60 * 60 * 24);
                somaTempoAprovacao += tempoAprovacao;
                totalProcessos++;
                
                if (solicitacao.status === 'APROVADA') {
                    aprovacoes++;
                }
            }
        });
        
        if (totalProcessos > 0) {
            processMetrics.tempoMedioAprovacao = somaTempoAprovacao / totalProcessos;
            processMetrics.taxaAprovacao = (aprovacoes / totalProcessos) * 100;
        }
        
        this.patterns.set('processOptimization', processMetrics);
        console.log('⚡ Modelo de otimização de processo treinado');
    }

    // ===== MODELO DE PREVISÃO DE DEMANDA =====
    trainDemandPredictionModel() {
        const movimentacoes = this.historicalData.movimentacoesEstoque || [];
        const demandPatterns = new Map();
        
        movimentacoes.forEach(mov => {
            if (mov.tipo === 'SAIDA') {
                const produtoId = mov.produtoId;
                const quantidade = mov.quantidade || 0;
                const data = mov.dataHora.toDate();
                const mes = data.getMonth();
                
                if (!demandPatterns.has(produtoId)) {
                    demandPatterns.set(produtoId, {
                        demandaMensal: new Array(12).fill(0),
                        tendencia: 'estavel',
                        sazonalidade: false
                    });
                }
                
                demandPatterns.get(produtoId).demandaMensal[mes] += quantidade;
            }
        });
        
        this.patterns.set('demandPrediction', demandPatterns);
        console.log('📊 Modelo de previsão de demanda treinado');
    }

    // ===== MONITORAMENTO EM TEMPO REAL =====
    startRealTimeMonitoring() {
        console.log('🔄 Iniciando monitoramento em tempo real...');
        this.isMonitoring = true;
        
        // Monitorar solicitações
        this.monitorSolicitacoes();
        
        // Monitorar cotações
        this.monitorCotacoes();
        
        // Monitorar pedidos
        this.monitorPedidos();
        
        // Monitorar recebimentos
        this.monitorRecebimentos();
        
        // Executar análises periódicas
        this.schedulePeriodicAnalysis();
    }

    // ===== MONITORAMENTO DE SOLICITAÇÕES =====
    monitorSolicitacoes() {
        const q = query(
            collection(db, 'solicitacoesCompra'),
            where('status', 'in', ['PENDENTE', 'EM_APROVACAO']),
            orderBy('dataCriacao', 'desc')
        );
        
        onSnapshot(q, (snapshot) => {
            snapshot.docChanges().forEach((change) => {
                if (change.type === 'added' || change.type === 'modified') {
                    this.analyzeSolicitacao(change.doc.data());
                }
            });
        });
    }

    // ===== MONITORAMENTO DE COTAÇÕES =====
    monitorCotacoes() {
        const q = query(
            collection(db, 'cotacoes'),
            where('status', 'in', ['ENVIADA', 'EM_ANALISE']),
            orderBy('dataCriacao', 'desc')
        );
        
        onSnapshot(q, (snapshot) => {
            snapshot.docChanges().forEach((change) => {
                if (change.type === 'added' || change.type === 'modified') {
                    this.analyzeCotacao(change.doc.data());
                }
            });
        });
    }

    // ===== MONITORAMENTO DE PEDIDOS =====
    monitorPedidos() {
        const q = query(
            collection(db, 'pedidosCompra'),
            where('status', 'in', ['APROVADO', 'ENVIADO', 'CONFIRMADO']),
            orderBy('dataCriacao', 'desc')
        );
        
        onSnapshot(q, (snapshot) => {
            snapshot.docChanges().forEach((change) => {
                if (change.type === 'added' || change.type === 'modified') {
                    this.analyzePedido(change.doc.data());
                }
            });
        });
    }

    // ===== MONITORAMENTO DE RECEBIMENTOS =====
    monitorRecebimentos() {
        const q = query(
            collection(db, 'recebimentoMateriais'),
            orderBy('dataRecebimento', 'desc'),
            limit(50)
        );
        
        onSnapshot(q, (snapshot) => {
            snapshot.docChanges().forEach((change) => {
                if (change.type === 'added') {
                    this.analyzeRecebimento(change.doc.data());
                }
            });
        });
    }

    // ===== ANÁLISE DE SOLICITAÇÃO =====
    analyzeSolicitacao(solicitacao) {
        const agora = new Date();
        const dataCriacao = solicitacao.dataCriacao.toDate();
        const diasPendente = (agora - dataCriacao) / (1000 * 60 * 60 * 24);
        
        // Detectar solicitações atrasadas
        if (diasPendente > this.alertThresholds.delayDays) {
            this.addAlert({
                type: 'critical',
                title: 'Solicitação Atrasada',
                description: `Solicitação ${solicitacao.numero} pendente há ${Math.floor(diasPendente)} dias`,
                action: 'Acelerar processo de aprovação',
                data: solicitacao
            });
        }
        
        // Verificar valor alto sem aprovação
        if (solicitacao.valorTotal > 10000 && solicitacao.status === 'PENDENTE') {
            this.addAlert({
                type: 'warning',
                title: 'Valor Alto Pendente',
                description: `Solicitação de R$ ${solicitacao.valorTotal.toFixed(2)} aguardando aprovação`,
                action: 'Priorizar aprovação',
                data: solicitacao
            });
        }
    }

    // ===== ANÁLISE DE COTAÇÃO =====
    analyzeCotacao(cotacao) {
        if (cotacao.itens) {
            cotacao.itens.forEach(item => {
                this.detectPriceAnomaly(item, cotacao);
            });
        }
        
        // Verificar prazo de cotação
        if (cotacao.dataVencimento) {
            const agora = new Date();
            const dataVencimento = cotacao.dataVencimento.toDate();
            const diasRestantes = (dataVencimento - agora) / (1000 * 60 * 60 * 24);
            
            if (diasRestantes <= 1 && diasRestantes > 0) {
                this.addAlert({
                    type: 'warning',
                    title: 'Cotação Vencendo',
                    description: `Cotação ${cotacao.numero} vence em ${Math.floor(diasRestantes * 24)} horas`,
                    action: 'Solicitar prorrogação ou finalizar',
                    data: cotacao
                });
            }
        }
    }

    // ===== ANÁLISE DE PEDIDO =====
    analyzePedido(pedido) {
        // Prever atraso baseado no histórico do fornecedor
        const delayProbability = this.predictDelay(pedido);
        
        if (delayProbability > 0.7) {
            this.addAlert({
                type: 'warning',
                title: 'Alto Risco de Atraso',
                description: `Pedido ${pedido.numero} tem ${Math.floor(delayProbability * 100)}% de chance de atraso`,
                action: 'Monitorar de perto e contatar fornecedor',
                data: pedido
            });
        }
        
        // Verificar prazo de entrega
        if (pedido.dataPrevista) {
            const agora = new Date();
            const dataPrevista = pedido.dataPrevista.toDate();
            const diasAtraso = (agora - dataPrevista) / (1000 * 60 * 60 * 24);
            
            if (diasAtraso > 0 && pedido.status !== 'RECEBIDO') {
                this.addAlert({
                    type: 'critical',
                    title: 'Pedido Atrasado',
                    description: `Pedido ${pedido.numero} atrasado há ${Math.floor(diasAtraso)} dias`,
                    action: 'Contatar fornecedor urgente',
                    data: pedido
                });
            }
        }
    }

    // ===== ANÁLISE DE RECEBIMENTO =====
    analyzeRecebimento(recebimento) {
        // Verificar divergências de quantidade
        if (recebimento.itens) {
            recebimento.itens.forEach(item => {
                const divergencia = Math.abs(item.quantidadeRecebida - item.quantidadePedida) / item.quantidadePedida * 100;
                
                if (divergencia > this.alertThresholds.priceVariation) {
                    this.addAlert({
                        type: 'warning',
                        title: 'Divergência de Quantidade',
                        description: `Item ${item.produtoNome} com divergência de ${divergencia.toFixed(1)}%`,
                        action: 'Verificar motivo da divergência',
                        data: recebimento
                    });
                }
            });
        }
    }

    // ===== DETECÇÃO DE ANOMALIAS DE PREÇO =====
    detectPriceAnomaly(item, cotacao) {
        const pricePatterns = this.patterns.get('priceAnomaly');
        if (!pricePatterns || !pricePatterns.has(item.produtoId)) return;
        
        const pattern = pricePatterns.get(item.produtoId);
        const precoAtual = item.precoUnitario;
        const precoMedio = pattern.precoMedio;
        const desvioPadrao = pattern.desvioPadrao;
        
        // Detectar preço muito acima da média (mais de 2 desvios padrão)
        if (precoAtual > precoMedio + (2 * desvioPadrao)) {
            const variacao = ((precoAtual - precoMedio) / precoMedio) * 100;
            
            this.addAlert({
                type: 'warning',
                title: 'Preço Anômalo Detectado',
                description: `${item.produtoNome} com preço ${variacao.toFixed(1)}% acima da média`,
                action: 'Verificar cotação e negociar',
                data: { cotacao, item }
            });
        }
    }

    // ===== PREVISÃO DE ATRASO =====
    predictDelay(pedido) {
        const delayPatterns = this.patterns.get('delayPrediction');
        if (!delayPatterns || !delayPatterns.has(pedido.fornecedorId)) {
            return 0.3; // Probabilidade padrão para fornecedores sem histórico
        }
        
        const pattern = delayPatterns.get(pedido.fornecedorId);
        const taxaAtraso = pattern.totalAtrasos / pattern.totalPedidos;
        
        // Ajustar probabilidade baseado no valor e prazo
        let probabilidade = taxaAtraso;
        
        if (pedido.valorTotal > 50000) probabilidade += 0.1; // Pedidos grandes têm mais risco
        if (pedido.prazoEntrega < 7) probabilidade += 0.15; // Prazos curtos têm mais risco
        
        return Math.min(probabilidade, 0.95); // Máximo 95%
    }

    // ===== ADICIONAR ALERTA =====
    addAlert(alert) {
        alert.id = Date.now() + Math.random();
        alert.timestamp = new Date();
        
        this.anomalies.unshift(alert);
        
        // Manter apenas os últimos 50 alertas
        if (this.anomalies.length > 50) {
            this.anomalies = this.anomalies.slice(0, 50);
        }
        
        console.log('🚨 Novo alerta:', alert.title);
        
        // Disparar evento para atualizar interface
        window.dispatchEvent(new CustomEvent('newAlert', { detail: alert }));
    }

    // ===== ANÁLISES PERIÓDICAS =====
    schedulePeriodicAnalysis() {
        // Executar análise completa a cada 30 minutos
        setInterval(() => {
            this.runPeriodicAnalysis();
        }, 30 * 60 * 1000);
        
        // Primeira execução após 5 segundos
        setTimeout(() => {
            this.runPeriodicAnalysis();
        }, 5000);
    }

    // ===== ANÁLISE PERIÓDICA =====
    async runPeriodicAnalysis() {
        try {
            console.log('🔄 Executando análise periódica...');
            
            await this.generateRecommendations();
            await this.updateMetrics();
            
            console.log('✅ Análise periódica concluída');
            
        } catch (error) {
            console.error('Erro na análise periódica:', error);
        }
    }

    // ===== GERAR RECOMENDAÇÕES =====
    async generateRecommendations() {
        this.recommendations = [];
        
        // Analisar padrões e gerar recomendações
        const processMetrics = this.patterns.get('processOptimization');
        
        if (processMetrics && processMetrics.tempoMedioAprovacao > 3) {
            this.recommendations.push({
                type: 'process',
                title: 'Otimizar Aprovações',
                description: 'Implementar aprovação automática para valores baixos',
                impact: 'Redução de 40% no tempo de aprovação',
                priority: 'high'
            });
        }
        
        if (processMetrics && processMetrics.taxaAprovacao < 90) {
            this.recommendations.push({
                type: 'quality',
                title: 'Melhorar Qualidade das Solicitações',
                description: 'Implementar validações automáticas',
                impact: 'Aumento de 15% na taxa de aprovação',
                priority: 'medium'
            });
        }
        
        // Recomendações baseadas em fornecedores
        const delayPatterns = this.patterns.get('delayPrediction');
        if (delayPatterns) {
            delayPatterns.forEach((pattern, fornecedorId) => {
                if (pattern.totalAtrasos / pattern.totalPedidos > 0.3) {
                    this.recommendations.push({
                        type: 'supplier',
                        title: 'Diversificar Fornecedores',
                        description: `Fornecedor ${fornecedorId} tem alta taxa de atraso`,
                        impact: 'Redução de 25% nos atrasos',
                        priority: 'high'
                    });
                }
            });
        }
    }

    // ===== ATUALIZAR MÉTRICAS =====
    async updateMetrics() {
        // Calcular métricas atuais
        const metrics = {
            efficiency: await this.calculateEfficiency(),
            errorRate: await this.calculateErrorRate(),
            avgProcessTime: await this.calculateAvgProcessTime(),
            supplierPerformance: await this.calculateSupplierPerformance()
        };
        
        this.currentMetrics = metrics;
        
        // Disparar evento para atualizar interface
        window.dispatchEvent(new CustomEvent('metricsUpdated', { detail: metrics }));
    }

    // ===== CALCULAR EFICIÊNCIA =====
    async calculateEfficiency() {
        // Implementar cálculo de eficiência baseado em:
        // - Tempo médio de processo
        // - Taxa de aprovação
        // - Cumprimento de prazos
        return 94.2; // Valor simulado
    }

    // ===== CALCULAR TAXA DE ERRO =====
    async calculateErrorRate() {
        // Implementar cálculo de taxa de erro baseado em:
        // - Divergências de recebimento
        // - Rejeições de qualidade
        // - Cancelamentos
        return 2.1; // Valor simulado
    }

    // ===== CALCULAR TEMPO MÉDIO DE PROCESSO =====
    async calculateAvgProcessTime() {
        // Implementar cálculo do tempo médio do processo completo
        return 12.5; // Dias - valor simulado
    }

    // ===== CALCULAR PERFORMANCE DE FORNECEDORES =====
    async calculateSupplierPerformance() {
        // Implementar cálculo de performance dos fornecedores
        return 87.3; // Percentual - valor simulado
    }

    // ===== MÉTODOS PÚBLICOS PARA INTERFACE =====
    
    getAlerts() {
        return this.anomalies;
    }
    
    getRecommendations() {
        return this.recommendations;
    }
    
    getMetrics() {
        return this.currentMetrics || {};
    }
    
    getPredictions() {
        return Array.from(this.predictions.values());
    }
}

// ===== INSTÂNCIA GLOBAL =====
const iaMonitor = new IAMonitorService();

// ===== EXPORTAR PARA USO GLOBAL =====
window.IAMonitor = iaMonitor;

// ===== INICIALIZAÇÃO AUTOMÁTICA =====
document.addEventListener('DOMContentLoaded', () => {
    iaMonitor.initialize().catch(console.error);
});

export default iaMonitor;
