<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Entradas de Material</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f7f7f7; }
    .container { max-width: 1100px; margin: 40px auto; background: #fff; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);}
    h1 { color: #0854a0; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { padding: 10px; border: 1px solid #d4d4d4; }
    th { background: #f0f3f6; }
    .actions button { margin-right: 8px; }
    .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
    .status-pendente { background: #ffc107; color: #333; }
    .status-aprovado { background: #107e3e; color: #fff; }
    .status-rejeitado { background: #bb0000; color: #fff; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Entradas de Material - Inspeção</h1>
    <table>
      <thead>
        <tr>
          <th>Pedido</th>
          <th>Código</th>
          <th>Descrição</th>
          <th>Quantidade</th>
          <th>Unidade</th>
          <th>Data Entrada</th>
          <th>Status</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="itensTable"></tbody>
    </table>
  </div>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, doc, updateDoc, addDoc, getDoc, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function loadItens() {
      const itensSnap = await getDocs(collection(db, "estoqueQualidade"));
      const table = document.getElementById('itensTable');
      table.innerHTML = '';
      for (const docSnap of itensSnap.docs) {
        const item = docSnap.data();
        if (item.status !== 'PENDENTE') continue;
        // Busca o número do pedido
        let pedidoNumero = '';
        if (item.pedidoId) {
          const pedidoSnap = await getDoc(doc(db, "pedidosCompra", item.pedidoId));
          pedidoNumero = pedidoSnap.exists() ? pedidoSnap.data().numero : '';
        }
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${pedidoNumero}</td>
          <td>${item.codigo}</td>
          <td>${item.descricao}</td>
          <td>${item.quantidade}</td>
          <td>${item.unidade}</td>
          <td>${item.dataEntrada?.seconds ? new Date(item.dataEntrada.seconds * 1000).toLocaleDateString() : ''}</td>
          <td><span class="status-badge status-pendente">PENDENTE</span></td>
          <td class="actions">
            <button onclick="aprovarItem('${docSnap.id}')">Aprovar</button>
            <button onclick="rejeitarItem('${docSnap.id}')">Rejeitar</button>
          </td>
        `;
        table.appendChild(row);
      }
    }

    window.aprovarItem = async function(id) {
      // Atualiza status para APROVADO e lança no estoque principal
      const itemRef = doc(db, "estoqueQualidade", id);
      const itemSnap = await getDoc(itemRef);
      if (!itemSnap.exists()) return alert('Item não encontrado!');
      const item = itemSnap.data();

      // Lançar no estoque principal
      await addDoc(collection(db, "estoque"), {
        produtoId: item.produtoId,
        codigo: item.codigo,
        descricao: item.descricao,
        quantidade: item.quantidade,
        unidade: item.unidade,
        dataEntrada: Timestamp.now(),
        origem: `Pedido de Compra ${item.pedidoId || ''}`,
        status: 'DISPONIVEL'
      });

      // Atualizar status do item de qualidade
      await updateDoc(itemRef, {
        status: 'APROVADO',
        dataAprovacao: Timestamp.now()
      });

      alert('Item aprovado e lançado no estoque!');
      loadItens();
    };

    window.rejeitarItem = async function(id) {
      const itemRef = doc(db, "estoqueQualidade", id);
      await updateDoc(itemRef, {
        status: 'REJEITADO',
        dataRejeicao: Timestamp.now()
      });
      alert('Item rejeitado!');
      loadItens();
    };

    loadItens();
  </script>
</body>
</html> 