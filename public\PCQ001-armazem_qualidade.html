<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PCQ001 - Armazém de Qualidade | Sistema de Qualidade</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="firebase-config.js"></script>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 15px;
      padding: 25px;
      text-align: center;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
      font-size: 48px;
      margin-bottom: 15px;
      opacity: 0.8;
    }

    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      color: #6c757d;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      margin-bottom: 25px;
      overflow: hidden;
    }

    .card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px 25px;
      border-bottom: 1px solid #dee2e6;
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-body {
      padding: 25px;
    }

    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-aprovado {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
    }

    .status-rejeitado {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      color: #721c24;
    }

    .status-quarentena {
      background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
      color: #0c5460;
    }

    .status-liberado {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
    }

    .material-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      margin-bottom: 10px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #3498db;
      transition: all 0.3s ease;
    }

    .material-item:hover {
      background: #e9ecef;
      transform: translateX(5px);
    }

    .material-item.approved {
      border-left-color: #27ae60;
    }

    .material-item.rejected {
      border-left-color: #e74c3c;
    }

    .material-item.quarantine {
      border-left-color: #f39c12;
    }

    .material-info {
      flex: 1;
    }

    .material-actions {
      display: flex;
      gap: 10px;
    }

    .action-btn {
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      transform: scale(1.05);
    }

    .filters {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .filter-group label {
      font-size: 12px;
      font-weight: 600;
      color: #6c757d;
      text-transform: uppercase;
    }

    .filter-group select, .filter-group input {
      padding: 8px 12px;
      border: 2px solid #e9ecef;
      border-radius: 6px;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .filters {
        flex-direction: column;
      }
      
      .material-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
      
      .material-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>
        <i class="fas fa-warehouse"></i>
        PCQ001 - Armazém de Qualidade
      </h1>
      <div class="header-actions">
        <button class="btn btn-warning" onclick="generateQualityReport()">
          <i class="fas fa-chart-bar"></i>
          Relatório
        </button>
        <button class="btn btn-primary" onclick="refreshData()">
          <i class="fas fa-sync-alt"></i>
          Atualizar
        </button>
        <button class="btn btn-primary" onclick="navigateBack()">
          <i class="fas fa-arrow-left"></i>
          Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- Estatísticas do Armazém -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon" style="color: #27ae60;">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="stat-number" id="approvedCount">0</div>
          <div class="stat-label">Materiais Aprovados</div>
        </div>

        <div class="stat-card">
          <div class="stat-icon" style="color: #e74c3c;">
            <i class="fas fa-times-circle"></i>
          </div>
          <div class="stat-number" id="rejectedCount">0</div>
          <div class="stat-label">Materiais Rejeitados</div>
        </div>

        <div class="stat-card">
          <div class="stat-icon" style="color: #f39c12;">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-number" id="quarantineCount">0</div>
          <div class="stat-label">Em Quarentena</div>
        </div>

        <div class="stat-card">
          <div class="stat-icon" style="color: #3498db;">
            <i class="fas fa-boxes"></i>
          </div>
          <div class="stat-number" id="totalCount">0</div>
          <div class="stat-label">Total de Materiais</div>
        </div>
      </div>

      <!-- Filtros -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <i class="fas fa-filter"></i>
            Filtros de Busca
          </div>
        </div>
        <div class="card-body">
          <div class="filters">
            <div class="filter-group">
              <label>Status</label>
              <select id="statusFilter" onchange="filterMaterials()">
                <option value="">Todos</option>
                <option value="APROVADO">Aprovado</option>
                <option value="REJEITADO">Rejeitado</option>
                <option value="QUARENTENA">Quarentena</option>
                <option value="LIBERADO">Liberado</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Material</label>
              <input type="text" id="materialFilter" placeholder="Código ou descrição" onkeyup="filterMaterials()">
            </div>

            <div class="filter-group">
              <label>Fornecedor</label>
              <input type="text" id="supplierFilter" placeholder="Nome do fornecedor" onkeyup="filterMaterials()">
            </div>

            <div class="filter-group">
              <label>Data Inicial</label>
              <input type="date" id="dateFromFilter" onchange="filterMaterials()">
            </div>

            <div class="filter-group">
              <label>Data Final</label>
              <input type="date" id="dateToFilter" onchange="filterMaterials()">
            </div>
          </div>
        </div>
      </div>

      <!-- Lista de Materiais -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <i class="fas fa-list"></i>
            Materiais no Armazém de Qualidade
          </div>
        </div>
        <div class="card-body">
          <div id="materialsList">
            <p style="text-align: center; color: #6c757d; padding: 40px;">
              <i class="fas fa-spinner fa-spin"></i>
              Carregando materiais...
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { 
      getFirestore, 
      collection, 
      doc, 
      getDoc, 
      getDocs, 
      updateDoc, 
      query, 
      where, 
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    const db = getFirestore();
    let allMaterials = [];
    let systemParams = {};

    // Carregar parâmetros do sistema
    async function loadSystemParams() {
      try {
        const paramsDoc = await getDoc(doc(db, "parametros", "sistema"));
        if (paramsDoc.exists()) {
          systemParams = paramsDoc.data();
        }
      } catch (error) {
        console.error("Erro ao carregar parâmetros:", error);
      }
    }

    // Carregar materiais do armazém de qualidade
    async function loadQualityMaterials() {
      try {
        // Simular dados de materiais no armazém de qualidade
        const materials = [
          {
            id: '1',
            materialCode: 'MP001',
            description: 'Aço Carbono 1020',
            supplier: 'Fornecedor ABC Ltda',
            batch: 'LT2024001',
            quantity: '1000 kg',
            status: 'APROVADO',
            inspectionDate: '2024-06-22',
            inspector: 'João Silva',
            location: 'A-01-001'
          },
          {
            id: '2',
            materialCode: 'MP002',
            description: 'Parafuso M8x20',
            supplier: 'Parafusos XYZ',
            batch: 'LT2024002',
            quantity: '5000 pcs',
            status: 'REJEITADO',
            inspectionDate: '2024-06-22',
            inspector: 'Maria Santos',
            location: 'R-01-001',
            rejectionReason: 'Dimensões incorretas'
          },
          {
            id: '3',
            materialCode: 'MP003',
            description: 'Chapa de Alumínio',
            supplier: 'Alumínio Brasil',
            batch: 'LT2024003',
            quantity: '500 kg',
            status: 'QUARENTENA',
            inspectionDate: '2024-06-21',
            inspector: 'Carlos Lima',
            location: 'Q-01-001'
          }
        ];

        allMaterials = materials;
        updateStatistics();
        displayMaterials(materials);
      } catch (error) {
        console.error("Erro ao carregar materiais:", error);
      }
    }

    // Atualizar estatísticas
    function updateStatistics() {
      const approved = allMaterials.filter(m => m.status === 'APROVADO').length;
      const rejected = allMaterials.filter(m => m.status === 'REJEITADO').length;
      const quarantine = allMaterials.filter(m => m.status === 'QUARENTENA').length;
      const total = allMaterials.length;

      document.getElementById('approvedCount').textContent = approved;
      document.getElementById('rejectedCount').textContent = rejected;
      document.getElementById('quarantineCount').textContent = quarantine;
      document.getElementById('totalCount').textContent = total;
    }

    // Exibir materiais
    function displayMaterials(materials) {
      const container = document.getElementById('materialsList');
      
      if (materials.length === 0) {
        container.innerHTML = `
          <p style="text-align: center; color: #6c757d; padding: 40px;">
            <i class="fas fa-box-open" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i><br>
            Nenhum material encontrado
          </p>
        `;
        return;
      }

      container.innerHTML = materials.map(material => {
        const statusClass = material.status.toLowerCase().replace('_', '');
        const statusColor = {
          'aprovado': '#27ae60',
          'rejeitado': '#e74c3c',
          'quarentena': '#f39c12',
          'liberado': '#3498db'
        }[statusClass] || '#6c757d';

        return `
          <div class="material-item ${statusClass}">
            <div class="material-info">
              <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 5px;">
                <strong>${material.materialCode}</strong>
                <span class="status-badge status-${statusClass}">${material.status}</span>
              </div>
              <div style="color: #6c757d; font-size: 14px;">
                ${material.description} | ${material.supplier}<br>
                Lote: ${material.batch} | Qtd: ${material.quantity} | Local: ${material.location}
              </div>
              <div style="color: #6c757d; font-size: 12px; margin-top: 5px;">
                Inspeção: ${new Date(material.inspectionDate).toLocaleDateString('pt-BR')} por ${material.inspector}
                ${material.rejectionReason ? `<br>Motivo: ${material.rejectionReason}` : ''}
              </div>
            </div>
            <div class="material-actions">
              ${material.status === 'APROVADO' ? `
                <button class="action-btn btn-success" onclick="releaseMaterial('${material.id}')">
                  <i class="fas fa-check"></i> Liberar
                </button>
              ` : ''}
              ${material.status === 'REJEITADO' ? `
                <button class="action-btn btn-danger" onclick="returnMaterial('${material.id}')">
                  <i class="fas fa-undo"></i> Devolver
                </button>
              ` : ''}
              ${material.status === 'QUARENTENA' ? `
                <button class="action-btn btn-warning" onclick="reInspect('${material.id}')">
                  <i class="fas fa-search"></i> Re-inspecionar
                </button>
              ` : ''}
              <button class="action-btn btn-primary" onclick="viewMaterialDetails('${material.id}')">
                <i class="fas fa-eye"></i> Detalhes
              </button>
            </div>
          </div>
        `;
      }).join('');
    }

    // Filtrar materiais
    window.filterMaterials = function() {
      const statusFilter = document.getElementById('statusFilter').value;
      const materialFilter = document.getElementById('materialFilter').value.toLowerCase();
      const supplierFilter = document.getElementById('supplierFilter').value.toLowerCase();
      const dateFrom = document.getElementById('dateFromFilter').value;
      const dateTo = document.getElementById('dateToFilter').value;

      let filtered = allMaterials.filter(material => {
        const matchStatus = !statusFilter || material.status === statusFilter;
        const matchMaterial = !materialFilter || 
          material.materialCode.toLowerCase().includes(materialFilter) ||
          material.description.toLowerCase().includes(materialFilter);
        const matchSupplier = !supplierFilter || 
          material.supplier.toLowerCase().includes(supplierFilter);
        
        let matchDate = true;
        if (dateFrom || dateTo) {
          const materialDate = new Date(material.inspectionDate);
          if (dateFrom) matchDate = matchDate && materialDate >= new Date(dateFrom);
          if (dateTo) matchDate = matchDate && materialDate <= new Date(dateTo);
        }

        return matchStatus && matchMaterial && matchSupplier && matchDate;
      });

      displayMaterials(filtered);
    };

    // Ações dos materiais
    window.releaseMaterial = async function(materialId) {
      if (confirm('Confirma a liberação deste material para o estoque principal?')) {
        try {
          // Atualizar status no Firebase
          // await updateDoc(doc(db, "materiais_qualidade", materialId), {
          //   status: 'LIBERADO',
          //   releaseDate: new Date(),
          //   releasedBy: 'Usuario Atual'
          // });
          
          alert('Material liberado para o estoque principal!');
          await loadQualityMaterials();
        } catch (error) {
          console.error("Erro ao liberar material:", error);
          alert("Erro ao liberar material: " + error.message);
        }
      }
    };

    window.returnMaterial = async function(materialId) {
      if (confirm('Confirma a devolução deste material ao fornecedor?')) {
        try {
          alert('Material marcado para devolução!');
          await loadQualityMaterials();
        } catch (error) {
          console.error("Erro ao devolver material:", error);
          alert("Erro ao devolver material: " + error.message);
        }
      }
    };

    window.reInspect = function(materialId) {
      if (confirm('Deseja enviar este material para re-inspeção?')) {
        // Redirecionar para o módulo de inspeção
        window.location.href = `PQ001-inspecao_recebimento.html?material=${materialId}`;
      }
    };

    window.viewMaterialDetails = function(materialId) {
      const material = allMaterials.find(m => m.id === materialId);
      if (material) {
        alert(`Detalhes do Material:\n\nCódigo: ${material.materialCode}\nDescrição: ${material.description}\nFornecedor: ${material.supplier}\nLote: ${material.batch}\nQuantidade: ${material.quantity}\nStatus: ${material.status}\nLocalização: ${material.location}`);
      }
    };

    // Gerar relatório
    window.generateQualityReport = function() {
      const reportData = {
        date: new Date().toLocaleDateString('pt-BR'),
        materials: allMaterials,
        statistics: {
          approved: allMaterials.filter(m => m.status === 'APROVADO').length,
          rejected: allMaterials.filter(m => m.status === 'REJEITADO').length,
          quarantine: allMaterials.filter(m => m.status === 'QUARENTENA').length,
          total: allMaterials.length
        }
      };

      // Gerar relatório em nova janela
      const reportWindow = window.open('', '_blank');
      reportWindow.document.write(`
        <html>
          <head>
            <title>Relatório do Armazém de Qualidade</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
              .stats { display: flex; justify-content: space-around; margin: 20px 0; }
              .stat { text-align: center; padding: 10px; border: 1px solid #ddd; }
              table { border-collapse: collapse; width: 100%; margin-top: 20px; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>RELATÓRIO DO ARMAZÉM DE QUALIDADE</h1>
              <h2>PCQ001 - ${reportData.date}</h2>
            </div>
            
            <div class="stats">
              <div class="stat">
                <h3>${reportData.statistics.approved}</h3>
                <p>Aprovados</p>
              </div>
              <div class="stat">
                <h3>${reportData.statistics.rejected}</h3>
                <p>Rejeitados</p>
              </div>
              <div class="stat">
                <h3>${reportData.statistics.quarantine}</h3>
                <p>Quarentena</p>
              </div>
              <div class="stat">
                <h3>${reportData.statistics.total}</h3>
                <p>Total</p>
              </div>
            </div>

            <table>
              <thead>
                <tr>
                  <th>Código</th>
                  <th>Descrição</th>
                  <th>Fornecedor</th>
                  <th>Lote</th>
                  <th>Quantidade</th>
                  <th>Status</th>
                  <th>Localização</th>
                </tr>
              </thead>
              <tbody>
                ${reportData.materials.map(m => `
                  <tr>
                    <td>${m.materialCode}</td>
                    <td>${m.description}</td>
                    <td>${m.supplier}</td>
                    <td>${m.batch}</td>
                    <td>${m.quantity}</td>
                    <td>${m.status}</td>
                    <td>${m.location}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </body>
        </html>
      `);
      reportWindow.document.close();
      reportWindow.print();
    };

    // Atualizar dados
    window.refreshData = async function() {
      await loadQualityMaterials();
    };

    // Navegação
    window.navigateBack = function() {
      window.history.back();
    };

    // Inicialização
    document.addEventListener('DOMContentLoaded', async function() {
      console.log("📦 Inicializando armazém de qualidade...");
      await loadSystemParams();
      await loadQualityMaterials();
      console.log("✅ Armazém de qualidade inicializado!");
    });
  </script>
</body>
</html>
