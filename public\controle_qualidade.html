<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Controle de Qualidade</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #f59e0b;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      margin: 0;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }

    .header {
      background-color: var(--primary-color);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    .filters {
      background-color: var(--secondary-color);
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .filter-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-group {
      flex: 1;
    }

    .filter-group label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .filter-group input,
    .filter-group select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .quality-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .quality-table th,
    .quality-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .quality-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
    }

    .quality-table tr:hover {
      background-color: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .status-pendente {
      background-color: var(--warning-color);
      color: white;
    }

    .status-aprovado {
      background-color: var(--success-color);
      color: white;
    }

    .status-rejeitado {
      background-color: var(--danger-color);
      color: white;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 20px;
      width: 80%;
      max-width: 800px;
      border-radius: 8px;
      position: relative;
    }

    .close-button {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .close-button:hover {
      color: var(--danger-color);
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .quality-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .metric-card {
      background-color: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .metric-title {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 5px;
    }

    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .chart-container {
      margin-top: 20px;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #qualityChart {
      width: 100%;
      height: 300px;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Controle de Qualidade</h1>
      <div>
        <button class="btn btn-primary" onclick="exportToExcel()">Exportar Relatório</button>
        <button class="btn btn-primary" onclick="window.location.href='index.html'">Voltar</button>
      </div>
    </div>

    <!-- Métricas de Qualidade -->
    <div class="quality-metrics">
      <div class="metric-card">
        <div class="metric-title">Taxa de Aprovação</div>
        <div class="metric-value" id="approvalRate">-</div>
      </div>
      <div class="metric-card">
        <div class="metric-title">Tempo Médio de Inspeção</div>
        <div class="metric-value" id="avgInspectionTime">-</div>
      </div>
      <div class="metric-card">
        <div class="metric-title">Itens Pendentes</div>
        <div class="metric-value" id="pendingItems">-</div>
      </div>
      <div class="metric-card">
        <div class="metric-title">Rejeições no Mês</div>
        <div class="metric-value" id="monthlyRejections">-</div>
      </div>
    </div>

    <!-- Gráfico de Qualidade -->
    <div class="chart-container">
      <h2>Histórico de Qualidade</h2>
      <canvas id="qualityChart"></canvas>
    </div>

    <!-- Filtros -->
    <div class="filters">
      <div class="filter-row">
        <div class="filter-group">
          <label>Período</label>
          <input type="date" id="startDate">
        </div>
        <div class="filter-group">
          <label>Até</label>
          <input type="date" id="endDate">
        </div>
        <div class="filter-group">
          <label>Status</label>
          <select id="statusFilter">
            <option value="">Todos</option>
            <option value="PENDENTE">Pendente</option>
            <option value="APROVADO">Aprovado</option>
            <option value="REJEITADO">Rejeitado</option>
          </select>
        </div>
        <div class="filter-group">
          <label>Setor</label>
          <select id="setorFilter">
            <option value="">Todos</option>
          </select>
        </div>
        <div class="filter-group">
          <label>Fornecedor</label>
          <select id="supplierFilter">
            <option value="">Todos</option>
          </select>
        </div>
      </div>
      <div style="text-align: right;">
        <button class="btn btn-primary" onclick="applyFilters()">Aplicar Filtros</button>
        <button class="btn btn-primary" onclick="resetFilters()">Limpar Filtros</button>
      </div>
    </div>

    <!-- Tabela de Itens -->
    <table class="quality-table">
      <thead>
        <tr>
          <th>Data</th>
          <th>Origem</th>
          <th>Código</th>
          <th>Descrição</th>
          <th>Quantidade</th>
          <th>Unidade</th>
          <th>Lote</th>
          <th>Status</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="qualityTableBody"></tbody>
    </table>
  </div>

  <!-- Modal de Inspeção -->
  <div id="inspectionModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal()">&times;</span>
      <h2>Inspeção de Qualidade</h2>
      <form id="inspectionForm" onsubmit="submitInspection(event)">
        <div class="form-group">
          <label>Item</label>
          <input type="text" id="itemDescription" readonly>
        </div>
        <div class="form-group">
          <label>Quantidade</label>
          <input type="number" id="itemQuantity" readonly>
        </div>
        <div class="form-group">
          <label>Critérios de Inspeção</label>
          <div id="criteriaContainer"></div>
        </div>
        <div class="form-group">
          <label>Observações</label>
          <textarea id="observations" rows="3" required></textarea>
        </div>
        <div class="form-group">
          <label>Fotos da Inspeção</label>
          <input type="file" id="inspectionPhotos" multiple accept="image/*">
        </div>
        <input type="hidden" id="itemId">
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success" data-action="approve">Aprovar</button>
          <button type="submit" class="btn btn-danger" data-action="reject">Rejeitar</button>
          <button type="button" class="btn btn-primary" onclick="closeModal()">Cancelar</button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      query, 
      where, 
      getDocs, 
      doc, 
      updateDoc, 
      addDoc, 
      Timestamp,
      orderBy,
      limit,
      getDoc
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let qualityItems = [];
    let currentUser = JSON.parse(localStorage.getItem('currentUser'));
    let qualityChart = null;

    window.onload = async function() {
      if (!currentUser) {
        window.location.href = 'login.html';
        return;
      }

      const configDoc = await getDoc(doc(db, "parametros", "sistema"));
      const config = configDoc.data();

      if (!config?.controleQualidade) {
        alert("Módulo de controle de qualidade desativado nas configurações do sistema.");
        window.location.href = 'index.html';
        return;
      }

      await loadData();
      setupChart();
      updateMetrics();
    };

    async function loadData() {
      try {
        const querySnapshot = await getDocs(collection(db, "estoqueQualidade"));
        qualityItems = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        displayItems();
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados: " + error.message);
      }
    }

    function displayItems() {
      const tableBody = document.getElementById('qualityTableBody');
      tableBody.innerHTML = '';

      qualityItems.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.dataEntrada.toDate().toLocaleString()}</td>
          <td>${item.origem}</td>
          <td>${item.codigo}</td>
          <td>${item.descricao}</td>
          <td>${item.quantidade}</td>
          <td>${item.unidade}</td>
          <td>${item.loteInterno || '-'}</td>
          <td><span class="status-badge status-${item.status.toLowerCase()}">${item.status}</span></td>
          <td>
            ${item.status === 'PENDENTE' ? 
              `<button class="btn btn-primary" onclick="openInspection('${item.id}')">Inspecionar</button>` : 
              `<button class="btn btn-primary" onclick="viewDetails('${item.id}')">Detalhes</button>`}
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function updateMetrics() {
      const total = qualityItems.length;
      const approved = qualityItems.filter(item => item.status === 'APROVADO').length;
      const pending = qualityItems.filter(item => item.status === 'PENDENTE').length;
      const rejected = qualityItems.filter(item => item.status === 'REJEITADO').length;

      document.getElementById('approvalRate').textContent = 
        total > 0 ? `${((approved / total) * 100).toFixed(1)}%` : '0%';
      
      document.getElementById('pendingItems').textContent = pending;
      document.getElementById('monthlyRejections').textContent = rejected;

      // Calcular tempo médio de inspeção
      const inspectedItems = qualityItems.filter(item => item.dataInspecao);
      const avgTime = inspectedItems.reduce((acc, item) => {
        const inspectionTime = item.dataInspecao.toDate() - item.dataEntrada.toDate();
        return acc + inspectionTime;
      }, 0) / (inspectedItems.length || 1);

      document.getElementById('avgInspectionTime').textContent = 
        `${Math.round(avgTime / (1000 * 60 * 60))}h`;
    }

    function setupChart() {
      const ctx = document.getElementById('qualityChart').getContext('2d');
      const monthlyData = getMonthlyData();

      qualityChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: monthlyData.labels,
          datasets: [
            {
              label: 'Aprovados',
              data: monthlyData.approved,
              borderColor: '#107e3e',
              backgroundColor: '#107e3e20',
              fill: true
            },
            {
              label: 'Rejeitados',
              data: monthlyData.rejected,
              borderColor: '#bb0000',
              backgroundColor: '#bb000020',
              fill: true
            }
          ]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'top',
            },
            title: {
              display: true,
              text: 'Histórico de Inspeções'
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    function getMonthlyData() {
      const months = [];
      const approved = [];
      const rejected = [];
      
      // Últimos 6 meses
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthYear = date.toLocaleString('pt-BR', { month: 'short', year: '2-digit' });
        months.push(monthYear);

        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

        const monthItems = qualityItems.filter(item => {
          const itemDate = item.dataInspecao?.toDate() || new Date();
          return itemDate >= monthStart && itemDate <= monthEnd;
        });

        approved.push(monthItems.filter(item => item.status === 'APROVADO').length);
        rejected.push(monthItems.filter(item => item.status === 'REJEITADO').length);
      }

      return { labels: months, approved, rejected };
    }

    window.openInspection = function(itemId) {
      const item = qualityItems.find(i => i.id === itemId);
      if (!item) return;

      document.getElementById('itemId').value = itemId;
      document.getElementById('itemDescription').value = `${item.codigo} - ${item.descricao}`;
      document.getElementById('itemQuantity').value = `${item.quantidade} ${item.unidade}`;

      // Carregar critérios de inspeção
      loadInspectionCriteria(item.produtoId);

      document.getElementById('inspectionModal').style.display = 'block';
    };

    async function loadInspectionCriteria(produtoId) {
      try {
        const criteriaSnapshot = await getDocs(
          query(collection(db, "criteriosInspecao"), where("produtoId", "==", produtoId))
        );
        
        const container = document.getElementById('criteriaContainer');
        container.innerHTML = '';

        criteriaSnapshot.docs.forEach((doc, index) => {
          const criteria = doc.data();
          container.innerHTML += `
            <div class="form-group">
              <label>${criteria.descricao}</label>
              <input type="text" 
                     class="criteria-input" 
                     data-criteria-id="${doc.id}"
                     data-min="${criteria.minimo}"
                     data-max="${criteria.maximo}"
                     required>
              <small>Valor esperado: ${criteria.minimo} - ${criteria.maximo} ${criteria.unidade}</small>
            </div>
          `;
        });
      } catch (error) {
        console.error("Erro ao carregar critérios:", error);
        alert("Erro ao carregar critérios de inspeção.");
      }
    }

    window.submitInspection = async function(event) {
      event.preventDefault();
      const action = event.submitter.dataset.action;
      const itemId = document.getElementById('itemId').value;
      const observations = document.getElementById('observations').value;
      const item = qualityItems.find(i => i.id === itemId);

      try {
        const criteriaResults = Array.from(document.querySelectorAll('.criteria-input')).map(input => ({
          criteriaId: input.dataset.criteriaId,
          value: parseFloat(input.value),
          min: parseFloat(input.dataset.min),
          max: parseFloat(input.dataset.max),
          conformity: parseFloat(input.value) >= parseFloat(input.dataset.min) && 
                     parseFloat(input.value) <= parseFloat(input.dataset.max)
        }));

        const allConform = criteriaResults.every(result => result.conformity);
        if (action === 'approve' && !allConform) {
          if (!confirm('Existem critérios fora da especificação. Deseja aprovar mesmo assim?')) {
            return;
          }
        }

        const status = action === 'approve' ? 'APROVADO' : 'REJEITADO';
        
        await updateDoc(doc(db, "estoqueQualidade", itemId), {
          status,
          dataInspecao: Timestamp.now(),
          inspecionadoPor: currentUser.nome,
          resultadosInspecao: criteriaResults,
          observacoes
        });

        if (status === 'APROVADO') {
          // Mover para estoque principal
          await addDoc(collection(db, "estoque"), {
            produtoId: item.produtoId,
            quantidade: item.quantidade,
            unidade: item.unidade,
            loteInterno: item.loteInterno,
            dataEntrada: Timestamp.now(),
            origem: item.origem
          });
        }

        await loadData();
        updateMetrics();
        closeModal();
        alert(`Item ${status.toLowerCase()} com sucesso!`);
      } catch (error) {
        console.error("Erro ao processar inspeção:", error);
        alert("Erro ao processar inspeção: " + error.message);
      }
    };

    window.closeModal = function() {
      document.getElementById('inspectionModal').style.display = 'none';
      document.getElementById('inspectionForm').reset();
    };

    window.applyFilters = function() {
      const startDate = document.getElementById('startDate').value;
      const endDate = document.getElementById('endDate').value;
      const status = document.getElementById('statusFilter').value;
      const supplier = document.getElementById('supplierFilter').value;

      let filtered = [...qualityItems];

      if (startDate) {
        filtered = filtered.filter(item => 
          item.dataEntrada.toDate() >= new Date(startDate)
        );
      }

      if (endDate) {
        filtered = filtered.filter(item => 
          item.dataEntrada.toDate() <= new Date(endDate + 'T23:59:59')
        );
      }

      if (status) {
        filtered = filtered.filter(item => item.status === status);
      }

      if (supplier) {
        filtered = filtered.filter(item => item.fornecedorId === supplier);
      }

      qualityItems = filtered;
      displayItems();
      updateMetrics();
    };

    window.resetFilters = function() {
      document.getElementById('startDate').value = '';
      document.getElementById('endDate').value = '';
      document.getElementById('statusFilter').value = '';
      document.getElementById('supplierFilter').value = '';
      loadData();
    };

    window.exportToExcel = async function() {
      try {
        const data = qualityItems.map(item => ({
          'Data': item.dataEntrada.toDate().toLocaleString(),
          'Código': item.codigo,
          'Descrição': item.descricao,
          'Quantidade': item.quantidade,
          'Unidade': item.unidade,
          'Lote': item.loteInterno || '-',
          'Status': item.status,
          'Observações': item.observacoes || '-'
        }));

        const ws = XLSX.utils.json_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Controle de Qualidade");

        XLSX.writeFile(wb, `Controle_Qualidade_${new Date().toISOString().split('T')[0]}.xlsx`);
      } catch (error) {
        console.error("Erro ao exportar:", error);
        alert("Erro ao exportar dados: " + error.message);
      }
    };
  </script>
</body>
</html>