// Controlador principal que orquestra todo o fluxo de produção
import { ProductionService } from '../services/production-service.js';
import { TransferService } from '../services/transfer-service.js';
import { MovementService } from '../services/movement-service.js';
import { RequirementsService } from '../services/requirements-service.js';

export class ProductionController {
  
  /**
   * Fluxo completo: Criar OP → Calcular Necessidades → Gerar Solicitações
   */
  static async createCompleteProductionFlow(orderData) {
    try {
      console.log('Iniciando fluxo completo de produção...');
      
      // 1. Criar ordem de produção com explosão BOM
      const productionResult = await ProductionService.createProductionOrder(orderData);
      console.log('✅ Ordem criada:', productionResult.orderNumber);
      
      // 2. Buscar dados atualizados
      const [ordensProducao, estoques, produtos] = await Promise.all([
        this.getActiveOrders(),
        this.getCurrentStocks(),
        this.getAllProducts()
      ]);
      
      // 3. Calcular necessidades de compra
      const necessidades = await RequirementsService.calculateRequirements(
        ordensProducao, 
        estoques, 
        produtos
      );
      console.log('✅ Necessidades calculadas:', necessidades.length);
      
      // 4. Gerar solicitações de compra se houver necessidades
      let solicitacoes = [];
      if (necessidades.length > 0) {
        solicitacoes = await RequirementsService.generatePurchaseRequests(
          necessidades, 
          'familia'
        );
        console.log('✅ Solicitações geradas:', solicitacoes.length);
      }
      
      // 5. Verificar transferências necessárias
      const transferenciasNecessarias = await this.checkRequiredTransfers(productionResult.orderId);
      console.log('✅ Transferências identificadas:', transferenciasNecessarias.length);
      
      return {
        success: true,
        ordemProducao: productionResult,
        necessidades: necessidades.length,
        solicitacoes: solicitacoes.length,
        transferencias: transferenciasNecessarias.length,
        resumo: {
          numeroOP: productionResult.orderNumber,
          materiaisNecessarios: productionResult.materialsCount,
          ordensFilhas: productionResult.childOrdersCount,
          necessidadesCompra: necessidades.length,
          solicitacoesGeradas: solicitacoes.length
        }
      };
      
    } catch (error) {
      console.error('Erro no fluxo completo:', error);
      throw new Error(`Falha no fluxo de produção: ${error.message}`);
    }
  }
  
  /**
   * Fluxo de suprimento: Transferir materiais para produção
   */
  static async supplyProductionOrder(orderId, materiais) {
    try {
      console.log('Iniciando suprimento da OP:', orderId);
      
      const transferencias = [];
      const erros = [];
      
      for (const material of materiais) {
        try {
          const resultado = await TransferService.autoTransferForOrder(
            orderId,
            material.produtoId,
            material.quantidade
          );
          
          transferencias.push({
            produtoId: material.produtoId,
            quantidade: material.quantidade,
            numero: resultado.numero,
            status: 'SUCESSO'
          });
          
        } catch (error) {
          erros.push({
            produtoId: material.produtoId,
            quantidade: material.quantidade,
            erro: error.message
          });
        }
      }
      
      console.log(`✅ Suprimento concluído: ${transferencias.length} sucessos, ${erros.length} erros`);
      
      return {
        success: erros.length === 0,
        transferencias,
        erros,
        resumo: {
          totalMateriais: materiais.length,
          sucessos: transferencias.length,
          falhas: erros.length
        }
      };
      
    } catch (error) {
      console.error('Erro no suprimento:', error);
      throw new Error(`Falha no suprimento: ${error.message}`);
    }
  }
  
  /**
   * Fluxo de apontamento: Registrar produção e consumir materiais
   */
  static async recordProductionAndConsumption(appointmentData) {
    try {
      console.log('Registrando apontamento:', appointmentData.orderId);
      
      // 1. Registrar apontamento (já consome materiais automaticamente)
      const resultado = await ProductionService.recordProduction(appointmentData);
      
      // 2. Verificar se OP foi finalizada
      if (resultado.status === 'FINALIZADA') {
        // Liberar empenhos restantes
        await this.releaseRemainingReservations(appointmentData.orderId);
        console.log('✅ OP finalizada - empenhos liberados');
      }
      
      // 3. Recalcular necessidades se necessário
      if (resultado.consumos > 0) {
        await this.recalculateRequirements();
        console.log('✅ Necessidades recalculadas');
      }
      
      return {
        success: true,
        apontamento: resultado,
        resumo: {
          quantidadeProduzida: resultado.quantidadeProduzida,
          status: resultado.status,
          materiaisConsumidos: resultado.consumos
        }
      };
      
    } catch (error) {
      console.error('Erro no apontamento:', error);
      throw new Error(`Falha no apontamento: ${error.message}`);
    }
  }
  
  /**
   * Fluxo de recebimento: Entrada de material comprado
   */
  static async receiveOrderedMaterial(recebimentoData) {
    try {
      console.log('Processando recebimento:', recebimentoData.pedidoId);
      
      const movimentacoes = [];
      
      for (const item of recebimentoData.itens) {
        // Determinar armazém de destino
        const armazemDestino = item.requerInspecao ? 
          recebimentoData.armazemQualidade : 
          recebimentoData.armazemAlmoxarifado;
        
        // Registrar entrada
        const movimentacao = await MovementService.executeMovement({
          produtoId: item.produtoId,
          armazemId: armazemDestino,
          tipo: 'ENTRADA',
          quantidade: item.quantidade,
          valorUnitario: item.valorUnitario,
          tipoDocumento: 'COMPRA',
          numeroDocumento: recebimentoData.numeroNF,
          observacoes: `Recebimento NF ${recebimentoData.numeroNF}`,
          pedidoCompraId: recebimentoData.pedidoId
        });
        
        movimentacoes.push(movimentacao);
      }
      
      // Recalcular necessidades após recebimento
      await this.recalculateRequirements();
      
      console.log('✅ Recebimento processado:', movimentacoes.length, 'itens');
      
      return {
        success: true,
        movimentacoes,
        resumo: {
          itensRecebidos: movimentacoes.length,
          valorTotal: recebimentoData.itens.reduce((total, item) => 
            total + (item.quantidade * item.valorUnitario), 0
          )
        }
      };
      
    } catch (error) {
      console.error('Erro no recebimento:', error);
      throw new Error(`Falha no recebimento: ${error.message}`);
    }
  }
  
  /**
   * Verifica transferências necessárias para uma OP
   */
  static async checkRequiredTransfers(orderId) {
    try {
      // Buscar ordem e seus materiais
      const orderDoc = await getDoc(doc(db, "ordensProducao", orderId));
      if (!orderDoc.exists()) return [];
      
      const order = orderDoc.data();
      const transferenciasNecessarias = [];
      
      if (order.materiaisNecessarios) {
        for (const material of order.materiaisNecessarios) {
          if (material.necessidade > 0) {
            // Verificar se há saldo no almoxarifado
            const saldoAlmoxarifado = await this.getStockInMainWarehouse(material.produtoId);
            
            if (saldoAlmoxarifado >= material.necessidade) {
              transferenciasNecessarias.push({
                produtoId: material.produtoId,
                quantidade: material.necessidade,
                motivo: 'SUPRIMENTO_OP'
              });
            }
          }
        }
      }
      
      return transferenciasNecessarias;
      
    } catch (error) {
      console.error('Erro ao verificar transferências:', error);
      return [];
    }
  }
  
  /**
   * Libera empenhos restantes de uma OP finalizada
   */
  static async releaseRemainingReservations(orderId) {
    try {
      // Implementar lógica para liberar empenhos
      // Buscar estoques com reservas para esta OP
      // Zerar saldoReservado correspondente
      console.log('Liberando empenhos da OP:', orderId);
    } catch (error) {
      console.error('Erro ao liberar empenhos:', error);
    }
  }
  
  /**
   * Recalcula necessidades após mudanças no estoque
   */
  static async recalculateRequirements() {
    try {
      const [ordensProducao, estoques, produtos] = await Promise.all([
        this.getActiveOrders(),
        this.getCurrentStocks(),
        this.getAllProducts()
      ]);
      
      await RequirementsService.calculateRequirements(ordensProducao, estoques, produtos);
      console.log('Necessidades recalculadas');
      
    } catch (error) {
      console.error('Erro ao recalcular necessidades:', error);
    }
  }
  
  /**
   * Métodos auxiliares para buscar dados
   */
  static async getActiveOrders() {
    const snapshot = await getDocs(
      query(
        collection(db, "ordensProducao"),
        where("status", "in", ["PENDENTE", "EM_PRODUCAO"])
      )
    );
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
  
  static async getCurrentStocks() {
    const snapshot = await getDocs(collection(db, "estoques"));
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
  
  static async getAllProducts() {
    const snapshot = await getDocs(collection(db, "produtos"));
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
  
  static async getStockInMainWarehouse(produtoId) {
    const estoqueQuery = query(
      collection(db, "estoques"),
      where("produtoId", "==", produtoId),
      where("armazemId", "==", "ALM01") // Assumindo ALM01 como almoxarifado principal
    );
    
    const snapshot = await getDocs(estoqueQuery);
    if (snapshot.empty) return 0;
    
    const estoque = snapshot.docs[0].data();
    return estoque.saldo - (estoque.saldoReservado || 0);
  }
  
  /**
   * Dashboard com status geral do sistema
   */
  static async getSystemStatus() {
    try {
      const [ordensAtivas, necessidades, solicitacoesPendentes] = await Promise.all([
        this.getActiveOrders(),
        this.getCurrentRequirements(),
        this.getPendingRequests()
      ]);
      
      return {
        ordensProducao: {
          ativas: ordensAtivas.length,
          pendentes: ordensAtivas.filter(o => o.status === 'PENDENTE').length,
          emProducao: ordensAtivas.filter(o => o.status === 'EM_PRODUCAO').length
        },
        necessidades: {
          total: necessidades.length,
          criticas: necessidades.filter(n => n.criticidade === 'CRITICA').length,
          altas: necessidades.filter(n => n.criticidade === 'ALTA').length
        },
        solicitacoes: {
          pendentes: solicitacoesPendentes.length,
          valorTotal: solicitacoesPendentes.reduce((total, s) => total + (s.valorEstimado || 0), 0)
        }
      };
      
    } catch (error) {
      console.error('Erro ao buscar status:', error);
      return null;
    }
  }
  
  static async getCurrentRequirements() {
    const snapshot = await getDocs(collection(db, "necessidadesCompra"));
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
  
  static async getPendingRequests() {
    const snapshot = await getDocs(
      query(
        collection(db, "solicitacoesCompra"),
        where("status", "==", "PENDENTE")
      )
    );
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
}

// Exportar para uso global
window.ProductionController = ProductionController;
