<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Lista Técnica de Equipamento (BOM)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            min-height: 80vh; /* Ajuste para que os painéis tenham altura */
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .panel {
            padding: 20px;
            overflow-y: auto;
        }

        .left-panel {
            width: 35%;
            border-right: 1px solid var(--border-color);
        }

        .right-panel {
            width: 65%;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--primary-color);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 10px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--success-hover);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: var(--danger-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .search-container {
            margin-bottom: 15px;
        }

        .search-container input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .equipment-list {
            list-style: none;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .equipment-item {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            background-color: #fff;
            transition: background-color 0.2s;
        }

        .equipment-item:last-child {
            border-bottom: none;
        }

        .equipment-item:hover {
            background-color: var(--secondary-color);
        }

        .equipment-item.selected {
            background-color: var(--primary-color);
            color: white;
        }

        .equipment-item.selected:hover {
             background-color: var(--primary-hover);
        }

        .bom-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .bom-table th,
        .bom-table td {
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        .bom-table th {
            background-color: var(--secondary-color);
            font-weight: 600;
            color: var(--text-secondary);
        }

        .bom-table tr:hover {
            background-color: #f8f9fa;
        }

        .add-item-form {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 20px;
        }

        .add-item-form .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .add-item-form button {
            flex-shrink: 0;
        }

        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 15px 20px;
          border-radius: 4px;
          color: white;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          display: none;
          align-items: center;
          gap: 10px;
          transition: opacity 0.3s ease;
        }

        .notification-success { background-color: var(--success-color); }
        .notification-error { background-color: var(--danger-color); }
        .notification-icon { font-weight: bold; font-size: 18px; }

         @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .left-panel,
            .right-panel {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }

            .add-item-form {
              flex-direction: column;
              align-items: stretch;
            }

             .add-item-form button {
                width: 100%;
            }
        }

    </style>
</head>
<body>
    <div class="header">
        <h1>Lista Técnica de Equipamento (BOM)</h1>
        <div>
          <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <div class="container">
        <div class="panel left-panel">
            <h2 class="panel-title">Selecionar Equipamento</h2>
            <div class="search-container">
                <input type="text" id="equipmentSearchInput" placeholder="Pesquisar equipamento...">
            </div>
            <ul id="equipmentList" class="equipment-list">
                <!-- Equipamentos serão carregados aqui -->
            </ul>
        </div>

        <div class="panel right-panel">
            <h2 class="panel-title">Componentes da BOM</h2>
            <div id="bomContent" style="display: none;">
                <p>Equipamento Selecionado: <strong id="selectedEquipmentName"></strong></p>

                <div class="add-item-form">
                    <div class="form-group">
                        <label for="materialSelect">Material</label>
                        <select id="materialSelect">
                            <option value="">Selecione um material...</option>
                            <!-- Materiais serão carregados aqui -->
                        </select>
                    </div>
                    <div class="form-group" style="flex: 0 0 100px;">
                        <label for="quantidade">Quantidade</label>
                        <input type="number" id="quantidade" min="1" value="1">
                    </div>
                    <button class="btn btn-primary" onclick="addMaterialToBom()"><i class="fas fa-plus"></i> Adicionar</button>
                </div>

                <table class="bom-table">
                    <thead>
                        <tr>
                            <th>Código Material</th>
                            <th>Descrição Material</th>
                            <th>Quantidade</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="bomTableBody">
                        <!-- Itens da BOM serão exibidos aqui -->
                    </tbody>
                </table>
            </div>
            <div id="noEquipmentSelectedMessage">
                <p>Selecione um equipamento na lista ao lado para visualizar e gerenciar sua Lista Técnica.</p>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, doc, setDoc, deleteDoc, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let equipamentos = [];
        let materiais = [];
        let currentEquipmentId = null;
        let currentBomItems = [];

        window.onload = async function() {
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

             const currentUser = JSON.parse(userSession);
             // Ajustar o nível de permissão conforme necessário para esta página
             if (currentUser.nivel < 9) { // Exemplo: Nível 9 para Superusuários
                 showNotification('Acesso restrito. Nível de permissão insuficiente.', 'error');
                 window.location.href = 'index.html'; // Redirecionar para a página inicial
                 return;
             }

            await loadEquipamentos();
            await loadMateriais();
            document.getElementById('equipmentSearchInput').addEventListener('input', filterEquipmentList);
        };

        async function loadEquipamentos() {
            try {
                const equipamentosSnapshot = await getDocs(collection(db, "recursos"));
                equipamentos = equipamentosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                displayEquipamentos(equipamentos);
            } catch (error) {
                console.error("Erro ao carregar equipamentos:", error);
                showNotification("Erro ao carregar equipamentos.", "error");
            }
        }

        function displayEquipamentos(equipList) {
            const equipmentListElement = document.getElementById('equipmentList');
            equipmentListElement.innerHTML = '';

            equipList.forEach(equip => {
                const li = document.createElement('li');
                li.className = 'equipment-item';
                li.textContent = `${equip.codigo || '-'} - ${equip.maquina || '-'}`;
                li.dataset.id = equip.id;
                li.onclick = () => selectEquipment(equip.id);
                equipmentListElement.appendChild(li);
            });
        }

        function filterEquipmentList() {
            const searchTerm = document.getElementById('equipmentSearchInput').value.toLowerCase();
            const filteredEquipments = equipamentos.filter(equip =>
                (equip.codigo || '').toLowerCase().includes(searchTerm) ||
                (equip.maquina || '').toLowerCase().includes(searchTerm)
            );
            displayEquipamentos(filteredEquipments);
        }

        async function loadMateriais() {
            try {
                const materiaisSnapshot = await getDocs(collection(db, "materiais"));
                materiais = materiaisSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const materialSelect = document.getElementById('materialSelect');
                materialSelect.innerHTML = '<option value="">Selecione um material...</option>';

                materiais.forEach(mat => {
                    const option = document.createElement('option');
                    option.value = mat.id;
                    option.textContent = `${mat.codigo || '-'} - ${mat.descricao || '-'}`;
                    materialSelect.appendChild(option);
                });
            } catch (error) {
                console.error("Erro ao carregar materiais:", error);
                showNotification("Erro ao carregar materiais.", "error");
            }
        }

        async function selectEquipment(equipId) {
            currentEquipmentId = equipId;
            const selectedEquip = equipamentos.find(equip => equip.id === equipId);
            document.getElementById('selectedEquipmentName').textContent = selectedEquip ? `${selectedEquip.codigo || '-'} - ${selectedEquip.maquina || '-'}` : '-';

            // Remove a seleção de todos os itens e seleciona o item atual
            document.querySelectorAll('.equipment-item').forEach(item => item.classList.remove('selected'));
            document.querySelector(`.equipment-item[data-id='${equipId}']`).classList.add('selected');

            document.getElementById('noEquipmentSelectedMessage').style.display = 'none';
            document.getElementById('bomContent').style.display = 'block';

            await loadBomForEquipment(equipId);
        }

        async function loadBomForEquipment(equipId) {
            try {
                // Assumindo que a BOM para um equipamento é armazenada em uma subcoleção
                // Ex: recursos/EQUIPAMENTO_ID/bom/MATERIAL_ID
                // Ou em uma coleção separada linkada pelo equipamentoId
                // Ex: bomEquipamentos onde cada documento é um item da BOM { equipamentoId, materialId, quantidade }

                // Vamos usar a segunda opção por simplicidade inicial: coleção 'bomEquipamentos'
                const bomSnapshot = await getDocs(
                    collection(db, "bomEquipamentos"),
                    where("equipamentoId", "==", equipId)
                );

                currentBomItems = bomSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                displayBomItems();

            } catch (error) {
                console.error("Erro ao carregar BOM:", error);
                showNotification("Erro ao carregar Lista Técnica do equipamento.", "error");
                currentBomItems = [];
                displayBomItems(); // Limpar a tabela em caso de erro
            }
        }

        function displayBomItems() {
            const bomTableBody = document.getElementById('bomTableBody');
            bomTableBody.innerHTML = '';

            currentBomItems.forEach(item => {
                const material = materiais.find(mat => mat.id === item.materialId);
                if (!material) return; // Não exibe se o material não for encontrado

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${material.codigo || '-'}</td>
                    <td>${material.descricao || '-'}</td>
                    <td>${item.quantidade}</td>
                    <td>
                        <button class="btn btn-danger" onclick="removeMaterialFromBom('${item.id}')">Remover</button>
                    </td>
                `;
                bomTableBody.appendChild(row);
            });
        }

        window.addMaterialToBom = async function() {
            if (!currentEquipmentId) {
                showNotification('Selecione um equipamento primeiro.', 'warning');
                return;
            }

            const materialId = document.getElementById('materialSelect').value;
            const quantidade = parseInt(document.getElementById('quantidade').value);

            if (!materialId) {
                showNotification('Selecione um material.', 'warning');
                return;
            }
            if (isNaN(quantidade) || quantidade <= 0) {
                showNotification('Informe uma quantidade válida.', 'warning');
                return;
            }

            // Verificar se o material já está na BOM deste equipamento
            const existingItem = currentBomItems.find(item => item.materialId === materialId);
            if (existingItem) {
                 showNotification('Material já existe nesta BOM.', 'warning');
                 return;
            }

            try {
                 // Adicionar item na coleção 'bomEquipamentos'
                 // O ID do documento será gerado automaticamente pelo Firebase
                 const newBomItem = {
                     equipamentoId: currentEquipmentId,
                     materialId: materialId,
                     quantidade: quantidade
                 };

                 await addDoc(collection(db, "bomEquipamentos"), newBomItem);

                 showNotification('Material adicionado à BOM com sucesso!', 'success');

                 // Recarregar a BOM para atualizar a tabela
                 await loadBomForEquipment(currentEquipmentId);

                 // Resetar formulário de adição
                 document.getElementById('materialSelect').value = '';
                 document.getElementById('quantidade').value = '1';

            } catch (error) {
                 console.error("Erro ao adicionar material à BOM:", error);
                 showNotification("Erro ao adicionar material à Lista Técnica. Por favor, tente novamente.", "error");
            }
        };

        window.removeMaterialFromBom = async function(itemId) {
            if (confirm('Tem certeza que deseja remover este item da BOM?')) {
                try {
                    await deleteDoc(doc(db, "bomEquipamentos", itemId));
                    showNotification('Item removido da BOM com sucesso!', 'success');
                    // Recarregar a BOM para atualizar a tabela
                    await loadBomForEquipment(currentEquipmentId);
                } catch (error) {
                    console.error("Erro ao remover item da BOM:", error);
                    showNotification("Erro ao remover item da Lista Técnica. Por favor, tente novamente.", "error");
                }
            }
        };

        function showNotification(message, type = 'success', duration = 3000) {
          const notification = document.getElementById('notification');
          notification.textContent = message;
          notification.className = '';
          notification.classList.add('notification', `notification-${type}`);
          notification.style.display = 'block';

          let icon = '';
          if (type === 'success') {
            icon = '✓';
          } else if (type === 'error') {
            icon = '✗';
          }

          notification.innerHTML = `<span class="notification-icon">${icon}</span> ${message}`;

          setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.style.display = 'none', 300);
          }, duration);
        }

    </script>
</body>
</html> 