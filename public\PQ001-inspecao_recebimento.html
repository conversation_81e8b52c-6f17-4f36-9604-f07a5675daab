<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PQ001 - Inspeção de Recebimento | Sistema de Qualidade</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="firebase-config.js"></script>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      margin-bottom: 25px;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px 25px;
      border-bottom: 1px solid #dee2e6;
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-body {
      padding: 25px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .required::after {
      content: " *";
      color: #e74c3c;
      font-weight: bold;
    }

    input, select, textarea {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: white;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }

    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-pendente {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      color: #856404;
    }

    .status-aprovado {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
    }

    .status-rejeitado {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      color: #721c24;
    }

    .status-quarentena {
      background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
      color: #0c5460;
    }

    .inspection-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
    }

    .criteria-list {
      list-style: none;
      padding: 0;
    }

    .criteria-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      margin-bottom: 10px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #3498db;
    }

    .criteria-item.critical {
      border-left-color: #e74c3c;
    }

    .criteria-item.approved {
      border-left-color: #27ae60;
      background: #f8fff9;
    }

    .criteria-item.rejected {
      border-left-color: #e74c3c;
      background: #fff8f8;
    }

    .criteria-check {
      display: flex;
      gap: 10px;
    }

    .check-btn {
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .check-btn.approve {
      background: #27ae60;
      color: white;
    }

    .check-btn.reject {
      background: #e74c3c;
      color: white;
    }

    .check-btn:hover {
      transform: scale(1.05);
    }

    .actions {
      display: flex;
      gap: 15px;
      justify-content: flex-end;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;
    }

    .info-panel {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border: 1px solid #2196f3;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .info-panel h4 {
      color: #1976d2;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .inspection-grid {
        grid-template-columns: 1fr;
      }
      
      .form-row {
        grid-template-columns: 1fr;
      }
      
      .actions {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>
        <i class="fas fa-search"></i>
        PQ001 - Inspeção de Recebimento
      </h1>
      <div class="header-actions">
        <button class="btn btn-primary" onclick="loadPendingInspections()">
          <i class="fas fa-sync-alt"></i>
          Atualizar Lista
        </button>
        <button class="btn btn-primary" onclick="navigateBack()">
          <i class="fas fa-arrow-left"></i>
          Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- Painel de Informações do Sistema -->
      <div class="info-panel" id="systemInfo">
        <h4>
          <i class="fas fa-info-circle"></i>
          Status do Módulo de Qualidade
        </h4>
        <div id="moduleStatus">Carregando configurações...</div>
      </div>

      <!-- Lista de Materiais Pendentes -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <i class="fas fa-clipboard-list"></i>
            Materiais Aguardando Inspeção
          </div>
        </div>
        <div class="card-body">
          <div id="pendingMaterials">
            <p style="text-align: center; color: #6c757d; padding: 40px;">
              <i class="fas fa-spinner fa-spin"></i>
              Carregando materiais pendentes...
            </p>
          </div>
        </div>
      </div>

      <!-- Formulário de Inspeção -->
      <div class="card" id="inspectionForm" style="display: none;">
        <div class="card-header">
          <div class="card-title">
            <i class="fas fa-microscope"></i>
            Formulário de Inspeção
          </div>
        </div>
        <div class="card-body">
          <form id="qualityInspectionForm">
            <input type="hidden" id="materialId">
            
            <div class="inspection-grid">
              <!-- Informações do Material -->
              <div>
                <h4 style="margin-bottom: 15px; color: #2c3e50;">
                  <i class="fas fa-box"></i>
                  Informações do Material
                </h4>
                
                <div class="form-row">
                  <div class="form-col">
                    <label>Código do Material</label>
                    <input type="text" id="materialCode" readonly>
                  </div>
                  <div class="form-col">
                    <label>Descrição</label>
                    <input type="text" id="materialDescription" readonly>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label>Fornecedor</label>
                    <input type="text" id="supplierName" readonly>
                  </div>
                  <div class="form-col">
                    <label>Lote</label>
                    <input type="text" id="batchNumber" readonly>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label>Quantidade Recebida</label>
                    <input type="text" id="receivedQuantity" readonly>
                  </div>
                  <div class="form-col">
                    <label>Data de Recebimento</label>
                    <input type="text" id="receivedDate" readonly>
                  </div>
                </div>
              </div>

              <!-- Critérios de Inspeção -->
              <div>
                <h4 style="margin-bottom: 15px; color: #2c3e50;">
                  <i class="fas fa-tasks"></i>
                  Critérios de Inspeção
                </h4>
                
                <ul class="criteria-list" id="inspectionCriteria">
                  <!-- Critérios serão carregados dinamicamente -->
                </ul>
              </div>
            </div>

            <!-- Resultado da Inspeção -->
            <div class="form-row" style="margin-top: 30px;">
              <div class="form-col">
                <label for="inspectionResult" class="required">Resultado da Inspeção</label>
                <select id="inspectionResult" required onchange="updateResultFields()">
                  <option value="">Selecione o resultado...</option>
                  <option value="APROVADO">✅ Aprovado</option>
                  <option value="APROVADO_RESTRICAO">⚠️ Aprovado com Restrição</option>
                  <option value="REJEITADO">❌ Rejeitado</option>
                  <option value="QUARENTENA">🔄 Quarentena</option>
                </select>
              </div>
              <div class="form-col">
                <label for="inspector" class="required">Inspetor Responsável</label>
                <input type="text" id="inspector" required placeholder="Nome do inspetor">
              </div>
            </div>

            <div class="form-row">
              <div class="form-col">
                <label for="observations">Observações</label>
                <textarea id="observations" placeholder="Descreva detalhes da inspeção, não conformidades encontradas, ações recomendadas..."></textarea>
              </div>
            </div>

            <!-- Campos condicionais -->
            <div id="rejectionFields" style="display: none;">
              <div class="form-row">
                <div class="form-col">
                  <label for="rejectionReason" class="required">Motivo da Rejeição</label>
                  <select id="rejectionReason">
                    <option value="">Selecione o motivo...</option>
                    <option value="DIMENSOES_INCORRETAS">Dimensões Incorretas</option>
                    <option value="DEFEITO_VISUAL">Defeito Visual</option>
                    <option value="MATERIAL_INCORRETO">Material Incorreto</option>
                    <option value="CONTAMINACAO">Contaminação</option>
                    <option value="DOCUMENTACAO_INCOMPLETA">Documentação Incompleta</option>
                    <option value="OUTROS">Outros</option>
                  </select>
                </div>
                <div class="form-col">
                  <label for="corrective Action">Ação Corretiva Sugerida</label>
                  <select id="correctiveAction">
                    <option value="DEVOLUCAO">Devolução ao Fornecedor</option>
                    <option value="RETRABALHO">Retrabalho</option>
                    <option value="DESCARTE">Descarte</option>
                    <option value="RECLASSIFICACAO">Reclassificação</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="actions">
              <button type="button" class="btn btn-danger" onclick="cancelInspection()">
                <i class="fas fa-times"></i>
                Cancelar
              </button>
              <button type="submit" class="btn btn-success">
                <i class="fas fa-check"></i>
                Finalizar Inspeção
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { 
      getFirestore, 
      collection, 
      doc, 
      getDoc, 
      getDocs, 
      addDoc, 
      updateDoc, 
      query, 
      where, 
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    const db = getFirestore();
    let systemParams = {};
    let currentMaterial = null;

    // Carregar parâmetros do sistema
    async function loadSystemParams() {
      try {
        const paramsDoc = await getDoc(doc(db, "parametros", "sistema"));
        if (paramsDoc.exists()) {
          systemParams = paramsDoc.data();
          updateSystemInfo();
        }
      } catch (error) {
        console.error("Erro ao carregar parâmetros:", error);
      }
    }

    // Atualizar informações do sistema
    function updateSystemInfo() {
      const moduleStatus = document.getElementById('moduleStatus');
      const isActive = systemParams.moduloQualidadeAtivo;
      
      moduleStatus.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <span class="status-badge ${isActive ? 'status-aprovado' : 'status-rejeitado'}">
            ${isActive ? '✅ Módulo Ativo' : '❌ Módulo Inativo'}
          </span>
          <span>Inspeção de Recebimento: ${systemParams.inspecaoRecebimento ? 'Habilitada' : 'Desabilitada'}</span>
          <span>Armazém de Qualidade: ${systemParams.armazemQualidade ? 'Habilitado' : 'Desabilitado'}</span>
        </div>
      `;
    }

    // Carregar materiais pendentes de inspeção
    window.loadPendingInspections = async function() {
      try {
        // Simular dados de materiais pendentes (integrar com seu sistema)
        const pendingMaterials = [
          {
            id: '1',
            materialCode: 'MP001',
            description: 'Aço Carbono 1020',
            supplier: 'Fornecedor ABC Ltda',
            batch: 'LT2024001',
            quantity: '1000 kg',
            receivedDate: '2024-06-22',
            priority: 'ALTA'
          },
          {
            id: '2',
            materialCode: 'MP002',
            description: 'Parafuso M8x20',
            supplier: 'Parafusos XYZ',
            batch: 'LT2024002',
            quantity: '5000 pcs',
            receivedDate: '2024-06-22',
            priority: 'NORMAL'
          }
        ];

        displayPendingMaterials(pendingMaterials);
      } catch (error) {
        console.error("Erro ao carregar materiais:", error);
      }
    };

    // Exibir materiais pendentes
    function displayPendingMaterials(materials) {
      const container = document.getElementById('pendingMaterials');
      
      if (materials.length === 0) {
        container.innerHTML = `
          <p style="text-align: center; color: #6c757d; padding: 40px;">
            <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i><br>
            Nenhum material pendente de inspeção
          </p>
        `;
        return;
      }

      container.innerHTML = materials.map(material => `
        <div class="criteria-item" style="cursor: pointer;" onclick="startInspection('${material.id}')">
          <div>
            <strong>${material.materialCode}</strong> - ${material.description}<br>
            <small style="color: #6c757d;">
              Fornecedor: ${material.supplier} | Lote: ${material.batch} | Qtd: ${material.quantity}
            </small>
          </div>
          <div style="display: flex; align-items: center; gap: 10px;">
            <span class="status-badge status-pendente">Pendente</span>
            <span class="status-badge ${material.priority === 'ALTA' ? 'status-rejeitado' : 'status-aprovado'}">
              ${material.priority}
            </span>
            <i class="fas fa-arrow-right"></i>
          </div>
        </div>
      `).join('');
    }

    // Iniciar inspeção de um material
    window.startInspection = function(materialId) {
      // Simular carregamento dos dados do material
      currentMaterial = {
        id: materialId,
        materialCode: 'MP001',
        description: 'Aço Carbono 1020',
        supplier: 'Fornecedor ABC Ltda',
        batch: 'LT2024001',
        quantity: '1000 kg',
        receivedDate: '2024-06-22'
      };

      // Preencher formulário
      document.getElementById('materialId').value = currentMaterial.id;
      document.getElementById('materialCode').value = currentMaterial.materialCode;
      document.getElementById('materialDescription').value = currentMaterial.description;
      document.getElementById('supplierName').value = currentMaterial.supplier;
      document.getElementById('batchNumber').value = currentMaterial.batch;
      document.getElementById('receivedQuantity').value = currentMaterial.quantity;
      document.getElementById('receivedDate').value = new Date(currentMaterial.receivedDate).toLocaleDateString('pt-BR');

      // Carregar critérios de inspeção
      loadInspectionCriteria();

      // Mostrar formulário
      document.getElementById('inspectionForm').style.display = 'block';
      document.getElementById('inspectionForm').scrollIntoView({ behavior: 'smooth' });
    };

    // Carregar critérios de inspeção
    function loadInspectionCriteria() {
      const criteria = [
        { id: 1, name: 'Dimensões conforme especificação', critical: true, status: null },
        { id: 2, name: 'Acabamento superficial adequado', critical: false, status: null },
        { id: 3, name: 'Certificado de qualidade presente', critical: true, status: null },
        { id: 4, name: 'Embalagem íntegra', critical: false, status: null },
        { id: 5, name: 'Identificação do lote visível', critical: true, status: null }
      ];

      const container = document.getElementById('inspectionCriteria');
      container.innerHTML = criteria.map(criterion => `
        <li class="criteria-item ${criterion.critical ? 'critical' : ''}" id="criterion-${criterion.id}">
          <div>
            <strong>${criterion.name}</strong>
            ${criterion.critical ? '<span style="color: #e74c3c; font-size: 12px;"> (CRÍTICO)</span>' : ''}
          </div>
          <div class="criteria-check">
            <button type="button" class="check-btn approve" onclick="setCriterion(${criterion.id}, true)">
              <i class="fas fa-check"></i>
            </button>
            <button type="button" class="check-btn reject" onclick="setCriterion(${criterion.id}, false)">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </li>
      `).join('');
    }

    // Definir status de um critério
    window.setCriterion = function(criterionId, approved) {
      const criterionElement = document.getElementById(`criterion-${criterionId}`);
      criterionElement.classList.remove('approved', 'rejected');
      criterionElement.classList.add(approved ? 'approved' : 'rejected');
      
      // Armazenar resultado
      criterionElement.dataset.result = approved;
      
      // Verificar se todos os critérios foram avaliados
      checkAllCriteria();
    };

    // Verificar se todos os critérios foram avaliados
    function checkAllCriteria() {
      const criteria = document.querySelectorAll('.criteria-item[id^="criterion-"]');
      const evaluated = Array.from(criteria).filter(c => c.dataset.result !== undefined);
      
      if (evaluated.length === criteria.length) {
        // Sugerir resultado baseado nos critérios
        const rejected = evaluated.filter(c => c.dataset.result === 'false');
        const criticalRejected = rejected.filter(c => c.classList.contains('critical'));
        
        const resultSelect = document.getElementById('inspectionResult');
        if (criticalRejected.length > 0) {
          resultSelect.value = 'REJEITADO';
        } else if (rejected.length > 0) {
          resultSelect.value = 'APROVADO_RESTRICAO';
        } else {
          resultSelect.value = 'APROVADO';
        }
        
        updateResultFields();
      }
    }

    // Atualizar campos baseado no resultado
    window.updateResultFields = function() {
      const result = document.getElementById('inspectionResult').value;
      const rejectionFields = document.getElementById('rejectionFields');
      
      if (result === 'REJEITADO') {
        rejectionFields.style.display = 'block';
        document.getElementById('rejectionReason').required = true;
      } else {
        rejectionFields.style.display = 'none';
        document.getElementById('rejectionReason').required = false;
      }
    };

    // Cancelar inspeção
    window.cancelInspection = function() {
      document.getElementById('inspectionForm').style.display = 'none';
      document.getElementById('qualityInspectionForm').reset();
      currentMaterial = null;
    };

    // Submeter inspeção
    document.getElementById('qualityInspectionForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const formData = {
        materialId: document.getElementById('materialId').value,
        result: document.getElementById('inspectionResult').value,
        inspector: document.getElementById('inspector').value,
        observations: document.getElementById('observations').value,
        rejectionReason: document.getElementById('rejectionReason').value,
        correctiveAction: document.getElementById('correctiveAction').value,
        inspectionDate: new Date(),
        criteria: Array.from(document.querySelectorAll('.criteria-item[id^="criterion-"]')).map(c => ({
          id: c.id.split('-')[1],
          result: c.dataset.result === 'true'
        }))
      };

      try {
        // Salvar inspeção no Firebase
        await addDoc(collection(db, "inspecoes_qualidade"), formData);
        
        // Atualizar status do material
        // await updateDoc(doc(db, "materiais_recebidos", formData.materialId), {
        //   status: formData.result,
        //   inspectionDate: formData.inspectionDate
        // });

        alert(`Inspeção finalizada com resultado: ${formData.result}`);
        cancelInspection();
        loadPendingInspections();
      } catch (error) {
        console.error("Erro ao salvar inspeção:", error);
        alert("Erro ao salvar inspeção: " + error.message);
      }
    });

    // Navegação
    window.navigateBack = function() {
      window.history.back();
    };

    // Funcionalidades adicionais para o inspetor

    // Gerar relatório de inspeção
    window.generateInspectionReport = function() {
      if (!currentMaterial) return;

      const reportData = {
        material: currentMaterial,
        criteria: Array.from(document.querySelectorAll('.criteria-item[id^="criterion-"]')).map(c => ({
          name: c.querySelector('strong').textContent,
          critical: c.classList.contains('critical'),
          result: c.dataset.result === 'true' ? 'Aprovado' : c.dataset.result === 'false' ? 'Rejeitado' : 'Não avaliado'
        })),
        result: document.getElementById('inspectionResult').value,
        inspector: document.getElementById('inspector').value,
        observations: document.getElementById('observations').value,
        date: new Date().toLocaleDateString('pt-BR')
      };

      // Gerar PDF ou imprimir relatório
      const reportWindow = window.open('', '_blank');
      reportWindow.document.write(`
        <html>
          <head>
            <title>Relatório de Inspeção - ${reportData.material.materialCode}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
              .section { margin: 20px 0; }
              .criteria { border-collapse: collapse; width: 100%; }
              .criteria th, .criteria td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .criteria th { background-color: #f2f2f2; }
              .approved { color: green; font-weight: bold; }
              .rejected { color: red; font-weight: bold; }
              .critical { background-color: #ffe6e6; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>RELATÓRIO DE INSPEÇÃO DE QUALIDADE</h1>
              <h2>PQ001 - Inspeção de Recebimento</h2>
            </div>

            <div class="section">
              <h3>Informações do Material</h3>
              <p><strong>Código:</strong> ${reportData.material.materialCode}</p>
              <p><strong>Descrição:</strong> ${reportData.material.description}</p>
              <p><strong>Fornecedor:</strong> ${reportData.material.supplier}</p>
              <p><strong>Lote:</strong> ${reportData.material.batch}</p>
              <p><strong>Quantidade:</strong> ${reportData.material.quantity}</p>
              <p><strong>Data de Recebimento:</strong> ${reportData.material.receivedDate}</p>
            </div>

            <div class="section">
              <h3>Critérios de Inspeção</h3>
              <table class="criteria">
                <thead>
                  <tr>
                    <th>Critério</th>
                    <th>Tipo</th>
                    <th>Resultado</th>
                  </tr>
                </thead>
                <tbody>
                  ${reportData.criteria.map(c => `
                    <tr class="${c.critical ? 'critical' : ''}">
                      <td>${c.name}</td>
                      <td>${c.critical ? 'CRÍTICO' : 'Normal'}</td>
                      <td class="${c.result === 'Aprovado' ? 'approved' : c.result === 'Rejeitado' ? 'rejected' : ''}">${c.result}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="section">
              <h3>Resultado da Inspeção</h3>
              <p><strong>Resultado:</strong> <span class="${reportData.result === 'APROVADO' ? 'approved' : 'rejected'}">${reportData.result}</span></p>
              <p><strong>Inspetor:</strong> ${reportData.inspector}</p>
              <p><strong>Data da Inspeção:</strong> ${reportData.date}</p>
              <p><strong>Observações:</strong> ${reportData.observations || 'Nenhuma observação registrada'}</p>
            </div>

            <div class="section" style="margin-top: 50px;">
              <p>_________________________________</p>
              <p>Assinatura do Inspetor</p>
            </div>
          </body>
        </html>
      `);
      reportWindow.document.close();
      reportWindow.print();
    };

    // Histórico de inspeções
    window.viewInspectionHistory = async function() {
      try {
        const inspectionsQuery = query(
          collection(db, "inspecoes_qualidade"),
          orderBy("inspectionDate", "desc")
        );
        const snapshot = await getDocs(inspectionsQuery);

        const inspections = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          inspectionDate: doc.data().inspectionDate?.toDate?.()?.toLocaleDateString('pt-BR') || 'N/A'
        }));

        showInspectionHistoryModal(inspections);
      } catch (error) {
        console.error("Erro ao carregar histórico:", error);
        alert("Erro ao carregar histórico de inspeções");
      }
    };

    // Modal de histórico
    function showInspectionHistoryModal(inspections) {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
        align-items: center; justify-content: center;
      `;

      modal.innerHTML = `
        <div style="background: white; border-radius: 15px; padding: 30px; max-width: 80%; max-height: 80%; overflow-y: auto;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2><i class="fas fa-history"></i> Histórico de Inspeções</h2>
            <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
          </div>

          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #f8f9fa;">
                  <th style="padding: 12px; border: 1px solid #ddd;">Data</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Material</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Resultado</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Inspetor</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Ações</th>
                </tr>
              </thead>
              <tbody>
                ${inspections.map(inspection => `
                  <tr>
                    <td style="padding: 12px; border: 1px solid #ddd;">${inspection.inspectionDate}</td>
                    <td style="padding: 12px; border: 1px solid #ddd;">${inspection.materialId}</td>
                    <td style="padding: 12px; border: 1px solid #ddd;">
                      <span class="status-badge status-${inspection.result.toLowerCase()}">${inspection.result}</span>
                    </td>
                    <td style="padding: 12px; border: 1px solid #ddd;">${inspection.inspector}</td>
                    <td style="padding: 12px; border: 1px solid #ddd;">
                      <button onclick="viewInspectionDetails('${inspection.id}')" class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">
                        <i class="fas fa-eye"></i> Ver
                      </button>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

      modal.className = 'modal';
      document.body.appendChild(modal);
    }

    // Configurações do inspetor
    window.openInspectorSettings = function() {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
        align-items: center; justify-content: center;
      `;

      modal.innerHTML = `
        <div style="background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2><i class="fas fa-cog"></i> Configurações do Inspetor</h2>
            <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
          </div>

          <form id="inspectorSettingsForm">
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">Nome do Inspetor Padrão:</label>
              <input type="text" id="defaultInspector" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"
                     value="${localStorage.getItem('defaultInspector') || ''}" placeholder="Seu nome completo">
            </div>

            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">Assinatura Digital:</label>
              <input type="text" id="digitalSignature" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"
                     value="${localStorage.getItem('digitalSignature') || ''}" placeholder="Código de assinatura">
            </div>

            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold;">Critérios Padrão:</label>
              <select id="defaultCriteria" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                <option value="standard">Critérios Padrão</option>
                <option value="strict">Critérios Rigorosos</option>
                <option value="custom">Critérios Personalizados</option>
              </select>
            </div>

            <div style="margin-bottom: 20px;">
              <label style="display: flex; align-items: center; gap: 10px;">
                <input type="checkbox" id="autoFillInspector" ${localStorage.getItem('autoFillInspector') === 'true' ? 'checked' : ''}>
                Preencher automaticamente nome do inspetor
              </label>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
              <button type="button" onclick="this.closest('.modal').remove()" class="btn btn-danger">Cancelar</button>
              <button type="submit" class="btn btn-success">Salvar</button>
            </div>
          </form>
        </div>
      `;

      modal.className = 'modal';
      document.body.appendChild(modal);

      // Handler do formulário
      document.getElementById('inspectorSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        localStorage.setItem('defaultInspector', document.getElementById('defaultInspector').value);
        localStorage.setItem('digitalSignature', document.getElementById('digitalSignature').value);
        localStorage.setItem('defaultCriteria', document.getElementById('defaultCriteria').value);
        localStorage.setItem('autoFillInspector', document.getElementById('autoFillInspector').checked);

        alert('Configurações salvas com sucesso!');
        modal.remove();

        // Aplicar configurações
        if (localStorage.getItem('autoFillInspector') === 'true') {
          document.getElementById('inspector').value = localStorage.getItem('defaultInspector') || '';
        }
      });
    };

    // Adicionar botões na interface
    function addInspectorButtons() {
      const headerActions = document.querySelector('.header-actions');

      // Botão de configurações
      const settingsBtn = document.createElement('button');
      settingsBtn.className = 'btn btn-warning';
      settingsBtn.onclick = openInspectorSettings;
      settingsBtn.innerHTML = '<i class="fas fa-cog"></i> Configurações';
      headerActions.insertBefore(settingsBtn, headerActions.lastElementChild);

      // Botão de histórico
      const historyBtn = document.createElement('button');
      historyBtn.className = 'btn btn-primary';
      historyBtn.onclick = viewInspectionHistory;
      historyBtn.innerHTML = '<i class="fas fa-history"></i> Histórico';
      headerActions.insertBefore(historyBtn, headerActions.lastElementChild);
    }

    // Aplicar configurações salvas
    function applyInspectorSettings() {
      if (localStorage.getItem('autoFillInspector') === 'true') {
        const inspectorField = document.getElementById('inspector');
        if (inspectorField) {
          inspectorField.value = localStorage.getItem('defaultInspector') || '';
        }
      }
    }

    // Inicialização
    document.addEventListener('DOMContentLoaded', async function() {
      console.log("🔍 Inicializando módulo de inspeção...");
      await loadSystemParams();
      await loadPendingInspections();
      addInspectorButtons();
      applyInspectorSettings();
      console.log("✅ Módulo de inspeção inicializado!");
    });
  </script>
</body>
</html>
