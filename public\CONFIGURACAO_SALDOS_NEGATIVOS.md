# 🔧 CONFIGURAÇÃO PARA EVITAR SALDOS NEGATIVOS

## 🎯 **CONFIGURAÇÕES NECESSÁRIAS NO SISTEMA**

Para garantir que o sistema **nunca permita saldos negativos** e **sempre bloqueie apontamentos sem material**, você precisa verificar e ajustar as seguintes configurações:

---

## 📋 **1. PARÂMETROS NO config_parametros.html**

### **🔧 ACESSE:**
```
config_parametros.html → Parâmetros de Estoque
```

### **⚙️ CONFIGURAÇÕES CRÍTICAS:**

#### **🚫 1.1 PERMITIR ESTOQUE NEGATIVO**
```html
Parâmetros de Estoque → Permitir Estoque Negativo
Status: ❌ DESABILITADO (OFF)
```

**📍 Localização:** Linha 378-382 no código
```html
<label>Permitir Estoque Negativo</label>
<label class="switch">
    <input type="checkbox" id="permitirEstoqueNegativo" 
           title="USADO EM: services/inventory-service.js, movimentacoes_estoque.html, apontamento_producao.html. Permite que saldo de estoque fique negativo.">
    <span class="slider"></span>
</label>
```

**✅ Valor Padrão:** `false` (linha 781)
```javascript
permitirEstoqueNegativo: false,
```

#### **🚫 1.2 PERMITIR PRODUÇÃO SEM ESTOQUE**
```html
Parâmetros de Produção → Permitir Produção sem Estoque
Status: ❌ DESABILITADO (OFF)
```

**📍 Localização:** Linha 346-351 no código
```html
<label>Permitir Produção sem Estoque</label>
<label class="switch">
    <input type="checkbox" id="permitirProducaoSemEstoque" 
           title="USADO EM: ordens_producao.html, apontamento_producao.html. Permite iniciar produção mesmo sem estoque suficiente de materiais.">
    <span class="slider"></span>
</label>
```

**✅ Valor Padrão:** `false` (linha 778)
```javascript
permitirProducaoSemEstoque: false,
```

---

## 🔍 **2. VERIFICAÇÃO DAS CONFIGURAÇÕES ATUAIS**

### **📋 COMO VERIFICAR:**

1. **Acesse** `config_parametros.html`
2. **Vá para** "Parâmetros de Estoque"
3. **Verifique** se "Permitir Estoque Negativo" está **DESABILITADO** (switch OFF)
4. **Vá para** "Parâmetros de Produção"  
5. **Verifique** se "Permitir Produção sem Estoque" está **DESABILITADO** (switch OFF)

### **✅ CONFIGURAÇÃO CORRETA:**
```
✅ Permitir Estoque Negativo: OFF (desabilitado)
✅ Permitir Produção sem Estoque: OFF (desabilitado)
```

### **❌ CONFIGURAÇÃO INCORRETA:**
```
❌ Permitir Estoque Negativo: ON (habilitado)
❌ Permitir Produção sem Estoque: ON (habilitado)
```

---

## 🎯 **3. IMPACTO DAS CONFIGURAÇÕES**

### **🔧 QUANDO "PERMITIR ESTOQUE NEGATIVO" = FALSE:**

#### **✅ COMPORTAMENTOS GARANTIDOS:**
- 🚫 **Movimentações de saída** são bloqueadas se resultarem em saldo negativo
- 🚫 **Apontamentos de produção** são bloqueados se não há material suficiente
- 🚫 **Transferências** são bloqueadas se não há saldo no armazém origem
- ✅ **Sistema força** resolução do problema antes de permitir operações

#### **📊 MÓDULOS AFETADOS:**
- `services/inventory-service.js` - Controle de estoque
- `movimentacoes_estoque.html` - Movimentações
- `apontamento_producao.html` - Apontamentos (já implementado)
- `transferencias_armazem.html` - Transferências

### **🔧 QUANDO "PERMITIR PRODUÇÃO SEM ESTOQUE" = FALSE:**

#### **✅ COMPORTAMENTOS GARANTIDOS:**
- 🚫 **Apontamentos** bloqueados sem material suficiente
- 🚫 **Ordens de produção** não podem ser iniciadas sem material
- ✅ **Validação rigorosa** antes de qualquer operação de produção

#### **📊 MÓDULOS AFETADOS:**
- `ordens_producao.html` - Criação e gestão de OPs
- `apontamento_producao.html` - Apontamentos de produção

---

## 🛠️ **4. COMO ALTERAR AS CONFIGURAÇÕES**

### **📋 PASSO A PASSO:**

1. **Acesse** o sistema como administrador
2. **Vá para** `config_parametros.html`
3. **Localize** "Parâmetros de Estoque"
4. **Encontre** "Permitir Estoque Negativo"
5. **Certifique-se** que o switch está **OFF** (desabilitado)
6. **Localize** "Parâmetros de Produção"
7. **Encontre** "Permitir Produção sem Estoque"
8. **Certifique-se** que o switch está **OFF** (desabilitado)
9. **Clique** "Salvar Parâmetros"
10. **Confirme** a mensagem "Parâmetros salvos com sucesso!"

### **💾 SALVAMENTO AUTOMÁTICO:**
```javascript
// Os parâmetros são salvos no Firebase automaticamente
await setDoc(docRef, {
    permitirEstoqueNegativo: false,
    permitirProducaoSemEstoque: false,
    // ... outros parâmetros
}, { merge: true });
```

---

## 🔒 **5. VALIDAÇÕES IMPLEMENTADAS**

### **✅ NO APONTAMENTO DE PRODUÇÃO:**

#### **🚫 BLOQUEIO TOTAL:**
```javascript
// SEMPRE bloquear apontamento se não há material suficiente
if (saldoDisponivel < quantidadeRestante) {
    statusClass = 'status-error';
    canProduce = false;
}

// SEMPRE verificar se pode produzir antes de abrir o modal
if (!canProduce) {
    mostrarModalMateriaisFaltantesApontamento(materiaisFaltantes, currentOrder);
    return; // Não abrir o modal de apontamento
}
```

#### **📊 MODAL INFORMATIVO:**
- 🚫 **Bloqueia** completamente o apontamento
- 📋 **Lista** todos os materiais em falta
- 💡 **Orienta** como resolver o problema
- ✅ **Força** transferência de materiais antes do apontamento

---

## 🧪 **6. TESTE DAS CONFIGURAÇÕES**

### **📋 CENÁRIO DE TESTE:**

1. **Configure** ambos parâmetros como `false`
2. **Encontre** uma OP com materiais em falta
3. **Tente** fazer apontamento
4. **Verifique** se:
   - ❌ Modal de apontamento **NÃO ABRE**
   - ✅ Modal de bloqueio **ABRE** com detalhes
   - 📋 Lista **TODOS** os materiais em falta
   - 💡 Mostra **INSTRUÇÕES** de resolução

### **✅ RESULTADO ESPERADO:**
```
🚫 APONTAMENTO BLOQUEADO
📋 Modal informativo com materiais em falta
💡 Instruções claras de como resolver
❌ Impossível fazer apontamento sem material
```

---

## 🎯 **7. RESUMO DAS CONFIGURAÇÕES**

### **⚙️ CONFIGURAÇÕES OBRIGATÓRIAS:**
```
config_parametros.html:
├── Parâmetros de Estoque
│   └── ❌ Permitir Estoque Negativo: OFF
└── Parâmetros de Produção
    └── ❌ Permitir Produção sem Estoque: OFF
```

### **✅ BENEFÍCIOS ALCANÇADOS:**
- 🚫 **Impossível** ter saldos negativos no sistema
- 🚫 **Impossível** fazer apontamentos sem material
- ✅ **Controle total** sobre movimentações de estoque
- 📊 **Dados confiáveis** de estoque sempre
- 💡 **Orientação clara** quando há problemas

### **🔒 SEGURANÇA GARANTIDA:**
- ✅ **Validação dupla** - parâmetros + código
- ✅ **Bloqueio preventivo** antes de operações
- ✅ **Interface informativa** para resolução
- ✅ **Controle rigoroso** de todas as movimentações

---

## 🎯 **RESULTADO FINAL**

**Com essas configurações:**

- 🚫 **NUNCA** haverá saldos negativos no sistema
- 🚫 **NUNCA** será possível apontar sem material suficiente  
- ✅ **SEMPRE** haverá validação antes de operações
- 📊 **SEMPRE** os dados de estoque serão confiáveis
- 💡 **SEMPRE** haverá orientação quando houver problemas

**Sistema totalmente protegido contra saldos negativos!** 🔒✅
