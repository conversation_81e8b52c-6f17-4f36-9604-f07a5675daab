<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🩺 Diagnóstico - FUNCIONANDO</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .diagnostic-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .test-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
            border-color: #e74c3c;
        }
        .test-card.running {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .test-card.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .test-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        .test-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .test-result {
            font-weight: bold;
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }
        .btn-success { background: #28a745; color: white; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .item-search {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
        }
        .search-input {
            width: 300px;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            margin: 10px;
            font-size: 16px;
        }
        .result-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="diagnostic-header">
            <h1>🩺 Diagnóstico do Sistema - FUNCIONANDO</h1>
            <p>Verificação específica para o item 101133 e problemas de saldo</p>
            <p><strong>Status:</strong> <span id="statusGeral">Pronto para diagnóstico</span></p>
        </div>

        <!-- Busca Específica de Item -->
        <div class="item-search">
            <h3>🔍 Investigar Item 101133 (Motor WEG)</h3>
            <input type="text" id="itemCodigo" class="search-input" placeholder="Digite o código do produto..." value="101133">
            <button onclick="investigarItem()" class="btn btn-primary">🔍 Investigar Item</button>
            <div id="itemResult"></div>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <h3>🎛️ Diagnósticos Específicos</h3>
            <button onclick="diagnosticoCompleto()" class="btn btn-primary">🩺 Diagnóstico Completo</button>
            <button onclick="verificarSaldosUndefined()" class="btn btn-success">❓ Saldos Undefined</button>
            <button onclick="limparLog()" class="btn btn-secondary">🗑️ Limpar Log</button>
        </div>

        <!-- Grid de Testes -->
        <div class="test-grid">
            <div class="test-card" onclick="testarConexao()">
                <div class="test-icon">🔗</div>
                <div class="test-title">Conexão Firebase</div>
                <div class="test-description">Testa conectividade com o banco</div>
                <div class="test-result" id="result-conexao"></div>
            </div>

            <div class="test-card" onclick="verificarQualidade()">
                <div class="test-icon">✅</div>
                <div class="test-title">Itens na Qualidade</div>
                <div class="test-description">Verifica itens pendentes</div>
                <div class="test-result" id="result-qualidade"></div>
            </div>

            <div class="test-card" onclick="verificarEstoques()">
                <div class="test-icon">🏪</div>
                <div class="test-title">Saldos de Estoque</div>
                <div class="test-description">Verifica saldos inconsistentes</div>
                <div class="test-result" id="result-estoques"></div>
            </div>

            <div class="test-card" onclick="buscarProblemas()">
                <div class="test-icon">⚠️</div>
                <div class="test-title">Problemas Gerais</div>
                <div class="test-description">Busca inconsistências</div>
                <div class="test-result" id="result-problemas"></div>
            </div>
        </div>

        <!-- Log -->
        <div class="log-area" id="logArea"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function setResult(testId, status, message) {
            const element = document.getElementById(`result-${testId}`);
            if (element) {
                const card = element.closest('.test-card');
                card.className = `test-card ${status}`;
                element.textContent = message;
                element.className = `test-result ${status}`;
            }
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de diagnóstico carregado e funcionando', 'info');
        };

        window.investigarItem = async function() {
            const codigo = document.getElementById('itemCodigo').value.trim();
            if (!codigo) {
                alert('Digite um código de produto!');
                return;
            }

            log(`🔍 Investigando item ${codigo}...`, 'info');
            const resultDiv = document.getElementById('itemResult');
            resultDiv.innerHTML = '<p>🔄 Investigando...</p>';

            try {
                // Buscar produto
                log('Carregando produtos...', 'info');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produto = produtos.find(p => p.codigo === codigo);

                if (!produto) {
                    resultDiv.innerHTML = `<div class="result-card" style="border-color: #dc3545;"><strong>❌ Produto ${codigo} não encontrado no cadastro</strong></div>`;
                    log(`❌ Produto ${codigo} não encontrado`, 'error');
                    return;
                }

                log(`✅ Produto encontrado: ${produto.descricao}`, 'success');

                // Buscar na qualidade
                log('Verificando na qualidade...', 'info');
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const itemQualidade = qualidade.find(q => q.produtoId === produto.id);

                // Buscar no estoque
                log('Verificando no estoque...', 'info');
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const itemEstoque = estoques.find(e => e.produtoId === produto.id);

                // Montar resultado detalhado
                let html = `
                    <div class="result-card">
                        <h4>📦 ${codigo} - ${produto.descricao}</h4>
                        <p><strong>ID do Produto:</strong> ${produto.id}</p>
                        <p><strong>Unidade:</strong> ${produto.unidade || 'N/A'}</p>
                        <hr>
                `;

                // Status na qualidade
                if (itemQualidade) {
                    const saldoQualidade = itemQualidade.saldo;
                    const statusQualidade = itemQualidade.status || 'PENDENTE';
                    const dataEntrada = itemQualidade.dataEntrada ? 
                        new Date(itemQualidade.dataEntrada.seconds * 1000).toLocaleString() : 'N/A';

                    html += `
                        <p><strong>🔍 STATUS NA QUALIDADE:</strong></p>
                        <ul>
                            <li><strong>Saldo:</strong> ${saldoQualidade === undefined ? '❌ UNDEFINED' : saldoQualidade + ' ' + (produto.unidade || 'UN')}</li>
                            <li><strong>Status:</strong> ${statusQualidade}</li>
                            <li><strong>Data Entrada:</strong> ${dataEntrada}</li>
                            <li><strong>ID Registro:</strong> ${itemQualidade.id}</li>
                        </ul>
                    `;
                } else {
                    html += `<p><strong>🔍 Na Qualidade:</strong> ❌ Não encontrado</p>`;
                }

                // Status no estoque
                if (itemEstoque) {
                    const saldoEstoque = itemEstoque.saldo;
                    const armazem = itemEstoque.armazemId || 'N/A';
                    const ultimaMovimentacao = itemEstoque.ultimaMovimentacao ? 
                        new Date(itemEstoque.ultimaMovimentacao.seconds * 1000).toLocaleString() : 'N/A';

                    html += `
                        <p><strong>📦 STATUS NO ESTOQUE:</strong></p>
                        <ul>
                            <li><strong>Saldo:</strong> ${saldoEstoque === undefined ? '❌ UNDEFINED' : saldoEstoque + ' ' + (produto.unidade || 'UN')}</li>
                            <li><strong>Armazém:</strong> ${armazem}</li>
                            <li><strong>Última Movimentação:</strong> ${ultimaMovimentacao}</li>
                            <li><strong>ID Registro:</strong> ${itemEstoque.id}</li>
                        </ul>
                    `;
                } else {
                    html += `<p><strong>📦 No Estoque:</strong> ❌ Não encontrado</p>`;
                }

                // Diagnóstico e recomendações
                html += `<hr><p><strong>🩺 DIAGNÓSTICO:</strong></p><ul>`;
                
                if (itemQualidade && (itemQualidade.saldo === undefined || itemQualidade.saldo === null)) {
                    html += `<li style="color: #dc3545;">❌ <strong>PROBLEMA:</strong> Saldo na qualidade está undefined</li>`;
                    html += `<li style="color: #ffc107;">💡 <strong>SOLUÇÃO:</strong> Use a ferramenta de correção de dados</li>`;
                }
                
                if (itemEstoque && itemEstoque.saldo === 0 && itemQualidade && itemQualidade.saldo > 0) {
                    html += `<li style="color: #ffc107;">⚠️ Item aprovado na qualidade mas não transferido</li>`;
                    html += `<li style="color: #ffc107;">💡 <strong>SOLUÇÃO:</strong> Execute a aprovação novamente</li>`;
                }
                
                if (!itemQualidade && !itemEstoque) {
                    html += `<li style="color: #dc3545;">❌ Item não encontrado em lugar nenhum</li>`;
                }
                
                if (itemEstoque && itemEstoque.saldo > 0) {
                    html += `<li style="color: #28a745;">✅ Item disponível no estoque (${itemEstoque.saldo} ${produto.unidade})</li>`;
                }

                if (itemQualidade && itemQualidade.status === 'PENDENTE' && itemQualidade.saldo > 0) {
                    html += `<li style="color: #007bff;">📋 <strong>AÇÃO:</strong> Item pode ser aprovado na qualidade</li>`;
                }

                html += `</ul></div>`;
                resultDiv.innerHTML = html;

                log(`✅ Investigação do item ${codigo} concluída`, 'success');

            } catch (error) {
                log(`❌ Erro na investigação: ${error.message}`, 'error');
                resultDiv.innerHTML = `<div class="result-card" style="border-color: #dc3545;"><strong>❌ Erro: ${error.message}</strong></div>`;
            }
        };

        window.testarConexao = async function() {
            log('🔗 Testando conexão...', 'info');
            try {
                const testSnap = await getDocs(query(collection(db, "produtos"), limit(1)));
                setResult('conexao', 'success', 'Conexão OK');
                log('✅ Conexão estabelecida', 'success');
            } catch (error) {
                setResult('conexao', 'error', `Erro: ${error.message}`);
                log(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        };

        window.verificarQualidade = async function() {
            log('✅ Verificando qualidade...', 'info');
            try {
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                let undefinedCount = 0;

                qualidade.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) {
                        problemas++;
                        undefinedCount++;
                    }
                    if (!item.produtoId) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'error';
                setResult('qualidade', status, `${qualidade.length} itens, ${undefinedCount} undefined`);
                log(`✅ ${qualidade.length} itens verificados, ${undefinedCount} com saldo undefined`, undefinedCount > 0 ? 'warning' : 'success');
            } catch (error) {
                setResult('qualidade', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.verificarEstoques = async function() {
            log('🏪 Verificando estoques...', 'info');
            try {
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                let undefinedCount = 0;
                let negativos = 0;

                estoques.forEach(estoque => {
                    if (estoque.saldo === undefined || estoque.saldo === null) {
                        problemas++;
                        undefinedCount++;
                    }
                    if (estoque.saldo < 0) {
                        problemas++;
                        negativos++;
                    }
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setResult('estoques', status, `${estoques.length} registros, ${undefinedCount} undefined, ${negativos} negativos`);
                log(`🏪 ${estoques.length} estoques verificados`, 'success');
            } catch (error) {
                setResult('estoques', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.buscarProblemas = async function() {
            log('⚠️ Buscando problemas gerais...', 'info');
            try {
                const [produtosSnap, estoquesSnap, qualidadeSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "estoqueQualidade"))
                ]);

                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;

                // Produtos sem código
                produtos.forEach(produto => {
                    if (!produto.codigo || produto.codigo.trim() === '') problemas++;
                });

                // Saldos negativos
                estoques.forEach(estoque => {
                    if (estoque.saldo < 0) problemas++;
                });

                // Saldos undefined
                [...estoques, ...qualidade].forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) problemas++;
                });

                const status = problemas === 0 ? 'success' : problemas < 10 ? 'warning' : 'error';
                setResult('problemas', status, `${problemas} problemas encontrados`);
                log(`⚠️ ${problemas} problemas encontrados`, problemas > 0 ? 'warning' : 'success');
            } catch (error) {
                setResult('problemas', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.verificarSaldosUndefined = async function() {
            log('❓ Verificando saldos undefined...', 'info');
            try {
                const [estoquesSnap, qualidadeSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "estoqueQualidade"))
                ]);

                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let undefinedEstoque = 0;
                let undefinedQualidade = 0;

                estoques.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) undefinedEstoque++;
                });

                qualidade.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) undefinedQualidade++;
                });

                const total = undefinedEstoque + undefinedQualidade;
                log(`❓ Encontrados ${undefinedEstoque} saldos undefined no estoque`, undefinedEstoque > 0 ? 'warning' : 'success');
                log(`❓ Encontrados ${undefinedQualidade} saldos undefined na qualidade`, undefinedQualidade > 0 ? 'warning' : 'success');
                log(`❓ Total: ${total} saldos undefined`, total > 0 ? 'warning' : 'success');

                document.getElementById('statusGeral').textContent = `${total} saldos undefined encontrados`;

            } catch (error) {
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.diagnosticoCompleto = async function() {
            log('🩺 Iniciando diagnóstico completo...', 'info');
            document.getElementById('statusGeral').textContent = 'Executando diagnóstico...';

            await testarConexao();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarQualidade();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarEstoques();
            await new Promise(resolve => setTimeout(resolve, 500));
            await buscarProblemas();
            await new Promise(resolve => setTimeout(resolve, 500));
            await investigarItem(); // Busca automática do 101133

            document.getElementById('statusGeral').textContent = 'Diagnóstico concluído';
            log('✅ Diagnóstico completo finalizado', 'success');
        };

        window.limparLog = function() {
            document.getElementById('logArea').innerHTML = '';
            log('🗑️ Log limpo', 'info');
        };
    </script>
</body>
</html>
