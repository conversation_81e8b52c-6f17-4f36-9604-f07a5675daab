<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FYRON MRP - Gestão de Compras (Demo)</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            border-bottom: 3px solid #ecf0f1;
            margin-bottom: 30px;
            overflow-x: auto;
        }

        .tab {
            padding: 15px 25px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #7f8c8d;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tab.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            transform: translateY(-2px);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .filters {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-cotacao {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .status-cotado {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .status-comprado {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .priority {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .priority-alta {
            background: #ffebee;
            color: #c62828;
        }

        .priority-media {
            background: #fff3e0;
            color: #ef6c00;
        }

        .priority-baixa {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 1000px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease;
            max-height: 90vh;
            overflow-y: auto;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close:hover {
            transform: scale(1.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.pendentes {
            border-left-color: #f39c12;
        }

        .stat-card.aprovadas {
            border-left-color: #27ae60;
        }

        .stat-card.cotacao {
            border-left-color: #6c757d;
        }

        .stat-card.cotadas {
            border-left-color: #17a2b8;
        }

        .stat-card.total {
            border-left-color: #3498db;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .cotacao-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .fornecedor-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .fornecedor-card:hover {
            border-color: #3498db;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .fornecedor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .fornecedor-nome {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .cotacao-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .cotacao-enviada {
            background: #fff3cd;
            color: #856404;
        }

        .cotacao-respondida {
            background: #d4edda;
            color: #155724;
        }

        .cotacao-vencedora {
            background: #cce5ff;
            color: #004085;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .cotacao-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .link-cotacao {
            background: #e9ecef;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }

        /* Estilos do Banner de Demonstração */
        .demo-banner {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px 25px;
            margin: -20px -20px 0 -20px;
            position: relative;
            overflow: hidden;
        }

        .demo-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="demo-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23demo-pattern)"/></svg>');
            opacity: 0.3;
        }

        .demo-content {
            display: flex;
            align-items: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .demo-icon {
            font-size: 32px;
            color: #ffd700;
        }

        .demo-text h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 700;
        }

        .demo-text p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .demo-actions {
            margin-left: auto;
            display: flex;
            gap: 10px;
        }

        .btn-demo-switch {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-demo-switch:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-demo-info {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-demo-info:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .demo-label {
            background: #ff6b6b;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 700;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Modal de Informações da Demo */
        .demo-info-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
        }

        .demo-info-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        .demo-info-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .demo-info-body {
            padding: 25px;
        }

        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .demo-feature {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }

        .demo-feature h4 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 16px;
        }

        .demo-feature p {
            color: #7f8c8d;
            margin: 0;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }

            .table-container {
                overflow-x: auto;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Banner de Demonstração -->
        <div class="demo-banner">
            <div class="demo-content">
                <div class="demo-icon">
                    <i class="fas fa-theater-masks"></i>
                </div>
                <div class="demo-text">
                    <h3>🎭 MODO DEMONSTRAÇÃO</h3>
                    <p>Esta é uma versão de demonstração com dados simulados para apresentação e treinamento.</p>
                </div>
                <div class="demo-actions">
                    <button class="btn-demo-switch" onclick="switchToProduction()">
                        <i class="fas fa-rocket"></i>
                        Ir para Sistema Real
                    </button>
                    <button class="btn-demo-info" onclick="showDemoInfo()">
                        <i class="fas fa-info-circle"></i>
                        Sobre Demo
                    </button>
                </div>
            </div>
        </div>

        <div class="header">
            <h1>
                <i class="fas fa-shopping-cart"></i>
                FYRON MRP - Gestão de Compras <span class="demo-label">DEMO</span>
            </h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="openModal('modalSolicitacao')">
                    <i class="fas fa-plus"></i>
                    Nova Solicitação
                </button>
                <button class="btn btn-warning" onclick="executarMRP()">
                    <i class="fas fa-cogs"></i>
                    Executar MRP
                </button>
                <button class="btn btn-primary" onclick="gerarRelatorio()">
                    <i class="fas fa-chart-bar"></i>
                    Relatórios
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card pendentes">
                    <div class="stat-number" id="statPendentes">8</div>
                    <div class="stat-label">Solicitações Pendentes</div>
                </div>
                <div class="stat-card aprovadas">
                    <div class="stat-number" id="statAprovadas">5</div>
                    <div class="stat-label">Aprovadas</div>
                </div>
                <div class="stat-card cotacao">
                    <div class="stat-number" id="statCotacao">3</div>
                    <div class="stat-label">Em Cotação</div>
                </div>
                <div class="stat-card cotadas">
                    <div class="stat-number" id="statCotadas">7</div>
                    <div class="stat-label">Cotadas</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-number" id="statTotal">23</div>
                    <div class="stat-label">Total do Mês</div>
                </div>
            </div>

            <!-- Abas -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('solicitacoes')">
                    <i class="fas fa-list"></i>
                    Solicitações
                </button>
                <button class="tab" onclick="showTab('aprovacao')">
                    <i class="fas fa-check-circle"></i>
                    Aprovação
                </button>
                <button class="tab" onclick="showTab('cotacao')">
                    <i class="fas fa-handshake"></i>
                    Cotação
                </button>
                <button class="tab" onclick="showTab('analise')">
                    <i class="fas fa-chart-line"></i>
                    Análise Cotações
                </button>
                <button class="tab" onclick="showTab('pedidos')">
                    <i class="fas fa-file-invoice"></i>
                    Pedidos
                </button>
                <button class="tab" onclick="showTab('mrp')">
                    <i class="fas fa-calculator"></i>
                    MRP
                </button>
            </div>

            <!-- Aba Solicitações -->
            <div id="solicitacoes" class="tab-content active">
                <div class="filters">
                    <h3 style="margin-bottom: 20px; color: #2c3e50;">
                        <i class="fas fa-filter"></i>
                        Filtros de Pesquisa
                    </h3>
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Número da Solicitação</label>
                            <input type="text" class="form-control" placeholder="Ex: SC001">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select class="form-control">
                                <option value="">Todos</option>
                                <option value="pendente">Pendente</option>
                                <option value="aprovado">Aprovado</option>
                                <option value="cotacao">Em Cotação</option>
                                <option value="cotado">Cotado</option>
                                <option value="comprado">Comprado</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Solicitante</label>
                            <input type="text" class="form-control" placeholder="Nome do solicitante">
                        </div>
                        <div class="form-group">
                            <label>Data Inicial</label>
                            <input type="date" class="form-control">
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            Pesquisar
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Data</th>
                                <th>Solicitante</th>
                                <th>Descrição</th>
                                <th>Valor Total</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>SC001</strong></td>
                                <td>15/01/2025</td>
                                <td>João Silva</td>
                                <td>Material de escritório</td>
                                <td>R$ 1.250,00</td>
                                <td><span class="status status-pendente">Pendente</span></td>
                                <td class="actions">
                                    <button class="btn btn-primary btn-sm" onclick="visualizarSolicitacao('SC001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="editarSolicitacao('SC001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>SC002</strong></td>
                                <td>14/01/2025</td>
                                <td>Maria Santos</td>
                                <td>Equipamentos de TI</td>
                                <td>R$ 5.800,00</td>
                                <td><span class="status status-cotacao">Em Cotação</span></td>
                                <td class="actions">
                                    <button class="btn btn-primary btn-sm" onclick="visualizarSolicitacao('SC002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-info btn-sm" onclick="acompanharCotacao('SC002')">
                                        <i class="fas fa-handshake"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>SC003</strong></td>
                                <td>13/01/2025</td>
                                <td>Pedro Costa</td>
                                <td>Matéria-prima produção</td>
                                <td>R$ 12.500,00</td>
                                <td><span class="status status-cotado">Cotado</span></td>
                                <td class="actions">
                                    <button class="btn btn-primary btn-sm" onclick="visualizarSolicitacao('SC003')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="analisarCotacoes('SC003')">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Aba Aprovação -->
            <div id="aprovacao" class="tab-content">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Atenção:</strong> Existem 3 solicitações aguardando aprovação.
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Data</th>
                                <th>Solicitante</th>
                                <th>Valor Total</th>
                                <th>Justificativa</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>SC001</strong></td>
                                <td>15/01/2025</td>
                                <td>João Silva</td>
                                <td>R$ 1.250,00</td>
                                <td>Reposição de estoque</td>
                                <td class="actions">
                                    <button class="btn btn-success btn-sm" onclick="aprovarSolicitacao('SC001')">
                                        <i class="fas fa-check"></i>
                                        Aprovar
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="rejeitarSolicitacao('SC001')">
                                        <i class="fas fa-times"></i>
                                        Rejeitar
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Aba Cotação -->
            <div id="cotacao" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Solicitações aprovadas são automaticamente enviadas para cotação com fornecedores cadastrados.
                </div>

                <div class="cotacao-section">
                    <h3 style="margin-bottom: 20px; color: #2c3e50;">
                        <i class="fas fa-handshake"></i>
                        Solicitação SC002 - Equipamentos de TI
                    </h3>
                    
                    <div class="cotacao-grid">
                        <div class="fornecedor-card">
                            <div class="fornecedor-header">
                                <div class="fornecedor-nome">TechSupply Ltda</div>
                                <span class="cotacao-status cotacao-respondida">Respondida</span>
                            </div>
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>Enviado em:</strong> 14/01/2025 09:30</p>
                            <p><strong>Respondido em:</strong> 15/01/2025 14:20</p>
                            <div class="link-cotacao">
                                Link: https://compras.empresa.com/cotacao/SC002/fornecedor1
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="visualizarCotacao('SC002', 'TechSupply')">
                                <i class="fas fa-eye"></i>
                                Ver Cotação
                            </button>
                        </div>

                        <div class="fornecedor-card">
                            <div class="fornecedor-header">
                                <div class="fornecedor-nome">InfoTech Solutions</div>
                                <span class="cotacao-status cotacao-respondida">Respondida</span>
                            </div>
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>Enviado em:</strong> 14/01/2025 09:30</p>
                            <p><strong>Respondido em:</strong> 15/01/2025 16:45</p>
                            <div class="link-cotacao">
                                Link: https://compras.empresa.com/cotacao/SC002/fornecedor2
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="visualizarCotacao('SC002', 'InfoTech')">
                                <i class="fas fa-eye"></i>
                                Ver Cotação
                            </button>
                        </div>

                        <div class="fornecedor-card">
                            <div class="fornecedor-header">
                                <div class="fornecedor-nome">Digital Store</div>
                                <span class="cotacao-status cotacao-enviada">Aguardando</span>
                            </div>
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>Enviado em:</strong> 14/01/2025 09:30</p>
                            <p><strong>Prazo:</strong> 16/01/2025</p>
                            <div class="link-cotacao">
                                Link: https://compras.empresa.com/cotacao/SC002/fornecedor3
                            </div>
                            <button class="btn btn-warning btn-sm" onclick="reenviarCotacao('SC002', 'Digital Store')">
                                <i class="fas fa-redo"></i>
                                Reenviar
                            </button>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: right;">
                        <button class="btn btn-success" onclick="analisarCotacoes('SC002')">
                            <i class="fas fa-chart-line"></i>
                            Analisar Cotações
                        </button>
                    </div>
                </div>
            </div>

            <!-- Aba Análise de Cotações -->
            <div id="analise" class="tab-content">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    Análise comparativa das cotações recebidas para tomada de decisão.
                </div>

                <div class="cotacao-section">
                    <h3 style="margin-bottom: 20px; color: #2c3e50;">
                        <i class="fas fa-chart-line"></i>
                        Análise SC003 - Matéria-prima produção
                    </h3>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Fornecedor A<br><small>Industrial Ltda</small></th>
                                    <th>Fornecedor B<br><small>Suprimentos SA</small></th>
                                    <th>Fornecedor C<br><small>MatPrima Corp</small></th>
                                    <th>Melhor Oferta</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Aço Inox 304</strong><br>100kg</td>
                                    <td>R$ 2.500,00<br><small>Prazo: 10 dias</small></td>
                                    <td style="background: #d4edda;">R$ 2.200,00<br><small>Prazo: 7 dias</small></td>
                                    <td>R$ 2.450,00<br><small>Prazo: 12 dias</small></td>
                                    <td>
                                        <button class="btn btn-success btn-sm" onclick="selecionarMelhorOferta('SC003', 'item1', 'B')">
                                            <i class="fas fa-trophy"></i>
                                            Fornecedor B
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Parafusos M8</strong><br>500 unid</td>
                                    <td style="background: #d4edda;">R$ 150,00<br><small>Prazo: 5 dias</small></td>
                                    <td>R$ 180,00<br><small>Prazo: 3 dias</small></td>
                                    <td>R$ 165,00<br><small>Prazo: 7 dias</small></td>
                                    <td>
                                        <button class="btn btn-success btn-sm" onclick="selecionarMelhorOferta('SC003', 'item2', 'A')">
                                            <i class="fas fa-trophy"></i>
                                            Fornecedor A
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div style="margin-top: 20px; text-align: right;">
                        <button class="btn btn-warning" onclick="solicitarNegociacao('SC003')">
                            <i class="fas fa-comments"></i>
                            Solicitar Negociação
                        </button>
                        <button class="btn btn-success" onclick="aprovarCotacoes('SC003')">
                            <i class="fas fa-check"></i>
                            Aprovar Selecionadas
                        </button>
                    </div>
                </div>
            </div>

            <!-- Aba Pedidos -->
            <div id="pedidos" class="tab-content">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Número PC</th>
                                <th>SC Origem</th>
                                <th>Fornecedor</th>
                                <th>Data Pedido</th>
                                <th>Valor Total</th>
                                <th>Status</th>
                                <th>Previsão Entrega</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>PC001</strong></td>
                                <td>SC003</td>
                                <td>Suprimentos SA</td>
                                <td>16/01/2025</td>
                                <td>R$ 2.200,00</td>
                                <td><span class="status status-aprovado">Enviado</span></td>
                                <td>23/01/2025</td>
                                <td class="actions">
                                    <button class="btn btn-primary btn-sm" onclick="acompanharPedido('PC001')">
                                        <i class="fas fa-truck"></i>
                                        Rastrear
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="imprimirPedido('PC001')">
                                        <i class="fas fa-print"></i>
                                        Imprimir
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Aba MRP -->
            <div id="mrp" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    O MRP analisa automaticamente o estoque, demanda e lead times para gerar solicitações de compra otimizadas.
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Estoque Atual</th>
                                <th>Estoque Mínimo</th>
                                <th>Demanda Prevista</th>
                                <th>Necessidade</th>
                                <th>Lead Time</th>
                                <th>Data Sugerida</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Papel A4</strong></td>
                                <td>50 unid</td>
                                <td>100 unid</td>
                                <td>200 unid</td>
                                <td style="color: #e74c3c; font-weight: bold;">250 unid</td>
                                <td>5 dias</td>
                                <td>20/01/2025</td>
                                <td>
                                    <button class="btn btn-success btn-sm" onclick="gerarSolicitacaoMRP('Papel A4')">
                                        <i class="fas fa-plus"></i>
                                        Gerar SC
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nova Solicitação -->
    <div id="modalSolicitacao" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-plus"></i> Nova Solicitação de Compra</h2>
                <span class="close" onclick="closeModal('modalSolicitacao')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="formSolicitacao">
                    <div class="form-row">
                        <div class="form-group">
                            <label>Solicitante *</label>
                            <input type="text" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>Centro de Custo *</label>
                            <select class="form-control" required>
                                <option value="">Selecione...</option>
                                <option>Administrativo</option>
                                <option>Produção</option>
                                <option>Vendas</option>
                                <option>TI</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Data Necessidade *</label>
                            <input type="date" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>Prioridade *</label>
                            <select class="form-control" required>
                                <option value="">Selecione...</option>
                                <option value="alta">Alta</option>
                                <option value="media">Média</option>
                                <option value="baixa">Baixa</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Justificativa *</label>
                        <textarea class="form-control" rows="3" required placeholder="Descreva a necessidade da compra..."></textarea>
                    </div>

                    <h4 style="margin: 25px 0 15px 0; color: #2c3e50;">Itens da Solicitação</h4>
                    
                    <div id="itensContainer">
                        <div class="item-row" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 15px; align-items: end; margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <div class="form-group">
                                <label>Produto/Serviço</label>
                                <input type="text" class="form-control" placeholder="Descrição do item">
                            </div>
                            <div class="form-group">
                                <label>Quantidade</label>
                                <input type="number" class="form-control" placeholder="Qtd">
                            </div>
                            <div class="form-group">
                                <label>Valor Unit.</label>
                                <input type="number" class="form-control" placeholder="0,00" step="0.01">
                            </div>
                            <div class="form-group">
                                <label>Total</label>
                                <input type="number" class="form-control" placeholder="0,00" readonly>
                            </div>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removerItem(this)" style="height: 40px;">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <button type="button" class="btn btn-success" onclick="adicionarItem()">
                            <i class="fas fa-plus"></i>
                            Adicionar Item
                        </button>
                    </div>

                    <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('modalSolicitacao')" style="background: #6c757d; color: white; margin-right: 10px;">
                            <i class="fas fa-times"></i>
                            Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i>
                            Salvar Solicitação
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Visualizar Cotação -->
    <div id="modalCotacao" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-handshake"></i> Detalhes da Cotação</h2>
                <span class="close" onclick="closeModal('modalCotacao')">&times;</span>
            </div>
            <div class="modal-body">
                <div id="cotacaoDetalhes">
                    <!-- Conteúdo será preenchido dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Informações da Demo -->
    <div id="demoInfoModal" class="demo-info-modal">
        <div class="demo-info-content">
            <div class="demo-info-header">
                <h2><i class="fas fa-info-circle"></i> Sobre o Modo Demonstração</h2>
                <span class="close" onclick="closeDemoInfo()">&times;</span>
            </div>
            <div class="demo-info-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Atenção:</strong> Esta é uma versão de demonstração. Nenhuma ação realizada aqui afetará dados reais.
                </div>

                <h3>🎯 Propósito desta Demonstração</h3>
                <p>Esta interface foi criada para:</p>
                <ul>
                    <li><strong>Apresentações comerciais</strong> - Mostrar as capacidades do sistema</li>
                    <li><strong>Treinamento de usuários</strong> - Aprender sem riscos</li>
                    <li><strong>Testes de interface</strong> - Validar usabilidade</li>
                    <li><strong>Desenvolvimento</strong> - Prototipar novas funcionalidades</li>
                </ul>

                <div class="demo-features">
                    <div class="demo-feature">
                        <h4>📊 Dados Simulados</h4>
                        <p>Todas as informações são fictícias e atualizadas automaticamente para demonstração.</p>
                    </div>
                    <div class="demo-feature">
                        <h4>🔄 Fluxo Completo</h4>
                        <p>Demonstra todo o processo de compras: solicitação → aprovação → cotação → pedido.</p>
                    </div>
                    <div class="demo-feature">
                        <h4>🎨 Interface Real</h4>
                        <p>Design idêntico ao sistema de produção para experiência autêntica.</p>
                    </div>
                    <div class="demo-feature">
                        <h4>🚀 Sistema Real</h4>
                        <p>Para operações reais, use o sistema integrado com dados persistentes.</p>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                    <button class="btn btn-primary" onclick="switchToProduction()">
                        <i class="fas fa-rocket"></i>
                        Acessar Sistema Real
                    </button>
                    <button class="btn btn-secondary" onclick="closeDemoInfo()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i>
                        Continuar Demo
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Controle de abas
        function showTab(tabName) {
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));
            
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Controle de modais
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Adicionar item na solicitação
        function adicionarItem() {
            const container = document.getElementById('itensContainer');
            const itemRow = document.createElement('div');
            itemRow.className = 'item-row';
            itemRow.style = 'display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 15px; align-items: end; margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;';
            
            itemRow.innerHTML = `
                <div class="form-group">
                    <label>Produto/Serviço</label>
                    <input type="text" class="form-control" placeholder="Descrição do item">
                </div>
                <div class="form-group">
                    <label>Quantidade</label>
                    <input type="number" class="form-control" placeholder="Qtd">
                </div>
                <div class="form-group">
                    <label>Valor Unit.</label>
                    <input type="number" class="form-control" placeholder="0,00" step="0.01">
                </div>
                <div class="form-group">
                    <label>Total</label>
                    <input type="number" class="form-control" placeholder="0,00" readonly>
                </div>
                <button type="button" class="btn btn-danger btn-sm" onclick="removerItem(this)" style="height: 40px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            
            container.appendChild(itemRow);
        }

        function removerItem(button) {
            button.closest('.item-row').remove();
        }

        // Aprovar solicitação e enviar para cotação
        function aprovarSolicitacao(numero) {
            if(confirm(`Aprovar a solicitação ${numero} e enviar para cotação?`)) {
                alert(`Solicitação ${numero} aprovada!\n\nEnviando automaticamente para cotação com fornecedores cadastrados:\n- TechSupply Ltda\n- InfoTech Solutions\n- Digital Store\n\nLinks de cotação enviados por email.`);
                // Aqui você atualizaria o status para "Em Cotação"
            }
        }

        function rejeitarSolicitacao(numero) {
            const motivo = prompt(`Motivo da rejeição da solicitação ${numero}:`);
            if(motivo) {
                alert(`Solicitação ${numero} rejeitada.\nMotivo: ${motivo}`);
            }
        }

        // Acompanhar cotação
        function acompanharCotacao(numero) {
            alert(`Status da cotação ${numero}:\n\n✅ TechSupply Ltda - Respondida\n✅ InfoTech Solutions - Respondida\n⏳ Digital Store - Aguardando\n\nPrazo final: 16/01/2025`);
        }

        // Visualizar cotação específica
        function visualizarCotacao(sc, fornecedor) {
            const detalhes = `
                <h4>Cotação ${sc} - ${fornecedor}</h4>
                <div class="table-container" style="margin-top: 20px;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Qtd</th>
                                <th>Valor Unit.</th>
                                <th>Total</th>
                                <th>Prazo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Notebook Dell Inspiron</td>
                                <td>2</td>
                                <td>R$ 2.500,00</td>
                                <td>R$ 5.000,00</td>
                                <td>7 dias</td>
                            </tr>
                            <tr>
                                <td>Mouse Wireless</td>
                                <td>2</td>
                                <td>R$ 80,00</td>
                                <td>R$ 160,00</td>
                                <td>3 dias</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p style="margin-top: 15px;"><strong>Total Geral:</strong> R$ 5.160,00</p>
                <p><strong>Condições:</strong> Pagamento em 30 dias</p>
                <p><strong>Observações:</strong> Garantia de 12 meses</p>
            `;
            
            document.getElementById('cotacaoDetalhes').innerHTML = detalhes;
            openModal('modalCotacao');
        }

        // Reenviar cotação
        function reenviarCotacao(sc, fornecedor) {
            if(confirm(`Reenviar link de cotação para ${fornecedor}?`)) {
                alert(`Link reenviado para ${fornecedor}!\n\nNovo prazo: 18/01/2025`);
            }
        }

        // Analisar cotações
        function analisarCotacoes(numero) {
            alert(`Redirecionando para análise comparativa da ${numero}...`);
            showTab('analise');
        }

        // Selecionar melhor oferta
        function selecionarMelhorOferta(sc, item, fornecedor) {
            if(confirm(`Selecionar Fornecedor ${fornecedor} como vencedor para este item?`)) {
                alert(`Fornecedor ${fornecedor} selecionado para o item!\n\nItem marcado como vencedor.`);
            }
        }

        // Solicitar negociação
        function solicitarNegociacao(numero) {
            const observacao = prompt(`Observações para negociação da ${numero}:`);
            if(observacao) {
                alert(`Solicitação de negociação enviada!\n\nObservação: ${observacao}\n\nFornecedores serão notificados.`);
            }
        }

        // Aprovar cotações selecionadas
        function aprovarCotacoes(numero) {
            if(confirm(`Aprovar as cotações selecionadas e gerar pedidos de compra?`)) {
                alert(`Cotações aprovadas!\n\nPedidos de compra gerados:\n- PC001 - Suprimentos SA\n- PC002 - Industrial Ltda\n\nTotal: R$ 2.650,00`);
            }
        }

        // Executar MRP
        function executarMRP() {
            alert('Executando análise MRP...\n\nProcessando:\n- Níveis de estoque\n- Demanda prevista\n- Lead times\n- Pontos de reposição\n\nResultados atualizados na aba MRP!');
        }

        // Gerar solicitação via MRP
        function gerarSolicitacaoMRP(produto) {
            if(confirm(`Gerar solicitação de compra para ${produto}?`)) {
                alert(`Solicitação gerada automaticamente para ${produto}!\nNúmero: SC${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}\n\nEnviada automaticamente para aprovação.`);
            }
        }

        // Outras funções
        function visualizarSolicitacao(numero) {
            alert(`Abrindo detalhes da solicitação ${numero}...`);
        }

        function editarSolicitacao(numero) {
            alert(`Abrindo editor para solicitação ${numero}...`);
        }

        function acompanharPedido(numero) {
            alert(`Rastreamento do pedido ${numero}:\n\nStatus: Enviado\nCódigo rastreio: BR123456789\nPrevisão entrega: 23/01/2025`);
        }

        function imprimirPedido(numero) {
            alert(`Imprimindo pedido ${numero}...`);
        }

        function gerarRelatorio() {
            alert('Gerando relatório de compras...\n\nRelatórios disponíveis:\n- Solicitações por período\n- Análise de fornecedores\n- Controle orçamentário\n- Performance de compras');
        }

        // Submissão do formulário
        document.getElementById('formSolicitacao').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Solicitação de compra salva com sucesso!\nNúmero: SC' + String(Math.floor(Math.random() * 1000)).padStart(3, '0') + '\n\nEnviada para aprovação.');
            closeModal('modalSolicitacao');
        });

        // Atualizar estatísticas
        function atualizarEstatisticas() {
            document.getElementById('statPendentes').textContent = Math.floor(Math.random() * 15) + 5;
            document.getElementById('statAprovadas').textContent = Math.floor(Math.random() * 10) + 3;
            document.getElementById('statCotacao').textContent = Math.floor(Math.random() * 8) + 2;
            document.getElementById('statCotadas').textContent = Math.floor(Math.random() * 12) + 4;
            document.getElementById('statTotal').textContent = Math.floor(Math.random() * 50) + 20;
        }

        setInterval(atualizarEstatisticas, 30000);

        // Funções específicas da demonstração
        function switchToProduction() {
            if(confirm('🚀 Deseja acessar o sistema real de gestão de compras?\n\nNo sistema real você poderá:\n✅ Salvar dados permanentemente\n✅ Integrar com outros módulos\n✅ Gerar relatórios reais\n✅ Gerenciar operações de produção')) {
                window.location.href = 'gestao_compras_integrada.html';
            }
        }

        function showDemoInfo() {
            document.getElementById('demoInfoModal').style.display = 'block';
        }

        function closeDemoInfo() {
            document.getElementById('demoInfoModal').style.display = 'none';
        }

        // Melhorar alertas da demo com informações mais detalhadas
        function aprovarSolicitacao(numero) {
            if(confirm(`🎭 MODO DEMO: Aprovar a solicitação ${numero}?\n\n⚠️ Esta é uma simulação - nenhum dado real será alterado.`)) {
                showDemoNotification(`✅ Solicitação ${numero} aprovada!\n\n📧 Emails enviados automaticamente para:\n• TechSupply Ltda\n• InfoTech Solutions\n• Digital Store\n\n🔗 Links de cotação gerados\n⏰ Prazo: 3 dias úteis`, 'success');
            }
        }

        function executarMRP() {
            showDemoNotification('🎭 DEMO: Executando análise MRP...\n\n🔍 Analisando:\n• Níveis de estoque atual\n• Demanda prevista (próximos 30 dias)\n• Lead times dos fornecedores\n• Pontos de reposição\n\n📊 Resultados atualizados na aba MRP!\n\n⚠️ Simulação - dados não persistem', 'info');
        }

        function gerarRelatorio() {
            showDemoNotification('🎭 DEMO: Gerando relatórios...\n\n📈 Relatórios disponíveis:\n• Solicitações por período\n• Performance de fornecedores\n• Análise de custos\n• Tempo médio de aprovação\n• Eficiência do processo\n\n⚠️ Em modo demo - dados simulados', 'info');
        }

        // Função para mostrar notificações da demo
        function showDemoNotification(message, type = 'info') {
            // Remover notificação existente
            const existing = document.querySelector('.demo-notification');
            if (existing) existing.remove();

            const notification = document.createElement('div');
            notification.className = `demo-notification demo-notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 20px 25px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 2000;
                max-width: 400px;
                font-weight: 500;
                line-height: 1.4;
                animation: slideInRight 0.3s ease;
                white-space: pre-line;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 10px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}" style="font-size: 20px; margin-top: 2px;"></i>
                    <div style="flex: 1;">
                        ${message}
                        <div style="margin-top: 15px; text-align: right;">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 5px 10px; border-radius: 5px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-times"></i> Fechar
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remover após 8 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 8000);
        }

        // Adicionar estilos de animação
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Fechar modal de demo info ao clicar fora
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('demoInfoModal');
            if (event.target === modal) {
                closeDemoInfo();
            }
        });

        // Mostrar notificação de boas-vindas
        setTimeout(() => {
            showDemoNotification('🎭 Bem-vindo ao Modo Demonstração!\n\nEste é um ambiente seguro para explorar todas as funcionalidades do sistema.\n\n💡 Clique em "Sobre Demo" para mais informações\n🚀 Use "Sistema Real" para operações de produção', 'info');
        }, 1000);
    </script>
</body>
</html>