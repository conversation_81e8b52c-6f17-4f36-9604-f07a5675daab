<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Gantt da Explosão do Processo</title>
  <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
  <style>
    body { font-family: Arial, sans-serif; background: #f7f7f7; margin: 0; padding: 0; }
    .container { max-width: 1100px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 24px; }
    h1 { color: #0854a0; }
    .input-row { display: flex; gap: 10px; margin-bottom: 20px; }
    input[type=text] { flex: 1; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    button { padding: 8px 18px; background: #0854a0; color: #fff; border: none; border-radius: 4px; font-weight: bold; cursor: pointer; }
    button:hover { background: #0a4d8c; }
    #gantt_chart { margin-top: 30px; }
    .legend { margin-top: 20px; }
    .legend span { display: inline-block; width: 18px; height: 18px; margin-right: 6px; border-radius: 3px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Gantt da Explosão do Processo</h1>
    <div class="input-row">
      <input type="text" id="codigoProduto" placeholder="Digite o código do produto final...">
      <button onclick="gerarGantt()">Gerar Gantt</button>
      <button onclick="gerarGanttInvertido()">Inverter Gantt (maior nível primeiro)</button>
    </div>
    <div style="margin-bottom: 10px;">
      <button id="btnRecuarNivel" onclick="recuarNivel()" disabled>Recuar Nível</button>
      <span id="nivelAtualSpan">Nível: 0</span>
      <button id="btnAvancarNivel" onclick="avancarNivel()">Avançar Nível</button>
    </div>
    <div id="gantt_chart" style="width:100%; height:600px;"></div>
    <div id="mensagem"></div>
  </div>
  <script type="module">
    import { db } from '/js/firebase-config.js';
    import { getDocs, collection } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let barrasGlobais = [];
    let maxNivelGlobal = 0;
    let modoInvertido = false;
    let nivelAtual = 0;

    window.gerarGantt = async function() {
      modoInvertido = false;
      nivelAtual = 0;
      await gerarGanttComNivel();
    };
    window.gerarGanttInvertido = async function() {
      modoInvertido = true;
      nivelAtual = 0;
      await gerarGanttComNivel();
    };
    window.avancarNivel = function() {
      if (nivelAtual < maxNivelGlobal) {
        nivelAtual++;
        desenharGanttFiltrado();
      }
    };
    window.recuarNivel = function() {
      if (nivelAtual > 0) {
        nivelAtual--;
        desenharGanttFiltrado();
      }
    };
    async function gerarGanttComNivel() {
      document.getElementById('gantt_chart').innerHTML = '';
      document.getElementById('mensagem').innerHTML = '';
      document.getElementById('nivelAtualSpan').textContent = 'Nível: 0';
      document.getElementById('btnRecuarNivel').disabled = true;
      document.getElementById('btnAvancarNivel').disabled = true;
      const codigo = document.getElementById('codigoProduto').value.trim();
      if (!codigo) {
        document.getElementById('mensagem').innerHTML = '<span style="color:red">Digite o código do produto.</span>';
        return;
      }
      const [produtosSnap, estruturasSnap] = await Promise.all([
        getDocs(collection(db, 'produtos')),
        getDocs(collection(db, 'estruturas'))
      ]);
      const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const produtoRaiz = produtos.find(p => p.codigo === codigo);
      if (!produtoRaiz) {
        document.getElementById('mensagem').innerHTML = '<span style="color:red">Produto não encontrado.</span>';
        return;
      }
      barrasGlobais = [];
      if (!modoInvertido) {
        let idCounter = 1;
        function explodir(produtoId, nivel = 0, parentId = null, inicioAcumulado = 0) {
          const produto = produtos.find(p => p.id === produtoId);
          if (produto && produto.tipo === 'MP') return;
          const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
          let ultimoFim = inicioAcumulado;
          if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
            for (const oper of estrutura.operacoes) {
              const id = 'T' + (idCounter++);
              const nome = `${'&nbsp;'.repeat(nivel*4)}${produto?.codigo || produtoId} - ${oper.operacao || ''}`;
              const duracaoMin = Number(oper.tempo || 0);
              const inicio = ultimoFim;
              const fim = inicio + duracaoMin;
              barrasGlobais.push({
                id,
                nome,
                nivel,
                parentId,
                inicio,
                fim,
                duracaoMin,
                tooltip: `${produto?.descricao || ''} (${produto?.codigo || produtoId})<br>Operação: ${oper.operacao || ''}<br>Tempo: ${duracaoMin} min` 
              });
              ultimoFim = fim;
              parentId = id;
            }
          }
          if (estrutura && estrutura.componentes && estrutura.componentes.length > 0) {
            for (const comp of estrutura.componentes) {
              explodir(comp.componentId, nivel + 1, parentId, ultimoFim);
            }
          }
        }
        explodir(produtoRaiz.id, 0, null, 0);
      } else {
        function somarTempos(produtoId, nivel = 0) {
          const produto = produtos.find(p => p.id === produtoId);
          if (produto && produto.tipo === 'MP') return 0;
          const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
          let tempoTotal = 0;
          if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
            for (const oper of estrutura.operacoes) {
              tempoTotal += Number(oper.tempo || 0);
            }
          }
          if (estrutura && estrutura.componentes && estrutura.componentes.length > 0) {
            for (const comp of estrutura.componentes) {
              tempoTotal += somarTempos(comp.componentId, nivel + 1);
            }
          }
          barrasGlobais.push({
            id: produtoId,
            nome: `${' '.repeat(nivel*2)}${produto?.codigo || produtoId} - ${produto?.descricao || ''}`,
            nivel,
            tempoTotal
          });
          return tempoTotal;
        }
        somarTempos(produtoRaiz.id, 0);
      }
      maxNivelGlobal = Math.max(...barrasGlobais.map(b => b.nivel));
      desenharGanttFiltrado();
    }
    function desenharGanttFiltrado() {
      document.getElementById('nivelAtualSpan').textContent = `Nível: ${nivelAtual}`;
      document.getElementById('btnRecuarNivel').disabled = nivelAtual === 0;
      document.getElementById('btnAvancarNivel').disabled = nivelAtual === maxNivelGlobal;
      // Filtrar barras do nível atual
      let barrasFiltradas = barrasGlobais.filter(b => b.nivel === nivelAtual);
      if (barrasFiltradas.length === 0) {
        document.getElementById('gantt_chart').innerHTML = '<span style="color:orange">Nenhuma barra para este nível.</span>';
        return;
      }
      google.charts.load('current', {'packages':['gantt']});
      google.charts.setOnLoadCallback(drawChart);
      function minutesToDate(minutes) {
        const base = new Date(2024,0,1,8,0,0,0);
        return new Date(base.getTime() + minutes*60000);
      }
      function drawChart() {
        const data = new google.visualization.DataTable();
        data.addColumn('string', 'Task ID');
        data.addColumn('string', 'Task Name');
        data.addColumn('string', 'Resource');
        data.addColumn('date', 'Start Date');
        data.addColumn('date', 'End Date');
        data.addColumn('number', 'Duration');
        data.addColumn('number', 'Percent Complete');
        data.addColumn('string', 'Dependencies');
        let inicio = 0;
        for (const b of barrasFiltradas) {
          if (modoInvertido) {
            data.addRow([
              b.id,
              b.nome,
              '',
              minutesToDate(inicio),
              minutesToDate(inicio + b.tempoTotal),
              null,
              0,
              null
            ]);
            inicio += b.tempoTotal + 5;
          } else {
            data.addRow([
              b.id,
              b.nome,
              '',
              minutesToDate(b.inicio),
              minutesToDate(b.fim),
              null,
              0,
              b.parentId ? b.parentId : null
            ]);
          }
        }
        const chart = new google.visualization.Gantt(document.getElementById('gantt_chart'));
        chart.draw(data, {
          height: Math.max(400, barrasFiltradas.length*40),
          gantt: {
            labelStyle: { fontSize: 14 },
            barHeight: 28,
            trackHeight: 36
          },
          tooltip: { isHtml: true }
        });
      }
    }
  </script>
</body>
</html> 