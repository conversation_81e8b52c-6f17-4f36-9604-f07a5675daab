<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gráfico de Gantt Hierárquico</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.dhtmlx.com/gantt/8.0.6/dhtmlxgantt.css">
    <script src="https://cdn.dhtmlx.com/gantt/8.0.6/dhtmlxgantt.js"></script>

    <!-- Fallback para biblioteca Gantt -->
    <script>
        // Verificar se a biblioteca principal falhou e tentar carregar fallback
        window.addEventListener('load', function() {
            if (typeof gantt === 'undefined') {
                console.warn('⚠️ Biblioteca principal falhou, tentando fallback...');
                const script = document.createElement('script');
                script.src = 'https://cdn.dhtmlx.com/gantt/7.1.13/dhtmlxgantt.js';
                script.onload = function() {
                    console.log('✅ Biblioteca fallback carregada');
                };
                script.onerror = function() {
                    console.error('❌ Falha ao carregar biblioteca Gantt');
                    alert('Erro: Não foi possível carregar a biblioteca Gantt. Verifique sua conexão com a internet.');
                };
                document.head.appendChild(script);

                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.dhtmlx.com/gantt/7.1.13/dhtmlxgantt.css';
                document.head.appendChild(link);
            }
        });
    </script>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .gantt-container {
            padding: 20px;
        }

        /* Estilos para progresso real */
        .has-real-progress .gantt_task_progress {
            background: linear-gradient(45deg, #28a745 25%, transparent 25%, transparent 50%, #28a745 50%, #28a745 75%, transparent 75%) !important;
            background-size: 8px 8px !important;
        }

        .has-real-progress {
            border: 2px solid #28a745 !important;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.3) !important;
        }

        /* Melhorar visualização do progresso real */
        .gantt_task_progress {
            transition: all 0.3s ease;
        }

        .gantt-controls {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            display: flex;
            gap: 10px;
        }
        
        .gantt-legend {
            margin: 10px 0;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }
        
        /* Estilo para indentação visual dos níveis */
        .gantt_grid_data .gantt_row.nivel-1 .gantt_cell {
            padding-left: 25px !important;
        }
        
        .gantt_grid_data .gantt_row.nivel-2 .gantt_cell {
            padding-left: 40px !important;
        }
        
        .gantt_grid_data .gantt_row.nivel-3 .gantt_cell {
            padding-left: 55px !important;
        }
        
        .gantt_grid_data .gantt_row.nivel-4 .gantt_cell {
            padding-left: 70px !important;
        }
        
        /* Estilo para dias nu00e3o-u00fateis */
        .gantt-weekend-cell {
            background-color: #f8f8f8 !important;
        }
        
        /* Estilo para feriados */
        .gantt-holiday-cell {
            background-color: #fff0f0 !important;
        }
        
        /* Estilo para o cabeçalho */
        .gantt-scale-weekend {
            color: #999;
            font-weight: bold;
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .gantt-scale-holiday {
            color: #d43939;
            font-weight: bold;
            background-color: #fff0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }

        /* Estilos para o progresso e comparativo */
        .gantt_task_progress {
            background-color: #4CAF50; /* Verde para o progresso planejado */
            opacity: 0.6;
        }
        
        .task-real-progress {
            position: absolute;
            height: 4px;
            background-color: #FF9800; /* Laranja para o progresso real */
            border-radius: 2px;
            z-index: 100;
            top: 18px; /* Posicionado abaixo da barra principal */
        }
        
        /* Estilos para tarefas completadas */
        .task-completed {
            background-color: #A5D6A7 !important; /* Verde claro */
            border: 1px solid #4CAF50 !important;
        }
        
        /* Estilos para tarefas atrasadas */
        .task-delayed {
            background-color: #FFCDD2 !important; /* Vermelho claro */
            border: 1px solid #E57373 !important;
        }
        
        /* Estilos para o caminho crítico */
        .critical-path {
            border: 2px solid #F44336 !important; /* Vermelho */
        }
        
        /* Estilos para a legenda de progresso */
        .legenda-progresso-planejado {
            height: 10px;
            background-color: #4CAF50;
            opacity: 0.6;
            width: 50px;
            display: inline-block;
            border-radius: 2px;
        }
        
        .legenda-progresso-real {
            height: 4px;
            background-color: #FF9800;
            width: 50px;
            display: inline-block;
            margin-top: 3px;
            border-radius: 2px;
        }
        
        .task-progress {
            height: 100%;
            background-color: rgba(33, 150, 243, 0.3);
        }
        
        .task-completed {
            background-color: rgba(76, 175, 80, 0.3) !important;
        }
        
        .task-delayed {
            background-color: rgba(244, 67, 54, 0.3) !important;
        }
        
        .task-progress-line {
            height: 2px;
            background-color: #2196F3;
            position: absolute;
            top: 50%;
            margin-top: -1px;
        }
        
        .task-real-progress {
            height: 6px;
            background-color: #4CAF50;
            position: absolute;
            bottom: 0;
        }

        .critical-path {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
            border: 2px solid #c0392b !important;
            box-shadow: 0 0 10px rgba(192, 57, 43, 0.4) !important;
        }
        
        /* Estilos para o caminho crítico - MELHORADOS */
        .critical {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
            border: 3px solid #c0392b !important;
            box-shadow: 0 0 15px rgba(192, 57, 43, 0.5) !important;
            position: relative !important;
            animation: critical-pulse 2s infinite;
        }

        .critical::after {
            content: "🔥 CRÍTICO";
            position: absolute;
            top: -8px;
            right: -8px;
            background: #c0392b;
            color: white;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 3px;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* Animação para tarefas críticas */
        @keyframes critical-pulse {
            0% { box-shadow: 0 0 15px rgba(192, 57, 43, 0.5); }
            50% { box-shadow: 0 0 25px rgba(192, 57, 43, 0.8); }
            100% { box-shadow: 0 0 15px rgba(192, 57, 43, 0.5); }
        }
        
        /* Estilos para os diferentes tipos de tarefas */
        .ordem .gantt_task_content {
            background-color: #2196F3;
            color: white;
            font-weight: bold;
        }
        
        .ordem .gantt_task_progress {
            background-color: #1976D2;
        }
        
        .componente .gantt_task_content {
            background-color: #4CAF50;
            color: white;
        }
        
        .componente .gantt_task_progress {
            background-color: #388E3C;
        }
        
        .operacao .gantt_task_content {
            background-color: #FF9800;
            color: white;
        }
        
        .operacao .gantt_task_progress {
            background-color: #F57C00;
        }
        
        /* Estilos para os diferentes níveis */
        .nivel-1 .gantt_task_content {
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .nivel-2 .gantt_task_content {
            border-radius: 3px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .nivel-3 .gantt_task_content {
            border-radius: 2px;
            font-size: 0.95em;
        }
        
        .nivel-4 .gantt_task_content {
            border-radius: 2px;
            font-size: 0.9em;
        }
        
        /* Ajustes de altura das barras por nível */
        .gantt_task_line.nivel-1 {
            height: 22px;
            line-height: 22px;
        }
        
        .gantt_task_line.nivel-2 {
            height: 20px;
            line-height: 20px;
        }
        
        .gantt_task_line.nivel-3 {
            height: 18px;
            line-height: 18px;
        }
        
        .gantt_task_line.nivel-4 {
            height: 16px;
            line-height: 16px;
        }

        .gantt_task_line.ordem {
            background-color: #4b88d3;
            border-color: #3a6ca8;
        }
        
        .gantt_task_line.componente {
            background-color: #5cb85c;
            border-color: #4cae4c;
        }
        
        .gantt_task_line.operacao {
            background-color: #f0ad4e;
            border-color: #eea236;
        }
        
        /* Estilos para os diferentes níveis da estrutura */
        .gantt_task_line.nivel-1 {
            height: 24px;
            line-height: 24px;
        }
        
        .gantt_task_line.nivel-2 {
            height: 22px;
            line-height: 22px;
            margin-top: 1px;
        }
        
        .gantt_task_line.nivel-3 {
            height: 20px;
            line-height: 20px;
            margin-top: 2px;
        }
        
        .gantt_task_line.nivel-4 {
            height: 18px;
            line-height: 18px;
            margin-top: 3px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-gantt"></i> Gráfico Gantt e Caminho Crítico</h1>
            <div>
                <button class="btn-primary" onclick="window.location.href='ordens_producao.html'"><i class="fas fa-arrow-left"></i> Voltar</button>
            </div>
        </div>
        
        <div class="gantt-container">
            <div class="gantt-controls">
                <div style="display: flex; align-items: center; margin-right: 15px;">
                    <label for="opSelector" style="margin-right: 8px; font-weight: 500;">Ordem de Produção:</label>
                    <select id="opSelector" style="padding: 8px; border-radius: 4px; border: 1px solid var(--border-color); min-width: 200px;">
                        <option value="todas">Todas as OPs</option>
                        <!-- Opções serão preenchidas dinamicamente -->
                    </select>
                </div>
                <button id="expandAll" class="btn-primary"><i class="fas fa-expand"></i> Expandir Tudo</button>
                <button id="collapseAll" class="btn-primary"><i class="fas fa-compress"></i> Recolher Tudo</button>
                <button id="loadExampleData" class="btn-primary"><i class="fas fa-database"></i> Carregar Exemplo</button>
                <button id="calculateCriticalPath" class="btn-primary" style="background: #c0392b;"><i class="fas fa-fire"></i> Caminho Crítico</button>
                <button id="backToOrdens" class="btn-primary" onclick="window.location.href='ordens_producao.html'"><i class="fas fa-arrow-left"></i> Voltar</button>
            </div>
            <div class="gantt-legend">
                <!-- Legenda será preenchida dinamicamente pelo JavaScript -->
            </div>
            <div id="gantt_here" style="width:100%; height:600px;"></div>
        </div>
    </div>

    <!-- Firebase Libraries -->
    <script src="https://www.gstatic.com/firebasejs/10.7.2/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.2/firebase-firestore-compat.js"></script>
    
    <script type="module">
        // ===================================================================
        // GANTT CHART - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Disponibilizar db globalmente para compatibilidade com código legado
        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;
        window.query = query;
        window.orderBy = orderBy;
        
        // Variáveis globais para armazenar dados
        let todasOPs = [];
        let todosComponentes = [];
        let todasOperacoes = [];
        let opSelecionadaId = 'todas';
        
        // Wait for DOM to load and Gantt library to be available
        document.addEventListener('DOMContentLoaded', function() {
            // Função para inicializar o Gantt quando a biblioteca estiver disponível
            function initializeGantt() {
                // Verificar se a biblioteca Gantt está carregada
                if (typeof gantt === 'undefined') {
                    console.log('⏳ Aguardando biblioteca Gantt carregar...');
                    setTimeout(initializeGantt, 100); // Tentar novamente em 100ms
                    return;
                }

                console.log('✅ Biblioteca Gantt carregada com sucesso');
                setupGanttChart();
            }

            // Função para configurar o Gantt Chart
            function setupGanttChart() {

            // ===================================================================
            // CONFIGURAÇÃO DE PLUGINS PARA CAMINHO CRÍTICO
            // ===================================================================

            // Configurar plugins essenciais
            gantt.plugins({
                critical_path: true,        // Plugin para caminho crítico
                auto_scheduling: true,      // Agendamento automático
                tooltip: true,              // Tooltips informativos
                marker: true,               // Marcadores de data
                multiselect: true          // Seleção múltipla
            });

            // Configurar cálculo automático do caminho crítico
            gantt.config.highlight_critical_path = true;
            gantt.config.auto_scheduling = true;
            gantt.config.auto_scheduling_strict = true;
            gantt.config.auto_scheduling_compatibility = true;

            // Configurar escala de tempo
            gantt.config.scales = [
                {unit: "month", step: 1, format: "%F, %Y"},
                {unit: "day", step: 1, format: "%D, %d"},  // Mostra o nome do dia da semana
                {unit: "day", step: 1, format: function(date) {
                    // Destacar finais de semana e feriados no cabeçalho
                    let html = "";
                    
                    // Verificar se é feriado
                    let isFeriado = false;
                    feriados2025.forEach(feriado => {
                        if (date.getDate() === feriado.getDate() && 
                            date.getMonth() === feriado.getMonth() && 
                            date.getFullYear() === feriado.getFullYear()) {
                            isFeriado = true;
                        }
                    });
                    
                    // Aplicar estilo diferente para finais de semana e feriados
                    if (isFeriado) {
                        html = `<div class="gantt-scale-holiday">${gantt.date.date_to_str("%d %M")(date)}</div>`;
                    } else if (date.getDay() === 0 || date.getDay() === 6) {
                        html = `<div class="gantt-scale-weekend">${gantt.date.date_to_str("%d %M")(date)}</div>`;
                    } else {
                        html = gantt.date.date_to_str("%d %M")(date);
                    }
                    
                    return html;
                }}
            ];
            gantt.config.xml_date = "%Y-%m-%d %H:%i";
            gantt.config.scale_unit = "day";
            gantt.config.date_scale = "%d %M";
            gantt.config.duration_unit = "day";
            gantt.config.row_height = 30;
            gantt.config.min_column_width = 40;
            gantt.config.work_time = true; // Habilitar tempo de trabalho
            
            // Configurar dias de trabalho (1-7 representa segunda a domingo)
            gantt.config.work_time = true;
            gantt.setWorkTime({day: 0, hours: false}); // Domingo não é dia útil
            gantt.setWorkTime({day: 6, hours: false}); // Sábado não é dia útil
            
            // Configurar feriados nacionais de 2025 (exemplo)
            const feriados2025 = [
                new Date(2025, 0, 1),  // Ano Novo
                new Date(2025, 2, 3),  // Carnaval
                new Date(2025, 2, 4),  // Carnaval
                new Date(2025, 3, 18), // Sexta-feira Santa
                new Date(2025, 3, 21), // Tiradentes
                new Date(2025, 4, 1),  // Dia do Trabalho
                new Date(2025, 5, 19), // Corpus Christi
                new Date(2025, 8, 7),  // Independência
                new Date(2025, 9, 12), // Nossa Senhora Aparecida
                new Date(2025, 10, 2), // Finados
                new Date(2025, 10, 15), // Proclamação da República
                new Date(2025, 11, 25) // Natal
            ];
            
            // Marcar feriados como dias não úteis
            feriados2025.forEach(feriado => {
                gantt.setWorkTime({
                    date: feriado,
                    hours: false
                });
            });
            
            // Adicionar template para destacar visualmente dias não-úteis
            gantt.templates.timeline_cell_class = function(task, date) {
                // Verificar se é feriado
                let isFeriado = false;
                feriados2025.forEach(feriado => {
                    if (date.getDate() === feriado.getDate() && 
                        date.getMonth() === feriado.getMonth() && 
                        date.getFullYear() === feriado.getFullYear()) {
                        isFeriado = true;
                    }
                });
                
                // Habilitar progresso nas tarefas
                gantt.config.show_progress = true;
                
                if (isFeriado) {
                    return "gantt-holiday-cell"; // Classe CSS para feriados
                }
                
                // Verificar se é final de semana (0 = domingo, 6 = sábado)
                if (date.getDay() === 0 || date.getDay() === 6) {
                    return "gantt-weekend-cell"; // Classe CSS para finais de semana
                }
                
                return "";
            };
            
            // Adicionar tooltip para mostrar informações sobre feriados e progresso
            gantt.templates.tooltip_text = function(start, end, task) {
                let tooltipText = `<b>${task.text}</b><br>
                                  <b>Início:</b> ${gantt.templates.tooltip_date_format(start)}<br>
                                  <b>Fim:</b> ${gantt.templates.tooltip_date_format(end)}<br>
                                  <b>Duração:</b> ${task.duration} dias`;
                
                // Adicionar informação sobre progresso se disponível
                if (task.progress !== undefined) {
                    const progressPercent = Math.round(task.progress * 100);
                    tooltipText += `<br><b>Progresso:</b> ${progressPercent}%`;
                }
                
                // Adicionar informação sobre apontamento real se disponível
                if (task.real_start_date && task.real_end_date) {
                    tooltipText += `<br><b>Início Real:</b> ${gantt.templates.tooltip_date_format(task.real_start_date)}`;
                    tooltipText += `<br><b>Fim Real:</b> ${gantt.templates.tooltip_date_format(task.real_end_date)}`;
                    
                    // Calcular desvio em dias
                    const desvioInicio = Math.round((task.real_start_date - start) / (24 * 60 * 60 * 1000));
                    const desvioFim = Math.round((task.real_end_date - end) / (24 * 60 * 60 * 1000));
                    
                    tooltipText += `<br><b>Desvio Início:</b> ${desvioInicio > 0 ? '+' + desvioInicio : desvioInicio} dias`;
                    tooltipText += `<br><b>Desvio Fim:</b> ${desvioFim > 0 ? '+' + desvioFim : desvioFim} dias`;
                }
                
                // Adicionar informação sobre feriado se o dia for feriado
                if (!gantt.isWorkTime(start)) {
                    // Verificar se é feriado (não apenas final de semana)
                    if (start.getDay() !== 0 && start.getDay() !== 6) {
                        tooltipText += `<br><b>Observação:</b> Este dia é um feriado`;
                    }
                }
                
                return tooltipText;
            };
            
            // Template para exibir o progresso nas barras do Gantt
            gantt.templates.progress_text = function(start, end, task) {
                return Math.round(task.progress * 100) + '%';
            };
            
            // Template para colorir as tarefas com base no seu status de progresso
            gantt.templates.task_class = function(start, end, task) {
                let cssClass = '';
                
                // Adicionar classe para caminho crítico
                if (task.critical) {
                    cssClass += ' critical-path';
                }
                
                // Adicionar classe para nível hierárquico
                if (task.nivel) {
                    cssClass += ' nivel-' + task.nivel;
                }
                
                // Adicionar classe para tipo de tarefa
                if (task.tipo) {
                    cssClass += ' tipo-' + task.tipo;
                }
                
                // Adicionar classe para status de progresso
                if (task.progress === 1) {
                    cssClass += ' task-completed'; // 100% completa
                } else if (task.real_end_date && task.real_end_date > end) {
                    cssClass += ' task-delayed'; // Atrasada
                }
                
                return cssClass;
            };
            
            // Template para renderizar as barras das tarefas
            gantt.templates.task_text = function(start, end, task) {
                let taskText = '';
                
                if (task.tipo === "ordem") {
                    taskText = `<b>${task.text}</b>`;
                } else if (task.tipo === "componente") {
                    taskText = `<i>${task.text}</i>`;
                } else {
                    taskText = task.text;
                }
                
                // Adicionar informação de progresso se disponível
                if (task.progress !== undefined) {
                    const progressPercent = Math.round(task.progress * 100);
                    taskText += ` (${progressPercent}%)`;
                }
                
                return taskText;
            };
            
            // Template para renderizar o progresso real nas barras (versão compatível)
            gantt.templates.task_class = function(start, end, task) {
                let classes = [];

                // Adicionar classe para caminho crítico
                if (task.$critical) {
                    classes.push("critical");
                }

                // Adicionar classes para os diferentes tipos de tarefas
                if (task.tipo) {
                    classes.push(task.tipo);
                }

                // Adicionar classes para os diferentes níveis
                if (task.nivel) {
                    classes.push(`nivel-${task.nivel}`);
                }

                // Adicionar classe para progresso real se disponível
                if (task.real_start_date && task.real_end_date) {
                    classes.push("has-real-progress");
                }

                return classes.join(" ");
            };
            
            // Adicionar template para mostrar informações sobre feriados no tooltip
            gantt.templates.tooltip_date_format = function(date) {
                let formatoData = gantt.date.date_to_str("%d %F %Y");
                let dataFormatada = formatoData(date);
                
                // Verificar se é feriado
                let nomeFeriado = "";
                feriados2025.forEach((feriado, index) => {
                    if (date.getDate() === feriado.getDate() && 
                        date.getMonth() === feriado.getMonth() && 
                        date.getFullYear() === feriado.getFullYear()) {
                        
                        // Lista de nomes dos feriados na mesma ordem do array feriados2025
                        const nomesFeriados = [
                            "Ano Novo", "Carnaval", "Carnaval", "Sexta-feira Santa", "Tiradentes", 
                            "Dia do Trabalho", "Corpus Christi", "Independência", "Nossa Senhora Aparecida", 
                            "Finados", "Proclamação da República", "Natal"
                        ];
                        
                        nomeFeriado = " - " + nomesFeriados[index];
                    }
                });
                
                return dataFormatada + nomeFeriado;
            };
            
            // Configure columns in the left grid
            gantt.config.columns = [
                {name: "text", label: "Tarefa", tree: true, width: 200},
                {name: "start_date", label: "Data Início", align: "center", width: 90},
                {name: "duration", label: "Duração", align: "center", width: 60}
            ];
            
            // Template task_class já definido acima - removendo duplicação
            
            // Personalizar o texto das tarefas para mostrar o nível
            gantt.templates.task_text = function(start, end, task) {
                if (task.nivel) {
                    return `[N${task.nivel}] ${task.text}`;
                }
                return task.text;
            };
            
            // Configurar botões de expandir e recolher
            document.getElementById('expandAll').addEventListener('click', function() {
                gantt.eachTask(function(task) {
                    task.$open = true;
                });
                gantt.render();
            });
            
            document.getElementById('collapseAll').addEventListener('click', function() {
                gantt.eachTask(function(task) {
                    if (task.parent != 0) { // Não fechar tarefas de nível superior
                        task.$open = false;
                    }
                });
                gantt.render();
            });
            
            // Adicionar evento ao seletor de OPs
            document.getElementById('opSelector').addEventListener('change', function() {
                opSelecionadaId = this.value;
                filtrarPorOP(opSelecionadaId);
            });
            
            // Função para preencher o seletor de OPs
            function preencherSeletorOPs(ordens) {
                const seletor = document.getElementById('opSelector');
                
                // Limpar opções existentes, exceto a primeira (Todas as OPs)
                while (seletor.options.length > 1) {
                    seletor.remove(1);
                }
                
                // Adicionar cada OP como uma opção
                ordens.forEach(op => {
                    const option = document.createElement('option');
                    option.value = op.id;
                    option.text = `OP #${op.numero} - ${op.descricao || 'Sem descrição'}`;
                    seletor.add(option);
                });
            }
            
            // Função para filtrar o gráfico por OP
            function filtrarPorOP(opId) {
                if (opId === 'todas') {
                    // Mostrar todas as OPs
                    gantt.clearAll();
                    gantt.parse({
                        data: gantt.$_tarefasOriginais || gantt.serialize().data,
                        links: gantt.$_linksOriginais || gantt.serialize().links
                    });
                    return;
                }
                
                // Guardar dados originais se ainda não foram salvos
                if (!gantt.$_tarefasOriginais) {
                    gantt.$_tarefasOriginais = gantt.serialize().data;
                    gantt.$_linksOriginais = gantt.serialize().links;
                }
                
                // Filtrar tarefas para mostrar apenas a OP selecionada e suas subtarefas
                const tarefasFiltradas = gantt.$_tarefasOriginais.filter(task => {
                    // Incluir a OP selecionada
                    if (task.id === opId) return true;
                    
                    // Incluir subtarefas da OP selecionada (verificando hierarquia de parent)
                    let currentParent = task.parent;
                    while (currentParent) {
                        if (currentParent === opId) return true;
                        
                        // Encontrar o parent do parent
                        const parentTask = gantt.$_tarefasOriginais.find(t => t.id === currentParent);
                        currentParent = parentTask ? parentTask.parent : null;
                    }
                    
                    return false;
                });
                
                // Filtrar links para incluir apenas aqueles entre as tarefas filtradas
                const taskIds = tarefasFiltradas.map(task => task.id);
                const linksFiltrados = gantt.$_linksOriginais.filter(link => 
                    taskIds.includes(link.source) && taskIds.includes(link.target)
                );
                
                // Atualizar o gráfico com os dados filtrados
                gantt.clearAll();
                gantt.parse({
                    data: tarefasFiltradas,
                    links: linksFiltrados
                });
                
                // Encontrar o caminho crítico nas tarefas filtradas
                const criticalTasks = findCriticalPath(tarefasFiltradas, linksFiltrados);
                
                // Marcar tarefas críticas
                tarefasFiltradas.forEach(task => {
                    if (criticalTasks.includes(task.id)) {
                        task.$critical = true;
                    }
                });
                
                // Atualizar a legenda
                atualizarLegenda();
            }
            
            // Carregar dados de exemplo com estruturas multinível
            document.getElementById('loadExampleData').addEventListener('click', function() {
                carregarDadosExemplo();
            });

            // Calcular e destacar caminho crítico
            document.getElementById('calculateCriticalPath').addEventListener('click', function() {
                calcularEDestacarCaminhoCritico();
            });
            
            // Função para atualizar a legenda
            function atualizarLegenda() {
                // Criar container para a legenda se não existir
                let legendaContainer = document.getElementById('legenda-container');
                if (!legendaContainer) {
                    legendaContainer = document.createElement('div');
                    legendaContainer.id = 'legenda-container';
                    legendaContainer.className = 'legenda';
                    document.body.appendChild(legendaContainer);
                }
                
                // Limpar conteúdo atual da legenda
                legendaContainer.innerHTML = '';
                
                // Título da legenda
                const titulo = document.createElement('h3');
                titulo.textContent = 'Legenda';
                legendaContainer.appendChild(titulo);
                
                // Seção de níveis hierárquicos
                const secaoNiveis = document.createElement('div');
                secaoNiveis.innerHTML = '<strong>Níveis Hierárquicos:</strong>';
                legendaContainer.appendChild(secaoNiveis);
                
                // Adicionar itens de níveis
                const niveis = [
                    { nivel: 1, descricao: 'Nível 1' },
                    { nivel: 2, descricao: 'Nível 2' },
                    { nivel: 3, descricao: 'Nível 3' }
                ];
                
                niveis.forEach(nivel => {
                    const item = document.createElement('div');
                    item.className = 'legenda-item';
                    item.innerHTML = `
                        <div class="legenda-cor nivel-${nivel.nivel}"></div>
                        <div class="legenda-texto">${nivel.descricao}</div>
                    `;
                    secaoNiveis.appendChild(item);
                });
                
                // Seção de tipos de tarefa
                const secaoTipos = document.createElement('div');
                secaoTipos.innerHTML = '<br><strong>Tipos de Tarefa:</strong>';
                legendaContainer.appendChild(secaoTipos);
                
                // Adicionar itens de tipos
                const tipos = [
                    { tipo: 'ordem', descricao: 'Ordem de Produção' },
                    { tipo: 'componente', descricao: 'Componente' },
                    { tipo: 'operacao', descricao: 'Operação' }
                ];
                
                tipos.forEach(tipo => {
                    const item = document.createElement('div');
                    item.className = 'legenda-item';
                    item.innerHTML = `
                        <div class="legenda-cor tipo-${tipo.tipo}"></div>
                        <div class="legenda-texto">${tipo.descricao}</div>
                    `;
                    secaoTipos.appendChild(item);
                });
                
                // Seção de status
                const secaoStatus = document.createElement('div');
                secaoStatus.innerHTML = '<br><strong>Status:</strong>';
                legendaContainer.appendChild(secaoStatus);
                
                // Adicionar itens de status
                const status = [
                    { classe: 'critical-path', descricao: 'Caminho Crítico' },
                    { classe: 'task-completed', descricao: 'Tarefa Concluída' },
                    { classe: 'task-delayed', descricao: 'Tarefa Atrasada' }
                ];
                
                status.forEach(st => {
                    const item = document.createElement('div');
                    item.className = 'legenda-item';
                    item.innerHTML = `
                        <div class="legenda-cor ${st.classe}"></div>
                        <div class="legenda-texto">${st.descricao}</div>
                    `;
                    secaoStatus.appendChild(item);
                });
                
                // Seção de dias não-úteis
                const secaoDiasNaoUteis = document.createElement('div');
                secaoDiasNaoUteis.innerHTML = '<br><strong>Dias Não-úteis:</strong>';
                legendaContainer.appendChild(secaoDiasNaoUteis);
                
                // Adicionar itens de dias não-úteis
                const diasNaoUteis = [
                    { classe: 'gantt-weekend-cell', descricao: 'Final de Semana' },
                    { classe: 'gantt-holiday-cell', descricao: 'Feriado' }
                ];
                
                diasNaoUteis.forEach(dia => {
                    const item = document.createElement('div');
                    item.className = 'legenda-item';
                    item.innerHTML = `
                        <div class="legenda-cor ${dia.classe}"></div>
                        <div class="legenda-texto">${dia.descricao}</div>
                    `;
                    secaoDiasNaoUteis.appendChild(item);
                });
                
                // Seção de progresso real vs. planejado
                const secaoProgresso = document.createElement('div');
                secaoProgresso.className = 'legenda-progresso';
                secaoProgresso.innerHTML = '<br><strong>Progresso:</strong>';
                legendaContainer.appendChild(secaoProgresso);
                
                // Adicionar itens de progresso
                const progressoItem1 = document.createElement('div');
                progressoItem1.className = 'legenda-item';
                progressoItem1.innerHTML = `
                    <div class="legenda-progresso-planejado"></div>
                    <div class="legenda-texto">Planejado</div>
                `;
                secaoProgresso.appendChild(progressoItem1);
                
                const progressoItem2 = document.createElement('div');
                progressoItem2.className = 'legenda-item';
                progressoItem2.innerHTML = `
                    <div class="legenda-progresso-real"></div>
                    <div class="legenda-texto">Realizado</div>
                `;
                secaoProgresso.appendChild(progressoItem2);
            }
            
            // Inicializar o Gantt
            gantt.init("gantt_here");
            
            // Carregar dados
            loadProductionOrders().then(() => {
                // Atualizar a legenda após carregar os dados
                atualizarLegenda();
            }).catch(error => {
                console.error('Erro ao carregar dados:', error);
                // Se houver erro, perguntar se deseja carregar dados de exemplo
                if (confirm('Não foi possível carregar dados do banco. Deseja carregar dados de exemplo?')) {
                    carregarDadosExemplo();
                }
            });
            
            // Atualizar visualização quando redimensionar
            window.addEventListener('resize', function() {
                gantt.render();
            });
            } // Fim da função setupGanttChart

            // Inicializar o Gantt Chart
            initializeGantt();
        });
        
        // Função para calcular duração em dias com validação
        function calculateDuration(start, end) {
            try {
                const startDate = new Date(start);
                const endDate = new Date(end);
                
                // Verificar se as datas são válidas
                if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                    console.warn('Data inválida detectada ao calcular duração. Usando duração padrão de 1 dia.');
                    return 1;
                }
                
                // Garantir que a data final não seja anterior à data inicial
                if (endDate < startDate) {
                    console.warn('Data final anterior à data inicial detectada. Usando duração padrão de 1 dia.');
                    return 1;
                }
                
                // Calcular a diferença em dias considerando apenas dias úteis
                if (gantt.config.work_time) {
                    // Usar a API do Gantt para calcular dias úteis entre as datas
                    const duracao = gantt.calculateDuration(startDate, endDate);
                    return duracao > 0 ? duracao : 1;
                } else {
                    // Calcular todos os dias (incluindo não-úteis) se work_time estiver desativado
                    const duracao = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                    return duracao > 0 ? duracao : 1;
                }
            } catch (e) {
                console.warn('Erro ao calcular duração:', e);
                return 1; // Retornar duração padrão de 1 dia em caso de erro
            }
        }
        
        // Função para calcular a data final com base na data inicial e duração, considerando dias úteis
        function calculateEndDate(startDate, duration) {
            try {
                startDate = new Date(startDate);
                if (isNaN(startDate.getTime())) {
                    console.warn('Data de início inválida ao calcular data final. Usando data atual.');
                    startDate = new Date();
                }
                
                // Usar a API do Gantt para calcular a data final considerando dias úteis
                if (gantt.config.work_time) {
                    return gantt.calculateEndDate(startDate, duration);
                } else {
                    // Se work_time estiver desativado, simplesmente adicionar dias
                    const endDate = new Date(startDate);
                    endDate.setDate(endDate.getDate() + duration);
                    return endDate;
                }
            } catch (e) {
                console.warn('Erro ao calcular data final:', e);
                // Em caso de erro, retornar data atual + duração
                const endDate = new Date();
                endDate.setDate(endDate.getDate() + (duration || 1));
                return endDate;
            }
        }
        
        // Função para atualizar a legenda com base nos dados carregados
        function atualizarLegenda() {
            // Encontrar todos os níveis presentes nos dados
            const niveis = new Set();
            gantt.eachTask(function(task) {
                if (task.nivel) {
                    niveis.add(task.nivel);
                }
            });
            
            // Criar HTML para a legenda
            let legendaHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background-color: #ffcccc; margin-right: 10px; border-radius: 4px;"></div>
                        <span>Caminho Crítico</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background-color: #4b88d3; margin-right: 10px; border-radius: 4px;"></div>
                        <span>Ordem de Produção</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background-color: #5cb85c; margin-right: 10px; border-radius: 4px;"></div>
                        <span>Componente</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background-color: #f0ad4e; margin-right: 10px; border-radius: 4px;"></div>
                        <span>Operação</span>
                    </div>
                </div>
            `;
            
            // Adicionar legenda para os níveis hierárquicos se houver mais de um nível
            if (niveis.size > 0) {
                legendaHTML += `<hr style="margin: 10px 0; border: 0; border-top: 1px solid #eee;">
                <div style="margin-top: 10px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 14px;">Níveis Hierárquicos:</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">`;
                
                // Ordenar os níveis
                const niveisOrdenados = Array.from(niveis).sort((a, b) => a - b);
                
                // Adicionar cada nível à legenda
                niveisOrdenados.forEach(nivel => {
                    legendaHTML += `
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: ${22 - (nivel-1)*2}px; background-color: #ddd; margin-right: 10px; border-radius: ${5 - (nivel-1)}px;"></div>
                            <span>Nível ${nivel} - ${nivel === 1 ? 'Componentes Principais' : nivel === 2 ? 'Subcomponentes' : `Componentes Nível ${nivel}`}</span>
                        </div>
                    `;
                });
                
                legendaHTML += `</div></div>`;
            }
            
            // Adicionar legenda para dias não-úteis
            legendaHTML += `<hr style="margin: 10px 0; border: 0; border-top: 1px solid #eee;">
            <div style="margin-top: 10px;">
                <h4 style="margin: 0 0 10px 0; font-size: 14px;">Dias Não-úteis:</h4>
                <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background-color: #f8f8f8; margin-right: 10px; border-radius: 4px; border: 1px solid #ddd;"></div>
                        <span>Finais de Semana (Sábado e Domingo)</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background-color: #fff0f0; margin-right: 10px; border-radius: 4px; border: 1px solid #ffdddd;"></div>
                        <span>Feriados Nacionais</span>
                    </div>
                </div>
            </div>`;
            
            // Atualizar o conteúdo da legenda
            document.querySelector('.gantt-legend').innerHTML = legendaHTML;
        }
        
        // Função para carregar dados de exemplo com estruturas multinível
        function carregarDadosExemplo() {
            // Limpar dados existentes
            gantt.clearAll();
            
            // Datas para o exemplo
            const hoje = new Date();
            const amanha = new Date(hoje);
            amanha.setDate(amanha.getDate() + 1);
            const proximaSemana = new Date(hoje);
            proximaSemana.setDate(proximaSemana.getDate() + 7);
            const duasSemanas = new Date(hoje);
            duasSemanas.setDate(duasSemanas.getDate() + 14);
            
            // Criar tarefas de exemplo
            const tasks = [
                // Ordem de Produção 1
                { 
                    id: 1, 
                    text: "OP #OP001 - Produto Final A", 
                    start_date: hoje, 
                    duration: 14, 
                    parent: 0, 
                    open: true, 
                    tipo: "ordem",
                    progress: 0.7, // 70% completo
                    real_start_date: new Date(hoje.getTime() - 2*24*60*60*1000), // Comeou 2 dias antes
                    real_end_date: new Date(hoje.getTime() + 16*24*60*60*1000) // Vai terminar 2 dias depois
                },
                
                // Componentes de nível 1
                { 
                    id: 2, 
                    text: "2 x Subconjunto A1", 
                    start_date: hoje, 
                    duration: 10, 
                    parent: 1, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 1,
                    progress: 0.9, // 90% completo
                    real_start_date: new Date(hoje.getTime() - 1*24*60*60*1000), // Comeou 1 dia antes
                    real_end_date: new Date(hoje.getTime() + 9*24*60*60*1000) // Vai terminar no prazo
                },
                { 
                    id: 3, 
                    text: "1 x Subconjunto A2", 
                    start_date: amanha, 
                    duration: 12, 
                    parent: 1, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 1,
                    progress: 0.5, // 50% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Comeou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 15*24*60*60*1000) // Vai terminar 3 dias depois
                },
                
                // Operações do Produto Final
                { 
                    id: 4, 
                    text: "10 - Montagem Final", 
                    start_date: new Date(proximaSemana), 
                    duration: 2, 
                    parent: 1, 
                    tipo: "operacao", 
                    nivel: 1,
                    progress: 0, // Não iniciado
                    real_start_date: null,
                    real_end_date: null
                },
                { 
                    id: 5, 
                    text: "20 - Teste Final", 
                    start_date: new Date(proximaSemana.getTime() + 2*24*60*60*1000), 
                    duration: 1, 
                    parent: 1, 
                    tipo: "operacao", 
                    nivel: 1,
                    progress: 0, // Não iniciado
                    real_start_date: null,
                    real_end_date: null
                },
                { 
                    id: 6, 
                    text: "30 - Embalagem", 
                    start_date: new Date(proximaSemana.getTime() + 3*24*60*60*1000), 
                    duration: 1, 
                    parent: 1, 
                    tipo: "operacao", 
                    nivel: 1,
                    progress: 0, // Não iniciado
                    real_start_date: null,
                    real_end_date: null
                },
                
                // Componentes de nível 2 para Subconjunto A1
                { 
                    id: 7, 
                    text: "3 x Peça A1.1", 
                    start_date: hoje, 
                    duration: 5, 
                    parent: 2, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 2,
                    progress: 1.0, // 100% completo
                    real_start_date: new Date(hoje.getTime()), // Comeou no prazo
                    real_end_date: new Date(hoje.getTime() + 4*24*60*60*1000) // Terminou 1 dia antes
                },
                { 
                    id: 8, 
                    text: "2 x Peça A1.2", 
                    start_date: hoje, 
                    duration: 4, 
                    parent: 2, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 2,
                    progress: 0.75, // 75% completo
                    real_start_date: new Date(hoje.getTime()), // Comeou no prazo
                    real_end_date: new Date(hoje.getTime() + 5*24*60*60*1000) // Vai terminar 1 dia depois
                },
                
                // Operações do Subconjunto A1
                { 
                    id: 9, 
                    text: "10 - Preparação A1", 
                    start_date: hoje, 
                    duration: 1, 
                    parent: 2, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 1.0, // 100% completo
                    real_start_date: new Date(hoje.getTime()), // Comeou no prazo
                    real_end_date: new Date(hoje.getTime() + 1*24*60*60*1000) // Terminou no prazo
                },
                { 
                    id: 10, 
                    text: "20 - Montagem A1", 
                    start_date: new Date(hoje.getTime() + 1*24*60*60*1000), 
                    duration: 2, 
                    parent: 2, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 1.0, // 100% completo
                    real_start_date: new Date(hoje.getTime() + 1*24*60*60*1000), // Comeou no prazo
                    real_end_date: new Date(hoje.getTime() + 3*24*60*60*1000) // Terminou no prazo
                },
                { 
                    id: 11, 
                    text: "30 - Teste A1", 
                    start_date: new Date(hoje.getTime() + 3*24*60*60*1000), 
                    duration: 1, 
                    parent: 2, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 0.5, // 50% completo
                    real_start_date: new Date(hoje.getTime() + 3*24*60*60*1000), // Comeou no prazo
                    real_end_date: new Date(hoje.getTime() + 4*24*60*60*1000) // Vai terminar no prazo
                },
                
                // Componentes de nível 2 para Subconjunto A2
                { 
                    id: 12, 
                    text: "1 x Peça A2.1", 
                    start_date: amanha, 
                    duration: 6, 
                    parent: 3, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 2,
                    progress: 0.3, // 30% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Comeou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 8*24*60*60*1000) // Vai terminar 2 dias depois
                },
                { 
                    id: 13, 
                    text: "4 x Peça A2.2", 
                    start_date: amanha, 
                    duration: 7, 
                    parent: 3, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 2,
                    progress: 0.4, // 40% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Comeou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 9*24*60*60*1000) // Vai terminar 2 dias depois
                },
                
                // Operações do Subconjunto A2
                { 
                    id: 14, 
                    text: "10 - Preparação A2", 
                    start_date: amanha, 
                    duration: 1, 
                    parent: 3, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 0.5, // 50% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Comeou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 2*24*60*60*1000) // Vai terminar 1 dia depois
                },
                { 
                    id: 15, 
                    text: "20 - Montagem A2", 
                    start_date: new Date(amanha.getTime() + 1*24*60*60*1000), 
                    duration: 3, 
                    parent: 3, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 0.5, // 50% completo
                    real_start_date: new Date(amanha.getTime() + 2*24*60*60*1000), // Comeou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 5*24*60*60*1000) // Vai terminar 2 dias depois
                },
                { 
                    id: 16, 
                    text: "30 - Teste A2", 
                    start_date: new Date(amanha.getTime() + 4*24*60*60*1000), 
                    duration: 1, 
                    parent: 3, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 0.5, // 50% completo
                    real_start_date: new Date(amanha.getTime() + 5*24*60*60*1000), // Comeou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 6*24*60*60*1000) // Vai terminar 1 dia depois
                },
                
                // Componentes de nível 3 para Peça A1.1
                { id: 17, text: "2 x Materia Prima X", start_date: hoje, duration: 3, parent: 7, open: true, tipo: "componente", nivel: 3 },
                
                // Operações da Peça A1.1
                { id: 18, text: "10 - Corte", start_date: hoje, duration: 1, parent: 7, tipo: "operacao", nivel: 3 },
                { id: 19, text: "20 - Dobra", start_date: new Date(hoje.getTime() + 1*24*60*60*1000), duration: 1, parent: 7, tipo: "operacao", nivel: 3 },
                { id: 20, text: "30 - Acabamento", start_date: new Date(hoje.getTime() + 2*24*60*60*1000), duration: 1, parent: 7, tipo: "operacao", nivel: 3 },
                
                // Ordem de Produção 2 (segundo exemplo)
                { 
                    id: 21, 
                    text: "OP #OP002 - Produto Final B", 
                    start_date: amanha, 
                    duration: 10, 
                    parent: 0, 
                    open: true, 
                    tipo: "ordem",
                    progress: 0.3, // 30% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Começou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 12*24*60*60*1000) // Vai terminar 2 dias depois
                },
                
                // Componentes de nível 1 para Produto B
                { 
                    id: 22, 
                    text: "1 x Subconjunto B1", 
                    start_date: amanha, 
                    duration: 8, 
                    parent: 21, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 1,
                    progress: 0.4, // 40% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Começou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 10*24*60*60*1000) // Vai terminar 2 dias depois
                },
                
                // Operações do Produto Final B
                { 
                    id: 23, 
                    text: "10 - Montagem Final B", 
                    start_date: new Date(amanha.getTime() + 8*24*60*60*1000), 
                    duration: 2, 
                    parent: 21, 
                    tipo: "operacao", 
                    nivel: 1,
                    progress: 0, // Não iniciado
                    real_start_date: null,
                    real_end_date: null
                },
                
                // Componentes de nível 2 para Subconjunto B1
                { 
                    id: 24, 
                    text: "2 x Peça B1.1", 
                    start_date: amanha, 
                    duration: 4, 
                    parent: 22, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 2,
                    progress: 0.8, // 80% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Começou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 5*24*60*60*1000) // Vai terminar 1 dia depois
                },
                { 
                    id: 25, 
                    text: "3 x Peça B1.2", 
                    start_date: amanha, 
                    duration: 5, 
                    parent: 22, 
                    open: true, 
                    tipo: "componente", 
                    nivel: 2,
                    progress: 0.2, // 20% completo
                    real_start_date: new Date(amanha.getTime() + 2*24*60*60*1000), // Começou 2 dias depois
                    real_end_date: new Date(amanha.getTime() + 8*24*60*60*1000) // Vai terminar 3 dias depois
                },
                
                // Operações do Subconjunto B1
                { 
                    id: 26, 
                    text: "10 - Preparação B1", 
                    start_date: amanha, 
                    duration: 1, 
                    parent: 22, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 1.0, // 100% completo
                    real_start_date: new Date(amanha.getTime() + 1*24*60*60*1000), // Começou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 2*24*60*60*1000) // Terminou no prazo
                },
                { 
                    id: 27, 
                    text: "20 - Montagem B1", 
                    start_date: new Date(amanha.getTime() + 5*24*60*60*1000), 
                    duration: 2, 
                    parent: 22, 
                    tipo: "operacao", 
                    nivel: 2,
                    progress: 0.5, // 50% completo
                    real_start_date: new Date(amanha.getTime() + 6*24*60*60*1000), // Começou 1 dia depois
                    real_end_date: new Date(amanha.getTime() + 8*24*60*60*1000) // Vai terminar no prazo
                },
            ];
            
            // Criar links entre tarefas
            const links = [
                // Links entre operações do Produto Final
                { id: 1, source: 4, target: 5, type: "0" },
                { id: 2, source: 5, target: 6, type: "0" },
                
                // Links entre operações do Subconjunto A1
                { id: 3, source: 9, target: 10, type: "0" },
                { id: 4, source: 10, target: 11, type: "0" },
                
                // Links entre operações do Subconjunto A2
                { id: 5, source: 14, target: 15, type: "0" },
                { id: 6, source: 15, target: 16, type: "0" },
                
                // Links entre operações da Peça A1.1
                { id: 7, source: 18, target: 19, type: "0" },
                { id: 8, source: 19, target: 20, type: "0" },
                
                // Links de dependência entre componentes e montagem final
                { id: 9, source: 11, target: 4, type: "0" },  // A1 deve terminar antes da montagem final
                { id: 10, source: 16, target: 4, type: "0" }, // A2 deve terminar antes da montagem final
                
                // Links para o Produto B
                { id: 11, source: 26, target: 27, type: "0" },
                { id: 12, source: 27, target: 23, type: "0" },
            ];
            
            // Encontrar o caminho crítico
            const criticalTasks = findCriticalPath(tasks, links);
            
            // Marcar tarefas críticas
            tasks.forEach(task => {
                if (criticalTasks.includes(task.id)) {
                    task.$critical = true;
                }
            });
            
            // Carregar dados no Gantt
            gantt.parse({
                data: tasks,
                links: links
            });
            
            // Atualizar a legenda com os níveis presentes nos dados
            atualizarLegenda();
        }
        
        // Fim da função carregarDadosExemplo
        
        // Carregar dados das ordens de produção
        function loadProductionOrders() {
            return new Promise(async (resolve, reject) => {
                try {
                    // Criar dados de teste se não houver dados reais
                    const opSnapshot = await db.collection("ordensProducao").get();
                    const estruturaSnapshot = await db.collection("estruturas").get();
                
                let ordens = opSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                let estruturas = estruturaSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Se não houver dados, criar dados de teste
                if (ordens.length === 0) {
                    console.log("Criando dados de teste para o Gantt");
                    // Criar dados de teste
                    const today = new Date();
                    const tomorrow = new Date(today);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const nextWeek = new Date(today);
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    
                    ordens = [
                        {
                            id: "op1",
                            numero: "OP001",
                            descricao: "Produto A",
                            dataInicio: today.toISOString(),
                            dataFim: nextWeek.toISOString(),
                            produtoId: "prod1"
                        },
                        {
                            id: "op2",
                            numero: "OP002",
                            descricao: "Produto B",
                            dataInicio: tomorrow.toISOString(),
                            dataFim: new Date(tomorrow.getTime() + 3*24*60*60*1000).toISOString()
                        }
                    ];
                    
                    estruturas = [
                        {
                            id: "est1",
                            produtoPaiId: "prod1",
                            operacoes: [
                                {
                                    id: "op1_1",
                                    numero: "10",
                                    descricao: "Corte",
                                    dataInicio: today.toISOString(),
                                    dataFim: new Date(today.getTime() + 2*24*60*60*1000).toISOString()
                                },
                                {
                                    id: "op1_2",
                                    numero: "20",
                                    descricao: "Montagem",
                                    dataInicio: new Date(today.getTime() + 2*24*60*60*1000).toISOString(),
                                    dataFim: new Date(today.getTime() + 5*24*60*60*1000).toISOString()
                                },
                                {
                                    id: "op1_3",
                                    numero: "30",
                                    descricao: "Acabamento",
                                    dataInicio: new Date(today.getTime() + 5*24*60*60*1000).toISOString(),
                                    dataFim: nextWeek.toISOString()
                                }
                            ]
                        },
                        {
                            id: "est2",
                            produtoPaiId: "prod2",
                            operacoes: [
                                {
                                    id: "op2_1",
                                    numero: "10",
                                    descricao: "Preparação",
                                    dataInicio: tomorrow.toISOString(),
                                    dataFim: new Date(tomorrow.getTime() + 3*24*60*60*1000).toISOString()
                                },
                                {
                                    id: "op2_2",
                                    numero: "20",
                                    descricao: "Processamento",
                                    dataInicio: new Date(tomorrow.getTime() + 3*24*60*60*1000).toISOString(),
                                    dataFim: nextWeek.toISOString()
                                }
                            ]
                        }
                    ];
                }
                
                // Processar dados para o Gantt
                const tasks = [];
                const links = [];
                
                // Armazenar todas as ordens para uso posterior
                todasOPs = ordens;
                
                // Preencher o seletor de OPs
                preencherSeletorOPs(ordens);
                
                // Processar ordens e suas dependências
                ordens.forEach(op => {
                    const estrutura = estruturas.find(e => e.produtoPaiId === op.produtoId);
                    if (!estrutura) return;
                    
                    // Adicionar tarefa principal (ordem de produção)
                    // Garantir que as datas sejam válidas
                    let startDate;
                    let duracao = 7; // Duração padrão de 7 dias para ordens
                    
                    try {
                        startDate = new Date(op.dataInicio);
                        if (isNaN(startDate.getTime())) {
                            console.warn(`Data de início inválida para ordem ${op.numero}. Usando data atual.`);
                            startDate = new Date();
                        }
                    } catch (e) {
                        console.warn(`Erro ao processar data de início para ordem ${op.numero}. Usando data atual.`);
                        startDate = new Date();
                    }
                    
                    try {
                        duracao = calculateDuration(op.dataInicio, op.dataFim) || 7;
                    } catch (e) {
                        console.warn(`Erro ao calcular duração para ordem ${op.numero}. Usando 7 dias.`);
                    }
                    
                    // Verificar se existem dados de progresso e datas reais
                    let progress = op.progresso || 0;
                    let real_start_date = null;
                    let real_end_date = null;
                    
                    try {
                        if (op.dataInicioReal) {
                            real_start_date = new Date(op.dataInicioReal);
                            if (isNaN(real_start_date.getTime())) {
                                real_start_date = null;
                            }
                        }
                    } catch (e) {
                        console.warn(`Erro ao processar data de início real para ordem ${op.numero}.`);
                        real_start_date = null;
                    }
                    
                    try {
                        if (op.dataFimReal) {
                            real_end_date = new Date(op.dataFimReal);
                            if (isNaN(real_end_date.getTime())) {
                                real_end_date = null;
                            }
                        }
                    } catch (e) {
                        console.warn(`Erro ao processar data de fim real para ordem ${op.numero}.`);
                        real_end_date = null;
                    }
                    
                    tasks.push({
                        id: op.id,
                        text: `OP #${op.numero} - ${op.descricao || 'Sem descrição'}`,
                        start_date: startDate,
                        duration: duracao,
                        parent: 0,
                        open: true,
                        tipo: 'ordem',
                        progress: progress,
                        real_start_date: real_start_date,
                        real_end_date: real_end_date
                    });
                    
                    // Processar estrutura de forma recursiva
                    processarEstrutura(estrutura, op.id, tasks, links, 1);
                });
                
                // Função para processar estruturas de forma recursiva
                function processarEstrutura(estrutura, parentId, tasks, links, nivel) {
                    // Adicionar componentes da estrutura
                    if (estrutura.componentes && Array.isArray(estrutura.componentes)) {
                        estrutura.componentes.forEach((componente, index) => {
                            // Encontrar a subestrutura do componente
                            const subEstrutura = estruturas.find(e => e.produtoPaiId === componente.produtoId);
                            
                            // Criar ID único para o componente
                            const componenteId = `${parentId}_comp_${index}`;
                            
                            // Data de início e fim para o componente
                            let startDate = new Date();
                            let endDate = new Date();
                            endDate.setDate(endDate.getDate() + 3); // Padrão de 3 dias se não houver dados
                            
                            // Garantir que as datas sejam válidas
                            try {
                                startDate = new Date(startDate);
                                if (isNaN(startDate.getTime())) {
                                    // Se a data for inválida, usar a data atual
                                    console.warn(`Data de início inválida para componente ${componente.produtoId}. Usando data atual.`);
                                    startDate = new Date();
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de início para componente ${componente.produtoId}. Usando data atual.`);
                                startDate = new Date();
                            }
                            
                            try {
                                endDate = new Date(endDate);
                                if (isNaN(endDate.getTime())) {
                                    // Se a data for inválida, usar a data atual
                                    console.warn(`Data de fim inválida para componente ${componente.produtoId}. Usando data atual.`);
                                    endDate = new Date();
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de fim para componente ${componente.produtoId}. Usando data atual.`);
                                endDate = new Date();
                            }
                            
                            // Se houver operações na subestrutura, usar suas datas
                            if (subEstrutura && subEstrutura.operacoes && subEstrutura.operacoes.length > 0) {
                                const opDates = subEstrutura.operacoes.map(op => {
                                    let start, end;
                                    let real_start = null;
                                    let real_end = null;
                                    let progress = op.progresso || 0;
                                    
                                    try {
                                        start = new Date(op.dataInicio);
                                        if (isNaN(start.getTime())) {
                                            console.warn(`Data de início inválida para operação ${op.numero}. Usando data atual.`);
                                            start = new Date();
                                        }
                                    } catch (e) {
                                        console.warn(`Erro ao processar data de início para operação ${op.numero}. Usando data atual.`);
                                        start = new Date();
                                    }
                                    
                                    try {
                                        end = new Date(op.dataFim);
                                        if (isNaN(end.getTime())) {
                                            console.warn(`Data de fim inválida para operação ${op.numero}. Usando data atual + 1 dia.`);
                                            end = new Date();
                                            end.setDate(end.getDate() + 1);
                                        }
                                    } catch (e) {
                                        console.warn(`Erro ao processar data de fim para operação ${op.numero}. Usando data atual + 1 dia.`);
                                        end = new Date();
                                        end.setDate(end.getDate() + 1);
                                    }
                                    
                                    // Processar datas reais de apontamento
                                    try {
                                        if (op.dataInicioReal) {
                                            real_start = new Date(op.dataInicioReal);
                                            if (isNaN(real_start.getTime())) {
                                                real_start = null;
                                            }
                                        }
                                    } catch (e) {
                                        console.warn(`Erro ao processar data de início real para operação ${op.numero}.`);
                                        real_start = null;
                                    }
                                    
                                    try {
                                        if (op.dataFimReal) {
                                            real_end = new Date(op.dataFimReal);
                                            if (isNaN(real_end.getTime())) {
                                                real_end = null;
                                            }
                                        }
                                    } catch (e) {
                                        console.warn(`Erro ao processar data de fim real para operação ${op.numero}.`);
                                        real_end = null;
                                    }
                                    
                                    return { start, end, real_start, real_end, progress };
                                });
                                
                                startDate = new Date(Math.min(...opDates.map(d => d.start.getTime())));
                                endDate = new Date(Math.max(...opDates.map(d => d.end.getTime())));
                            }
                            
                            // Verificar se existem dados de progresso e datas reais para o componente
                            let progress = componente.progresso || 0;
                            let real_start_date = null;
                            let real_end_date = null;
                            
                            // Se houver operações, calcular progresso médio e datas reais
                            if (subEstrutura && subEstrutura.operacoes && subEstrutura.operacoes.length > 0) {
                                // Calcular progresso médio das operações se não houver progresso definido
                                if (!componente.progresso) {
                                    const progressValues = opDates.map(d => d.progress).filter(p => p !== undefined && p !== null);
                                    if (progressValues.length > 0) {
                                        progress = progressValues.reduce((sum, val) => sum + val, 0) / progressValues.length;
                                    }
                                }
                                
                                // Usar primeira data real de início e última data real de fim das operações
                                const realStartDates = opDates.map(d => d.real_start).filter(d => d !== null);
                                const realEndDates = opDates.map(d => d.real_end).filter(d => d !== null);
                                
                                if (realStartDates.length > 0) {
                                    real_start_date = new Date(Math.min(...realStartDates.map(d => d.getTime())));
                                }
                                
                                if (realEndDates.length > 0) {
                                    real_end_date = new Date(Math.max(...realEndDates.map(d => d.getTime())));
                                }
                            }
                            
                            // Verificar se o componente tem suas próprias datas reais definidas
                            try {
                                if (componente.dataInicioReal) {
                                    const date = new Date(componente.dataInicioReal);
                                    if (!isNaN(date.getTime())) {
                                        real_start_date = date;
                                    }
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de início real para componente.`);
                            }
                            
                            try {
                                if (componente.dataFimReal) {
                                    const date = new Date(componente.dataFimReal);
                                    if (!isNaN(date.getTime())) {
                                        real_end_date = date;
                                    }
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de fim real para componente.`);
                            }
                            
                            // Adicionar componente como tarefa
                            tasks.push({
                                id: componenteId,
                                text: `${componente.quantidade || 1} x ${componente.descricao || componente.produtoId}`,
                                start_date: startDate,
                                duration: calculateDuration(startDate, endDate),
                                parent: parentId,
                                open: true,
                                tipo: 'componente',
                                nivel: nivel,
                                progress: progress,
                                real_start_date: real_start_date,
                                real_end_date: real_end_date
                            });
                            
                            // Se houver subestrutura, processar recursivamente
                            if (subEstrutura) {
                                processarEstrutura(subEstrutura, componenteId, tasks, links, nivel + 1);
                            }
                        });
                    }
                    
                    // Adicionar operações da estrutura
                    if (estrutura.operacoes && Array.isArray(estrutura.operacoes)) {
                        let prevOpId = null;
                        
                        estrutura.operacoes.forEach((operacao, index) => {
                            const taskId = `${parentId}_op_${index}`;
                            
                            // Adicionar operação como tarefa
                            // Garantir que as datas sejam válidas
                            let startDate;
                            try {
                                startDate = new Date(operacao.dataInicio);
                                if (isNaN(startDate.getTime())) {
                                    // Se a data for inválida, usar a data atual
                                    console.warn(`Data de início inválida para operação ${operacao.numero}. Usando data atual.`);
                                    startDate = new Date();
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de início para operação ${operacao.numero}. Usando data atual.`);
                                startDate = new Date();
                            }
                            
                            let endDate;
                            try {
                                endDate = new Date(operacao.dataFim);
                                if (isNaN(endDate.getTime())) {
                                    // Se a data for inválida, usar a data atual + 1 dia
                                    console.warn(`Data de fim inválida para operação ${operacao.numero}. Usando data atual + 1 dia.`);
                                    endDate = new Date();
                                    endDate.setDate(endDate.getDate() + 1);
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de fim para operação ${operacao.numero}. Usando data atual + 1 dia.`);
                                endDate = new Date();
                                endDate.setDate(endDate.getDate() + 1);
                            }
                            
                            // Verificar se existem dados de progresso e datas reais
                            let progress = operacao.progresso || 0;
                            let real_start_date = null;
                            let real_end_date = null;
                            
                            try {
                                if (operacao.dataInicioReal) {
                                    real_start_date = new Date(operacao.dataInicioReal);
                                    if (isNaN(real_start_date.getTime())) {
                                        real_start_date = null;
                                    }
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de início real para operação ${operacao.numero}.`);
                                real_start_date = null;
                            }
                            
                            try {
                                if (operacao.dataFimReal) {
                                    real_end_date = new Date(operacao.dataFimReal);
                                    if (isNaN(real_end_date.getTime())) {
                                        real_end_date = null;
                                    }
                                }
                            } catch (e) {
                                console.warn(`Erro ao processar data de fim real para operação ${operacao.numero}.`);
                                real_end_date = null;
                            }
                            
                            tasks.push({
                                id: taskId,
                                text: `${operacao.numero} - ${operacao.descricao || 'Operação sem descrição'}`,
                                start_date: startDate,
                                duration: calculateDuration(startDate, endDate),
                                parent: parentId,
                                tipo: 'operacao',
                                nivel: nivel,
                                progress: progress,
                                real_start_date: real_start_date,
                                real_end_date: real_end_date
                            });
                            
                            // Adicionar links entre operações
                            if (prevOpId) {
                                links.push({
                                    id: `link_${taskId}`,
                                    source: prevOpId,
                                    target: taskId,
                                    type: "0"
                                });
                            }
                            
                            prevOpId = taskId;
                        });
                    }
                }
                // Encontrar o caminho crítico
                const criticalTasks = findCriticalPath(tasks, links);
                
                // Marcar tarefas críticas
                tasks.forEach(task => {
                    if (criticalTasks.includes(task.id)) {
                        task.$critical = true;
                    }
                });
                
                // Chamar função para atualizar a legenda
                atualizarLegenda();
                
                // Função para atualizar a legenda com todos os elementos visuais
                function atualizarLegenda() {
                    // Verificar se o container da legenda existe, se não, criar
                    let legendaContainer = document.querySelector('.gantt-legend');
                    if (!legendaContainer) {
                        legendaContainer = document.createElement('div');
                        legendaContainer.className = 'gantt-legend';
                        legendaContainer.style.padding = '10px';
                        legendaContainer.style.backgroundColor = '#fff';
                        legendaContainer.style.border = '1px solid #ddd';
                        legendaContainer.style.borderRadius = '4px';
                        legendaContainer.style.margin = '10px 0';
                        legendaContainer.style.boxShadow = '0 1px 4px rgba(0,0,0,0.1)';
                        document.body.appendChild(legendaContainer);
                    }
                    
                    // Criar HTML da legenda com todas as seções
                    let legendaHTML = `<h3 style="margin-top: 0; color: #333;">Legenda do Gráfico Gantt</h3>`;
                    
                    // Seção 1: Níveis Hierárquicos
                    legendaHTML += `
                    <div style="margin-bottom: 15px;">
                        <h4 style="margin: 5px 0; color: #555;">Níveis Hierárquicos</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; border-left: 3px solid #4b88d3; margin-right: 10px;"></div>
                                <span>Nível 1</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; border-left: 3px solid #5cb85c; margin-right: 10px;"></div>
                                <span>Nível 2</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; border-left: 3px solid #f0ad4e; margin-right: 10px;"></div>
                                <span>Nível 3</span>
                            </div>
                        </div>
                    </div>`;
                    
                    // Seção 2: Tipos de Tarefa
                    legendaHTML += `
                    <div style="margin-bottom: 15px;">
                        <h4 style="margin: 5px 0; color: #555;">Tipos de Tarefa</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #4b88d3; margin-right: 10px; border-radius: 4px;"></div>
                                <span><b>Ordem de Produção</b></span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #5cb85c; margin-right: 10px; border-radius: 4px;"></div>
                                <span>Componente</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #f0ad4e; margin-right: 10px; border-radius: 4px;"></div>
                                <span><i>Operação</i></span>
                            </div>
                        </div>
                    </div>`;
                    
                    // Seção 3: Status da Tarefa
                    legendaHTML += `
                    <div style="margin-bottom: 15px;">
                        <h4 style="margin: 5px 0; color: #555;">Status da Tarefa</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; border: 2px solid #F44336; margin-right: 10px; border-radius: 4px;"></div>
                                <span>Caminho Crítico</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #A5D6A7; margin-right: 10px; border-radius: 4px; border: 1px solid #4CAF50;"></div>
                                <span>Tarefa Concluída</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #FFCDD2; margin-right: 10px; border-radius: 4px; border: 1px solid #E57373;"></div>
                                <span>Tarefa Atrasada</span>
                            </div>
                        </div>
                    </div>`;
                    
                    // Seção 4: Dias Não Úteis
                    legendaHTML += `
                    <div style="margin-bottom: 15px;">
                        <h4 style="margin: 5px 0; color: #555;">Dias Não Úteis</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #f8f8f8; margin-right: 10px; border-radius: 4px; border: 1px solid #ddd;"></div>
                                <span>Final de Semana</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 20px; height: 20px; background-color: #fff0f0; margin-right: 10px; border-radius: 4px; border: 1px solid #ffcccc;"></div>
                                <span>Feriado</span>
                            </div>
                        </div>
                    </div>`;
                    
                    // Seção 5: Progresso
                    legendaHTML += `
                    <div style="margin-bottom: 15px;">
                        <h4 style="margin: 5px 0; color: #555;">Progresso</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 50px; height: 10px; background-color: #4CAF50; opacity: 0.6; margin-right: 10px; border-radius: 2px;"></div>
                                <span>Planejado</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 50px; height: 4px; background-color: #FF9800; margin-right: 10px; border-radius: 2px;"></div>
                                <span>Realizado</span>
                            </div>
                        </div>
                    </div>`;
                    
                    // Atualizar o conteúdo da legenda
                    legendaContainer.innerHTML = legendaHTML;
                }
                
                // Carregar dados no Gantt
                gantt.parse({
                    data: tasks,
                    links: links
                });
                
                // Resolver a Promise com sucesso
                resolve();
                
            } catch (error) {
                console.error("Erro ao carregar dados:", error);
                // Rejeitar a Promise com o erro
                reject(error);
            }
            });
        }
        
        // Função para encontrar o caminho crítico - MELHORADA
        function findCriticalPath(tasks, links) {
            console.log('🔍 Calculando caminho crítico...');
            console.log(`📊 Analisando ${tasks.length} tarefas e ${links.length} dependências`);

            // Criar grafo de dependências
            const graph = {};
            const durations = {};
            const predecessors = {};

            // Inicializar grafo
            tasks.forEach(task => {
                graph[task.id] = [];
                durations[task.id] = task.duration || 1;
            });
            
            // Adicionar dependências
            links.forEach(link => {
                if (!graph[link.target]) {
                    graph[link.target] = [];
                }
                graph[link.target].push(link.source);
                
                if (!predecessors[link.source]) {
                    predecessors[link.source] = [];
                }
                predecessors[link.source].push(link.target);
            });
            
            // Encontrar tarefas sem predecessores (início)
            const startTasks = Object.keys(graph).filter(task => graph[task].length === 0);
            
            // Calcular earliest start/finish
            const earliestStart = {};
            const earliestFinish = {};
            
            // Inicializar earliest start para tarefas iniciais
            startTasks.forEach(task => {
                earliestStart[task] = 0;
                earliestFinish[task] = durations[task];
            });
            
            // Calcular earliest start/finish para todas as tarefas
            const visited = new Set();
            
            function calculateEarliestTimes(taskId) {
                if (visited.has(taskId)) return;
                visited.add(taskId);
                
                // Processar predecessores primeiro
                graph[taskId].forEach(pred => {
                    if (!visited.has(pred)) {
                        calculateEarliestTimes(pred);
                    }
                });
                
                // Calcular earliest start como o máximo dos earliest finish dos predecessores
                let maxPredFinish = 0;
                graph[taskId].forEach(pred => {
                    maxPredFinish = Math.max(maxPredFinish, earliestFinish[pred] || 0);
                });
                
                earliestStart[taskId] = maxPredFinish;
                earliestFinish[taskId] = maxPredFinish + durations[taskId];
            }
            
            // Calcular para todas as tarefas
            tasks.forEach(task => {
                if (!visited.has(task.id)) {
                    calculateEarliestTimes(task.id);
                }
            });
            
            // Encontrar tarefas sem sucessores (fim)
            const endTasks = Object.keys(graph).filter(task => !predecessors[task] || predecessors[task].length === 0);
            
            // Calcular latest start/finish
            const latestStart = {};
            const latestFinish = {};
            
            // Encontrar o tempo de projeto total
            let projectEnd = 0;
            endTasks.forEach(task => {
                projectEnd = Math.max(projectEnd, earliestFinish[task] || 0);
            });
            
            // Inicializar latest finish para tarefas finais
            endTasks.forEach(task => {
                latestFinish[task] = projectEnd;
                latestStart[task] = projectEnd - durations[task];
            });
            
            // Calcular latest start/finish para todas as tarefas (backward pass)
            const visitedBackward = new Set();
            
            function calculateLatestTimes(taskId) {
                if (visitedBackward.has(taskId)) return;
                visitedBackward.add(taskId);
                
                // Processar sucessores primeiro
                if (predecessors[taskId]) {
                    predecessors[taskId].forEach(succ => {
                        if (!visitedBackward.has(succ)) {
                            calculateLatestTimes(succ);
                        }
                    });
                }
                
                // Calcular latest finish como o mínimo dos latest start dos sucessores
                let minSuccStart = projectEnd;
                if (predecessors[taskId]) {
                    predecessors[taskId].forEach(succ => {
                        minSuccStart = Math.min(minSuccStart, latestStart[succ] || projectEnd);
                    });
                }
                
                latestFinish[taskId] = minSuccStart;
                latestStart[taskId] = minSuccStart - durations[taskId];
            }
            
            // Calcular para todas as tarefas
            tasks.forEach(task => {
                if (!visitedBackward.has(task.id)) {
                    calculateLatestTimes(task.id);
                }
            });
            
            // Calcular folga e identificar caminho crítico
            const criticalPath = [];
            const taskAnalysis = [];

            tasks.forEach(task => {
                const es = earliestStart[task.id] || 0;
                const ef = earliestFinish[task.id] || 0;
                const ls = latestStart[task.id] || 0;
                const lf = latestFinish[task.id] || 0;
                const slack = ls - es;
                const isCritical = Math.abs(slack) < 0.01; // Considerar folga quase zero

                if (isCritical) {
                    criticalPath.push(task.id);
                }

                taskAnalysis.push({
                    id: task.id,
                    text: task.text,
                    duration: durations[task.id],
                    earliestStart: es,
                    earliestFinish: ef,
                    latestStart: ls,
                    latestFinish: lf,
                    slack: slack,
                    isCritical: isCritical
                });
            });

            // Log detalhado do caminho crítico
            console.log('📊 Análise do Caminho Crítico:');
            console.log(`🎯 Tarefas críticas encontradas: ${criticalPath.length}`);
            console.log(`⏱️ Duração total do projeto: ${projectEnd} dias`);

            // Mostrar tarefas críticas
            taskAnalysis.forEach(task => {
                if (task.isCritical) {
                    console.log(`🔥 CRÍTICA: ${task.text} (Folga: ${task.slack.toFixed(2)} dias)`);
                }
            });

            // Mostrar tarefas com maior folga (menos críticas)
            const nonCritical = taskAnalysis.filter(t => !t.isCritical).sort((a, b) => b.slack - a.slack);
            if (nonCritical.length > 0) {
                console.log('💡 Tarefas com maior folga:');
                nonCritical.slice(0, 3).forEach(task => {
                    console.log(`   ${task.text} (Folga: ${task.slack.toFixed(2)} dias)`);
                });
            }

            return {
                criticalTasks: criticalPath,
                taskAnalysis: taskAnalysis,
                projectDuration: projectEnd
            };
        }

        // Função para calcular e destacar caminho crítico
        function calcularEDestacarCaminhoCritico() {
            try {
                console.log('🔥 Iniciando cálculo do caminho crítico...');

                // Obter dados atuais do Gantt
                const tasks = gantt.getTaskByTime();
                const links = gantt.getLinks();

                if (tasks.length === 0) {
                    alert('⚠️ Nenhuma tarefa encontrada! Carregue dados primeiro.');
                    return;
                }

                // Calcular caminho crítico
                const result = findCriticalPath(tasks, links);

                if (result.criticalTasks.length === 0) {
                    alert('ℹ️ Nenhuma tarefa crítica encontrada. Todas as tarefas têm folga.');
                    return;
                }

                // Marcar tarefas como críticas
                result.criticalTasks.forEach(taskId => {
                    const task = gantt.getTask(taskId);
                    if (task) {
                        task.$critical = true;
                        gantt.updateTask(taskId);
                    }
                });

                // Atualizar visualização
                gantt.render();

                // Mostrar resumo
                const criticalCount = result.criticalTasks.length;
                const totalTasks = tasks.length;
                const projectDuration = result.projectDuration;

                alert(`🔥 CAMINHO CRÍTICO CALCULADO!\n\n` +
                      `📊 Tarefas críticas: ${criticalCount} de ${totalTasks}\n` +
                      `⏱️ Duração do projeto: ${projectDuration} dias\n\n` +
                      `As tarefas críticas estão destacadas em vermelho com animação.`);

                // Log detalhado no console
                console.log('🎉 Caminho crítico calculado e destacado com sucesso!');

            } catch (error) {
                console.error('❌ Erro ao calcular caminho crítico:', error);
                alert('❌ Erro ao calcular caminho crítico. Verifique o console para detalhes.');
            }
        }
    </script>
</body>
</html>
