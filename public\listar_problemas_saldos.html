<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Lista de Problemas de Saldo</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .problems-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }
        .problems-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #e74c3c;
        }
        .summary-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .problems-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin: 25px 0;
        }
        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
            font-weight: bold;
            color: #495057;
        }
        .table-content {
            max-height: 600px;
            overflow-y: auto;
        }
        .problem-item {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
        }
        .problem-item:hover {
            background-color: #f8f9fa;
        }
        .problem-item:last-child {
            border-bottom: none;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .item-code {
            font-size: 1.2em;
            font-weight: bold;
            color: #e74c3c;
        }
        .item-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-undefined {
            background: #f8d7da;
            color: #721c24;
        }
        .status-negative {
            background: #fff3cd;
            color: #856404;
        }
        .item-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
            font-size: 14px;
        }
        .detail-group {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
        }
        .detail-label {
            font-weight: bold;
            color: #6c757d;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .detail-value {
            color: #495057;
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        .filter-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        .filter-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }
        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }
        .filter-input:focus, .filter-select:focus {
            outline: none;
            border-color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="problems-container">
        <div class="problems-header">
            <h1>📋 Lista de Problemas de Saldo</h1>
            <p>Análise detalhada dos itens com saldos undefined ou negativos</p>
            <p><strong>Status:</strong> <span id="statusGeral">Pronto para análise</span></p>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <h3>🎛️ Análises Disponíveis</h3>
            <button onclick="analisarTodosProblemas()" class="btn btn-primary">📋 Analisar Todos os Problemas</button>
            <button onclick="analisarSaldosUndefined()" class="btn btn-warning">❓ Apenas Saldos Undefined</button>
            <button onclick="analisarSaldosNegativos()" class="btn btn-warning">➖ Apenas Saldos Negativos</button>
            <button onclick="exportarRelatorio()" class="btn btn-success">📄 Exportar Relatório</button>
            <button onclick="limparResultados()" class="btn btn-secondary">🗑️ Limpar</button>
        </div>

        <!-- Filtros -->
        <div class="filter-section">
            <h4>🔍 Filtros</h4>
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">Buscar por Código:</label>
                    <input type="text" id="filterCodigo" class="filter-input" placeholder="Digite o código..." onkeyup="aplicarFiltros()">
                </div>
                <div class="filter-group">
                    <label class="filter-label">Tipo de Problema:</label>
                    <select id="filterTipo" class="filter-select" onchange="aplicarFiltros()">
                        <option value="">Todos</option>
                        <option value="undefined">Saldo Undefined</option>
                        <option value="negative">Saldo Negativo</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Local:</label>
                    <select id="filterLocal" class="filter-select" onchange="aplicarFiltros()">
                        <option value="">Todos</option>
                        <option value="qualidade">Qualidade</option>
                        <option value="estoque">Estoque</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Resumo -->
        <div class="summary-cards" id="summaryCards" style="display: none;">
            <div class="summary-card">
                <div class="summary-number" id="totalProblemas">0</div>
                <div>Total de Problemas</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="undefinedCount">0</div>
                <div>Saldos Undefined</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="negativeCount">0</div>
                <div>Saldos Negativos</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="qualidadeProblemas">0</div>
                <div>Problemas na Qualidade</div>
            </div>
        </div>

        <!-- Tabela de Problemas -->
        <div class="problems-table" id="problemsTable" style="display: none;">
            <div class="table-header">
                <h3>📋 Itens com Problemas Identificados</h3>
            </div>
            <div class="table-content" id="problemsList">
                <!-- Problemas serão listados aqui -->
            </div>
        </div>

        <!-- Log -->
        <div class="log-area" id="logArea"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let allProblems = [];
        let filteredProblems = [];

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateSummary(problems) {
            const undefinedCount = problems.filter(p => p.tipo === 'undefined').length;
            const negativeCount = problems.filter(p => p.tipo === 'negative').length;
            const qualidadeProblemas = problems.filter(p => p.local === 'qualidade').length;

            document.getElementById('totalProblemas').textContent = problems.length;
            document.getElementById('undefinedCount').textContent = undefinedCount;
            document.getElementById('negativeCount').textContent = negativeCount;
            document.getElementById('qualidadeProblemas').textContent = qualidadeProblemas;

            document.getElementById('summaryCards').style.display = 'grid';
        }

        function renderProblems(problems) {
            const problemsList = document.getElementById('problemsList');
            
            if (problems.length === 0) {
                problemsList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <h3>Nenhum problema encontrado!</h3>
                        <p>Todos os saldos estão corretos.</p>
                    </div>
                `;
                return;
            }

            let html = '';
            problems.forEach(problem => {
                const statusClass = problem.tipo === 'undefined' ? 'status-undefined' : 'status-negative';
                const statusText = problem.tipo === 'undefined' ? 'Undefined' : 'Negativo';
                
                html += `
                    <div class="problem-item">
                        <div class="item-header">
                            <div class="item-code">${problem.codigo} - ${problem.descricao}</div>
                            <div class="item-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="item-details">
                            <div class="detail-group">
                                <div class="detail-label">Local</div>
                                <div class="detail-value">${problem.local === 'qualidade' ? '✅ Qualidade' : '🏪 Estoque'}</div>
                            </div>
                            <div class="detail-group">
                                <div class="detail-label">Saldo Atual</div>
                                <div class="detail-value">${problem.saldo === undefined ? '❌ undefined' : problem.saldo + ' ' + (problem.unidade || 'UN')}</div>
                            </div>
                            <div class="detail-group">
                                <div class="detail-label">ID do Registro</div>
                                <div class="detail-value">${problem.registroId}</div>
                            </div>
                        </div>
                        ${problem.dataEntrada ? `
                            <div style="margin-top: 10px; font-size: 12px; color: #6c757d;">
                                <strong>Data de Entrada:</strong> ${new Date(problem.dataEntrada.seconds * 1000).toLocaleString()}
                            </div>
                        ` : ''}
                        ${problem.ultimaMovimentacao ? `
                            <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">
                                <strong>Última Movimentação:</strong> ${new Date(problem.ultimaMovimentacao.seconds * 1000).toLocaleString()}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            problemsList.innerHTML = html;
            document.getElementById('problemsTable').style.display = 'block';
        }

        window.analisarTodosProblemas = async function() {
            log('📋 Iniciando análise completa de problemas...', 'info');
            document.getElementById('statusGeral').textContent = 'Analisando problemas...';

            try {
                allProblems = [];

                // Buscar produtos para referência
                log('Carregando produtos...', 'info');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtosMap = {};
                produtos.forEach(p => produtosMap[p.id] = p);

                // Analisar qualidade
                log('Analisando estoque qualidade...', 'info');
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                qualidade.forEach(item => {
                    const produto = produtosMap[item.produtoId];
                    if (!produto) return;

                    if (item.saldo === undefined || item.saldo === null) {
                        allProblems.push({
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            unidade: produto.unidade,
                            saldo: item.saldo,
                            tipo: 'undefined',
                            local: 'qualidade',
                            registroId: item.id,
                            produtoId: item.produtoId,
                            dataEntrada: item.dataEntrada,
                            status: item.status
                        });
                    } else if (item.saldo < 0) {
                        allProblems.push({
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            unidade: produto.unidade,
                            saldo: item.saldo,
                            tipo: 'negative',
                            local: 'qualidade',
                            registroId: item.id,
                            produtoId: item.produtoId,
                            dataEntrada: item.dataEntrada,
                            status: item.status
                        });
                    }
                });

                // Analisar estoque
                log('Analisando estoques...', 'info');
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                estoques.forEach(item => {
                    const produto = produtosMap[item.produtoId];
                    if (!produto) return;

                    if (item.saldo === undefined || item.saldo === null) {
                        allProblems.push({
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            unidade: produto.unidade,
                            saldo: item.saldo,
                            tipo: 'undefined',
                            local: 'estoque',
                            registroId: item.id,
                            produtoId: item.produtoId,
                            ultimaMovimentacao: item.ultimaMovimentacao,
                            armazemId: item.armazemId
                        });
                    } else if (item.saldo < 0) {
                        allProblems.push({
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            unidade: produto.unidade,
                            saldo: item.saldo,
                            tipo: 'negative',
                            local: 'estoque',
                            registroId: item.id,
                            produtoId: item.produtoId,
                            ultimaMovimentacao: item.ultimaMovimentacao,
                            armazemId: item.armazemId
                        });
                    }
                });

                // Ordenar por código
                allProblems.sort((a, b) => a.codigo.localeCompare(b.codigo));
                filteredProblems = [...allProblems];

                updateSummary(allProblems);
                renderProblems(allProblems);

                log(`✅ Análise concluída: ${allProblems.length} problemas encontrados`, allProblems.length > 0 ? 'warning' : 'success');
                document.getElementById('statusGeral').textContent = `${allProblems.length} problemas encontrados`;

            } catch (error) {
                log(`❌ Erro na análise: ${error.message}`, 'error');
                document.getElementById('statusGeral').textContent = 'Erro na análise';
            }
        };

        window.analisarSaldosUndefined = async function() {
            log('❓ Analisando apenas saldos undefined...', 'info');
            await analisarTodosProblemas();

            const undefinedProblems = allProblems.filter(p => p.tipo === 'undefined');
            filteredProblems = undefinedProblems;

            updateSummary(undefinedProblems);
            renderProblems(undefinedProblems);

            log(`❓ ${undefinedProblems.length} saldos undefined encontrados`, 'warning');
        };

        window.analisarSaldosNegativos = async function() {
            log('➖ Analisando apenas saldos negativos...', 'info');
            await analisarTodosProblemas();

            const negativeProblems = allProblems.filter(p => p.tipo === 'negative');
            filteredProblems = negativeProblems;

            updateSummary(negativeProblems);
            renderProblems(negativeProblems);

            log(`➖ ${negativeProblems.length} saldos negativos encontrados`, 'warning');
        };

        window.aplicarFiltros = function() {
            const filterCodigo = document.getElementById('filterCodigo').value.toLowerCase();
            const filterTipo = document.getElementById('filterTipo').value;
            const filterLocal = document.getElementById('filterLocal').value;

            filteredProblems = allProblems.filter(problem => {
                const matchCodigo = !filterCodigo || problem.codigo.toLowerCase().includes(filterCodigo) || problem.descricao.toLowerCase().includes(filterCodigo);
                const matchTipo = !filterTipo || problem.tipo === filterTipo;
                const matchLocal = !filterLocal || problem.local === filterLocal;

                return matchCodigo && matchTipo && matchLocal;
            });

            updateSummary(filteredProblems);
            renderProblems(filteredProblems);

            log(`🔍 Filtros aplicados: ${filteredProblems.length} itens exibidos`, 'info');
        };

        window.exportarRelatorio = function() {
            if (filteredProblems.length === 0) {
                alert('Nenhum problema para exportar!');
                return;
            }

            let csv = 'Código,Descrição,Unidade,Saldo,Tipo Problema,Local,ID Registro,ID Produto\n';

            filteredProblems.forEach(problem => {
                csv += `"${problem.codigo}","${problem.descricao}","${problem.unidade || ''}","${problem.saldo}","${problem.tipo}","${problem.local}","${problem.registroId}","${problem.produtoId}"\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `problemas_saldos_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            log('📄 Relatório exportado com sucesso', 'success');
        };

        window.limparResultados = function() {
            allProblems = [];
            filteredProblems = [];
            document.getElementById('summaryCards').style.display = 'none';
            document.getElementById('problemsTable').style.display = 'none';
            document.getElementById('logArea').innerHTML = '';
            document.getElementById('filterCodigo').value = '';
            document.getElementById('filterTipo').value = '';
            document.getElementById('filterLocal').value = '';
            document.getElementById('statusGeral').textContent = 'Pronto para análise';
            log('🗑️ Resultados limpos', 'info');
        };

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de análise de problemas carregado', 'info');
        };
    </script>
</body>
</html>
