/**
 * UTILITÁRIOS DE DATA - SISTEMA NALITECK
 * Funções padronizadas para tratamento de datas
 * Resolve inconsistências e problemas de formato
 */

import { Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

export class DateUtils {
    
    // ===== CONVERSÕES SEGURAS =====
    
    /**
     * Converte qualquer formato de data para Firebase Timestamp
     * @param {*} date - Data em qualquer formato
     * @returns {Timestamp|null} - Firebase Timestamp ou null se inválido
     */
    static toTimestamp(date) {
        if (!date) return null;
        
        try {
            // Já é um Timestamp do Firebase
            if (date && typeof date.seconds === 'number') {
                return date;
            }
            
            // É uma string de data
            if (typeof date === 'string') {
                const parsedDate = new Date(date);
                if (isNaN(parsedDate.getTime())) return null;
                return Timestamp.fromDate(parsedDate);
            }
            
            // É um objeto Date
            if (date instanceof Date) {
                if (isNaN(date.getTime())) return null;
                return Timestamp.fromDate(date);
            }
            
            // É um número (timestamp em milliseconds)
            if (typeof date === 'number') {
                const dateObj = new Date(date);
                if (isNaN(dateObj.getTime())) return null;
                return Timestamp.fromDate(dateObj);
            }
            
            return null;
            
        } catch (error) {
            console.warn('Erro ao converter data para Timestamp:', error);
            return null;
        }
    }
    
    /**
     * Converte Timestamp para Date object
     * @param {*} timestamp - Firebase Timestamp ou qualquer formato
     * @returns {Date|null} - Date object ou null se inválido
     */
    static toDate(timestamp) {
        if (!timestamp) return null;
        
        try {
            // É um Timestamp do Firebase
            if (timestamp && typeof timestamp.seconds === 'number') {
                return new Date(timestamp.seconds * 1000);
            }
            
            // É uma string
            if (typeof timestamp === 'string') {
                const date = new Date(timestamp);
                return isNaN(date.getTime()) ? null : date;
            }
            
            // É um Date object
            if (timestamp instanceof Date) {
                return isNaN(timestamp.getTime()) ? null : timestamp;
            }
            
            // É um número
            if (typeof timestamp === 'number') {
                const date = new Date(timestamp);
                return isNaN(date.getTime()) ? null : date;
            }
            
            return null;
            
        } catch (error) {
            console.warn('Erro ao converter para Date:', error);
            return null;
        }
    }
    
    // ===== FORMATAÇÃO PARA EXIBIÇÃO =====
    
    /**
     * Formata data para exibição em formato brasileiro
     * @param {*} date - Data em qualquer formato
     * @param {boolean} includeTime - Se deve incluir horário
     * @returns {string} - Data formatada ou 'N/A'
     */
    static toDisplayDate(date, includeTime = false) {
        const dateObj = this.toDate(date);
        if (!dateObj) return 'N/A';
        
        try {
            const options = {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            };
            
            if (includeTime) {
                options.hour = '2-digit';
                options.minute = '2-digit';
                options.second = '2-digit';
            }
            
            return dateObj.toLocaleDateString('pt-BR', options);
            
        } catch (error) {
            console.warn('Erro ao formatar data:', error);
            return 'Data Inválida';
        }
    }
    
    /**
     * Formata data para input HTML (YYYY-MM-DD)
     * @param {*} date - Data em qualquer formato
     * @returns {string} - Data no formato YYYY-MM-DD ou ''
     */
    static toInputDate(date) {
        const dateObj = this.toDate(date);
        if (!dateObj) return '';
        
        try {
            return dateObj.toISOString().split('T')[0];
        } catch (error) {
            console.warn('Erro ao formatar data para input:', error);
            return '';
        }
    }
    
    // ===== VALIDAÇÕES =====
    
    /**
     * Verifica se uma data é válida
     * @param {*} date - Data para validar
     * @returns {boolean} - true se válida
     */
    static isValidDate(date) {
        if (!date) return false;
        
        const dateObj = this.toDate(date);
        return dateObj !== null;
    }
    
    /**
     * Verifica se uma data está no futuro
     * @param {*} date - Data para verificar
     * @returns {boolean} - true se no futuro
     */
    static isFutureDate(date) {
        const dateObj = this.toDate(date);
        if (!dateObj) return false;
        
        return dateObj > new Date();
    }
    
    /**
     * Verifica se uma data está no passado
     * @param {*} date - Data para verificar
     * @returns {boolean} - true se no passado
     */
    static isPastDate(date) {
        const dateObj = this.toDate(date);
        if (!dateObj) return false;
        
        return dateObj < new Date();
    }
    
    // ===== COMPARAÇÕES =====
    
    /**
     * Compara duas datas
     * @param {*} date1 - Primeira data
     * @param {*} date2 - Segunda data
     * @returns {number} - -1 se date1 < date2, 0 se iguais, 1 se date1 > date2
     */
    static compareDates(date1, date2) {
        const d1 = this.toDate(date1);
        const d2 = this.toDate(date2);
        
        if (!d1 && !d2) return 0;
        if (!d1) return -1;
        if (!d2) return 1;
        
        if (d1 < d2) return -1;
        if (d1 > d2) return 1;
        return 0;
    }
    
    /**
     * Calcula diferença em dias entre duas datas
     * @param {*} date1 - Data inicial
     * @param {*} date2 - Data final
     * @returns {number|null} - Diferença em dias ou null se inválido
     */
    static daysDifference(date1, date2) {
        const d1 = this.toDate(date1);
        const d2 = this.toDate(date2);
        
        if (!d1 || !d2) return null;
        
        const diffTime = Math.abs(d2 - d1);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    
    // ===== FILTROS DE DATA =====
    
    /**
     * Filtra array por intervalo de datas
     * @param {Array} items - Array de objetos
     * @param {string} startDate - Data inicial (YYYY-MM-DD)
     * @param {string} endDate - Data final (YYYY-MM-DD)
     * @param {string} dateField - Campo de data no objeto
     * @returns {Array} - Array filtrado
     */
    static filterByDateRange(items, startDate, endDate, dateField = 'dataCriacao') {
        if (!Array.isArray(items)) return [];
        
        return items.filter(item => {
            const itemDate = this.toDate(item[dateField]);
            if (!itemDate) return false;
            
            // Verificar data inicial
            if (startDate) {
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                if (itemDate < start) return false;
            }
            
            // Verificar data final
            if (endDate) {
                const end = new Date(endDate);
                end.setHours(23, 59, 59, 999);
                if (itemDate > end) return false;
            }
            
            return true;
        });
    }
    
    // ===== UTILITÁRIOS ESPECÍFICOS =====
    
    /**
     * Calcula data de vencimento baseada em condição de pagamento
     * @param {*} baseDate - Data base
     * @param {number} days - Dias para adicionar
     * @returns {Timestamp} - Data de vencimento
     */
    static calculateDueDate(baseDate, days = 30) {
        const date = this.toDate(baseDate) || new Date();
        const dueDate = new Date(date);
        dueDate.setDate(dueDate.getDate() + days);
        return this.toTimestamp(dueDate);
    }
    
    /**
     * Verifica se uma data está dentro do prazo
     * @param {*} targetDate - Data alvo
     * @param {number} toleranceDays - Dias de tolerância
     * @returns {object} - {isOnTime, daysLate, status}
     */
    static checkDeadline(targetDate, toleranceDays = 0) {
        const target = this.toDate(targetDate);
        if (!target) return { isOnTime: false, daysLate: null, status: 'invalid' };
        
        const now = new Date();
        const diffTime = target - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays > toleranceDays) {
            return { isOnTime: true, daysLate: 0, status: 'on_time' };
        } else if (diffDays >= -toleranceDays) {
            return { isOnTime: true, daysLate: 0, status: 'warning' };
        } else {
            return { isOnTime: false, daysLate: Math.abs(diffDays), status: 'late' };
        }
    }
    
    // ===== CONFIGURAÇÕES REGIONAIS =====
    
    /**
     * Obtém configurações de data para o Brasil
     * @returns {object} - Configurações de localização
     */
    static getBrazilianLocale() {
        return {
            locale: 'pt-BR',
            timezone: 'America/Sao_Paulo',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: 'HH:mm:ss',
            currency: 'BRL'
        };
    }
    
    // ===== LOGS E DEBUGGING =====
    
    /**
     * Log detalhado de conversão de data para debugging
     * @param {*} originalDate - Data original
     * @param {string} context - Contexto da conversão
     */
    static debugDateConversion(originalDate, context = '') {
        console.group(`🔍 Debug Date Conversion ${context ? `- ${context}` : ''}`);
        console.log('Original:', originalDate);
        console.log('Type:', typeof originalDate);
        console.log('Is Valid:', this.isValidDate(originalDate));
        console.log('To Date:', this.toDate(originalDate));
        console.log('To Timestamp:', this.toTimestamp(originalDate));
        console.log('Display:', this.toDisplayDate(originalDate));
        console.groupEnd();
    }
}

// ===== FUNÇÕES AUXILIARES GLOBAIS =====

/**
 * Função global para validar campo de data em formulários
 * @param {string} fieldId - ID do campo
 * @param {boolean} required - Se é obrigatório
 * @param {string} fieldName - Nome do campo para mensagens
 * @returns {boolean} - true se válido
 */
export function validateDateField(fieldId, required = false, fieldName = 'Data') {
    const field = document.getElementById(fieldId);
    if (!field) return false;
    
    const value = field.value;
    
    if (!value && required) {
        alert(`${fieldName} é obrigatório`);
        field.focus();
        return false;
    }
    
    if (value && !DateUtils.isValidDate(value)) {
        alert(`${fieldName} possui formato inválido`);
        field.focus();
        return false;
    }
    
    return true;
}

/**
 * Função global para configurar filtros de data
 * @param {string} startDateId - ID do campo data inicial
 * @param {string} endDateId - ID do campo data final
 */
export function setupDateFilters(startDateId, endDateId) {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startField = document.getElementById(startDateId);
    const endField = document.getElementById(endDateId);
    
    if (startField) {
        startField.value = DateUtils.toInputDate(firstDayOfMonth);
    }
    
    if (endField) {
        endField.value = DateUtils.toInputDate(today);
    }
}

// Exportar como padrão
export default DateUtils;
