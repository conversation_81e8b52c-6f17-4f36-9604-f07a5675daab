/**
 * SERVIÇO DE AUDITORIA COMPLETO - WIZAR ERP
 * Implementa logs detalhados de auditoria para todas as operações críticas
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    addDoc,
    getDocs,
    query,
    where,
    orderBy,
    limit,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

import { AuthService } from './auth-service.js';
import { ValidationService } from './validation-service.js';

export class AuditService {
    
    // Tipos de eventos auditáveis
    static EVENT_TYPES = {
        // Autenticação e Segurança
        LOGIN: 'LOGIN',
        LOGOUT: 'LOGOUT',
        LOGIN_FAILED: 'LOGIN_FAILED',
        PASSWORD_CHANGE: 'PASSWORD_CHANGE',
        PERMISSION_DENIED: 'PERMISSION_DENIED',
        
        // Compras
        PURCHASE_REQUEST_CREATED: 'PURCHASE_REQUEST_CREATED',
        PURCHASE_REQUEST_MODIFIED: 'PURCHASE_REQUEST_MODIFIED',
        PURCHASE_REQUEST_APPROVED: 'PURCHASE_REQUEST_APPROVED',
        PURCHASE_REQUEST_REJECTED: 'PURCHASE_REQUEST_REJECTED',
        PURCHASE_ORDER_CREATED: 'PURCHASE_ORDER_CREATED',
        PURCHASE_ORDER_MODIFIED: 'PURCHASE_ORDER_MODIFIED',
        QUOTATION_CREATED: 'QUOTATION_CREATED',
        QUOTATION_MODIFIED: 'QUOTATION_MODIFIED',
        
        // Estoque
        STOCK_ENTRY: 'STOCK_ENTRY',
        STOCK_EXIT: 'STOCK_EXIT',
        STOCK_TRANSFER: 'STOCK_TRANSFER',
        STOCK_ADJUSTMENT: 'STOCK_ADJUSTMENT',
        MATERIAL_RECEIVED: 'MATERIAL_RECEIVED',
        
        // Financeiro
        PRICE_CHANGE: 'PRICE_CHANGE',
        DISCOUNT_APPLIED: 'DISCOUNT_APPLIED',
        BUDGET_EXCEEDED: 'BUDGET_EXCEEDED',
        PAYMENT_PROCESSED: 'PAYMENT_PROCESSED',
        
        // Sistema
        DATA_EXPORT: 'DATA_EXPORT',
        DATA_IMPORT: 'DATA_IMPORT',
        BACKUP_CREATED: 'BACKUP_CREATED',
        SYSTEM_CONFIG_CHANGED: 'SYSTEM_CONFIG_CHANGED',
        
        // Produção
        PRODUCTION_ORDER_CREATED: 'PRODUCTION_ORDER_CREATED',
        PRODUCTION_ORDER_STARTED: 'PRODUCTION_ORDER_STARTED',
        PRODUCTION_ORDER_COMPLETED: 'PRODUCTION_ORDER_COMPLETED',
        MATERIAL_CONSUMED: 'MATERIAL_CONSUMED'
    };
    
    // Níveis de criticidade
    static SEVERITY_LEVELS = {
        LOW: 'LOW',
        MEDIUM: 'MEDIUM',
        HIGH: 'HIGH',
        CRITICAL: 'CRITICAL'
    };
    
    /**
     * 📝 REGISTRAR EVENTO DE AUDITORIA
     */
    static async logEvent(eventType, data, options = {}) {
        try {
            // Validar tipo de evento
            if (!Object.values(this.EVENT_TYPES).includes(eventType)) {
                console.warn(`Tipo de evento não reconhecido: ${eventType}`);
            }
            
            // Obter usuário atual
            const currentUser = await AuthService.getCurrentUser();
            
            // Preparar dados do evento
            const auditData = {
                eventType,
                severity: options.severity || this.SEVERITY_LEVELS.MEDIUM,
                module: options.module || 'SYSTEM',
                userId: currentUser?.id || 'anonymous',
                userName: currentUser?.nome || 'Sistema',
                userLevel: currentUser?.nivel || 0,
                sessionId: options.sessionId || this.getSessionId(),
                ip: await AuthService.getUserIP(),
                userAgent: navigator.userAgent,
                timestamp: Timestamp.now(),
                data: this.sanitizeAuditData(data),
                metadata: {
                    url: window.location.href,
                    referrer: document.referrer,
                    screenResolution: `${screen.width}x${screen.height}`,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    language: navigator.language
                }
            };
            
            // Adicionar dados específicos baseados no tipo de evento
            this.enrichEventData(auditData, eventType, data);
            
            // Salvar no banco
            const auditRef = await addDoc(collection(db, "auditoria"), auditData);
            
            // Log crítico também vai para coleção separada
            if (auditData.severity === this.SEVERITY_LEVELS.CRITICAL) {
                await addDoc(collection(db, "criticalAuditLogs"), {
                    ...auditData,
                    auditId: auditRef.id
                });
            }
            
            return auditRef.id;
            
        } catch (error) {
            console.error('Erro ao registrar evento de auditoria:', error);
            // Não falhar a operação principal por erro de auditoria
            return null;
        }
    }
    
    /**
     * 🧹 SANITIZAR DADOS DE AUDITORIA
     */
    static sanitizeAuditData(data) {
        if (!data || typeof data !== 'object') {
            return data;
        }
        
        const sanitized = {};
        
        for (const [key, value] of Object.entries(data)) {
            // Remover campos sensíveis
            if (['senha', 'password', 'token', 'secret', 'key'].includes(key.toLowerCase())) {
                sanitized[key] = '[REDACTED]';
                continue;
            }
            
            if (typeof value === 'string') {
                sanitized[key] = ValidationService.sanitizeString(value);
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeAuditData(value);
            } else {
                sanitized[key] = value;
            }
        }
        
        return sanitized;
    }
    
    /**
     * 🔍 ENRIQUECER DADOS DO EVENTO
     */
    static enrichEventData(auditData, eventType, originalData) {
        switch (eventType) {
            case this.EVENT_TYPES.PURCHASE_REQUEST_CREATED:
            case this.EVENT_TYPES.PURCHASE_REQUEST_MODIFIED:
                auditData.businessData = {
                    requestId: originalData.requestId,
                    totalValue: originalData.totalValue,
                    itemCount: originalData.itemCount,
                    department: originalData.department,
                    costCenter: originalData.costCenter
                };
                break;
                
            case this.EVENT_TYPES.STOCK_ENTRY:
            case this.EVENT_TYPES.STOCK_EXIT:
                auditData.businessData = {
                    productId: originalData.productId,
                    productCode: originalData.productCode,
                    quantity: originalData.quantity,
                    unitValue: originalData.unitValue,
                    warehouseId: originalData.warehouseId,
                    documentType: originalData.documentType,
                    documentNumber: originalData.documentNumber
                };
                break;
                
            case this.EVENT_TYPES.PRICE_CHANGE:
                auditData.businessData = {
                    productId: originalData.productId,
                    oldPrice: originalData.oldPrice,
                    newPrice: originalData.newPrice,
                    changePercentage: originalData.changePercentage,
                    reason: originalData.reason
                };
                auditData.severity = this.SEVERITY_LEVELS.HIGH;
                break;
                
            case this.EVENT_TYPES.LOGIN_FAILED:
                auditData.severity = this.SEVERITY_LEVELS.HIGH;
                auditData.securityData = {
                    attemptedEmail: originalData.email,
                    failureReason: originalData.reason,
                    consecutiveFailures: originalData.consecutiveFailures
                };
                break;
                
            case this.EVENT_TYPES.PERMISSION_DENIED:
                auditData.severity = this.SEVERITY_LEVELS.HIGH;
                auditData.securityData = {
                    attemptedAction: originalData.action,
                    requiredPermission: originalData.permission,
                    userLevel: originalData.userLevel,
                    requiredLevel: originalData.requiredLevel
                };
                break;
        }
    }
    
    /**
     * 📊 CONSULTAR LOGS DE AUDITORIA
     */
    static async queryAuditLogs(filters = {}) {
        try {
            let auditQuery = collection(db, "auditoria");
            
            // Aplicar filtros
            if (filters.eventType) {
                auditQuery = query(auditQuery, where("eventType", "==", filters.eventType));
            }
            
            if (filters.userId) {
                auditQuery = query(auditQuery, where("userId", "==", filters.userId));
            }
            
            if (filters.module) {
                auditQuery = query(auditQuery, where("module", "==", filters.module));
            }
            
            if (filters.severity) {
                auditQuery = query(auditQuery, where("severity", "==", filters.severity));
            }
            
            if (filters.startDate) {
                auditQuery = query(auditQuery, where("timestamp", ">=", Timestamp.fromDate(filters.startDate)));
            }
            
            if (filters.endDate) {
                auditQuery = query(auditQuery, where("timestamp", "<=", Timestamp.fromDate(filters.endDate)));
            }
            
            // Ordenar por timestamp decrescente
            auditQuery = query(auditQuery, orderBy("timestamp", "desc"));
            
            // Limitar resultados se especificado
            if (filters.limit) {
                auditQuery = query(auditQuery, limit(filters.limit));
            }
            
            const auditSnapshot = await getDocs(auditQuery);
            const logs = [];
            
            auditSnapshot.docs.forEach(doc => {
                const data = doc.data();
                logs.push({
                    id: doc.id,
                    ...data,
                    timestamp: data.timestamp.toDate()
                });
            });
            
            return logs;
            
        } catch (error) {
            console.error('Erro ao consultar logs de auditoria:', error);
            throw error;
        }
    }
    
    /**
     * 🚨 DETECTAR ATIVIDADES SUSPEITAS
     */
    static async detectSuspiciousActivity(userId, timeWindowHours = 24) {
        try {
            const startTime = new Date(Date.now() - (timeWindowHours * 60 * 60 * 1000));
            
            const suspiciousQuery = query(
                collection(db, "auditoria"),
                where("userId", "==", userId),
                where("timestamp", ">=", Timestamp.fromDate(startTime)),
                orderBy("timestamp", "desc")
            );
            
            const logs = await getDocs(suspiciousQuery);
            const activities = logs.docs.map(doc => doc.data());
            
            const suspiciousPatterns = [];
            
            // Detectar múltiplas tentativas de login falhadas
            const failedLogins = activities.filter(a => a.eventType === this.EVENT_TYPES.LOGIN_FAILED);
            if (failedLogins.length >= 5) {
                suspiciousPatterns.push({
                    type: 'MULTIPLE_FAILED_LOGINS',
                    count: failedLogins.length,
                    severity: this.SEVERITY_LEVELS.HIGH,
                    description: `${failedLogins.length} tentativas de login falhadas em ${timeWindowHours}h`
                });
            }
            
            // Detectar múltiplas negações de permissão
            const permissionDenials = activities.filter(a => a.eventType === this.EVENT_TYPES.PERMISSION_DENIED);
            if (permissionDenials.length >= 10) {
                suspiciousPatterns.push({
                    type: 'MULTIPLE_PERMISSION_DENIALS',
                    count: permissionDenials.length,
                    severity: this.SEVERITY_LEVELS.MEDIUM,
                    description: `${permissionDenials.length} negações de permissão em ${timeWindowHours}h`
                });
            }
            
            // Detectar atividade fora do horário normal
            const offHoursActivity = activities.filter(a => {
                const hour = a.timestamp.toDate().getHours();
                return hour < 6 || hour > 22; // Fora do horário 6h-22h
            });
            
            if (offHoursActivity.length >= 20) {
                suspiciousPatterns.push({
                    type: 'OFF_HOURS_ACTIVITY',
                    count: offHoursActivity.length,
                    severity: this.SEVERITY_LEVELS.MEDIUM,
                    description: `${offHoursActivity.length} atividades fora do horário normal`
                });
            }
            
            // Detectar mudanças de preço frequentes
            const priceChanges = activities.filter(a => a.eventType === this.EVENT_TYPES.PRICE_CHANGE);
            if (priceChanges.length >= 50) {
                suspiciousPatterns.push({
                    type: 'FREQUENT_PRICE_CHANGES',
                    count: priceChanges.length,
                    severity: this.SEVERITY_LEVELS.HIGH,
                    description: `${priceChanges.length} mudanças de preço em ${timeWindowHours}h`
                });
            }
            
            return {
                userId,
                timeWindow: timeWindowHours,
                totalActivities: activities.length,
                suspiciousPatterns,
                riskLevel: this.calculateRiskLevel(suspiciousPatterns)
            };
            
        } catch (error) {
            console.error('Erro ao detectar atividades suspeitas:', error);
            throw error;
        }
    }
    
    /**
     * ⚠️ CALCULAR NÍVEL DE RISCO
     */
    static calculateRiskLevel(patterns) {
        if (patterns.length === 0) return 'LOW';
        
        const highSeverityCount = patterns.filter(p => p.severity === this.SEVERITY_LEVELS.HIGH).length;
        const criticalSeverityCount = patterns.filter(p => p.severity === this.SEVERITY_LEVELS.CRITICAL).length;
        
        if (criticalSeverityCount > 0) return 'CRITICAL';
        if (highSeverityCount >= 2) return 'HIGH';
        if (patterns.length >= 3) return 'MEDIUM';
        
        return 'LOW';
    }
    
    /**
     * 🆔 OBTER ID DA SESSÃO
     */
    static getSessionId() {
        let sessionId = sessionStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }
}
