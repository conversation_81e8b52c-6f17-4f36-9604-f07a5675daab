<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Apontamento de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }
    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
      padding: 20px;
    }
    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
    }
    .header h1 {
      font-size: 24px;
      margin: 0;
    }
    .search-bar {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 25px;
    }
    .form-col {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #495057;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-col label {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-col label::before {
      content: '•';
      color: #0854a0;
      font-weight: bold;
      font-size: 16px;
    }

    input, select, textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      background-color: white;
      margin-bottom: 8px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #0854a0;
      box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
      transform: translateY(-1px);
    }

    textarea {
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      background: var(--primary-color);
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      font-weight: 500;
    }
    button:hover {
      background: var(--primary-hover);
    }
    .back-button {
      background-color: var(--header-bg);
      color: white;
      border: none;
    }
    .back-button:hover {
      background-color: #2a3b4d;
    }
    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      font-size: 13px;
    }
    .orders-table th, .orders-table td {
      padding: 10px 8px;
      border: 1px solid var(--border-color);
      text-align: left;
    }
    .orders-table th {
      background: var(--secondary-color);
      font-weight: 600;
      color: var(--primary-color);
    }
    .orders-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .orders-table tr:hover {
      background-color: #e6f2ff;
    }
    .status-badge {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-pendente { background: #fff4cc; color: #8c6c00; border: 1px solid #ffd43b; }
    .status-em-producao { background: #e5f2f9; color: #0854a0; border: 1px solid #0854a0; }
    .status-concluida { background: #e8f3e8; color: #107e3e; border: 1px solid #107e3e; }
    .status-cancelada { background: #ffeaea; color: #bb0000; border: 1px solid #bb0000; }
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(5px);
      animation: modalFadeIn 0.3s ease;
    }

    @keyframes modalFadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .modal-content {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 900px;
      border-radius: 16px;
      position: relative;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      animation: modalSlideIn 0.4s ease;
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      flex-direction: column;
    }
    .modal-header {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
      color: white;
      padding: 25px 30px;
      border-radius: 16px 16px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .modal-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }

    .modal-header h2 {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;
      z-index: 1;
    }

    .modal-header h2::before {
      content: '🏭';
      font-size: 28px;
      opacity: 0.9;
    }
    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: 30px;
      max-height: calc(85vh - 150px);
      background: white;
      position: relative;
    }

    /* Seção de informações da ordem */
    #orderInfo {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 25px;
      border-left: 4px solid #0854a0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    #orderInfo h3 {
      color: #0854a0;
      margin: 0 0 15px 0;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    #orderInfo h3::before {
      content: '📋';
      font-size: 20px;
    }

    .order-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }

    .order-detail-item {
      background: white;
      padding: 12px 15px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .order-detail-label {
      font-size: 12px;
      color: #6c757d;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
    }

    .order-detail-value {
      font-size: 16px;
      color: #212529;
      font-weight: 600;
    }
    .modal-footer {
      padding: 25px 30px;
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      text-align: right;
      border-radius: 0 0 16px 16px;
    }

    .modal-footer button {
      background: linear-gradient(135deg, #107e3e, #0d6e36);
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(16, 126, 62, 0.3);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .modal-footer button:hover {
      background: linear-gradient(135deg, #0d6e36, #0a5a2e);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(16, 126, 62, 0.4);
    }

    .modal-footer button:disabled {
      background: linear-gradient(135deg, #6c757d, #5a6268);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .close-button {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      position: relative;
      z-index: 2;
    }

    .close-button:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: rotate(90deg) scale(1.1);
    }
    .materials-list {
      max-height: 400px;
      overflow-y: auto;
      margin: 25px 0;
      padding: 0;
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .materials-list h4 {
      background: linear-gradient(135deg, #6f42c1, #5a32a3);
      color: white;
      margin: 0;
      padding: 15px 20px;
      border-radius: 12px 12px 0 0;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .materials-list h4::before {
      content: '📦';
      font-size: 18px;
    }

    .materials-container {
      padding: 15px;
    }

    .material-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background: white;
      margin-bottom: 10px;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .material-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .material-item:last-child {
      margin-bottom: 0;
    }

    .material-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .material-code {
      font-weight: 600;
      color: #0854a0;
      font-size: 14px;
    }

    .material-description {
      color: #495057;
      font-size: 13px;
      line-height: 1.4;
    }

    .material-status {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      font-weight: 600;
    }

    .status-ok {
      color: #107e3e;
    }

    .status-warning {
      color: #e9730c;
    }

    .status-error {
      color: #bb0000;
    }

    .material-quantities {
      text-align: right;
      font-size: 12px;
      color: #6c757d;
      line-height: 1.4;
    }

    .generate-stock-btn {
      background: linear-gradient(135deg, #e9730c, #d66a0b);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .generate-stock-btn:hover {
      background: linear-gradient(135deg, #d66a0b, #c4600a);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(233, 115, 12, 0.3);
    }

    /* Estilos específicos para modal de materiais faltantes */
    #modalMateriaisFaltantes .modal-content {
      box-shadow: 0 20px 60px rgba(220, 53, 69, 0.15);
      border: 2px solid #dc3545;
    }

    #modalMateriaisFaltantes .modal-header {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    #modalMateriaisFaltantes .close {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    #modalMateriaisFaltantes .close:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: rotate(90deg) scale(1.1);
    }

    #modalMateriaisFaltantes table tbody tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    #modalMateriaisFaltantes table tbody tr:hover {
      background-color: #fff3cd;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    #modalMateriaisFaltantes button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* Animação para linhas da tabela */
    #modalMateriaisFaltantes tbody tr {
      animation: slideInRow 0.3s ease-out;
    }

    @keyframes slideInRow {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    /* Estilo para valores em falta */
    #modalMateriaisFaltantes .falta-valor {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: bold;
      font-size: 12px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }
    .material-status {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-ok { background: #e8f3e8; color: #107e3e; border: 1px solid #107e3e; }
    .status-warning { background: #fff4cc; color: #8c6c00; border: 1px solid #ffd43b; }
    .status-error { background: #ffeaea; color: #bb0000; border: 1px solid #bb0000; }
    .progress-bar {
      width: 100%;
      height: 16px;
      background-color: var(--secondary-color);
      border-radius: 8px;
      overflow: hidden;
      margin-top: 5px;
      border: 1px solid var(--border-color);
    }
    .progress-fill {
      height: 100%;
      background-color: var(--success-color);
      transition: width 0.3s ease;
    }
    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* Estilos para notificações */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 2000;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      background: linear-gradient(135deg, #107e3e, #0d6e36);
    }

    .notification.warning {
      background: linear-gradient(135deg, #e9730c, #d66a0b);
    }

    .notification.error {
      background: linear-gradient(135deg, #bb0000, #a30000);
    }

    .notification.info {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
    }

    .metric-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .metric-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
  </style>

    <script type="module" src="js/main.js"></script>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Apontamento de Produção</h1>
      <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
    </div>

    <!-- Dashboard de Métricas -->
    <div class="dashboard-metrics" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
      <div class="metric-card" style="background: linear-gradient(135deg, #0854a0, #0a4d8c); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="totalOPs">0</div>
        <div style="font-size: 12px; opacity: 0.9;">OPs Ativas</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #107e3e, #0d6e36); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="emProducao">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Em Produção</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #e9730c, #d66a0b); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="materiaisFalta">0</div>
        <div style="font-size: 12px; opacity: 0.9;">Materiais em Falta</div>
      </div>
      <div class="metric-card" style="background: linear-gradient(135deg, #bb0000, #a30000); color: white; padding: 15px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; font-weight: bold;" id="opsAtrasadas">0</div>
        <div style="font-size: 12px; opacity: 0.9;">OPs Atrasadas</div>
      </div>
    </div>

    <div class="search-bar">
      <div class="form-row">
        <div class="form-col">
          <input type="text" id="searchInput" placeholder="Buscar por número da ordem ou produto..." oninput="filterOrders()">
        </div>
        <div class="form-col">
          <select id="statusFilter" onchange="filterOrders()">
            <option value="">Todos os status</option>
            <option value="Pendente">Pendente</option>
            <option value="Em Produção">Em Produção</option>
          </select>
        </div>
        <div class="form-col">
          <button onclick="atualizarDashboard()" style="background: var(--success-color);">
            <i class="fas fa-sync-alt"></i> Atualizar Métricas
          </button>
        </div>
      </div>
    </div>

    <table class="orders-table">
      <thead>
        <tr>
          <th>Ordem</th>
          <th>Produto</th>
          <th>Quantidade</th>
          <th>Produzido</th>
          <th>Status</th>
          <th>Data Entrega</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="ordersTableBody">
      </tbody>
    </table>
  </div>

  <div id="appointmentModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Apontamento de Produção</h2>
        <span class="close-button" onclick="closeModal()">×</span>
      </div>

      <div class="modal-body">
        <div id="orderInfo"></div>

        <div id="materialsList" class="materials-list">
          <h4>Materiais Necessários</h4>
          <div class="materials-container"></div>
        </div>

        <form id="appointmentForm" onsubmit="submitAppointment(event)">
          <div class="form-row">
            <div class="form-col">
              <label for="quantity">Quantidade Produzida</label>
              <input type="number" id="quantity" min="0.001" step="0.001" required placeholder="Digite a quantidade produzida">
            </div>
            <div class="form-col">
              <label for="scrap">Quantidade de Refugo</label>
              <input type="number" id="scrap" min="0" step="0.001" value="0" placeholder="Digite a quantidade de refugo">
            </div>
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="observations">Observações</label>
              <textarea id="observations" rows="3" placeholder="Digite observações sobre a produção (opcional)"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="submit" form="appointmentForm" id="submitButton">
          <i class="fas fa-check"></i> Confirmar Apontamento
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de Materiais Faltantes -->
  <div id="modalMateriaisFaltantes" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
      <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border-radius: 8px 8px 0 0;">
        <h2><i class="fas fa-exclamation-triangle"></i> Materiais Insuficientes para Impressão</h2>
        <span class="close" onclick="fecharModalMateriaisFaltantes()" style="color: white; font-size: 28px;">&times;</span>
      </div>

      <div class="modal-body" style="padding: 0;">
        <!-- Seção: Informações da OP -->
        <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6;">
          <div style="display: flex; align-items: center; gap: 15px;">
            <div style="background: #dc3545; color: white; padding: 10px; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
              <i class="fas fa-industry" style="font-size: 20px;"></i>
            </div>
            <div>
              <h3 style="margin: 0; color: #495057;">Ordem de Produção: <span id="numeroOPFaltantes" style="color: #dc3545; font-weight: bold;"></span></h3>
              <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">Produto: <span id="produtoOPFaltantes"></span></p>
            </div>
          </div>
        </div>

        <!-- Seção: Alerta -->
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px; border-radius: 8px;">
          <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-exclamation-triangle" style="color: #856404; font-size: 20px;"></i>
            <div>
              <h4 style="margin: 0; color: #856404;">⚠️ Atenção: Estoque Insuficiente</h4>
              <p style="margin: 5px 0 0 0; color: #856404; font-size: 14px;">
                Não é possível imprimir a OP pois há materiais com estoque insuficiente.
                Transfira os materiais necessários ou ajuste o estoque antes de prosseguir.
              </p>
            </div>
          </div>
        </div>

        <!-- Seção: Lista de Materiais Faltantes -->
        <div style="padding: 20px;">
          <h4 style="color: #495057; margin-bottom: 15px;">
            <i class="fas fa-list-ul" style="color: #dc3545;"></i>
            Materiais com Estoque Insuficiente
          </h4>

          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <thead style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                <tr>
                  <th style="padding: 12px 8px; text-align: left; font-weight: 600; font-size: 12px; text-transform: uppercase;">Código</th>
                  <th style="padding: 12px 8px; text-align: left; font-weight: 600; font-size: 12px; text-transform: uppercase;">Descrição</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Tipo</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Necessário</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Disponível</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Falta</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Unidade</th>
                </tr>
              </thead>
              <tbody id="tbodyMateriaisFaltantes">
                <!-- Preenchido dinamicamente -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Seção: Ações -->
        <div style="background: #f8f9fa; padding: 20px; border-top: 1px solid #dee2e6; display: flex; gap: 15px; justify-content: flex-end;">
          <button type="button" onclick="fecharModalMateriaisFaltantes()" style="padding: 12px 24px; border-radius: 25px; font-weight: 600; background: #6c757d; color: white; border: none; cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-times"></i> Cancelar
          </button>
          <button type="button" onclick="forcarImpressaoOP()" style="padding: 12px 24px; border-radius: 25px; font-weight: 600; background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; border: none; cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-exclamation-triangle"></i> Forçar Impressão
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Área oculta para impressão -->
  <div id="printArea" style="display:none"></div>

  <!-- Modal para análise de produção viável -->
  <div id="modalAnaliseProducao" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 90%; max-height: 90%; overflow-y: auto;">
      <span class="close" onclick="fecharModalAnalise()">&times;</span>
      <h2>🎯 Análise de Produção Viável</h2>
      <div id="conteudoAnaliseProducao"></div>
      <div class="modal-actions">
        <button onclick="exportarRelatorio()" class="btn-primary">📊 Exportar Relatório</button>
        <button onclick="iniciarProducaoLote()" class="btn-success">🚀 Iniciar Lote Viável</button>
        <button onclick="fecharModalAnalise()" class="btn-secondary">Fechar</button>
      </div>
    </div>
  </div>

  <!-- Modal para diagnóstico de impossibilidade -->
  <div id="modalDiagnosticoImpossibilidade" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 95%; max-height: 95%; overflow-y: auto;">
      <span class="close" onclick="fecharModalDiagnostico()">&times;</span>
      <h2>🔍 Diagnóstico Completo de Impossibilidade</h2>
      <div id="conteudoDiagnosticoImpossibilidade"></div>
      <div class="modal-actions">
        <button onclick="exportarDiagnostico()" class="btn-primary">📊 Exportar Diagnóstico</button>
        <button onclick="executarSugestoes()" class="btn-warning">💡 Executar Sugestões</button>
        <button onclick="fecharModalDiagnostico()" class="btn-secondary">Fechar</button>
      </div>
    </div>
  </div>

  <!-- Modal para decodificador de produtos -->
  <div id="modalDecodificadorProdutos" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 90%; max-height: 90%; overflow-y: auto;">
      <span class="close" onclick="fecharModalDecodificador()">&times;</span>
      <h2>🔍 Decodificador de Produtos & Correção de Estoque</h2>
      <div id="conteudoDecodificadorProdutos">
        <div style="padding: 20px;">
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">🔍 Buscar Produto por Código Críptico</h4>
            <div style="display: flex; gap: 10px; align-items: center;">
              <input type="text" id="codigoCriptico" placeholder="Ex: 9QIS3qMMN0Ca9Bhc2R73"
                     style="flex: 1; padding: 10px; border: 1px solid #ced4da; border-radius: 4px; font-family: monospace;">
              <button onclick="buscarProdutoPorCodigo()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                🔍 Buscar
              </button>
            </div>
          </div>

          <div id="resultadoBusca" style="display: none;">
            <!-- Resultado da busca será inserido aqui -->
          </div>

          <div style="background: #e9ecef; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">📊 Produtos com Problemas de Estoque</h4>
            <div id="produtosComProblemas">
              <p style="color: #6c757d; margin: 0;">Clique em "🔍 Analisar Produtos" para identificar produtos com estoque negativo ou problemas.</p>
            </div>
            <button onclick="analisarProdutosComProblemas()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
              🔍 Analisar Produtos
            </button>
          </div>
        </div>
      </div>
      <div class="modal-actions">
        <button onclick="exportarRelatorioDecodificacao()" class="btn-primary">📊 Exportar Relatório</button>
        <button onclick="corrigirEstoquesLote()" class="btn-success">🔧 Corrigir Estoques em Lote</button>
        <button onclick="fecharModalDecodificador()" class="btn-secondary">Fechar</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { doc, getDoc, collection, onSnapshot, updateDoc, Timestamp, addDoc, writeBatch, getDocs, query, where, runTransaction } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // ===================================================================
    // SERVIÇO DE EMPENHOS - INLINE
    // ===================================================================
    class EmpenhoService {
        static async transferirReservasParaEmpenhos(ordemProducaoId) {
            console.log(`🔄 Transferindo reservas para empenhos - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                const opRef = doc(db, "ordensProducao", ordemProducaoId);
                const opDoc = await transaction.get(opRef);

                if (!opDoc.exists()) {
                    throw new Error('Ordem de produção não encontrada');
                }

                const op = opDoc.data();
                const materiaisNecessarios = op.materiaisNecessarios || [];

                let transferencias = 0;
                let erros = [];

                for (const material of materiaisNecessarios) {
                    if (!material.quantidadeReservada || material.quantidadeReservada <= 0) {
                        continue;
                    }

                    try {
                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", material.produtoId),
                            where("armazemId", "==", op.armazemProducaoId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (estoqueSnapshot.empty) {
                            erros.push(`Estoque não encontrado para produto ${material.produtoId}`);
                            continue;
                        }

                        const estoqueDoc = estoqueSnapshot.docs[0];
                        const estoque = estoqueDoc.data();

                        const quantidadeTransferir = material.quantidadeReservada;
                        const novoSaldoReservado = Math.max(0, (estoque.saldoReservado || 0) - quantidadeTransferir);
                        const novoSaldoEmpenhado = (estoque.saldoEmpenhado || 0) + quantidadeTransferir;

                        transaction.update(doc(db, "estoques", estoqueDoc.id), {
                            saldoReservado: novoSaldoReservado,
                            saldoEmpenhado: novoSaldoEmpenhado,
                            ultimaMovimentacao: Timestamp.now()
                        });

                        const empenhoRef = doc(collection(db, "empenhos"));
                        transaction.set(empenhoRef, {
                            ordemProducaoId,
                            produtoId: material.produtoId,
                            armazemId: op.armazemProducaoId,
                            quantidadeEmpenhada: quantidadeTransferir,
                            quantidadeConsumida: 0,
                            status: 'ATIVO',
                            dataEmpenho: Timestamp.now(),
                            origemReserva: true
                        });

                        transferencias++;

                    } catch (error) {
                        erros.push(`Erro no material ${material.produtoId}: ${error.message}`);
                    }
                }

                transaction.update(opRef, {
                    status: 'Em Produção',
                    dataInicioProducao: Timestamp.now(),
                    empenhosAtivos: transferencias
                });

                return { transferencias, erros, ordemProducaoId };
            });
        }

        static async consumirMaterialEmpenhado(ordemProducaoId, consumos) {
            console.log(`⚡ Consumindo materiais empenhados - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                let consumosRealizados = 0;
                let erros = [];

                for (const consumo of consumos) {
                    try {
                        const empenhoQuery = query(
                            collection(db, "empenhos"),
                            where("ordemProducaoId", "==", ordemProducaoId),
                            where("produtoId", "==", consumo.produtoId),
                            where("status", "==", "ATIVO")
                        );

                        const empenhoSnapshot = await getDocs(empenhoQuery);
                        if (empenhoSnapshot.empty) {
                            erros.push(`Empenho não encontrado para produto ${consumo.produtoId}`);
                            continue;
                        }

                        const empenhoDoc = empenhoSnapshot.docs[0];
                        const empenho = empenhoDoc.data();

                        const quantidadeDisponivel = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;
                        const quantidadeConsumir = Math.min(consumo.quantidade, quantidadeDisponivel);

                        if (quantidadeConsumir <= 0) {
                            erros.push(`Sem quantidade empenhada disponível para ${consumo.produtoId}`);
                            continue;
                        }

                        const novaQuantidadeConsumida = empenho.quantidadeConsumida + quantidadeConsumir;
                        const novoStatus = novaQuantidadeConsumida >= empenho.quantidadeEmpenhada ? 'CONSUMIDO' : 'ATIVO';

                        transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                            quantidadeConsumida: novaQuantidadeConsumida,
                            status: novoStatus,
                            ultimoConsumo: Timestamp.now()
                        });

                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", consumo.produtoId),
                            where("armazemId", "==", empenho.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldo: estoque.saldo - quantidadeConsumir,
                                saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeConsumir),
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                        transaction.set(movimentacaoRef, {
                            produtoId: consumo.produtoId,
                            armazemId: empenho.armazemId,
                            tipo: 'SAIDA',
                            quantidade: quantidadeConsumir,
                            tipoDocumento: 'CONSUMO_PRODUCAO',
                            numeroDocumento: ordemProducaoId,
                            observacoes: `Consumo OP ${ordemProducaoId} - Empenho`,
                            dataHora: Timestamp.now(),
                            empenhoId: empenhoDoc.id
                        });

                        consumosRealizados++;

                    } catch (error) {
                        erros.push(`Erro no consumo ${consumo.produtoId}: ${error.message}`);
                    }
                }

                return { consumosRealizados, erros };
            });
        }

        static async liberarEmpenhosRestantes(ordemProducaoId, motivo = 'OP_FINALIZADA') {
            console.log(`🔓 Liberando empenhos restantes - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("ordemProducaoId", "==", ordemProducaoId),
                    where("status", "==", "ATIVO")
                );

                const empenhosSnapshot = await getDocs(empenhosQuery);
                let liberacoes = 0;

                for (const empenhoDoc of empenhosSnapshot.docs) {
                    const empenho = empenhoDoc.data();
                    const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

                    if (quantidadeRestante > 0) {
                        transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                            status: 'LIBERADO',
                            quantidadeLiberada: quantidadeRestante,
                            dataLiberacao: Timestamp.now(),
                            motivoLiberacao: motivo
                        });

                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", empenho.produtoId),
                            where("armazemId", "==", empenho.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeRestante),
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        liberacoes++;
                    }
                }

                return { liberacoes, ordemProducaoId };
            });
        }

        static async consultarEmpenhosOP(ordemProducaoId) {
            const empenhosQuery = query(
                collection(db, "empenhos"),
                where("ordemProducaoId", "==", ordemProducaoId)
            );

            const empenhosSnapshot = await getDocs(empenhosQuery);
            return empenhosSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        }

        static async inicializarCampoEmpenho() {
            console.log('🔧 Inicializando campo saldoEmpenhado nos estoques...');

            const estoquesSnapshot = await getDocs(collection(db, "estoques"));
            let atualizados = 0;

            for (const estoqueDoc of estoquesSnapshot.docs) {
                const estoque = estoqueDoc.data();

                if (estoque.saldoEmpenhado === undefined) {
                    await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                        saldoEmpenhado: 0
                    });
                    atualizados++;
                }
            }

            console.log(`✅ ${atualizados} estoques atualizados com campo saldoEmpenhado`);
            return atualizados;
        }
    }

    let produtos = [];
    let ordensProducao = [];
    let estoques = [];
    let empenhos = []; // Adicionar variável empenhos
    let armazens = [];
    let estruturas = [];
    let operacoes = [];
    let recursos = [];
    let currentOrder = null;
    let permitirProducaoSemEstoque = false;
    let permitirGerarEstoqueAutomatico = false;

    window.onload = async function() {
      // Verificar se o usuário está logado
      const currentUser = JSON.parse(localStorage.getItem('currentUser'));
      if (!currentUser) {
        window.location.href = 'login.html';
        return;
      }

      await loadSystemParameters();
      setupRealTimeListeners();
    };

    async function loadSystemParameters() {
      try {
        const docSnap = await getDoc(doc(db, "parametros", "sistema"));
        if (docSnap.exists()) {
          permitirProducaoSemEstoque = docSnap.data().permitirProducaoSemEstoque || false;
          permitirGerarEstoqueAutomatico = docSnap.data().permitirGerarEstoqueAutomatico || false;
        }
      } catch (error) {
        console.error("Erro ao carregar parâmetros do sistema:", error);
      }
    }

    // ===================================================================
    // SISTEMA DE ATUALIZAÇÃO EM TEMPO REAL MELHORADO
    // ===================================================================

    let unsubscribeFunctions = []; // Para gerenciar listeners
    let lastUpdateTime = Date.now();
    let isUpdating = false;

    async function setupRealTimeListeners() {
      try {
        console.log('🔄 Configurando listeners em tempo real...');

        // Limpar listeners anteriores
        unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
        unsubscribeFunctions = [];

        // 1. LISTENER PARA PRODUTOS
        const unsubscribeProdutos = onSnapshot(collection(db, "produtos"), (snap) => {
          console.log('📦 Produtos atualizados:', snap.docs.length);
          produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('produtos');
        });
        unsubscribeFunctions.push(unsubscribeProdutos);

        // 2. LISTENER PARA ORDENS DE PRODUÇÃO (PRINCIPAL)
        const unsubscribeOPs = onSnapshot(collection(db, "ordensProducao"), (snap) => {
          console.log('🏭 Ordens de Produção atualizadas:', snap.docs.length);
          ordensProducao = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('ordensProducao');
        });
        unsubscribeFunctions.push(unsubscribeOPs);

        // 3. LISTENER PARA ESTOQUES
        const unsubscribeEstoques = onSnapshot(collection(db, "estoques"), (snap) => {
          console.log('📊 Estoques atualizados:', snap.docs.length);
          estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('estoques');
        });
        unsubscribeFunctions.push(unsubscribeEstoques);

        // 4. LISTENER PARA EMPENHOS
        const unsubscribeEmpenhos = onSnapshot(collection(db, "empenhos"), (snap) => {
          console.log('⚡ Empenhos atualizados:', snap.docs.length);
          empenhos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('empenhos');
        });
        unsubscribeFunctions.push(unsubscribeEmpenhos);

        // 5. LISTENERS PARA DADOS AUXILIARES
        const unsubscribeArmazens = onSnapshot(collection(db, "armazens"), (snap) => {
          armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeArmazens);

        const unsubscribeEstruturas = onSnapshot(collection(db, "estruturas"), (snap) => {
          estruturas = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeEstruturas);

        const unsubscribeOperacoes = onSnapshot(collection(db, "operacoes"), (snap) => {
          operacoes = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeOperacoes);

        const unsubscribeRecursos = onSnapshot(collection(db, "recursos"), (snap) => {
          recursos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeRecursos);

        console.log('✅ Listeners configurados com sucesso!');

        // Carregar dados iniciais
        await loadOrders();
        atualizarDashboard();

      } catch (error) {
        console.error("❌ Erro ao configurar listeners:", error);
        mostrarNotificacao('❌ Erro ao configurar atualização automática', 'error', 5000);
      }
    }

    // Função para debounce das atualizações (evita atualizações excessivas)
    function debounceUpdate(source) {
      if (isUpdating) return;

      const now = Date.now();
      if (now - lastUpdateTime < 500) { // Mínimo 500ms entre atualizações
        return;
      }

      lastUpdateTime = now;
      isUpdating = true;

      console.log(`🔄 Atualizando interface (fonte: ${source})`);

      setTimeout(async () => {
        try {
          await loadOrders();
          atualizarDashboard();

          // Mostrar notificação sutil de atualização
          if (source === 'ordensProducao' || source === 'estoques') {
            mostrarNotificacao('🔄 Dados atualizados automaticamente', 'info', 2000);
          }

        } catch (error) {
          console.error('Erro na atualização:', error);
        } finally {
          isUpdating = false;
        }
      }, 100);
    }

    // Função para forçar atualização manual
    window.forcarAtualizacao = async function() {
      console.log('🔄 Forçando atualização manual...');
      mostrarNotificacao('🔄 Atualizando dados...', 'info', 1000);

      try {
        await setupRealTimeListeners();
        mostrarNotificacao('✅ Dados atualizados com sucesso!', 'success', 2000);
      } catch (error) {
        console.error('Erro na atualização manual:', error);
        mostrarNotificacao('❌ Erro ao atualizar dados', 'error', 3000);
      }
    };

    // Detectar quando a aba fica ativa novamente
    document.addEventListener('visibilitychange', function() {
      if (!document.hidden) {
        console.log('👁️ Aba ficou ativa - verificando atualizações...');
        setTimeout(() => {
          debounceUpdate('visibilitychange');
        }, 1000);
      }
    });

    // Detectar quando a janela ganha foco
    window.addEventListener('focus', function() {
      console.log('🎯 Janela ganhou foco - verificando atualizações...');
      setTimeout(() => {
        debounceUpdate('focus');
      }, 500);
    });

    // ===================================================================
    // MONITORAMENTO DE CONEXÃO
    // ===================================================================

    function atualizarStatusConexao(online = true) {
      const statusIndicator = document.getElementById('connectionStatus');
      if (statusIndicator) {
        if (online) {
          statusIndicator.innerHTML = '<i class="fas fa-circle" style="color: #28a745;"></i> Online';
          statusIndicator.style.color = '#28a745';
        } else {
          statusIndicator.innerHTML = '<i class="fas fa-circle" style="color: #dc3545;"></i> Offline';
          statusIndicator.style.color = '#dc3545';
        }
      }
    }

    // Monitorar conexão com a internet
    window.addEventListener('online', function() {
      console.log('🌐 Conexão restaurada');
      atualizarStatusConexao(true);
      mostrarNotificacao('🌐 Conexão restaurada - Sincronizando dados...', 'success', 3000);
      setTimeout(() => {
        debounceUpdate('reconnect');
      }, 1000);
    });

    window.addEventListener('offline', function() {
      console.log('🚫 Conexão perdida');
      atualizarStatusConexao(false);
      mostrarNotificacao('🚫 Conexão perdida - Dados podem estar desatualizados', 'warning', 5000);
    });

    // Verificar conexão periodicamente
    setInterval(() => {
      if (navigator.onLine) {
        atualizarStatusConexao(true);
      } else {
        atualizarStatusConexao(false);
      }
    }, 30000); // Verificar a cada 30 segundos

    async function loadOrders() {
      const tableBody = document.getElementById('ordersTableBody');
      tableBody.innerHTML = '';

      const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');

      // Atualizar métricas do dashboard
      atualizarDashboard();

      ordensAtivas
        .sort((a, b) => {
          // Primeiro ordena por nível
          if (a.nivel !== b.nivel) return a.nivel - b.nivel;
          // Depois ordena por data de entrega, tratando casos onde dataEntrega pode estar ausente
          const dateA = a.dataEntrega?.seconds ? new Date(a.dataEntrega.seconds * 1000) : new Date(0);
          const dateB = b.dataEntrega?.seconds ? new Date(b.dataEntrega.seconds * 1000) : new Date(0);
          return dateB - dateA;
        })
        .forEach(ordem => {
          const produto = produtos.find(p => p.id === ordem.produtoId);
          if (!produto) return;

          const row = document.createElement('tr');
          const progress = ordem.quantidadeProduzida ? 
            (ordem.quantidadeProduzida / ordem.quantidade * 100).toFixed(1) : 0;

          // Trata a exibição da data de entrega para casos onde pode estar ausente
          const dataEntrega = ordem.dataEntrega?.seconds 
            ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString() 
            : 'Não definida';

          row.innerHTML = `
            <td>${ordem.numero}</td>
            <td>${produto.codigo} - ${produto.descricao}</td>
            <td>${ordem.quantidade} ${produto.unidade}</td>
            <td>
              ${ordem.quantidadeProduzida || 0} ${produto.unidade}
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress}%"></div>
              </div>
            </td>
            <td><span class="status-badge status-${ordem.status.toLowerCase()}">${ordem.status}</span></td>
            <td>${dataEntrega}</td>
            <td>
              ${ordem.status === 'Pendente' ? `
                <button onclick="iniciarProducao('${ordem.id}')" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; margin-right: 5px;">
                  <i class="fas fa-play"></i> Iniciar Produção
                </button>
              ` : ''}
              ${ordem.status !== 'Pendente' && ordem.status !== 'Concluída' && ordem.status !== 'Cancelada' ? `
                <button onclick="openAppointmentModal('${ordem.id}')">
                  <i class="fas fa-clipboard"></i> Apontar
                </button>
              ` : ''}
              <button onclick="printOrderReport('${ordem.id}')">
                <i class="fas fa-print"></i> Imprimir OP
              </button>
              <button onclick="consultarEmpenhosOP('${ordem.id}')" style="background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; margin-left: 5px;">
                <i class="fas fa-bolt"></i> Empenhos
              </button>
            </td>
          `;
          tableBody.appendChild(row);
        });
    }

    window.filterOrders = function() {
      const searchText = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;

      const rows = document.getElementById('ordersTableBody').getElementsByTagName('tr');

      for (const row of rows) {
        const numero = row.cells[0].textContent.toLowerCase();
        const produto = row.cells[1].textContent.toLowerCase();
        const status = row.cells[4].textContent;

        const matchesSearch = numero.includes(searchText) || produto.includes(searchText);
        const matchesStatus = !statusFilter || status === statusFilter;

        row.style.display = matchesSearch && matchesStatus ? '' : 'none';
      }
    };

    window.openAppointmentModal = async function(orderId) {
      // Atualiza os estoques antes de abrir o modal
      await new Promise(resolve => onSnapshot(collection(db, "estoques"), snap => { estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() })); resolve(); }));
      currentOrder = ordensProducao.find(op => op.id === orderId);
      if (!currentOrder) return;

      const produto = produtos.find(p => p.id === currentOrder.produtoId);
      let materialsHtml = '';
      let canProduce = true;

      // Verifica se o armazém de produção da OP é do tipo PRODUCAO
      const armazemProducao = armazens.find(a => a.id === currentOrder.armazemProducaoId);
      if (!armazemProducao || armazemProducao.tipo !== 'PRODUCAO') {
        alert('A ordem de produção deve estar associada a um armazém do tipo Produção.');
        return;
      }

      if (Array.isArray(currentOrder.materiaisNecessarios) && currentOrder.materiaisNecessarios.length > 0) {
        for (const material of currentOrder.materiaisNecessarios) {
          const materialProduto = produtos.find(p => p.id === material.produtoId);
          const armazemProducaoMateriais = armazens.find(a => a.id === currentOrder.armazemProducaoId && a.tipo === 'PRODUCAO');
          if (!armazemProducaoMateriais) {
            canProduce = false;
            materialsHtml += `
              <div class="material-item">
                <div class="material-info">
                  <div class="material-code">${materialProduto.codigo}</div>
                  <div class="material-description">${materialProduto.descricao}</div>
                </div>
                <div class="material-quantities">
                  <div>Necessário: ${material.quantidade.toFixed(3)} ${materialProduto.unidade}</div>
                  <div>Disponível: 0 ${materialProduto.unidade}</div>
                  <div>Armazém: Não encontrado</div>
                </div>
                <div class="material-status status-error">
                  <i class="fas fa-times-circle"></i> 0%
                </div>
              </div>`;
            continue;
          }

          const estoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === armazemProducaoMateriais.id) || { saldo: 0, saldoReservado: 0 };
          const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);

          const quantidadeRestante = material.quantidade - (currentOrder.quantidadeProduzida || 0) * (material.quantidade / currentOrder.quantidade);
          const disponibilidade = saldoDisponivel >= quantidadeRestante ? 100 : (saldoDisponivel / quantidadeRestante * 100).toFixed(1);
          let statusClass = 'status-ok';

          // SEMPRE bloquear apontamento se não há material suficiente
          if (saldoDisponivel < quantidadeRestante) {
            statusClass = 'status-error';
            canProduce = false;
          } else if (saldoDisponivel < quantidadeRestante * 1.2) {
            statusClass = 'status-warning';
          }

          const statusIcon = statusClass === 'status-ok' ? 'check-circle' :
                            statusClass === 'status-warning' ? 'exclamation-triangle' : 'times-circle';

          materialsHtml += `
            <div class="material-item">
              <div class="material-info">
                <div class="material-code">${materialProduto.codigo}</div>
                <div class="material-description">${materialProduto.descricao}</div>
              </div>
              <div class="material-quantities">
                <div>Necessário: ${quantidadeRestante.toFixed(3)} ${materialProduto.unidade}</div>
                <div>Disponível: ${saldoDisponivel.toFixed(3)} ${materialProduto.unidade}</div>
                <div>Armazém: ${armazemProducaoMateriais.codigo}</div>
              </div>
              <div class="material-status ${statusClass}">
                <i class="fas fa-${statusIcon}"></i> ${disponibilidade}%
              </div>
              ${(saldoDisponivel < quantidadeRestante && permitirGerarEstoqueAutomatico) ?
                `<button class='generate-stock-btn' onclick='ajustarETransferirMaterial("${material.produtoId}", ${quantidadeRestante - saldoDisponivel})'>
                  <i class="fas fa-magic"></i> Gerar Estoque
                </button>` : ''}
            </div>`;
        }
      } else {
        materialsHtml += '<p>Sem materiais necessários registrados.</p>';
      }

      document.getElementById('orderInfo').innerHTML = `
        <h3>Ordem de Produção: ${currentOrder.numero}</h3>
        <div class="order-details">
          <div class="order-detail-item">
            <div class="order-detail-label">Produto</div>
            <div class="order-detail-value">${produto.codigo} - ${produto.descricao}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Quantidade Total</div>
            <div class="order-detail-value">${currentOrder.quantidade} ${produto.unidade}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Já Produzido</div>
            <div class="order-detail-value">${currentOrder.quantidadeProduzida || 0} ${produto.unidade}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Armazém de Produção</div>
            <div class="order-detail-value">${armazemProducao.codigo} - ${armazemProducao.nome}</div>
          </div>
        </div>
      `;

      document.querySelector('#materialsList .materials-container').innerHTML = materialsHtml;

      // SEMPRE verificar se pode produzir antes de abrir o modal (independente da configuração)
      if (!canProduce) {
        // Mostrar modal de materiais faltantes em vez de apenas um alert
        const materiaisFaltantes = verificarMateriaisFaltantesParaApontamento(currentOrder);
        if (materiaisFaltantes.length > 0) {
          mostrarModalMateriaisFaltantesApontamento(materiaisFaltantes, currentOrder);
          return; // Não abrir o modal de apontamento
        } else {
          alert('Não há material suficiente no armazém de produção para realizar o apontamento. Transfira os materiais necessários do armazém tipo Almoxarifado para o armazém tipo Produção usando o módulo de movimentação.');
          return; // Não abrir o modal de apontamento
        }
      }

      document.getElementById('submitButton').disabled = !canProduce;
      document.getElementById('appointmentModal').style.display = 'block';
    };

    window.closeModal = function() {
      document.getElementById('appointmentModal').style.display = 'none';
      document.getElementById('appointmentForm').reset();
      currentOrder = null;
    };

    async function updateInventory(produtoId, armazemId, quantidade, tipo) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldo = tipo === 'entrada' ? estoque.saldo + quantidade : estoque.saldo - quantidade;
        if (novoSaldo < 0 && !permitirProducaoSemEstoque) throw new Error(`Saldo insuficiente no armazém ${armazemId}.`);
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldo = novoSaldo;
      } else if (tipo === 'entrada') {
        const novoEstoque = { produtoId, armazemId, saldo: quantidade, ultimaMovimentacao: Timestamp.now() };
        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        estoques.push({ ...novoEstoque, id: docRef.id });
      }
    }

    async function updateInventoryReservation(produtoId, armazemId, quantidade) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldoReservado = (estoque.saldoReservado || 0) + quantidade;
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldoReservado: Math.max(0, novoSaldoReservado),
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldoReservado = Math.max(0, novoSaldoReservado);
      }
    }

    window.submitAppointment = async function(event) {
      event.preventDefault();
      const submitButton = document.getElementById('submitButton');
      submitButton.disabled = true;

      try {
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser) {
          throw new Error('Usuário não está logado');
        }

        const quantidade = parseFloat(document.getElementById('quantity').value);
        const refugo = parseFloat(document.getElementById('scrap').value) || 0;
        const observacoes = document.getElementById('observations').value;

        if (quantidade <= 0) {
          throw new Error('A quantidade produzida deve ser maior que zero');
        }

        if (quantidade + (currentOrder.quantidadeProduzida || 0) > currentOrder.quantidade) {
          throw new Error('A quantidade total produzida não pode exceder a quantidade da ordem');
        }

        // Atualizar ordem de produção
        const orderRef = doc(db, "ordensProducao", currentOrder.id);
        const novaQuantidadeProduzida = (currentOrder.quantidadeProduzida || 0) + quantidade;
        const novoRefugo = (currentOrder.refugo || 0) + refugo;

        // Determinar novo status
        let novoStatus = currentOrder.status;
        if (novaQuantidadeProduzida >= currentOrder.quantidade) {
          novoStatus = 'Concluída';
        } else if (novoStatus === 'Pendente') {
          novoStatus = 'Em Produção';
        }

        // 🔄 CONSUMIR MATERIAIS EMPENHADOS
        if (currentOrder.materiaisNecessarios) {
          const consumos = [];

          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * quantidade;

            // Preparar dados para consumo de empenho
            consumos.push({
              produtoId: material.produtoId,
              quantidade: quantidadeNecessaria
            });
          }

          // ⚡ CONSUMIR EMPENHOS (se existirem)
          try {
            const resultadoConsumo = await EmpenhoService.consumirMaterialEmpenhado(currentOrder.id, consumos);
            if (resultadoConsumo.consumosRealizados > 0) {
              console.log(`✅ ${resultadoConsumo.consumosRealizados} empenho(s) consumido(s)`);
              mostrarNotificacao(`⚡ ${resultadoConsumo.consumosRealizados} empenho(s) consumido(s)`, 'info', 2000);
            }
            if (resultadoConsumo.erros.length > 0) {
              console.warn('⚠️ Erros no consumo de empenhos:', resultadoConsumo.erros);
            }
          } catch (empenhoError) {
            console.warn('⚠️ Erro ao consumir empenhos (continuando com apontamento):', empenhoError);
          }
        }

        // Baixar materiais do estoque (lógica tradicional)
        const batch = writeBatch(db);

        if (currentOrder.materiaisNecessarios) {
          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * quantidade;
            const armazemId = material.armazemId || currentOrder.armazemProducaoId;

            // Encontrar o estoque correspondente
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === armazemId
            );

            if (estoque) {
              const saldoAtual = estoque.saldo;
              const saldoReservado = estoque.saldoReservado || 0;

              if (saldoAtual - saldoReservado < quantidadeNecessaria && !permitirProducaoSemEstoque) {
                throw new Error(`Saldo insuficiente para o material ${material.codigo}`);
              }

              batch.update(doc(db, "estoques", estoque.id), {
                saldo: saldoAtual - quantidadeNecessaria,
                ultimaMovimentacao: Timestamp.now()
              });
            } else if (!permitirProducaoSemEstoque) {
              throw new Error(`Estoque não encontrado para o material ${material.codigo}`);
            }
          }
        }

        // Atualizar ordem
        batch.update(orderRef, {
          quantidadeProduzida: novaQuantidadeProduzida,
          refugo: novoRefugo,
          status: novoStatus,
          ultimaAtualizacao: Timestamp.now()
        });

        // 🔓 LIBERAR EMPENHOS RESTANTES (se OP foi finalizada)
        if (novoStatus === 'Concluída') {
          try {
            const resultadoLiberacao = await EmpenhoService.liberarEmpenhosRestantes(currentOrder.id, 'OP_FINALIZADA');
            if (resultadoLiberacao.liberacoes > 0) {
              console.log(`✅ ${resultadoLiberacao.liberacoes} empenho(s) liberado(s) - OP finalizada`);
              mostrarNotificacao(`🔓 ${resultadoLiberacao.liberacoes} empenho(s) liberado(s)`, 'success', 3000);
            }
          } catch (liberacaoError) {
            console.warn('⚠️ Erro ao liberar empenhos (OP finalizada):', liberacaoError);
          }
        }

        // Registrar apontamento
        const apontamentoRef = collection(db, "apontamentos");
        const novoApontamento = {
          ordemId: currentOrder.id,
          numeroOrdem: currentOrder.numero,
          produtoId: currentOrder.produtoId,
          quantidade: quantidade,
          refugo: refugo,
          observacoes: observacoes,
          usuario: currentUser.email,
          nomeUsuario: currentUser.nome,
          dataHora: Timestamp.now()
        };

        // Primeiro executar o batch para atualizar estoque e ordem
        await batch.commit();

        // Depois adicionar o apontamento
        await addDoc(apontamentoRef, novoApontamento);

        // Registrar movimentações de estoque
        // 1. Consumo de materiais
        if (!currentOrder.materiaisNecessarios || currentOrder.materiaisNecessarios.length === 0) {
          alert('Atenção: Esta ordem de produção não possui materiais necessários cadastrados. Nenhuma movimentação de consumo será registrada!');
        } else {
          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * quantidade;
            const produtoMaterial = produtos.find(p => p.id === material.produtoId);
            await addDoc(collection(db, "movimentacoesEstoque"), {
              produtoId: material.produtoId,
              tipo: 'SAIDA',
              quantidade: quantidadeNecessaria,
              unidade: produtoMaterial?.unidade || '',
              tipoDocumento: 'PRODUCAO',
              numeroDocumento: currentOrder.numero,
              observacoes: `Consumo para OP ${currentOrder.numero}`,
              dataHora: Timestamp.now(),
              armazemId: material.armazemId || currentOrder.armazemProducaoId
            });
          }
        }
        // 2. Entrada do produto acabado
        const produtoAcabado = produtos.find(p => p.id === currentOrder.produtoId);
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId: currentOrder.produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidade,
          unidade: produtoAcabado?.unidade || '',
          tipoDocumento: 'PRODUCAO',
          numeroDocumento: currentOrder.numero,
          observacoes: `Produção OP ${currentOrder.numero}`,
          dataHora: Timestamp.now(),
          armazemId: currentOrder.armazemProducaoId
        });
        // Atualizar saldo do estoque do produto acabado no armazem de produção
        let estoqueProd = estoques.find(e => e.produtoId === currentOrder.produtoId && e.armazemId === currentOrder.armazemProducaoId);
        if (estoqueProd) {
          await updateDoc(doc(db, "estoques", estoqueProd.id), {
            saldo: (estoqueProd.saldo || 0) + quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
          estoqueProd.saldo += quantidade;
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId: currentOrder.produtoId,
            armazemId: currentOrder.armazemProducaoId,
            saldo: quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId: currentOrder.produtoId, armazemId: currentOrder.armazemProducaoId, saldo: quantidade });
        }

        mostrarNotificacao('✅ Apontamento registrado com sucesso!', 'success');
        closeModal();

      } catch (error) {
        console.error("Erro ao registrar apontamento:", error);
        alert(error.message || 'Erro ao registrar apontamento');
      } finally {
        submitButton.disabled = false;
      }
    };

    window.onclick = function(event) {
      const modal = document.getElementById('appointmentModal');
      if (event.target === modal) {
        closeModal();
      }
    };

    // Adicionar função global para ajuste e transferência automática
    window.ajustarETransferirMaterial = async function(produtoId, quantidadeNecessaria) {
      try {
        const ALM01 = armazens.find(a => a.codigo === 'ALM01');
        const PROD1 = armazens.find(a => a.id === currentOrder.armazemProducaoId);
        if (!ALM01) {
          alert('Armazém ALM01 não encontrado!');
          return;
        }
        if (!PROD1) {
          alert('Armazém de produção não encontrado!');
          return;
        }
        // 1. Ajuste de inventário (entrada) no ALM01
        const produto = produtos.find(p => p.id === produtoId);
        let estoqueAlm01 = estoques.find(e => e.produtoId === produtoId && e.armazemId === ALM01.id);
        let novoSaldo = (estoqueAlm01?.saldo || 0) + quantidadeNecessaria;
        if (estoqueAlm01) {
          await updateDoc(doc(db, "estoques", estoqueAlm01.id), {
            saldo: novoSaldo,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId,
            armazemId: ALM01.id,
            saldo: quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId, armazemId: ALM01.id, saldo: quantidadeNecessaria });
        }
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'AJUSTE',
          numeroDocumento: `AJU-${Date.now()}`,
          observacoes: `Ajuste automático para suprir OP ${currentOrder.numero}`,
          dataHora: Timestamp.now(),
          armazemId: ALM01.id
        });
        // 2. Transferência ALM01 -> PROD1
        // Atualizar saldo ALM01
        estoqueAlm01 = estoques.find(e => e.produtoId === produtoId && e.armazemId === ALM01.id);
        if (estoqueAlm01) {
          await updateDoc(doc(db, "estoques", estoqueAlm01.id), {
            saldo: estoqueAlm01.saldo - quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
        }
        // Atualizar saldo PROD1
        let estoqueProd1 = estoques.find(e => e.produtoId === produtoId && e.armazemId === PROD1.id);
        if (estoqueProd1) {
          await updateDoc(doc(db, "estoques", estoqueProd1.id), {
            saldo: (estoqueProd1.saldo || 0) + quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId,
            armazemId: PROD1.id,
            saldo: quantidadeNecessaria,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId, armazemId: PROD1.id, saldo: quantidadeNecessaria });
        }
        // Registrar movimentações
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'SAIDA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: `TRF-${Date.now()}`,
          observacoes: `Transferência automática para OP ${currentOrder.numero} - Armazém ${PROD1.codigo}`,
          dataHora: Timestamp.now(),
          armazemId: ALM01.id
        });
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: `TRF-${Date.now()}`,
          observacoes: `Transferência automática para OP ${currentOrder.numero} - Do armazém ${ALM01.codigo}`,
          dataHora: Timestamp.now(),
          armazemId: PROD1.id
        });
        mostrarNotificacao('✅ Ajuste e transferência realizados com sucesso!', 'success');
        debounceUpdate('ajusteTransferencia'); // Atualiza os dados na tela
        openAppointmentModal(currentOrder.id); // Reabre o modal atualizado
      } catch (error) {
        console.error(error);
        alert('Erro ao ajustar e transferir material: ' + error.message);
      }
    };

    // Função para verificar materiais faltantes para apontamento
    function verificarMateriaisFaltantesParaApontamento(ordem) {
      if (!ordem || !ordem.materiaisNecessarios) return [];

      const materiaisFaltantes = [];

      ordem.materiaisNecessarios.forEach(material => {
        const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
        const armazemProducao = armazens.find(a => a.id === ordem.armazemProducaoId);
        const estoque = estoques.find(e =>
          e.produtoId === material.produtoId &&
          e.armazemId === ordem.armazemProducaoId
        ) || { saldo: 0, saldoReservado: 0 };

        const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
        const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

        // SEMPRE considerar material em falta se saldo insuficiente (independente da configuração)
        if (saldoDisponivel < quantidadeRestante) {
          materiaisFaltantes.push({
            produtoId: material.produtoId,
            codigo: materialProduto.codigo || 'N/A',
            descricao: materialProduto.descricao || 'N/A',
            tipo: materialProduto.tipo || 'N/A',
            unidade: materialProduto.unidade || 'UN',
            quantidadeNecessaria: quantidadeRestante,
            saldoDisponivel: saldoDisponivel,
            falta: quantidadeRestante - saldoDisponivel,
            armazemId: ordem.armazemProducaoId,
            armazemCodigo: armazemProducao?.codigo || 'N/A',
            armazemNome: armazemProducao?.nome || 'N/A'
          });
        }
      });

      return materiaisFaltantes;
    }

    // Função para verificar estoque antes da impressão
    function verificarEstoqueParaImpressao(orderId) {
      const ordem = ordensProducao.find(op => op.id === orderId);
      if (!ordem || !ordem.materiaisNecessarios) return [];

      const materiaisFaltantes = [];

      ordem.materiaisNecessarios.forEach(material => {
        const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
        const estoque = estoques.find(e =>
          e.produtoId === material.produtoId &&
          e.armazemId === (material.armazemId || ordem.armazemProducaoId)
        ) || { saldo: 0, saldoReservado: 0 };

        const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
        const quantidadeNecessaria = material.quantidade;

        if (saldoDisponivel < quantidadeNecessaria) {
          materiaisFaltantes.push({
            produtoId: material.produtoId,
            codigo: materialProduto.codigo || 'N/A',
            descricao: materialProduto.descricao || 'N/A',
            tipo: materialProduto.tipo || 'N/A',
            unidade: materialProduto.unidade || 'UN',
            quantidadeNecessaria: quantidadeNecessaria,
            saldoDisponivel: saldoDisponivel,
            falta: quantidadeNecessaria - saldoDisponivel,
            armazemId: material.armazemId || ordem.armazemProducaoId
          });
        }
      });

      return materiaisFaltantes;
    }

    // Função para mostrar modal de materiais faltantes para apontamento
    function mostrarModalMateriaisFaltantesApontamento(materiaisFaltantes, ordem) {
      // Criar modal dinamicamente se não existir
      let modal = document.getElementById('modalMateriaisFaltantesApontamento');
      if (!modal) {
        modal = document.createElement('div');
        modal.id = 'modalMateriaisFaltantesApontamento';
        modal.className = 'modal';
        modal.style.display = 'none';
        modal.innerHTML = `
          <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header" style="background: #dc3545; color: white;">
              <h2 style="margin: 0; color: white;">⚠️ Apontamento Bloqueado - Materiais em Falta</h2>
              <span class="close-button" onclick="fecharModalMateriaisFaltantesApontamento()" style="color: white;">&times;</span>
            </div>
            <div class="modal-body">
              <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
                <h4 style="color: #721c24; margin: 0 0 10px 0;">🚫 Não é possível realizar o apontamento</h4>
                <p style="color: #721c24; margin: 0;">
                  Os materiais abaixo não possuem saldo suficiente no armazém de produção.
                  É necessário transferir os materiais do almoxarifado para o armazém de produção antes de realizar o apontamento.
                </p>
              </div>

              <div style="margin-bottom: 20px;">
                <h4>📋 Ordem de Produção: <span id="numeroOPFaltantesApontamento"></span></h4>
                <p><strong>Produto:</strong> <span id="produtoOPFaltantesApontamento"></span></p>
                <p><strong>Armazém de Produção:</strong> <span id="armazemProducaoApontamento"></span></p>
              </div>

              <h4 style="color: #dc3545;">📦 Materiais em Falta:</h4>
              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                  <thead>
                    <tr style="background: #f8f9fa;">
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Código</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Descrição</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Tipo</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Necessário</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Disponível</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Falta</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Unidade</th>
                    </tr>
                  </thead>
                  <tbody id="tbodyMateriaisFaltantesApontamento">
                  </tbody>
                </table>
              </div>

              <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px; margin-top: 20px;">
                <h5 style="color: #0c5460; margin: 0 0 10px 0;">💡 Como resolver:</h5>
                <ol style="color: #0c5460; margin: 0; padding-left: 20px;">
                  <li>Acesse o módulo de <strong>Movimentação de Estoque</strong></li>
                  <li>Realize a <strong>transferência</strong> dos materiais do almoxarifado para o armazém de produção</li>
                  <li>Ou use o botão <strong>"Gerar Estoque"</strong> nos materiais em falta (se disponível)</li>
                  <li>Após a transferência, tente realizar o apontamento novamente</li>
                </ol>
              </div>
            </div>
            <div class="modal-footer" style="text-align: right; padding: 15px; border-top: 1px solid #ddd;">
              <button onclick="fecharModalMateriaisFaltantesApontamento()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                Fechar
              </button>
            </div>
          </div>
        `;
        document.body.appendChild(modal);
      }

      // Preencher informações da OP
      document.getElementById('numeroOPFaltantesApontamento').textContent = ordem.numero || 'N/A';
      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      document.getElementById('produtoOPFaltantesApontamento').textContent = `${produto.codigo || 'N/A'} - ${produto.descricao || 'N/A'}`;

      const armazemProducao = armazens.find(a => a.id === ordem.armazemProducaoId) || {};
      document.getElementById('armazemProducaoApontamento').textContent = `${armazemProducao.codigo || 'N/A'} - ${armazemProducao.nome || 'N/A'}`;

      // Limpar e preencher tabela
      const tbody = document.getElementById('tbodyMateriaisFaltantesApontamento');
      tbody.innerHTML = '';

      materiaisFaltantes.forEach(material => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td style="padding: 10px; border: 1px solid #ddd;">${material.codigo}</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${material.descricao}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.tipo}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.quantidadeNecessaria.toFixed(3)}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.saldoDisponivel.toFixed(3)}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;">${material.falta.toFixed(3)}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.unidade}</td>
        `;
        tbody.appendChild(row);
      });

      // Mostrar modal
      modal.style.display = 'block';
    }

    // Função para mostrar modal de materiais faltantes
    function mostrarModalMateriaisFaltantes(materiaisFaltantes, ordem) {
      const modal = document.getElementById('modalMateriaisFaltantes');
      const tbody = document.getElementById('tbodyMateriaisFaltantes');
      const numeroOP = document.getElementById('numeroOPFaltantes');
      const produtoOP = document.getElementById('produtoOPFaltantes');

      // Preencher informações da OP
      numeroOP.textContent = ordem.numero || 'N/A';
      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      produtoOP.textContent = `${produto.codigo || 'N/A'} - ${produto.descricao || 'N/A'}`;

      // Limpar tabela
      tbody.innerHTML = '';

      // Preencher tabela com materiais faltantes
      materiaisFaltantes.forEach(material => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td style="padding: 8px; border-bottom: 1px solid #ddd;">${material.codigo}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd;">${material.descricao}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.tipo}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.quantidadeNecessaria}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.saldoDisponivel}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;">${material.falta}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.unidade}</td>
        `;
        tbody.appendChild(row);
      });

      // Mostrar modal
      modal.style.display = 'block';
    }

    // Função para fechar modal de materiais faltantes para apontamento
    window.fecharModalMateriaisFaltantesApontamento = function() {
      const modal = document.getElementById('modalMateriaisFaltantesApontamento');
      if (modal) {
        modal.style.display = 'none';
      }
    };

    // Função para fechar modal de materiais faltantes
    window.fecharModalMateriaisFaltantes = function() {
      document.getElementById('modalMateriaisFaltantes').style.display = 'none';
    };

    // Função para forçar impressão mesmo com materiais faltantes
    window.forcarImpressaoOP = function() {
      const orderId = document.getElementById('modalMateriaisFaltantes').dataset.orderId;
      fecharModalMateriaisFaltantes();

      // Mostrar confirmação
      if (confirm('⚠️ ATENÇÃO: Existem materiais em falta!\n\nDeseja realmente imprimir a OP mesmo assim?\n\nEsta ação pode causar problemas na produção.')) {
        imprimirOPSemValidacao(orderId);
      }
    };

    // Função para imprimir OP sem validação de estoque
    function imprimirOPSemValidacao(orderId) {
      const ordem = ordensProducao.find(op => op.id === orderId);
      if (!ordem) return alert('Ordem não encontrada!');

      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);
      const printArea = document.getElementById('printArea');

      // Continuar com a impressão normal...
      processarImpressaoOP(ordem, produto, estrutura, printArea);
    }

    // Adicionar a função global para impressão:
    window.printOrderReport = async function(orderId) {
      try {
        const ordem = ordensProducao.find(op => op.id === orderId);
        if (!ordem) return alert('Ordem não encontrada!');

        console.log(`🔍 Validando estoque para impressão da OP: ${orderId}`);

        // 1. VALIDAR ESTOQUE ANTES DE PERMITIR IMPRESSÃO
        const validacao = await validarEstoqueParaProducao(orderId);
        const nomeOP = ordem.numero || orderId;

        if (!validacao.podeProuzir) {
          // Mostrar detalhes dos materiais em falta
          const detalhes = mostrarDetalhesValidacao(validacao, nomeOP);
          alert(`🖨️ IMPRESSÃO BLOQUEADA\n\n${detalhes}\n💡 Resolva os problemas de estoque antes de imprimir a OP.`);

          mostrarNotificacao('🖨️ Impressão bloqueada - Materiais insuficientes', 'warning', 8000);
          return;
        }

        // 2. MOSTRAR ALERTAS SE HOUVER E CONFIRMAR
        if (validacao.alertas.length > 0) {
          const detalhes = mostrarDetalhesValidacao(validacao, nomeOP);
          if (!confirm(`${detalhes}\n🖨️ Há alertas de estoque baixo. Deseja imprimir mesmo assim?\n\n⚠️ A impressão pode gerar confusão na fábrica se não houver material suficiente.`)) {
            return;
          }
        }

        // 3. SE CHEGOU AQUI, PODE IMPRIMIR
        console.log(`🖨️ Imprimindo OP: ${orderId} - Estoque validado`);

        const produto = produtos.find(p => p.id === ordem.produtoId) || {};
        const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);
        const printArea = document.getElementById('printArea');

        // Processar impressão normal
        processarImpressaoOP(ordem, produto, estrutura, printArea);

        mostrarNotificacao('🖨️ OP impressa - Estoque validado', 'success', 3000);

      } catch (error) {
        console.error('❌ Erro ao validar estoque para impressão:', error);
        mostrarNotificacao('❌ Erro ao validar estoque para impressão', 'error', 5000);
        alert('❌ Erro ao validar estoque para impressão: ' + error.message);
      }
    };

    // Função para processar a impressão da OP
    function processarImpressaoOP(ordem, produto, estrutura, printArea) {
      let roteiroHtml = '';
      if (estrutura && Array.isArray(estrutura.operacoes) && estrutura.operacoes.length > 0) {
        roteiroHtml = `
          <div class='section' style='margin-bottom:15px;'>
            <div class='section-title' style='background:#f0f0f0;padding:3px 8px;font-weight:bold;border:1px solid #ccc;font-size:11px;'>ROTEIRO DE PRODUÇÃO</div>
            <table style='width:100%;border-collapse:collapse;margin-top:3px;font-size:11px;'>
              <thead>
                <tr>
                  <th style='border:1px solid #ccc;padding:4px;'>Seq.</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Operação</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Recurso</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Tempo (min)</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Descrição</th>
                </tr>
              </thead>
              <tbody>
                ${estrutura.operacoes.sort((a, b) => a.sequencia - b.sequencia).map(op => {
                  const operacao = operacoes.find(o => o.id === op.operacaoId) || {};
                  const recurso = recursos.find(r => r.id === op.recursoId) || {};
                  return `<tr>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.sequencia}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${operacao.operacao || ''}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${recurso.codigo || ''} - ${recurso.maquina || ''}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.tempo}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.descricao || ''}</td>
                  </tr>`;
                }).join('')}
              </tbody>
            </table>
          </div>`;
      }
      printArea.innerHTML = `
        <div class='container' style='max-width:210mm;margin:0 auto;font-family:Arial,sans-serif;font-size:12px;'>
          <div class='order-page' style='background:white;padding:15px;margin-bottom:20px;box-shadow:0 2px 8px rgba(0,0,0,0.1);'>
            <div class='header' style='display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:10px;border-bottom:1px solid #000;padding-bottom:5px;'>
              <img src='https://www.naliteck.com.br/img/logo.png' alt='Logo' style='width:100px;height:auto;'>
              <div class='order-title' style='text-align:center;flex-grow:1;margin:0 10px;'>
                <h1 style='margin:0;font-size:18px;'>ORDEM DE PRODUÇÃO</h1>
                <h2 style='margin:3px 0;font-size:16px;'>${ordem.numero}</h2>
              </div>
              <div style='text-align:right;font-size:10px;'>
                <strong>Data: </strong>${new Date().toLocaleDateString()}<br>
                <strong>Hora: </strong>${new Date().toLocaleTimeString()}
              </div>
            </div>
            <div class='order-info' style='display:grid;grid-template-columns:repeat(4,1fr);gap:5px;margin-bottom:15px;border:1px solid #ccc;padding:5px;'>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Produto:</strong><span style='font-size:12px;'>${produto.codigo || ''} - ${produto.descricao || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Tipo:</strong><span style='font-size:12px;'>${produto.tipo || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Quantidade:</strong><span style='font-size:12px;'>${ordem.quantidade} ${produto.unidade || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Status:</strong><span class='status-badge status-${ordem.status.toLowerCase()}' style='font-size:10px;font-weight:bold;'>${ordem.status}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Data de Criação:</strong><span style='font-size:12px;'>${ordem.dataCriacao ? new Date(ordem.dataCriacao.seconds * 1000).toLocaleDateString() : ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Data de Entrega:</strong><span style='font-size:12px;'>${ordem.dataEntrega ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString() : ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Prioridade:</strong><span style='font-size:12px;'>${ordem.prioridade || 'Normal'}</span></div>
            </div>
            ${(Array.isArray(ordem.materiaisNecessarios) && ordem.materiaisNecessarios.length > 0) ? `
            <div class='section' style='margin-bottom:15px;'>
              <div class='section-title' style='background:#f0f0f0;padding:3px 8px;font-weight:bold;border:1px solid #ccc;font-size:11px;'>LISTA DE MATERIAIS</div>
              <table style='width:100%;border-collapse:collapse;margin-top:3px;font-size:11px;'>
                <thead>
                  <tr>
                    <th style='border:1px solid #ccc;padding:4px;'>Código</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Descrição</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Tipo</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Quantidade</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Unidade</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Disponível</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Necessidade</th>
                  </tr>
                </thead>
                <tbody>
                  ${ordem.materiaisNecessarios.map(material => {
                    const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
                    const estoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === (material.armazemId || ordem.armazemProducaoId)) || { saldo: 0, saldoReservado: 0 };
                    const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
                    return `<tr>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.codigo || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.descricao || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.tipo || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${material.quantidade}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.unidade || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${saldoDisponivel !== undefined ? saldoDisponivel : 0}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${material.necessidade !== undefined ? material.necessidade : material.quantidade}</td>
                    </tr>`;
                  }).join('')}
                </tbody>
              </table>
            </div>` : ''}
            ${roteiroHtml}
            <div class='signatures' style='margin-top:20px;display:flex;justify-content:space-between;'>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Produção</div></div>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Qualidade</div></div>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Supervisor</div></div>
            </div>
          </div>
        </div>
      `;
      // Abrir nova janela e imprimir
      const printWindow = window.open('', '_blank');
      printWindow.document.write(`<!DOCTYPE html><html><head><title>Impressão OP</title><meta charset='UTF-8'><style>@page{size:A4 portrait;margin:15mm;}@media print{.no-print{display:none;}}</style></head><body>${printArea.innerHTML}<script>window.onload=function(){window.print();}<' + '/script></body></html>`);
      printWindow.document.close();
    }

    // Função removida: marcarEnviadaParaFabrica (redundante com iniciarProducao)

    // Função para atualizar dashboard de métricas
    window.atualizarDashboard = function() {
      try {
        const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');
        const ordensEmProducao = ordensAtivas.filter(op => op.status === 'Em Produção');

        // Calcular OPs atrasadas (data de entrega passou)
        const hoje = new Date();
        const opsAtrasadas = ordensAtivas.filter(op => {
          if (!op.dataEntrega?.seconds) return false;
          const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
          return dataEntrega < hoje;
        });

        // Calcular materiais em falta
        let materiaisEmFalta = 0;
        ordensAtivas.forEach(ordem => {
          if (ordem.materiaisNecessarios) {
            ordem.materiaisNecessarios.forEach(material => {
              const estoque = estoques.find(e =>
                e.produtoId === material.produtoId &&
                e.armazemId === (material.armazemId || ordem.armazemProducaoId)
              ) || { saldo: 0, saldoReservado: 0 };

              const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
              const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

              if (saldoDisponivel < quantidadeRestante) {
                materiaisEmFalta++;
              }
            });
          }
        });

        // Atualizar elementos do dashboard
        document.getElementById('totalOPs').textContent = ordensAtivas.length;
        document.getElementById('emProducao').textContent = ordensEmProducao.length;
        document.getElementById('materiaisFalta').textContent = materiaisEmFalta;
        document.getElementById('opsAtrasadas').textContent = opsAtrasadas.length;

        console.log('Dashboard atualizado:', {
          totalOPs: ordensAtivas.length,
          emProducao: ordensEmProducao.length,
          materiaisFalta: materiaisEmFalta,
          opsAtrasadas: opsAtrasadas.length
        });

      } catch (error) {
        console.error('Erro ao atualizar dashboard:', error);
      }
    };

    // Sistema de notificações
    function mostrarNotificacao(mensagem, tipo = 'info', duracao = 5000) {
      // Remover notificações existentes
      const existingNotifications = document.querySelectorAll('.notification');
      existingNotifications.forEach(n => n.remove());

      // Criar nova notificação
      const notification = document.createElement('div');
      notification.className = `notification ${tipo}`;
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="fas fa-${getIconForType(tipo)}"></i>
          <span>${mensagem}</span>
          <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
        </div>
      `;

      document.body.appendChild(notification);

      // Mostrar notificação
      setTimeout(() => notification.classList.add('show'), 100);

      // Remover automaticamente
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
      }, duracao);
    }

    function getIconForType(tipo) {
      switch (tipo) {
        case 'success': return 'check-circle';
        case 'warning': return 'exclamation-triangle';
        case 'error': return 'times-circle';
        case 'info':
        default: return 'info-circle';
      }
    }

    // Verificar alertas automaticamente
    function verificarAlertas() {
      const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');

      // Verificar OPs atrasadas
      const hoje = new Date();
      const opsAtrasadas = ordensAtivas.filter(op => {
        if (!op.dataEntrega?.seconds) return false;
        const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
        return dataEntrega < hoje;
      });

      if (opsAtrasadas.length > 0) {
        mostrarNotificacao(`⚠️ ${opsAtrasadas.length} ordem(ns) de produção atrasada(s)!`, 'warning', 8000);
      }

      // Verificar materiais em falta críticos
      let materiaisCriticos = 0;
      ordensAtivas.forEach(ordem => {
        if (ordem.materiaisNecessarios) {
          ordem.materiaisNecessarios.forEach(material => {
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || ordem.armazemProducaoId)
            ) || { saldo: 0, saldoReservado: 0 };

            const saldoDisponivel = estoque.saldo - (estoque.saldoReservado || 0);
            const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

            if (saldoDisponivel <= 0 && quantidadeRestante > 0) {
              materiaisCriticos++;
            }
          });
        }
      });

      if (materiaisCriticos > 0) {
        mostrarNotificacao(`🚨 ${materiaisCriticos} material(is) em falta crítica!`, 'error', 10000);
      }
    }

    // Executar verificação de alertas a cada 5 minutos
    setInterval(verificarAlertas, 5 * 60 * 1000);

    // ===================================================================
    // VALIDAÇÃO DE ESTOQUE PARA PRODUÇÃO
    // ===================================================================

    // Função para validar se há estoque suficiente para produzir
    async function validarEstoqueParaProducao(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        if (!ordem) {
          throw new Error('Ordem de produção não encontrada');
        }

        const materiaisInsuficientes = [];
        const alertas = [];

        // Verificar cada material necessário
        if (ordem.materiaisNecessarios && ordem.materiaisNecessarios.length > 0) {
          for (const material of ordem.materiaisNecessarios) {
            // Buscar estoque do material
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || ordem.armazemProducaoId)
            );

            if (!estoque) {
              materiaisInsuficientes.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: 0,
                falta: material.quantidade,
                motivo: 'Estoque não encontrado'
              });
              continue;
            }

            // Calcular saldo disponível (considerando reservas e empenhos)
            const saldoTotal = estoque.saldo || 0;
            const saldoReservado = estoque.saldoReservado || 0;
            const saldoEmpenhado = estoque.saldoEmpenhado || 0;
            const saldoLivre = saldoTotal - saldoReservado - saldoEmpenhado;

            // VERIFICAR SE JÁ ESTÁ RESERVADO PARA ESTA OP
            let quantidadeReservadaParaEstaOP = 0;
            if (material.quantidadeReservada) {
              quantidadeReservadaParaEstaOP = material.quantidadeReservada;
            }

            // DISPONIBILIDADE REAL = SALDO LIVRE + RESERVADO PARA ESTA OP
            const disponivelParaEstaOP = saldoLivre + quantidadeReservadaParaEstaOP;

            // Verificar se há quantidade suficiente
            if (disponivelParaEstaOP < material.quantidade) {
              const falta = material.quantidade - disponivelParaEstaOP;
              materiaisInsuficientes.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: disponivelParaEstaOP,
                saldoLivre: saldoLivre,
                quantidadeReservadaParaEstaOP: quantidadeReservadaParaEstaOP,
                falta: falta,
                motivo: disponivelParaEstaOP <= 0 ? 'Sem estoque' : 'Estoque insuficiente',
                usandoReserva: quantidadeReservadaParaEstaOP > 0
              });
            }

            // Alertas para estoque baixo (menos de 20% do necessário extra)
            const margemSeguranca = material.quantidade * 0.2;
            if (saldoDisponivel >= material.quantidade && saldoDisponivel < (material.quantidade + margemSeguranca)) {
              alertas.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                disponivel: saldoDisponivel,
                necessario: material.quantidade,
                motivo: 'Estoque baixo - pouca margem de segurança'
              });
            }
          }
        }

        return {
          podeProuzir: materiaisInsuficientes.length === 0,
          materiaisInsuficientes,
          alertas,
          totalMateriais: ordem.materiaisNecessarios?.length || 0
        };

      } catch (error) {
        console.error('Erro ao validar estoque:', error);
        return {
          podeProuzir: false,
          materiaisInsuficientes: [],
          alertas: [],
          erro: error.message
        };
      }
    }

    // Função para mostrar detalhes da validação
    function mostrarDetalhesValidacao(validacao, nomeOP) {
      let mensagem = `📋 VALIDAÇÃO DE ESTOQUE - OP: ${nomeOP}\n\n`;

      if (validacao.erro) {
        mensagem += `❌ ERRO: ${validacao.erro}\n`;
        return mensagem;
      }

      if (validacao.podeProuzir) {
        mensagem += `✅ PODE PRODUZIR!\n`;
        mensagem += `📊 ${validacao.totalMateriais} material(is) verificado(s)\n\n`;

        if (validacao.alertas.length > 0) {
          mensagem += `⚠️ ALERTAS (${validacao.alertas.length}):\n`;
          validacao.alertas.forEach(alerta => {
            mensagem += `🔸 ${alerta.codigo}: ${alerta.disponivel.toFixed(3)} disponível (necessário: ${alerta.necessario.toFixed(3)})\n`;
            mensagem += `   ${alerta.motivo}\n\n`;
          });
        }
      } else {
        mensagem += `❌ NÃO PODE PRODUZIR!\n`;
        mensagem += `🚫 ${validacao.materiaisInsuficientes.length} material(is) insuficiente(s):\n\n`;

        validacao.materiaisInsuficientes.forEach(material => {
          mensagem += `🔸 ${material.codigo}:\n`;
          mensagem += `   Necessário: ${material.necessario.toFixed(3)}\n`;
          mensagem += `   Disponível: ${material.disponivel.toFixed(3)}\n`;
          mensagem += `   Falta: ${material.falta.toFixed(3)}\n`;
          mensagem += `   Motivo: ${material.motivo}\n\n`;
        });

        mensagem += `💡 AÇÕES SUGERIDAS:\n`;
        mensagem += `• Verificar compras pendentes\n`;
        mensagem += `• Solicitar transferência entre armazéns\n`;
        mensagem += `• Ajustar quantidade da OP\n`;
        mensagem += `• Aguardar recebimento de materiais\n`;
      }

      return mensagem;
    }

    // ===================================================================
    // DECODIFICADOR DE PRODUTOS & CORREÇÃO DE ESTOQUE
    // ===================================================================

    // Função principal para abrir decodificador
    window.abrirDecodificadorProdutos = function() {
      document.getElementById('modalDecodificadorProdutos').style.display = 'block';
      document.getElementById('codigoCriptico').focus();
    };

    // Buscar produto por código críptico
    window.buscarProdutoPorCodigo = function() {
      const codigoCriptico = document.getElementById('codigoCriptico').value.trim();

      if (!codigoCriptico) {
        alert('Por favor, digite um código para buscar.');
        return;
      }

      console.log(`🔍 Buscando produto com código: ${codigoCriptico}`);

      // Buscar produto no array de produtos
      const produto = produtos.find(p =>
        p.id === codigoCriptico ||
        p.codigo === codigoCriptico ||
        (p.codigoInterno && p.codigoInterno === codigoCriptico)
      );

      // Buscar estoque do produto
      const estoque = estoques.find(e => e.produtoId === codigoCriptico);

      // Buscar em estruturas (pode ser componente)
      const estruturasQueUsam = estruturas.filter(est =>
        est.itens && est.itens.some(item => item.produtoId === codigoCriptico)
      );

      // Buscar em OPs que usam este material
      const opsQueUsam = ordensProducao.filter(op =>
        op.materiaisNecessarios && op.materiaisNecessarios.some(mat => mat.produtoId === codigoCriptico)
      );

      mostrarResultadoBusca(codigoCriptico, produto, estoque, estruturasQueUsam, opsQueUsam);
    };

    // Mostrar resultado da busca
    function mostrarResultadoBusca(codigoCriptico, produto, estoque, estruturasQueUsam, opsQueUsam) {
      const resultadoDiv = document.getElementById('resultadoBusca');

      let html = `
        <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px;">
          <h5 style="color: #495057; margin: 0 0 15px 0;">🔍 Resultado da Busca: <code>${codigoCriptico}</code></h5>
      `;

      // Informações do produto
      if (produto) {
        html += `
          <div style="background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #155724; margin: 0 0 10px 0;">✅ Produto Encontrado</h6>
            <table style="width: 100%; border-collapse: collapse;">
              <tr><td style="padding: 5px; font-weight: bold;">ID:</td><td style="padding: 5px;">${produto.id}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Código:</td><td style="padding: 5px;">${produto.codigo || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Descrição:</td><td style="padding: 5px;">${produto.descricao || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Unidade:</td><td style="padding: 5px;">${produto.unidade || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Tipo:</td><td style="padding: 5px;">${produto.tipo || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Família:</td><td style="padding: 5px;">${produto.familia || 'N/A'}</td></tr>
            </table>
          </div>
        `;
      } else {
        html += `
          <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #721c24; margin: 0;">❌ Produto não encontrado no cadastro</h6>
            <p style="color: #721c24; margin: 5px 0 0 0; font-size: 14px;">
              O código pode ser um ID interno do Firebase ou um código não cadastrado.
            </p>
          </div>
        `;
      }

      // Informações do estoque
      if (estoque) {
        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;
        const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;
        const armazem = armazens.find(a => a.id === estoque.armazemId);

        const corSaldo = saldoTotal < 0 ? '#dc3545' : saldoTotal === 0 ? '#ffc107' : '#28a745';

        html += `
          <div style="background: #e2e3e5; border-left: 4px solid ${corSaldo}; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #383d41; margin: 0 0 10px 0;">📦 Informações de Estoque</h6>
            <table style="width: 100%; border-collapse: collapse;">
              <tr><td style="padding: 5px; font-weight: bold;">Armazém:</td><td style="padding: 5px;">${armazem?.nome || estoque.armazemId}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Total:</td><td style="padding: 5px; color: ${corSaldo}; font-weight: bold;">${saldoTotal.toFixed(3)}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Reservado:</td><td style="padding: 5px;">${saldoReservado.toFixed(3)}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Empenhado:</td><td style="padding: 5px;">${saldoEmpenhado.toFixed(3)}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Disponível:</td><td style="padding: 5px; color: ${saldoDisponivel < 0 ? '#dc3545' : '#28a745'}; font-weight: bold;">${saldoDisponivel.toFixed(3)}</td></tr>
            </table>

            ${saldoTotal < 0 ? `
              <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-top: 10px;">
                <strong style="color: #721c24;">⚠️ ESTOQUE NEGATIVO DETECTADO!</strong>
                <br><button onclick="corrigirEstoque('${estoque.id}', '${codigoCriptico}')"
                           style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-top: 5px; cursor: pointer;">
                  🔧 Corrigir Estoque
                </button>
              </div>
            ` : ''}
          </div>
        `;
      } else {
        html += `
          <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #856404; margin: 0;">⚠️ Estoque não encontrado</h6>
            <p style="color: #856404; margin: 5px 0 0 0; font-size: 14px;">
              Este produto não possui registro de estoque.
            </p>
          </div>
        `;
      }

      // OPs que usam este material
      if (opsQueUsam.length > 0) {
        html += `
          <div style="background: #cce5ff; border-left: 4px solid #007bff; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #004085; margin: 0 0 10px 0;">🏭 OPs que usam este material (${opsQueUsam.length})</h6>
            <ul style="margin: 0; padding-left: 20px;">
        `;

        opsQueUsam.slice(0, 5).forEach(op => {
          const material = op.materiaisNecessarios.find(m => m.produtoId === codigoCriptico);
          html += `<li>OP ${op.numero} - Necessário: ${material.quantidade?.toFixed(3) || 'N/A'} - Status: ${op.status}</li>`;
        });

        if (opsQueUsam.length > 5) {
          html += `<li><em>... e mais ${opsQueUsam.length - 5} OP(s)</em></li>`;
        }

        html += `</ul></div>`;
      }

      html += `</div>`;

      resultadoDiv.innerHTML = html;
      resultadoDiv.style.display = 'block';
    }

    // Analisar produtos com problemas de estoque
    window.analisarProdutosComProblemas = function() {
      console.log('🔍 Analisando produtos com problemas de estoque...');

      const produtosComProblemas = [];

      estoques.forEach(estoque => {
        const produto = produtos.find(p => p.id === estoque.produtoId);
        const armazem = armazens.find(a => a.id === estoque.armazemId);

        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;
        const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;

        // Identificar problemas
        const problemas = [];
        if (saldoTotal < 0) problemas.push('ESTOQUE_NEGATIVO');
        if (saldoReservado > saldoTotal) problemas.push('RESERVA_MAIOR_QUE_TOTAL');
        if (saldoEmpenhado > saldoTotal) problemas.push('EMPENHO_MAIOR_QUE_TOTAL');
        if (saldoDisponivel < 0) problemas.push('DISPONIVEL_NEGATIVO');
        if (!produto) problemas.push('PRODUTO_NAO_ENCONTRADO');

        if (problemas.length > 0) {
          produtosComProblemas.push({
            estoque,
            produto,
            armazem,
            saldoTotal,
            saldoReservado,
            saldoEmpenhado,
            saldoDisponivel,
            problemas
          });
        }
      });

      mostrarProdutosComProblemas(produtosComProblemas);
    };

    // Mostrar produtos com problemas
    function mostrarProdutosComProblemas(produtosComProblemas) {
      const container = document.getElementById('produtosComProblemas');

      if (produtosComProblemas.length === 0) {
        container.innerHTML = `
          <div style="background: #d4edda; padding: 15px; border-radius: 4px; text-align: center;">
            <strong style="color: #155724;">✅ Nenhum problema de estoque detectado!</strong>
            <p style="color: #155724; margin: 5px 0 0 0;">Todos os estoques estão consistentes.</p>
          </div>
        `;
        return;
      }

      let html = `
        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
          <strong style="color: #721c24;">⚠️ ${produtosComProblemas.length} produto(s) com problemas detectado(s)</strong>
        </div>

        <div style="max-height: 400px; overflow-y: auto;">
          <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
              <tr style="background: #e9ecef; position: sticky; top: 0;">
                <th style="padding: 8px; text-align: left; border: 1px solid #dee2e6;">Código</th>
                <th style="padding: 8px; text-align: left; border: 1px solid #dee2e6;">Descrição</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Armazém</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Saldo</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Problemas</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Ação</th>
              </tr>
            </thead>
            <tbody>
      `;

      produtosComProblemas.forEach((item, index) => {
        const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';
        const codigoProduto = item.produto?.codigo || item.estoque.produtoId;
        const descricaoProduto = item.produto?.descricao || 'Produto não encontrado';
        const nomeArmazem = item.armazem?.nome || item.estoque.armazemId;

        const problemasTexto = item.problemas.map(p => {
          switch(p) {
            case 'ESTOQUE_NEGATIVO': return '🔴 Negativo';
            case 'RESERVA_MAIOR_QUE_TOTAL': return '🟡 Reserva > Total';
            case 'EMPENHO_MAIOR_QUE_TOTAL': return '🟠 Empenho > Total';
            case 'DISPONIVEL_NEGATIVO': return '🔴 Disponível < 0';
            case 'PRODUTO_NAO_ENCONTRADO': return '❓ Produto não encontrado';
            default: return p;
          }
        }).join('<br>');

        html += `
          <tr style="background: ${rowColor};">
            <td style="padding: 8px; border: 1px solid #dee2e6; font-family: monospace;">${codigoProduto}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">${descricaoProduto}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">${nomeArmazem}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">
              <div>Total: <strong style="color: ${item.saldoTotal < 0 ? '#dc3545' : '#28a745'};">${item.saldoTotal.toFixed(3)}</strong></div>
              <div>Disp: <strong style="color: ${item.saldoDisponivel < 0 ? '#dc3545' : '#28a745'};">${item.saldoDisponivel.toFixed(3)}</strong></div>
            </td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center; font-size: 10px;">${problemasTexto}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">
              <button onclick="corrigirEstoque('${item.estoque.id}', '${item.estoque.produtoId}')"
                      style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer;">
                🔧 Corrigir
              </button>
            </td>
          </tr>
        `;
      });

      html += `
            </tbody>
          </table>
        </div>
      `;

      container.innerHTML = html;
    }

    // Corrigir estoque individual
    window.corrigirEstoque = function(estoqueId, produtoId) {
      const estoque = estoques.find(e => e.id === estoqueId);
      const produto = produtos.find(p => p.id === produtoId);

      if (!estoque) {
        alert('Estoque não encontrado!');
        return;
      }

      const codigoProduto = produto?.codigo || produtoId;
      const descricaoProduto = produto?.descricao || 'Produto não identificado';

      const novoSaldo = prompt(
        `🔧 CORREÇÃO DE ESTOQUE\n\n` +
        `Produto: ${codigoProduto}\n` +
        `Descrição: ${descricaoProduto}\n` +
        `Saldo Atual: ${(estoque.saldo || 0).toFixed(3)}\n\n` +
        `Digite o novo saldo correto:`,
        '0.000'
      );

      if (novoSaldo === null) return; // Cancelou

      const saldoNumerico = parseFloat(novoSaldo);
      if (isNaN(saldoNumerico)) {
        alert('Valor inválido! Digite um número.');
        return;
      }

      if (!confirm(`Confirma a correção?\n\nDe: ${(estoque.saldo || 0).toFixed(3)}\nPara: ${saldoNumerico.toFixed(3)}`)) {
        return;
      }

      // Aqui seria a integração com Firebase para atualizar o estoque
      console.log(`🔧 Corrigindo estoque ${estoqueId}: ${estoque.saldo} → ${saldoNumerico}`);

      // Simular atualização local (em produção seria updateDoc do Firebase)
      estoque.saldo = saldoNumerico;

      mostrarNotificacao(`✅ Estoque corrigido: ${codigoProduto}`, 'success', 3000);

      // Recarregar análise
      analisarProdutosComProblemas();
    };

    // Gerar código mais legível a partir do código críptico
    function gerarCodigoLegivel(codigoCriptico, produto) {
      if (!produto) {
        return codigoCriptico.length > 12 ? codigoCriptico.substring(0, 12) + '...' : codigoCriptico;
      }

      // Se tem código do produto, usar ele
      if (produto.codigo && produto.codigo !== codigoCriptico) {
        return produto.codigo;
      }

      // Se tem descrição, tentar extrair código dela
      if (produto.descricao) {
        const desc = produto.descricao.toUpperCase();

        // Padrões comuns de códigos em descrições
        const padroes = [
          /([A-Z]{2,}-[A-Z0-9-]+)/,  // Ex: MP-ACUCAR, FERRO-CHATO
          /([A-Z]+\s*\d+[X\*]\d+)/,  // Ex: FERRO 2X5, CHAPA 4*8
          /([A-Z]+[-_][A-Z0-9]+)/,   // Ex: FERRO_CHATO, MP_001
          /^([A-Z\s]+)\s*\(/,        // Ex: FERRO CHATO (antes do parênteses)
        ];

        for (const padrao of padroes) {
          const match = desc.match(padrao);
          if (match) {
            return match[1].trim().replace(/\s+/g, '-');
          }
        }

        // Se não achou padrão, usar primeiras palavras
        const palavras = desc.split(/\s+/).slice(0, 2);
        if (palavras.length > 0) {
          return palavras.join('-');
        }
      }

      // Fallback: código críptico abreviado
      return codigoCriptico.length > 12 ? codigoCriptico.substring(0, 12) + '...' : codigoCriptico;
    }

    // Mostrar detalhes completos do código críptico
    window.mostrarDetalhesCodigoCriptico = function(codigoCriptico, produtoId) {
      const produto = produtos.find(p => p.id === produtoId || p.id === codigoCriptico);
      const estoque = estoques.find(e => e.produtoId === produtoId || e.produtoId === codigoCriptico);

      let detalhes = `🔍 DETALHES DO CÓDIGO CRÍPTICO\n\n`;
      detalhes += `Código Críptico: ${codigoCriptico}\n`;
      detalhes += `ID do Produto: ${produtoId}\n\n`;

      if (produto) {
        detalhes += `✅ PRODUTO ENCONTRADO:\n`;
        detalhes += `• Código: ${produto.codigo || 'N/A'}\n`;
        detalhes += `• Descrição: ${produto.descricao || 'N/A'}\n`;
        detalhes += `• Unidade: ${produto.unidade || 'N/A'}\n`;
        detalhes += `• Tipo: ${produto.tipo || 'N/A'}\n`;
        detalhes += `• Família: ${produto.familia || 'N/A'}\n\n`;
      } else {
        detalhes += `❌ PRODUTO NÃO ENCONTRADO\n\n`;
      }

      if (estoque) {
        detalhes += `📦 ESTOQUE:\n`;
        detalhes += `• Saldo Total: ${(estoque.saldo || 0).toFixed(3)}\n`;
        detalhes += `• Saldo Reservado: ${(estoque.saldoReservado || 0).toFixed(3)}\n`;
        detalhes += `• Saldo Empenhado: ${(estoque.saldoEmpenhado || 0).toFixed(3)}\n`;
        detalhes += `• Saldo Disponível: ${((estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0)).toFixed(3)}\n`;

        const armazem = armazens.find(a => a.id === estoque.armazemId);
        detalhes += `• Armazém: ${armazem?.nome || estoque.armazemId}\n\n`;
      } else {
        detalhes += `❌ ESTOQUE NÃO ENCONTRADO\n\n`;
      }

      detalhes += `💡 DICA: Use o botão "🔍 Decodificar" para abrir o decodificador completo.`;

      alert(detalhes);
    };

    // Abrir decodificador com código pré-preenchido
    window.abrirDecodificadorComCodigo = function(codigo) {
      abrirDecodificadorProdutos();
      setTimeout(() => {
        document.getElementById('codigoCriptico').value = codigo;
        buscarProdutoPorCodigo();
      }, 100);
    };

    // Corrigir estoque diretamente
    window.corrigirEstoqueDireto = function(produtoId) {
      const estoque = estoques.find(e => e.produtoId === produtoId);
      const produto = produtos.find(p => p.id === produtoId);

      if (!estoque) {
        alert('❌ Estoque não encontrado para este produto!');
        return;
      }

      const codigoLegivel = gerarCodigoLegivel(produtoId, produto);
      const descricaoProduto = produto?.descricao || 'Produto não identificado';

      const novoSaldo = prompt(
        `🔧 CORREÇÃO RÁPIDA DE ESTOQUE\n\n` +
        `Produto: ${codigoLegivel}\n` +
        `Descrição: ${descricaoProduto}\n` +
        `Saldo Atual: ${(estoque.saldo || 0).toFixed(3)}\n\n` +
        `Digite o novo saldo correto:`,
        '0.000'
      );

      if (novoSaldo === null) return; // Cancelou

      const saldoNumerico = parseFloat(novoSaldo);
      if (isNaN(saldoNumerico)) {
        alert('❌ Valor inválido! Digite um número.');
        return;
      }

      if (!confirm(`✅ Confirma a correção?\n\nDe: ${(estoque.saldo || 0).toFixed(3)}\nPara: ${saldoNumerico.toFixed(3)}`)) {
        return;
      }

      // Aqui seria a integração com Firebase para atualizar o estoque
      console.log(`🔧 Corrigindo estoque ${estoque.id}: ${estoque.saldo} → ${saldoNumerico}`);

      // Simular atualização local (em produção seria updateDoc do Firebase)
      estoque.saldo = saldoNumerico;

      mostrarNotificacao(`✅ Estoque corrigido: ${codigoLegivel}`, 'success', 3000);

      // Forçar atualização da interface
      debounceUpdate('correcaoEstoque');
    };

    // Funções do modal
    window.fecharModalDecodificador = function() {
      document.getElementById('modalDecodificadorProdutos').style.display = 'none';
    };

    window.exportarRelatorioDecodificacao = function() {
      // Implementar exportação de relatório
      alert('📊 Funcionalidade de exportação será implementada');
    };

    window.corrigirEstoquesLote = function() {
      // Implementar correção em lote
      alert('🔧 Funcionalidade de correção em lote será implementada');
    };

    // ===================================================================
    // DIAGNÓSTICO COMPLETO DE IMPOSSIBILIDADE
    // ===================================================================

    // Função principal para diagnóstico completo
    window.executarDiagnosticoCompleto = async function() {
      try {
        console.log('🔍 Iniciando diagnóstico completo de impossibilidade...');
        mostrarNotificacao('🔍 Executando diagnóstico profundo...', 'info', 3000);

        // 1. ANÁLISE BÁSICA
        const opsPendentes = ordensProducao.filter(op => op.status === 'Pendente');
        if (opsPendentes.length === 0) {
          alert('ℹ️ Nenhuma OP pendente para diagnosticar.');
          return;
        }

        // 2. ANÁLISE DETALHADA DE IMPOSSIBILIDADE
        const diagnostico = await analisarImpossibilidadeCompleta(opsPendentes);

        // 3. MOSTRAR RELATÓRIO DE DIAGNÓSTICO
        mostrarRelatorioImpossibilidade(diagnostico);

        console.log('✅ Diagnóstico completo concluído');
        mostrarNotificacao('✅ Diagnóstico concluído!', 'success', 2000);

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
        mostrarNotificacao('❌ Erro no diagnóstico', 'error', 5000);
        alert('❌ Erro no diagnóstico: ' + error.message);
      }
    };

    // Análise completa de impossibilidade
    async function analisarImpossibilidadeCompleta(opsPendentes) {
      const diagnostico = {
        totalOPs: opsPendentes.length,
        opsAnalisadas: [],
        materiaisDisponiveis: {},
        materiaisNecessarios: {},
        gargalosIdentificados: [],
        oportunidadesProducao: [],
        sugestoesDesbloqueio: [],
        resumoImpossibilidade: {}
      };

      // 1. MAPEAR TODOS OS MATERIAIS DISPONÍVEIS
      console.log('📦 Mapeando materiais disponíveis...');
      diagnostico.materiaisDisponiveis = mapearMateriaisDisponiveis();

      // 2. MAPEAR TODOS OS MATERIAIS NECESSÁRIOS
      console.log('📋 Mapeando materiais necessários...');
      diagnostico.materiaisNecessarios = mapearMateriaisNecessarios(opsPendentes);

      // 3. ANÁLISE DETALHADA DE CADA OP
      console.log('🔍 Analisando cada OP em detalhes...');
      for (const op of opsPendentes) {
        const analiseDetalhada = await analisarOPDetalhadamente(op, diagnostico.materiaisDisponiveis);
        diagnostico.opsAnalisadas.push(analiseDetalhada);
      }

      // 4. IDENTIFICAR GARGALOS CRÍTICOS
      console.log('🚫 Identificando gargalos críticos...');
      diagnostico.gargalosIdentificados = identificarGargalosCriticos(diagnostico);

      // 5. BUSCAR OPORTUNIDADES DE PRODUÇÃO
      console.log('🎯 Buscando oportunidades de produção...');
      diagnostico.oportunidadesProducao = buscarOportunidadesProducao(diagnostico);

      // 6. GERAR SUGESTÕES DE DESBLOQUEIO
      console.log('💡 Gerando sugestões de desbloqueio...');
      diagnostico.sugestoesDesbloqueio = gerarSugestoesDesbloqueio(diagnostico);

      // 7. RESUMO FINAL
      diagnostico.resumoImpossibilidade = gerarResumoImpossibilidade(diagnostico);

      return diagnostico;
    }

    // Mapear todos os materiais disponíveis no estoque (POR ARMAZÉM)
    function mapearMateriaisDisponiveis() {
      const materiaisDisponiveis = {};

      estoques.forEach(estoque => {
        const produto = produtos.find(p => p.id === estoque.produtoId);
        const armazem = armazens.find(a => a.id === estoque.armazemId);
        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;
        const saldoDisponivel = Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);

        // Chave única: produtoId + armazemId
        const chaveEstoque = `${estoque.produtoId}_${estoque.armazemId}`;

        materiaisDisponiveis[chaveEstoque] = {
          produto: produto,
          codigo: produto?.codigo || estoque.produtoId,
          descricao: produto?.descricao || 'Sem descrição',
          unidade: produto?.unidade || 'UN',
          saldoTotal,
          saldoReservado,
          saldoEmpenhado,
          saldoDisponivel,
          armazemId: estoque.armazemId,
          armazemNome: armazem?.nome || 'Armazém não identificado',
          armazemCodigo: armazem?.codigo || estoque.armazemId,
          status: saldoDisponivel > 0 ? 'DISPONIVEL' : 'ZERADO'
        };
      });

      // Também criar um mapa consolidado por produto (soma de todos os armazéns)
      const materiaisConsolidados = {};
      Object.values(materiaisDisponiveis).forEach(material => {
        const produtoId = material.produto?.id;
        if (!produtoId) return;

        if (!materiaisConsolidados[produtoId]) {
          materiaisConsolidados[produtoId] = {
            produto: material.produto,
            codigo: material.codigo,
            descricao: material.descricao,
            unidade: material.unidade,
            saldoTotalConsolidado: 0,
            saldoDisponivelConsolidado: 0,
            armazensComEstoque: [],
            armazensDetalhes: {}
          };
        }

        materiaisConsolidados[produtoId].saldoTotalConsolidado += material.saldoTotal;
        materiaisConsolidados[produtoId].saldoDisponivelConsolidado += material.saldoDisponivel;

        if (material.saldoDisponivel > 0) {
          materiaisConsolidados[produtoId].armazensComEstoque.push({
            armazemId: material.armazemId,
            armazemNome: material.armazemNome,
            saldoDisponivel: material.saldoDisponivel
          });
        }

        materiaisConsolidados[produtoId].armazensDetalhes[material.armazemId] = {
          armazemNome: material.armazemNome,
          saldoTotal: material.saldoTotal,
          saldoDisponivel: material.saldoDisponivel,
          saldoReservado: material.saldoReservado,
          saldoEmpenhado: material.saldoEmpenhado
        };
      });

      return {
        porArmazem: materiaisDisponiveis,
        consolidado: materiaisConsolidados
      };
    }

    // Mapear todos os materiais necessários para as OPs
    function mapearMateriaisNecessarios(opsPendentes) {
      const materiaisNecessarios = {};

      opsPendentes.forEach(op => {
        if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
          op.materiaisNecessarios.forEach(material => {
            const materialId = material.produtoId;

            if (!materiaisNecessarios[materialId]) {
              const produto = produtos.find(p => p.id === materialId);
              materiaisNecessarios[materialId] = {
                produto: produto,
                codigo: produto?.codigo || materialId,
                descricao: produto?.descricao || 'Sem descrição',
                unidade: produto?.unidade || 'UN',
                quantidadeTotal: 0,
                opsQueUsam: [],
                prioridadeMaxima: 'BAIXA'
              };
            }

            materiaisNecessarios[materialId].quantidadeTotal += material.quantidade || 0;
            materiaisNecessarios[materialId].opsQueUsam.push({
              opId: op.id,
              opNumero: op.numero,
              quantidade: material.quantidade,
              prioridade: op.prioridade || 'NORMAL'
            });

            // Atualizar prioridade máxima
            const prioridades = ['BAIXA', 'NORMAL', 'MÉDIA', 'ALTA'];
            const prioridadeAtual = prioridades.indexOf(materiaisNecessarios[materialId].prioridadeMaxima);
            const prioridadeOP = prioridades.indexOf(op.prioridade || 'NORMAL');
            if (prioridadeOP > prioridadeAtual) {
              materiaisNecessarios[materialId].prioridadeMaxima = op.prioridade || 'NORMAL';
            }
          });
        }
      });

      return materiaisNecessarios;
    }

    // Análise detalhada de uma OP específica (COM CONTROLE DE ARMAZÉM)
    async function analisarOPDetalhadamente(op, materiaisDisponiveis) {
      const produto = produtos.find(p => p.id === op.produtoId);
      const armazemProducao = armazens.find(a => a.id === op.armazemProducaoId);

      const analise = {
        op: op,
        produto: produto,
        armazemProducaoId: op.armazemProducaoId,
        armazemProducaoNome: armazemProducao?.nome || 'Armazém não identificado',
        podeProuzir: false,
        quantidadeMaximaPossivel: 0,
        limitadoPor: null,
        materiaisAnalise: [],
        motivosImpossibilidade: [],
        percentualViabilidade: 0,
        alertasTransferencia: []
      };

      if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
        // OP sem materiais necessários - pode produzir
        analise.podeProuzir = true;
        analise.quantidadeMaximaPossivel = op.quantidade;
        analise.percentualViabilidade = 100;
        analise.motivosImpossibilidade.push('OP sem estrutura de materiais definida');
        return analise;
      }

      let quantidadeMaxima = op.quantidade;
      let podeProuzirTudo = true;

      // Analisar cada material necessário
      for (const material of op.materiaisNecessarios) {
        // 1. VERIFICAR NO ARMAZÉM DE PRODUÇÃO ESPECÍFICO
        const armazemMaterial = material.armazemId || op.armazemProducaoId;
        const chaveEstoque = `${material.produtoId}_${armazemMaterial}`;
        const materialNoArmazem = materiaisDisponiveis.porArmazem[chaveEstoque];

        // 2. VERIFICAR EM TODOS OS ARMAZÉNS (para sugestões de transferência)
        const materialConsolidado = materiaisDisponiveis.consolidado[material.produtoId];

        const materialAnalise = {
          produtoId: material.produtoId,
          codigo: material.codigo || material.produtoId,
          necessario: material.quantidade,
          armazemNecessario: armazemMaterial,
          armazemNecessarioNome: armazens.find(a => a.id === armazemMaterial)?.nome || 'N/A',
          disponivelNoArmazem: materialNoArmazem ? materialNoArmazem.saldoDisponivel : 0,
          disponivelTotal: materialConsolidado ? materialConsolidado.saldoDisponivelConsolidado : 0,
          suficiente: false,
          quantidadeMaximaPossivel: 0,
          problema: null,
          podeTransferir: false,
          armazensAlternativos: []
        };

        // 3. ANÁLISE NO ARMAZÉM CORRETO (CONSIDERANDO RESERVAS DA OP)
        if (!materialNoArmazem) {
          materialAnalise.problema = `Material não encontrado no armazém ${materialAnalise.armazemNecessarioNome}`;
          materialAnalise.suficiente = false;
          podeProuzirTudo = false;

          // Verificar se existe em outros armazéns
          if (materialConsolidado && materialConsolidado.armazensComEstoque.length > 0) {
            materialAnalise.podeTransferir = true;
            materialAnalise.armazensAlternativos = materialConsolidado.armazensComEstoque;
            analise.alertasTransferencia.push({
              material: materialAnalise.codigo,
              armazemOrigem: materialAnalise.armazemNecessarioNome,
              armazensComEstoque: materialConsolidado.armazensComEstoque
            });
          }

          analise.motivosImpossibilidade.push(
            `${materialAnalise.codigo}: não encontrado no armazém ${materialAnalise.armazemNecessarioNome}${materialAnalise.podeTransferir ? ' (disponível em outros armazéns)' : ''}`
          );
        } else {
          // LÓGICA CORRIGIDA: CONSIDERAR RESERVAS DA OP
          const saldoTotal = materialNoArmazem.saldoTotal;
          const saldoReservado = materialNoArmazem.saldoReservado;
          const saldoEmpenhado = materialNoArmazem.saldoEmpenhado;
          const saldoLivre = materialNoArmazem.saldoDisponivel; // Já calculado: total - reservado - empenhado
          const necessario = material.quantidade;

          // VERIFICAR SE JÁ ESTÁ RESERVADO PARA ESTA OP
          let quantidadeReservadaParaEstaOP = 0;
          if (op.materiaisNecessarios) {
            const materialNaOP = op.materiaisNecessarios.find(m => m.produtoId === material.produtoId);
            if (materialNaOP && materialNaOP.quantidadeReservada) {
              quantidadeReservadaParaEstaOP = materialNaOP.quantidadeReservada;
            }
          }

          // DISPONIBILIDADE REAL = SALDO LIVRE + RESERVADO PARA ESTA OP
          const disponivelParaEstaOP = saldoLivre + quantidadeReservadaParaEstaOP;

          // Adicionar informações de reserva na análise
          materialAnalise.saldoTotal = saldoTotal;
          materialAnalise.saldoReservado = saldoReservado;
          materialAnalise.saldoEmpenhado = saldoEmpenhado;
          materialAnalise.saldoLivre = saldoLivre;
          materialAnalise.quantidadeReservadaParaEstaOP = quantidadeReservadaParaEstaOP;
          materialAnalise.disponivelParaEstaOP = disponivelParaEstaOP;

          if (disponivelParaEstaOP >= necessario) {
            materialAnalise.suficiente = true;
            materialAnalise.quantidadeMaximaPossivel = Math.floor(disponivelParaEstaOP / (necessario / op.quantidade));

            // Indicar se está usando reserva
            if (quantidadeReservadaParaEstaOP > 0) {
              materialAnalise.usandoReserva = true;
              materialAnalise.observacao = `Usando ${quantidadeReservadaParaEstaOP.toFixed(3)} já reservado para esta OP`;
            }
          } else {
            materialAnalise.suficiente = false;
            materialAnalise.quantidadeMaximaPossivel = Math.floor(disponivelParaEstaOP / (necessario / op.quantidade));

            const falta = necessario - disponivelParaEstaOP;
            materialAnalise.problema = disponivelParaEstaOP <= 0 ? 'Sem estoque no armazém' : 'Estoque insuficiente no armazém';

            podeProuzirTudo = false;
            quantidadeMaxima = Math.min(quantidadeMaxima, materialAnalise.quantidadeMaximaPossivel);

            if (!analise.limitadoPor || materialAnalise.quantidadeMaximaPossivel < quantidadeMaxima) {
              analise.limitadoPor = material.produtoId;
            }

            // Verificar se transferência resolveria
            if (materialConsolidado && materialConsolidado.saldoDisponivelConsolidado >= falta) {
              materialAnalise.podeTransferir = true;
              materialAnalise.armazensAlternativos = materialConsolidado.armazensComEstoque;
              analise.alertasTransferencia.push({
                material: materialAnalise.codigo,
                faltaNoArmazem: falta,
                disponivelTotal: materialConsolidado.saldoDisponivelConsolidado,
                armazensComEstoque: materialConsolidado.armazensComEstoque
              });
            }

            let motivoDetalhado = `${materialAnalise.codigo}: falta ${falta.toFixed(3)} ${materialNoArmazem.unidade}`;
            if (quantidadeReservadaParaEstaOP > 0) {
              motivoDetalhado += ` (já tem ${quantidadeReservadaParaEstaOP.toFixed(3)} reservado)`;
            }
            motivoDetalhado += ` no armazém ${materialAnalise.armazemNecessarioNome}`;
            if (materialAnalise.podeTransferir) {
              motivoDetalhado += ' (transferência resolveria)';
            }

            analise.motivosImpossibilidade.push(motivoDetalhado);
          }
        }

        analise.materiaisAnalise.push(materialAnalise);
      }

      analise.podeProuzir = podeProuzirTudo;
      analise.quantidadeMaximaPossivel = Math.max(0, quantidadeMaxima);
      analise.percentualViabilidade = (analise.quantidadeMaximaPossivel / op.quantidade * 100);

      return analise;
    }

    // Identificar gargalos críticos
    function identificarGargalosCriticos(diagnostico) {
      const gargalos = [];
      const materiaisCriticos = {};

      // Analisar materiais que bloqueiam múltiplas OPs
      Object.values(diagnostico.materiaisNecessarios).forEach(material => {
        const materialDisponivel = diagnostico.materiaisDisponiveis[material.produto?.id];
        const disponivel = materialDisponivel ? materialDisponivel.saldoDisponivel : 0;
        const necessario = material.quantidadeTotal;

        if (disponivel < necessario) {
          const falta = necessario - disponivel;
          const opsAfetadas = material.opsQueUsam.length;
          const criticidade = calcularCriticidade(falta, necessario, opsAfetadas, material.prioridadeMaxima);

          gargalos.push({
            materialId: material.produto?.id,
            codigo: material.codigo,
            descricao: material.descricao,
            unidade: material.unidade,
            disponivel,
            necessario,
            falta,
            opsAfetadas,
            prioridadeMaxima: material.prioridadeMaxima,
            criticidade,
            opsDetalhes: material.opsQueUsam
          });
        }
      });

      // Ordenar por criticidade
      gargalos.sort((a, b) => b.criticidade - a.criticidade);

      return gargalos;
    }

    // Calcular criticidade de um gargalo
    function calcularCriticidade(falta, necessario, opsAfetadas, prioridade) {
      const percentualFalta = (falta / necessario) * 100;
      const pesoPrioridade = { 'BAIXA': 1, 'NORMAL': 2, 'MÉDIA': 3, 'ALTA': 4 };
      const pesoOPs = Math.min(opsAfetadas * 10, 50); // Máximo 50 pontos
      const pesoFalta = Math.min(percentualFalta, 100); // Máximo 100 pontos
      const pesoPrio = (pesoPrioridade[prioridade] || 2) * 10; // Máximo 40 pontos

      return pesoFalta + pesoOPs + pesoPrio;
    }

    // Buscar oportunidades de produção
    function buscarOportunidadesProducao(diagnostico) {
      const oportunidades = [];

      // 1. OPs que podem ser produzidas parcialmente
      diagnostico.opsAnalisadas.forEach(analise => {
        if (!analise.podeProuzir && analise.quantidadeMaximaPossivel > 0) {
          oportunidades.push({
            tipo: 'PRODUCAO_PARCIAL',
            opId: analise.op.id,
            opNumero: analise.op.numero,
            produtoCodigo: analise.produto?.codigo,
            quantidadeOriginal: analise.op.quantidade,
            quantidadePossivel: analise.quantidadeMaximaPossivel,
            percentual: analise.percentualViabilidade,
            limitadoPor: analise.limitadoPor,
            descricao: `Produzir ${analise.quantidadeMaximaPossivel} de ${analise.op.quantidade} (${analise.percentualViabilidade.toFixed(1)}%)`
          });
        }
      });

      // 2. Materiais próximos do limite
      Object.values(diagnostico.materiaisDisponiveis).forEach(material => {
        if (material.saldoDisponivel > 0 && material.saldoDisponivel < 10) { // Menos de 10 unidades
          const materialNecessario = diagnostico.materiaisNecessarios[material.produto?.id];
          if (materialNecessario) {
            oportunidades.push({
              tipo: 'MATERIAL_LIMITE',
              materialId: material.produto?.id,
              codigo: material.codigo,
              disponivel: material.saldoDisponivel,
              unidade: material.unidade,
              opsAfetadas: materialNecessario.opsQueUsam.length,
              descricao: `Material ${material.codigo} com apenas ${material.saldoDisponivel} ${material.unidade} disponível`
            });
          }
        }
      });

      return oportunidades;
    }

    // Gerar sugestões de desbloqueio
    function gerarSugestoesDesbloqueio(diagnostico) {
      const sugestoes = [];

      // 1. Compras urgentes baseadas em gargalos
      const top5Gargalos = diagnostico.gargalosIdentificados.slice(0, 5);
      if (top5Gargalos.length > 0) {
        sugestoes.push({
          tipo: 'COMPRA_URGENTE',
          prioridade: 'ALTA',
          titulo: 'Compras Urgentes - Top 5 Gargalos',
          descricao: 'Solicitar compra imediata dos materiais mais críticos',
          materiais: top5Gargalos.map(g => ({
            codigo: g.codigo,
            falta: g.falta,
            unidade: g.unidade,
            opsAfetadas: g.opsAfetadas
          })),
          impacto: `Desbloquearia ${top5Gargalos.reduce((total, g) => total + g.opsAfetadas, 0)} OP(s)`
        });
      }

      // 2. Transferências entre armazéns
      sugestoes.push({
        tipo: 'TRANSFERENCIA',
        prioridade: 'MÉDIA',
        titulo: 'Verificar Transferências',
        descricao: 'Verificar se há materiais em outros armazéns que podem ser transferidos',
        acao: 'Consultar saldos em todos os armazéns para os materiais críticos'
      });

      // 3. Produção parcial estratégica
      const oportunidadesParciais = diagnostico.oportunidadesProducao.filter(o => o.tipo === 'PRODUCAO_PARCIAL');
      if (oportunidadesParciais.length > 0) {
        const melhorOportunidade = oportunidadesParciais.sort((a, b) => b.percentual - a.percentual)[0];
        sugestoes.push({
          tipo: 'PRODUCAO_PARCIAL',
          prioridade: 'MÉDIA',
          titulo: 'Produção Parcial Estratégica',
          descricao: `Produzir parcialmente OP ${melhorOportunidade.opNumero} (${melhorOportunidade.percentual.toFixed(1)}% viável)`,
          detalhes: melhorOportunidade
        });
      }

      // 4. Revisão de prioridades
      sugestoes.push({
        tipo: 'REVISAO_PRIORIDADES',
        prioridade: 'BAIXA',
        titulo: 'Revisar Prioridades',
        descricao: 'Considerar alterar prioridades das OPs conforme disponibilidade de material',
        acao: 'Priorizar OPs com materiais disponíveis'
      });

      return sugestoes;
    }

    // Gerar resumo final da impossibilidade
    function gerarResumoImpossibilidade(diagnostico) {
      const totalOPs = diagnostico.totalOPs;
      const opsViaveis = diagnostico.opsAnalisadas.filter(a => a.podeProuzir).length;
      const opsParciais = diagnostico.opsAnalisadas.filter(a => !a.podeProuzir && a.quantidadeMaximaPossivel > 0).length;
      const opsInviaveis = diagnostico.opsAnalisadas.filter(a => a.quantidadeMaximaPossivel === 0).length;

      const totalMateriais = Object.keys(diagnostico.materiaisDisponiveis.porArmazem).length;
      const materiaisDisponiveis = Object.values(diagnostico.materiaisDisponiveis.porArmazem).filter(m => m.saldoDisponivel > 0).length;
      const materiaisZerados = totalMateriais - materiaisDisponiveis;

      // Contar alertas de transferência
      let totalAlertasTransferencia = 0;
      diagnostico.opsAnalisadas.forEach(analise => {
        if (analise.alertasTransferencia) {
          totalAlertasTransferencia += analise.alertasTransferencia.length;
        }
      });

      return {
        totalOPs,
        opsViaveis,
        opsParciais,
        opsInviaveis,
        percentualViabilidade: Math.round((opsViaveis / totalOPs) * 100),
        totalMateriais,
        materiaisDisponiveis,
        materiaisZerados,
        percentualMateriaisDisponiveis: Math.round((materiaisDisponiveis / totalMateriais) * 100),
        gargalosCriticos: diagnostico.gargalosIdentificados.length,
        oportunidades: diagnostico.oportunidadesProducao.length,
        alertasTransferencia: totalAlertasTransferencia,
        confirmacaoImpossibilidade: opsViaveis === 0 && opsParciais === 0
      };
    }

    // Função para solicitar transferência
    window.solicitarTransferencia = function(materialCodigo, opNumero) {
      const mensagem = `🔄 SOLICITAÇÃO DE TRANSFERÊNCIA\n\n` +
                      `Material: ${materialCodigo}\n` +
                      `Para OP: ${opNumero}\n\n` +
                      `Esta funcionalidade pode ser integrada com:\n` +
                      `• Sistema de transferências entre armazéns\n` +
                      `• Workflow de aprovação\n` +
                      `• Notificações automáticas\n` +
                      `• Controle de movimentação\n\n` +
                      `Deseja implementar esta integração?`;

      alert(mensagem);

      // Aqui seria a integração real com sistema de transferências
      mostrarNotificacao(`🔄 Transferência solicitada: ${materialCodigo} para OP ${opNumero}`, 'info', 3000);
    };

    // Mostrar relatório de impossibilidade
    function mostrarRelatorioImpossibilidade(diagnostico) {
      // Salvar diagnóstico globalmente
      window.diagnosticoAtual = diagnostico;

      const resumo = diagnostico.resumoImpossibilidade;

      let html = `
        <div style="padding: 20px;">
          <!-- RESUMO EXECUTIVO -->
          <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-triangle"></i> Diagnóstico de Impossibilidade
              ${resumo.confirmacaoImpossibilidade ? '<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 10px;">CONFIRMADO</span>' : '<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 10px;">PARCIAL</span>'}
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;">${resumo.totalOPs}</div>
                <div style="font-size: 12px;">OPs Analisadas</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: ${resumo.opsViaveis > 0 ? '#90EE90' : '#FFB6C1'};">${resumo.opsViaveis}</div>
                <div style="font-size: 12px;">Viáveis (${resumo.percentualViabilidade}%)</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFD700;">${resumo.opsParciais}</div>
                <div style="font-size: 12px;">Parciais</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFB6C1;">${resumo.opsInviaveis}</div>
                <div style="font-size: 12px;">Impossíveis</div>
              </div>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
              <h6 style="margin: 0 0 8px 0; display: flex; align-items: center; gap: 5px;">
                <i class="fas fa-warehouse"></i> Status do Estoque por Armazém
              </h6>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; font-size: 12px;">
                <div>📦 <strong>${resumo.totalMateriais}</strong> posições de estoque</div>
                <div>✅ <strong>${resumo.materiaisDisponiveis}</strong> com saldo</div>
                <div>❌ <strong>${resumo.materiaisZerados}</strong> zeradas</div>
                <div>📊 <strong>${resumo.percentualMateriaisDisponiveis}%</strong> disponibilidade</div>
              </div>
              <div style="margin-top: 8px; font-size: 11px; color: rgba(255,255,255,0.8);">
                <i class="fas fa-info-circle"></i> Análise considera armazém específico de cada OP
              </div>
            </div>
          </div>
      `;

      // CONFIRMAÇÃO DE IMPOSSIBILIDADE
      if (resumo.confirmacaoImpossibilidade) {
        html += `
          <div style="background: #f8d7da; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px; text-align: center;">
            <h4 style="color: #721c24; margin: 0 0 10px 0; display: flex; align-items: center; justify-content: center; gap: 10px;">
              <i class="fas fa-ban" style="font-size: 24px;"></i> CONFIRMAÇÃO DE IMPOSSIBILIDADE
            </h4>
            <p style="color: #721c24; margin: 0; font-size: 16px; font-weight: bold;">
              ✅ CONFIRMADO: Com os materiais disponíveis no estoque atual, NÃO É POSSÍVEL produzir nenhuma OP completamente.
            </p>
            <p style="color: #721c24; margin: 10px 0 0 0; font-size: 14px;">
              ${resumo.opsParciais > 0 ? `Porém, ${resumo.opsParciais} OP(s) podem ser produzidas parcialmente.` : 'Nenhuma produção parcial é possível.'}
            </p>
          </div>
        `;
      } else {
        html += `
          <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin-bottom: 20px; text-align: center;">
            <h4 style="color: #856404; margin: 0 0 10px 0; display: flex; align-items: center; justify-content: center; gap: 10px;">
              <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i> IMPOSSIBILIDADE PARCIAL
            </h4>
            <p style="color: #856404; margin: 0; font-size: 16px; font-weight: bold;">
              ⚠️ A maioria das OPs não pode ser produzida, mas ${resumo.opsViaveis + resumo.opsParciais} OP(s) têm alguma viabilidade.
            </p>
          </div>
        `;
      }

      // GARGALOS CRÍTICOS
      if (diagnostico.gargalosIdentificados.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #dc3545; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-circle"></i> Gargalos Críticos (${diagnostico.gargalosIdentificados.length})
            </h4>
            <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; border-radius: 5px;">
              <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 5px; overflow: hidden;">
                <thead>
                  <tr style="background: linear-gradient(135deg, #dc3545, #c82333); color: white;">
                    <th style="padding: 12px; text-align: left;">Material</th>
                    <th style="padding: 12px; text-align: center;">Disponível</th>
                    <th style="padding: 12px; text-align: center;">Necessário</th>
                    <th style="padding: 12px; text-align: center;">Falta</th>
                    <th style="padding: 12px; text-align: center;">OPs Afetadas</th>
                    <th style="padding: 12px; text-align: center;">Criticidade</th>
                  </tr>
                </thead>
                <tbody>
        `;

        diagnostico.gargalosIdentificados.slice(0, 10).forEach((gargalo, index) => {
          const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';
          const criticidadeCor = gargalo.criticidade > 150 ? '#dc3545' : gargalo.criticidade > 100 ? '#fd7e14' : '#ffc107';

          html += `
            <tr style="background: ${rowColor};">
              <td style="padding: 10px;">
                <div style="font-weight: bold;">${gargalo.codigo}</div>
                <div style="font-size: 12px; color: #6c757d;">${gargalo.descricao}</div>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="color: #dc3545; font-weight: bold;">${gargalo.disponivel.toFixed(3)}</span>
                <br><small>${gargalo.unidade}</small>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="font-weight: bold;">${gargalo.necessario.toFixed(3)}</span>
                <br><small>${gargalo.unidade}</small>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="color: #dc3545; font-weight: bold; font-size: 14px;">${gargalo.falta.toFixed(3)}</span>
                <br><small>${gargalo.unidade}</small>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-weight: bold;">
                  ${gargalo.opsAfetadas}
                </span>
              </td>
              <td style="padding: 10px; text-align: center;">
                <div style="background: ${criticidadeCor}; color: white; padding: 4px 8px; border-radius: 8px; font-size: 12px; font-weight: bold;">
                  ${gargalo.criticidade.toFixed(0)}
                </div>
                <small style="color: #6c757d;">${gargalo.prioridadeMaxima}</small>
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // ALERTAS DE TRANSFERÊNCIA
      const alertasTransferencia = [];
      diagnostico.opsAnalisadas.forEach(analise => {
        if (analise.alertasTransferencia && analise.alertasTransferencia.length > 0) {
          analise.alertasTransferencia.forEach(alerta => {
            alerta.opNumero = analise.op.numero;
            alerta.armazemProducao = analise.armazemProducaoNome;
            alertasTransferencia.push(alerta);
          });
        }
      });

      if (alertasTransferencia.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #17a2b8; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exchange-alt"></i> Oportunidades de Transferência (${alertasTransferencia.length})
            </h4>
            <div style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; border-radius: 5px;">
              <div style="background: #bee5eb; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                <p style="margin: 0; color: #0c5460; font-weight: bold;">
                  <i class="fas fa-lightbulb"></i> Materiais disponíveis em outros armazéns podem resolver alguns bloqueios!
                </p>
              </div>
              <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 5px; overflow: hidden;">
                <thead>
                  <tr style="background: linear-gradient(135deg, #17a2b8, #138496); color: white;">
                    <th style="padding: 12px; text-align: left;">OP</th>
                    <th style="padding: 12px; text-align: left;">Material</th>
                    <th style="padding: 12px; text-align: center;">Armazém Produção</th>
                    <th style="padding: 12px; text-align: center;">Disponível Em</th>
                    <th style="padding: 12px; text-align: center;">Ação</th>
                  </tr>
                </thead>
                <tbody>
        `;

        alertasTransferencia.slice(0, 15).forEach((alerta, index) => {
          const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

          html += `
            <tr style="background: ${rowColor};">
              <td style="padding: 10px;">
                <div style="font-weight: bold;">${alerta.opNumero}</div>
                <div style="font-size: 12px; color: #6c757d;">${alerta.armazemProducao}</div>
              </td>
              <td style="padding: 10px;">
                <div style="font-weight: bold;">${alerta.material}</div>
                ${alerta.faltaNoArmazem ? `<div style="font-size: 12px; color: #dc3545;">Falta: ${alerta.faltaNoArmazem.toFixed(3)}</div>` : ''}
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">
                  SEM ESTOQUE
                </span>
              </td>
              <td style="padding: 10px; text-align: center;">
          `;

          alerta.armazensComEstoque.forEach((arm, armIndex) => {
            if (armIndex < 2) { // Mostrar apenas os 2 primeiros
              html += `
                <div style="margin: 2px 0;">
                  <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px;">
                    ${arm.armazemNome}: ${arm.saldoDisponivel.toFixed(3)}
                  </span>
                </div>
              `;
            }
          });

          if (alerta.armazensComEstoque.length > 2) {
            html += `<div style="font-size: 10px; color: #6c757d;">+${alerta.armazensComEstoque.length - 2} outros</div>`;
          }

          html += `
              </td>
              <td style="padding: 10px; text-align: center;">
                <button onclick="solicitarTransferencia('${alerta.material}', '${alerta.opNumero}')"
                        style="background: #17a2b8; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer;">
                  🔄 Transferir
                </button>
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      html += `</div>`;

      // Mostrar no modal
      document.getElementById('conteudoDiagnosticoImpossibilidade').innerHTML = html;
      document.getElementById('modalDiagnosticoImpossibilidade').style.display = 'block';

      console.log('📊 Diagnóstico detalhado:', diagnostico);
    }

    // Funções do modal de diagnóstico
    window.fecharModalDiagnostico = function() {
      document.getElementById('modalDiagnosticoImpossibilidade').style.display = 'none';
    };

    window.exportarDiagnostico = function() {
      if (!window.diagnosticoAtual) return;

      const diagnostico = window.diagnosticoAtual;
      const resumo = diagnostico.resumoImpossibilidade;

      let texto = `DIAGNÓSTICO COMPLETO DE IMPOSSIBILIDADE - ${new Date().toLocaleString()}\n\n`;
      texto += `RESUMO:\n`;
      texto += `- Total de OPs: ${resumo.totalOPs}\n`;
      texto += `- OPs Viáveis: ${resumo.opsViaveis}\n`;
      texto += `- OPs Parciais: ${resumo.opsParciais}\n`;
      texto += `- OPs Impossíveis: ${resumo.opsInviaveis}\n`;
      texto += `- Confirmação de Impossibilidade: ${resumo.confirmacaoImpossibilidade ? 'SIM' : 'NÃO'}\n\n`;

      texto += `GARGALOS CRÍTICOS:\n`;
      diagnostico.gargalosIdentificados.forEach((gargalo, index) => {
        texto += `${index + 1}. ${gargalo.codigo} - Falta: ${gargalo.falta.toFixed(3)} ${gargalo.unidade} (${gargalo.opsAfetadas} OPs afetadas)\n`;
      });

      const blob = new Blob([texto], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `diagnostico-impossibilidade-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      window.URL.revokeObjectURL(url);

      mostrarNotificacao('📊 Diagnóstico exportado!', 'success', 2000);
    };

    window.executarSugestoes = function() {
      if (!window.diagnosticoAtual) return;

      const sugestoes = window.diagnosticoAtual.sugestoesDesbloqueio;
      if (sugestoes.length === 0) {
        alert('Nenhuma sugestão disponível.');
        return;
      }

      let mensagem = '💡 SUGESTÕES DE DESBLOQUEIO:\n\n';
      sugestoes.forEach((sugestao, index) => {
        mensagem += `${index + 1}. ${sugestao.titulo} (${sugestao.prioridade})\n`;
        mensagem += `   ${sugestao.descricao}\n\n`;
      });

      alert(mensagem);
    };

    // ===================================================================
    // ANÁLISE DE PRODUÇÃO VIÁVEL - PCP INTELIGENTE (BOTTOM-UP)
    // ===================================================================

    // Função para análise bottom-up (de baixo para cima)
    async function analisarProducaoBottomUp() {
      try {
        console.log('🔄 Iniciando análise bottom-up...');

        // 1. MAPEAR ESTRUTURAS POR NÍVEL
        const estruturasPorNivel = mapearEstruturasPorNivel();

        // 2. ANALISAR DISPONIBILIDADE POR NÍVEL (DO MAIS BAIXO PARA O MAIS ALTO)
        const disponibilidadePorNivel = {};
        const niveisOrdenados = Object.keys(estruturasPorNivel).sort((a, b) => b - a); // Maior para menor

        for (const nivel of niveisOrdenados) {
          console.log(`📊 Analisando nível ${nivel}...`);
          disponibilidadePorNivel[nivel] = await analisarDisponibilidadeNivel(nivel, estruturasPorNivel[nivel], disponibilidadePorNivel);
        }

        // 3. IDENTIFICAR OPs VIÁVEIS BASEADO NA DISPONIBILIDADE REAL
        const opsViaveisBottomUp = identificarOPsViaveisBottomUp(disponibilidadePorNivel);

        return {
          estruturasPorNivel,
          disponibilidadePorNivel,
          opsViaveisBottomUp,
          resumo: gerarResumoBottomUp(disponibilidadePorNivel, opsViaveisBottomUp)
        };

      } catch (error) {
        console.error('❌ Erro na análise bottom-up:', error);
        throw error;
      }
    }

    // Mapear estruturas por nível hierárquico
    function mapearEstruturasPorNivel() {
      const estruturasPorNivel = {};

      // Analisar todas as estruturas para determinar níveis
      estruturas.forEach(estrutura => {
        const nivel = calcularNivelEstrutura(estrutura.produtoPaiId);

        if (!estruturasPorNivel[nivel]) {
          estruturasPorNivel[nivel] = [];
        }

        estruturasPorNivel[nivel].push({
          produtoId: estrutura.produtoPaiId,
          estrutura: estrutura,
          produto: produtos.find(p => p.id === estrutura.produtoPaiId)
        });
      });

      return estruturasPorNivel;
    }

    // Calcular nível hierárquico de um produto
    function calcularNivelEstrutura(produtoId, visitados = new Set()) {
      // Evitar loops infinitos
      if (visitados.has(produtoId)) {
        return 0;
      }
      visitados.add(produtoId);

      // Buscar estrutura do produto
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!estrutura || !estrutura.itens || estrutura.itens.length === 0) {
        // Produto sem estrutura = nível mais baixo (MP)
        return 0;
      }

      // Calcular nível máximo dos componentes + 1
      let nivelMaximo = 0;
      estrutura.itens.forEach(item => {
        const nivelComponente = calcularNivelEstrutura(item.produtoId, new Set(visitados));
        nivelMaximo = Math.max(nivelMaximo, nivelComponente);
      });

      return nivelMaximo + 1;
    }

    // Analisar disponibilidade de um nível específico
    async function analisarDisponibilidadeNivel(nivel, produtosNivel, disponibilidadeNiveisInferiores) {
      const disponibilidade = {};

      for (const item of produtosNivel) {
        const produtoId = item.produtoId;
        const estrutura = item.estrutura;

        // Calcular disponibilidade baseada em estoque + produção possível dos níveis inferiores
        const disponibilidadeTotal = calcularDisponibilidadeTotal(produtoId, estrutura, disponibilidadeNiveisInferiores);

        disponibilidade[produtoId] = {
          produto: item.produto,
          nivel: nivel,
          disponibilidadeEstoque: obterSaldoDisponivel(produtoId),
          disponibilidadeProducao: disponibilidadeTotal.producao,
          disponibilidadeTotal: disponibilidadeTotal.total,
          limitadoPor: disponibilidadeTotal.limitadoPor,
          detalhesComponentes: disponibilidadeTotal.detalhes
        };
      }

      return disponibilidade;
    }

    // Calcular disponibilidade total (estoque + produção possível)
    function calcularDisponibilidadeTotal(produtoId, estrutura, disponibilidadeNiveisInferiores) {
      // Saldo em estoque
      const saldoEstoque = obterSaldoDisponivel(produtoId);

      if (!estrutura || !estrutura.itens || estrutura.itens.length === 0) {
        // Produto sem estrutura (MP) - só tem estoque
        return {
          total: saldoEstoque,
          producao: 0,
          limitadoPor: saldoEstoque <= 0 ? 'SEM_ESTOQUE' : null,
          detalhes: []
        };
      }

      // Calcular quantas unidades podem ser produzidas baseado nos componentes
      let quantidadeMaximaProduzivel = Infinity;
      let limitadoPor = null;
      const detalhes = [];

      estrutura.itens.forEach(componente => {
        const componenteId = componente.produtoId;
        const quantidadeNecessaria = componente.quantidade || 1;

        // Disponibilidade do componente (estoque + produção de níveis inferiores)
        let disponibilidadeComponente = obterSaldoDisponivel(componenteId);

        // Se o componente tem disponibilidade de produção de níveis inferiores
        Object.values(disponibilidadeNiveisInferiores).forEach(nivelDisp => {
          if (nivelDisp[componenteId]) {
            disponibilidadeComponente += nivelDisp[componenteId].disponibilidadeTotal;
          }
        });

        // Calcular quantas unidades do produto pai podem ser feitas
        const quantidadePossivel = Math.floor(disponibilidadeComponente / quantidadeNecessaria);

        if (quantidadePossivel < quantidadeMaximaProduzivel) {
          quantidadeMaximaProduzivel = quantidadePossivel;
          limitadoPor = componenteId;
        }

        detalhes.push({
          componenteId,
          produto: produtos.find(p => p.id === componenteId),
          necessario: quantidadeNecessaria,
          disponivel: disponibilidadeComponente,
          quantidadePossivel
        });
      });

      const producaoPossivel = quantidadeMaximaProduzivel === Infinity ? 0 : quantidadeMaximaProduzivel;

      return {
        total: saldoEstoque + producaoPossivel,
        producao: producaoPossivel,
        limitadoPor,
        detalhes
      };
    }

    // Obter saldo disponível de um produto
    function obterSaldoDisponivel(produtoId) {
      const estoque = estoques.find(e => e.produtoId === produtoId);
      if (!estoque) return 0;

      const saldoTotal = estoque.saldo || 0;
      const saldoReservado = estoque.saldoReservado || 0;
      const saldoEmpenhado = estoque.saldoEmpenhado || 0;

      return Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);
    }

    // Identificar OPs viáveis baseado na análise bottom-up
    function identificarOPsViaveisBottomUp(disponibilidadePorNivel) {
      const opsViaveisBottomUp = [];

      // Filtrar OPs pendentes
      const opsPendentes = ordensProducao.filter(op => op.status === 'Pendente');

      opsPendentes.forEach(op => {
        const produto = produtos.find(p => p.id === op.produtoId);
        if (!produto) return;

        // Verificar disponibilidade do produto final
        let disponibilidadeTotal = 0;
        Object.values(disponibilidadePorNivel).forEach(nivel => {
          if (nivel[op.produtoId]) {
            disponibilidadeTotal = nivel[op.produtoId].disponibilidadeTotal;
          }
        });

        // Se não encontrou na análise por níveis, verificar estoque direto
        if (disponibilidadeTotal === 0) {
          disponibilidadeTotal = obterSaldoDisponivel(op.produtoId);
        }

        const quantidadePossivel = Math.min(disponibilidadeTotal, op.quantidade);

        let status = 'INVIAVEL';
        if (quantidadePossivel >= op.quantidade) {
          status = 'VIAVEL';
        } else if (quantidadePossivel > 0) {
          status = 'PARCIAL';
        }

        opsViaveisBottomUp.push({
          op,
          produto,
          status,
          quantidadePossivel,
          disponibilidadeTotal,
          percentualViabilidade: (quantidadePossivel / op.quantidade * 100).toFixed(1),
          origemAnalise: 'BOTTOM_UP'
        });
      });

      return opsViaveisBottomUp;
    }

    // Gerar resumo da análise bottom-up
    function gerarResumoBottomUp(disponibilidadePorNivel, opsViaveisBottomUp) {
      const totalNiveis = Object.keys(disponibilidadePorNivel).length;
      const totalProdutos = Object.values(disponibilidadePorNivel).reduce((total, nivel) =>
        total + Object.keys(nivel).length, 0);

      const viaveisBottomUp = opsViaveisBottomUp.filter(op => op.status === 'VIAVEL');
      const parciaisBottomUp = opsViaveisBottomUp.filter(op => op.status === 'PARCIAL');
      const inviaveis = opsViaveisBottomUp.filter(op => op.status === 'INVIAVEL');

      return {
        totalNiveis,
        totalProdutos,
        totalOPs: opsViaveisBottomUp.length,
        viaveisBottomUp: viaveisBottomUp.length,
        parciaisBottomUp: parciaisBottomUp.length,
        inviaveis: inviaveis.length,
        percentualViabilidade: Math.round((viaveisBottomUp.length / opsViaveisBottomUp.length) * 100)
      };
    }

    // Função principal para analisar produção viável (HÍBRIDA: TOP-DOWN + BOTTOM-UP)
    window.analisarProducaoViavel = async function() {
      try {
        console.log('🎯 Iniciando análise híbrida de produção viável...');
        mostrarNotificacao('🔍 Analisando produção viável (método inteligente)...', 'info', 2000);

        // Filtrar apenas OPs Pendentes
        const opsPendentes = ordensProducao.filter(op => op.status === 'Pendente');

        if (opsPendentes.length === 0) {
          alert('ℹ️ Nenhuma OP pendente encontrada para análise.');
          return;
        }

        console.log(`📋 Analisando ${opsPendentes.length} OP(s) pendente(s)...`);

        // 1. ANÁLISE BOTTOM-UP (NOVA ABORDAGEM)
        console.log('🔄 Executando análise bottom-up...');
        const analiseBottomUp = await analisarProducaoBottomUp();

        // 2. ANÁLISE TOP-DOWN TRADICIONAL (para comparação)
        console.log('🔄 Executando análise top-down tradicional...');
        const opsParaAnalisar = opsPendentes.slice(0, 100);
        if (opsPendentes.length > 100) {
          console.warn(`⚠️ Limitando análise a 100 OPs (total: ${opsPendentes.length})`);
          mostrarNotificacao(`⚠️ Analisando apenas as primeiras 100 OPs de ${opsPendentes.length}`, 'warning', 3000);
        }

        const analises = [];
        for (const op of opsParaAnalisar) {
          try {
            const analise = await analisarViabilidadeOP(op);
            analises.push(analise);
          } catch (error) {
            console.error(`Erro ao analisar OP ${op.numero}:`, error);
          }
        }

        // 3. COMBINAR RESULTADOS (HÍBRIDO)
        const resultadoHibrido = combinarAnalises(analises, analiseBottomUp.opsViaveisBottomUp);

        // 4. Classificar resultados finais
        const podeProuzir = resultadoHibrido.filter(a => a && a.status === 'VIAVEL');
        const producaoParcial = resultadoHibrido.filter(a => a && a.status === 'PARCIAL');
        const naoPodeProuzir = resultadoHibrido.filter(a => a && a.status === 'INVIAVEL');

        console.log(`📊 Resultados híbridos: ${podeProuzir.length} viáveis, ${producaoParcial.length} parciais, ${naoPodeProuzir.length} inviáveis`);

        // 5. Gerar relatório com informações bottom-up
        const relatorio = gerarRelatorioProducaoViavel(podeProuzir, producaoParcial, naoPodeProuzir);
        relatorio.analiseBottomUp = analiseBottomUp;
        relatorio.metodo = 'HIBRIDO';

        // Mostrar relatório
        mostrarRelatorioProducaoViavel(relatorio);

        console.log('✅ Análise híbrida de produção viável concluída');
        mostrarNotificacao('✅ Análise híbrida concluída!', 'success', 2000);

      } catch (error) {
        console.error('❌ Erro na análise de produção viável:', error);
        mostrarNotificacao('❌ Erro na análise de produção', 'error', 5000);
        alert('❌ Erro na análise de produção: ' + error.message);
      }
    };

    // Combinar análises top-down e bottom-up
    function combinarAnalises(analiseTopDown, analiseBottomUp) {
      const resultadoCombinado = [];

      // Criar mapa da análise bottom-up para consulta rápida
      const mapBottomUp = {};
      analiseBottomUp.forEach(item => {
        mapBottomUp[item.op.id] = item;
      });

      // Combinar resultados
      analiseTopDown.forEach(analiseTop => {
        const analiseBottom = mapBottomUp[analiseTop.op.id];

        if (analiseBottom) {
          // Usar o resultado mais otimista (bottom-up geralmente é mais preciso)
          const statusFinal = escolherMelhorStatus(analiseTop.status, analiseBottom.status);
          const quantidadeFinal = Math.max(analiseTop.quantidadePossivel || 0, analiseBottom.quantidadePossivel || 0);

          resultadoCombinado.push({
            ...analiseTop,
            status: statusFinal,
            quantidadePossivel: quantidadeFinal,
            analiseBottomUp: analiseBottom,
            metodoAnalise: 'HIBRIDO',
            observacoes: [
              ...analiseTop.observacoes || [],
              `Análise bottom-up: ${analiseBottom.percentualViabilidade}% viável`
            ]
          });
        } else {
          // Só análise top-down disponível
          resultadoCombinado.push({
            ...analiseTop,
            metodoAnalise: 'TOP_DOWN'
          });
        }
      });

      return resultadoCombinado;
    }

    // Escolher melhor status entre duas análises
    function escolherMelhorStatus(statusTop, statusBottom) {
      const prioridade = { 'VIAVEL': 3, 'PARCIAL': 2, 'INVIAVEL': 1 };

      const prioTop = prioridade[statusTop] || 1;
      const prioBottom = prioridade[statusBottom] || 1;

      return prioTop >= prioBottom ? statusTop : statusBottom;
    }

    // Analisar viabilidade de uma OP específica
    async function analisarViabilidadeOP(op) {
      try {
        const produto = produtos.find(p => p.id === op.produtoId) || {};
        const analise = {
          op: op,
          produto: produto,
          status: 'VIAVEL', // VIAVEL | PARCIAL | INVIAVEL
          quantidadePossivel: op.quantidade,
          materiaisProblema: [],
          prioridade: op.prioridade || 'NORMAL',
          dataEntrega: op.dataEntrega,
          observacoes: []
        };

        // Verificar cada material necessário
        if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
          let menorQuantidadePossivel = op.quantidade;

          for (const material of op.materiaisNecessarios) {
            // Verificações de segurança
            if (!material || !material.produtoId || !material.quantidade) {
              console.warn(`Material inválido na OP ${op.numero}:`, material);
              continue;
            }
            // Buscar estoque do material
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || op.armazemProducaoId)
            );

            if (!estoque) {
              analise.status = 'INVIAVEL';
              analise.quantidadePossivel = 0;
              analise.materiaisProblema.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: 0,
                problema: 'Estoque não encontrado'
              });
              continue;
            }

            // Calcular saldo disponível
            const saldoTotal = estoque.saldo || 0;
            const saldoReservado = estoque.saldoReservado || 0;
            const saldoEmpenhado = estoque.saldoEmpenhado || 0;
            const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;

            // Verificar disponibilidade
            if (saldoDisponivel < material.quantidade) {
              // Calcular quantas unidades do produto podem ser feitas com o material disponível
              const quantidadePossivelComEsteMaterial = Math.floor(saldoDisponivel / (material.quantidade / op.quantidade));

              if (quantidadePossivelComEsteMaterial === 0) {
                analise.status = 'INVIAVEL';
                analise.quantidadePossivel = 0;
              } else {
                analise.status = 'PARCIAL';
                menorQuantidadePossivel = Math.min(menorQuantidadePossivel, quantidadePossivelComEsteMaterial);
              }

              analise.materiaisProblema.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: saldoDisponivel,
                falta: material.quantidade - saldoDisponivel,
                problema: saldoDisponivel <= 0 ? 'Sem estoque' : 'Estoque insuficiente'
              });
            }

            // Alertas de estoque baixo
            const margemSeguranca = material.quantidade * 0.1; // 10% de margem
            if (saldoDisponivel >= material.quantidade && saldoDisponivel < (material.quantidade + margemSeguranca)) {
              analise.observacoes.push(`Material ${material.codigo || material.produtoId}: estoque baixo (margem < 10%)`);
            }
          }

          if (analise.status === 'PARCIAL') {
            analise.quantidadePossivel = menorQuantidadePossivel;
          }
        }

        return analise;

      } catch (error) {
        console.error(`Erro ao analisar OP ${op.id}:`, error);
        return {
          op: op,
          produto: produtos.find(p => p.id === op.produtoId) || {},
          status: 'ERRO',
          quantidadePossivel: 0,
          materiaisProblema: [],
          erro: error.message
        };
      }
    }

    // Gerar relatório de produção viável
    function gerarRelatorioProducaoViavel(viaveis, parciais, inviaveis) {
      const agora = new Date();
      const relatorio = {
        dataHora: agora.toLocaleString(),
        resumo: {
          totalOPs: viaveis.length + parciais.length + inviaveis.length,
          viaveis: viaveis.length,
          parciais: parciais.length,
          inviaveis: inviaveis.length,
          percentualViabilidade: Math.round((viaveis.length / (viaveis.length + parciais.length + inviaveis.length)) * 100)
        },
        viaveis: viaveis.sort((a, b) => {
          // Ordenar por prioridade e data de entrega
          const prioridadeOrder = { 'ALTA': 1, 'MÉDIA': 2, 'NORMAL': 3, 'BAIXA': 4 };
          const prioA = prioridadeOrder[a.prioridade] || 3;
          const prioB = prioridadeOrder[b.prioridade] || 3;

          if (prioA !== prioB) return prioA - prioB;

          // Se mesma prioridade, ordenar por data de entrega
          if (a.dataEntrega && b.dataEntrega) {
            return new Date(a.dataEntrega.seconds * 1000) - new Date(b.dataEntrega.seconds * 1000);
          }

          return 0;
        }),
        parciais: parciais.sort((a, b) => b.quantidadePossivel - a.quantidadePossivel),
        inviaveis: inviaveis,
        sugestoes: gerarSugestoesSequenciamento(viaveis, parciais, inviaveis)
      };

      return relatorio;
    }

    // Gerar sugestões de sequenciamento
    function gerarSugestoesSequenciamento(viaveis, parciais, inviaveis) {
      const sugestoes = [];

      // Verificações de segurança
      if (!viaveis) viaveis = [];
      if (!parciais) parciais = [];
      if (!inviaveis) inviaveis = [];

      // Sugestão 1: Priorizar por urgência e viabilidade
      if (viaveis.length > 0) {
        const maisUrgente = viaveis[0]; // Já ordenado por prioridade e data
        sugestoes.push({
          tipo: 'PRIORIDADE',
          titulo: 'Iniciar pela mais urgente',
          descricao: `Começar pela OP ${maisUrgente.op.numero} (${maisUrgente.produto.codigo}) - Prioridade ${maisUrgente.prioridade}`,
          acao: `iniciarProducao('${maisUrgente.op.id}')`
        });
      }

      // Sugestão 2: Agrupar por família de produtos
      const familias = {};
      viaveis.forEach(analise => {
        const familia = analise.produto.familia || 'GERAL';
        if (!familias[familia]) familias[familia] = [];
        familias[familia].push(analise);
      });

      Object.keys(familias).forEach(familia => {
        if (familias[familia].length > 1) {
          sugestoes.push({
            tipo: 'AGRUPAMENTO',
            titulo: `Agrupar família ${familia}`,
            descricao: `Produzir ${familias[familia].length} OPs da família ${familia} em sequência (reduz setup)`,
            ops: familias[familia].map(a => a.op.numero).join(', ')
          });
        }
      });

      // Sugestão 3: Produção parcial para liberar material
      if (parciais.length > 0) {
        const melhorParcial = parciais[0]; // Maior quantidade possível
        sugestoes.push({
          tipo: 'PARCIAL',
          titulo: 'Produção parcial estratégica',
          descricao: `Produzir ${melhorParcial.quantidadePossivel} de ${melhorParcial.op.quantidade} da OP ${melhorParcial.op.numero} para liberar material`,
          acao: `ajustarQuantidadeOP('${melhorParcial.op.id}', ${melhorParcial.quantidadePossivel})`
        });
      }

      // Sugestão 4: Compras urgentes
      const materiaisCriticos = {};
      [...parciais, ...inviaveis].forEach(analise => {
        analise.materiaisProblema.forEach(material => {
          if (!materiaisCriticos[material.codigo]) {
            materiaisCriticos[material.codigo] = {
              codigo: material.codigo,
              faltaTotal: 0,
              opsAfetadas: 0
            };
          }
          materiaisCriticos[material.codigo].faltaTotal += material.falta || 0;
          materiaisCriticos[material.codigo].opsAfetadas++;
        });
      });

      const materiaisOrdenados = Object.values(materiaisCriticos)
        .sort((a, b) => b.opsAfetadas - a.opsAfetadas)
        .slice(0, 3);

      if (materiaisOrdenados.length > 0) {
        sugestoes.push({
          tipo: 'COMPRA',
          titulo: 'Compras prioritárias',
          descricao: `Solicitar urgente: ${materiaisOrdenados.map(m => `${m.codigo} (${m.opsAfetadas} OPs afetadas)`).join(', ')}`,
          materiais: materiaisOrdenados
        });
      }

      return sugestoes;
    }

    // Mostrar relatório de produção viável
    function mostrarRelatorioProducaoViavel(relatorio) {
      // Salvar relatório globalmente para outras funções
      window.relatorioAtual = relatorio;

      // Gerar HTML visual para o modal
      let html = `
        <div style="padding: 20px;">
          <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-chart-line"></i> Resumo Executivo
              ${relatorio.metodo ? `<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 10px;">ANÁLISE ${relatorio.metodo}</span>` : ''}
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;">${relatorio.resumo.totalOPs}</div>
                <div style="font-size: 12px;">OPs Analisadas</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #90EE90;">${relatorio.resumo.viaveis}</div>
                <div style="font-size: 12px;">Viáveis (${relatorio.resumo.percentualViabilidade}%)</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFD700;">${relatorio.resumo.parciais}</div>
                <div style="font-size: 12px;">Parciais</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFB6C1;">${relatorio.resumo.inviaveis}</div>
                <div style="font-size: 12px;">Inviáveis</div>
              </div>
            </div>

            ${relatorio.analiseBottomUp ? `
              <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                <h6 style="margin: 0 0 8px 0; display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-layer-group"></i> Análise Hierárquica (Bottom-Up)
                </h6>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; font-size: 12px;">
                  <div>📊 <strong>${relatorio.analiseBottomUp.resumo.totalNiveis}</strong> níveis estruturais</div>
                  <div>🏗️ <strong>${relatorio.analiseBottomUp.resumo.totalProdutos}</strong> produtos mapeados</div>
                  <div>🎯 <strong>${relatorio.analiseBottomUp.resumo.percentualViabilidade}%</strong> viabilidade bottom-up</div>
                </div>
              </div>
            ` : ''}
          </div>
      `;

      // OPs Viáveis
      if (relatorio.viaveis.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #28a745; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-check-circle"></i> Pode Produzir Agora (${relatorio.viaveis.length} OPs)
            </h4>
            <div style="background: #f8f9fa; border-left: 4px solid #28a745; padding: 15px; border-radius: 5px;">
              <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <thead>
                  <tr style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #1e7e34;">OP</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #1e7e34;">Produto</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Quantidade</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Prioridade</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Entrega</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Ação</th>
                  </tr>
                </thead>
                <tbody>
        `;

        relatorio.viaveis.forEach((analise, index) => {
          const dataEntrega = analise.dataEntrega ? new Date(analise.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem prazo';
          const prioridadeCor = {
            'ALTA': '#dc3545',
            'MÉDIA': '#ffc107',
            'NORMAL': '#6c757d',
            'BAIXA': '#28a745'
          };

          const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

          html += `
            <tr style="background: ${rowColor}; border-bottom: 1px solid #dee2e6;">
              <td style="padding: 12px; font-weight: bold; color: #495057;">${analise.op.numero}</td>
              <td style="padding: 12px;">
                <div style="font-weight: bold; color: #495057;">${analise.produto.codigo || 'N/A'}</div>
                <div style="font-size: 12px; color: #6c757d;">${analise.produto.descricao || 'Sem descrição'}</div>
              </td>
              <td style="padding: 12px; text-align: center;">
                <span style="font-weight: bold;">${analise.op.quantidade}</span>
                <br><small style="color: #6c757d;">${analise.produto.unidade || 'UN'}</small>
              </td>
              <td style="padding: 12px; text-align: center;">
                <span style="background: ${prioridadeCor[analise.prioridade] || '#6c757d'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                  ${analise.prioridade}
                </span>
              </td>
              <td style="padding: 12px; text-align: center; font-size: 12px;">${dataEntrega}</td>
              <td style="padding: 12px; text-align: center;">
                <button onclick="iniciarProducao('${analise.op.id}')" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 12px; font-weight: bold; transition: all 0.3s;">
                  🚀 Iniciar
                </button>
              </td>
            </tr>
          `;

          // Adicionar observações e informações de análise
          const temObservacoes = analise.observacoes && analise.observacoes.length > 0;
          const temAnaliseBottomUp = analise.analiseBottomUp;
          const metodoAnalise = analise.metodoAnalise;

          if (temObservacoes || temAnaliseBottomUp || metodoAnalise) {
            html += `
              <tr style="background: #f8f9fa; border-left: 4px solid #17a2b8;">
                <td colspan="6" style="padding: 8px 12px; font-size: 11px;">
            `;

            if (metodoAnalise) {
              const iconeMetodo = metodoAnalise === 'HIBRIDO' ? 'fas fa-exchange-alt' :
                                 metodoAnalise === 'BOTTOM_UP' ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
              html += `
                <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 8px; margin-right: 8px;">
                  <i class="${iconeMetodo}"></i> ${metodoAnalise}
                </span>
              `;
            }

            if (temAnaliseBottomUp) {
              html += `
                <span style="color: #17a2b8; margin-right: 8px;">
                  <i class="fas fa-layer-group"></i> Bottom-up: ${analise.analiseBottomUp.percentualViabilidade}% viável
                </span>
              `;
            }

            if (temObservacoes) {
              html += `
                <span style="color: #856404;">
                  <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                  <strong>Alertas:</strong> ${analise.observacoes.join(', ')}
                </span>
              `;
            }

            html += `
                </td>
              </tr>
            `;
          }
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // OPs Parciais
      if (relatorio.parciais.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #ffc107; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-triangle"></i> Produção Parcial Possível (${relatorio.parciais.length} OPs)
            </h4>
            <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; border-radius: 5px;">
        `;

        relatorio.parciais.forEach((analise, index) => {
          const percentualPossivel = ((analise.quantidadePossivel / analise.op.quantidade) * 100).toFixed(1);

          html += `
            <div style="margin-bottom: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="background: linear-gradient(135deg, #ffc107, #e0a800); color: white; padding: 12px;">
                <h5 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                  <span>OP ${analise.op.numero} - ${analise.produto.codigo}</span>
                  <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                    ${percentualPossivel}% Viável
                  </span>
                </h5>
                <div style="font-size: 12px; margin-top: 5px;">
                  ${analise.produto.descricao || 'Sem descrição'} |
                  Pode produzir: <strong>${analise.quantidadePossivel}/${analise.op.quantidade} ${analise.produto.unidade || 'UN'}</strong>
                </div>
              </div>

              <div style="padding: 15px;">
                <h6 style="color: #dc3545; margin: 0 0 10px 0; display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-exclamation-circle"></i> Materiais em Falta
                </h6>
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Código</th>
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Descrição</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Necessário</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Disponível</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Falta</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Status</th>
                    </tr>
                  </thead>
                  <tbody>
          `;

          analise.materiaisProblema.forEach((material, matIndex) => {
            const produtoMaterial = produtos.find(p => p.id === material.produtoId) || {};
            const rowColor = matIndex % 2 === 0 ? '#ffffff' : '#f8f9fa';
            const statusColor = material.disponivel <= 0 ? '#dc3545' : '#ffc107';

            // Criar código mais legível
            const codigoLegivel = gerarCodigoLegivel(material.codigo, produtoMaterial);

            html += `
              <tr style="background: ${rowColor}; border-bottom: 1px solid #dee2e6;">
                <td style="padding: 8px; font-size: 12px;">
                  <div style="font-weight: bold; color: #007bff; cursor: pointer;"
                       onclick="mostrarDetalhesCodigoCriptico('${material.codigo}', '${material.produtoId}')"
                       title="Clique para ver detalhes completos">
                    ${codigoLegivel}
                  </div>
                  <div style="font-size: 10px; color: #6c757d; font-family: monospace; margin-top: 2px;">
                    ${material.codigo.length > 15 ? material.codigo.substring(0, 15) + '...' : material.codigo}
                  </div>
                  <button onclick="abrirDecodificadorComCodigo('${material.codigo}')"
                          style="background: #fd7e14; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 9px; cursor: pointer; margin-top: 2px;">
                    🔍 Decodificar
                  </button>
                </td>
                <td style="padding: 8px; font-size: 12px;">
                  <div style="font-weight: bold;">${produtoMaterial.descricao || 'Sem descrição'}</div>
                  ${produtoMaterial.familia ? `<div style="font-size: 10px; color: #6c757d;">Família: ${produtoMaterial.familia}</div>` : ''}
                  ${produtoMaterial.tipo ? `<div style="font-size: 10px; color: #6c757d;">Tipo: ${produtoMaterial.tipo}</div>` : ''}
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold;">${material.necessario?.toFixed(3) || 'N/A'}</span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: ${material.disponivel > 0 ? '#28a745' : '#dc3545'};">
                    ${material.disponivel?.toFixed(3) || '0.000'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                  ${material.quantidadeReservadaParaEstaOP > 0 ? `
                    <br><span style="background: #17a2b8; color: white; padding: 1px 4px; border-radius: 6px; font-size: 9px;">
                      📌 ${material.quantidadeReservadaParaEstaOP.toFixed(3)} reservado
                    </span>
                  ` : ''}
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: #dc3545;">
                    ${material.falta?.toFixed(3) || 'N/A'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center;">
                  <span style="background: ${statusColor}; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px; font-weight: bold;">
                    ${material.problema}
                  </span>
                  ${material.usandoReserva ? `
                    <br><small style="color: #17a2b8; font-weight: bold; font-size: 9px;">
                      ✅ Usando reserva
                    </small>
                  ` : ''}
                  <br><button onclick="corrigirEstoqueDireto('${material.produtoId}')"
                             style="background: #28a745; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 9px; cursor: pointer; margin-top: 2px;">
                    🔧 Corrigir
                  </button>
                </td>
              </tr>
            `;
          });

          html += `
                  </tbody>
                </table>
              </div>
            </div>
          `;
        });

        html += `</div></div>`;
      }

      // OPs Inviáveis
      if (relatorio.inviaveis.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #dc3545; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-times-circle"></i> Não Pode Produzir (${relatorio.inviaveis.length} OPs)
            </h4>
            <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; border-radius: 5px;">
        `;

        relatorio.inviaveis.forEach((analise, index) => {
          html += `
            <div style="margin-bottom: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 12px;">
                <h5 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                  <span>OP ${analise.op.numero} - ${analise.produto.codigo}</span>
                  <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                    <i class="fas fa-ban"></i> Bloqueada
                  </span>
                </h5>
                <div style="font-size: 12px; margin-top: 5px;">
                  ${analise.produto.descricao || 'Sem descrição'} |
                  Quantidade: <strong>${analise.op.quantidade} ${analise.produto.unidade || 'UN'}</strong>
                </div>
              </div>

              <div style="padding: 15px;">
                <h6 style="color: #dc3545; margin: 0 0 10px 0; display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-exclamation-circle"></i> Materiais Críticos em Falta
                </h6>
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Código</th>
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Descrição</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Necessário</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Disponível</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Falta</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Criticidade</th>
                    </tr>
                  </thead>
                  <tbody>
          `;

          analise.materiaisProblema.forEach((material, matIndex) => {
            const produtoMaterial = produtos.find(p => p.id === material.produtoId) || {};
            const rowColor = matIndex % 2 === 0 ? '#ffffff' : '#f8f9fa';

            // Calcular criticidade baseada na falta
            let criticidade = 'BAIXA';
            let criticidadeCor = '#ffc107';
            if (material.disponivel <= 0) {
              criticidade = 'CRÍTICA';
              criticidadeCor = '#dc3545';
            } else if (material.falta > (material.necessario * 0.5)) {
              criticidade = 'ALTA';
              criticidadeCor = '#fd7e14';
            }

            html += `
              <tr style="background: ${rowColor}; border-bottom: 1px solid #dee2e6;">
                <td style="padding: 8px; font-weight: bold; font-size: 12px;">${material.codigo}</td>
                <td style="padding: 8px; font-size: 12px;">${produtoMaterial.descricao || 'Sem descrição'}</td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold;">${material.necessario?.toFixed(3) || 'N/A'}</span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: ${material.disponivel > 0 ? '#28a745' : '#dc3545'};">
                    ${material.disponivel?.toFixed(3) || '0.000'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: #dc3545; font-size: 14px;">
                    ${material.falta?.toFixed(3) || 'N/A'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center;">
                  <span style="background: ${criticidadeCor}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 10px; font-weight: bold;">
                    ${criticidade}
                  </span>
                  <br><small style="color: #6c757d; font-size: 10px;">${material.problema}</small>
                </td>
              </tr>
            `;
          });

          html += `
                  </tbody>
                </table>

                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                  <h6 style="margin: 0 0 5px 0; color: #856404; display: flex; align-items: center; gap: 5px;">
                    <i class="fas fa-lightbulb"></i> Ações Recomendadas
                  </h6>
                  <ul style="margin: 5px 0; padding-left: 20px; font-size: 12px; color: #856404;">
                    <li>Solicitar compra urgente dos materiais críticos</li>
                    <li>Verificar transferências entre armazéns</li>
                    <li>Considerar fornecedores alternativos</li>
                    <li>Revisar prioridade da OP conforme necessidade</li>
                  </ul>
                </div>
              </div>
            </div>
          `;
        });

        html += `</div></div>`;
      }

      // Sugestões
      if (relatorio.sugestoes.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #6f42c1; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-lightbulb"></i> Sugestões Inteligentes de PCP
            </h4>
            <div style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-left: 4px solid #6f42c1; padding: 15px; border-radius: 5px;">
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
        `;

        relatorio.sugestoes.forEach((sugestao, index) => {
          const icones = {
            'PRIORIDADE': 'fas fa-flag',
            'AGRUPAMENTO': 'fas fa-layer-group',
            'PARCIAL': 'fas fa-cut',
            'COMPRA': 'fas fa-shopping-cart'
          };

          const cores = {
            'PRIORIDADE': '#dc3545',
            'AGRUPAMENTO': '#28a745',
            'PARCIAL': '#ffc107',
            'COMPRA': '#17a2b8'
          };

          const icone = icones[sugestao.tipo] || 'fas fa-lightbulb';
          const cor = cores[sugestao.tipo] || '#6f42c1';

          html += `
            <div style="background: white; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-top: 4px solid ${cor};">
              <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <div style="background: ${cor}; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                  <i class="${icone}"></i>
                </div>
                <div>
                  <h6 style="margin: 0; color: ${cor}; font-weight: bold;">${sugestao.titulo}</h6>
                  <small style="color: #6c757d; text-transform: uppercase; font-weight: bold;">${sugestao.tipo}</small>
                </div>
              </div>
              <p style="margin: 0; font-size: 13px; color: #495057; line-height: 1.4;">
                ${sugestao.descricao}
              </p>
          `;

          // Adicionar informações específicas por tipo
          if (sugestao.tipo === 'COMPRA' && sugestao.materiais) {
            html += `
              <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <small style="font-weight: bold; color: #495057;">Materiais prioritários:</small>
                <ul style="margin: 5px 0; padding-left: 15px; font-size: 12px;">
            `;
            sugestao.materiais.forEach(material => {
              html += `<li>${material.codigo} - ${material.opsAfetadas} OP(s) afetada(s)</li>`;
            });
            html += `</ul></div>`;
          }

          if (sugestao.tipo === 'AGRUPAMENTO' && sugestao.ops) {
            html += `
              <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <small style="font-weight: bold; color: #495057;">OPs envolvidas:</small>
                <div style="font-size: 12px; margin-top: 3px;">${sugestao.ops}</div>
              </div>
            `;
          }

          html += `</div>`;
        });

        html += `
              </div>
            </div>
          </div>
        `;
      }

      html += `</div>`;

      // Mostrar no modal
      document.getElementById('conteudoAnaliseProducao').innerHTML = html;
      document.getElementById('modalAnaliseProducao').style.display = 'block';

      // Salvar relatório no console para análise detalhada
      console.log('📊 Relatório detalhado:', relatorio);
    }

    // Funções do modal
    window.fecharModalAnalise = function() {
      document.getElementById('modalAnaliseProducao').style.display = 'none';
    };

    window.exportarRelatorio = function() {
      if (!window.relatorioAtual) return;

      // Gerar texto para exportação
      let texto = `ANÁLISE DE PRODUÇÃO VIÁVEL - ${window.relatorioAtual.dataHora}\n\n`;
      texto += `RESUMO: ${window.relatorioAtual.resumo.viaveis} viáveis, ${window.relatorioAtual.resumo.parciais} parciais, ${window.relatorioAtual.resumo.inviaveis} inviáveis\n\n`;

      // Criar e baixar arquivo
      const blob = new Blob([texto], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analise-producao-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      window.URL.revokeObjectURL(url);

      mostrarNotificacao('📊 Relatório exportado!', 'success', 2000);
    };

    window.iniciarProducaoLote = async function() {
      if (!window.relatorioAtual || window.relatorioAtual.viaveis.length === 0) {
        alert('Nenhuma OP viável para iniciar em lote.');
        return;
      }

      if (!confirm(`🚀 Iniciar produção de ${window.relatorioAtual.viaveis.length} OP(s) viáveis?\n\nIsso irá transferir todas as reservas para empenhos.`)) {
        return;
      }

      let sucessos = 0;
      let erros = 0;

      for (const analise of window.relatorioAtual.viaveis) {
        try {
          await EmpenhoService.transferirReservasParaEmpenhos(analise.op.id);
          sucessos++;
        } catch (error) {
          console.error(`Erro ao iniciar OP ${analise.op.numero}:`, error);
          erros++;
        }
      }

      mostrarNotificacao(`✅ Lote iniciado: ${sucessos} sucessos, ${erros} erros`, sucessos > 0 ? 'success' : 'error', 5000);
      fecharModalAnalise();
      debounceUpdate('iniciarLote');
    };

    // ===================================================================
    // FUNÇÕES DE EMPENHOS
    // ===================================================================

    // Função para iniciar produção (transferir reservas para empenhos)
    window.iniciarProducao = async function(opId) {
      try {
        console.log(`🔍 Validando estoque para OP: ${opId}`);

        // 1. VALIDAR ESTOQUE ANTES DE INICIAR
        const validacao = await validarEstoqueParaProducao(opId);
        const ordem = ordensProducao.find(op => op.id === opId);
        const nomeOP = ordem ? (ordem.numero || opId) : opId;

        if (!validacao.podeProuzir) {
          // Mostrar detalhes dos materiais em falta
          const detalhes = mostrarDetalhesValidacao(validacao, nomeOP);
          alert(detalhes);

          mostrarNotificacao('❌ Não é possível iniciar produção - Materiais insuficientes', 'error', 8000);
          return;
        }

        // 2. MOSTRAR ALERTAS SE HOUVER
        if (validacao.alertas.length > 0) {
          const detalhes = mostrarDetalhesValidacao(validacao, nomeOP);
          if (!confirm(`${detalhes}\n⚠️ Há alertas de estoque baixo. Deseja continuar mesmo assim?`)) {
            return;
          }
        }

        // 3. CONFIRMAR INÍCIO DA PRODUÇÃO
        const confirmacao = validacao.alertas.length > 0
          ? '🚀 Iniciar produção desta OP?\n\n⚠️ Atenção aos alertas de estoque baixo mostrados acima.'
          : '🚀 Iniciar produção desta OP?\n\n✅ Todos os materiais estão disponíveis.\nIsso irá transferir as reservas para empenhos.';

        if (!confirm(confirmacao)) {
          return;
        }

        console.log(`🚀 Iniciando produção da OP: ${opId}`);

        // 4. TRANSFERIR RESERVAS PARA EMPENHOS
        const resultado = await EmpenhoService.transferirReservasParaEmpenhos(opId);

        if (resultado.erros.length > 0) {
          console.warn('⚠️ Erros durante transferência:', resultado.erros);
          mostrarNotificacao(`⚠️ Produção iniciada com ${resultado.erros.length} erro(s)`, 'warning', 5000);
          alert(`⚠️ Produção iniciada com ${resultado.erros.length} erro(s):\n${resultado.erros.join('\n')}`);
        } else {
          mostrarNotificacao(`✅ Produção iniciada! ${resultado.transferencias} material(is) empenhado(s)`, 'success', 3000);
          alert(`✅ Produção iniciada com sucesso!\n${resultado.transferencias} material(is) empenhado(s).`);
        }

        // 5. ATUALIZAR INTERFACE (não precisa reconfigurar listeners)
        debounceUpdate('iniciarProducao');
      } catch (error) {
        console.error("❌ Erro ao iniciar produção:", error);
        mostrarNotificacao('❌ Erro ao iniciar produção', 'error', 5000);
        alert("❌ Erro ao iniciar produção: " + error.message);
      }
    };

    // Função para consultar empenhos de uma OP
    window.consultarEmpenhosOP = async function(opId) {
      try {
        const empenhosOP = await EmpenhoService.consultarEmpenhosOP(opId);

        if (empenhosOP.length === 0) {
          alert(`ℹ️ Nenhum empenho encontrado para a OP: ${opId}`);
          return;
        }

        let detalhes = `📋 EMPENHOS DA OP: ${opId}\n\n`;

        empenhosOP.forEach(empenho => {
          const produto = produtos.find(p => p.id === empenho.produtoId);
          const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

          detalhes += `🔹 Produto: ${produto ? produto.codigo + ' - ' + produto.descricao : empenho.produtoId}\n`;
          detalhes += `   Status: ${empenho.status}\n`;
          detalhes += `   Empenhado: ${empenho.quantidadeEmpenhada.toFixed(3)}\n`;
          detalhes += `   Consumido: ${empenho.quantidadeConsumida.toFixed(3)}\n`;
          detalhes += `   Restante: ${quantidadeRestante.toFixed(3)}\n`;
          detalhes += `   Data: ${empenho.dataEmpenho ? new Date(empenho.dataEmpenho.seconds * 1000).toLocaleDateString() : 'N/A'}\n\n`;
        });

        alert(detalhes);
      } catch (error) {
        console.error('❌ Erro ao consultar empenhos:', error);
        mostrarNotificacao('❌ Erro ao consultar empenhos', 'error', 5000);
        alert('❌ Erro ao consultar empenhos: ' + error.message);
      }
    };

    // Função para abrir painel de empenhos
    window.abrirPainelEmpenhos = function() {
      window.open('painel_empenhos.html', '_blank');
    };

    // Adicionar botões no header
    document.addEventListener('DOMContentLoaded', function() {
      const header = document.querySelector('.header');
      if (header) {
        // Botão de análise de produção viável
        const analisarBtn = document.createElement('button');
        analisarBtn.innerHTML = '<i class="fas fa-search-plus"></i> Analisar Produção Viável';
        analisarBtn.onclick = analisarProducaoViavel;
        analisarBtn.title = 'Analisar quais OPs podem ser produzidas com o estoque atual';
        analisarBtn.style.cssText = `
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          margin-left: 10px;
          font-weight: 600;
        `;
        header.appendChild(analisarBtn);

        // Botão de atualização manual
        const atualizarBtn = document.createElement('button');
        atualizarBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Atualizar';
        atualizarBtn.onclick = forcarAtualizacao;
        atualizarBtn.title = 'Forçar atualização dos dados (F5 inteligente)';
        atualizarBtn.style.cssText = `
          background: linear-gradient(135deg, #17a2b8, #138496);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          margin-left: 10px;
        `;
        header.appendChild(atualizarBtn);

        // Botão decodificador de produtos
        const decodificadorBtn = document.createElement('button');
        decodificadorBtn.innerHTML = '<i class="fas fa-code"></i> Decodificar Produtos';
        decodificadorBtn.onclick = abrirDecodificadorProdutos;
        decodificadorBtn.title = 'Identificar produtos por códigos crípticos e corrigir estoque';
        decodificadorBtn.style.cssText = `
          background: linear-gradient(135deg, #fd7e14, #e55a00);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          margin-left: 10px;
          font-weight: 600;
        `;
        header.appendChild(decodificadorBtn);

        // Botão de diagnóstico de impossibilidade
        const diagnosticoBtn = document.createElement('button');
        diagnosticoBtn.innerHTML = '<i class="fas fa-search"></i> Diagnóstico Completo';
        diagnosticoBtn.onclick = executarDiagnosticoCompleto;
        diagnosticoBtn.title = 'Verificar se REALMENTE não dá para produzir nada';
        diagnosticoBtn.style.cssText = `
          background: linear-gradient(135deg, #dc3545, #c82333);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          margin-left: 10px;
          font-weight: 600;
        `;
        header.appendChild(diagnosticoBtn);

        // Botão do painel de empenhos
        const painelBtn = document.createElement('button');
        painelBtn.innerHTML = '<i class="fas fa-bolt"></i> Painel Empenhos';
        painelBtn.onclick = abrirPainelEmpenhos;
        painelBtn.style.cssText = `
          background: linear-gradient(135deg, #6f42c1, #5a32a3);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          margin-left: 10px;
        `;
        header.appendChild(painelBtn);

        // Indicador de status da conexão
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'connectionStatus';
        statusIndicator.innerHTML = '<i class="fas fa-circle" style="color: #28a745;"></i> Online';
        statusIndicator.style.cssText = `
          display: inline-flex;
          align-items: center;
          gap: 5px;
          margin-left: 15px;
          font-size: 12px;
          color: #666;
        `;
        header.appendChild(statusIndicator);
      }

      // Atalho de teclado para atualização (Ctrl+R ou F5)
      document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
          e.preventDefault();
          forcarAtualizacao();
        }
      });
    });
  </script>
</body>
</html>