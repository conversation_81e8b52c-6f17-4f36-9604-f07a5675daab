// Serviço centralizado para transferências entre armazéns
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  runTransaction, 
  addDoc, 
  updateDoc, 
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class TransferService {
  
  /**
   * Executa transferência entre armazéns com validações
   */
  static async executeTransfer(transferData) {
    try {
      const result = await runTransaction(db, async (transaction) => {
        // 1. Validar dados
        this.validateTransferData(transferData);
        
        // 2. Verificar saldo origem
        const estoqueOrigemQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", transferData.produtoId),
          where("armazemId", "==", transferData.armazemOrigemId)
        );
        const estoqueOrigemSnap = await getDocs(estoqueOrigemQuery);
        
        if (estoqueOrigemSnap.empty) {
          throw new Error('Produto não encontrado no armazém de origem');
        }
        
        const estoqueOrigem = estoqueOrigemSnap.docs[0];
        const dadosOrigem = estoqueOrigem.data();
        const saldoDisponivel = dadosOrigem.saldo - (dadosOrigem.saldoReservado || 0);
        
        if (saldoDisponivel < transferData.quantidade) {
          throw new Error(`Saldo insuficiente. Disponível: ${saldoDisponivel}, Solicitado: ${transferData.quantidade}`);
        }
        
        // 3. Buscar ou criar estoque destino
        const estoqueDestinoQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", transferData.produtoId),
          where("armazemId", "==", transferData.armazemDestinoId)
        );
        const estoqueDestinoSnap = await getDocs(estoqueDestinoQuery);
        
        let estoqueDestino;
        let dadosDestino;
        
        if (estoqueDestinoSnap.empty) {
          // Criar novo estoque no destino
          estoqueDestino = doc(collection(db, "estoques"));
          dadosDestino = {
            produtoId: transferData.produtoId,
            armazemId: transferData.armazemDestinoId,
            saldo: 0,
            saldoReservado: 0,
            dataCriacao: Timestamp.now()
          };
          transaction.set(estoqueDestino, dadosDestino);
        } else {
          estoqueDestino = estoqueDestinoSnap.docs[0].ref;
          dadosDestino = estoqueDestinoSnap.docs[0].data();
        }
        
        // 4. Gerar número da transferência
        const numeroTransferencia = await this.generateTransferNumber();
        
        // 5. Atualizar estoques
        transaction.update(estoqueOrigem.ref, {
          saldo: dadosOrigem.saldo - transferData.quantidade,
          ultimaMovimentacao: Timestamp.now()
        });
        
        transaction.update(estoqueDestino, {
          saldo: dadosDestino.saldo + transferData.quantidade,
          ultimaMovimentacao: Timestamp.now()
        });
        
        // 6. Registrar transferência
        const transferenciaRef = doc(collection(db, "transferenciasArmazem"));
        const transferenciaData = {
          numero: numeroTransferencia,
          produtoId: transferData.produtoId,
          armazemOrigemId: transferData.armazemOrigemId,
          armazemDestinoId: transferData.armazemDestinoId,
          quantidade: transferData.quantidade,
          motivo: transferData.motivo,
          ordemProducaoId: transferData.ordemProducaoId,
          solicitanteId: transferData.solicitanteId,
          dataHora: Timestamp.now(),
          status: 'CONCLUIDA',
          observacoes: transferData.observacoes
        };
        transaction.set(transferenciaRef, transferenciaData);
        
        // 7. Registrar movimentações de estoque
        // Saída do armazém origem
        const movSaidaRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movSaidaRef, {
          produtoId: transferData.produtoId,
          armazemId: transferData.armazemOrigemId,
          tipo: 'SAIDA',
          quantidade: transferData.quantidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: numeroTransferencia,
          observacoes: `Transferência para ${transferData.armazemDestinoId} - ${transferData.motivo}`,
          dataHora: Timestamp.now(),
          transferenciaId: transferenciaRef.id
        });
        
        // Entrada no armazém destino
        const movEntradaRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movEntradaRef, {
          produtoId: transferData.produtoId,
          armazemId: transferData.armazemDestinoId,
          tipo: 'ENTRADA',
          quantidade: transferData.quantidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: numeroTransferencia,
          observacoes: `Transferência de ${transferData.armazemOrigemId} - ${transferData.motivo}`,
          dataHora: Timestamp.now(),
          transferenciaId: transferenciaRef.id
        });
        
        // 8. Atualizar necessidades da OP se aplicável
        if (transferData.ordemProducaoId) {
          await this.updateOrderRequirements(
            transaction,
            transferData.ordemProducaoId,
            transferData.produtoId,
            transferData.quantidade
          );
        }
        
        return {
          transferenciaId: transferenciaRef.id,
          numero: numeroTransferencia,
          saldoOrigemAnterior: dadosOrigem.saldo,
          saldoOrigemPosterior: dadosOrigem.saldo - transferData.quantidade,
          saldoDestinoPosterior: dadosDestino.saldo + transferData.quantidade
        };
      });
      
      console.log('Transferência executada:', result);
      
      // Notificar sistemas relacionados
      await this.notifyTransfer(result);
      
      return result;
      
    } catch (error) {
      console.error('Erro na transferência:', error);
      throw new Error(`Falha na transferência: ${error.message}`);
    }
  }
  
  /**
   * Transferência automática para suprir OP
   */
  static async autoTransferForOrder(orderId, produtoId, quantidade) {
    try {
      // Buscar armazém principal (ALM01)
      const armazensSnap = await getDocs(collection(db, "armazens"));
      const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      const almoxarifado = armazens.find(a => a.codigo === 'ALM01' || a.tipo === 'ALMOXARIFADO');
      if (!almoxarifado) {
        throw new Error('Armazém principal não encontrado');
      }
      
      // Buscar ordem de produção
      const orderDoc = await getDoc(doc(db, "ordensProducao", orderId));
      if (!orderDoc.exists()) {
        throw new Error('Ordem de produção não encontrada');
      }
      
      const order = orderDoc.data();
      const armazemProducao = armazens.find(a => a.id === order.armazemProducaoId);
      
      if (!armazemProducao) {
        throw new Error('Armazém de produção não encontrado');
      }
      
      // Executar transferência
      return await this.executeTransfer({
        produtoId,
        armazemOrigemId: almoxarifado.id,
        armazemDestinoId: armazemProducao.id,
        quantidade,
        motivo: 'SUPRIMENTO_OP',
        ordemProducaoId: orderId,
        observacoes: `Transferência automática para suprir OP ${order.numero}`,
        solicitanteId: 'SISTEMA'
      });
      
    } catch (error) {
      console.error('Erro na transferência automática:', error);
      throw new Error(`Falha na transferência automática: ${error.message}`);
    }
  }
  
  /**
   * Transferência em lote para múltiplos materiais
   */
  static async batchTransfer(transfers) {
    const results = [];
    const errors = [];
    
    for (const transfer of transfers) {
      try {
        const result = await this.executeTransfer(transfer);
        results.push(result);
      } catch (error) {
        errors.push({
          transfer,
          error: error.message
        });
      }
    }
    
    return {
      sucessos: results.length,
      erros: errors.length,
      detalhes: { results, errors }
    };
  }
  
  /**
   * Valida dados da transferência
   */
  static validateTransferData(transferData) {
    const required = ['produtoId', 'armazemOrigemId', 'armazemDestinoId', 'quantidade'];
    
    for (const field of required) {
      if (!transferData[field]) {
        throw new Error(`Campo obrigatório: ${field}`);
      }
    }
    
    if (transferData.quantidade <= 0) {
      throw new Error('Quantidade deve ser maior que zero');
    }
    
    if (transferData.armazemOrigemId === transferData.armazemDestinoId) {
      throw new Error('Armazém de origem e destino não podem ser iguais');
    }
  }
  
  /**
   * Atualiza necessidades da ordem de produção
   */
  static async updateOrderRequirements(transaction, orderId, produtoId, quantidade) {
    const orderRef = doc(db, "ordensProducao", orderId);
    const orderDoc = await transaction.get(orderRef);
    
    if (orderDoc.exists()) {
      const order = orderDoc.data();
      
      if (order.materiaisNecessarios) {
        const materiaisAtualizados = order.materiaisNecessarios.map(material => {
          if (material.produtoId === produtoId) {
            return {
              ...material,
              necessidade: Math.max(0, material.necessidade - quantidade),
              saldoEstoque: (material.saldoEstoque || 0) + quantidade
            };
          }
          return material;
        });
        
        transaction.update(orderRef, {
          materiaisNecessarios: materiaisAtualizados,
          ultimaAtualizacao: Timestamp.now()
        });
      }
    }
  }
  
  /**
   * Gera número sequencial para transferência
   */
  static async generateTransferNumber() {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Buscar último número
    const lastTransfer = await getDocs(
      query(
        collection(db, "transferenciasArmazem"),
        where("numero", ">=", `TRF${year}${month}`),
        where("numero", "<", `TRF${year}${month}9999`),
        orderBy("numero", "desc")
      )
    );
    
    let nextNumber = 1;
    if (!lastTransfer.empty) {
      const lastNumber = lastTransfer.docs[0].data().numero;
      const lastSequence = parseInt(lastNumber.slice(-7));
      nextNumber = lastSequence + 1;
    }
    
    return `TRF${year}${month}${String(nextNumber).padStart(4, '0')}`;
  }
  
  /**
   * Notifica sistemas sobre transferência
   */
  static async notifyTransfer(result) {
    try {
      // Atualizar cache de relatórios
      // Disparar eventos para dashboards
      // Verificar alertas de estoque
      console.log('Notificações de transferência enviadas');
    } catch (error) {
      console.warn('Erro ao enviar notificações:', error);
    }
  }
  
  /**
   * Busca histórico de transferências
   */
  static async getTransferHistory(filters = {}) {
    try {
      let transferQuery = collection(db, "transferenciasArmazem");
      
      // Aplicar filtros
      if (filters.produtoId) {
        transferQuery = query(transferQuery, where("produtoId", "==", filters.produtoId));
      }
      
      if (filters.armazemOrigemId) {
        transferQuery = query(transferQuery, where("armazemOrigemId", "==", filters.armazemOrigemId));
      }
      
      if (filters.dataInicio && filters.dataFim) {
        transferQuery = query(
          transferQuery,
          where("dataHora", ">=", filters.dataInicio),
          where("dataHora", "<=", filters.dataFim)
        );
      }
      
      // Ordenar por data
      transferQuery = query(transferQuery, orderBy("dataHora", "desc"));
      
      // Aplicar limite
      if (filters.limit) {
        transferQuery = query(transferQuery, limit(filters.limit));
      }
      
      const snapshot = await getDocs(transferQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
    } catch (error) {
      console.error('Erro ao buscar histórico:', error);
      throw new Error(`Falha ao buscar histórico: ${error.message}`);
    }
  }
}

// Exportar para uso global
window.TransferService = TransferService;
