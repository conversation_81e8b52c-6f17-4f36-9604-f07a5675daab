// Integração entre sistema de compras e estoque
import { db } from '../firebase-config.js';
import { collection, query, where, getDocs, doc, updateDoc, addDoc, getDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class IntegracaoEstoque {
    constructor() {
        this.estoqueCollection = collection(db, 'estoques');
        this.movimentacoesCollection = collection(db, 'movimentacoesEstoque');
        this.parametrosDoc = doc(db, 'parametros', 'integracao_estoque');
    }

    // Verifica se as funcionalidades estão ativas nas configurações
    async verificarConfiguracoes() {
        try {
            const configDoc = await getDoc(this.parametrosDoc);
            if (!configDoc.exists()) {
                return {
                    monitoramentoAtivo: false,
                    alertasAtivos: false,
                    sugestoesAtivas: false,
                    frequenciaHoras: 12
                };
            }
            return configDoc.data();
        } catch (error) {
            console.error('Erro ao verificar configurações:', error);
            throw error;
        }
    }

    // Verifica saldo em estoque de um produto
    async verificarSaldoEstoque(produtoId, armazemId) {
        const config = await this.verificarConfiguracoes();
        if (!config.monitoramentoAtivo) {
            console.log('Monitoramento de estoque não está ativo nas configurações');
            return 0;
        }
        try {
            const q = query(this.estoqueCollection, 
                where('produtoId', '==', produtoId),
                where('armazemId', '==', armazemId)
            );
            const querySnapshot = await getDocs(q);
            if (querySnapshot.empty) {
                return 0;
            }
            return querySnapshot.docs[0].data().saldo || 0;
        } catch (error) {
            console.error('Erro ao verificar saldo:', error);
            throw error;
        }
    }

    // Atualiza o saldo em estoque após aprovação de compra
    async atualizarEstoqueAposCompra(produtoId, armazemId, quantidade, documento) {
        const config = await this.verificarConfiguracoes();
        if (!config.monitoramentoAtivo) {
            console.log('Monitoramento de estoque não está ativo nas configurações');
            return false;
        }
        try {
            // Verifica se já existe registro de estoque
            const q = query(this.estoqueCollection,
                where('produtoId', '==', produtoId),
                where('armazemId', '==', armazemId)
            );
            const querySnapshot = await getDocs(q);

            const movimentacao = {
                produtoId,
                armazemId,
                quantidade,
                tipo: 'ENTRADA',
                origem: 'COMPRA',
                documento,
                dataMovimentacao: new Date(),
            };

            // Registra a movimentação
            await addDoc(this.movimentacoesCollection, movimentacao);

            if (querySnapshot.empty) {
                // Cria novo registro de estoque
                await addDoc(this.estoqueCollection, {
                    produtoId,
                    armazemId,
                    saldo: quantidade
                });
            } else {
                // Atualiza saldo existente
                const estoqueDoc = querySnapshot.docs[0];
                const saldoAtual = estoqueDoc.data().saldo || 0;
                await updateDoc(doc(this.estoqueCollection, estoqueDoc.id), {
                    saldo: saldoAtual + quantidade
                });
            }

            return true;
        } catch (error) {
            console.error('Erro ao atualizar estoque:', error);
            throw error;
        }
    }

    // Verifica disponibilidade de estoque para uma lista de itens
    async verificarDisponibilidadeItens(itens) {
        const config = await this.verificarConfiguracoes();
        if (!config.monitoramentoAtivo) {
            console.log('Monitoramento de estoque não está ativo nas configurações');
            return itens.map(item => ({
                ...item,
                saldoDisponivel: 0,
                disponivel: false
            }));
        }
        try {
            const resultado = [];
            for (const item of itens) {
                const saldo = await this.verificarSaldoEstoque(item.produtoId, item.armazemId);
                resultado.push({
                    produtoId: item.produtoId,
                    armazemId: item.armazemId,
                    quantidadeSolicitada: item.quantidade,
                    saldoDisponivel: saldo,
                    disponivel: saldo >= item.quantidade
                });
            }
            return resultado;
        } catch (error) {
            console.error('Erro ao verificar disponibilidade:', error);
            throw error;
        }
    }

    // Retorna o histórico de movimentações de um produto
    async obterHistoricoMovimentacoes(produtoId, limit = 10) {
        const config = await this.verificarConfiguracoes();
        if (!config.monitoramentoAtivo) {
            console.log('Monitoramento de estoque não está ativo nas configurações');
            return [];
        }
        try {
            const q = query(this.movimentacoesCollection,
                where('produtoId', '==', produtoId),
                limit
            );
            const querySnapshot = await getDocs(q);
            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Erro ao obter histórico:', error);
            throw error;
        }
    }
}
