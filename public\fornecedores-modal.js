// Modal de busca e seleção de fornecedores
const fornecedores = [] // Declare fornecedores variable
const viewLinkedItems = () => {} // Declare viewLinkedItems function

function abrirModalFornecedores() {
  // Criar o modal dinamicamente
  const modal = document.createElement("div")
  modal.className = "modal"
  modal.id = "fornecedoresModal"

  const content = document.createElement("div")
  content.className = "modal-content"
  content.style.maxWidth = "800px"

  // Cabeçalho do modal
  const header = document.createElement("div")
  header.style.display = "flex"
  header.style.justifyContent = "space-between"
  header.style.alignItems = "center"
  header.style.padding = "15px 20px"
  header.style.borderBottom = "1px solid var(--border-color)"

  const title = document.createElement("h2")
  title.textContent = "Selecionar Fornecedor"
  header.appendChild(title)

  const closeButton = document.createElement("span")
  closeButton.className = "close-button"
  closeButton.textContent = "×"
  closeButton.onclick = () => {
    document.body.removeChild(modal)
  }
  header.appendChild(closeButton)
  content.appendChild(header)

  // Campo de busca
  const searchContainer = document.createElement("div")
  searchContainer.style.padding = "15px 20px"
  searchContainer.innerHTML = `
    <div style="margin-bottom: 15px;">
      <input type="text" id="searchFornecedor" placeholder="Buscar por nome, CNPJ ou cidade..." 
             style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
    </div>
  `
  content.appendChild(searchContainer)

  // Tabela de fornecedores
  const tableContainer = document.createElement("div")
  tableContainer.style.padding = "0 20px 15px 20px"
  tableContainer.style.maxHeight = "400px"
  tableContainer.style.overflowY = "auto"
  tableContainer.innerHTML = `
    <table class="items-table">
      <thead>
        <tr>
          <th>Razão Social</th>
          <th>CNPJ</th>
          <th>Cidade</th>
          <th>Status</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="fornecedoresTableBody"></tbody>
    </table>
  `
  content.appendChild(tableContainer)

  modal.appendChild(content)
  document.body.appendChild(modal)

  // Carregar fornecedores
  carregarFornecedoresNoModal()

  // Adicionar evento de busca
  document.getElementById("searchFornecedor").addEventListener("input", function () {
    filtrarFornecedores(this.value)
  })
}

// Função para carregar fornecedores no modal
function carregarFornecedoresNoModal() {
  const tableBody = document.getElementById("fornecedoresTableBody")
  if (!tableBody) return

  tableBody.innerHTML = ""

  if (!fornecedores || !Array.isArray(fornecedores) || fornecedores.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center;">Nenhum fornecedor encontrado</td></tr>'
    return
  }

  // Filtrar apenas fornecedores homologados
  const fornecedoresHomologados = fornecedores.filter((f) => f.statusHomologacao === "Homologado")

  fornecedoresHomologados.forEach((fornecedor) => {
    const row = document.createElement("tr")
    row.innerHTML = `
      <td>${fornecedor.razaoSocial || "N/A"}</td>
      <td>${fornecedor.cnpj || "N/A"}</td>
      <td>${fornecedor.cidade || "N/A"}</td>
      <td><span class="status-badge status-aprovada">Homologado</span></td>
      <td>
        <button class="btn-primary" onclick="selecionarFornecedor('${fornecedor.id}')">Selecionar</button>
      </td>
    `
    tableBody.appendChild(row)
  })
}

// Função para filtrar fornecedores
function filtrarFornecedores(searchText) {
  const rows = document.querySelectorAll("#fornecedoresTableBody tr")
  const search = searchText.toLowerCase()

  rows.forEach((row) => {
    const razaoSocial = row.cells[0].textContent.toLowerCase()
    const cnpj = row.cells[1].textContent.toLowerCase()
    const cidade = row.cells[2].textContent.toLowerCase()

    if (razaoSocial.includes(search) || cnpj.includes(search) || cidade.includes(search)) {
      row.style.display = ""
    } else {
      row.style.display = "none"
    }
  })
}

// Função para selecionar um fornecedor
function selecionarFornecedor(fornecedorId) {
  const fornecedor = fornecedores.find((f) => f.id === fornecedorId)
  if (!fornecedor) {
    alert("Fornecedor não encontrado")
    return
  }

  const selectFornecedor = document.getElementById("fornecedor")
  if (selectFornecedor) {
    selectFornecedor.value = fornecedorId

    // Disparar evento de mudança para atualizar qualquer lógica dependente
    const event = new Event("change")
    selectFornecedor.dispatchEvent(event)

    // Verificar se há itens vinculados a este fornecedor
    const temItensVinculados =
      window.produtosFornecedores && window.produtosFornecedores.some((v) => v.fornecedorId === fornecedorId)

    if (temItensVinculados) {
      if (confirm("Este fornecedor possui itens vinculados. Deseja visualizá-los?")) {
        viewLinkedItems()
      }
    }
  }

  // Fechar o modal
  const modal = document.getElementById("fornecedoresModal")
  if (modal) {
    document.body.removeChild(modal)
  }
}
