<?xml version="1.0" encoding="UTF-8"?>
<svg width="320" height="140" viewBox="0 0 320 140" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradiente azul para FYRON -->
    <linearGradient id="fyronGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>

    <!-- Gradiente cinza para MRP -->
    <linearGradient id="mrpGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d1d5db;stop-opacity:1" />
    </linearGradient>

    <!-- Gradientes para as formas dinâmicas -->
    <linearGradient id="shapeBlue1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="shapeBlue2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="shapeGreen" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <!-- Sombra 3D -->
    <filter id="shadow3d" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="6" stdDeviation="4" flood-color="#000000" flood-opacity="0.4"/>
    </filter>

    <!-- Brilho -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Formas dinâmicas 3D inspiradas no logo original -->
  <!-- Forma azul superior esquerda -->
  <path d="M 15 20 Q 35 10 55 20 Q 65 25 75 15 Q 85 10 95 20 Q 105 25 115 15 L 110 30 Q 100 35 90 30 Q 80 25 70 30 Q 60 35 50 30 Q 40 25 30 30 L 20 35 Z"
        fill="url(#shapeBlue1)"
        filter="url(#shadow3d)"
        opacity="0.95"/>

  <!-- Forma azul inferior esquerda -->
  <path d="M 25 35 Q 45 25 65 35 Q 75 40 85 30 Q 95 25 105 35 Q 115 40 125 30 L 120 45 Q 110 50 100 45 Q 90 40 80 45 Q 70 50 60 45 Q 50 40 40 45 L 30 50 Z"
        fill="url(#shapeBlue2)"
        filter="url(#shadow3d)"
        opacity="0.9"/>

  <!-- Forma verde direita -->
  <path d="M 115 25 Q 135 15 155 25 Q 165 30 175 20 Q 185 15 195 25 L 190 40 Q 180 45 170 40 Q 160 35 150 40 Q 140 45 130 40 Q 120 35 115 40 Z"
        fill="url(#shapeGreen)"
        filter="url(#shadow3d)"
        opacity="0.95"/>

  <!-- Texto FYRON com efeito 3D -->
  <text x="20" y="85"
        font-family="'Arial Black', 'Helvetica', sans-serif"
        font-size="42"
        font-weight="900"
        fill="url(#fyronGradient)"
        filter="url(#shadow3d)"
        letter-spacing="3">
    FYRON
  </text>

  <!-- Texto MRP -->
  <text x="100" y="110"
        font-family="'Arial Black', 'Helvetica', sans-serif"
        font-size="16"
        font-weight="700"
        fill="url(#mrpGradient)"
        filter="url(#shadow3d)"
        letter-spacing="2">
    MRP
  </text>

  <!-- Linha decorativa -->
  <line x1="20" y1="120" x2="200" y2="120"
        stroke="url(#fyronGradient)"
        stroke-width="2"
        opacity="0.3"/>

  <!-- Tagline -->
  <text x="20" y="135"
        font-family="'Segoe UI', sans-serif"
        font-size="10"
        font-weight="400"
        fill="#6b7280"
        letter-spacing="1">
    Sistema de Gestão Empresarial
  </text>

  <!-- Efeito de brilho sutil animado -->
  <rect x="0" y="0" width="320" height="140" fill="none" stroke="none">
    <animate attributeName="opacity" values="0.9;1;0.9" dur="4s" repeatCount="indefinite"/>
  </rect>
</svg>
