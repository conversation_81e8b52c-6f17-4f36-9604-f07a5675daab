<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ005 - Homologação de Fornecedores</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .process-info {
            background: #f8f9fa;
            border-left: 5px solid #2c3e50;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .process-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-info ul {
            list-style: none;
            padding: 0;
        }

        .process-info li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .process-info li:last-child {
            border-bottom: none;
        }

        .process-info li i {
            color: #2c3e50;
            margin-right: 10px;
            width: 20px;
        }

        .homologation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .homologation-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .homologation-card:hover {
            transform: translateY(-5px);
        }

        .homologation-card.pendente { border-left-color: #f39c12; }
        .homologation-card.avaliacao { border-left-color: #3498db; }
        .homologation-card.homologado { border-left-color: #27ae60; }
        .homologation-card.suspenso { border-left-color: #e74c3c; }

        .homologation-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .homologation-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            text-transform: uppercase;
            margin-top: 5px;
        }

        .criteria-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .criteria-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .criteria-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .criteria-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .criteria-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .criteria-weight {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            float: right;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
        }

        .status-avaliacao {
            background: #cce5ff;
            color: #004085;
        }

        .status-homologado {
            background: #d4edda;
            color: #155724;
        }

        .status-suspenso {
            background: #f8d7da;
            color: #721c24;
        }

        .score-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c 0%, #f39c12 50%, #27ae60 100%);
            transition: width 0.3s ease;
        }

        .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .homologation-grid {
                grid-template-columns: 1fr;
            }

            .criteria-grid {
                grid-template-columns: 1fr;
            }

            .filters {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 PQ005 - Homologação de Fornecedores</h1>
            <p>Qualificação e aprovação de fornecedores</p>
        </div>

        <div class="content">
            <!-- Informações do Processo -->
            <div class="process-info">
                <h3>📋 Sobre este Processo</h3>
                <ul>
                    <li><i class="fas fa-target"></i><strong>Objetivo:</strong> Qualificar fornecedores através de critérios técnicos e comerciais</li>
                    <li><i class="fas fa-clipboard-check"></i><strong>Processo:</strong> Avaliação sistemática baseada em critérios pré-definidos</li>
                    <li><i class="fas fa-award"></i><strong>Resultado:</strong> Fornecedores homologados aptos para fornecimento</li>
                    <li><i class="fas fa-sync"></i><strong>Renovação:</strong> Reavaliação periódica para manter homologação</li>
                </ul>
            </div>

            <!-- Cards de Homologação -->
            <div class="homologation-grid">
                <div class="homologation-card pendente">
                    <h4><i class="fas fa-clock"></i> Pendentes</h4>
                    <div class="homologation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="pendenteCount">0</div>
                            <div class="stat-label">Fornecedores</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="pendenteDias">0</div>
                            <div class="stat-label">Dias Médios</div>
                        </div>
                    </div>
                    <button class="btn btn-warning btn-full" onclick="filtrarStatus('PENDENTE')">
                        <i class="fas fa-eye"></i> Ver Pendentes
                    </button>
                </div>

                <div class="homologation-card avaliacao">
                    <h4><i class="fas fa-search"></i> Em Avaliação</h4>
                    <div class="homologation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="avaliacaoCount">0</div>
                            <div class="stat-label">Fornecedores</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avaliacaoScore">0</div>
                            <div class="stat-label">Score Médio</div>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" onclick="filtrarStatus('AVALIACAO')">
                        <i class="fas fa-eye"></i> Ver Avaliação
                    </button>
                </div>

                <div class="homologation-card homologado">
                    <h4><i class="fas fa-check-circle"></i> Homologados</h4>
                    <div class="homologation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="homologadoCount">0</div>
                            <div class="stat-label">Fornecedores</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="homologadoScore">0</div>
                            <div class="stat-label">Score Médio</div>
                        </div>
                    </div>
                    <button class="btn btn-success btn-full" onclick="filtrarStatus('HOMOLOGADO')">
                        <i class="fas fa-eye"></i> Ver Homologados
                    </button>
                </div>

                <div class="homologation-card suspenso">
                    <h4><i class="fas fa-ban"></i> Suspensos</h4>
                    <div class="homologation-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="suspensoCount">0</div>
                            <div class="stat-label">Fornecedores</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="suspensoMotivos">0</div>
                            <div class="stat-label">Motivos</div>
                        </div>
                    </div>
                    <button class="btn btn-danger btn-full" onclick="filtrarStatus('SUSPENSO')">
                        <i class="fas fa-eye"></i> Ver Suspensos
                    </button>
                </div>
            </div>

            <!-- Critérios de Homologação -->
            <div class="criteria-section">
                <h3><i class="fas fa-list-check"></i> Critérios de Homologação</h3>
                <div class="criteria-grid">
                    <div class="criteria-item">
                        <div class="criteria-weight">Peso 25%</div>
                        <div class="criteria-title">Qualidade</div>
                        <div class="criteria-description">Certificações, sistema de qualidade, histórico de não conformidades</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-weight">Peso 20%</div>
                        <div class="criteria-title">Prazo de Entrega</div>
                        <div class="criteria-description">Pontualidade, capacidade de atendimento, flexibilidade</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-weight">Peso 20%</div>
                        <div class="criteria-title">Preço</div>
                        <div class="criteria-description">Competitividade, condições de pagamento, estabilidade</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-weight">Peso 15%</div>
                        <div class="criteria-title">Capacidade Técnica</div>
                        <div class="criteria-description">Recursos, tecnologia, expertise, inovação</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-weight">Peso 10%</div>
                        <div class="criteria-title">Situação Financeira</div>
                        <div class="criteria-description">Solidez financeira, histórico de crédito, sustentabilidade</div>
                    </div>
                    <div class="criteria-item">
                        <div class="criteria-weight">Peso 10%</div>
                        <div class="criteria-title">Responsabilidade Social</div>
                        <div class="criteria-description">Meio ambiente, trabalhistas, ética, compliance</div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter">
                        <option value="">Todos os Status</option>
                        <option value="PENDENTE">Pendente</option>
                        <option value="AVALIACAO">Em Avaliação</option>
                        <option value="HOMOLOGADO">Homologado</option>
                        <option value="SUSPENSO">Suspenso</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="categoriaFilter">Categoria</label>
                    <select id="categoriaFilter">
                        <option value="">Todas as Categorias</option>
                        <option value="MATERIA_PRIMA">Matéria Prima</option>
                        <option value="COMPONENTES">Componentes</option>
                        <option value="SERVICOS">Serviços</option>
                        <option value="EMBALAGENS">Embalagens</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="scoreMin">Score Mínimo</label>
                    <input type="number" id="scoreMin" min="0" max="100" placeholder="0-100">
                </div>
                <div class="filter-group">
                    <label for="vencimentoFilter">Vencimento</label>
                    <select id="vencimentoFilter">
                        <option value="">Todos</option>
                        <option value="VENCIDO">Vencido</option>
                        <option value="30_DIAS">Próximos 30 dias</option>
                        <option value="90_DIAS">Próximos 90 dias</option>
                    </select>
                </div>
                <div class="filter-group" style="align-self: end;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div style="margin-bottom: 20px; display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="novaHomologacao()">
                    <i class="fas fa-plus"></i> Nova Homologação
                </button>
                <button class="btn btn-primary" onclick="avaliacaoLote()">
                    <i class="fas fa-clipboard-list"></i> Avaliação em Lote
                </button>
                <button class="btn btn-warning" onclick="renovarHomologacoes()">
                    <i class="fas fa-sync"></i> Renovar Homologações
                </button>
                <button class="btn btn-danger" onclick="alertasVencimento()">
                    <i class="fas fa-exclamation-triangle"></i> Alertas
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando homologações...</p>
            </div>

            <!-- Tabela de Homologações -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Fornecedor</th>
                            <th>Categoria</th>
                            <th>Score</th>
                            <th>Status</th>
                            <th>Data Homologação</th>
                            <th>Vencimento</th>
                            <th>Avaliador</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-user-check"></i>
                <h3>Nenhuma homologação encontrada</h3>
                <p>Não há fornecedores em processo de homologação no momento.</p>
                <button class="btn btn-primary" onclick="novaHomologacao()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Iniciar Primeira Homologação
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let homologacoes = [];
        let fornecedores = [];

        // Carregar dados iniciais
        async function loadData() {
            try {
                showLoading(true);

                // Carregar homologações e fornecedores
                const [homologacoesSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "homologacoesFornecedores")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                homologacoes = homologacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PQ005 - Dados carregados:', {
                    homologacoes: homologacoes.length,
                    fornecedores: fornecedores.length
                });

                updateStats();
                renderTable();
                showLoading(false);

            } catch (error) {
                console.error('❌ Erro ao carregar dados PQ005:', error);
                showLoading(false);
                showError('Erro ao carregar dados de homologação');
            }
        }

        function updateStats() {
            const stats = {
                pendente: { count: 0, dias: 0 },
                avaliacao: { count: 0, score: 0 },
                homologado: { count: 0, score: 0 },
                suspenso: { count: 0, motivos: 0 }
            };

            const today = new Date();

            homologacoes.forEach(item => {
                const status = item.status?.toLowerCase() || 'pendente';

                if (stats[status]) {
                    stats[status].count++;

                    if (status === 'pendente' && item.dataInicio) {
                        const dataInicio = new Date(item.dataInicio.seconds * 1000);
                        const dias = Math.floor((today - dataInicio) / (1000 * 60 * 60 * 24));
                        stats[status].dias += dias;
                    }

                    if ((status === 'avaliacao' || status === 'homologado') && item.score) {
                        stats[status].score += item.score;
                    }
                }
            });

            // Calcular médias
            if (stats.pendente.count > 0) {
                stats.pendente.dias = Math.round(stats.pendente.dias / stats.pendente.count);
            }
            if (stats.avaliacao.count > 0) {
                stats.avaliacao.score = Math.round(stats.avaliacao.score / stats.avaliacao.count);
            }
            if (stats.homologado.count > 0) {
                stats.homologado.score = Math.round(stats.homologado.score / stats.homologado.count);
            }

            // Atualizar interface
            document.getElementById('pendenteCount').textContent = stats.pendente.count;
            document.getElementById('pendenteDias').textContent = stats.pendente.dias;
            document.getElementById('avaliacaoCount').textContent = stats.avaliacao.count;
            document.getElementById('avaliacaoScore').textContent = stats.avaliacao.score;
            document.getElementById('homologadoCount').textContent = stats.homologado.count;
            document.getElementById('homologadoScore').textContent = stats.homologado.score;
            document.getElementById('suspensoCount').textContent = stats.suspenso.count;
            document.getElementById('suspensoMotivos').textContent = stats.suspenso.count; // Simplificado
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (homologacoes.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = homologacoes.map(item => {
                const fornecedor = fornecedores.find(f => f.id === item.fornecedorId);
                const dataHomologacao = item.dataHomologacao ?
                    new Date(item.dataHomologacao.seconds * 1000).toLocaleDateString() : 'N/A';
                const dataVencimento = item.dataVencimento ?
                    new Date(item.dataVencimento.seconds * 1000).toLocaleDateString() : 'N/A';

                const score = item.score || 0;
                const scoreColor = score >= 80 ? '#27ae60' : score >= 60 ? '#f39c12' : '#e74c3c';

                return `
                    <tr>
                        <td><strong>${fornecedor?.razaoSocial || 'N/A'}</strong></td>
                        <td>${item.categoria || 'N/A'}</td>
                        <td>
                            <div class="score-bar">
                                <div class="score-fill" style="width: ${score}%; background: ${scoreColor};"></div>
                                <div class="score-text">${score}%</div>
                            </div>
                        </td>
                        <td><span class="status-badge status-${item.status?.toLowerCase() || 'pendente'}">${item.status || 'PENDENTE'}</span></td>
                        <td>${dataHomologacao}</td>
                        <td>${dataVencimento}</td>
                        <td>${item.avaliador || 'N/A'}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarHomologacao('${item.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${item.status === 'PENDENTE' ? `
                                    <button class="btn btn-warning btn-action" onclick="iniciarAvaliacao('${item.id}')" title="Iniciar Avaliação">
                                        <i class="fas fa-play"></i>
                                    </button>
                                ` : ''}
                                ${item.status === 'AVALIACAO' ? `
                                    <button class="btn btn-success btn-action" onclick="finalizarAvaliacao('${item.id}')" title="Finalizar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                ` : ''}
                                ${item.status === 'HOMOLOGADO' ? `
                                    <button class="btn btn-warning btn-action" onclick="renovarHomologacao('${item.id}')" title="Renovar">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            alert('❌ ' + message);
        }

        // Funções globais
        window.filtrarStatus = function(status) {
            document.getElementById('statusFilter').value = status;
            aplicarFiltros();
        };

        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar filtros
        };

        window.novaHomologacao = function() {
            alert('👥 Funcionalidade em desenvolvimento: Nova Homologação');
        };

        window.avaliacaoLote = function() {
            alert('📋 Funcionalidade em desenvolvimento: Avaliação em Lote');
        };

        window.renovarHomologacoes = function() {
            alert('🔄 Funcionalidade em desenvolvimento: Renovar Homologações');
        };

        window.alertasVencimento = function() {
            alert('⚠️ Funcionalidade em desenvolvimento: Alertas de Vencimento');
        };

        window.visualizarHomologacao = function(id) {
            alert('👁️ Funcionalidade em desenvolvimento: Visualizar Homologação ' + id);
        };

        window.iniciarAvaliacao = function(id) {
            alert('▶️ Funcionalidade em desenvolvimento: Iniciar Avaliação ' + id);
        };

        window.finalizarAvaliacao = function(id) {
            alert('✅ Funcionalidade em desenvolvimento: Finalizar Avaliação ' + id);
        };

        window.renovarHomologacao = function(id) {
            alert('🔄 Funcionalidade em desenvolvimento: Renovar Homologação ' + id);
        };

        // Inicializar
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>