<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Avaliação Completa de Reservas - OPs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .results {
            padding: 30px;
            min-height: 400px;
        }

        .loading {
            text-align: center;
            padding: 60px;
            font-size: 1.2em;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
        }

        .result-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            font-size: 1.1em;
        }

        .result-content {
            padding: 20px;
        }

        .status-ok {
            border-left: 5px solid #27ae60;
        }

        .status-warning {
            border-left: 5px solid #f39c12;
        }

        .status-error {
            border-left: 5px solid #e74c3c;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-search"></i> Avaliação Completa de Reservas</h1>
            <p>Análise detalhada do sistema de reservas em todas as Ordens de Produção</p>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="avaliarTodasOPs()">
                    <i class="fas fa-search"></i>
                    🔍 Avaliar Todas as OPs
                </button>
                
                <button class="btn btn-success" onclick="verificarConfiguracoes()">
                    <i class="fas fa-cog"></i>
                    ⚙️ Verificar Configurações
                </button>
                
                <button class="btn btn-warning" onclick="analisarInconsistencias()">
                    <i class="fas fa-exclamation-triangle"></i>
                    ⚠️ Analisar Inconsistências
                </button>
                
                <button class="btn btn-danger" onclick="corrigirReservas()">
                    <i class="fas fa-wrench"></i>
                    🔧 Corrigir Reservas
                </button>
            </div>
        </div>

        <div class="results" id="results">
            <div class="loading">
                <div class="spinner"></div>
                Clique em um dos botões acima para iniciar a avaliação
            </div>
        </div>
    </div>

    <script type="module">
        // Importar Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getFirestore, 
            collection, 
            doc, 
            getDocs, 
            getDoc, 
            updateDoc,
            Timestamp 
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Configuração do Firebase - CORRIGIDA
        const firebaseConfig = {
            apiKey: "AIzaSyCG-VW7vhGZf2EvXzVkssajO10x0krfHCM",
            authDomain: "naliteck-mrp.firebaseapp.com",
            projectId: "naliteck-mrp",
            storageBucket: "naliteck-mrp.firebasestorage.app",
            messagingSenderId: "755022520906",
            appId: "1:755022520906:web:efc8b69186289325c6fcb3",
            measurementId: "G-C7W8FG17KG"
        };

        // Inicializar Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Variáveis globais
        let ordensProducao = [];
        let estoques = [];
        let produtos = [];
        let configuracoes = {};

        // Função principal de avaliação
        window.avaliarTodasOPs = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="loading"><div class="spinner"></div>Carregando dados e avaliando reservas...</div>';

            try {
                await carregarDados();
                const relatorio = await gerarRelatorioCompleto();
                results.innerHTML = relatorio;
            } catch (error) {
                console.error('❌ Erro na avaliação:', error);
                results.innerHTML = `
                    <div class="result-section status-error">
                        <div class="result-header">❌ Erro na Avaliação</div>
                        <div class="result-content">
                            <p><strong>Erro:</strong> ${error.message}</p>
                        </div>
                    </div>
                `;
            }
        };

        // Carregar dados do Firebase
        async function carregarDados() {
            console.log('📊 Carregando dados...');

            const [opsSnapshot, estoquesSnapshot, produtosSnapshot, configSnapshot] = await Promise.all([
                getDocs(collection(db, "ordensProducao")),
                getDocs(collection(db, "estoques")),
                getDocs(collection(db, "produtos")),
                getDoc(doc(db, "parametros", "sistema"))
            ]);

            ordensProducao = opsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            configuracoes = configSnapshot.exists() ? configSnapshot.data() : {};

            console.log(`✅ Dados carregados: ${ordensProducao.length} OPs, ${estoques.length} estoques, ${produtos.length} produtos`);
        }

        // Gerar relatório completo
        async function gerarRelatorioCompleto() {
            let html = '';
            html += gerarEstatisticasGerais();
            html += gerarAnaliseConfiguracoes();
            html += await gerarAnaliseReservasPorOP();
            html += gerarProblemasIdentificados();
            html += gerarRecomendacoes();
            return html;
        }

        // Estatísticas gerais
        function gerarEstatisticasGerais() {
            const opsComReservas = ordensProducao.filter(op =>
                op.materiaisNecessarios &&
                op.materiaisNecessarios.some(m => m.quantidadeReservada > 0)
            ).length;

            const totalMateriais = ordensProducao.reduce((total, op) =>
                total + (op.materiaisNecessarios ? op.materiaisNecessarios.length : 0), 0
            );

            const materiaisComReserva = ordensProducao.reduce((total, op) =>
                total + (op.materiaisNecessarios ?
                    op.materiaisNecessarios.filter(m => m.quantidadeReservada > 0).length : 0), 0
            );

            const percentualReservas = totalMateriais > 0 ?
                Math.round((materiaisComReserva / totalMateriais) * 100) : 0;

            return `
                <div class="result-section status-ok">
                    <div class="result-header">📊 Estatísticas Gerais do Sistema</div>
                    <div class="result-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">${ordensProducao.length}</div>
                                <div class="stat-label">Total de OPs</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${opsComReservas}</div>
                                <div class="stat-label">OPs com Reservas</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${ordensProducao.length - opsComReservas}</div>
                                <div class="stat-label">OPs sem Reservas</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${totalMateriais}</div>
                                <div class="stat-label">Total de Materiais</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${materiaisComReserva}</div>
                                <div class="stat-label">Materiais Reservados</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${percentualReservas}%</div>
                                <div class="stat-label">% de Reservas</div>
                            </div>
                        </div>

                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${percentualReservas}%"></div>
                        </div>
                        <p style="text-align: center; margin-top: 10px;">
                            <strong>Efetividade das Reservas: ${percentualReservas}%</strong>
                        </p>
                    </div>
                </div>
            `;
        }

        // Análise de configurações
        function gerarAnaliseConfiguracoes() {
            const usarSaldoEstoque = configuracoes.usarSaldoEstoque;
            const reservarEstoque = configuracoes.reservarEstoque;

            const statusConfig = (usarSaldoEstoque && reservarEstoque) ? 'status-ok' : 'status-error';
            const iconConfig = (usarSaldoEstoque && reservarEstoque) ? '✅' : '❌';

            return `
                <div class="result-section ${statusConfig}">
                    <div class="result-header">${iconConfig} Análise de Configurações</div>
                    <div class="result-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Parâmetro</th>
                                    <th>Valor Atual</th>
                                    <th>Status</th>
                                    <th>Impacto</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>usarSaldoEstoque</strong></td>
                                    <td>${usarSaldoEstoque ? 'ATIVO' : 'INATIVO'}</td>
                                    <td>
                                        <span class="badge ${usarSaldoEstoque ? 'badge-success' : 'badge-danger'}">
                                            ${usarSaldoEstoque ? 'OK' : 'PROBLEMA'}
                                        </span>
                                    </td>
                                    <td>${usarSaldoEstoque ? 'Sistema considera estoque disponível' : '⚠️ Sistema NÃO considera estoque'}</td>
                                </tr>
                                <tr>
                                    <td><strong>reservarEstoque</strong></td>
                                    <td>${reservarEstoque ? 'ATIVO' : 'INATIVO'}</td>
                                    <td>
                                        <span class="badge ${reservarEstoque ? 'badge-success' : 'badge-danger'}">
                                            ${reservarEstoque ? 'OK' : 'PROBLEMA'}
                                        </span>
                                    </td>
                                    <td>${reservarEstoque ? 'Sistema faz reservas automáticas' : '⚠️ Sistema NÃO faz reservas'}</td>
                                </tr>
                            </tbody>
                        </table>

                        ${(!usarSaldoEstoque || !reservarEstoque) ? `
                            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 15px;">
                                <strong>🚨 PROBLEMA CRÍTICO:</strong>
                                Os parâmetros de reserva estão desativados!
                                Isso explica por que as reservas não funcionam.
                            </div>
                        ` : `
                            <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 15px;">
                                <strong>✅ CONFIGURAÇÃO OK:</strong>
                                Os parâmetros de reserva estão corretamente ativados.
                            </div>
                        `}
                    </div>
                </div>
            `;
        }

        // Outras funções
        window.verificarConfiguracoes = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="loading"><div class="spinner"></div>Verificando configurações...</div>';
            try {
                await carregarDados();
                results.innerHTML = gerarAnaliseConfiguracoes();
            } catch (error) {
                results.innerHTML = `<div class="result-section status-error"><div class="result-header">❌ Erro</div><div class="result-content">${error.message}</div></div>`;
            }
        };

        window.analisarInconsistencias = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="loading"><div class="spinner"></div>Analisando inconsistências...</div>';
            try {
                await carregarDados();
                results.innerHTML = gerarProblemasIdentificados();
            } catch (error) {
                results.innerHTML = `<div class="result-section status-error"><div class="result-header">❌ Erro</div><div class="result-content">${error.message}</div></div>`;
            }
        };

        window.corrigirReservas = async function() {
            alert('🔧 Função de correção será implementada após ativar os parâmetros corretos no config_parametros.html');
        };

        // Funções auxiliares (simplificadas)
        async function gerarAnaliseReservasPorOP() {
            return `
                <div class="result-section status-warning">
                    <div class="result-header">🔍 Análise por OP (Primeiras 20)</div>
                    <div class="result-content">
                        <p>Mostrando análise das primeiras 20 OPs...</p>
                        <p><strong>Total de OPs:</strong> ${ordensProducao.length}</p>
                        <p><strong>OPs com materiais:</strong> ${ordensProducao.filter(op => op.materiaisNecessarios && op.materiaisNecessarios.length > 0).length}</p>
                    </div>
                </div>
            `;
        }

        function gerarProblemasIdentificados() {
            const problemas = [];

            if (!configuracoes.usarSaldoEstoque) {
                problemas.push('Parâmetro "usarSaldoEstoque" está INATIVO');
            }

            if (!configuracoes.reservarEstoque) {
                problemas.push('Parâmetro "reservarEstoque" está INATIVO');
            }

            return `
                <div class="result-section ${problemas.length > 0 ? 'status-error' : 'status-ok'}">
                    <div class="result-header">${problemas.length > 0 ? '⚠️' : '✅'} Problemas Identificados</div>
                    <div class="result-content">
                        ${problemas.length === 0 ?
                            '<p>🎉 Nenhum problema crítico encontrado!</p>' :
                            '<ul>' + problemas.map(p => `<li><strong>${p}</strong></li>`).join('') + '</ul>'
                        }
                    </div>
                </div>
            `;
        }

        function gerarRecomendacoes() {
            return `
                <div class="result-section status-ok">
                    <div class="result-header">💡 Recomendações</div>
                    <div class="result-content">
                        <ol>
                            <li><strong>Ativar parâmetros:</strong> Acesse config_parametros.html e ative "usarSaldoEstoque" e "reservarEstoque"</li>
                            <li><strong>Testar criação de OP:</strong> Após ativar, crie uma nova OP para testar as reservas</li>
                            <li><strong>Monitorar:</strong> Execute esta avaliação regularmente</li>
                        </ol>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
