<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Planejamento de Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --border-color: #d4d4d4;
    }

    * { box-sizing: border-box; margin: 0; padding: 0; }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: #333;
    }

    .container {
      width: 95%;
      max-width: 1600px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header h1::before {
      content: '📅';
      font-size: 28px;
    }

    .planning-tabs {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border-color);
    }

    .tab {
      padding: 15px 25px;
      cursor: pointer;
      border: none;
      background: none;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
    }

    .tab.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
      background: white;
    }

    .tab-content {
      display: none;
      padding: 20px;
    }

    .tab-content.active {
      display: block;
    }

    .calendar-container {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      overflow: hidden;
    }

    .calendar-header {
      background: var(--primary-color);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .calendar-nav {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .nav-btn {
      background: rgba(255,255,255,0.2);
      border: none;
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .nav-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      background: var(--border-color);
    }

    .calendar-day-header {
      background: #f8f9fa;
      padding: 10px;
      text-align: center;
      font-weight: 600;
      font-size: 12px;
      color: #666;
    }

    .calendar-day {
      background: white;
      min-height: 120px;
      padding: 8px;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .calendar-day:hover {
      background: #f8f9fa;
      border-color: var(--primary-color);
    }

    .calendar-day.other-month {
      background: #f5f5f5;
      color: #999;
    }

    .calendar-day.today {
      background: #e3f2fd;
      border-color: var(--primary-color);
    }

    .day-number {
      font-weight: 600;
      margin-bottom: 5px;
      font-size: 14px;
    }

    .day-events {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .event-item {
      background: var(--primary-color);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 10px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      cursor: pointer;
    }

    .event-preventiva { background: var(--success-color); }
    .event-corretiva { background: var(--danger-color); }
    .event-preditiva { background: var(--warning-color); }

    .form-container {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin: 20px 0;
      background: white;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      margin-bottom: 5px;
      color: #666;
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
      transition: background-color 0.2s;
    }

    .btn-primary { background-color: var(--primary-color); color: white; }
    .btn-success { background-color: var(--success-color); color: white; }
    .btn-secondary { background-color: #6c757d; color: white; }

    .resource-planning {
      background: #f8f9fa;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }

    .resource-grid {
      display: grid;
      grid-template-columns: 200px 1fr;
      gap: 20px;
    }

    .resource-list {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 15px;
    }

    .resource-item {
      padding: 10px;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .resource-item:hover {
      background: #e3f2fd;
      border-color: var(--primary-color);
    }

    .resource-item.selected {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }

    .timeline-container {
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 15px;
    }

    .timeline-header {
      display: grid;
      grid-template-columns: repeat(24, 1fr);
      gap: 1px;
      margin-bottom: 10px;
    }

    .timeline-hour {
      text-align: center;
      font-size: 10px;
      color: #666;
      padding: 5px 2px;
    }

    .timeline-slot {
      height: 30px;
      border: 1px solid #e9ecef;
      position: relative;
      cursor: pointer;
    }

    .timeline-slot.occupied {
      background: var(--primary-color);
      color: white;
    }

    .timeline-slot.maintenance {
      background: var(--warning-color);
    }

    .gantt-container {
      overflow-x: auto;
      margin: 20px 0;
    }

    .gantt-chart {
      min-width: 800px;
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
    }

    .gantt-header {
      display: grid;
      grid-template-columns: 200px 1fr;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border-color);
    }

    .gantt-dates {
      display: grid;
      grid-template-columns: repeat(30, 1fr);
      gap: 1px;
    }

    .gantt-date {
      padding: 10px 5px;
      text-align: center;
      font-size: 12px;
      border-right: 1px solid var(--border-color);
    }

    .gantt-row {
      display: grid;
      grid-template-columns: 200px 1fr;
      border-bottom: 1px solid var(--border-color);
    }

    .gantt-task-name {
      padding: 15px 10px;
      background: #f8f9fa;
      border-right: 1px solid var(--border-color);
      font-size: 14px;
    }

    .gantt-timeline {
      display: grid;
      grid-template-columns: repeat(30, 1fr);
      gap: 1px;
      position: relative;
    }

    .gantt-task-bar {
      background: var(--primary-color);
      color: white;
      padding: 8px 4px;
      border-radius: 3px;
      font-size: 10px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 4px;
      color: white;
      z-index: 1001;
      display: none;
    }

    .notification-success { background-color: var(--success-color); }
    .notification-error { background-color: var(--danger-color); }

    @media (max-width: 768px) {
      .container { width: 100%; margin: 0; border-radius: 0; }
      .planning-tabs { flex-wrap: wrap; }
      .form-row { grid-template-columns: 1fr; }
      .resource-grid { grid-template-columns: 1fr; }
      .calendar-grid { font-size: 12px; }
      .calendar-day { min-height: 80px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Planejamento de Manutenção</h1>
      <div>
        <button class="btn btn-primary" onclick="novoPlano()">
          <i class="fas fa-plus"></i> Novo Plano
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div id="notification" class="notification"></div>

    <!-- Tabs de Planejamento -->
    <div class="planning-tabs">
      <button class="tab active" onclick="showTab('calendario')">
        <i class="fas fa-calendar"></i> Calendário
      </button>
      <button class="tab" onclick="showTab('recursos')">
        <i class="fas fa-users"></i> Recursos
      </button>
      <button class="tab" onclick="showTab('gantt')">
        <i class="fas fa-chart-bar"></i> Gantt
      </button>
      <button class="tab" onclick="showTab('relatorios')">
        <i class="fas fa-chart-line"></i> Relatórios
      </button>
    </div>

    <!-- Tab Calendário -->
    <div id="calendario" class="tab-content active">
      <div class="calendar-container">
        <div class="calendar-header">
          <h3 id="currentMonth">Janeiro 2024</h3>
          <div class="calendar-nav">
            <button class="nav-btn" onclick="changeMonth(-1)">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button class="nav-btn" onclick="goToToday()">Hoje</button>
            <button class="nav-btn" onclick="changeMonth(1)">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
        <div class="calendar-grid">
          <div class="calendar-day-header">Dom</div>
          <div class="calendar-day-header">Seg</div>
          <div class="calendar-day-header">Ter</div>
          <div class="calendar-day-header">Qua</div>
          <div class="calendar-day-header">Qui</div>
          <div class="calendar-day-header">Sex</div>
          <div class="calendar-day-header">Sáb</div>
          <!-- Calendar days will be generated by JavaScript -->
          <div id="calendarDays"></div>
        </div>
      </div>
    </div>

    <!-- Tab Recursos -->
    <div id="recursos" class="tab-content">
      <div class="resource-planning">
        <h3>Planejamento de Recursos</h3>
        <div class="resource-grid">
          <div class="resource-list">
            <h4>Funcionários</h4>
            <div id="funcionariosList"></div>
          </div>
          <div class="timeline-container">
            <h4>Cronograma do Dia</h4>
            <div class="timeline-header">
              <!-- Hours 0-23 -->
            </div>
            <div id="timelineSlots"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Gantt -->
    <div id="gantt" class="tab-content">
      <div class="gantt-container">
        <div class="gantt-chart">
          <div class="gantt-header">
            <div style="padding: 15px; font-weight: 600;">Ordens de Manutenção</div>
            <div class="gantt-dates" id="ganttDates"></div>
          </div>
          <div id="ganttRows"></div>
        </div>
      </div>
    </div>

    <!-- Tab Relatórios -->
    <div id="relatorios" class="tab-content">
      <div class="form-container">
        <h2 class="form-title">Relatórios de Planejamento</h2>
        
        <div class="form-row">
          <div class="form-col">
            <label for="relatorioTipo">Tipo de Relatório</label>
            <select id="relatorioTipo">
              <option value="capacidade">Capacidade de Recursos</option>
              <option value="carga">Carga de Trabalho</option>
              <option value="disponibilidade">Disponibilidade de Equipamentos</option>
              <option value="eficiencia">Eficiência de Manutenção</option>
            </select>
          </div>
          <div class="form-col">
            <label for="periodoInicio">Período Início</label>
            <input type="date" id="periodoInicio">
          </div>
          <div class="form-col">
            <label for="periodoFim">Período Fim</label>
            <input type="date" id="periodoFim">
          </div>
          <div class="form-col" style="display: flex; align-items: end;">
            <button class="btn btn-primary" onclick="gerarRelatorio()">
              <i class="fas fa-chart-bar"></i> Gerar Relatório
            </button>
          </div>
        </div>

        <div id="relatorioResultado" style="margin-top: 30px;"></div>
      </div>
    </div>
  </div>

  <!-- Modal de Novo Plano -->
  <div id="novoPlanoModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.6); z-index: 1000;">
    <div style="background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 600px; border-radius: 8px;">
      <h3>Novo Plano de Manutenção</h3>
      <form id="novoPlanoForm">
        <div class="form-row">
          <div class="form-col">
            <label>Equipamento</label>
            <select id="planoEquipamento" required>
              <option value="">Selecione...</option>
            </select>
          </div>
          <div class="form-col">
            <label>Tipo de Manutenção</label>
            <select id="planoTipo" required>
              <option value="PREVENTIVA">Preventiva</option>
              <option value="CORRETIVA">Corretiva</option>
              <option value="PREDITIVA">Preditiva</option>
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-col">
            <label>Data Planejada</label>
            <input type="date" id="planoData" required>
          </div>
          <div class="form-col">
            <label>Responsável</label>
            <select id="planoResponsavel" required>
              <option value="">Selecione...</option>
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-col">
            <label>Duração Estimada (horas)</label>
            <input type="number" id="planoDuracao" min="0.5" step="0.5" required>
          </div>
          <div class="form-col">
            <label>Prioridade</label>
            <select id="planoPrioridade">
              <option value="BAIXA">Baixa</option>
              <option value="MEDIA">Média</option>
              <option value="ALTA">Alta</option>
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-col">
            <label>Descrição</label>
            <textarea id="planoDescricao" rows="3"></textarea>
          </div>
        </div>
        <div style="text-align: right; margin-top: 20px;">
          <button type="button" class="btn btn-secondary" onclick="fecharModal()">Cancelar</button>
          <button type="submit" class="btn btn-success">Salvar Plano</button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, addDoc, getDocs, doc, updateDoc, serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let currentDate = new Date();
    let planosList = [];
    let funcionariosList = [];
    let equipamentosList = [];
    let ordensList = [];

    async function loadData() {
      try {
        const [planosSnap, funcSnap, equipSnap, ordensSnap] = await Promise.all([
          getDocs(collection(db, "planosManutencao")),
          getDocs(collection(db, "funcionariosManutencao")),
          getDocs(collection(db, "recursos")),
          getDocs(collection(db, "ordensManutencao"))
        ]);

        planosList = planosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        funcionariosList = funcSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        equipamentosList = equipSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensList = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        updateCalendar();
        updateResourcePlanning();
        updateGanttChart();
        loadSelectOptions();
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
      }
    }

    function updateCalendar() {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();
      
      document.getElementById('currentMonth').textContent = 
        new Intl.DateTimeFormat('pt-BR', { month: 'long', year: 'numeric' }).format(currentDate);

      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      const calendarDays = document.getElementById('calendarDays');
      calendarDays.innerHTML = '';

      for (let i = 0; i < 42; i++) {
        const cellDate = new Date(startDate);
        cellDate.setDate(startDate.getDate() + i);
        
        const dayDiv = document.createElement('div');
        dayDiv.className = 'calendar-day';
        
        if (cellDate.getMonth() !== month) {
          dayDiv.classList.add('other-month');
        }
        
        if (cellDate.toDateString() === new Date().toDateString()) {
          dayDiv.classList.add('today');
        }

        const dayEvents = planosList.filter(plano => {
          if (!plano.dataPlano) return false;
          const planoDate = plano.dataPlano.toDate ? plano.dataPlano.toDate() : new Date(plano.dataPlano);
          return planoDate.toDateString() === cellDate.toDateString();
        });

        dayDiv.innerHTML = `
          <div class="day-number">${cellDate.getDate()}</div>
          <div class="day-events">
            ${dayEvents.map(evento => `
              <div class="event-item event-${evento.tipo.toLowerCase()}" title="${evento.descricao || ''}">
                ${evento.equipamentoNome || 'Manutenção'}
              </div>
            `).join('')}
          </div>
        `;

        dayDiv.onclick = () => selectDate(cellDate);
        calendarDays.appendChild(dayDiv);
      }
    }

    function updateResourcePlanning() {
      const funcionariosDiv = document.getElementById('funcionariosList');
      funcionariosDiv.innerHTML = funcionariosList.map(func => `
        <div class="resource-item" onclick="selectResource('${func.id}')">
          <strong>${func.nome}</strong><br>
          <small>${func.cargo || 'N/A'}</small>
        </div>
      `).join('');

      // Update timeline
      const timelineDiv = document.getElementById('timelineSlots');
      timelineDiv.innerHTML = '';
      
      for (let hour = 0; hour < 24; hour++) {
        const slot = document.createElement('div');
        slot.className = 'timeline-slot';
        slot.style.gridColumn = `${hour + 1}`;
        slot.textContent = `${hour}:00`;
        timelineDiv.appendChild(slot);
      }
    }

    function updateGanttChart() {
      // Generate dates for next 30 days
      const datesDiv = document.getElementById('ganttDates');
      const rowsDiv = document.getElementById('ganttRows');
      
      datesDiv.innerHTML = '';
      rowsDiv.innerHTML = '';

      const today = new Date();
      for (let i = 0; i < 30; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        
        const dateDiv = document.createElement('div');
        dateDiv.className = 'gantt-date';
        dateDiv.textContent = date.getDate();
        datesDiv.appendChild(dateDiv);
      }

      // Add gantt rows for orders
      ordensList.filter(ordem => ordem.status !== 'CONCLUIDA').forEach(ordem => {
        const rowDiv = document.createElement('div');
        rowDiv.className = 'gantt-row';
        
        rowDiv.innerHTML = `
          <div class="gantt-task-name">${ordem.codigo}</div>
          <div class="gantt-timeline">
            <!-- Task bars will be positioned here -->
          </div>
        `;
        
        rowsDiv.appendChild(rowDiv);
      });
    }

    function loadSelectOptions() {
      // Load equipment options
      const equipSelect = document.getElementById('planoEquipamento');
      equipSelect.innerHTML = '<option value="">Selecione...</option>' +
        equipamentosList.map(equip => 
          `<option value="${equip.id}">${equip.codigo} - ${equip.maquina}</option>`
        ).join('');

      // Load responsible options
      const respSelect = document.getElementById('planoResponsavel');
      respSelect.innerHTML = '<option value="">Selecione...</option>' +
        funcionariosList.filter(func => func.status === 'ATIVO').map(func => 
          `<option value="${func.id}">${func.nome}</option>`
        ).join('');
    }

    window.showTab = function(tabName) {
      // Hide all tabs
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
      });
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });

      // Show selected tab
      document.getElementById(tabName).classList.add('active');
      event.target.classList.add('active');
    };

    window.changeMonth = function(direction) {
      currentDate.setMonth(currentDate.getMonth() + direction);
      updateCalendar();
    };

    window.goToToday = function() {
      currentDate = new Date();
      updateCalendar();
    };

    window.selectDate = function(date) {
      // Open new plan modal with selected date
      document.getElementById('planoData').value = date.toISOString().split('T')[0];
      novoPlano();
    };

    window.selectResource = function(resourceId) {
      document.querySelectorAll('.resource-item').forEach(item => {
        item.classList.remove('selected');
      });
      event.target.classList.add('selected');
    };

    window.novoPlano = function() {
      document.getElementById('novoPlanoModal').style.display = 'block';
    };

    window.fecharModal = function() {
      document.getElementById('novoPlanoModal').style.display = 'none';
      document.getElementById('novoPlanoForm').reset();
    };

    window.gerarRelatorio = function() {
      const tipo = document.getElementById('relatorioTipo').value;
      const inicio = document.getElementById('periodoInicio').value;
      const fim = document.getElementById('periodoFim').value;

      if (!inicio || !fim) {
        showNotification('Selecione o período para o relatório', 'error');
        return;
      }

      // Generate sample report
      const resultDiv = document.getElementById('relatorioResultado');
      resultDiv.innerHTML = `
        <div style="background: white; border: 1px solid var(--border-color); border-radius: 8px; padding: 20px;">
          <h4>Relatório de ${tipo.charAt(0).toUpperCase() + tipo.slice(1)}</h4>
          <p>Período: ${new Date(inicio).toLocaleDateString('pt-BR')} - ${new Date(fim).toLocaleDateString('pt-BR')}</p>
          <div style="margin-top: 20px;">
            <p>📊 Total de ordens planejadas: ${planosList.length}</p>
            <p>🔧 Funcionários ativos: ${funcionariosList.filter(f => f.status === 'ATIVO').length}</p>
            <p>⚙️ Equipamentos cadastrados: ${equipamentosList.length}</p>
          </div>
        </div>
      `;
    };

    async function salvarPlano(event) {
      event.preventDefault();

      try {
        const formData = new FormData(event.target);
        
        const planoData = {
          equipamentoId: document.getElementById('planoEquipamento').value,
          equipamentoNome: document.getElementById('planoEquipamento').selectedOptions[0].text,
          tipo: document.getElementById('planoTipo').value,
          dataPlano: new Date(document.getElementById('planoData').value),
          responsavelId: document.getElementById('planoResponsavel').value,
          duracaoEstimada: parseFloat(document.getElementById('planoDuracao').value),
          prioridade: document.getElementById('planoPrioridade').value,
          descricao: document.getElementById('planoDescricao').value,
          status: 'PLANEJADO',
          dataCriacao: serverTimestamp()
        };

        await addDoc(collection(db, "planosManutencao"), planoData);
        showNotification("Plano criado com sucesso!", "success");
        fecharModal();
        loadData();
      } catch (error) {
        console.error("Erro ao salvar plano:", error);
        showNotification("Erro ao salvar plano", "error");
      }
    }

    function showNotification(message, type) {
      const notification = document.getElementById('notification');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      notification.style.display = 'block';

      setTimeout(() => {
        notification.style.display = 'none';
      }, 3000);
    }

    document.addEventListener('DOMContentLoaded', () => {
      loadData();
      
      // Set default dates
      const today = new Date();
      document.getElementById('periodoInicio').value = today.toISOString().split('T')[0];
      const nextMonth = new Date(today);
      nextMonth.setMonth(today.getMonth() + 1);
      document.getElementById('periodoFim').value = nextMonth.toISOString().split('T')[0];

      document.getElementById('novoPlanoForm').addEventListener('submit', salvarPlano);

      // Close modal when clicking outside
      window.onclick = function(event) {
        const modal = document.getElementById('novoPlanoModal');
        if (event.target === modal) {
          fecharModal();
        }
      };
    });
  </script>
</body>
</html> 