<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PLAZHA - SISTEMAS - Transportadoras</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    /* Reusing the same style from orcamentos.html */
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      font-size: 13px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .sap-header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sap-logo {
      font-weight: bold;
      font-size: 24px;
    }

    .sap-title {
      color: var(--primary-color);
      font-size: 20px;
      margin: 0 0 20px 0;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
    }

    .totvs-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-top: 15px;
      font-size: 12px;
    }

    .totvs-table th {
      background-color: var(--secondary-color);
      color: var(--primary-color);
      font-weight: 600;
      padding: 8px 5px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .totvs-table td {
      padding: 6px 5px;
      border: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .totvs-form {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-group {
      margin-bottom: 10px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }

    .totvs-input, .totvs-select {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid var(--border-color);
      border-radius: 3px;
      font-size: 13px;
    }

    .btn-totvs {
      padding: 8px 15px;
      border: 1px solid var(--primary-color);
      border-radius: 3px;
      background-color: white;
      color: var(--primary-color);
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-totvs-primary {
      background-color: var(--primary-color);
      color: white;
      border: none;
    }

    .btn-totvs-danger {
      background-color: var(--danger-color);
      color: white;
      border: none;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: white;
      margin: 2% auto;
      padding: 20px;
      width: 90%;
      max-width: 800px;
      border-radius: 4px;
      position: relative;
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .status-active {
      background-color: #d4edda;
      color: #155724;
      padding: 3px 6px;
      border-radius: 3px;
      font-size: 11px;
    }

    .status-inactive {
      background-color: #f8d7da;
      color: #721c24;
      padding: 3px 6px;
      border-radius: 3px;
      font-size: 11px;
    }
  </style>
</head>
<body>
  <div class="sap-header">
    <div class="sap-logo">PLAZHA - SISTEMAS</div>
    <div id="authContainer">Usuário: <span id="userStatus">Não logado</span> | Empresa: 1000</div>
  </div>

  <div class="container">
    <div class="sap-title">
      <i class="fas fa-truck"></i> Cadastro de Transportadoras
    </div>

    <div class="totvs-form">
      <h2>Dados da Transportadora</h2>
      <form id="transportadoraForm" onsubmit="handleTransportadora(event)">
        <div class="form-row">
          <div class="form-group">
            <label>Código</label>
            <input type="text" id="codigo" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Razão Social</label>
            <input type="text" id="razaoSocial" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Nome Fantasia</label>
            <input type="text" id="nomeFantasia" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>CNPJ</label>
            <input type="text" id="cnpj" class="totvs-input" required pattern="\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2}">
          </div>
          <div class="form-group">
            <label>Inscrição Estadual</label>
            <input type="text" id="inscricaoEstadual" class="totvs-input">
          </div>
        </div>

        <h3>Endereço</h3>
        <div class="form-row">
          <div class="form-group">
            <label>CEP</label>
            <input type="text" id="cep" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Logradouro</label>
            <input type="text" id="logradouro" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Número</label>
            <input type="text" id="numero" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Complemento</label>
            <input type="text" id="complemento" class="totvs-input">
          </div>
          <div class="form-group">
            <label>Bairro</label>
            <input type="text" id="bairro" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Cidade</label>
            <input type="text" id="cidade" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>UF</label>
            <select id="uf" class="totvs-select" required>
              <option value="">Selecione...</option>
              <option value="AC">AC</option>
              <option value="AL">AL</option>
              <option value="AP">AP</option>
              <option value="AM">AM</option>
              <option value="BA">BA</option>
              <option value="CE">CE</option>
              <option value="DF">DF</option>
              <option value="ES">ES</option>
              <option value="GO">GO</option>
              <option value="MA">MA</option>
              <option value="MT">MT</option>
              <option value="MS">MS</option>
              <option value="MG">MG</option>
              <option value="PA">PA</option>
              <option value="PB">PB</option>
              <option value="PR">PR</option>
              <option value="PE">PE</option>
              <option value="PI">PI</option>
              <option value="RJ">RJ</option>
              <option value="RN">RN</option>
              <option value="RS">RS</option>
              <option value="RO">RO</option>
              <option value="RR">RR</option>
              <option value="SC">SC</option>
              <option value="SP">SP</option>
              <option value="SE">SE</option>
              <option value="TO">TO</option>
            </select>
          </div>
        </div>

        <h3>Contato</h3>
        <div class="form-row">
          <div class="form-group">
            <label>Telefone</label>
            <input type="tel" id="telefone" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Email</label>
            <input type="email" id="email" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Contato Principal</label>
            <input type="text" id="contatoPrincipal" class="totvs-input" required>
          </div>
        </div>

        <h3>Informações de Serviço</h3>
        <div class="form-row">
          <div class="form-group">
            <label>Regiões Atendidas</label>
            <select id="regioesAtendidas" class="totvs-select" multiple required>
              <option value="Norte">Norte</option>
              <option value="Nordeste">Nordeste</option>
              <option value="Centro-Oeste">Centro-Oeste</option>
              <option value="Sudeste">Sudeste</option>
              <option value="Sul">Sul</option>
            </select>
          </div>
          <div class="form-group">
            <label>Prazo Médio de Entrega (dias)</label>
            <input type="number" id="prazoMedioEntrega" class="totvs-input" required min="1">
          </div>
          <div class="form-group">
            <label>Tipos de Veículos</label>
            <select id="tiposVeiculos" class="totvs-select" multiple required>
              <option value="VUC">VUC</option>
              <option value="3/4">3/4</option>
              <option value="Toco">Toco</option>
              <option value="Truck">Truck</option>
              <option value="Carreta">Carreta</option>
              <option value="Bitrem">Bitrem</option>
            </select>
          </div>
        </div>

        <h3>Informações de Frete</h3>
        <div class="form-row">
          <div class="form-group">
            <label>Valor Base (R$)</label>
            <input type="number" id="valorBase" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor por KG (R$)</label>
            <input type="number" id="valorPorKg" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor por M³ (R$)</label>
            <input type="number" id="valorPorM3" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor por KM (R$)</label>
            <input type="number" id="valorPorKm" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor Mínimo (R$)</label>
            <input type="number" id="valorMinimo" class="totvs-input" required min="0" step="0.01">
          </div>
        </div>

        <div class="form-group">
          <label>Status</label>
          <select id="status" class="totvs-select" required>
            <option value="ativo">Ativo</option>
            <option value="inativo">Inativo</option>
          </select>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-totvs btn-totvs-primary">
            <i class="fas fa-save"></i> Salvar
          </button>
          <button type="button" class="btn-totvs" onclick="window.location.href='index.html'">
            <i class="fas fa-arrow-left"></i> Voltar
          </button>
        </div>
      </form>
    </div>

    <table class="totvs-table">
      <thead>
        <tr>
          <th>Código</th>
          <th>Razão Social</th>
          <th>CNPJ</th>
          <th>Cidade/UF</th>
          <th>Regiões Atendidas</th>
          <th>Prazo Médio</th>
          <th>Status</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="transportadoraTableBody"></tbody>
    </table>

    <div style="margin-top: 20px;">
      <button class="btn-totvs btn-totvs-primary" onclick="openCarrierModal()">
        <i class="fas fa-plus"></i> Nova Transportadora
      </button>
      <button class="btn-totvs" onclick="window.location.href='home.html'">
        <i class="fas fa-arrow-left"></i> Voltar
      </button>
    </div>
  </div>

  <!-- Modal de Transportadora -->
  <div id="carrierModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal()">&times;</span>
      <h2 id="modalTitle" class="sap-title">Nova Transportadora</h2>
      
      <form id="carrierForm" onsubmit="handleCarrier(event)">
        <div class="form-row">
          <div class="form-group">
            <label>Código</label>
            <input type="text" id="codigo" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Razão Social</label>
            <input type="text" id="razaoSocial" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Nome Fantasia</label>
            <input type="text" id="nomeFantasia" class="totvs-input" required>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>CNPJ</label>
            <input type="text" id="cnpj" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Inscrição Estadual</label>
            <input type="text" id="ie" class="totvs-input">
          </div>
          <div class="form-group">
            <label>Status</label>
            <select id="status" class="totvs-select" required>
              <option value="true">Ativo</option>
              <option value="false">Inativo</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Telefone</label>
            <input type="tel" id="telefone" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Email</label>
            <input type="email" id="email" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Site</label>
            <input type="url" id="site" class="totvs-input">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>CEP</label>
            <input type="text" id="cep" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Endereço</label>
            <input type="text" id="endereco" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Número</label>
            <input type="text" id="numero" class="totvs-input" required>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Complemento</label>
            <input type="text" id="complemento" class="totvs-input">
          </div>
          <div class="form-group">
            <label>Bairro</label>
            <input type="text" id="bairro" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Cidade</label>
            <input type="text" id="cidade" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>UF</label>
            <select id="uf" class="totvs-select" required>
              <option value="">Selecione...</option>
              <option value="AC">AC</option>
              <option value="AL">AL</option>
              <option value="AP">AP</option>
              <option value="AM">AM</option>
              <option value="BA">BA</option>
              <option value="CE">CE</option>
              <option value="DF">DF</option>
              <option value="ES">ES</option>
              <option value="GO">GO</option>
              <option value="MA">MA</option>
              <option value="MT">MT</option>
              <option value="MS">MS</option>
              <option value="MG">MG</option>
              <option value="PA">PA</option>
              <option value="PB">PB</option>
              <option value="PR">PR</option>
              <option value="PE">PE</option>
              <option value="PI">PI</option>
              <option value="RJ">RJ</option>
              <option value="RN">RN</option>
              <option value="RS">RS</option>
              <option value="RO">RO</option>
              <option value="RR">RR</option>
              <option value="SC">SC</option>
              <option value="SP">SP</option>
              <option value="SE">SE</option>
              <option value="TO">TO</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Regiões Atendidas</label>
            <select id="regioes" class="totvs-select" multiple required>
              <option value="Norte">Norte</option>
              <option value="Nordeste">Nordeste</option>
              <option value="Centro-Oeste">Centro-Oeste</option>
              <option value="Sudeste">Sudeste</option>
              <option value="Sul">Sul</option>
            </select>
          </div>
          <div class="form-group">
            <label>Prazo Médio de Entrega (dias)</label>
            <input type="number" id="prazoMedio" class="totvs-input" required min="1">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Valor Base Frete (R$)</label>
            <input type="number" id="valorBase" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor por KG (R$)</label>
            <input type="number" id="valorKg" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor por M³ (R$)</label>
            <input type="number" id="valorM3" class="totvs-input" required min="0" step="0.01">
          </div>
          <div class="form-group">
            <label>Valor por KM (R$)</label>
            <input type="number" id="valorKm" class="totvs-input" required min="0" step="0.01">
          </div>
        </div>

        <div class="form-group">
          <label>Observações</label>
          <textarea id="observacoes" class="totvs-input" rows="3"></textarea>
        </div>

        <div style="margin-top: 20px; text-align: right;">
          <button type="submit" class="btn-totvs btn-totvs-primary">
            <i class="fas fa-save"></i> Salvar
          </button>
          <button type="button" class="btn-totvs" onclick="closeModal()">
            <i class="fas fa-times"></i> Cancelar
          </button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      getDoc,
      doc,
      updateDoc,
      deleteDoc,
      query,
      where,
      onSnapshot 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
    import Swal from 'https://cdn.jsdelivr.net/npm/sweetalert2@11/+esm';

    let transportadoras = [];
    let usuarioAtual = null;
    let editingId = null;

    window.onload = async function() {
      usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
      if (!usuarioAtual) {
        window.location.href = 'login.html';
        return;
      }

      document.getElementById('userStatus').textContent = usuarioAtual.nome;
      
      // Listener para mudanças em tempo real
      onSnapshot(collection(db, "transportadoras"), (snapshot) => {
        transportadoras = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        updateTable();
      });

      // Setup dos listeners de filtro
      document.getElementById('searchInput').addEventListener('input', updateTable);
      document.getElementById('statusFilter').addEventListener('change', updateTable);
    };

    function updateTable() {
      const tableBody = document.getElementById('transportadoraTableBody');
      const searchText = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;

      const filteredCarriers = transportadoras.filter(carrier => {
        const matchesSearch = 
          carrier.codigo?.toLowerCase().includes(searchText) ||
          carrier.razaoSocial?.toLowerCase().includes(searchText) ||
          carrier.nomeFantasia?.toLowerCase().includes(searchText) ||
          carrier.cnpj?.toLowerCase().includes(searchText);

        const matchesStatus = !statusFilter || carrier.ativo.toString() === statusFilter;

        return matchesSearch && matchesStatus;
      });

      tableBody.innerHTML = '';
      
      filteredCarriers.forEach(carrier => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${carrier.codigo}</td>
          <td>${carrier.razaoSocial}</td>
          <td>${carrier.cnpj}</td>
          <td>${carrier.cidade}/${carrier.uf}</td>
          <td>${carrier.regioesAtendidas?.join(', ') || '-'}</td>
          <td>${carrier.prazoMedioEntrega} dias</td>
          <td>
            <span class="status-${carrier.ativo ? 'active' : 'inactive'}">
              ${carrier.ativo ? 'Ativo' : 'Inativo'}
            </span>
          </td>
          <td>
            <button class="btn-totvs" onclick="editCarrier('${carrier.id}')">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn-totvs btn-totvs-danger" onclick="deleteCarrier('${carrier.id}')">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.openCarrierModal = function() {
      document.getElementById('modalTitle').textContent = 'Nova Transportadora';
      document.getElementById('carrierForm').reset();
      document.getElementById('carrierModal').style.display = 'block';
      editingId = null;
    };

    window.closeModal = function() {
      document.getElementById('carrierModal').style.display = 'none';
      document.getElementById('carrierForm').reset();
      editingId = null;
    };

    window.editCarrier = async function(id) {
      const carrier = transportadoras.find(c => c.id === id);
      if (!carrier) return;

      editingId = id;
      document.getElementById('modalTitle').textContent = 'Editar Transportadora';
      
      // Preenche o formulário com os dados da transportadora
      Object.keys(carrier).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
          if (key === 'regioesAtendidas' && Array.isArray(carrier[key])) {
            Array.from(element.options).forEach(option => {
              option.selected = carrier[key].includes(option.value);
            });
          } else {
            element.value = carrier[key];
          }
        }
      });

      document.getElementById('carrierModal').style.display = 'block';
    };

    window.deleteCarrier = async function(id) {
      const result = await Swal.fire({
        title: 'Confirmar exclusão',
        text: 'Deseja realmente excluir esta transportadora?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, excluir',
        cancelButtonText: 'Cancelar'
      });

      if (result.isConfirmed) {
        try {
          await deleteDoc(doc(db, "transportadoras", id));
          await Swal.fire('Sucesso', 'Transportadora excluída com sucesso!', 'success');
        } catch (error) {
          console.error("Erro ao excluir:", error);
          await Swal.fire('Erro', 'Erro ao excluir transportadora', 'error');
        }
      }
    };

    window.handleCarrier = async function(event) {
      event.preventDefault();

      const formData = {
        codigo: document.getElementById('codigo').value,
        razaoSocial: document.getElementById('razaoSocial').value,
        nomeFantasia: document.getElementById('nomeFantasia').value,
        cnpj: document.getElementById('cnpj').value,
        ie: document.getElementById('ie').value,
        ativo: document.getElementById('status').value === 'true',
        telefone: document.getElementById('telefone').value,
        email: document.getElementById('email').value,
        site: document.getElementById('site').value,
        cep: document.getElementById('cep').value,
        endereco: document.getElementById('endereco').value,
        numero: document.getElementById('numero').value,
        complemento: document.getElementById('complemento').value,
        bairro: document.getElementById('bairro').value,
        cidade: document.getElementById('cidade').value,
        uf: document.getElementById('uf').value,
        regioesAtendidas: Array.from(document.getElementById('regioes').selectedOptions).map(option => option.value),
        prazoMedioEntrega: parseInt(document.getElementById('prazoMedio').value),
        tiposVeiculos: Array.from(document.getElementById('tiposVeiculos').selectedOptions).map(option => option.value),
        valorBase: parseFloat(document.getElementById('valorBase').value),
        valorKg: parseFloat(document.getElementById('valorKg').value),
        valorM3: parseFloat(document.getElementById('valorM3').value),
        valorKm: parseFloat(document.getElementById('valorKm').value),
        valorMinimo: parseFloat(document.getElementById('valorMinimo').value),
        status: document.getElementById('status').value,
        dataCadastro: new Date(),
        usuarioCadastro: {
          id: usuarioAtual.id,
          nome: usuarioAtual.nome
        }
      };

      try {
        if (editingId) {
          await updateDoc(doc(db, "transportadoras", editingId), formData);
          await Swal.fire('Sucesso', 'Transportadora atualizada com sucesso!', 'success');
        } else {
          await addDoc(collection(db, "transportadoras"), formData);
          await Swal.fire('Sucesso', 'Transportadora cadastrada com sucesso!', 'success');
        }
        closeModal();
      } catch (error) {
        console.error("Erro ao salvar:", error);
        await Swal.fire('Erro', 'Erro ao salvar transportadora', 'error');
      }
    };
  </script>
</body>
</html> 