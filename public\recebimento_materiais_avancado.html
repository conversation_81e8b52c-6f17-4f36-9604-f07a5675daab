<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recebimento de Materiais - Sistema Avançado</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        /* Alert Styles */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Form Styles */
        .form-section {
            background: var(--light-bg);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid var(--secondary-color);
        }

        .form-section h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-control {
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-control.error {
            border-color: var(--danger-color);
            background: #fff5f5;
        }

        .form-control.warning {
            border-color: var(--warning-color);
            background: #fffbf0;
        }

        /* Supplier Info Card */
        .supplier-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .supplier-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .supplier-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .supplier-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .supplier-status.active {
            background: #d4edda;
            color: #155724;
        }

        .supplier-status.delayed {
            background: #f8d7da;
            color: #721c24;
        }

        .supplier-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            font-size: 0.9rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .detail-value {
            color: var(--primary-color);
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, var(--primary-color) 100%);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            font-size: 0.9rem;
        }

        .table tbody tr:hover {
            background: var(--light-bg);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Status Badges */
        .status-badge {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.quality {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-badge.stock {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-badge.delayed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.ok {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }

        /* Price Validation */
        .price-validation {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.8rem;
        }

        .price-original {
            color: #6c757d;
            text-decoration: line-through;
        }

        .price-current {
            font-weight: bold;
        }

        .price-current.ok {
            color: var(--success-color);
        }

        .price-current.warning {
            color: var(--warning-color);
        }

        .price-current.error {
            color: var(--danger-color);
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.8rem;
        }

        /* Delivery History */
        .delivery-history {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .delivery-history h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .delivery-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
        }

        .delivery-item:last-child {
            border-bottom: none;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transform: translateX(400px);
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .supplier-details {
                grid-template-columns: 1fr;
            }
            
            .table {
                font-size: 0.8rem;
            }
            
            .table th,
            .table td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-truck-loading"></i> Recebimento de Materiais - Sistema Avançado</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Alertas do Sistema -->
            <div id="systemAlerts"></div>

            <!-- Seleção do Pedido -->
            <div class="form-section">
                <h3><i class="fas fa-search"></i> Selecionar Pedido de Compra</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label>Pedido de Compra</label>
                        <select class="form-control" id="orderSelect" onchange="loadOrderDetails()">
                            <option value="">Selecione o pedido...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Buscar por Número/Fornecedor</label>
                        <input type="text" class="form-control" id="orderSearch" placeholder="Digite para buscar..." onkeyup="filterOrders()">
                    </div>
                </div>
            </div>

            <!-- Informações do Fornecedor -->
            <div id="supplierSection" style="display: none;">
                <div class="supplier-card">
                    <div class="supplier-header">
                        <div class="supplier-name" id="supplierName"></div>
                        <div class="supplier-status" id="supplierStatus"></div>
                    </div>
                    <div class="supplier-details">
                        <div class="detail-item">
                            <div class="detail-label">CNPJ</div>
                            <div class="detail-value" id="supplierCnpj"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Contato</div>
                            <div class="detail-value" id="supplierContact"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Solicitante</div>
                            <div class="detail-value" id="orderRequester"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Data do Pedido</div>
                            <div class="detail-value" id="orderDate"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Entrega Prevista</div>
                            <div class="detail-value" id="deliveryDate"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Status do Prazo</div>
                            <div class="detail-value" id="deliveryStatus"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dados do Recebimento -->
            <div id="receiptSection" class="form-section" style="display: none;">
                <h3><i class="fas fa-clipboard-list"></i> Dados do Recebimento</h3>

                <!-- Alerta de Entrega Parcial -->
                <div id="partialDeliveryAlert" class="alert alert-info" style="display: none;">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Entrega Parcial Detectada:</strong>
                        <span id="partialDeliveryMessage"></span>
                        <br><small>Cada nota fiscal deve ser processada separadamente, mesmo para o mesmo pedido.</small>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Número da Nota Fiscal</label>
                        <input type="text" class="form-control" id="invoiceNumber" required>
                    </div>
                    <div class="form-group">
                        <label>Data da Nota Fiscal</label>
                        <input type="date" class="form-control" id="invoiceDate" required>
                    </div>
                    <div class="form-group">
                        <label>Valor Total da NF</label>
                        <input type="number" class="form-control" id="invoiceValue" step="0.01" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Armazém de Destino</label>
                        <select class="form-control" id="warehouseSelect" required>
                            <option value="">Selecione o armazém...</option>
                        </select>
                        <small id="warehouseHint" style="color: #6c757d; margin-top: 5px;"></small>
                    </div>
                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" id="observations" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- Resumo do Pedido -->
            <div id="orderSummary" class="form-section" style="display: none;">
                <h3><i class="fas fa-chart-pie"></i> Resumo do Pedido</h3>
                <div class="supplier-details">
                    <div class="detail-item">
                        <div class="detail-label">Total de Itens</div>
                        <div class="detail-value" id="summaryTotalItems">0</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Valor Total do Pedido</div>
                        <div class="detail-value" id="summaryTotalValue">R$ 0,00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Valor Já Recebido</div>
                        <div class="detail-value" id="summaryReceivedValue">R$ 0,00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Valor Pendente</div>
                        <div class="detail-value" id="summaryPendingValue">R$ 0,00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Progresso</div>
                        <div class="detail-value" id="summaryProgress">0%</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Status do Pedido</div>
                        <div class="detail-value" id="summaryStatus">PENDENTE</div>
                    </div>
                </div>
            </div>

            <!-- Itens do Pedido -->
            <div id="itemsSection" style="display: none;">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Qtd. Pedida</th>
                                <th>Qtd. Recebida</th>
                                <th>Saldo Pendente</th>
                                <th>Qtd. a Receber</th>
                                <th>Preço Unitário</th>
                                <th>Preço NF</th>
                                <th>Destino</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <!-- Itens serão carregados dinamicamente -->
                        </tbody>
                    </table>
                </div>

                <!-- Histórico de Entregas -->
                <div id="deliveryHistory" class="delivery-history" style="display: none;">
                    <h4><i class="fas fa-history"></i> Histórico de Entregas Parciais</h4>
                    <div id="deliveryHistoryContent"></div>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <button class="btn btn-success" onclick="processReceipt()">
                        <i class="fas fa-check"></i> Processar Recebimento
                    </button>
                    <button class="btn btn-warning" onclick="requestAuthorization()" id="authButton" style="display: none;">
                        <i class="fas fa-key"></i> Solicitar Autorização
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <!-- Scripts -->
    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            getDoc,
            Timestamp,
            query,
            where,
            orderBy
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Variáveis globais
        let systemConfig = {};
        let pedidosCompra = [];
        let produtos = [];
        let armazens = [];
        let fornecedores = [];
        let currentOrder = null;
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Usuário' };

        // ===== INICIALIZAÇÃO =====
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                showNotification('Carregando sistema avançado...', 'info');
                await loadSystemConfiguration();
                await loadInitialData();
                setupInterface();
                showNotification('Sistema carregado com sucesso!', 'success');
            } catch (error) {
                console.error('Erro na inicialização:', error);
                showNotification('Erro ao carregar sistema: ' + error.message, 'error');
            }
        });

        // ===== CARREGAMENTO DE CONFIGURAÇÕES =====
        async function loadSystemConfiguration() {
            try {
                const configDoc = await getDoc(doc(db, "parametros", "sistema"));

                if (configDoc.exists()) {
                    const data = configDoc.data();
                    systemConfig = {
                        // Configurações de qualidade
                        controleQualidade: data.configuracaoSistema?.controleQualidade || false,
                        armazemQualidade: data.configuracaoSistema?.armazemQualidade || false,
                        inspecaoRecebimento: data.configuracaoSistema?.inspecaoRecebimento || 'manual',
                        controleQualidadeObrigatorio: data.controleQualidadeObrigatorio || false,

                        // Configurações de estoque
                        armazemPadrao: data.armazemPadrao || '',
                        permitirEstoqueNegativo: data.permitirEstoqueNegativo || false,
                        toleranciaRecebimento: data.toleranciaRecebimento || 10,
                        toleranciaPreco: data.toleranciaPreco || 5,
                        bloquearPrecoDivergente: data.bloquearPrecoDivergente || false,
                        permitirRecebimentoParcial: data.permitirRecebimentoParcial || true,
                        diasAlerteAtraso: data.diasAlerteAtraso || 3,
                        exigirAutorizacaoExcesso: data.exigirAutorizacaoExcesso || false,
                        controlarHistoricoEntregas: data.controlarHistoricoEntregas || true,

                        // Outras configurações
                        homologacaoFornecedor: data.configuracaoSistema?.homologacaoFornecedor || false
                    };
                } else {
                    // Configurações padrão
                    systemConfig = {
                        controleQualidade: false,
                        armazemQualidade: false,
                        inspecaoRecebimento: 'manual',
                        controleQualidadeObrigatorio: false,
                        armazemPadrao: '',
                        permitirEstoqueNegativo: false,
                        toleranciaRecebimento: 10,
                        toleranciaPreco: 5,
                        bloquearPrecoDivergente: false,
                        permitirRecebimentoParcial: true,
                        diasAlerteAtraso: 3,
                        exigirAutorizacaoExcesso: false,
                        controlarHistoricoEntregas: true,
                        homologacaoFornecedor: false
                    };
                }

                console.log('Configurações carregadas:', systemConfig);
                updateSystemAlerts();

            } catch (error) {
                console.error('Erro ao carregar configurações:', error);
                throw error;
            }
        }

        function updateSystemAlerts() {
            const alertsContainer = document.getElementById('systemAlerts');
            let alerts = [];

            // Alerta sobre controle de qualidade
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                alerts.push({
                    type: 'warning',
                    icon: 'fas fa-clipboard-check',
                    message: `<strong>Controle de Qualidade Ativo:</strong> Materiais críticos serão direcionados para inspeção. Tipo: ${getInspectionTypeText(systemConfig.inspecaoRecebimento)}`
                });
            } else {
                alerts.push({
                    type: 'info',
                    icon: 'fas fa-warehouse',
                    message: `<strong>Entrada Direta:</strong> Materiais serão direcionados diretamente para o estoque após recebimento.`
                });
            }

            // Alerta sobre tolerâncias
            alerts.push({
                type: 'info',
                icon: 'fas fa-percentage',
                message: `<strong>Tolerâncias Configuradas:</strong> Quantidade: ${systemConfig.toleranciaRecebimento}% | Preço: ${systemConfig.toleranciaPreco}%`
            });

            // Alerta sobre recebimento parcial
            if (!systemConfig.permitirRecebimentoParcial) {
                alerts.push({
                    type: 'warning',
                    icon: 'fas fa-ban',
                    message: `<strong>Recebimento Parcial Bloqueado:</strong> Só será permitido receber a quantidade total do pedido.`
                });
            }

            alertsContainer.innerHTML = alerts.map(alert => `
                <div class="alert alert-${alert.type}">
                    <i class="${alert.icon}"></i>
                    <span>${alert.message}</span>
                </div>
            `).join('');
        }

        function getInspectionTypeText(type) {
            const types = {
                'todos': 'Todos os materiais',
                'criticos': 'Apenas materiais críticos',
                'manual': 'Definição manual por produto'
            };
            return types[type] || 'Não definido';
        }

        // ===== CARREGAMENTO DE DADOS =====
        async function loadInitialData() {
            try {
                const [pedidosSnap, produtosSnap, armazensSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                pedidosCompra = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', {
                    pedidos: pedidosCompra.length,
                    produtos: produtos.length,
                    armazens: armazens.length,
                    fornecedores: fornecedores.length
                });

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                throw error;
            }
        }

        function setupInterface() {
            populateOrderSelect();
            populateWarehouseSelect();
        }

        function populateOrderSelect() {
            const select = document.getElementById('orderSelect');
            select.innerHTML = '<option value="">Selecione o pedido...</option>';

            // Filtrar pedidos aprovados que ainda têm itens pendentes
            const availableOrders = pedidosCompra.filter(p =>
                p.status === 'APROVADO' &&
                p.itens &&
                p.itens.some(item => (item.quantidadeRecebida || 0) < item.quantidade)
            );

            // Ordenar por prioridade (atrasados primeiro, depois por data)
            availableOrders.sort((a, b) => {
                const aDelayed = isOrderDelayed(a);
                const bDelayed = isOrderDelayed(b);

                if (aDelayed && !bDelayed) return -1;
                if (!aDelayed && bDelayed) return 1;

                return (b.numero || 0) - (a.numero || 0);
            });

            availableOrders.forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const isDelayed = isOrderDelayed(pedido);

                const option = document.createElement('option');
                option.value = pedido.id;
                option.textContent = `${isDelayed ? '⚠️ ' : ''}PC-${pedido.numero || 'N/A'} - ${fornecedor?.razaoSocial || pedido.fornecedorNome || 'Fornecedor N/A'}`;
                option.style.color = isDelayed ? '#e74c3c' : '';
                option.style.fontWeight = isDelayed ? 'bold' : '';
                select.appendChild(option);
            });
        }

        function isOrderDelayed(order) {
            if (!order.dataEntregaPrevista) return false;

            const today = new Date();
            let deliveryDate;

            // Tratar diferentes formatos de data
            if (order.dataEntregaPrevista.toDate) {
                deliveryDate = order.dataEntregaPrevista.toDate();
            } else if (order.dataEntregaPrevista.seconds) {
                deliveryDate = new Date(order.dataEntregaPrevista.seconds * 1000);
            } else if (typeof order.dataEntregaPrevista === 'string') {
                deliveryDate = new Date(order.dataEntregaPrevista);
            } else {
                deliveryDate = new Date(order.dataEntregaPrevista);
            }

            const diffDays = Math.ceil((today - deliveryDate) / (1000 * 60 * 60 * 24));
            return diffDays >= (systemConfig.diasAlerteAtraso || 3);
        }

        function populateWarehouseSelect() {
            const select = document.getElementById('warehouseSelect');
            const hint = document.getElementById('warehouseHint');

            select.innerHTML = '<option value="">Selecione o armazém...</option>';

            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.descricao}`;
                select.appendChild(option);
            });

            // Definir armazém padrão baseado na configuração
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                const qualityWarehouse = armazens.find(a =>
                    a.tipo === 'QUALIDADE' ||
                    a.codigo?.toUpperCase().includes('QUALIDADE') ||
                    a.descricao?.toUpperCase().includes('QUALIDADE')
                );

                if (qualityWarehouse) {
                    select.value = qualityWarehouse.id;
                    hint.textContent = 'Armazém da qualidade selecionado automaticamente';
                    hint.style.color = '#f39c12';
                } else {
                    hint.textContent = 'Atenção: Armazém da qualidade não encontrado';
                    hint.style.color = '#e74c3c';
                }
            } else if (systemConfig.armazemPadrao) {
                const defaultWarehouse = armazens.find(a => a.id === systemConfig.armazemPadrao);
                if (defaultWarehouse) {
                    select.value = systemConfig.armazemPadrao;
                    hint.textContent = 'Armazém padrão selecionado automaticamente';
                    hint.style.color = '#27ae60';
                }
            }
        }

        // ===== FILTRO DE PEDIDOS =====
        window.filterOrders = function() {
            const searchTerm = document.getElementById('orderSearch').value.toLowerCase();
            const select = document.getElementById('orderSelect');
            const options = Array.from(select.options);

            options.forEach(option => {
                if (option.value === '') return; // Manter opção vazia

                const text = option.textContent.toLowerCase();
                option.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        };

        // ===== CARREGAMENTO DE DETALHES DO PEDIDO =====
        window.loadOrderDetails = async function() {
            const orderId = document.getElementById('orderSelect').value;

            if (!orderId) {
                document.getElementById('supplierSection').style.display = 'none';
                document.getElementById('receiptSection').style.display = 'none';
                document.getElementById('itemsSection').style.display = 'none';
                return;
            }

            try {
                currentOrder = pedidosCompra.find(p => p.id === orderId);

                if (!currentOrder) {
                    showNotification('Pedido não encontrado', 'error');
                    return;
                }

                // Carregar informações do fornecedor
                await loadSupplierInfo();

                // Mostrar seções
                document.getElementById('supplierSection').style.display = 'block';
                document.getElementById('receiptSection').style.display = 'block';
                document.getElementById('orderSummary').style.display = 'block';
                document.getElementById('itemsSection').style.display = 'block';

                // Carregar resumo e itens
                loadOrderSummary();
                loadOrderItems();
                checkPartialDelivery();

                // Carregar histórico de entregas
                if (systemConfig.controlarHistoricoEntregas) {
                    await loadDeliveryHistory();
                }

                showNotification('Pedido carregado com sucesso', 'success');

            } catch (error) {
                console.error('Erro ao carregar pedido:', error);
                showNotification('Erro ao carregar pedido: ' + error.message, 'error');
            }
        };

        async function loadSupplierInfo() {
            const fornecedor = fornecedores.find(f => f.id === currentOrder.fornecedorId);
            const isDelayed = isOrderDelayed(currentOrder);

            // Nome e status
            document.getElementById('supplierName').textContent = fornecedor?.razaoSocial || currentOrder.fornecedorNome || 'Fornecedor N/A';

            const statusElement = document.getElementById('supplierStatus');
            if (isDelayed) {
                statusElement.textContent = 'EM ATRASO';
                statusElement.className = 'supplier-status delayed';
            } else {
                statusElement.textContent = 'NO PRAZO';
                statusElement.className = 'supplier-status active';
            }

            // Detalhes
            document.getElementById('supplierCnpj').textContent = fornecedor?.cnpj || 'N/A';
            document.getElementById('supplierContact').textContent = fornecedor?.telefone || fornecedor?.email || 'N/A';
            document.getElementById('orderRequester').textContent = currentOrder.solicitante || currentOrder.criadoPor || 'N/A';

            // Datas - tratamento robusto para diferentes formatos
            let orderDate = null;
            if (currentOrder.dataCriacao) {
                if (currentOrder.dataCriacao.toDate) {
                    orderDate = currentOrder.dataCriacao.toDate();
                } else if (currentOrder.dataCriacao.seconds) {
                    orderDate = new Date(currentOrder.dataCriacao.seconds * 1000);
                } else {
                    orderDate = new Date(currentOrder.dataCriacao);
                }
            }

            let deliveryDate = null;
            if (currentOrder.dataEntregaPrevista) {
                if (currentOrder.dataEntregaPrevista.toDate) {
                    deliveryDate = currentOrder.dataEntregaPrevista.toDate();
                } else if (currentOrder.dataEntregaPrevista.seconds) {
                    deliveryDate = new Date(currentOrder.dataEntregaPrevista.seconds * 1000);
                } else {
                    deliveryDate = new Date(currentOrder.dataEntregaPrevista);
                }
            }

            document.getElementById('orderDate').textContent = orderDate ? formatDate(orderDate) : 'N/A';
            document.getElementById('deliveryDate').textContent = deliveryDate ? formatDate(deliveryDate) : 'N/A';

            // Status do prazo
            const statusPrazo = document.getElementById('deliveryStatus');
            if (isDelayed) {
                const diffDays = Math.ceil((new Date() - deliveryDate) / (1000 * 60 * 60 * 24));
                statusPrazo.innerHTML = `<span class="status-badge delayed">${diffDays} dias em atraso</span>`;
            } else {
                const diffDays = Math.ceil((deliveryDate - new Date()) / (1000 * 60 * 60 * 24));
                if (diffDays <= systemConfig.diasAlerteAtraso) {
                    statusPrazo.innerHTML = `<span class="status-badge warning">Vence em ${diffDays} dias</span>`;
                } else {
                    statusPrazo.innerHTML = `<span class="status-badge ok">No prazo</span>`;
                }
            }
        }

        function formatDate(date) {
            return date.toLocaleDateString('pt-BR');
        }

        function loadOrderSummary() {
            if (!currentOrder || !currentOrder.itens) return;

            let totalItems = currentOrder.itens.length;
            let totalValue = 0;
            let receivedValue = 0;
            let totalQuantity = 0;
            let receivedQuantity = 0;

            currentOrder.itens.forEach(item => {
                const itemTotal = item.quantidade * (item.valorUnitario || 0);
                const itemReceived = (item.quantidadeRecebida || 0) * (item.valorUnitario || 0);

                totalValue += itemTotal;
                receivedValue += itemReceived;
                totalQuantity += item.quantidade;
                receivedQuantity += (item.quantidadeRecebida || 0);
            });

            const pendingValue = totalValue - receivedValue;
            const progress = totalValue > 0 ? (receivedValue / totalValue * 100) : 0;

            // Atualizar interface
            document.getElementById('summaryTotalItems').textContent = totalItems;
            document.getElementById('summaryTotalValue').textContent = `R$ ${totalValue.toFixed(2)}`;
            document.getElementById('summaryReceivedValue').textContent = `R$ ${receivedValue.toFixed(2)}`;
            document.getElementById('summaryPendingValue').textContent = `R$ ${pendingValue.toFixed(2)}`;
            document.getElementById('summaryProgress').textContent = `${progress.toFixed(1)}%`;

            // Status do pedido
            const statusElement = document.getElementById('summaryStatus');
            if (progress === 0) {
                statusElement.innerHTML = '<span class="status-badge pending">PENDENTE</span>';
            } else if (progress < 100) {
                statusElement.innerHTML = '<span class="status-badge warning">PARCIAL</span>';
            } else {
                statusElement.innerHTML = '<span class="status-badge ok">COMPLETO</span>';
            }
        }

        function checkPartialDelivery() {
            if (!currentOrder || !currentOrder.itens) return;

            const hasPartialDelivery = currentOrder.itens.some(item =>
                (item.quantidadeRecebida || 0) > 0 &&
                (item.quantidadeRecebida || 0) < item.quantidade
            );

            const hasCompleteItems = currentOrder.itens.some(item =>
                (item.quantidadeRecebida || 0) >= item.quantidade
            );

            const alertElement = document.getElementById('partialDeliveryAlert');
            const messageElement = document.getElementById('partialDeliveryMessage');

            if (hasPartialDelivery || hasCompleteItems) {
                const totalReceived = currentOrder.itens.reduce((sum, item) =>
                    sum + (item.quantidadeRecebida || 0), 0
                );
                const totalOrdered = currentOrder.itens.reduce((sum, item) =>
                    sum + item.quantidade, 0
                );

                messageElement.innerHTML = `
                    Este pedido já teve entregas anteriores.
                    <strong>Recebido:</strong> ${totalReceived} de ${totalOrdered} itens totais.
                    <br><strong>Importante:</strong> Processe cada nota fiscal separadamente.
                `;
                alertElement.style.display = 'block';
            } else {
                alertElement.style.display = 'none';
            }
        }

        function loadOrderItems() {
            const tbody = document.getElementById('itemsTableBody');
            tbody.innerHTML = '';

            if (!currentOrder.itens) {
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center;">Nenhum item encontrado</td></tr>';
                return;
            }

            let hasExcessQuantity = false;
            let hasPriceDivergence = false;

            currentOrder.itens.forEach((item, index) => {
                const produto = produtos.find(p => p.id === item.produtoId);
                const quantidadeRecebida = item.quantidadeRecebida || 0;
                const saldoPendente = item.quantidade - quantidadeRecebida;

                if (saldoPendente <= 0 && !systemConfig.permitirRecebimentoParcial) return;

                const destinationInfo = getItemDestination(produto);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${produto?.codigo || 'N/A'}</td>
                    <td>${produto?.descricao || item.descricao || 'N/A'}</td>
                    <td>${item.quantidade}</td>
                    <td>${quantidadeRecebida}</td>
                    <td><strong>${Math.max(0, saldoPendente)}</strong></td>
                    <td>
                        <input type="number"
                               class="form-control"
                               id="qty_${index}"
                               max="${systemConfig.exigirAutorizacaoExcesso ? saldoPendente : saldoPendente * 2}"
                               min="0"
                               step="0.01"
                               value="${Math.max(0, saldoPendente)}"
                               style="width: 100px;"
                               onchange="validateQuantity(${index}, this.value)"
                               onkeyup="validateQuantity(${index}, this.value)">
                    </td>
                    <td>
                        <div class="price-validation">
                            <span class="price-original">R$ ${(item.valorUnitario || 0).toFixed(2)}</span>
                        </div>
                    </td>
                    <td>
                        <input type="number"
                               class="form-control"
                               id="price_${index}"
                               step="0.01"
                               value="${(item.valorUnitario || 0).toFixed(2)}"
                               style="width: 100px;"
                               onchange="validatePrice(${index}, this.value)"
                               onkeyup="validatePrice(${index}, this.value)">
                    </td>
                    <td>
                        <span class="status-badge ${destinationInfo.class}">
                            ${destinationInfo.text}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge pending" id="status_${index}">PENDENTE</span>
                    </td>
                `;
                tbody.appendChild(row);

                // Validar preço inicial
                validatePrice(index, item.valorUnitario || 0);
            });
        }

        window.validateQuantity = function(index, quantity) {
            const item = currentOrder.itens[index];
            const quantidadeRecebida = item.quantidadeRecebida || 0;
            const saldoPendente = item.quantidade - quantidadeRecebida;
            const qtyInput = document.getElementById(`qty_${index}`);
            const statusElement = document.getElementById(`status_${index}`);
            const authButton = document.getElementById('authButton');

            const qty = parseFloat(quantity) || 0;
            const tolerance = (saldoPendente * systemConfig.toleranciaRecebimento / 100);
            const maxAllowed = saldoPendente + tolerance;

            // Resetar classes
            qtyInput.classList.remove('error', 'warning');

            if (qty > saldoPendente) {
                const excess = qty - saldoPendente;
                const excessPercent = (excess / saldoPendente) * 100;

                if (excessPercent > systemConfig.toleranciaRecebimento) {
                    if (systemConfig.exigirAutorizacaoExcesso) {
                        qtyInput.classList.add('error');
                        statusElement.innerHTML = '<span class="status-badge delayed">EXCESSO - AUTORIZAÇÃO</span>';
                        authButton.style.display = 'inline-flex';
                        return false;
                    } else {
                        qtyInput.classList.add('warning');
                        statusElement.innerHTML = '<span class="status-badge warning">EXCESSO</span>';
                    }
                } else {
                    qtyInput.classList.add('warning');
                    statusElement.innerHTML = '<span class="status-badge warning">TOLERÂNCIA</span>';
                }
            } else if (qty < saldoPendente && !systemConfig.permitirRecebimentoParcial) {
                qtyInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge delayed">PARCIAL BLOQUEADO</span>';
                return false;
            } else {
                statusElement.innerHTML = '<span class="status-badge ok">OK</span>';
            }

            // Atualizar resumo em tempo real
            updateCurrentReceiptSummary();

            return true;
        };

        window.validatePrice = function(index, price) {
            const item = currentOrder.itens[index];
            const priceInput = document.getElementById(`price_${index}`);
            const statusElement = document.getElementById(`status_${index}`);

            const currentPrice = parseFloat(price) || 0;
            const originalPrice = item.valorUnitario || 0;
            const priceDiff = Math.abs(currentPrice - originalPrice);
            const pricePercent = originalPrice > 0 ? (priceDiff / originalPrice) * 100 : 0;

            // Resetar classes
            priceInput.classList.remove('error', 'warning');

            if (pricePercent > systemConfig.toleranciaPreco) {
                if (systemConfig.bloquearPrecoDivergente) {
                    priceInput.classList.add('error');
                    statusElement.innerHTML = '<span class="status-badge delayed">PREÇO DIVERGENTE</span>';
                    return false;
                } else {
                    priceInput.classList.add('warning');
                    statusElement.innerHTML = '<span class="status-badge warning">PREÇO ALTERADO</span>';
                }
            }

            // Atualizar visualização do preço
            const priceValidation = priceInput.parentElement.previousElementSibling.querySelector('.price-validation');
            if (priceValidation) {
                const priceCurrentElement = priceValidation.querySelector('.price-current') || document.createElement('span');
                priceCurrentElement.className = 'price-current';
                priceCurrentElement.textContent = `R$ ${currentPrice.toFixed(2)}`;

                if (pricePercent > systemConfig.toleranciaPreco) {
                    priceCurrentElement.classList.add(systemConfig.bloquearPrecoDivergente ? 'error' : 'warning');
                } else {
                    priceCurrentElement.classList.add('ok');
                }

                if (!priceValidation.querySelector('.price-current')) {
                    priceValidation.appendChild(document.createTextNode(' → '));
                    priceValidation.appendChild(priceCurrentElement);
                }
            }

            return true;
        };

        function getItemDestination(produto) {
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                const needsInspection = checkIfNeedsInspection(produto);

                if (needsInspection) {
                    return {
                        text: 'QUALIDADE',
                        class: 'quality'
                    };
                }
            }

            return {
                text: 'ESTOQUE',
                class: 'stock'
            };
        }

        function checkIfNeedsInspection(produto) {
            if (!produto) return false;

            switch (systemConfig.inspecaoRecebimento) {
                case 'todos':
                    return true;
                case 'criticos':
                    return produto.critico === true || produto.tipo === 'CRITICO';
                case 'manual':
                    return produto.inspecaoObrigatoria === true;
                default:
                    return false;
            }
        }

        async function loadDeliveryHistory() {
            if (!systemConfig.controlarHistoricoEntregas) return;

            const historySection = document.getElementById('deliveryHistory');
            const historyContent = document.getElementById('deliveryHistoryContent');

            // Buscar histórico de recebimentos anteriores
            try {
                const movimentacoesQuery = query(
                    collection(db, "movimentacoesEstoque"),
                    where("pedidoCompraId", "==", currentOrder.id),
                    where("tipo", "==", "ENTRADA"),
                    orderBy("dataMovimentacao", "desc")
                );

                const movimentacoesSnap = await getDocs(movimentacoesQuery);
                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                if (movimentacoes.length > 0) {
                    historyContent.innerHTML = movimentacoes.map(mov => `
                        <div class="delivery-item">
                            <div>
                                <strong>NF: ${mov.numeroNF}</strong> - ${formatDate(mov.dataMovimentacao.toDate())}
                                <br><small>${mov.itens?.length || 0} itens recebidos</small>
                            </div>
                            <div>
                                <span class="status-badge ok">R$ ${(mov.valorTotal || 0).toFixed(2)}</span>
                            </div>
                        </div>
                    `).join('');

                    historySection.style.display = 'block';
                } else {
                    historySection.style.display = 'none';
                }
            } catch (error) {
                console.error('Erro ao carregar histórico:', error);
                historySection.style.display = 'none';
            }
        }

        function updateCurrentReceiptSummary() {
            if (!currentOrder || !currentOrder.itens) return;

            let currentReceiptValue = 0;
            let currentReceiptItems = 0;

            currentOrder.itens.forEach((item, index) => {
                const qtyInput = document.getElementById(`qty_${index}`);
                const priceInput = document.getElementById(`price_${index}`);

                if (qtyInput && priceInput) {
                    const qty = parseFloat(qtyInput.value) || 0;
                    const price = parseFloat(priceInput.value) || 0;

                    if (qty > 0) {
                        currentReceiptValue += qty * price;
                        currentReceiptItems++;
                    }
                }
            });

            // Atualizar campo de valor da NF se estiver vazio
            const invoiceValueInput = document.getElementById('invoiceValue');
            if (invoiceValueInput && !invoiceValueInput.value && currentReceiptValue > 0) {
                invoiceValueInput.value = currentReceiptValue.toFixed(2);
            }

            // Mostrar resumo da entrega atual
            const receiptSection = document.getElementById('receiptSection');
            let currentSummary = receiptSection.querySelector('.current-receipt-summary');

            if (!currentSummary && currentReceiptValue > 0) {
                currentSummary = document.createElement('div');
                currentSummary.className = 'current-receipt-summary alert alert-info';
                currentSummary.innerHTML = `
                    <i class="fas fa-calculator"></i>
                    <div>
                        <strong>Resumo da Entrega Atual:</strong>
                        <span id="currentReceiptSummaryText"></span>
                    </div>
                `;
                receiptSection.appendChild(currentSummary);
            }

            if (currentSummary) {
                const summaryText = document.getElementById('currentReceiptSummaryText');
                if (currentReceiptValue > 0) {
                    summaryText.textContent = `${currentReceiptItems} itens - Valor: R$ ${currentReceiptValue.toFixed(2)}`;
                    currentSummary.style.display = 'block';
                } else {
                    currentSummary.style.display = 'none';
                }
            }
        }

        // ===== PROCESSAMENTO DO RECEBIMENTO =====
        window.processReceipt = async function() {
            try {
                if (!validateReceiptData()) {
                    return;
                }

                showNotification('Processando recebimento...', 'info');

                // Simular processamento (implementar lógica real aqui)
                setTimeout(() => {
                    showNotification('Recebimento processado com sucesso!', 'success');
                }, 1500);

            } catch (error) {
                console.error('Erro ao processar recebimento:', error);
                showNotification('Erro ao processar recebimento: ' + error.message, 'error');
            }
        };

        window.requestAuthorization = function() {
            showNotification('Solicitação de autorização enviada para aprovação', 'info');
            // Implementar lógica de autorização
        };

        function validateReceiptData() {
            const invoiceNumber = document.getElementById('invoiceNumber').value.trim();
            const invoiceDate = document.getElementById('invoiceDate').value;
            const invoiceValue = document.getElementById('invoiceValue').value;
            const warehouse = document.getElementById('warehouseSelect').value;

            if (!invoiceNumber) {
                showNotification('Número da nota fiscal é obrigatório', 'warning');
                document.getElementById('invoiceNumber').focus();
                return false;
            }

            if (!invoiceDate) {
                showNotification('Data da nota fiscal é obrigatória', 'warning');
                document.getElementById('invoiceDate').focus();
                return false;
            }

            if (!invoiceValue || parseFloat(invoiceValue) <= 0) {
                showNotification('Valor da nota fiscal deve ser maior que zero', 'warning');
                document.getElementById('invoiceValue').focus();
                return false;
            }

            if (!warehouse) {
                showNotification('Selecione o armazém de destino', 'warning');
                document.getElementById('warehouseSelect').focus();
                return false;
            }

            // Validar itens
            let hasValidItems = false;
            let hasErrors = false;

            currentOrder.itens.forEach((item, index) => {
                const qtyInput = document.getElementById(`qty_${index}`);
                const priceInput = document.getElementById(`price_${index}`);

                if (qtyInput && parseFloat(qtyInput.value) > 0) {
                    hasValidItems = true;

                    if (qtyInput.classList.contains('error') || priceInput.classList.contains('error')) {
                        hasErrors = true;
                    }
                }
            });

            if (!hasValidItems) {
                showNotification('Informe a quantidade a receber para pelo menos um item', 'warning');
                return false;
            }

            if (hasErrors) {
                showNotification('Corrija os erros nos itens antes de processar', 'error');
                return false;
            }

            return true;
        }

        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    </script>
</body>
</html>
