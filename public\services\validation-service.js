/**
 * SERVIÇO DE VALIDAÇÃO E SANITIZAÇÃO - WIZAR ERP
 * Implementa validação rigorosa e sanitização de dados para prevenir vulnerabilidades
 */

export class ValidationService {
    
    /**
     * 🧹 SANITIZAÇÃO DE STRINGS
     */
    static sanitizeString(input) {
        if (typeof input !== 'string') {
            return '';
        }
        
        return input
            .trim()
            .replace(/[<>]/g, '') // Remove < e >
            .replace(/javascript:/gi, '') // Remove javascript:
            .replace(/on\w+=/gi, '') // Remove event handlers
            .replace(/script/gi, '') // Remove script
            .substring(0, 1000); // <PERSON><PERSON> ta<PERSON>
    }
    
    /**
     * 🔢 SANITIZAÇÃO DE NÚMEROS
     */
    static sanitizeNumber(input, options = {}) {
        const { min = -Infinity, max = Infinity, decimals = 2 } = options;
        
        let num = parseFloat(input);
        
        if (isNaN(num)) {
            return 0;
        }
        
        // Aplicar limites
        num = Math.max(min, Math.min(max, num));
        
        // Arredondar decimais
        return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
    }
    
    /**
     * 📧 VALIDAR EMAIL
     */
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (!email || typeof email !== 'string') {
            return { valid: false, message: 'Email é obrigatório' };
        }
        
        const sanitizedEmail = this.sanitizeString(email).toLowerCase();
        
        if (!emailRegex.test(sanitizedEmail)) {
            return { valid: false, message: 'Formato de email inválido' };
        }
        
        if (sanitizedEmail.length > 255) {
            return { valid: false, message: 'Email muito longo' };
        }
        
        return { valid: true, value: sanitizedEmail };
    }
    
    /**
     * 💰 VALIDAR VALOR MONETÁRIO
     */
    static validateCurrency(value, options = {}) {
        const { min = 0, max = 999999999.99, required = true } = options;
        
        if (!value && value !== 0) {
            if (required) {
                return { valid: false, message: 'Valor é obrigatório' };
            }
            return { valid: true, value: 0 };
        }
        
        const sanitizedValue = this.sanitizeNumber(value, { min, max, decimals: 2 });
        
        if (sanitizedValue < min) {
            return { valid: false, message: `Valor mínimo é ${min}` };
        }
        
        if (sanitizedValue > max) {
            return { valid: false, message: `Valor máximo é ${max}` };
        }
        
        return { valid: true, value: sanitizedValue };
    }
    
    /**
     * 📊 VALIDAR QUANTIDADE
     */
    static validateQuantity(quantity, options = {}) {
        const { min = 0.001, max = 999999, decimals = 3, required = true } = options;
        
        if (!quantity && quantity !== 0) {
            if (required) {
                return { valid: false, message: 'Quantidade é obrigatória' };
            }
            return { valid: true, value: 0 };
        }
        
        const sanitizedQuantity = this.sanitizeNumber(quantity, { min, max, decimals });
        
        if (sanitizedQuantity <= 0) {
            return { valid: false, message: 'Quantidade deve ser positiva' };
        }
        
        if (sanitizedQuantity > max) {
            return { valid: false, message: `Quantidade máxima é ${max}` };
        }
        
        return { valid: true, value: sanitizedQuantity };
    }
    
    /**
     * 📅 VALIDAR DATA
     */
    static validateDate(date, options = {}) {
        const { required = true, minDate = null, maxDate = null } = options;
        
        if (!date) {
            if (required) {
                return { valid: false, message: 'Data é obrigatória' };
            }
            return { valid: true, value: null };
        }
        
        let dateObj;
        
        if (date instanceof Date) {
            dateObj = date;
        } else if (typeof date === 'string') {
            dateObj = new Date(date);
        } else {
            return { valid: false, message: 'Formato de data inválido' };
        }
        
        if (isNaN(dateObj.getTime())) {
            return { valid: false, message: 'Data inválida' };
        }
        
        if (minDate && dateObj < minDate) {
            return { valid: false, message: `Data deve ser posterior a ${minDate.toLocaleDateString()}` };
        }
        
        if (maxDate && dateObj > maxDate) {
            return { valid: false, message: `Data deve ser anterior a ${maxDate.toLocaleDateString()}` };
        }
        
        return { valid: true, value: dateObj };
    }
    
    /**
     * 🏷️ VALIDAR CÓDIGO DE PRODUTO
     */
    static validateProductCode(code) {
        if (!code || typeof code !== 'string') {
            return { valid: false, message: 'Código do produto é obrigatório' };
        }
        
        const sanitizedCode = this.sanitizeString(code).toUpperCase();
        
        // Código deve ter entre 3 e 20 caracteres alfanuméricos
        const codeRegex = /^[A-Z0-9]{3,20}$/;
        
        if (!codeRegex.test(sanitizedCode)) {
            return { valid: false, message: 'Código deve ter 3-20 caracteres alfanuméricos' };
        }
        
        return { valid: true, value: sanitizedCode };
    }
    
    /**
     * 📝 VALIDAR SOLICITAÇÃO DE COMPRA
     */
    static validatePurchaseRequest(data) {
        const errors = [];
        const sanitizedData = {};
        
        // Validar solicitante
        if (!data.solicitante || typeof data.solicitante !== 'string') {
            errors.push('Solicitante é obrigatório');
        } else {
            sanitizedData.solicitante = this.sanitizeString(data.solicitante);
        }
        
        // Validar departamento
        if (!data.departamento) {
            errors.push('Departamento é obrigatório');
        } else {
            sanitizedData.departamento = this.sanitizeString(data.departamento);
        }
        
        // Validar centro de custo
        if (!data.centroCusto) {
            errors.push('Centro de custo é obrigatório');
        } else {
            sanitizedData.centroCusto = this.sanitizeString(data.centroCusto);
        }
        
        // Validar data necessária
        const dateValidation = this.validateDate(data.dataNecessaria, {
            required: true,
            minDate: new Date()
        });
        
        if (!dateValidation.valid) {
            errors.push(dateValidation.message);
        } else {
            sanitizedData.dataNecessaria = dateValidation.value;
        }
        
        // Validar justificativa
        if (!data.justificativa || data.justificativa.trim().length < 10) {
            errors.push('Justificativa deve ter pelo menos 10 caracteres');
        } else {
            sanitizedData.justificativa = this.sanitizeString(data.justificativa);
        }
        
        // Validar itens
        if (!data.itens || !Array.isArray(data.itens) || data.itens.length === 0) {
            errors.push('Pelo menos um item é obrigatório');
        } else {
            const itemsValidation = this.validatePurchaseItems(data.itens);
            if (!itemsValidation.valid) {
                errors.push(...itemsValidation.errors);
            } else {
                sanitizedData.itens = itemsValidation.items;
            }
        }
        
        // Validar valor total
        const totalValue = data.itens?.reduce((sum, item) => sum + (item.quantidade * item.valorUnitario || 0), 0) || 0;
        const valueValidation = this.validateCurrency(totalValue, { min: 0.01, max: 10000000 });
        
        if (!valueValidation.valid) {
            errors.push(valueValidation.message);
        } else {
            sanitizedData.valorTotal = valueValidation.value;
        }
        
        return {
            valid: errors.length === 0,
            errors,
            data: sanitizedData
        };
    }
    
    /**
     * 📦 VALIDAR ITENS DE COMPRA
     */
    static validatePurchaseItems(items) {
        const errors = [];
        const sanitizedItems = [];
        const seenCodes = new Set();
        
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const itemErrors = [];
            const sanitizedItem = {};
            
            // Validar código
            const codeValidation = this.validateProductCode(item.codigo);
            if (!codeValidation.valid) {
                itemErrors.push(`Item ${i + 1}: ${codeValidation.message}`);
            } else {
                if (seenCodes.has(codeValidation.value)) {
                    itemErrors.push(`Item ${i + 1}: Código duplicado`);
                } else {
                    seenCodes.add(codeValidation.value);
                    sanitizedItem.codigo = codeValidation.value;
                }
            }
            
            // Validar descrição
            if (!item.descricao || item.descricao.trim().length < 3) {
                itemErrors.push(`Item ${i + 1}: Descrição deve ter pelo menos 3 caracteres`);
            } else {
                sanitizedItem.descricao = this.sanitizeString(item.descricao);
            }
            
            // Validar quantidade
            const quantityValidation = this.validateQuantity(item.quantidade);
            if (!quantityValidation.valid) {
                itemErrors.push(`Item ${i + 1}: ${quantityValidation.message}`);
            } else {
                sanitizedItem.quantidade = quantityValidation.value;
            }
            
            // Validar unidade
            if (!item.unidade || item.unidade.trim().length === 0) {
                itemErrors.push(`Item ${i + 1}: Unidade é obrigatória`);
            } else {
                sanitizedItem.unidade = this.sanitizeString(item.unidade).toUpperCase();
            }
            
            // Validar valor unitário (se fornecido)
            if (item.valorUnitario !== undefined && item.valorUnitario !== null) {
                const priceValidation = this.validateCurrency(item.valorUnitario, { min: 0 });
                if (!priceValidation.valid) {
                    itemErrors.push(`Item ${i + 1}: ${priceValidation.message}`);
                } else {
                    sanitizedItem.valorUnitario = priceValidation.value;
                }
            }
            
            if (itemErrors.length === 0) {
                sanitizedItems.push(sanitizedItem);
            } else {
                errors.push(...itemErrors);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors,
            items: sanitizedItems
        };
    }
}
