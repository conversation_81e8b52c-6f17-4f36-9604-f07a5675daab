/**
 * SERVIÇO DE AUTENTICAÇÃO SEGURA - WIZAR ERP
 * Implementa autenticação JWT com validação rigorosa e controle de sessão
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc, 
    updateDoc, 
    addDoc,
    query,
    where,
    getDocs,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class AuthService {
    static SESSION_TIMEOUT = 3600000; // 1 hora
    static REFRESH_THRESHOLD = 300000; // 5 minutos antes de expirar
    
    /**
     * 🔐 AUTENTICAÇÃO SEGURA
     */
    static async authenticateUser(email, password) {
        try {
            // Validar entrada
            if (!email || !password) {
                throw new Error('Email e senha são obrigatórios');
            }
            
            // Sanitizar email
            email = email.toLowerCase().trim();
            
            // Buscar usuário
            const userQuery = query(
                collection(db, "usuarios"),
                where("email", "==", email),
                where("ativo", "==", true)
            );
            
            const userSnapshot = await getDocs(userQuery);
            
            if (userSnapshot.empty) {
                throw new Error('Credenciais inválidas');
            }
            
            const userDoc = userSnapshot.docs[0];
            const userData = userDoc.data();
            
            // Verificar senha (em produção, usar hash)
            if (userData.senha !== password) {
                // Registrar tentativa de login inválida
                await this.logSecurityEvent('LOGIN_FAILED', {
                    email,
                    ip: await this.getUserIP(),
                    timestamp: new Date()
                });
                throw new Error('Credenciais inválidas');
            }
            
            // Verificar se usuário não está bloqueado
            if (userData.bloqueado) {
                throw new Error('Usuário bloqueado. Contate o administrador.');
            }
            
            // Gerar token JWT
            const token = await this.generateJWT(userDoc.id, userData);
            
            // Criar sessão
            const sessionData = await this.createSession(userDoc.id, userData, token);
            
            // Registrar login bem-sucedido
            await this.logSecurityEvent('LOGIN_SUCCESS', {
                userId: userDoc.id,
                email: userData.email,
                ip: await this.getUserIP(),
                timestamp: new Date()
            });
            
            // Atualizar último login
            await updateDoc(doc(db, "usuarios", userDoc.id), {
                ultimoLogin: Timestamp.now(),
                ultimoIP: await this.getUserIP()
            });
            
            return {
                success: true,
                token,
                user: {
                    id: userDoc.id,
                    nome: userData.nome,
                    email: userData.email,
                    nivel: userData.nivel,
                    departamento: userData.departamento,
                    permissoes: userData.permissoes || {}
                },
                sessionId: sessionData.sessionId,
                expiresAt: sessionData.expiresAt
            };
            
        } catch (error) {
            console.error('Erro na autenticação:', error);
            throw error;
        }
    }
    
    /**
     * 🎫 GERAÇÃO DE JWT
     */
    static async generateJWT(userId, userData) {
        const header = {
            alg: 'HS256',
            typ: 'JWT'
        };
        
        const payload = {
            sub: userId,
            email: userData.email,
            nivel: userData.nivel,
            departamento: userData.departamento,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor((Date.now() + this.SESSION_TIMEOUT) / 1000),
            jti: this.generateUUID() // JWT ID único
        };
        
        // Em produção, usar biblioteca JWT adequada
        const token = btoa(JSON.stringify(header)) + '.' + 
                     btoa(JSON.stringify(payload)) + '.' + 
                     btoa('signature_placeholder');
        
        return token;
    }
    
    /**
     * 📝 CRIAR SESSÃO
     */
    static async createSession(userId, userData, token) {
        const sessionId = this.generateUUID();
        const expiresAt = new Date(Date.now() + this.SESSION_TIMEOUT);
        
        const sessionData = {
            sessionId,
            userId,
            token,
            createdAt: Timestamp.now(),
            expiresAt: Timestamp.fromDate(expiresAt),
            ip: await this.getUserIP(),
            userAgent: navigator.userAgent,
            active: true,
            lastActivity: Timestamp.now()
        };
        
        await addDoc(collection(db, "sessoes"), sessionData);
        
        // Armazenar no localStorage de forma segura
        const secureStorage = {
            token,
            sessionId,
            expiresAt: expiresAt.getTime(),
            userId
        };
        
        localStorage.setItem('authData', JSON.stringify(secureStorage));
        
        return { sessionId, expiresAt };
    }
    
    /**
     * ✅ VALIDAR TOKEN
     */
    static async validateToken(token) {
        try {
            if (!token) {
                return { valid: false, reason: 'Token não fornecido' };
            }
            
            // Decodificar token (simplificado)
            const parts = token.split('.');
            if (parts.length !== 3) {
                return { valid: false, reason: 'Token malformado' };
            }
            
            const payload = JSON.parse(atob(parts[1]));
            
            // Verificar expiração
            if (payload.exp * 1000 < Date.now()) {
                return { valid: false, reason: 'Token expirado' };
            }
            
            // Verificar se sessão ainda está ativa
            const sessionQuery = query(
                collection(db, "sessoes"),
                where("userId", "==", payload.sub),
                where("active", "==", true)
            );
            
            const sessionSnapshot = await getDocs(sessionQuery);
            
            if (sessionSnapshot.empty) {
                return { valid: false, reason: 'Sessão inválida' };
            }
            
            // Atualizar última atividade
            const sessionDoc = sessionSnapshot.docs[0];
            await updateDoc(sessionDoc.ref, {
                lastActivity: Timestamp.now()
            });
            
            return { 
                valid: true, 
                userId: payload.sub,
                nivel: payload.nivel,
                departamento: payload.departamento
            };
            
        } catch (error) {
            console.error('Erro na validação do token:', error);
            return { valid: false, reason: 'Erro na validação' };
        }
    }
    
    /**
     * 🚪 LOGOUT SEGURO
     */
    static async logout() {
        try {
            const authData = JSON.parse(localStorage.getItem('authData') || '{}');
            
            if (authData.sessionId) {
                // Invalidar sessão no banco
                const sessionQuery = query(
                    collection(db, "sessoes"),
                    where("sessionId", "==", authData.sessionId)
                );
                
                const sessionSnapshot = await getDocs(sessionQuery);
                
                if (!sessionSnapshot.empty) {
                    const sessionDoc = sessionSnapshot.docs[0];
                    await updateDoc(sessionDoc.ref, {
                        active: false,
                        logoutAt: Timestamp.now()
                    });
                }
            }
            
            // Limpar localStorage
            localStorage.removeItem('authData');
            localStorage.removeItem('currentUser');
            
            // Registrar logout
            await this.logSecurityEvent('LOGOUT', {
                userId: authData.userId,
                sessionId: authData.sessionId,
                timestamp: new Date()
            });
            
            return { success: true };
            
        } catch (error) {
            console.error('Erro no logout:', error);
            // Mesmo com erro, limpar dados locais
            localStorage.removeItem('authData');
            localStorage.removeItem('currentUser');
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 🔄 RENOVAR TOKEN
     */
    static async refreshToken() {
        try {
            const authData = JSON.parse(localStorage.getItem('authData') || '{}');
            
            if (!authData.token) {
                throw new Error('Token não encontrado');
            }
            
            const validation = await this.validateToken(authData.token);
            
            if (!validation.valid) {
                throw new Error('Token inválido para renovação');
            }
            
            // Verificar se precisa renovar
            const timeToExpire = authData.expiresAt - Date.now();
            
            if (timeToExpire > this.REFRESH_THRESHOLD) {
                return { success: true, token: authData.token }; // Não precisa renovar ainda
            }
            
            // Buscar dados do usuário
            const userDoc = await getDoc(doc(db, "usuarios", validation.userId));
            
            if (!userDoc.exists()) {
                throw new Error('Usuário não encontrado');
            }
            
            const userData = userDoc.data();
            
            // Gerar novo token
            const newToken = await this.generateJWT(validation.userId, userData);
            
            // Atualizar sessão
            const sessionQuery = query(
                collection(db, "sessoes"),
                where("sessionId", "==", authData.sessionId)
            );
            
            const sessionSnapshot = await getDocs(sessionQuery);
            
            if (!sessionSnapshot.empty) {
                const sessionDoc = sessionSnapshot.docs[0];
                await updateDoc(sessionDoc.ref, {
                    token: newToken,
                    lastActivity: Timestamp.now(),
                    refreshedAt: Timestamp.now()
                });
            }
            
            // Atualizar localStorage
            const newAuthData = {
                ...authData,
                token: newToken,
                expiresAt: Date.now() + this.SESSION_TIMEOUT
            };
            
            localStorage.setItem('authData', JSON.stringify(newAuthData));
            
            return { success: true, token: newToken };
            
        } catch (error) {
            console.error('Erro na renovação do token:', error);
            throw error;
        }
    }

    /**
     * 🛡️ VERIFICAR PERMISSÕES
     */
    static async checkPermission(userId, permission, action = 'read') {
        try {
            const userDoc = await getDoc(doc(db, "usuarios", userId));

            if (!userDoc.exists()) {
                return false;
            }

            const userData = userDoc.data();

            // Administradores têm todas as permissões
            if (userData.nivel >= 9) {
                return true;
            }

            // Verificar permissões específicas
            const userPermissions = userData.permissoes || {};

            if (userPermissions[permission]) {
                const permissionData = userPermissions[permission];

                // Se é boolean, verificar se é true
                if (typeof permissionData === 'boolean') {
                    return permissionData;
                }

                // Se é objeto, verificar ação específica
                if (typeof permissionData === 'object') {
                    return permissionData[action] === true;
                }
            }

            return false;

        } catch (error) {
            console.error('Erro na verificação de permissão:', error);
            return false;
        }
    }

    /**
     * 📊 LOG DE EVENTOS DE SEGURANÇA
     */
    static async logSecurityEvent(eventType, data) {
        try {
            await addDoc(collection(db, "securityLogs"), {
                eventType,
                data,
                timestamp: Timestamp.now(),
                ip: await this.getUserIP(),
                userAgent: navigator.userAgent
            });
        } catch (error) {
            console.error('Erro ao registrar evento de segurança:', error);
        }
    }

    /**
     * 🌐 OBTER IP DO USUÁRIO
     */
    static async getUserIP() {
        try {
            // Em produção, usar serviço adequado para obter IP
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * 🆔 GERAR UUID
     */
    static generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * 🔍 VERIFICAR SESSÃO ATIVA
     */
    static async getCurrentUser() {
        try {
            const authData = JSON.parse(localStorage.getItem('authData') || '{}');

            if (!authData.token) {
                return null;
            }

            // Verificar se token ainda é válido
            const validation = await this.validateToken(authData.token);

            if (!validation.valid) {
                // Tentar renovar token
                try {
                    await this.refreshToken();
                    const newAuthData = JSON.parse(localStorage.getItem('authData') || '{}');
                    const newValidation = await this.validateToken(newAuthData.token);

                    if (!newValidation.valid) {
                        return null;
                    }

                    validation.userId = newValidation.userId;
                    validation.nivel = newValidation.nivel;
                    validation.departamento = newValidation.departamento;
                } catch (refreshError) {
                    return null;
                }
            }

            // Buscar dados completos do usuário
            const userDoc = await getDoc(doc(db, "usuarios", validation.userId));

            if (!userDoc.exists()) {
                return null;
            }

            const userData = userDoc.data();

            return {
                id: validation.userId,
                nome: userData.nome,
                email: userData.email,
                nivel: userData.nivel,
                departamento: userData.departamento,
                permissoes: userData.permissoes || {},
                token: authData.token
            };

        } catch (error) {
            console.error('Erro ao obter usuário atual:', error);
            return null;
        }
    }

    /**
     * 🔒 MIDDLEWARE DE AUTENTICAÇÃO
     */
    static async requireAuth() {
        const user = await this.getCurrentUser();

        if (!user) {
            // Redirecionar para login
            window.location.href = 'login.html';
            throw new Error('Autenticação necessária');
        }

        return user;
    }

    /**
     * 🛡️ MIDDLEWARE DE PERMISSÃO
     */
    static async requirePermission(permission, action = 'read') {
        const user = await this.requireAuth();

        const hasPermission = await this.checkPermission(user.id, permission, action);

        if (!hasPermission) {
            throw new Error(`Permissão negada: ${permission}:${action}`);
        }

        return user;
    }

    /**
     * 🧹 LIMPAR SESSÕES EXPIRADAS
     */
    static async cleanupExpiredSessions() {
        try {
            const expiredQuery = query(
                collection(db, "sessoes"),
                where("expiresAt", "<", Timestamp.now()),
                where("active", "==", true)
            );

            const expiredSnapshot = await getDocs(expiredQuery);

            const batch = [];
            expiredSnapshot.docs.forEach(doc => {
                batch.push(updateDoc(doc.ref, { active: false }));
            });

            await Promise.all(batch);

            console.log(`${expiredSnapshot.docs.length} sessões expiradas limpas`);

        } catch (error) {
            console.error('Erro na limpeza de sessões:', error);
        }
    }
}
