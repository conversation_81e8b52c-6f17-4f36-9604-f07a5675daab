// Serviço para gerenciar fluxos de trabalho do sistema
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  updateDoc, 
  addDoc, 
  Timestamp,
  writeBatch,
  arrayUnion,
  query,
  where,
  getDocs,
  increment,
  getDoc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class WorkflowService {
  // Gerencia rejeições de documentos (orçamentos, pedidos, etc)
  static async handleRejection(params) {
    const { 
      documentType,
      documentId,
      motivo,
      observacoes,
      usuarioId,
      usuarioNome,
      nivelAprovacao
    } = params;

    const batch = writeBatch(db);

    try {
      // Verificar impactos nos prazos
      if (documentType === 'cotacao') {
        const produtoRef = doc(db, "produtos", documentId);
        const produtoDoc = await getDoc(produtoRef);
        if(produtoDoc.exists()){
            const produto = produtoDoc.data();

            const leadTimeFornecedor = produto.leadTimeCompra || 0;
            const dataAtual = new Date();
            // Assuming prazoNecessidade is available in params or document data
            // For example: const prazoNecessidade = new Date(params.prazoNecessidade);
            // Or fetch it from the document data
            // const cotacaoDoc = await getDoc(doc(db, "cotacoes", documentId));
            // const prazoNecessidade = cotacaoDoc.data().prazoNecessidade;
            const prazoNecessidade = new Date() // Assigning a default value. Need to get from params or DB

            const prazoEstimadoEntrega = new Date(dataAtual.getTime() + (leadTimeFornecedor * 24 * 60 * 60 * 1000));

            if (prazoEstimadoEntrega > prazoNecessidade) {
              // Assuming NotificationService is available
               //await NotificationService.createSystemAlert({
              //  tipo: 'ATRASO_PREVISTO',
              //  severidade: 'CRITICO',
              //  mensagem: `Risco de atraso na entrega do produto ${produto.codigo}. Lead time do fornecedor (${leadTimeFornecedor} dias) ultrapassa o prazo necessário.`,
              //  modulo: 'compras',
              //  documentoId,
              //  documentoTipo: 'cotacao',
              //  acaoRequerida: true
              //});
            }
        }

      }

      const docRef = doc(db, `${documentType}s`, documentId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        throw new Error('Documento não encontrado');
      }

      const documento = docSnapshot.data();

      // Atualizar status e histórico
      const historicoItem = {
        data: Timestamp.now(),
        acao: 'REJEITADO',
        motivo,
        observacoes,
        usuario: {
          id: usuarioId,
          nome: usuarioNome
        }
      };

    try {
      const docRef = doc(db, `${documentType}s`, documentId);

      // Atualiza status e adiciona histórico
      batch.update(docRef, {
        status: 'Rejeitado',
        dataRejeicao: Timestamp.now(),
        motivoRejeicao: motivo,
        observacoesRejeicao: observacoes,
        rejeitadoPor: {
          id: usuarioId,
          nome: usuarioNome,
          nivel: nivelAprovacao
        },
        historico: arrayUnion({
          data: Timestamp.now(),
          acao: 'Rejeitado',
          motivo,
          observacoes,
          usuario: {
            id: usuarioId,
            nome: usuarioNome
          }
        })
      });

      // Se for pedido, libera reservas de estoque
      if (documentType === 'pedido') {
        const pedidoDoc = await getDocs(query(collection(db, "pedidosVenda"), where("id", "==", documentId)));
        const pedido = pedidoDoc.docs[0].data();

        for (const item of pedido.itens) {
          const estoqueRef = doc(db, "estoques", item.produtoId);
          batch.update(estoqueRef, {
            quantidade: increment(item.quantidade),
            saldoReservado: increment(-item.quantidade)
          });
        }
      }

      await batch.commit();

      // Envia notificações
      await this.sendRejectionNotifications({
        documentType,
        documentId,
        motivo,
        usuarioNome
      });

      return { success: true };
    } catch (error) {
      console.error("Erro ao processar rejeição:", error);
      throw error;
    }
  }

  // Gerencia cancelamentos
  static async handleCancellation(params) {
    const {
      documentType,
      documentId,
      motivo,
      observacoes,
      usuarioId,
      usuarioNome
    } = params;

    // Verificar impactos do cancelamento
    const impactos = await this.analisarImpactosCancelamento(documentType, documentId);
    if (impactos.temImpactos) {
      await this.createNotification({
        tipo: 'ALERTA_CANCELAMENTO',
        titulo: `Impactos identificados no cancelamento - ${documentType}`,
        mensagem: `O cancelamento afetará: ${impactos.descricao}`,
        severidade: 'AVISO',
        destinatarios: impactos.usuariosImpactados,
        modulo: documentType,
        documentoId,
        acaoRequerida: true
      });
    }

    const batch = writeBatch(db);

    try {
      const docRef = doc(db, `${documentType}s`, documentId);

      // Atualiza status e adiciona histórico
      batch.update(docRef, {
        status: 'Cancelado',
        dataCancelamento: Timestamp.now(),
        motivoCancelamento: motivo,
        observacoesCancelamento: observacoes,
        canceladoPor: {
          id: usuarioId,
          nome: usuarioNome
        },
        historico: arrayUnion({
          data: Timestamp.now(),
          acao: 'Cancelado',
          motivo,
          observacoes,
          usuario: {
            id: usuarioId,
            nome: usuarioNome
          }
        })
      });

      // Processar efeitos colaterais do cancelamento
      await this.handleCancellationSideEffects({
        documentType,
        documentId,
        batch
      });

      await batch.commit();

      // Envia notificações
      await this.sendCancellationNotifications({
        documentType,
        documentId,
        motivo,
        usuarioNome
      });

      return { success: true };
    } catch (error) {
      console.error("Erro ao processar cancelamento:", error);
      throw error;
    }
  }

  // Gerencia ajustes em documentos
  static async handleAdjustment(params) {
    const {
      documentType,
      documentId,
      ajustes,
      motivo,
      usuarioId,
      usuarioNome
    } = params;

    const batch = writeBatch(db);

    try {
      const docRef = doc(db, `${documentType}s`, documentId);
      const docSnapshot = await getDocs(query(collection(db, `${documentType}s`), where("id", "==", documentId)));
      const documentoOriginal = docSnapshot.docs[0].data();

      // Cria versão do documento
      const versao = {
        numero: (documentoOriginal.versoes?.length || 0) + 1,
        dataAjuste: Timestamp.now(),
        ajustes,
        motivo,
        usuarioAjuste: {
          id: usuarioId,
          nome: usuarioNome
        }
      };

      // Atualiza documento com ajustes
      batch.update(docRef, {
        ...ajustes,
        versoes: arrayUnion(versao),
        ultimaAtualizacao: Timestamp.now(),
        historico: arrayUnion({
          data: Timestamp.now(),
          acao: 'Ajustado',
          motivo,
          ajustes: Object.keys(ajustes),
          usuario: {
            id: usuarioId,
            nome: usuarioNome
          }
        })
      });

      await batch.commit();

      // Envia notificações
      await this.sendAdjustmentNotifications({
        documentType,
        documentId,
        ajustes,
        motivo,
        usuarioNome
      });

      return { success: true, versao };
    } catch (error) {
      console.error("Erro ao processar ajuste:", error);
      throw error;
    }
  }

  // Gerencia alertas críticos do sistema
  static async checkCriticalAlerts() {
    try {
      const batch = writeBatch(db);
      const alertsToCreate = [];

      // Verifica limite de crédito dos clientes
      const clientesQuery = query(
        collection(db, "clientes"),
        where("ativo", "==", true)
      );

      const clientesSnapshot = await getDocs(clientesQuery);
      const clientes = clientesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      for (const cliente of clientes) {
        const limiteInfo = await this.verificarLimiteCredito(cliente.id);

        if (limiteInfo && limiteInfo.limiteExcedido) {
          alertsToCreate.push({
            tipo: 'LIMITE_CREDITO',
            severidade: 'CRITICA',
            titulo: `Limite de Crédito Excedido - ${cliente.nome || cliente.razaoSocial}`,
            mensagem: `O cliente excedeu seu limite de crédito. Limite: R$ ${limiteInfo.limiteTotal.toFixed(2)}, Utilizado: R$ ${limiteInfo.limiteUtilizado.toFixed(2)}`,
            cliente: {
              id: cliente.id,
              nome: cliente.nome || cliente.razaoSocial
            },
            dataCriacao: Timestamp.now(),
            status: 'ATIVO',
            acaoRequerida: true
          });
        }
      }

      // Verifica estoque mínimo dos produtos
      const produtosQuery = query(
        collection(db, "produtos"),
        where("controleEstoque", "==", true)
      );

      const produtosSnapshot = await getDocs(produtosQuery);
      const produtos = produtosSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      for (const produto of produtos) {
        const estoqueInfo = await this.verificarEstoque(produto.id);

        if (estoqueInfo && estoqueInfo.abaixoMinimo) {
          alertsToCreate.push({
            tipo: 'ESTOQUE_MINIMO',
            severidade: 'ALTA',
            titulo: `Estoque Mínimo Atingido - ${produto.codigo}`,
            mensagem: `O produto ${produto.codigo} - ${produto.descricao} atingiu o estoque mínimo. Atual: ${estoqueInfo.quantidadeAtual}, Mínimo: ${produto.estoqueMinimo}`,
            produto: {
              id: produto.id,
              codigo: produto.codigo,
              descricao: produto.descricao
            },
            dataCriacao: Timestamp.now(),
            status: 'ATIVO',
            acaoRequerida: true
          });
        }
      }

      // Cria os alertas no banco
      for (const alerta of alertsToCreate) {
        const alertaRef = doc(collection(db, "alertas"));
        batch.set(alertaRef, alerta);
      }

      await batch.commit();
    } catch (error) {
      console.error('Erro ao verificar alertas críticos:', error);
      throw error;
    }
  }

  static async verificarLimiteCredito(clienteId) {
    try {
      const clienteRef = doc(db, "clientes", clienteId);
      const clienteDoc = await getDoc(clienteRef);

      if (!clienteDoc.exists()) {
        return null;
      }

      const cliente = clienteDoc.data();
      const limiteTotal = cliente.limiteCredito || 0;

      // Busca pedidos em aberto
      const pedidosQuery = query(
        collection(db, "pedidosVenda"),
        where("cliente.id", "==", clienteId),
        where("status", "in", ["Aguardando Aprovação", "Aprovado", "Em Produção"])
      );

      const pedidosSnapshot = await getDocs(pedidosQuery);
      const valorPedidosAbertos = pedidosSnapshot.docs.reduce((total, doc) => 
        total + (doc.data().valorTotal || 0), 0
      );

      return {
        limiteTotal,
        limiteUtilizado: valorPedidosAbertos,
        limiteDisponivel: limiteTotal - valorPedidosAbertos,
        limiteExcedido: valorPedidosAbertos > limiteTotal
      };
    } catch (error) {
      console.error('Erro ao verificar limite de crédito:', error);
      return null;
    }
  }

  static async verificarEstoque(produtoId) {
    try {
      const produtoRef = doc(db, "produtos", produtoId);
      const produtoDoc = await getDoc(produtoRef);

      if (!produtoDoc.exists()) {
        return null;
      }

      const produto = produtoDoc.data();
      const estoqueQuery = query(
        collection(db, "estoques"),
        where("produtoId", "==", produtoId)
      );

      const estoqueSnapshot = await getDocs(estoqueQuery);
      const quantidadeAtual = estoqueSnapshot.docs.reduce((total, doc) => 
        total + (doc.data().quantidade || 0), 0
      );

      return {
        quantidadeAtual,
        estoqueMinimo: produto.estoqueMinimo || 0,
        abaixoMinimo: quantidadeAtual <= (produto.estoqueMinimo || 0)
      };
    } catch (error) {
      console.error('Erro ao verificar estoque:', error);
      return null;
    }
  }

  // Métodos auxiliares privados
  static async handleCancellationSideEffects({ documentType, documentId, batch }) {
    switch (documentType) {
      case 'pedido':
        await this.handlePedidoCancellation(documentId, batch);
        break;
      case 'orcamento':
        await this.handleOrcamentoCancellation(documentId, batch);
        break;
      // Adicione outros tipos conforme necessário
    }
  }

  static async handlePedidoCancellation(pedidoId, batch) {
    const pedidoDoc = await getDocs(query(collection(db, "pedidosVenda"), where("id", "==", pedidoId)));
    const pedido = pedidoDoc.docs[0].data();

    // Libera reservas de estoque
    for (const item of pedido.itens) {
      const estoqueRef = doc(db, "estoques", item.produtoId);
      batch.update(estoqueRef, {
        quantidade: increment(item.quantidade),
        saldoReservado: increment(-item.quantidade)
      });
    }

    // Cancela ordens de produção relacionadas
    const ordensQuery = query(
      collection(db, "ordensProducao"),
      where("pedidoId", "==", pedidoId),
      where("status", "in", ["Em Andamento", "Aguardando"])
    );
    const ordensSnapshot = await getDocs(ordensQuery);

    ordensSnapshot.forEach(doc => {
      batch.update(doc.ref, {
        status: 'Cancelada',
        dataCancelamento: Timestamp.now()
      });
    });
  }

  static async handleOrcamentoCancellation(orcamentoId, batch) {
    // Implementar lógica específica para cancelamento de orçamentos
  }

  static async sendRejectionNotifications(params) {
    // Implementar envio de notificações de rejeição
  }

  static async sendCancellationNotifications(params) {
    // Implementar envio de notificações de cancelamento
  }

  static async sendAdjustmentNotifications(params) {
    // Implementar envio de notificações de ajuste
  }

  // Nova função para analisar impactos do cancelamento
  static async analisarImpactosCancelamento(documentType, documentId) {
    // Implementar lógica para análise de impactos
    return {
      temImpactos: false,
      descricao: '',
      usuariosImpactados: []
    };
  }

  // Nova função para criar notificações
  static async createNotification(notification) {
    // Implementar lógica para criar notificações
  }
}