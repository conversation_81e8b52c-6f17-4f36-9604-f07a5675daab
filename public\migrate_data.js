// ===================================================================
// MIGRATE DATA - CONFIGURAÇÃO CENTRALIZADA DO FIREBASE
// ===================================================================
// Para scripts Node.js, mantemos a configuração local por compatibilidade
// ===================================================================

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs } = require('firebase/firestore');

// Configuração Firebase centralizada (copiada do firebase-config.js)
const firebaseConfig = {
  apiKey: "AIzaSyDud1ir9Ta-0_jSB8dAaBCQau99RJY66RQ",
  authDomain: "banco-mrp.firebaseapp.com",
  projectId: "banco-mrp",
  storageBucket: "banco-mrp.firebasestorage.app",
  messagingSenderId: "740147152218",
  appId: "1:740147152218:web:2d301340bf314e68d75f63",
  measurementId: "G-YNNQ1VX1EH"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function migrateData() {
  console.log('Usando apenas Firestore, não é necessária migração.');
}

migrateData().catch(console.error);