<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Apontamento de Ordens de Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --border-color: #d4d4d4;
    }

    * { box-sizing: border-box; margin: 0; padding: 0; }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: #333;
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header h1::before {
      content: '🔧';
      font-size: 28px;
    }

    .form-container {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin: 20px;
      background: white;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      margin-bottom: 5px;
      color: #666;
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
      transition: background-color 0.2s;
    }

    .btn-primary { background-color: var(--primary-color); color: white; }
    .btn-success { background-color: var(--success-color); color: white; }
    .btn-danger { background-color: var(--danger-color); color: white; }
    .btn-warning { background-color: var(--warning-color); color: white; }
    .btn-secondary { background-color: #6c757d; color: white; }

    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .orders-table th,
    .orders-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .orders-table th {
      background-color: #f0f3f6;
      font-weight: 600;
      color: #666;
    }

    .orders-table tr:hover {
      background-color: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      display: inline-block;
    }

    .status-pendente { background-color: #fff3cd; color: #856404; }
    .status-em-andamento { background-color: #cce5ff; color: #004085; }
    .status-concluida { background-color: #d4edda; color: #155724; }
    .status-cancelada { background-color: #f8d7da; color: #721c24; }

    .priority-alta { background-color: #f8d7da; color: #721c24; }
    .priority-media { background-color: #fff3cd; color: #856404; }
    .priority-baixa { background-color: #d4edda; color: #155724; }

    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.6);
    }

    .modal-content {
      background: white;
      margin: 5% auto;
      padding: 0;
      width: 90%;
      max-width: 800px;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .modal-header {
      background: var(--primary-color);
      color: white;
      padding: 20px;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-body {
      padding: 20px;
    }

    .close-button {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 4px;
      color: white;
      z-index: 1001;
      display: none;
    }

    .notification-success { background-color: var(--success-color); }
    .notification-error { background-color: var(--danger-color); }

    .time-tracker {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      border: 1px solid #2196f3;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
      text-align: center;
    }

    .timer-display {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
      margin: 10px 0;
    }

    .timer-controls {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 10px;
    }

    .search-container {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
      align-items: center;
    }

    @media (max-width: 768px) {
      .container { width: 100%; margin: 0; border-radius: 0; }
      .form-row { grid-template-columns: 1fr; }
      .search-container { flex-direction: column; align-items: stretch; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Apontamento de Ordens de Manutenção</h1>
      <div>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div id="notification" class="notification"></div>

    <!-- Lista de Ordens -->
    <div class="form-container">
      <h2 class="form-title">Ordens de Manutenção em Andamento</h2>

      <div class="search-container">
        <input type="text" id="searchInput" placeholder="Pesquisar por código, recurso, fornecedor...">
        <select id="statusFilter">
          <option value="">Todos os Status</option>
          <option value="PENDENTE">Pendente</option>
          <option value="EM_ANDAMENTO">Em Andamento</option>
          <option value="CONCLUIDA">Concluída</option>
        </select>
        <select id="prioridadeFilter">
          <option value="">Todas as Prioridades</option>
          <option value="ALTA">Alta</option>
          <option value="MEDIA">Média</option>
          <option value="BAIXA">Baixa</option>
        </select>
      </div>

      <table class="orders-table">
        <thead>
          <tr>
            <th>Código</th>
            <th>Recurso</th>
            <th>Tipo</th>
            <th>Prioridade</th>
            <th>Status</th>
            <th>Data Prevista</th>
            <th>Responsável</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="ordersTableBody">
        </tbody>
      </table>
    </div>
  </div>

  <!-- Modal de Apontamento -->
  <div id="apontamentoModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Apontamento de Manutenção</h3>
        <button class="close-button" onclick="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div id="orderInfo"></div>
        
        <div class="time-tracker">
          <h4>Controle de Tempo</h4>
          <div class="timer-display" id="timerDisplay">00:00:00</div>
          <div class="timer-controls">
            <button class="btn btn-success" id="startButton" onclick="startTimer()">
              <i class="fas fa-play"></i> Iniciar
            </button>
            <button class="btn btn-warning" id="pauseButton" onclick="pauseTimer()" disabled>
              <i class="fas fa-pause"></i> Pausar
            </button>
            <button class="btn btn-danger" id="stopButton" onclick="stopTimer()" disabled>
              <i class="fas fa-stop"></i> Parar
            </button>
          </div>
        </div>

        <form id="apontamentoForm">
          <input type="hidden" id="ordemId">
          
          <div class="form-row">
            <div class="form-col">
              <label for="funcionario">Funcionário Responsável</label>
              <select id="funcionario" required>
                <option value="">Selecione o funcionário...</option>
              </select>
            </div>
            <div class="form-col">
              <label for="tempoGasto">Tempo Gasto (horas)</label>
              <input type="number" id="tempoGasto" step="0.1" min="0" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <label for="statusApontamento">Status da Ordem</label>
              <select id="statusApontamento" required>
                <option value="EM_ANDAMENTO">Em Andamento</option>
                <option value="CONCLUIDA">Concluída</option>
                <option value="CANCELADA">Cancelada</option>
              </select>
            </div>
            <div class="form-col">
              <label for="tipoServico">Tipo de Serviço</label>
              <select id="tipoServico">
                <option value="MANUTENCAO">Manutenção</option>
                <option value="REPARO">Reparo</option>
                <option value="INSTALACAO">Instalação</option>
                <option value="INSPECAO">Inspeção</option>
                <option value="LIMPEZA">Limpeza</option>
                <option value="TROCA_PECA">Troca de Peça</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <label for="servicosRealizados">Serviços Realizados</label>
              <textarea id="servicosRealizados" rows="3" required placeholder="Descreva os serviços realizados..."></textarea>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <label for="pecasUtilizadas">Peças Utilizadas</label>
              <textarea id="pecasUtilizadas" rows="2" placeholder="Liste as peças utilizadas (opcional)..."></textarea>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <label for="observacoes">Observações</label>
              <textarea id="observacoes" rows="2" placeholder="Observações adicionais (opcional)..."></textarea>
            </div>
          </div>

          <div class="form-actions" style="text-align: right; padding-top: 20px;">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancelar</button>
            <button type="submit" class="btn btn-success">Salvar Apontamento</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, addDoc, getDocs, doc, updateDoc, query, where, serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let ordensList = [];
    let funcionariosList = [];
    let currentOrder = null;
    let timerInterval = null;
    let startTime = null;
    let elapsedTime = 0;

    async function loadOrdens() {
      try {
        const ordensRef = collection(db, "ordensManutencao");
        const q = query(ordensRef, where("status", "in", ["PENDENTE", "EM_ANDAMENTO"]));
        const querySnapshot = await getDocs(q);

        ordensList = querySnapshot.docs.map(doc => ({
          id: doc.id, ...doc.data()
        }));

        updateOrdersTable(ordensList);
      } catch (error) {
        console.error("Erro ao carregar ordens:", error);
      }
    }

    async function loadFuncionarios() {
      try {
        const funcionariosRef = collection(db, "funcionariosManutencao");
        const q = query(funcionariosRef, where("status", "==", "ATIVO"));
        const querySnapshot = await getDocs(q);

        funcionariosList = querySnapshot.docs.map(doc => ({
          id: doc.id, ...doc.data()
        }));

        const select = document.getElementById('funcionario');
        select.innerHTML = '<option value="">Selecione o funcionário...</option>' +
          funcionariosList.map(func => 
            `<option value="${func.id}">${func.nome} - ${func.matricula}</option>`
          ).join('');
      } catch (error) {
        console.error("Erro ao carregar funcionários:", error);
      }
    }

    function updateOrdersTable(ordens) {
      const tbody = document.querySelector('#ordersTableBody');
      tbody.innerHTML = ordens.map(ordem => `
        <tr>
          <td>${ordem.codigo}</td>
          <td>${ordem.recursoNome || 'N/A'}</td>
          <td>${ordem.tipoOrdem}</td>
          <td><span class="status-badge priority-${ordem.prioridade?.toLowerCase() || 'media'}">${ordem.prioridade || 'MÉDIA'}</span></td>
          <td><span class="status-badge status-${ordem.status.toLowerCase().replace('_', '-')}">${ordem.status.replace('_', ' ')}</span></td>
          <td>${formatDate(ordem.dataPrevista)}</td>
          <td>${ordem.responsavelNome || 'N/A'}</td>
          <td>
            <button onclick="abrirApontamento('${ordem.id}')" class="btn btn-primary">
              <i class="fas fa-clipboard"></i> Apontar
            </button>
          </td>
        </tr>
      `).join('');
    }

    function formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleDateString('pt-BR');
    }

    window.abrirApontamento = function(ordemId) {
      currentOrder = ordensList.find(o => o.id === ordemId);
      if (!currentOrder) return;

      document.getElementById('ordemId').value = ordemId;
      document.getElementById('orderInfo').innerHTML = `
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
          <h4>Ordem: ${currentOrder.codigo}</h4>
          <p><strong>Recurso:</strong> ${currentOrder.recursoNome || 'N/A'}</p>
          <p><strong>Tipo:</strong> ${currentOrder.tipoOrdem}</p>
          <p><strong>Prioridade:</strong> ${currentOrder.prioridade || 'MÉDIA'}</p>
          <p><strong>Descrição:</strong> ${currentOrder.descricao || 'N/A'}</p>
        </div>
      `;

      resetTimer();
      document.getElementById('apontamentoModal').style.display = 'block';
    };

    window.closeModal = function() {
      document.getElementById('apontamentoModal').style.display = 'none';
      document.getElementById('apontamentoForm').reset();
      resetTimer();
      currentOrder = null;
    };

    // Timer functions
    window.startTimer = function() {
      startTime = new Date();
      timerInterval = setInterval(updateTimer, 1000);
      
      document.getElementById('startButton').disabled = true;
      document.getElementById('pauseButton').disabled = false;
      document.getElementById('stopButton').disabled = false;
    };

    window.pauseTimer = function() {
      clearInterval(timerInterval);
      elapsedTime += new Date() - startTime;
      
      document.getElementById('startButton').disabled = false;
      document.getElementById('pauseButton').disabled = true;
    };

    window.stopTimer = function() {
      clearInterval(timerInterval);
      if (startTime) {
        elapsedTime += new Date() - startTime;
      }
      
      const hours = Math.round((elapsedTime / (1000 * 60 * 60)) * 10) / 10;
      document.getElementById('tempoGasto').value = hours;
      
      resetTimer();
    };

    function resetTimer() {
      clearInterval(timerInterval);
      elapsedTime = 0;
      startTime = null;
      document.getElementById('timerDisplay').textContent = '00:00:00';
      document.getElementById('startButton').disabled = false;
      document.getElementById('pauseButton').disabled = true;
      document.getElementById('stopButton').disabled = true;
    }

    function updateTimer() {
      const now = new Date();
      const totalElapsed = elapsedTime + (now - startTime);
      const hours = Math.floor(totalElapsed / (1000 * 60 * 60));
      const minutes = Math.floor((totalElapsed % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((totalElapsed % (1000 * 60)) / 1000);
      
      document.getElementById('timerDisplay').textContent = 
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    async function salvarApontamento(event) {
      event.preventDefault();

      try {
        const formData = new FormData(event.target);
        
        const apontamentoData = {
          ordemId: formData.get('ordemId'),
          funcionarioId: formData.get('funcionario'),
          tempoGasto: parseFloat(formData.get('tempoGasto')),
          status: formData.get('statusApontamento'),
          tipoServico: formData.get('tipoServico'),
          servicosRealizados: formData.get('servicosRealizados'),
          pecasUtilizadas: formData.get('pecasUtilizadas'),
          observacoes: formData.get('observacoes'),
          dataApontamento: serverTimestamp()
        };

        // Salvar apontamento
        await addDoc(collection(db, "apontamentosManutencao"), apontamentoData);

        // Atualizar status da ordem
        await updateDoc(doc(db, "ordensManutencao", apontamentoData.ordemId), {
          status: apontamentoData.status,
          ultimoApontamento: serverTimestamp()
        });

        showNotification("Apontamento salvo com sucesso!", "success");
        closeModal();
        loadOrdens();
      } catch (error) {
        console.error("Erro ao salvar apontamento:", error);
        showNotification("Erro ao salvar apontamento", "error");
      }
    }

    function showNotification(message, type) {
      const notification = document.getElementById('notification');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      notification.style.display = 'block';

      setTimeout(() => {
        notification.style.display = 'none';
      }, 3000);
    }

    function filtrarOrdens() {
      const status = document.getElementById('statusFilter').value;
      const prioridade = document.getElementById('prioridadeFilter').value;
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();

      const ordensFiltradas = ordensList.filter(ordem => {
        const matchStatus = !status || ordem.status === status;
        const matchPrioridade = !prioridade || ordem.prioridade === prioridade;
        const matchSearch = !searchTerm || 
          ordem.codigo.toLowerCase().includes(searchTerm) ||
          (ordem.recursoNome || '').toLowerCase().includes(searchTerm);

        return matchStatus && matchPrioridade && matchSearch;
      });

      updateOrdersTable(ordensFiltradas);
    }

    document.addEventListener('DOMContentLoaded', () => {
      loadOrdens();
      loadFuncionarios();

      document.getElementById('statusFilter').addEventListener('change', filtrarOrdens);
      document.getElementById('prioridadeFilter').addEventListener('change', filtrarOrdens);
      document.getElementById('searchInput').addEventListener('input', filtrarOrdens);
      document.getElementById('apontamentoForm').addEventListener('submit', salvarApontamento);

      // Close modal when clicking outside
      window.onclick = function(event) {
        const modal = document.getElementById('apontamentoModal');
        if (event.target === modal) {
          closeModal();
        }
      };
    });
  </script>
</body>
</html> 