<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCQ002 - Cotações com Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e67e22, #f39c12);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .quality-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content {
            padding: 30px;
        }

        .quality-info {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border: 2px solid #f39c12;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .quality-info h3 {
            color: #e67e22;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quality-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .quality-feature {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .quality-feature h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quality-feature p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .filters-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .filters-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group select,
        .filter-group input {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #f39c12;
            box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
        }

        .homologation-alert {
            background: #fff3cd;
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }

        .homologation-alert.active {
            display: block;
        }

        .homologation-alert h4 {
            color: #856404;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .homologation-list {
            display: grid;
            gap: 8px;
        }

        .homologation-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid;
        }

        .homologation-item.approved {
            border-left-color: #27ae60;
        }

        .homologation-item.pending {
            border-left-color: #f39c12;
        }

        .homologation-item.rejected {
            border-left-color: #e74c3c;
        }

        .homologation-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-nova {
            background: #fff3cd;
            color: #856404;
        }

        .status-enviada {
            background: #cce5ff;
            color: #004085;
        }

        .status-respondida {
            background: #d4edda;
            color: #155724;
        }

        .status-vencida {
            background: #f8d7da;
            color: #721c24;
        }

        .quality-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .quality-required {
            background: #fff3cd;
            color: #856404;
        }

        .quality-optional {
            background: #e2e3e5;
            color: #383d41;
        }

        .supplier-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #f39c12;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .solicitacoes-list {
            display: grid;
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .solicitacao-item {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .solicitacao-item:hover {
            border-color: #f39c12;
        }

        .solicitacao-checkbox {
            display: none;
        }

        .solicitacao-checkbox:checked + .solicitacao-label {
            border-color: #f39c12;
            background: #fef9e7;
        }

        .solicitacao-label {
            display: block;
            padding: 15px;
            cursor: pointer;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sol-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .sol-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .sol-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }

            .quality-features {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .sol-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="quality-badge">
                <i class="fas fa-shield-alt"></i>
                Processo com Qualidade
            </div>
            <h1>💰 PCQ002 - Cotações</h1>
            <p>Cotações integradas com controle de qualidade e homologação</p>
        </div>

        <div class="content">
            <!-- Informações sobre Qualidade -->
            <div class="quality-info">
                <h3><i class="fas fa-award"></i> Funcionalidades de Qualidade Integradas</h3>
                <div class="quality-features">
                    <div class="quality-feature">
                        <h4><i class="fas fa-user-check"></i> Verificação de Homologação</h4>
                        <p>Apenas fornecedores homologados podem participar das cotações</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-certificate"></i> Certificações Obrigatórias</h4>
                        <p>Verificação automática de certificações requeridas</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-clipboard-check"></i> Especificações Técnicas</h4>
                        <p>Cotação baseada em especificações detalhadas de qualidade</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-chart-line"></i> Histórico de Performance</h4>
                        <p>Consideração do histórico de qualidade dos fornecedores</p>
                    </div>
                </div>
            </div>

            <!-- Alertas -->
            <div id="alertContainer"></div>

            <!-- Alerta de Homologação -->
            <div class="homologation-alert" id="homologationAlert">
                <h4><i class="fas fa-exclamation-triangle"></i> Status de Homologação dos Fornecedores</h4>
                <div class="homologation-list" id="homologationList">
                    <!-- Lista será preenchida dinamicamente -->
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters-section">
                <h3><i class="fas fa-filter"></i> Filtros e Busca</h3>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="statusFilter">
                            <i class="fas fa-flag"></i>
                            Status da Cotação
                        </label>
                        <select id="statusFilter">
                            <option value="">Todos os Status</option>
                            <option value="NOVA">Nova</option>
                            <option value="ENVIADA">Enviada</option>
                            <option value="RESPONDIDA">Respondida</option>
                            <option value="VENCIDA">Vencida</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="solicitacaoFilter">
                            <i class="fas fa-hashtag"></i>
                            Número da Solicitação
                        </label>
                        <input type="text" id="solicitacaoFilter" placeholder="Ex: SOL-123456">
                    </div>
                    <div class="filter-group">
                        <label for="fornecedorFilter">
                            <i class="fas fa-building"></i>
                            Fornecedor
                        </label>
                        <select id="fornecedorFilter">
                            <option value="">Todos os Fornecedores</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="homologacaoFilter">
                            <i class="fas fa-user-check"></i>
                            Status Homologação
                        </label>
                        <select id="homologacaoFilter">
                            <option value="">Todos</option>
                            <option value="HOMOLOGADO">Homologados</option>
                            <option value="PENDENTE">Pendentes</option>
                            <option value="SUSPENSO">Suspensos</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="dataInicio">
                            <i class="fas fa-calendar"></i>
                            Data Início
                        </label>
                        <input type="date" id="dataInicio">
                    </div>
                    <div class="filter-group">
                        <label for="dataFim">
                            <i class="fas fa-calendar"></i>
                            Data Fim
                        </label>
                        <input type="date" id="dataFim">
                    </div>
                </div>
                
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        <i class="fas fa-search"></i> Buscar Cotações
                    </button>
                    <button class="btn btn-secondary" onclick="limparFiltros()">
                        <i class="fas fa-eraser"></i> Limpar Filtros
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div class="actions">
                <button class="btn btn-success" onclick="novaCotacao()">
                    <i class="fas fa-plus"></i> Nova Cotação
                </button>
                <button class="btn btn-warning" onclick="importarSolicitacoes()">
                    <i class="fas fa-download"></i> Importar Solicitações
                </button>
                <button class="btn btn-primary" onclick="verificarHomologacoes()">
                    <i class="fas fa-user-check"></i> Verificar Homologações
                </button>
                <button class="btn btn-danger" onclick="relatorioCotacoes()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando cotações...</p>
            </div>

            <!-- Tabela de Cotações -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Solicitação</th>
                            <th>Fornecedor</th>
                            <th>Homologação</th>
                            <th>Itens</th>
                            <th>Qualidade</th>
                            <th>Data Envio</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-file-invoice-dollar"></i>
                <h3>Nenhuma cotação encontrada</h3>
                <p>Não há cotações cadastradas ou que atendam aos filtros selecionados.</p>
                <button class="btn btn-primary" onclick="novaCotacao()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Criar Primeira Cotação
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let cotacoes = [];
        let fornecedores = [];
        let homologacoes = [];
        let solicitacoes = [];
        let parametrosQualidade = {};

        // Inicializar página
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarParametrosQualidade();
            await carregarDados();
            popularFiltros();
            renderizarTabela();
        });

        // Carregar parâmetros de qualidade
        async function carregarParametrosQualidade() {
            try {
                const doc = await db.collection('parametros').doc('sistema').get();
                if (doc.exists) {
                    parametrosQualidade = doc.data();
                    console.log('✅ PCQ002 - Parâmetros de qualidade carregados:', parametrosQualidade);

                    // Verificar se módulo está ativo
                    if (!parametrosQualidade.moduloQualidadeAtivo) {
                        mostrarAlerta('warning', '⚠️ Módulo de qualidade não está ativo. Redirecionando para versão padrão...');
                        setTimeout(() => {
                            window.location.href = 'cotacoes/index.html';
                        }, 3000);
                        return;
                    }
                }
            } catch (error) {
                console.error('❌ Erro ao carregar parâmetros:', error);
                mostrarAlerta('danger', 'Erro ao carregar configurações de qualidade');
            }
        }

        // Carregar todos os dados necessários
        async function carregarDados() {
            try {
                mostrarLoading(true);

                const [cotacoesSnap, fornecedoresSnap, homologacoesSnap, solicitacoesSnap] = await Promise.all([
                    getDocs(collection(db, "cotacoes")),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "homologacoesFornecedores")),
                    getDocs(collection(db, "solicitacoesCompras"))
                ]);

                cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                homologacoes = homologacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PCQ002 - Dados carregados:', {
                    cotacoes: cotacoes.length,
                    fornecedores: fornecedores.length,
                    homologacoes: homologacoes.length,
                    solicitacoes: solicitacoes.length
                });

                // Verificar homologações se módulo estiver ativo
                if (parametrosQualidade.homologacaoFornecedores) {
                    verificarStatusHomologacoes();
                }

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                mostrarAlerta('danger', 'Erro ao carregar dados das cotações');
                mostrarLoading(false);
            }
        }

        // Verificar status de homologações
        function verificarStatusHomologacoes() {
            const statusHomologacao = [];

            fornecedores.forEach(fornecedor => {
                const homologacao = homologacoes.find(h => h.fornecedorId === fornecedor.id);

                statusHomologacao.push({
                    fornecedor: fornecedor.razaoSocial || fornecedor.nome,
                    status: homologacao?.status || 'PENDENTE',
                    dataVencimento: homologacao?.dataVencimento,
                    score: homologacao?.score || 0
                });
            });

            renderizarStatusHomologacao(statusHomologacao);
        }

        // Renderizar status de homologação
        function renderizarStatusHomologacao(status) {
            const container = document.getElementById('homologationList');
            const alert = document.getElementById('homologationAlert');

            if (status.length === 0) {
                alert.style.display = 'none';
                return;
            }

            // Verificar se há fornecedores não homologados
            const naoHomologados = status.filter(s => s.status !== 'HOMOLOGADO');

            if (naoHomologados.length > 0) {
                alert.classList.add('active');
            }

            container.innerHTML = status.map(item => {
                const statusClass = item.status.toLowerCase();
                const isVencido = item.dataVencimento && new Date(item.dataVencimento.seconds * 1000) < new Date();

                return `
                    <div class="homologation-item ${statusClass}">
                        <div>
                            <strong>${item.fornecedor}</strong>
                            ${item.score > 0 ? `<span style="margin-left: 10px; color: #7f8c8d;">Score: ${item.score}%</span>` : ''}
                            ${isVencido ? '<span style="color: #e74c3c; margin-left: 10px;">(VENCIDO)</span>' : ''}
                        </div>
                        <span class="homologation-status status-${statusClass}">
                            ${item.status}
                        </span>
                    </div>
                `;
            }).join('');
        }

        // Popular filtros
        function popularFiltros() {
            const fornecedorSelect = document.getElementById('fornecedorFilter');
            fornecedorSelect.innerHTML = '<option value="">Todos os Fornecedores</option>';

            fornecedores.forEach(fornecedor => {
                const option = document.createElement('option');
                option.value = fornecedor.id;
                option.textContent = fornecedor.razaoSocial || fornecedor.nome;
                fornecedorSelect.appendChild(option);
            });
        }

        // Renderizar tabela de cotações
        function renderizarTabela() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (cotacoes.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = cotacoes.map(cotacao => {
                const fornecedor = fornecedores.find(f => f.id === cotacao.fornecedorId);
                const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
                const homologacao = homologacoes.find(h => h.fornecedorId === cotacao.fornecedorId);

                const dataEnvio = cotacao.dataEnvio ?
                    new Date(cotacao.dataEnvio.seconds * 1000).toLocaleDateString() : 'N/A';

                const statusHomologacao = homologacao?.status || 'PENDENTE';
                const isHomologado = statusHomologacao === 'HOMOLOGADO';

                // Verificar se tem processo de qualidade
                const temQualidade = cotacao.processoQualidade?.ativo ||
                                   solicitacao?.processoQualidade?.ativo || false;

                return `
                    <tr>
                        <td><strong>${cotacao.numero || 'N/A'}</strong></td>
                        <td>${solicitacao?.numero || 'N/A'}</td>
                        <td>
                            ${fornecedor?.razaoSocial || 'N/A'}
                            ${!isHomologado ? '<br><small style="color: #e74c3c;">⚠️ Não homologado</small>' : ''}
                        </td>
                        <td>
                            <span class="homologation-status status-${statusHomologacao.toLowerCase()}">
                                ${statusHomologacao}
                            </span>
                        </td>
                        <td>${cotacao.itens?.length || 0} itens</td>
                        <td>
                            <span class="quality-indicator ${temQualidade ? 'quality-required' : 'quality-optional'}">
                                <i class="fas ${temQualidade ? 'fa-shield-alt' : 'fa-info-circle'}"></i>
                                ${temQualidade ? 'Qualidade' : 'Padrão'}
                            </span>
                        </td>
                        <td>${dataEnvio}</td>
                        <td>
                            <span class="status-badge status-${cotacao.status?.toLowerCase() || 'nova'}">
                                ${cotacao.status || 'NOVA'}
                            </span>
                        </td>
                        <td>
                            <div class="supplier-actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarCotacao('${cotacao.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${cotacao.status === 'NOVA' ? `
                                    <button class="btn btn-warning btn-action" onclick="enviarCotacao('${cotacao.id}')" title="Enviar">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                ` : ''}
                                ${cotacao.status === 'RESPONDIDA' ? `
                                    <button class="btn btn-success btn-action" onclick="analisarResposta('${cotacao.id}')" title="Analisar">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                ` : ''}
                                ${!isHomologado && parametrosQualidade.homologacaoFornecedores ? `
                                    <button class="btn btn-danger btn-action" onclick="iniciarHomologacao('${cotacao.fornecedorId}')" title="Homologar">
                                        <i class="fas fa-user-plus"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Aplicar filtros
        window.aplicarFiltros = function() {
            console.log('🔍 Aplicando filtros...');
            // TODO: Implementar lógica de filtros
            mostrarAlerta('success', 'Filtros aplicados com sucesso');
        };

        // Limpar filtros
        window.limparFiltros = function() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('solicitacaoFilter').value = '';
            document.getElementById('fornecedorFilter').value = '';
            document.getElementById('homologacaoFilter').value = '';
            document.getElementById('dataInicio').value = '';
            document.getElementById('dataFim').value = '';

            mostrarAlerta('success', 'Filtros limpos');
        };

        // Nova cotação
        window.novaCotacao = function() {
            // Verificar se há fornecedores homologados
            if (parametrosQualidade.homologacaoFornecedores) {
                const fornecedoresHomologados = homologacoes.filter(h => h.status === 'HOMOLOGADO');

                if (fornecedoresHomologados.length === 0) {
                    mostrarAlerta('warning', '⚠️ Não há fornecedores homologados disponíveis. Inicie o processo de homologação primeiro.');
                    return;
                }
            }

            alert('📝 Funcionalidade em desenvolvimento: Nova Cotação com Qualidade');
        };

        // Importar solicitações
        window.importarSolicitacoes = function() {
            // Filtrar solicitações com processo de qualidade
            const solicitacoesQualidade = solicitacoes.filter(s => s.processoQualidade?.ativo);

            if (solicitacoesQualidade.length === 0) {
                mostrarAlerta('warning', 'Não há solicitações com processo de qualidade para importar');
                return;
            }

            mostrarModalImportacao(solicitacoesQualidade);
        };

        // Mostrar modal de importação
        function mostrarModalImportacao(solicitacoesDisponiveis) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-download"></i> Importar Solicitações</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>Selecione as solicitações que deseja importar para cotação:</p>
                        <div class="solicitacoes-list">
                            ${solicitacoesDisponiveis.map(sol => `
                                <div class="solicitacao-item">
                                    <input type="checkbox" id="sol_${sol.id}" class="solicitacao-checkbox" checked>
                                    <label for="sol_${sol.id}" class="solicitacao-label">
                                        <div class="sol-header">
                                            <strong>${sol.numero}</strong>
                                            <span class="sol-status status-${sol.status?.toLowerCase()}">${sol.status}</span>
                                        </div>
                                        <div class="sol-details">
                                            <div>Solicitante: ${sol.solicitante}</div>
                                            <div>Departamento: ${sol.departamento}</div>
                                            <div>Itens: ${sol.itens?.length || 0}</div>
                                            <div>Prioridade: ${sol.prioridade}</div>
                                        </div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button class="btn btn-success" onclick="confirmarImportacao()">
                            <i class="fas fa-check"></i> Importar Selecionadas
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Confirmar importação
        window.confirmarImportacao = async function() {
            const checkboxes = document.querySelectorAll('.solicitacao-checkbox:checked');
            const solicitacoesSelecionadas = Array.from(checkboxes).map(cb =>
                cb.id.replace('sol_', '')
            );

            if (solicitacoesSelecionadas.length === 0) {
                mostrarAlerta('warning', 'Selecione pelo menos uma solicitação para importar');
                return;
            }

            try {
                mostrarLoading(true);

                for (const solicitacaoId of solicitacoesSelecionadas) {
                    const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
                    if (solicitacao) {
                        await criarCotacaoAutomatica(solicitacao);
                    }
                }

                mostrarAlerta('success', `✅ ${solicitacoesSelecionadas.length} cotações criadas com sucesso!`);

                // Recarregar dados
                await carregarDados();
                renderizarTabela();

                fecharModal();
                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao importar solicitações:', error);
                mostrarAlerta('danger', 'Erro ao importar solicitações');
                mostrarLoading(false);
            }
        };

        // Criar cotação automática
        async function criarCotacaoAutomatica(solicitacao) {
            const novaCotacao = {
                numero: 'COT-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5),
                solicitacaoId: solicitacao.id,
                solicitacaoNumero: solicitacao.numero,
                dataCotacao: Timestamp.now(),
                dataLimite: solicitacao.dataLimite,
                status: 'RASCUNHO',
                itens: solicitacao.itens || [],

                // 🔍 PROCESSO DE QUALIDADE TRANSFERIDO
                processoQualidade: {
                    ativo: true,
                    versao: 'PCQ002',
                    origem: 'PCQ001',
                    requisitos: solicitacao.processoQualidade?.requisitos || {},
                    nivelQualidade: solicitacao.processoQualidade?.nivelQualidade || 'BASICO',
                    certificacaoRequerida: solicitacao.processoQualidade?.certificacaoRequerida || ''
                },

                dataCriacao: Timestamp.now(),
                usuarioCriacao: 'usuario_atual'
            };

            await addDoc(collection(db, "cotacoes"), novaCotacao);
            console.log('✅ Cotação criada automaticamente:', novaCotacao.numero);
        }

        // Fechar modal
        window.fecharModal = function() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        };

        // Verificar homologações
        window.verificarHomologacoes = function() {
            verificarStatusHomologacoes();
            mostrarAlerta('success', '✅ Status de homologações atualizado');
        };

        // Relatório de cotações
        window.relatorioCotacoes = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório de Cotações com Qualidade');
        };

        // Visualizar cotação
        window.visualizarCotacao = function(id) {
            alert('👁️ Funcionalidade em desenvolvimento: Visualizar Cotação ' + id);
        };

        // Enviar cotação
        window.enviarCotacao = function(id) {
            const cotacao = cotacoes.find(c => c.id === id);
            const fornecedor = fornecedores.find(f => f.id === cotacao.fornecedorId);
            const homologacao = homologacoes.find(h => h.fornecedorId === cotacao.fornecedorId);

            // Verificar homologação se obrigatória
            if (parametrosQualidade.homologacaoFornecedores && homologacao?.status !== 'HOMOLOGADO') {
                mostrarAlerta('danger', `❌ Fornecedor ${fornecedor?.razaoSocial} não está homologado. Não é possível enviar cotação.`);
                return;
            }

            alert(`📤 Funcionalidade em desenvolvimento: Enviar Cotação ${id} para ${fornecedor?.razaoSocial}`);
        };

        // Analisar resposta
        window.analisarResposta = function(id) {
            alert('📈 Funcionalidade em desenvolvimento: Analisar Resposta ' + id);
        };

        // Iniciar homologação
        window.iniciarHomologacao = function(fornecedorId) {
            const fornecedor = fornecedores.find(f => f.id === fornecedorId);

            if (confirm(`Deseja iniciar o processo de homologação para ${fornecedor?.razaoSocial}?`)) {
                // Redirecionar para PQ005
                window.location.href = `PQ005-homologacao-fornecedores.html?fornecedor=${fornecedorId}`;
            }
        };

        // Mostrar alerta
        function mostrarAlerta(tipo, mensagem) {
            const container = document.getElementById('alertContainer');
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.innerHTML = `
                <i class="fas fa-${tipo === 'success' ? 'check-circle' : tipo === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${mensagem}
            `;

            container.innerHTML = '';
            container.appendChild(alerta);
            alerta.style.display = 'block';

            // Auto-hide após 5 segundos
            setTimeout(() => {
                alerta.style.display = 'none';
            }, 5000);
        }

        // Mostrar/esconder loading
        function mostrarLoading(mostrar) {
            document.getElementById('loading').style.display = mostrar ? 'block' : 'none';
        }

        console.log('✅ PCQ002 - Cotações com Qualidade inicializada');
    </script>
</body>
</html>
