<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Ordens de Manutenção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
      --status-pending: #ffd700;
      --status-in-progress: #1e90ff;
      --status-completed: #32cd32;
      --status-cancelled: #dc143c;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
    }

    .form-container {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-warning {
      background-color: var(--warning-color);
      color: white;
    }

    .btn-warning:hover {
      background-color: #d66800;
    }

    .table-container {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
      margin-top: 20px;
    }

    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .orders-table th,
    .orders-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .orders-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
      cursor: pointer;
    }

    .orders-table tr:hover {
      background-color: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      display: inline-block;
      min-width: 100px;
    }

    .status-pending {
      background-color: #fff3cd;
      color: #856404;
    }

    .status-in-progress {
      background-color: #cce5ff;
      color: #004085;
    }

    .status-completed {
      background-color: #d4edda;
      color: #155724;
    }

    .status-cancelled {
      background-color: #f8d7da;
      color: #721c24;
    }

    .priority-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .priority-high {
      background-color: #f8d7da;
      color: #721c24;
    }

    .priority-medium {
      background-color: #fff3cd;
      color: #856404;
    }

    .priority-low {
      background-color: #d4edda;
      color: #155724;
    }

    .priority-badge.alta {
      background-color: #dc3545;
      color: white;
    }

    .priority-badge.media {
      background-color: #ffc107;
      color: #212529;
    }

    .priority-badge.baixa {
      background-color: #28a745;
      color: white;
    }

    /* Estilos para botões de ação */
    .btn-action {
      background: none;
      border: none;
      padding: 6px 8px;
      margin: 0 2px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .btn-action:hover {
      transform: scale(1.1);
    }

    .btn-action.edit {
      color: #007bff;
    }

    .btn-action.edit:hover {
      background-color: #e3f2fd;
    }

    .btn-action.start {
      color: #28a745;
    }

    .btn-action.start:hover {
      background-color: #d4edda;
    }

    .btn-action.complete {
      color: #17a2b8;
    }

    .btn-action.complete:hover {
      background-color: #d1ecf1;
    }

    .btn-action.delete {
      color: #dc3545;
    }

    .btn-action.delete:hover {
      background-color: #f8d7da;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 4px;
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      display: none;
      align-items: center;
      gap: 10px;
      transition: opacity 0.3s ease;
    }

    .notification-success { background-color: var(--success-color); }
    .notification-error { background-color: var(--danger-color); }
    .notification-warning { background-color: var(--warning-color); }
    .notification-icon { font-weight: bold; font-size: 18px; }

    .search-container {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .search-container input {
      width: 300px;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .filter-container {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .filter-container select {
      width: 200px;
    }

    @media (max-width: 768px) {
      .container {
        width: 100%;
        margin: 0;
        border-radius: 0;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .search-container {
        flex-direction: column;
        align-items: stretch;
      }

      .search-container input {
        width: 100%;
      }

      .filter-container {
        flex-direction: column;
      }

      .filter-container select {
        width: 100%;
      }

      .orders-table {
        font-size: 12px;
      }

      .orders-table th,
      .orders-table td {
        padding: 8px;
      }
    }

    .required::after {
      content: "*";
      color: var(--danger-color);
      margin-left: 4px;
    }

    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Ordens de Manutenção</h1>
      <div>
        <button class="btn btn-primary" onclick="showNewOrderForm()">
          <i class="fas fa-plus"></i> Nova Ordem
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div id="notification" class="notification"></div>

    <!-- Formulário de Nova Ordem (inicialmente oculto) -->
    <div id="orderFormContainer" class="form-container" style="display: none;">
      <h2 class="form-title">Nova Ordem de Manutenção</h2>
      <form id="orderForm">
        <input type="hidden" id="editingId">

        <!-- Dados Básicos -->
        <div class="form-row">
          <div class="form-col">
            <label for="recurso" class="required">Recurso</label>
            <select id="recurso" name="recurso" required>
              <option value="">Selecione um recurso...</option>
            </select>
          </div>
          <div class="form-col">
            <label for="tipoOrdem" class="required">Tipo de Ordem</label>
            <select id="tipoOrdem" name="tipoOrdem" required>
              <option value="">Selecione o tipo...</option>
              <option value="PREVENTIVA">Preventiva</option>
              <option value="CORRETIVA">Corretiva</option>
              <option value="PREDITIVA">Preditiva</option>
            </select>
          </div>
          <div class="form-col">
            <label for="prioridade" class="required">Prioridade</label>
            <select id="prioridade" name="prioridade" required>
              <option value="BAIXA">Baixa</option>
              <option value="MEDIA">Média</option>
              <option value="ALTA">Alta</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="fornecedor" class="required">Fornecedor</label>
            <select id="fornecedor" name="fornecedor" required>
              <option value="">Selecione um fornecedor...</option>
            </select>
          </div>
          <div class="form-col">
            <label for="dataPrevista" class="required">Data Prevista</label>
            <input type="date" id="dataPrevista" name="dataPrevista" required>
          </div>
          <div class="form-col">
            <label for="horaPrevista">Hora Prevista</label>
            <input type="time" id="horaPrevista" name="horaPrevista">
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="descricao" class="required">Descrição do Serviço</label>
            <textarea id="descricao" name="descricao" required></textarea>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="observacoes">Observações</label>
            <textarea id="observacoes" name="observacoes"></textarea>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn btn-secondary" onclick="hideOrderForm()">Cancelar</button>
          <button type="submit" class="btn btn-success" id="submitButton">Criar Ordem</button>
        </div>
      </form>
    </div>

    <!-- Lista de Ordens -->
    <div class="table-container">
      <h2 class="form-title">Ordens de Manutenção</h2>

      <div class="search-container">
        <input type="text" id="searchInput" placeholder="Pesquisar por código, recurso, fornecedor...">
        <div class="filter-container">
          <select id="statusFilter" aria-label="Filtrar por status">
            <option value="">Todos os Status</option>
            <option value="PENDENTE">Pendente</option>
            <option value="EM_ANDAMENTO">Em Andamento</option>
            <option value="CONCLUIDA">Concluída</option>
            <option value="CANCELADA">Cancelada</option>
          </select>
          <select id="tipoFilter" aria-label="Filtrar por tipo de ordem">
            <option value="">Todos os Tipos</option>
            <option value="PREVENTIVA">Preventiva</option>
            <option value="CORRETIVA">Corretiva</option>
            <option value="PREDITIVA">Preditiva</option>
          </select>
          <select id="prioridadeFilter" aria-label="Filtrar por prioridade">
            <option value="">Todas as Prioridades</option>
            <option value="BAIXA">Baixa</option>
            <option value="MEDIA">Média</option>
            <option value="ALTA">Alta</option>
          </select>
        </div>
      </div>

      <table class="orders-table">
        <thead>
          <tr>
            <th>Código</th>
            <th>Recurso</th>
            <th>Tipo</th>
            <th>Fornecedor</th>
            <th>Status</th>
            <th>Prioridade</th>
            <th>Data Prevista</th>
            <th>Data Início</th>
            <th>Data Conclusão</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="ordersTableBody">
        </tbody>
      </table>
    </div>
  </div>

  <!-- Funções globais -->
  <script>
    // Funções globais para ações de ordem
    window.showNewOrderForm = function() {
      const formContainer = document.getElementById('orderFormContainer');
      formContainer.style.display = 'block';
      document.getElementById('orderForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('submitButton').textContent = 'Criar Ordem';
    };

    window.hideOrderForm = function() {
      const formContainer = document.getElementById('orderFormContainer');
      formContainer.style.display = 'none';
      document.getElementById('orderForm').reset();
    };

    window.editarOrdem = function(ordemId) {
      const ordem = ordensList.find(o => o.id === ordemId);
      if (!ordem) {
        showNotification("Ordem não encontrada", "error");
        return;
      }

      // Mostrar formulário
      showNewOrderForm();

      // Preencher campos
      document.getElementById('editingId').value = ordemId;
      document.getElementById('recurso').value = ordem.recursoId;
      document.getElementById('tipoOrdem').value = ordem.tipoOrdem;
      document.getElementById('prioridade').value = ordem.prioridade || 'MEDIA';
      document.getElementById('fornecedor').value = ordem.fornecedorId;

      // Converter timestamp para formato de input date
      if (ordem.dataPrevista) {
        const date = ordem.dataPrevista.toDate ? ordem.dataPrevista.toDate() : new Date(ordem.dataPrevista);
        document.getElementById('dataPrevista').value = date.toISOString().split('T')[0];
      }

      if (ordem.horaPrevista) {
        document.getElementById('horaPrevista').value = ordem.horaPrevista;
      }

      document.getElementById('descricao').value = ordem.descricao || '';
      document.getElementById('observacoes').value = ordem.observacoes || '';

      // Alterar texto do botão
      document.getElementById('submitButton').textContent = 'Atualizar Ordem';

      // Carregar fornecedores do recurso
      if (ordem.recursoId) {
        loadFornecedoresRecurso(ordem.recursoId);
      }
    };

    window.showNotification = function(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;

      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    };
  </script>

  <script type="module">
    // Importações do Firebase
    import { db } from './firebase-config.js';
    import { 
      collection,
      addDoc,
      getDocs,
      doc,
      updateDoc,
      deleteDoc,
      query,
      where,
      serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // Variáveis globais
    let recursosList = [];
    let fornecedoresList = [];
    let ordensList = [];

    // Função para carregar recursos
    async function loadRecursos() {
      try {
        console.log('Iniciando carregamento de recursos...');
        const recursosRef = collection(db, "recursos");
        console.log('Referência da coleção criada');

        const q = query(recursosRef, where("status", "==", "ATIVO"));
        console.log('Query criada:', q);

        const querySnapshot = await getDocs(q);
        console.log('Query executada, documentos encontrados:', querySnapshot.size);

        recursosList = querySnapshot.docs.map(doc => {
          const data = doc.data();
          console.log('Recurso encontrado:', { id: doc.id, ...data });
          return {
            id: doc.id,
            ...data
          };
        }).sort((a, b) => a.nome.localeCompare(b.nome));

        console.log('Lista de recursos processada:', recursosList);

        const select = document.getElementById('recurso');
        if (!select) {
          console.error('Elemento select#recurso não encontrado!');
          return;
        }

        select.innerHTML = '<option value="">Selecione um recurso</option>' +
          recursosList.map(recurso => 
            `<option value="${recurso.id}">${recurso.nome}</option>`
          ).join('');

        console.log('Select atualizado com', recursosList.length, 'recursos');
      } catch (error) {
        console.error("Erro detalhado ao carregar recursos:", error);
        console.error("Stack trace:", error.stack);
        showNotification("Erro ao carregar recursos", "error");
      }
    }

    // Função para carregar fornecedores
    async function loadFornecedoresRecurso(recursoId) {
      try {
        const recurso = recursosList.find(r => r.id === recursoId);
        if (!recurso?.fornecedoresManutencao) {
          document.getElementById('fornecedor').innerHTML = '<option value="">Nenhum fornecedor disponível</option>';
          return;
        }

        const fornecedoresRef = collection(db, "fornecedores");
        const q = query(fornecedoresRef, where("status", "==", "ATIVO"));
        const querySnapshot = await getDocs(q);

        fornecedoresList = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        const fornecedoresRecurso = fornecedoresList.filter(f => 
          recurso.fornecedoresManutencao.some(fm => fm.id === f.id)
        );

        const select = document.getElementById('fornecedor');
        select.innerHTML = '<option value="">Selecione um fornecedor</option>' +
          fornecedoresRecurso.map(fornecedor => 
            `<option value="${fornecedor.id}">${fornecedor.nomeFantasia || fornecedor.razaoSocial}</option>`
          ).join('');
      } catch (error) {
        console.error("Erro ao carregar fornecedores:", error);
        showNotification("Erro ao carregar fornecedores", "error");
      }
    }

    // Função para carregar ordens
    async function loadOrdens() {
      try {
        const ordensRef = collection(db, "ordensManutencao");
        const querySnapshot = await getDocs(ordensRef);

        ordensList = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        updateOrdensTable(ordensList);
      } catch (error) {
        console.error("Erro ao carregar ordens:", error);
        showNotification("Erro ao carregar ordens", "error");
      }
    }

    // Função para atualizar a tabela
    function updateOrdensTable(ordens) {
      const tbody = document.querySelector('#ordersTableBody');
      tbody.innerHTML = ordens.map(ordem => `
        <tr>
          <td>${ordem.codigo}</td>
          <td>${getRecursoNome(ordem.recursoId)}</td>
          <td>${ordem.tipoOrdem}</td>
          <td>${getFornecedorNome(ordem.fornecedorId)}</td>
          <td><span class="status-badge ${ordem.status.toLowerCase()}">${ordem.status}</span></td>
          <td><span class="priority-badge ${(ordem.prioridade || 'MEDIA').toLowerCase()}">${ordem.prioridade || 'MÉDIA'}</span></td>
          <td>${formatDate(ordem.dataPrevista)}</td>
          <td>${formatDate(ordem.dataInicio)}</td>
          <td>${formatDate(ordem.dataConclusao)}</td>
          <td>
            ${ordem.status === 'PENDENTE' ? `
              <button onclick="editarOrdem('${ordem.id}')" class="btn-action edit" title="Editar ordem">
                <i class="fas fa-edit"></i>
              </button>
              <button onclick="iniciarOrdem('${ordem.id}')" class="btn-action start" title="Iniciar ordem">
                <i class="fas fa-play"></i>
              </button>
            ` : ordem.status === 'EM_ANDAMENTO' ? `
              <button onclick="concluirOrdem('${ordem.id}')" class="btn-action complete" title="Concluir ordem">
                <i class="fas fa-check"></i>
              </button>
            ` : ''}
            <button onclick="excluirOrdem('${ordem.id}')" class="btn-action delete" title="Excluir ordem">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        </tr>
      `).join('');
    }

    // Funções auxiliares
    function getRecursoNome(recursoId) {
      return recursosList.find(r => r.id === recursoId)?.nome || 'Recurso não encontrado';
    }

    function getFornecedorNome(fornecedorId) {
      return fornecedoresList.find(f => f.id === fornecedorId)?.nomeFantasia || 'Fornecedor não encontrado';
    }

    function formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = timestamp.toDate();
      return date.toLocaleDateString('pt-BR');
    }

    // Função para criar/editar ordem
    async function criarOrdem(event) {
      event.preventDefault();

      try {
        const formData = new FormData(event.target);
        const editingId = formData.get('editingId');

        const ordemData = {
          recursoId: formData.get('recurso'),
          tipoOrdem: formData.get('tipoOrdem'),
          prioridade: formData.get('prioridade'),
          fornecedorId: formData.get('fornecedor'),
          dataPrevista: new Date(formData.get('dataPrevista')),
          horaPrevista: formData.get('horaPrevista'),
          descricao: formData.get('descricao'),
          observacoes: formData.get('observacoes')
        };

        if (editingId) {
          // Editar ordem existente
          ordemData.dataUltimaAlteracao = serverTimestamp();
          await updateDoc(doc(db, "ordensManutencao", editingId), ordemData);
          showNotification("Ordem atualizada com sucesso", "success");
        } else {
          // Criar nova ordem
          ordemData.codigo = `OM${new Date().getTime().toString().slice(-6)}`;
          ordemData.status = 'PENDENTE';
          ordemData.dataCriacao = serverTimestamp();
          await addDoc(collection(db, "ordensManutencao"), ordemData);
          showNotification("Ordem criada com sucesso", "success");
        }

        event.target.reset();
        hideOrderForm();
        loadOrdens();
      } catch (error) {
        console.error("Erro ao salvar ordem:", error);
        showNotification("Erro ao salvar ordem", "error");
      }
    }

    // Funções de ação
    async function iniciarOrdem(ordemId) {
      try {
        await updateDoc(doc(db, "ordensManutencao", ordemId), {
          status: 'EM_ANDAMENTO',
          dataInicio: serverTimestamp()
        });
        showNotification("Ordem iniciada", "success");
        loadOrdens();
      } catch (error) {
        console.error("Erro ao iniciar ordem:", error);
        showNotification("Erro ao iniciar ordem", "error");
      }
    }

    async function concluirOrdem(ordemId) {
      try {
        await updateDoc(doc(db, "ordensManutencao", ordemId), {
          status: 'CONCLUIDA',
          dataConclusao: serverTimestamp()
        });
        showNotification("Ordem concluída", "success");
        loadOrdens();
      } catch (error) {
        console.error("Erro ao concluir ordem:", error);
        showNotification("Erro ao concluir ordem", "error");
      }
    }

    async function excluirOrdem(ordemId) {
      if (!confirm('Tem certeza que deseja excluir esta ordem?')) return;

      try {
        await deleteDoc(doc(db, "ordensManutencao", ordemId));
        showNotification("Ordem excluída", "success");
        loadOrdens();
      } catch (error) {
        console.error("Erro ao excluir ordem:", error);
        showNotification("Erro ao excluir ordem", "error");
      }
    }

    // Funções de filtro e busca
    function filtrarOrdens() {
      const status = document.getElementById('statusFilter').value;
      const tipo = document.getElementById('tipoFilter').value;
      const prioridade = document.getElementById('prioridadeFilter').value;
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();

      const ordensFiltradas = ordensList.filter(ordem => {
        const matchStatus = !status || ordem.status === status;
        const matchTipo = !tipo || ordem.tipoOrdem === tipo;
        const matchPrioridade = !prioridade || ordem.prioridade === prioridade;
        const matchSearch = !searchTerm || 
          ordem.codigo.toLowerCase().includes(searchTerm) ||
          getRecursoNome(ordem.recursoId).toLowerCase().includes(searchTerm) ||
          getFornecedorNome(ordem.fornecedorId).toLowerCase().includes(searchTerm);

        return matchStatus && matchTipo && matchPrioridade && matchSearch;
      });

      updateOrdensTable(ordensFiltradas);
    }

    // Event Listeners
    document.addEventListener('DOMContentLoaded', () => {
      loadRecursos();
      loadOrdens();

      // Event listeners para filtros
      document.getElementById('statusFilter').addEventListener('change', filtrarOrdens);
      document.getElementById('tipoFilter').addEventListener('change', filtrarOrdens);
      document.getElementById('prioridadeFilter').addEventListener('change', filtrarOrdens);
      document.getElementById('searchInput').addEventListener('input', filtrarOrdens);

      // Event listener para mudança de recurso
      document.getElementById('recurso').addEventListener('change', (e) => {
        loadFornecedoresRecurso(e.target.value);
      });

      // Event listener para formulário
      document.getElementById('orderForm').addEventListener('submit', criarOrdem);
    });

    // Atribuir as funções de ordem ao objeto window
    window.iniciarOrdem = iniciarOrdem;
    window.concluirOrdem = concluirOrdem;
    window.excluirOrdem = excluirOrdem;
  </script>
</body>
</html> 