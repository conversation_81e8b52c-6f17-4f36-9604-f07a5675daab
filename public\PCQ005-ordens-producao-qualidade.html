<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCQ005 - Ordens de Produção com Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .quality-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content {
            padding: 30px;
        }

        .quality-info {
            background: linear-gradient(135deg, #ecf0f1, #d5dbdb);
            border: 2px solid #34495e;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .quality-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quality-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .quality-feature {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #34495e;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .quality-feature h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quality-feature p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .workflow-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .workflow-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
        }

        .workflow-step {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
            border-top: 4px solid #34495e;
        }

        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #bdc3c7;
        }

        .workflow-step:last-child::after {
            display: none;
        }

        .workflow-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #34495e;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5em;
        }

        .workflow-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .workflow-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .quality-checkpoints {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .quality-checkpoints h3 {
            color: #27ae60;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkpoints-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .checkpoint {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #ddd;
            transition: all 0.3s ease;
        }

        .checkpoint:hover {
            border-color: #27ae60;
            transform: translateY(-2px);
        }

        .checkpoint.required {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .checkpoint.optional {
            border-color: #f39c12;
            background: #fef9e7;
        }

        .checkpoint h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkpoint p {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 15px;
        }

        .checkpoint-features {
            list-style: none;
        }

        .checkpoint-features li {
            color: #27ae60;
            font-size: 0.8em;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #34495e;
            box-shadow: 0 0 0 3px rgba(52, 73, 94, 0.1);
        }

        .form-group.required label::after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-planejada {
            background: #e2e3e5;
            color: #383d41;
        }

        .status-iniciada {
            background: #cce5ff;
            color: #004085;
        }

        .status-producao {
            background: #fff3cd;
            color: #856404;
        }

        .status-inspecao {
            background: #f8d7da;
            color: #721c24;
        }

        .status-finalizada {
            background: #d4edda;
            color: #155724;
        }

        .quality-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .quality-active {
            background: #d4edda;
            color: #155724;
        }

        .quality-inactive {
            background: #e2e3e5;
            color: #383d41;
        }

        .checkpoint-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .checkpoint-pending {
            background: #fff3cd;
            color: #856404;
        }

        .checkpoint-approved {
            background: #d4edda;
            color: #155724;
        }

        .checkpoint-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .item-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .quality-features {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                grid-template-columns: 1fr;
            }
            
            .checkpoints-grid {
                grid-template-columns: 1fr;
            }
            
            .workflow-step::after {
                content: '↓';
                right: 50%;
                top: auto;
                bottom: -15px;
                transform: translateX(50%);
            }
            
            .workflow-step:last-child::after {
                display: none;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="quality-badge">
                <i class="fas fa-shield-alt"></i>
                Processo com Qualidade
            </div>
            <h1>🏭 PCQ005 - Ordens de Produção</h1>
            <p>Ordens de produção integradas com controle de qualidade</p>
        </div>

        <div class="content">
            <!-- Informações sobre Qualidade -->
            <div class="quality-info">
                <h3><i class="fas fa-award"></i> Funcionalidades de Qualidade Integradas</h3>
                <div class="quality-features">
                    <div class="quality-feature">
                        <h4><i class="fas fa-clipboard-check"></i> Pontos de Controle</h4>
                        <p>Inspeções programadas em etapas críticas da produção</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-search"></i> Inspeção de Processo</h4>
                        <p>Verificação contínua da qualidade durante a produção</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-check-circle"></i> Liberação Controlada</h4>
                        <p>Aprovação obrigatória antes de avançar para próxima etapa</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-chart-line"></i> Rastreabilidade</h4>
                        <p>Controle completo de lotes e materiais utilizados</p>
                    </div>
                </div>
            </div>

            <!-- Fluxo do Processo -->
            <div class="workflow-section">
                <h3><i class="fas fa-sitemap"></i> Fluxo da Produção com Qualidade</h3>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="workflow-title">1. Início</div>
                        <div class="workflow-description">Liberação de materiais e início da produção</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="workflow-title">2. Produção</div>
                        <div class="workflow-description">Execução com pontos de controle</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="workflow-title">3. Inspeção</div>
                        <div class="workflow-description">Verificação de qualidade em processo</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="workflow-title">4. Montagem</div>
                        <div class="workflow-description">Montagem com controle de qualidade</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="workflow-title">5. Acabamento</div>
                        <div class="workflow-description">Finalização e inspeção final</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="workflow-title">6. Liberação</div>
                        <div class="workflow-description">Aprovação final e liberação</div>
                    </div>
                </div>
            </div>

            <!-- Pontos de Controle de Qualidade -->
            <div class="quality-checkpoints">
                <h3><i class="fas fa-map-marker-alt"></i> Pontos de Controle de Qualidade</h3>
                <div class="checkpoints-grid">
                    <div class="checkpoint required">
                        <h4><i class="fas fa-exclamation-triangle"></i> Controles Obrigatórios</h4>
                        <p>Pontos de controle que devem ser aprovados obrigatoriamente</p>
                        <ul class="checkpoint-features">
                            <li><i class="fas fa-check"></i> Liberação de materiais</li>
                            <li><i class="fas fa-check"></i> Inspeção de processo</li>
                            <li><i class="fas fa-check"></i> Controle dimensional</li>
                            <li><i class="fas fa-check"></i> Inspeção final</li>
                        </ul>
                    </div>

                    <div class="checkpoint optional">
                        <h4><i class="fas fa-info-circle"></i> Controles Opcionais</h4>
                        <p>Pontos de controle configuráveis conforme necessidade</p>
                        <ul class="checkpoint-features">
                            <li><i class="fas fa-check"></i> Controle de temperatura</li>
                            <li><i class="fas fa-check"></i> Teste de funcionamento</li>
                            <li><i class="fas fa-check"></i> Verificação de acabamento</li>
                            <li><i class="fas fa-check"></i> Controle de embalagem</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Alertas -->
            <div id="alertContainer"></div>

            <!-- Ações -->
            <div class="actions">
                <button class="btn btn-success" onclick="novaOrdem()">
                    <i class="fas fa-plus"></i> Nova Ordem
                </button>
                <button class="btn btn-warning" onclick="importarPedidos()">
                    <i class="fas fa-download"></i> Importar Pedidos
                </button>
                <button class="btn btn-primary" onclick="consultarInspecoes()">
                    <i class="fas fa-search"></i> Consultar Inspeções
                </button>
                <button class="btn btn-danger" onclick="relatorioProducao()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando ordens de produção...</p>
            </div>

            <!-- Tabela de Ordens -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Produto</th>
                            <th>Quantidade</th>
                            <th>Data Início</th>
                            <th>Data Prevista</th>
                            <th>Qualidade</th>
                            <th>Pontos Controle</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-industry"></i>
                <h3>Nenhuma ordem de produção encontrada</h3>
                <p>Não há ordens de produção cadastradas no momento.</p>
                <button class="btn btn-primary" onclick="novaOrdem()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Criar Primeira Ordem
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, addDoc, getDocs, query, where, orderBy, Timestamp, updateDoc, doc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let ordens = [];
        let produtos = [];
        let parametrosQualidade = {};

        // Inicializar página
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarParametrosQualidade();
            await carregarDados();
            renderizarTabela();
        });

        // Carregar parâmetros de qualidade
        async function carregarParametrosQualidade() {
            try {
                const doc = await db.collection('parametros').doc('sistema').get();
                if (doc.exists) {
                    parametrosQualidade = doc.data();
                    console.log('✅ PCQ005 - Parâmetros de qualidade carregados:', parametrosQualidade);

                    // Verificar se módulo está ativo
                    if (!parametrosQualidade.moduloQualidadeAtivo) {
                        mostrarAlerta('warning', '⚠️ Módulo de qualidade não está ativo. Redirecionando para versão padrão...');
                        setTimeout(() => {
                            window.location.href = 'ordens_producao.html';
                        }, 3000);
                        return;
                    }
                }
            } catch (error) {
                console.error('❌ Erro ao carregar parâmetros:', error);
                mostrarAlerta('danger', 'Erro ao carregar configurações de qualidade');
            }
        }

        // Carregar todos os dados necessários
        async function carregarDados() {
            try {
                mostrarLoading(true);

                const [ordensSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "produtos"))
                ]);

                ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PCQ005 - Dados carregados:', {
                    ordens: ordens.length,
                    produtos: produtos.length
                });

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                mostrarAlerta('danger', 'Erro ao carregar dados das ordens de produção');
                mostrarLoading(false);
            }
        }

        // Renderizar tabela de ordens
        function renderizarTabela() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (ordens.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = ordens.map(ordem => {
                const produto = produtos.find(p => p.id === ordem.produtoId);
                const dataInicio = ordem.dataInicio ?
                    new Date(ordem.dataInicio.seconds * 1000).toLocaleDateString() : 'N/A';
                const dataPrevista = ordem.dataPrevista ?
                    new Date(ordem.dataPrevista.seconds * 1000).toLocaleDateString() : 'N/A';

                // Verificar se tem processo de qualidade
                const temQualidade = ordem.processoQualidade?.ativo || false;

                // Calcular status dos pontos de controle
                const pontosControle = ordem.pontosControle || [];
                const pontosAprovados = pontosControle.filter(p => p.status === 'APROVADO').length;
                const totalPontos = pontosControle.length;

                let statusPontos = 'checkpoint-pending';
                let textoPontos = `${pontosAprovados}/${totalPontos}`;

                if (totalPontos > 0) {
                    if (pontosAprovados === totalPontos) {
                        statusPontos = 'checkpoint-approved';
                    } else if (pontosControle.some(p => p.status === 'REJEITADO')) {
                        statusPontos = 'checkpoint-rejected';
                    }
                }

                return `
                    <tr>
                        <td><strong>${ordem.numero || 'N/A'}</strong></td>
                        <td>${produto?.nome || 'N/A'}</td>
                        <td>${ordem.quantidade || 0} ${ordem.unidade || 'UN'}</td>
                        <td>${dataInicio}</td>
                        <td>${dataPrevista}</td>
                        <td>
                            <span class="quality-indicator ${temQualidade ? 'quality-active' : 'quality-inactive'}">
                                <i class="fas ${temQualidade ? 'fa-shield-alt' : 'fa-info-circle'}"></i>
                                ${temQualidade ? 'Ativo' : 'Padrão'}
                            </span>
                        </td>
                        <td>
                            <span class="checkpoint-indicator ${statusPontos}">
                                <i class="fas ${statusPontos === 'checkpoint-approved' ? 'fa-check-circle' : statusPontos === 'checkpoint-rejected' ? 'fa-times-circle' : 'fa-clock'}"></i>
                                ${textoPontos}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${ordem.status?.toLowerCase() || 'planejada'}">
                                ${ordem.status || 'PLANEJADA'}
                            </span>
                        </td>
                        <td>
                            <div class="item-actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarOrdem('${ordem.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${ordem.status === 'PLANEJADA' ? `
                                    <button class="btn btn-success btn-action" onclick="iniciarProducao('${ordem.id}')" title="Iniciar">
                                        <i class="fas fa-play"></i>
                                    </button>
                                ` : ''}
                                ${ordem.status === 'PRODUCAO' && temQualidade ? `
                                    <button class="btn btn-warning btn-action" onclick="verificarPontos('${ordem.id}')" title="Pontos Controle">
                                        <i class="fas fa-clipboard-check"></i>
                                    </button>
                                ` : ''}
                                ${temQualidade ? `
                                    <button class="btn btn-secondary btn-action" onclick="verInspecoes('${ordem.id}')" title="Inspeções">
                                        <i class="fas fa-search"></i>
                                    </button>
                                ` : ''}
                                ${ordem.status === 'PRODUCAO' ? `
                                    <button class="btn btn-danger btn-action" onclick="finalizarOrdem('${ordem.id}')" title="Finalizar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Nova ordem
        window.novaOrdem = function() {
            // Verificar se há produtos cadastrados
            if (produtos.length === 0) {
                mostrarAlerta('warning', '⚠️ Não há produtos cadastrados para criar ordens de produção.');
                return;
            }

            criarNovaOrdem();
        };

        // Criar nova ordem
        async function criarNovaOrdem() {
            try {
                mostrarLoading(true);

                // Simular criação de ordem
                const novaOrdem = {
                    numero: 'OP-' + Date.now(),
                    produtoId: 'produto_exemplo', // TODO: Implementar seleção de produto
                    quantidade: 100,
                    unidade: 'UN',
                    dataInicio: Timestamp.now(),
                    dataPrevista: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 dias
                    status: 'PLANEJADA',

                    // 🔍 PROCESSO DE QUALIDADE
                    processoQualidade: {
                        ativo: parametrosQualidade.moduloQualidadeAtivo,
                        versao: 'PCQ005',
                        inspecaoProcesso: parametrosQualidade.inspecaoProcesso || false,
                        pontosControleObrigatorios: parametrosQualidade.pontosControleObrigatorios || false
                    },

                    // Pontos de controle baseados nos parâmetros
                    pontosControle: [],

                    dataCriacao: Timestamp.now(),
                    usuarioCriacao: 'usuario_atual'
                };

                // 🔍 LÓGICA ESPECÍFICA DE QUALIDADE
                if (parametrosQualidade.moduloQualidadeAtivo) {
                    // Criar pontos de controle automáticos
                    await criarPontosControle(novaOrdem);
                }

                await addDoc(collection(db, "ordensProducao"), novaOrdem);

                mostrarAlerta('success', `🚀 Ordem de produção criada com sucesso! ${parametrosQualidade.moduloQualidadeAtivo ? 'Pontos de controle de qualidade programados.' : ''}`);

                // Recarregar dados
                await carregarDados();
                renderizarTabela();

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao criar ordem:', error);
                mostrarAlerta('danger', 'Erro ao criar ordem de produção');
                mostrarLoading(false);
            }
        }

        // Criar pontos de controle
        async function criarPontosControle(ordem) {
            const pontosControle = [
                {
                    etapa: 'INICIO',
                    nome: 'Liberação de Materiais',
                    obrigatorio: true,
                    status: 'PENDENTE',
                    ordem: 1
                },
                {
                    etapa: 'PRODUCAO',
                    nome: 'Inspeção de Processo',
                    obrigatorio: parametrosQualidade.inspecaoProcesso || false,
                    status: 'AGUARDANDO',
                    ordem: 2
                },
                {
                    etapa: 'MONTAGEM',
                    nome: 'Controle Dimensional',
                    obrigatorio: false,
                    status: 'AGUARDANDO',
                    ordem: 3
                },
                {
                    etapa: 'ACABAMENTO',
                    nome: 'Inspeção Final',
                    obrigatorio: true,
                    status: 'AGUARDANDO',
                    ordem: 4
                }
            ];

            ordem.pontosControle = pontosControle;

            // Criar inspeções de processo se necessário
            if (parametrosQualidade.inspecaoProcesso) {
                await criarInspecoesProcesso(ordem);
            }
        }

        // Criar inspeções de processo
        async function criarInspecoesProcesso(ordem) {
            try {
                const inspecoesProcesso = ordem.pontosControle
                    .filter(ponto => ponto.obrigatorio)
                    .map(ponto => ({
                        codigo: `INS-PROC-${ordem.numero}-${ponto.etapa}`,
                        tipo: 'PROCESSO',
                        ordemProducaoId: ordem.id,
                        produtoId: ordem.produtoId,
                        etapa: ponto.etapa,
                        nome: ponto.nome,
                        status: 'PROGRAMADA',
                        prioridade: 'MEDIA',
                        dataProgramada: ordem.dataInicio,
                        responsavel: 'equipe_qualidade',
                        dataCriacao: Timestamp.now()
                    }));

                for (const inspecao of inspecoesProcesso) {
                    await addDoc(collection(db, "inspecoesProcesso"), inspecao);
                }

                console.log('✅ Inspeções de processo criadas:', inspecoesProcesso.length);

            } catch (error) {
                console.error('❌ Erro ao criar inspeções de processo:', error);
            }
        }

        // Iniciar produção
        window.iniciarProducao = async function(id) {
            if (confirm('Iniciar a produção desta ordem?')) {
                try {
                    await updateDoc(doc(db, "ordensProducao", id), {
                        status: 'INICIADA',
                        dataInicioReal: Timestamp.now(),
                        usuarioInicio: 'usuario_atual'
                    });

                    // Se tem qualidade, ativar primeiro ponto de controle
                    const ordem = ordens.find(o => o.id === id);
                    if (ordem?.processoQualidade?.ativo) {
                        await ativarProximoPontoControle(id);
                    }

                    mostrarAlerta('success', '✅ Produção iniciada com sucesso');
                    await carregarDados();
                    renderizarTabela();

                } catch (error) {
                    console.error('❌ Erro ao iniciar produção:', error);
                    mostrarAlerta('danger', 'Erro ao iniciar produção');
                }
            }
        };

        // Ativar próximo ponto de controle
        async function ativarProximoPontoControle(ordemId) {
            try {
                const ordem = ordens.find(o => o.id === ordemId);
                if (!ordem || !ordem.pontosControle) return;

                const proximoPonto = ordem.pontosControle.find(p => p.status === 'PENDENTE');
                if (proximoPonto) {
                    proximoPonto.status = 'ATIVO';
                    proximoPonto.dataAtivacao = Timestamp.now();

                    await updateDoc(doc(db, "ordensProducao", ordemId), {
                        pontosControle: ordem.pontosControle,
                        status: 'PRODUCAO'
                    });

                    console.log('✅ Ponto de controle ativado:', proximoPonto.nome);
                }

            } catch (error) {
                console.error('❌ Erro ao ativar ponto de controle:', error);
            }
        }

        // Verificar pontos de controle
        window.verificarPontos = function(id) {
            // Redirecionar para PQ002 com filtro da ordem
            window.location.href = `PQ002-inspecao-processo.html?ordem=${id}`;
        };

        // Ver inspeções
        window.verInspecoes = function(id) {
            // Redirecionar para PQ002 com filtro da ordem
            window.location.href = `PQ002-inspecao-processo.html?ordem=${id}&tipo=PROCESSO`;
        };

        // Finalizar ordem
        window.finalizarOrdem = async function(id) {
            const ordem = ordens.find(o => o.id === id);

            // Verificar pontos de controle se tem qualidade
            if (ordem?.processoQualidade?.ativo) {
                const pontosObrigatorios = ordem.pontosControle?.filter(p => p.obrigatorio) || [];
                const pontosAprovados = pontosObrigatorios.filter(p => p.status === 'APROVADO');

                if (pontosAprovados.length < pontosObrigatorios.length) {
                    mostrarAlerta('warning', '⚠️ Nem todos os pontos de controle obrigatórios foram aprovados. Não é possível finalizar a ordem.');
                    return;
                }
            }

            if (confirm('Finalizar esta ordem de produção?')) {
                try {
                    await updateDoc(doc(db, "ordensProducao", id), {
                        status: 'FINALIZADA',
                        dataFinalizacao: Timestamp.now(),
                        usuarioFinalizacao: 'usuario_atual'
                    });

                    mostrarAlerta('success', '✅ Ordem de produção finalizada com sucesso');
                    await carregarDados();
                    renderizarTabela();

                } catch (error) {
                    console.error('❌ Erro ao finalizar ordem:', error);
                    mostrarAlerta('danger', 'Erro ao finalizar ordem');
                }
            }
        };

        // Importar pedidos
        window.importarPedidos = function() {
            alert('📥 Funcionalidade em desenvolvimento: Importar pedidos de venda para produção');
        };

        // Consultar inspeções
        window.consultarInspecoes = function() {
            // Redirecionar para PQ002 com filtro de processo
            window.location.href = 'PQ002-inspecao-processo.html?tipo=PROCESSO';
        };

        // Relatório de produção
        window.relatorioProducao = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório de Produção com Qualidade');
        };

        // Visualizar ordem
        window.visualizarOrdem = function(id) {
            alert('👁️ Funcionalidade em desenvolvimento: Visualizar Ordem ' + id);
        };

        // Mostrar alerta
        function mostrarAlerta(tipo, mensagem) {
            const container = document.getElementById('alertContainer');
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.innerHTML = `
                <i class="fas fa-${tipo === 'success' ? 'check-circle' : tipo === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${mensagem}
            `;

            container.innerHTML = '';
            container.appendChild(alerta);
            alerta.style.display = 'block';

            // Auto-hide após 5 segundos
            setTimeout(() => {
                alerta.style.display = 'none';
            }, 5000);
        }

        // Mostrar/esconder loading
        function mostrarLoading(mostrar) {
            document.getElementById('loading').style.display = mostrar ? 'block' : 'none';
        }

        console.log('✅ PCQ005 - Ordens de Produção com Qualidade inicializada');
    </script>
</body>
</html>
