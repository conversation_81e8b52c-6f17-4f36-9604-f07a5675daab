<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCQ003 - Pedidos de Compra com Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .quality-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content {
            padding: 30px;
        }

        .quality-info {
            background: linear-gradient(135deg, #f3e5f5, #e1bee7);
            border: 2px solid #9b59b6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .quality-info h3 {
            color: #8e44ad;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quality-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .quality-feature {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #9b59b6;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .quality-feature h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quality-feature p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .workflow-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .workflow-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .workflow-step {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
            border-top: 4px solid #9b59b6;
        }

        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #bdc3c7;
        }

        .workflow-step:last-child::after {
            display: none;
        }

        .workflow-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #9b59b6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5em;
        }

        .workflow-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .workflow-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #9b59b6;
            box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
        }

        .form-group.required label::after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
        }

        .quality-config {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .quality-config h4 {
            color: #27ae60;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }

        .config-checkbox {
            width: 18px;
            height: 18px;
        }

        .config-label {
            color: #2c3e50;
            font-size: 0.9em;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-rascunho {
            background: #e2e3e5;
            color: #383d41;
        }

        .status-enviado {
            background: #cce5ff;
            color: #004085;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .quality-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .quality-active {
            background: #d4edda;
            color: #155724;
        }

        .quality-inactive {
            background: #e2e3e5;
            color: #383d41;
        }

        .destination-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .dest-qualidade {
            background: #fff3cd;
            color: #856404;
        }

        .dest-estoque {
            background: #cce5ff;
            color: #004085;
        }

        .item-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 900px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #9b59b6;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .cotacoes-list, .pedido-details {
            display: grid;
            gap: 15px;
        }

        .cotacao-item {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .cotacao-item:hover {
            border-color: #9b59b6;
        }

        .cotacao-checkbox {
            display: none;
        }

        .cotacao-checkbox:checked + .cotacao-label {
            border-color: #9b59b6;
            background: #f3e5f5;
        }

        .cotacao-label {
            display: block;
            padding: 15px;
            cursor: pointer;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .cot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .cot-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .cot-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .detail-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
        }

        .detail-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .detail-grid div {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .quality-section {
            background: #f0f8f0;
            border-color: #27ae60;
        }

        .quality-config-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .config-item.active {
            background: #d4edda;
            color: #155724;
        }

        .config-item.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .destino-info {
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #9b59b6;
        }

        .itens-list {
            display: grid;
            gap: 10px;
        }

        .item-detail {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #9b59b6;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .item-qty {
            background: #9b59b6;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .item-description {
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .item-price {
            color: #27ae60;
            font-weight: 600;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .quality-features {
                grid-template-columns: 1fr;
            }

            .workflow-steps {
                grid-template-columns: 1fr;
            }

            .workflow-step::after {
                content: '↓';
                right: 50%;
                top: auto;
                bottom: -15px;
                transform: translateX(50%);
            }

            .workflow-step:last-child::after {
                display: none;
            }

            .actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .cot-details, .detail-grid, .quality-config-display {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="quality-badge">
                <i class="fas fa-shield-alt"></i>
                Processo com Qualidade
            </div>
            <h1>🛒 PCQ003 - Pedidos de Compra</h1>
            <p>Pedidos de compra integrados com controle de qualidade</p>
        </div>

        <div class="content">
            <!-- Informações sobre Qualidade -->
            <div class="quality-info">
                <h3><i class="fas fa-award"></i> Funcionalidades de Qualidade Integradas</h3>
                <div class="quality-features">
                    <div class="quality-feature">
                        <h4><i class="fas fa-warehouse"></i> Destino Automático</h4>
                        <p>Configuração automática para Armazém de Qualidade baseada nos parâmetros</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-search"></i> Inspeção Programada</h4>
                        <p>Criação automática de inspeções de recebimento para todos os itens</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-barcode"></i> Rastreabilidade</h4>
                        <p>Controle de lotes e rastreabilidade completa desde o pedido</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-clipboard-check"></i> Especificações</h4>
                        <p>Transferência automática das especificações da solicitação original</p>
                    </div>
                </div>
            </div>

            <!-- Fluxo do Processo -->
            <div class="workflow-section">
                <h3><i class="fas fa-sitemap"></i> Fluxo do Pedido com Qualidade</h3>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="workflow-title">1. Criação</div>
                        <div class="workflow-description">Pedido criado com especificações de qualidade</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="workflow-title">2. Envio</div>
                        <div class="workflow-description">Enviado para fornecedor homologado</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="workflow-title">3. Recebimento</div>
                        <div class="workflow-description">Material direcionado para Armazém de Qualidade</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="workflow-title">4. Inspeção</div>
                        <div class="workflow-description">Inspeção automática baseada nas especificações</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="workflow-title">5. Liberação</div>
                        <div class="workflow-description">Aprovação e transferência para estoque principal</div>
                    </div>
                </div>
            </div>

            <!-- Alertas -->
            <div id="alertContainer"></div>

            <!-- Formulário de Novo Pedido -->
            <div class="form-section" id="formSection" style="display: none;">
                <h3><i class="fas fa-plus"></i> Novo Pedido de Compra</h3>
                
                <div class="form-grid">
                    <div class="form-group required">
                        <label for="numeroPedido">
                            <i class="fas fa-hashtag"></i>
                            Número do Pedido
                        </label>
                        <input type="text" id="numeroPedido" readonly>
                    </div>
                    <div class="form-group required">
                        <label for="cotacaoOrigem">
                            <i class="fas fa-file-invoice-dollar"></i>
                            Cotação de Origem
                        </label>
                        <select id="cotacaoOrigem" required>
                            <option value="">Selecione a cotação</option>
                        </select>
                    </div>
                    <div class="form-group required">
                        <label for="fornecedorPedido">
                            <i class="fas fa-building"></i>
                            Fornecedor
                        </label>
                        <input type="text" id="fornecedorPedido" readonly>
                    </div>
                    <div class="form-group required">
                        <label for="dataPedido">
                            <i class="fas fa-calendar"></i>
                            Data do Pedido
                        </label>
                        <input type="date" id="dataPedido" required>
                    </div>
                    <div class="form-group required">
                        <label for="dataEntrega">
                            <i class="fas fa-calendar-check"></i>
                            Data de Entrega
                        </label>
                        <input type="date" id="dataEntrega" required>
                    </div>
                    <div class="form-group">
                        <label for="condicoesPagamento">
                            <i class="fas fa-credit-card"></i>
                            Condições de Pagamento
                        </label>
                        <select id="condicoesPagamento">
                            <option value="30_DIAS">30 dias</option>
                            <option value="45_DIAS">45 dias</option>
                            <option value="60_DIAS">60 dias</option>
                            <option value="A_VISTA">À vista</option>
                        </select>
                    </div>
                </div>

                <!-- Configurações de Qualidade -->
                <div class="quality-config">
                    <h4><i class="fas fa-cogs"></i> Configurações de Qualidade</h4>
                    <div class="config-grid">
                        <div class="config-item">
                            <input type="checkbox" id="configInspecaoRecebimento" class="config-checkbox" checked disabled>
                            <label for="configInspecaoRecebimento" class="config-label">Inspeção de recebimento obrigatória</label>
                        </div>
                        <div class="config-item">
                            <input type="checkbox" id="configArmazemQualidade" class="config-checkbox" checked disabled>
                            <label for="configArmazemQualidade" class="config-label">Destino: Armazém de Qualidade</label>
                        </div>
                        <div class="config-item">
                            <input type="checkbox" id="configRastreabilidade" class="config-checkbox">
                            <label for="configRastreabilidade" class="config-label">Rastreabilidade de lote completa</label>
                        </div>
                        <div class="config-item">
                            <input type="checkbox" id="configCertificados" class="config-checkbox">
                            <label for="configCertificados" class="config-label">Certificados de qualidade obrigatórios</label>
                        </div>
                        <div class="config-item">
                            <input type="checkbox" id="configAnalises" class="config-checkbox">
                            <label for="configAnalises" class="config-label">Análises laboratoriais requeridas</label>
                        </div>
                        <div class="config-item">
                            <input type="checkbox" id="configQuarentena" class="config-checkbox">
                            <label for="configQuarentena" class="config-label">Período de quarentena obrigatório</label>
                        </div>
                    </div>
                </div>

                <div class="form-group" style="margin-top: 20px;">
                    <label for="observacoesPedido">
                        <i class="fas fa-comment"></i>
                        Observações do Pedido
                    </label>
                    <textarea id="observacoesPedido" rows="3" placeholder="Observações adicionais sobre o pedido..."></textarea>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-secondary" onclick="cancelarPedido()">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button class="btn btn-warning" onclick="salvarRascunhoPedido()">
                        <i class="fas fa-save"></i> Salvar Rascunho
                    </button>
                    <button class="btn btn-success" onclick="finalizarPedido()">
                        <i class="fas fa-check"></i> Finalizar Pedido
                    </button>
                </div>
            </div>

            <!-- Ações -->
            <div class="actions">
                <button class="btn btn-success" onclick="novoPedido()">
                    <i class="fas fa-plus"></i> Novo Pedido
                </button>
                <button class="btn btn-warning" onclick="importarCotacoes()">
                    <i class="fas fa-download"></i> Importar Cotações
                </button>
                <button class="btn btn-primary" onclick="relatorioPedidos()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-danger" onclick="monitorarEntregas()">
                    <i class="fas fa-truck"></i> Monitorar Entregas
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando pedidos de compra...</p>
            </div>

            <!-- Tabela de Pedidos -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Fornecedor</th>
                            <th>Data Pedido</th>
                            <th>Data Entrega</th>
                            <th>Valor Total</th>
                            <th>Qualidade</th>
                            <th>Destino</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-shopping-cart"></i>
                <h3>Nenhum pedido de compra encontrado</h3>
                <p>Não há pedidos de compra cadastrados no momento.</p>
                <button class="btn btn-primary" onclick="novoPedido()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Criar Primeiro Pedido
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, addDoc, getDocs, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let pedidos = [];
        let cotacoes = [];
        let fornecedores = [];
        let homologacoes = [];
        let parametrosQualidade = {};
        let pedidoAtual = null;

        // Inicializar página
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarParametrosQualidade();
            await carregarDados();
            renderizarTabela();
        });

        // Carregar parâmetros de qualidade
        async function carregarParametrosQualidade() {
            try {
                const doc = await db.collection('parametros').doc('sistema').get();
                if (doc.exists) {
                    parametrosQualidade = doc.data();
                    console.log('✅ PCQ003 - Parâmetros de qualidade carregados:', parametrosQualidade);

                    // Verificar se módulo está ativo
                    if (!parametrosQualidade.moduloQualidadeAtivo) {
                        mostrarAlerta('warning', '⚠️ Módulo de qualidade não está ativo. Redirecionando para versão padrão...');
                        setTimeout(() => {
                            window.location.href = 'pedidos_compra.html';
                        }, 3000);
                        return;
                    }
                }
            } catch (error) {
                console.error('❌ Erro ao carregar parâmetros:', error);
                mostrarAlerta('danger', 'Erro ao carregar configurações de qualidade');
            }
        }

        // Carregar todos os dados necessários
        async function carregarDados() {
            try {
                mostrarLoading(true);

                const [pedidosSnap, cotacoesSnap, fornecedoresSnap, homologacoesSnap] = await Promise.all([
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "cotacoes")),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "homologacoesFornecedores"))
                ]);

                pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                homologacoes = homologacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PCQ003 - Dados carregados:', {
                    pedidos: pedidos.length,
                    cotacoes: cotacoes.length,
                    fornecedores: fornecedores.length,
                    homologacoes: homologacoes.length
                });

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                mostrarAlerta('danger', 'Erro ao carregar dados dos pedidos');
                mostrarLoading(false);
            }
        }

        // Renderizar tabela de pedidos
        function renderizarTabela() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (pedidos.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = pedidos.map(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const dataPedido = pedido.dataPedido ?
                    new Date(pedido.dataPedido.seconds * 1000).toLocaleDateString() : 'N/A';
                const dataEntrega = pedido.dataEntrega ?
                    new Date(pedido.dataEntrega.seconds * 1000).toLocaleDateString() : 'N/A';

                // Verificar se tem processo de qualidade
                const temQualidade = pedido.processoQualidade?.ativo || false;
                const destino = pedido.destino || 'ESTOQUE_PRINCIPAL';
                const valorTotal = pedido.valorTotal || 0;

                return `
                    <tr>
                        <td><strong>${pedido.numero || 'N/A'}</strong></td>
                        <td>${fornecedor?.razaoSocial || 'N/A'}</td>
                        <td>${dataPedido}</td>
                        <td>${dataEntrega}</td>
                        <td>R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
                        <td>
                            <span class="quality-indicator ${temQualidade ? 'quality-active' : 'quality-inactive'}">
                                <i class="fas ${temQualidade ? 'fa-shield-alt' : 'fa-info-circle'}"></i>
                                ${temQualidade ? 'Ativo' : 'Padrão'}
                            </span>
                        </td>
                        <td>
                            <span class="destination-indicator ${destino === 'ARMAZEM_QUALIDADE' ? 'dest-qualidade' : 'dest-estoque'}">
                                <i class="fas ${destino === 'ARMAZEM_QUALIDADE' ? 'fa-warehouse' : 'fa-boxes'}"></i>
                                ${destino === 'ARMAZEM_QUALIDADE' ? 'Qualidade' : 'Estoque'}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${pedido.status?.toLowerCase() || 'rascunho'}">
                                ${pedido.status || 'RASCUNHO'}
                            </span>
                        </td>
                        <td>
                            <div class="item-actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarPedido('${pedido.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${pedido.status === 'RASCUNHO' ? `
                                    <button class="btn btn-warning btn-action" onclick="editarPedido('${pedido.id}')" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-success btn-action" onclick="enviarPedido('${pedido.id}')" title="Enviar">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                ` : ''}
                                ${pedido.status === 'ENVIADO' ? `
                                    <button class="btn btn-danger btn-action" onclick="acompanharEntrega('${pedido.id}')" title="Acompanhar">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                ` : ''}
                                ${temQualidade ? `
                                    <button class="btn btn-secondary btn-action" onclick="verInspecoes('${pedido.id}')" title="Inspeções">
                                        <i class="fas fa-search"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Novo pedido
        window.novoPedido = function() {
            // Verificar se há cotações aprovadas disponíveis
            const cotacoesAprovadas = cotacoes.filter(c => c.status === 'APROVADA' || c.status === 'RESPONDIDA');

            if (cotacoesAprovadas.length === 0) {
                mostrarAlerta('warning', '⚠️ Não há cotações aprovadas disponíveis para criar pedidos.');
                return;
            }

            // Verificar homologação se obrigatória
            if (parametrosQualidade.homologacaoFornecedores) {
                const fornecedoresHomologados = homologacoes.filter(h => h.status === 'HOMOLOGADO');

                if (fornecedoresHomologados.length === 0) {
                    mostrarAlerta('warning', '⚠️ Não há fornecedores homologados disponíveis.');
                    return;
                }
            }

            mostrarFormularioPedido();
            popularCotacoes();
            gerarNumeroPedido();
            definirDataAtual();
            configurarQualidade();
        };

        // Mostrar formulário de pedido
        function mostrarFormularioPedido() {
            document.getElementById('formSection').style.display = 'block';
            document.getElementById('tableContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
        }

        // Popular cotações disponíveis
        function popularCotacoes() {
            const select = document.getElementById('cotacaoOrigem');
            select.innerHTML = '<option value="">Selecione a cotação</option>';

            const cotacoesDisponiveis = cotacoes.filter(c =>
                c.status === 'APROVADA' || c.status === 'RESPONDIDA'
            );

            cotacoesDisponiveis.forEach(cotacao => {
                const fornecedor = fornecedores.find(f => f.id === cotacao.fornecedorId);
                const option = document.createElement('option');
                option.value = cotacao.id;
                option.textContent = `${cotacao.numero} - ${fornecedor?.razaoSocial || 'N/A'}`;
                option.dataset.fornecedorId = cotacao.fornecedorId;
                select.appendChild(option);
            });

            // Evento para atualizar fornecedor quando cotação for selecionada
            select.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.dataset.fornecedorId) {
                    const fornecedor = fornecedores.find(f => f.id === selectedOption.dataset.fornecedorId);
                    document.getElementById('fornecedorPedido').value = fornecedor?.razaoSocial || '';
                }
            });
        }

        // Gerar número do pedido
        function gerarNumeroPedido() {
            const numero = 'PC-' + Date.now();
            document.getElementById('numeroPedido').value = numero;
        }

        // Definir data atual
        function definirDataAtual() {
            const hoje = new Date().toISOString().split('T')[0];
            document.getElementById('dataPedido').value = hoje;

            // Data de entrega padrão: 15 dias
            const dataEntrega = new Date();
            dataEntrega.setDate(dataEntrega.getDate() + 15);
            document.getElementById('dataEntrega').value = dataEntrega.toISOString().split('T')[0];
        }

        // Configurar qualidade baseado nos parâmetros
        function configurarQualidade() {
            // Configurações obrigatórias baseadas nos parâmetros
            document.getElementById('configInspecaoRecebimento').checked = parametrosQualidade.inspecaoRecebimento || false;
            document.getElementById('configArmazemQualidade').checked = parametrosQualidade.armazemQualidade || false;
            document.getElementById('configRastreabilidade').checked = parametrosQualidade.rastreabilidadeLote || false;

            // Desabilitar configurações obrigatórias
            document.getElementById('configInspecaoRecebimento').disabled = parametrosQualidade.inspecaoRecebimento || false;
            document.getElementById('configArmazemQualidade').disabled = parametrosQualidade.armazemQualidade || false;
        }

        // Cancelar pedido
        window.cancelarPedido = function() {
            if (confirm('Deseja cancelar a criação do pedido? Todos os dados serão perdidos.')) {
                document.getElementById('formSection').style.display = 'none';
                renderizarTabela();
                pedidoAtual = null;
            }
        };

        // Salvar rascunho do pedido
        window.salvarRascunhoPedido = async function() {
            if (!validarFormularioPedido()) {
                return;
            }

            try {
                mostrarLoading(true);

                const pedido = coletarDadosPedido();
                pedido.status = 'RASCUNHO';

                await addDoc(collection(db, "pedidosCompra"), pedido);

                mostrarAlerta('success', '💾 Rascunho do pedido salvo com sucesso!');

                // Recarregar dados e voltar para lista
                await carregarDados();
                document.getElementById('formSection').style.display = 'none';
                renderizarTabela();

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao salvar rascunho:', error);
                mostrarAlerta('danger', 'Erro ao salvar rascunho do pedido');
                mostrarLoading(false);
            }
        };

        // Finalizar pedido
        window.finalizarPedido = async function() {
            if (!validarFormularioPedido()) {
                return;
            }

            // Verificar homologação do fornecedor se obrigatória
            const cotacaoId = document.getElementById('cotacaoOrigem').value;
            const cotacao = cotacoes.find(c => c.id === cotacaoId);

            if (parametrosQualidade.homologacaoFornecedores && cotacao) {
                const homologacao = homologacoes.find(h => h.fornecedorId === cotacao.fornecedorId);

                if (!homologacao || homologacao.status !== 'HOMOLOGADO') {
                    const fornecedor = fornecedores.find(f => f.id === cotacao.fornecedorId);
                    mostrarAlerta('danger', `❌ Fornecedor ${fornecedor?.razaoSocial} não está homologado. Não é possível finalizar o pedido.`);
                    return;
                }
            }

            try {
                mostrarLoading(true);

                const pedido = coletarDadosPedido();
                pedido.status = 'ENVIADO';
                pedido.dataEnvio = Timestamp.now();

                // 🔍 LÓGICA ESPECÍFICA DE QUALIDADE
                if (parametrosQualidade.moduloQualidadeAtivo) {
                    // Configurar destino baseado nos parâmetros
                    if (parametrosQualidade.armazemQualidade || parametrosQualidade.inspecaoRecebimento) {
                        pedido.destino = 'ARMAZEM_QUALIDADE';
                        pedido.requerInspecao = true;
                    } else {
                        pedido.destino = 'ESTOQUE_PRINCIPAL';
                        pedido.requerInspecao = false;
                    }

                    // Programar inspeções se necessário
                    if (parametrosQualidade.inspecaoRecebimento) {
                        await programarInspecoesRecebimento(pedido);
                    }
                }

                await addDoc(collection(db, "pedidosCompra"), pedido);

                mostrarAlerta('success', '🚀 Pedido finalizado e enviado com sucesso! Processo de qualidade ativado.');

                // Recarregar dados e voltar para lista
                await carregarDados();
                document.getElementById('formSection').style.display = 'none';
                renderizarTabela();

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao finalizar pedido:', error);
                mostrarAlerta('danger', 'Erro ao finalizar pedido');
                mostrarLoading(false);
            }
        };

        // Coletar dados do pedido
        function coletarDadosPedido() {
            const cotacaoId = document.getElementById('cotacaoOrigem').value;
            const cotacao = cotacoes.find(c => c.id === cotacaoId);

            return {
                numero: document.getElementById('numeroPedido').value,
                cotacaoId: cotacaoId,
                fornecedorId: cotacao?.fornecedorId,
                dataPedido: Timestamp.fromDate(new Date(document.getElementById('dataPedido').value)),
                dataEntrega: Timestamp.fromDate(new Date(document.getElementById('dataEntrega').value)),
                condicoesPagamento: document.getElementById('condicoesPagamento').value,
                observacoes: document.getElementById('observacoesPedido').value,

                // 🔍 PROCESSO DE QUALIDADE
                processoQualidade: {
                    ativo: true,
                    versao: 'PCQ003',
                    configuracoes: {
                        inspecaoRecebimento: document.getElementById('configInspecaoRecebimento').checked,
                        armazemQualidade: document.getElementById('configArmazemQualidade').checked,
                        rastreabilidade: document.getElementById('configRastreabilidade').checked,
                        certificados: document.getElementById('configCertificados').checked,
                        analises: document.getElementById('configAnalises').checked,
                        quarentena: document.getElementById('configQuarentena').checked
                    }
                },

                // Dados da cotação
                itens: cotacao?.itens || [],
                valorTotal: cotacao?.valorTotal || 0,

                dataCriacao: Timestamp.now(),
                usuarioCriacao: 'usuario_atual' // Pegar do sistema de auth
            };
        }

        // Programar inspeções de recebimento
        async function programarInspecoesRecebimento(pedido) {
            try {
                const inspecao = {
                    codigo: `INS-REC-${pedido.numero}`,
                    tipo: 'RECEBIMENTO',
                    pedidoId: pedido.id,
                    fornecedorId: pedido.fornecedorId,
                    itens: pedido.itens,
                    status: 'PROGRAMADA',
                    dataProgramada: pedido.dataEntrega,
                    configuracoes: pedido.processoQualidade.configuracoes,
                    dataCriacao: Timestamp.now()
                };

                await addDoc(collection(db, "inspecoesRecebimento"), inspecao);
                console.log('✅ Inspeção de recebimento programada:', inspecao.codigo);

            } catch (error) {
                console.error('❌ Erro ao programar inspeção:', error);
            }
        }

        // Validar formulário do pedido
        function validarFormularioPedido() {
            const campos = ['cotacaoOrigem', 'dataPedido', 'dataEntrega'];

            for (const campo of campos) {
                const elemento = document.getElementById(campo);
                if (!elemento.value.trim()) {
                    mostrarAlerta('warning', `Campo "${elemento.previousElementSibling.textContent}" é obrigatório`);
                    elemento.focus();
                    return false;
                }
            }

            return true;
        }

        // Importar cotações
        window.importarCotacoes = function() {
            const cotacoesDisponiveis = cotacoes.filter(c =>
                c.status === 'APROVADA' || c.status === 'RESPONDIDA'
            );

            if (cotacoesDisponiveis.length === 0) {
                mostrarAlerta('warning', 'Não há cotações aprovadas para importar');
                return;
            }

            mostrarModalImportacaoCotacoes(cotacoesDisponiveis);
        };

        // Mostrar modal de importação de cotações
        function mostrarModalImportacaoCotacoes(cotacoesDisponiveis) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-download"></i> Importar Cotações Aprovadas</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>Selecione as cotações aprovadas que deseja converter em pedidos de compra:</p>
                        <div class="cotacoes-list">
                            ${cotacoesDisponiveis.map(cot => {
                                const fornecedor = fornecedores.find(f => f.id === cot.fornecedorId);
                                return `
                                    <div class="cotacao-item">
                                        <input type="checkbox" id="cot_${cot.id}" class="cotacao-checkbox" checked>
                                        <label for="cot_${cot.id}" class="cotacao-label">
                                            <div class="cot-header">
                                                <strong>${cot.numero}</strong>
                                                <span class="cot-status status-${cot.status?.toLowerCase()}">${cot.status}</span>
                                            </div>
                                            <div class="cot-details">
                                                <div>Fornecedor: ${fornecedor?.razaoSocial || 'N/A'}</div>
                                                <div>Solicitação: ${cot.solicitacaoNumero || 'N/A'}</div>
                                                <div>Itens: ${cot.itens?.length || 0}</div>
                                                <div>Valor: R$ ${(cot.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                                            </div>
                                        </label>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button class="btn btn-success" onclick="confirmarImportacaoCotacoes()">
                            <i class="fas fa-check"></i> Criar Pedidos
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Confirmar importação de cotações
        window.confirmarImportacaoCotacoes = async function() {
            const checkboxes = document.querySelectorAll('.cotacao-checkbox:checked');
            const cotacoesSelecionadas = Array.from(checkboxes).map(cb =>
                cb.id.replace('cot_', '')
            );

            if (cotacoesSelecionadas.length === 0) {
                mostrarAlerta('warning', 'Selecione pelo menos uma cotação para importar');
                return;
            }

            try {
                mostrarLoading(true);

                for (const cotacaoId of cotacoesSelecionadas) {
                    const cotacao = cotacoes.find(c => c.id === cotacaoId);
                    if (cotacao) {
                        await criarPedidoAutomatico(cotacao);
                    }
                }

                mostrarAlerta('success', `✅ ${cotacoesSelecionadas.length} pedidos de compra criados com sucesso!`);

                // Recarregar dados
                await carregarDados();
                renderizarTabela();

                fecharModal();
                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao importar cotações:', error);
                mostrarAlerta('danger', 'Erro ao importar cotações');
                mostrarLoading(false);
            }
        };

        // Criar pedido automático
        async function criarPedidoAutomatico(cotacao) {
            const novoPedido = {
                numero: 'PC-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5),
                cotacaoId: cotacao.id,
                fornecedorId: cotacao.fornecedorId,
                dataPedido: Timestamp.now(),
                dataEntrega: Timestamp.fromDate(new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)), // 15 dias
                condicoesPagamento: '30_DIAS',
                itens: cotacao.itens || [],
                valorTotal: cotacao.valorTotal || 0,
                status: 'RASCUNHO',

                // 🔍 PROCESSO DE QUALIDADE TRANSFERIDO
                processoQualidade: {
                    ativo: true,
                    versao: 'PCQ003',
                    origem: 'PCQ002',
                    configuracoes: {
                        inspecaoRecebimento: parametrosQualidade.inspecaoRecebimento || false,
                        armazemQualidade: parametrosQualidade.armazemQualidade || false,
                        rastreabilidade: parametrosQualidade.rastreabilidadeLote || false,
                        certificados: cotacao.processoQualidade?.certificacaoRequerida || false,
                        analises: false,
                        quarentena: false
                    }
                },

                // Configurar destino baseado nos parâmetros
                destino: (parametrosQualidade.armazemQualidade || parametrosQualidade.inspecaoRecebimento) ?
                         'ARMAZEM_QUALIDADE' : 'ESTOQUE_PRINCIPAL',

                dataCriacao: Timestamp.now(),
                usuarioCriacao: 'usuario_atual'
            };

            await addDoc(collection(db, "pedidosCompra"), novoPedido);
            console.log('✅ Pedido criado automaticamente:', novoPedido.numero);
        }

        // Relatório de pedidos
        window.relatorioPedidos = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório de Pedidos com Qualidade');
        };

        // Monitorar entregas
        window.monitorarEntregas = function() {
            const pedidosEnviados = pedidos.filter(p => p.status === 'ENVIADO');

            if (pedidosEnviados.length === 0) {
                mostrarAlerta('warning', 'Não há pedidos enviados para monitorar');
                return;
            }

            alert(`🚚 Funcionalidade em desenvolvimento: Monitorar ${pedidosEnviados.length} entregas`);
        };

        // Visualizar pedido
        window.visualizarPedido = function(id) {
            const pedido = pedidos.find(p => p.id === id);
            if (!pedido) {
                mostrarAlerta('danger', 'Pedido não encontrado');
                return;
            }

            const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
            const cotacao = cotacoes.find(c => c.id === pedido.cotacaoId);

            mostrarModalVisualizacao(pedido, fornecedor, cotacao);
        };

        // Mostrar modal de visualização
        function mostrarModalVisualizacao(pedido, fornecedor, cotacao) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-eye"></i> Visualizar Pedido ${pedido.numero}</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="pedido-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-info-circle"></i> Dados Gerais</h4>
                                <div class="detail-grid">
                                    <div><strong>Número:</strong> ${pedido.numero}</div>
                                    <div><strong>Status:</strong> <span class="status-badge status-${pedido.status?.toLowerCase()}">${pedido.status}</span></div>
                                    <div><strong>Fornecedor:</strong> ${fornecedor?.razaoSocial || 'N/A'}</div>
                                    <div><strong>Cotação:</strong> ${cotacao?.numero || 'N/A'}</div>
                                    <div><strong>Data Pedido:</strong> ${pedido.dataPedido ? new Date(pedido.dataPedido.seconds * 1000).toLocaleDateString() : 'N/A'}</div>
                                    <div><strong>Data Entrega:</strong> ${pedido.dataEntrega ? new Date(pedido.dataEntrega.seconds * 1000).toLocaleDateString() : 'N/A'}</div>
                                    <div><strong>Condições:</strong> ${pedido.condicoesPagamento || 'N/A'}</div>
                                    <div><strong>Valor Total:</strong> R$ ${(pedido.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                                </div>
                            </div>

                            ${pedido.processoQualidade?.ativo ? `
                                <div class="detail-section quality-section">
                                    <h4><i class="fas fa-shield-alt"></i> Configurações de Qualidade</h4>
                                    <div class="quality-config-display">
                                        <div class="config-item ${pedido.processoQualidade.configuracoes?.inspecaoRecebimento ? 'active' : 'inactive'}">
                                            <i class="fas ${pedido.processoQualidade.configuracoes?.inspecaoRecebimento ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            Inspeção de recebimento
                                        </div>
                                        <div class="config-item ${pedido.processoQualidade.configuracoes?.armazemQualidade ? 'active' : 'inactive'}">
                                            <i class="fas ${pedido.processoQualidade.configuracoes?.armazemQualidade ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            Armazém de qualidade
                                        </div>
                                        <div class="config-item ${pedido.processoQualidade.configuracoes?.rastreabilidade ? 'active' : 'inactive'}">
                                            <i class="fas ${pedido.processoQualidade.configuracoes?.rastreabilidade ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            Rastreabilidade de lote
                                        </div>
                                        <div class="config-item ${pedido.processoQualidade.configuracoes?.certificados ? 'active' : 'inactive'}">
                                            <i class="fas ${pedido.processoQualidade.configuracoes?.certificados ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            Certificados obrigatórios
                                        </div>
                                    </div>
                                    <div class="destino-info">
                                        <strong>Destino:</strong>
                                        <span class="destination-indicator ${pedido.destino === 'ARMAZEM_QUALIDADE' ? 'dest-qualidade' : 'dest-estoque'}">
                                            <i class="fas ${pedido.destino === 'ARMAZEM_QUALIDADE' ? 'fa-warehouse' : 'fa-boxes'}"></i>
                                            ${pedido.destino === 'ARMAZEM_QUALIDADE' ? 'Armazém de Qualidade' : 'Estoque Principal'}
                                        </span>
                                    </div>
                                </div>
                            ` : ''}

                            <div class="detail-section">
                                <h4><i class="fas fa-list"></i> Itens (${pedido.itens?.length || 0})</h4>
                                <div class="itens-list">
                                    ${(pedido.itens || []).map(item => `
                                        <div class="item-detail">
                                            <div class="item-header">
                                                <strong>${item.codigo || 'N/A'}</strong>
                                                <span class="item-qty">${item.quantidade || 0} ${item.unidade || 'UN'}</span>
                                            </div>
                                            <div class="item-description">${item.nome || item.descricao || 'N/A'}</div>
                                            ${item.valorUnitario ? `<div class="item-price">R$ ${item.valorUnitario.toLocaleString('pt-BR', {minimumFractionDigits: 2})} / ${item.unidade || 'UN'}</div>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>

                            ${pedido.observacoes ? `
                                <div class="detail-section">
                                    <h4><i class="fas fa-comment"></i> Observações</h4>
                                    <p>${pedido.observacoes}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                        ${pedido.status === 'RASCUNHO' ? `
                            <button class="btn btn-warning" onclick="fecharModal(); editarPedido('${pedido.id}');">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                        ` : ''}
                        ${pedido.processoQualidade?.ativo ? `
                            <button class="btn btn-primary" onclick="fecharModal(); verInspecoes('${pedido.id}');">
                                <i class="fas fa-search"></i> Ver Inspeções
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Editar pedido
        window.editarPedido = function(id) {
            alert('✏️ Funcionalidade em desenvolvimento: Editar Pedido ' + id);
        };

        // Enviar pedido
        window.enviarPedido = function(id) {
            alert('📤 Funcionalidade em desenvolvimento: Enviar Pedido ' + id);
        };

        // Acompanhar entrega
        window.acompanharEntrega = function(id) {
            alert('🚚 Funcionalidade em desenvolvimento: Acompanhar Entrega ' + id);
        };

        // Ver inspeções
        window.verInspecoes = function(id) {
            // Redirecionar para PQ001 com filtro do pedido
            window.location.href = `PQ001-inspecao-recebimento.html?pedido=${id}`;
        };

        // Mostrar alerta
        function mostrarAlerta(tipo, mensagem) {
            const container = document.getElementById('alertContainer');
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.innerHTML = `
                <i class="fas fa-${tipo === 'success' ? 'check-circle' : tipo === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${mensagem}
            `;

            container.innerHTML = '';
            container.appendChild(alerta);
            alerta.style.display = 'block';

            // Auto-hide após 5 segundos
            setTimeout(() => {
                alerta.style.display = 'none';
            }, 5000);
        }

        // Fechar modal
        window.fecharModal = function() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        };

        // Mostrar/esconder loading
        function mostrarLoading(mostrar) {
            document.getElementById('loading').style.display = mostrar ? 'block' : 'none';
        }

        console.log('✅ PCQ003 - Pedidos de Compra com Qualidade inicializada');
    </script>
</body>
</html>
