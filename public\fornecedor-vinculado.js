// Função para atualizar o botão de itens vinculados
window.updateLinkedItemsButton = () => {
  const fornecedorId = document.getElementById("fornecedor").value

  // Verificar se existe o botão, se não, criar
  let viewLinkedItemsBtn = document.getElementById("viewLinkedItemsBtn")

  if (!viewLinkedItemsBtn) {
    const fornecedorContainer = document.getElementById("fornecedor").parentElement
    viewLinkedItemsBtn = document.createElement("button")
    viewLinkedItemsBtn.id = "viewLinkedItemsBtn"
    viewLinkedItemsBtn.type = "button"
    viewLinkedItemsBtn.className = "btn-secondary"
    viewLinkedItemsBtn.textContent = "Ver Itens Vinculados"
    viewLinkedItemsBtn.style.marginTop = "5px"
    fornecedorContainer.appendChild(viewLinkedItemsBtn)
  }

  // Mostrar ou esconder o botão dependendo se há fornecedor selecionado
  viewLinkedItemsBtn.style.display = fornecedorId ? "block" : "none"

  // Verificar se há itens vinculados
  const temItensVinculados =
    window.produtosFornecedores && window.produtosFornecedores.some((v) => v.fornecedorId === fornecedorId)

  if (!temItensVinculados && fornecedorId) {
    viewLinkedItemsBtn.textContent = "Nenhum Item Vinculado"
    viewLinkedItemsBtn.disabled = true
  } else if (fornecedorId) {
    viewLinkedItemsBtn.textContent = "Ver Itens Vinculados"
    viewLinkedItemsBtn.disabled = false
  }
}

// Função para visualizar itens vinculados
function viewLinkedItems() {
  const fornecedorId = document.getElementById("fornecedor").value
  const itensVinculados = window.produtosFornecedores.filter((v) => v.fornecedorId === fornecedorId)

  if (itensVinculados.length > 0) {
    alert("Itens Vinculados: " + itensVinculados.map((i) => i.nome).join(", "))
  } else {
    alert("Nenhum item vinculado para este fornecedor.")
  }
}

// Adicionar evento de mudança ao select de fornecedor
document.addEventListener("DOMContentLoaded", () => {
  const fornecedorSelect = document.getElementById("fornecedor")
  if (fornecedorSelect) {
    fornecedorSelect.addEventListener("change", window.updateLinkedItemsButton)
  }
})
