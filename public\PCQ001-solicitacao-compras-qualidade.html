<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCQ001 - Solicitação de Compras com Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .quality-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content {
            padding: 30px;
        }

        .quality-info {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .quality-info h3 {
            color: #27ae60;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quality-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .quality-feature {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .quality-feature h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quality-feature p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-group.required label::after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
        }

        .quality-requirements {
            background: #fff3cd;
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .quality-requirements h4 {
            color: #856404;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .requirements-list {
            display: grid;
            gap: 8px;
        }

        .requirement-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }

        .requirement-checkbox {
            width: 18px;
            height: 18px;
        }

        .requirement-label {
            color: #2c3e50;
            font-size: 0.9em;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .items-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .items-table th {
            background: #34495e;
            color: white;
            font-weight: 600;
        }

        .items-table tbody tr:hover {
            background: #f8f9fa;
        }

        .quality-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .quality-required {
            background: #fff3cd;
            color: #856404;
        }

        .quality-optional {
            background: #e2e3e5;
            color: #383d41;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .preview-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
        }

        .preview-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .preview-grid div {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .requisitos-preview {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .requisitos-preview ul {
            margin: 10px 0 0 20px;
            color: #27ae60;
        }

        .items-preview {
            display: grid;
            gap: 10px;
        }

        .item-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .quality-required {
            color: #e67e22;
            font-weight: 600;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .quality-features {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .preview-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="quality-badge">
                <i class="fas fa-shield-alt"></i>
                Processo com Qualidade
            </div>
            <h1>📋 PCQ001 - Solicitação de Compras</h1>
            <p>Solicitação de compras integrada com controle de qualidade</p>
        </div>

        <div class="content">
            <!-- Informações sobre Qualidade -->
            <div class="quality-info">
                <h3><i class="fas fa-award"></i> Funcionalidades de Qualidade Integradas</h3>
                <div class="quality-features">
                    <div class="quality-feature">
                        <h4><i class="fas fa-clipboard-check"></i> Especificações Obrigatórias</h4>
                        <p>Definição de critérios de qualidade para cada item solicitado</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-user-check"></i> Fornecedores Homologados</h4>
                        <p>Verificação automática de homologação de fornecedores</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-search"></i> Inspeção Programada</h4>
                        <p>Criação automática de inspeções de recebimento</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-barcode"></i> Rastreabilidade</h4>
                        <p>Controle de lotes e rastreabilidade completa</p>
                    </div>
                </div>
            </div>

            <!-- Alertas -->
            <div id="alertContainer"></div>

            <!-- Dados Gerais -->
            <div class="form-section">
                <h3><i class="fas fa-info-circle"></i> Dados Gerais da Solicitação</h3>
                <div class="form-grid">
                    <div class="form-group required">
                        <label for="numeroSolicitacao">
                            <i class="fas fa-hashtag"></i>
                            Número da Solicitação
                        </label>
                        <input type="text" id="numeroSolicitacao" readonly>
                    </div>
                    <div class="form-group required">
                        <label for="dataSolicitacao">
                            <i class="fas fa-calendar"></i>
                            Data da Solicitação
                        </label>
                        <input type="date" id="dataSolicitacao" required>
                    </div>
                    <div class="form-group required">
                        <label for="solicitante">
                            <i class="fas fa-user"></i>
                            Solicitante
                        </label>
                        <input type="text" id="solicitante" required>
                    </div>
                    <div class="form-group required">
                        <label for="departamento">
                            <i class="fas fa-building"></i>
                            Departamento
                        </label>
                        <select id="departamento" required>
                            <option value="">Selecione o departamento</option>
                            <option value="PRODUCAO">Produção</option>
                            <option value="MANUTENCAO">Manutenção</option>
                            <option value="QUALIDADE">Qualidade</option>
                            <option value="ADMINISTRATIVO">Administrativo</option>
                            <option value="VENDAS">Vendas</option>
                        </select>
                    </div>
                    <div class="form-group required">
                        <label for="prioridade">
                            <i class="fas fa-exclamation-triangle"></i>
                            Prioridade
                        </label>
                        <select id="prioridade" required>
                            <option value="">Selecione a prioridade</option>
                            <option value="BAIXA">Baixa</option>
                            <option value="MEDIA">Média</option>
                            <option value="ALTA">Alta</option>
                            <option value="URGENTE">Urgente</option>
                        </select>
                    </div>
                    <div class="form-group required">
                        <label for="dataLimite">
                            <i class="fas fa-clock"></i>
                            Data Limite
                        </label>
                        <input type="date" id="dataLimite" required>
                    </div>
                </div>

                <div class="form-group" style="margin-top: 20px;">
                    <label for="observacoes">
                        <i class="fas fa-comment"></i>
                        Observações Gerais
                    </label>
                    <textarea id="observacoes" rows="3" placeholder="Observações adicionais sobre a solicitação..."></textarea>
                </div>
            </div>

            <!-- Configurações de Qualidade -->
            <div class="form-section">
                <h3><i class="fas fa-cogs"></i> Configurações de Qualidade</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="nivelQualidade">
                            <i class="fas fa-star"></i>
                            Nível de Qualidade Requerido
                        </label>
                        <select id="nivelQualidade" required>
                            <option value="BASICO">Básico - Inspeção visual</option>
                            <option value="INTERMEDIARIO">Intermediário - Inspeção + Testes</option>
                            <option value="AVANCADO">Avançado - Inspeção + Testes + Certificação</option>
                            <option value="CRITICO">Crítico - Controle total + Rastreabilidade</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="certificacaoRequerida">
                            <i class="fas fa-certificate"></i>
                            Certificação Requerida
                        </label>
                        <select id="certificacaoRequerida">
                            <option value="">Nenhuma certificação específica</option>
                            <option value="ISO9001">ISO 9001</option>
                            <option value="ISO14001">ISO 14001</option>
                            <option value="INMETRO">INMETRO</option>
                            <option value="CE">Marcação CE</option>
                            <option value="ANVISA">ANVISA</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="validadeLote">
                            <i class="fas fa-calendar-check"></i>
                            Validade Mínima do Lote (dias)
                        </label>
                        <input type="number" id="validadeLote" min="0" placeholder="Ex: 365">
                    </div>
                    <div class="form-group">
                        <label for="temperaturaArmazenamento">
                            <i class="fas fa-thermometer-half"></i>
                            Temperatura de Armazenamento
                        </label>
                        <select id="temperaturaArmazenamento">
                            <option value="AMBIENTE">Temperatura ambiente</option>
                            <option value="REFRIGERADO">Refrigerado (2-8°C)</option>
                            <option value="CONGELADO">Congelado (-18°C)</option>
                            <option value="CONTROLADO">Temperatura controlada</option>
                        </select>
                    </div>
                </div>

                <!-- Requisitos de Qualidade -->
                <div class="quality-requirements">
                    <h4><i class="fas fa-list-check"></i> Requisitos de Qualidade</h4>
                    <div class="requirements-list">
                        <div class="requirement-item">
                            <input type="checkbox" id="reqInspecaoRecebimento" class="requirement-checkbox" checked>
                            <label for="reqInspecaoRecebimento" class="requirement-label">Inspeção de recebimento obrigatória</label>
                        </div>
                        <div class="requirement-item">
                            <input type="checkbox" id="reqRastreabilidade" class="requirement-checkbox">
                            <label for="reqRastreabilidade" class="requirement-label">Rastreabilidade de lote completa</label>
                        </div>
                        <div class="requirement-item">
                            <input type="checkbox" id="reqCertificadoQualidade" class="requirement-checkbox">
                            <label for="reqCertificadoQualidade" class="requirement-label">Certificado de qualidade do fornecedor</label>
                        </div>
                        <div class="requirement-item">
                            <input type="checkbox" id="reqAnaliseQuimica" class="requirement-checkbox">
                            <label for="reqAnaliseQuimica" class="requirement-label">Análise química/física obrigatória</label>
                        </div>
                        <div class="requirement-item">
                            <input type="checkbox" id="reqArmazemQualidade" class="requirement-checkbox">
                            <label for="reqArmazemQualidade" class="requirement-label">Armazenamento em área de qualidade</label>
                        </div>
                        <div class="requirement-item">
                            <input type="checkbox" id="reqFornecedorHomologado" class="requirement-checkbox" checked>
                            <label for="reqFornecedorHomologado" class="requirement-label">Fornecedor deve estar homologado</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Itens da Solicitação -->
            <div class="items-section">
                <h3><i class="fas fa-list"></i> Itens da Solicitação</h3>

                <div class="form-grid" style="margin-bottom: 20px;">
                    <div class="form-group">
                        <label for="codigoProduto">
                            <i class="fas fa-barcode"></i>
                            Código do Produto
                        </label>
                        <input type="text" id="codigoProduto" placeholder="Digite o código ou descrição">
                    </div>
                    <div class="form-group">
                        <label for="quantidade">
                            <i class="fas fa-calculator"></i>
                            Quantidade
                        </label>
                        <input type="number" id="quantidade" min="1" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="unidade">
                            <i class="fas fa-ruler"></i>
                            Unidade
                        </label>
                        <select id="unidade">
                            <option value="UN">Unidade</option>
                            <option value="KG">Quilograma</option>
                            <option value="L">Litro</option>
                            <option value="M">Metro</option>
                            <option value="M2">Metro²</option>
                            <option value="M3">Metro³</option>
                            <option value="CX">Caixa</option>
                            <option value="PC">Peça</option>
                        </select>
                    </div>
                    <div class="form-group" style="align-self: end;">
                        <button type="button" class="btn btn-primary" onclick="adicionarItem()">
                            <i class="fas fa-plus"></i> Adicionar Item
                        </button>
                    </div>
                </div>

                <table class="items-table" id="itensTable">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Quantidade</th>
                            <th>Unidade</th>
                            <th>Qualidade</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="itensTableBody">
                        <!-- Itens serão adicionados dinamicamente -->
                    </tbody>
                </table>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading" style="display: none;">
                <i class="fas fa-spinner"></i>
                <p>Processando solicitação...</p>
            </div>

            <!-- Ações -->
            <div class="actions">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
                <button type="button" class="btn btn-warning" onclick="salvarRascunho()">
                    <i class="fas fa-save"></i> Salvar Rascunho
                </button>
                <button type="button" class="btn btn-primary" onclick="visualizarPrevia()">
                    <i class="fas fa-eye"></i> Prévia
                </button>
                <button type="button" class="btn btn-success" onclick="enviarSolicitacao()">
                    <i class="fas fa-paper-plane"></i> Enviar Solicitação
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, addDoc, getDocs, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let itens = [];
        let produtos = [];
        let parametrosQualidade = {};

        // Inicializar página
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarParametrosQualidade();
            await carregarProdutos();
            gerarNumeroSolicitacao();
            definirDataAtual();
            definirSolicitante();
        });

        // Carregar parâmetros de qualidade
        async function carregarParametrosQualidade() {
            try {
                const doc = await db.collection('parametros').doc('sistema').get();
                if (doc.exists) {
                    parametrosQualidade = doc.data();
                    console.log('✅ PCQ001 - Parâmetros de qualidade carregados:', parametrosQualidade);

                    // Verificar se módulo está ativo
                    if (!parametrosQualidade.moduloQualidadeAtivo) {
                        mostrarAlerta('warning', '⚠️ Módulo de qualidade não está ativo. Redirecionando para versão padrão...');
                        setTimeout(() => {
                            window.location.href = 'solicitacao_compras_melhorada.html';
                        }, 3000);
                        return;
                    }

                    // Configurar requisitos baseado nos parâmetros
                    configurarRequisitosQualidade();
                }
            } catch (error) {
                console.error('❌ Erro ao carregar parâmetros:', error);
                mostrarAlerta('danger', 'Erro ao carregar configurações de qualidade');
            }
        }

        // Configurar requisitos de qualidade baseado nos parâmetros
        function configurarRequisitosQualidade() {
            // Inspeção de recebimento
            document.getElementById('reqInspecaoRecebimento').checked = parametrosQualidade.inspecaoRecebimento || false;
            document.getElementById('reqInspecaoRecebimento').disabled = parametrosQualidade.inspecaoRecebimento || false;

            // Rastreabilidade de lote
            document.getElementById('reqRastreabilidade').checked = parametrosQualidade.rastreabilidadeLote || false;

            // Armazém de qualidade
            document.getElementById('reqArmazemQualidade').checked = parametrosQualidade.armazemQualidade || false;
            document.getElementById('reqArmazemQualidade').disabled = parametrosQualidade.armazemQualidade || false;

            // Fornecedor homologado
            document.getElementById('reqFornecedorHomologado').checked = parametrosQualidade.homologacaoFornecedores || false;
            document.getElementById('reqFornecedorHomologado').disabled = parametrosQualidade.homologacaoFornecedores || false;
        }

        // Carregar produtos
        async function carregarProdutos() {
            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log('📦 Produtos carregados:', produtos.length);
            } catch (error) {
                console.error('❌ Erro ao carregar produtos:', error);
            }
        }

        // Gerar número da solicitação
        function gerarNumeroSolicitacao() {
            const numero = 'SOL-' + Date.now();
            document.getElementById('numeroSolicitacao').value = numero;
        }

        // Definir data atual
        function definirDataAtual() {
            const hoje = new Date().toISOString().split('T')[0];
            document.getElementById('dataSolicitacao').value = hoje;

            // Data limite padrão: 30 dias
            const dataLimite = new Date();
            dataLimite.setDate(dataLimite.getDate() + 30);
            document.getElementById('dataLimite').value = dataLimite.toISOString().split('T')[0];
        }

        // Definir solicitante atual
        function definirSolicitante() {
            // Aqui você pode pegar do sistema de autenticação
            document.getElementById('solicitante').value = 'Usuário Atual'; // Placeholder
        }

        // Adicionar item à solicitação
        window.adicionarItem = function() {
            const codigo = document.getElementById('codigoProduto').value.trim();
            const quantidade = parseFloat(document.getElementById('quantidade').value);
            const unidade = document.getElementById('unidade').value;

            if (!codigo || !quantidade || quantidade <= 0) {
                mostrarAlerta('warning', 'Preencha código do produto e quantidade válida');
                return;
            }

            // Buscar produto
            const produto = produtos.find(p =>
                p.codigo?.toLowerCase().includes(codigo.toLowerCase()) ||
                p.nome?.toLowerCase().includes(codigo.toLowerCase())
            );

            if (!produto) {
                mostrarAlerta('warning', 'Produto não encontrado. Verifique o código ou descrição.');
                return;
            }

            // Verificar se já foi adicionado
            if (itens.find(item => item.produtoId === produto.id)) {
                mostrarAlerta('warning', 'Este produto já foi adicionado à solicitação');
                return;
            }

            // Determinar nível de qualidade baseado no produto e configurações
            const nivelQualidade = determinarNivelQualidade(produto);

            const item = {
                id: Date.now(),
                produtoId: produto.id,
                codigo: produto.codigo,
                nome: produto.nome,
                quantidade: quantidade,
                unidade: unidade,
                nivelQualidade: nivelQualidade,
                especificacoes: produto.especificacoes || '',
                requerInspecao: parametrosQualidade.inspecaoRecebimento || false,
                requerHomologacao: parametrosQualidade.homologacaoFornecedores || false
            };

            itens.push(item);
            renderizarItens();
            limparFormularioItem();
            mostrarAlerta('success', `Item ${produto.nome} adicionado com sucesso`);
        };

        // Determinar nível de qualidade do item
        function determinarNivelQualidade(produto) {
            // Lógica para determinar nível baseado no produto
            if (produto.categoria === 'CRITICO' || produto.categoria === 'MEDICAMENTO') {
                return 'CRITICO';
            } else if (produto.categoria === 'COMPONENTE' || produto.categoria === 'MATERIA_PRIMA') {
                return 'AVANCADO';
            } else if (produto.categoria === 'CONSUMIVEL') {
                return 'INTERMEDIARIO';
            } else {
                return 'BASICO';
            }
        }

        // Renderizar tabela de itens
        function renderizarItens() {
            const tbody = document.getElementById('itensTableBody');

            if (itens.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #7f8c8d;">Nenhum item adicionado</td></tr>';
                return;
            }

            tbody.innerHTML = itens.map(item => `
                <tr>
                    <td><strong>${item.codigo}</strong></td>
                    <td>${item.nome}</td>
                    <td>${item.quantidade}</td>
                    <td>${item.unidade}</td>
                    <td>
                        <span class="quality-indicator ${item.requerInspecao ? 'quality-required' : 'quality-optional'}">
                            <i class="fas ${item.requerInspecao ? 'fa-shield-alt' : 'fa-info-circle'}"></i>
                            ${item.nivelQualidade}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="removerItem(${item.id})" title="Remover">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Remover item
        window.removerItem = function(itemId) {
            itens = itens.filter(item => item.id !== itemId);
            renderizarItens();
            mostrarAlerta('success', 'Item removido com sucesso');
        };

        // Limpar formulário de item
        function limparFormularioItem() {
            document.getElementById('codigoProduto').value = '';
            document.getElementById('quantidade').value = '';
            document.getElementById('unidade').value = 'UN';
        }

        // Salvar rascunho
        window.salvarRascunho = async function() {
            if (itens.length === 0) {
                mostrarAlerta('warning', 'Adicione pelo menos um item antes de salvar');
                return;
            }

            try {
                mostrarLoading(true);

                const solicitacao = coletarDadosSolicitacao();
                solicitacao.status = 'RASCUNHO';

                await addDoc(collection(db, "solicitacoesCompras"), solicitacao);

                mostrarAlerta('success', '💾 Rascunho salvo com sucesso!');
                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao salvar rascunho:', error);
                mostrarAlerta('danger', 'Erro ao salvar rascunho');
                mostrarLoading(false);
            }
        };

        // Enviar solicitação
        window.enviarSolicitacao = async function() {
            if (!validarFormulario()) {
                return;
            }

            try {
                mostrarLoading(true);

                const solicitacao = coletarDadosSolicitacao();
                solicitacao.status = 'PENDENTE';
                solicitacao.dataEnvio = Timestamp.now();

                // 🔍 LÓGICA ESPECÍFICA DE QUALIDADE
                solicitacao.processoQualidade = {
                    ativo: true,
                    versao: 'PCQ001',
                    requisitos: coletarRequisitosQualidade(),
                    nivelQualidade: document.getElementById('nivelQualidade').value,
                    certificacaoRequerida: document.getElementById('certificacaoRequerida').value
                };

                await addDoc(collection(db, "solicitacoesCompras"), solicitacao);

                mostrarAlerta('success', '🚀 Solicitação enviada com sucesso! Processo de qualidade ativado.');

                // Redirecionar após 2 segundos
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao enviar solicitação:', error);
                mostrarAlerta('danger', 'Erro ao enviar solicitação');
                mostrarLoading(false);
            }
        };

        // Coletar dados da solicitação
        function coletarDadosSolicitacao() {
            return {
                numero: document.getElementById('numeroSolicitacao').value,
                dataSolicitacao: Timestamp.fromDate(new Date(document.getElementById('dataSolicitacao').value)),
                solicitante: document.getElementById('solicitante').value,
                departamento: document.getElementById('departamento').value,
                prioridade: document.getElementById('prioridade').value,
                dataLimite: Timestamp.fromDate(new Date(document.getElementById('dataLimite').value)),
                observacoes: document.getElementById('observacoes').value,
                itens: itens,
                dataCriacao: Timestamp.now(),
                usuarioCriacao: 'usuario_atual' // Pegar do sistema de auth
            };
        }

        // Coletar requisitos de qualidade
        function coletarRequisitosQualidade() {
            return {
                inspecaoRecebimento: document.getElementById('reqInspecaoRecebimento').checked,
                rastreabilidade: document.getElementById('reqRastreabilidade').checked,
                certificadoQualidade: document.getElementById('reqCertificadoQualidade').checked,
                analiseQuimica: document.getElementById('reqAnaliseQuimica').checked,
                armazemQualidade: document.getElementById('reqArmazemQualidade').checked,
                fornecedorHomologado: document.getElementById('reqFornecedorHomologado').checked,
                validadeLote: parseInt(document.getElementById('validadeLote').value) || 0,
                temperaturaArmazenamento: document.getElementById('temperaturaArmazenamento').value
            };
        }

        // Validar formulário
        function validarFormulario() {
            const campos = ['dataSolicitacao', 'solicitante', 'departamento', 'prioridade', 'dataLimite'];

            for (const campo of campos) {
                const elemento = document.getElementById(campo);
                if (!elemento.value.trim()) {
                    mostrarAlerta('warning', `Campo "${elemento.previousElementSibling.textContent}" é obrigatório`);
                    elemento.focus();
                    return false;
                }
            }

            if (itens.length === 0) {
                mostrarAlerta('warning', 'Adicione pelo menos um item à solicitação');
                return false;
            }

            return true;
        }

        // Prévia da solicitação
        window.visualizarPrevia = function() {
            if (!validarFormulario()) {
                return;
            }

            const solicitacao = coletarDadosSolicitacao();
            const requisitos = coletarRequisitosQualidade();

            mostrarModalPrevia(solicitacao, requisitos);
        };

        // Mostrar modal de prévia
        function mostrarModalPrevia(solicitacao, requisitos) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-eye"></i> Prévia da Solicitação</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="preview-section">
                            <h4><i class="fas fa-info-circle"></i> Dados Gerais</h4>
                            <div class="preview-grid">
                                <div><strong>Data:</strong> ${solicitacao.dataSolicitacao}</div>
                                <div><strong>Solicitante:</strong> ${solicitacao.solicitante}</div>
                                <div><strong>Departamento:</strong> ${solicitacao.departamento}</div>
                                <div><strong>Prioridade:</strong> ${solicitacao.prioridade}</div>
                                <div><strong>Data Limite:</strong> ${solicitacao.dataLimite}</div>
                            </div>
                        </div>

                        <div class="preview-section">
                            <h4><i class="fas fa-shield-alt"></i> Configurações de Qualidade</h4>
                            <div class="preview-grid">
                                <div><strong>Nível:</strong> ${document.getElementById('nivelQualidade').value}</div>
                                <div><strong>Certificação:</strong> ${document.getElementById('certificacaoRequerida').value}</div>
                            </div>
                            <div class="requisitos-preview">
                                <strong>Requisitos Ativos:</strong>
                                <ul>
                                    ${Object.entries(requisitos).filter(([key, value]) => value).map(([key]) =>
                                        `<li>${key.replace(/([A-Z])/g, ' $1').toLowerCase()}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>

                        <div class="preview-section">
                            <h4><i class="fas fa-list"></i> Itens (${itens.length})</h4>
                            <div class="items-preview">
                                ${itens.map(item => `
                                    <div class="item-preview">
                                        <strong>${item.codigo}</strong> - ${item.nome}
                                        <br>Qtd: ${item.quantidade} ${item.unidade}
                                        <br>Qualidade: ${item.nivelQualidade}
                                        ${item.requerInspecao ? '<br><span class="quality-required">🔍 Inspeção Obrigatória</span>' : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        ${solicitacao.observacoes ? `
                            <div class="preview-section">
                                <h4><i class="fas fa-comment"></i> Observações</h4>
                                <p>${solicitacao.observacoes}</p>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                        <button class="btn btn-success" onclick="fecharModal(); enviarSolicitacao();">
                            <i class="fas fa-paper-plane"></i> Confirmar e Enviar
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Fechar modal
        window.fecharModal = function() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        };

        // Mostrar alerta
        function mostrarAlerta(tipo, mensagem) {
            const container = document.getElementById('alertContainer');
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.innerHTML = `
                <i class="fas fa-${tipo === 'success' ? 'check-circle' : tipo === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${mensagem}
            `;

            container.innerHTML = '';
            container.appendChild(alerta);
            alerta.style.display = 'block';

            // Auto-hide após 5 segundos
            setTimeout(() => {
                alerta.style.display = 'none';
            }, 5000);
        }

        // Mostrar/esconder loading
        function mostrarLoading(mostrar) {
            document.getElementById('loading').style.display = mostrar ? 'block' : 'none';
        }

        console.log('✅ PCQ001 - Solicitação de Compras com Qualidade inicializada');
    </script>
</body>
</html>