<!-- Modal Editar Cotação -->
<div id="editQuotationModal" class="modal">
    <div class="modal-content" style="max-width: 1400px;">
        <div class="modal-header">
            <h2><i class="fas fa-edit"></i> Editar Cotação</h2>
            <span class="close" onclick="closeModal('editQuotationModal')">&times;</span>
        </div>
        <div class="modal-body">
            <!-- Informações Básicas -->
            <div class="quotation-header">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Número da Cotação</label>
                            <input type="text" class="form-control" id="editQuotationNumber" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Data de Criação</label>
                            <input type="date" class="form-control" id="editQuotationDate" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Data Limite para Resposta</label>
                            <input type="date" class="form-control" id="editQuotationDeadline">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Status</label>
                            <select class="form-control" id="editQuotationStatus">
                                <option value="ABERTA">Aberta</option>
                                <option value="ENVIADA">Enviada</option>
                                <option value="RESPONDIDA">Respondida</option>
                                <option value="APROVADA">Aprovada</option>
                                <option value="FECHADA">Fechada</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Abas de Edição -->
            <div class="edit-tabs">
                <button class="edit-tab active" onclick="showEditTab('itens', event)">
                    <i class="fas fa-list"></i> Itens da Cotação
                </button>
                <button class="edit-tab" onclick="showEditTab('fornecedores', event)">
                    <i class="fas fa-truck"></i> Fornecedores
                </button>
                <button class="edit-tab" onclick="showEditTab('condicoes', event)">
                    <i class="fas fa-file-contract"></i> Condições Comerciais
                </button>
                <button class="edit-tab" onclick="showEditTab('observacoes', event)">
                    <i class="fas fa-comment"></i> Observações
                </button>
            </div>

            <!-- Aba Itens -->
            <div id="editTabItens" class="edit-tab-content active">
                <div class="items-section">
                    <div class="section-header">
                        <h4><i class="fas fa-list"></i> Itens da Cotação</h4>
                        <button class="btn btn-success btn-sm" onclick="addQuotationItem()">
                            <i class="fas fa-plus"></i> Adicionar Item
                        </button>
                    </div>
                    
                    <div class="table-container">
                        <table class="table" id="editItemsTable">
                            <thead>
                                <tr>
                                    <th style="width: 100px;">Código</th>
                                    <th>Descrição</th>
                                    <th style="width: 120px;">
                                        Unidade Interna
                                        <small style="display: block; font-weight: normal; opacity: 0.8;">
                                            Controle
                                        </small>
                                    </th>
                                    <th style="width: 120px;">
                                        Unidade Compra
                                        <small style="display: block; font-weight: normal; opacity: 0.8;">
                                            Fornecedor
                                        </small>
                                    </th>
                                    <th style="width: 140px;">
                                        Quantidade Interna
                                        <small style="display: block; font-weight: normal; opacity: 0.8;">
                                            Controle
                                        </small>
                                    </th>
                                    <th style="width: 140px;">
                                        Quantidade Compra
                                        <small style="display: block; font-weight: normal; opacity: 0.8;">
                                            Convertida
                                        </small>
                                    </th>
                                    <th style="width: 120px;">Valor Unitário</th>
                                    <th style="width: 100px;">IPI (%)</th>
                                    <th style="width: 100px;">ICMS (%)</th>
                                    <th style="width: 120px;">Valor Total</th>
                                    <th style="width: 80px;">Ações</th>
                                </tr>
                            </thead>
                            <tbody id="editItemsTableBody">
                                <!-- Itens serão carregados dinamicamente -->
                            </tbody>
                            <tfoot>
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td colspan="7">TOTAL GERAL:</td>
                                    <td id="editTotalValue">R$ 0,00</td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Aba Fornecedores -->
            <div id="editTabFornecedores" class="edit-tab-content">
                <div class="suppliers-section">
                    <div class="section-header">
                        <h4><i class="fas fa-truck"></i> Fornecedores Selecionados</h4>
                        <button class="btn btn-success btn-sm" onclick="addSupplierToQuotation()">
                            <i class="fas fa-plus"></i> Adicionar Fornecedor
                        </button>
                    </div>
                    
                    <div id="editSuppliersContainer">
                        <!-- Fornecedores serão carregados dinamicamente -->
                    </div>

                    <!-- Seção de Links de Resposta -->
                    <div class="section-header" style="margin-top: 30px;">
                        <h4><i class="fas fa-link"></i> Links de Resposta para Fornecedores</h4>
                        <small style="color: #6c757d;">Envie estes links para os fornecedores responderem à cotação</small>
                    </div>

                    <div id="responseLinksContainer" class="response-links-container">
                        <!-- Links serão gerados aqui -->
                    </div>

                    <div style="margin-top: 15px;">
                        <button type="button" class="btn btn-info btn-sm" onclick="copyAllLinks()">
                            <i class="fas fa-copy"></i> Copiar Todos os Links
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="generateResponseLinks()">
                            <i class="fas fa-refresh"></i> Atualizar Links
                        </button>
                    </div>
                </div>
            </div>

            <!-- Aba Condições Comerciais -->
            <div id="editTabCondicoes" class="edit-tab-content">
                <div class="commercial-conditions">
                    <h4><i class="fas fa-file-contract"></i> Condições Comerciais</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Prazo de Entrega (dias)</label>
                                <input type="number" class="form-control" id="editDeliveryTerm" placeholder="Ex: 30">
                            </div>
                            
                            <div class="form-group">
                                <label>Condições de Pagamento</label>
                                <select class="form-control" id="editPaymentTerms">
                                    <option value="">Selecione...</option>
                                    <option value="A_VISTA">À Vista</option>
                                    <option value="30_DIAS">30 dias</option>
                                    <option value="60_DIAS">60 dias</option>
                                    <option value="30_60_DIAS">30/60 dias</option>
                                    <option value="30_60_90_DIAS">30/60/90 dias</option>
                                    <option value="PERSONALIZADO">Personalizado</option>
                                </select>
                            </div>
                            
                            <div class="form-group" id="customPaymentGroup" style="display: none;">
                                <label>Condições Personalizadas</label>
                                <textarea class="form-control" id="editCustomPayment" rows="3" placeholder="Descreva as condições de pagamento..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label>Frete</label>
                                <select class="form-control" id="editFreightType">
                                    <option value="CIF">CIF (por conta do fornecedor)</option>
                                    <option value="FOB">FOB (por conta do comprador)</option>
                                    <option value="VALOR_FIXO">Valor Fixo</option>
                                </select>
                            </div>
                            
                            <div class="form-group" id="freightValueGroup" style="display: none;">
                                <label>Valor do Frete</label>
                                <input type="number" class="form-control" id="editFreightValue" step="0.01" placeholder="0,00">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Validade da Proposta (dias)</label>
                                <input type="number" class="form-control" id="editProposalValidity" placeholder="Ex: 15">
                            </div>
                            
                            <div class="form-group">
                                <label>Local de Entrega</label>
                                <textarea class="form-control" id="editDeliveryLocation" rows="3" placeholder="Endereço completo para entrega..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label>Garantia</label>
                                <input type="text" class="form-control" id="editWarranty" placeholder="Ex: 12 meses">
                            </div>
                            
                            <div class="form-group">
                                <label>Certificações Exigidas</label>
                                <textarea class="form-control" id="editCertifications" rows="2" placeholder="ISO, certificados de qualidade, etc..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resumo Financeiro -->
                    <div class="financial-summary">
                        <h5><i class="fas fa-calculator"></i> Resumo Financeiro</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="summary-item">
                                    <label>Subtotal</label>
                                    <div class="summary-value" id="editSubtotal">R$ 0,00</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-item">
                                    <label>Total IPI</label>
                                    <div class="summary-value" id="editTotalIPI">R$ 0,00</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-item">
                                    <label>Frete</label>
                                    <div class="summary-value" id="editFreightTotal">R$ 0,00</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-item total">
                                    <label>Total Geral</label>
                                    <div class="summary-value" id="editGrandTotal">R$ 0,00</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Observações -->
            <div id="editTabObservacoes" class="edit-tab-content">
                <div class="observations-section">
                    <h4><i class="fas fa-comment"></i> Observações</h4>
                    
                    <div class="form-group">
                        <label>Observações Internas</label>
                        <textarea class="form-control" id="editInternalNotes" rows="4" placeholder="Observações para uso interno..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>Observações para Fornecedores</label>
                        <textarea class="form-control" id="editSupplierNotes" rows="4" placeholder="Observações que serão enviadas aos fornecedores..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>Especificações Técnicas</label>
                        <textarea class="form-control" id="editTechnicalSpecs" rows="4" placeholder="Especificações técnicas detalhadas..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Botões de Ação -->
            <div class="modal-actions">
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editQuotationModal')">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveQuotationDraft()">
                        <i class="fas fa-save"></i> Salvar Rascunho
                    </button>
                    <button type="button" class="btn btn-warning" onclick="sendQuotationForApproval()">
                        <i class="fas fa-paper-plane"></i> Enviar para Aprovação
                    </button>
                    <button type="button" class="btn btn-success" onclick="sendQuotationToSuppliers()">
                        <i class="fas fa-share"></i> Enviar aos Fornecedores
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
