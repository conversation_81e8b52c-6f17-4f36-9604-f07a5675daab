// Componente para gerenciar notificações do sistema
import { db } from '../firebase-config.js';
import { 
  collection, 
  query, 
  where, 
  getDocs,
  onSnapshot,
  Timestamp,
  updateDoc,
  doc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class NotificationsComponent {
  constructor(containerId, usuarioId) {
    this.containerId = containerId;
    this.usuarioId = usuarioId;
    this.container = document.getElementById(containerId);
    this.notifications = [];
    this.alerts = [];
    this.unreadCount = 0;
    
    this.initialize();
  }

  async initialize() {
    // Cria estrutura básica
    this.container.innerHTML = `
      <button class="notifications-toggle" onclick="notificationsComponent.toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge" style="display: none">0</span>
      </button>
      <div class="notifications-wrapper">
        <div class="notifications-header">
          <h3>Notificações</h3>
          <button onclick="notificationsComponent.markAllAsRead()">
            <i class="fas fa-check-double"></i> Marcar todas como lidas
          </button>
        </div>
        <div class="alerts-container"></div>
        <div class="notifications-container"></div>
      </div>
    `;

    // Inicia monitoramento
    await this.startMonitoring();
    
    // Carrega dados iniciais
    await this.loadData();

    // Atualiza interface
    this.render();

    // Adiciona listener para fechar ao clicar fora
    document.addEventListener('click', (event) => {
      if (!this.container.contains(event.target)) {
        this.closeNotifications();
      }
    });
  }

  async startMonitoring() {
    // Monitora notificações em tempo real
    const notificationsQuery = query(
      collection(db, "notificacoes"),
      where("destinatarios", "array-contains", this.usuarioId)
    );

    onSnapshot(notificationsQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === "added" || change.type === "modified") {
          this.handleNewNotification(change.doc.id, change.doc.data());
        }
      });
    });

    // Monitora alertas críticos
    const alertsQuery = query(
      collection(db, "alertas"),
      where("status", "==", "ATIVO")
    );

    onSnapshot(alertsQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === "added" || change.type === "modified") {
          this.handleNewAlert(change.doc.id, change.doc.data());
        }
      });
    });
  }

  async loadData() {
    try {
      // Carrega notificações
      const notificationsQuery = query(
        collection(db, "notificacoes"),
        where("destinatarios", "array-contains", this.usuarioId),
        where("dataEnvio", ">=", Timestamp.fromDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))) // últimos 30 dias
      );

      const notificationsSnapshot = await getDocs(notificationsQuery);
      this.notifications = notificationsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Carrega alertas ativos
      const alertsQuery = query(
        collection(db, "alertas"),
        where("status", "==", "ATIVO")
      );

      const alertsSnapshot = await getDocs(alertsQuery);
      this.alerts = alertsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Atualiza contador
      this.updateUnreadCount();
    } catch (error) {
      console.error('Erro ao carregar notificações:', error);
    }
  }

  render() {
    this.renderAlerts();
    this.renderNotifications();
    this.updateBadge();
  }

  renderAlerts() {
    const container = this.container.querySelector('.alerts-container');
    container.innerHTML = this.alerts
      .map(alert => `
        <div class="alert-item ${this.getSeverityClass(alert.severidade)}">
          <div class="alert-header">
            <span class="alert-title">${alert.titulo}</span>
            <span class="alert-time">${this.formatDate(alert.dataCriacao)}</span>
          </div>
          <div class="alert-message">${alert.mensagem}</div>
          <div class="alert-actions">
            <button onclick="notificationsComponent.resolveAlert('${alert.id}')">
              <i class="fas fa-check"></i> Resolver
            </button>
            <button onclick="notificationsComponent.dismissAlert('${alert.id}')">
              <i class="fas fa-times"></i> Dispensar
            </button>
          </div>
        </div>
      `)
      .join('');
  }

  renderNotifications() {
    const container = this.container.querySelector('.notifications-container');
    container.innerHTML = this.notifications
      .sort((a, b) => b.dataEnvio.seconds - a.dataEnvio.seconds)
      .map(notification => `
        <div class="notification-item ${notification.lida ? '' : 'unread'}" 
             onclick="notificationsComponent.markAsRead('${notification.id}')">
          <div class="notification-header">
            <span class="notification-title">${notification.titulo}</span>
            <span class="notification-time">${this.formatDate(notification.dataEnvio)}</span>
          </div>
          <div class="notification-message">${notification.mensagem}</div>
        </div>
      `)
      .join('');
  }

  getSeverityClass(severidade) {
    switch (severidade?.toUpperCase()) {
      case 'CRITICA':
        return 'alert-critical';
      case 'ALTA':
        return 'alert-warning';
      default:
        return 'alert-info';
    }
  }

  formatDate(timestamp) {
    if (!timestamp) return '';
    
    const date = timestamp.toDate();
    const now = new Date();
    const diffMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffMinutes < 60) {
      return `${diffMinutes} min atrás`;
    } else if (diffMinutes < 1440) {
      const hours = Math.floor(diffMinutes / 60);
      return `${hours}h atrás`;
    } else {
      return date.toLocaleDateString();
    }
  }

  async markAsRead(notificationId) {
    try {
      const notificationRef = doc(db, "notificacoes", notificationId);
      await updateDoc(notificationRef, {
        lida: true,
        dataLeitura: Timestamp.now()
      });

      // Atualiza localmente
      const notification = this.notifications.find(n => n.id === notificationId);
      if (notification) {
        notification.lida = true;
        this.updateUnreadCount();
        this.render();
      }
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
    }
  }

  async resolveAlert(alertId) {
    try {
      const alertRef = doc(db, "alertas", alertId);
      await updateDoc(alertRef, {
        status: 'RESOLVIDO',
        dataResolucao: Timestamp.now(),
        resolvidoPor: {
          id: this.usuarioId,
          data: Timestamp.now()
        }
      });

      // Remove localmente
      this.alerts = this.alerts.filter(a => a.id !== alertId);
      this.render();
    } catch (error) {
      console.error('Erro ao resolver alerta:', error);
    }
  }

  showResolveDialog() {
    // Implementar diálogo de resolução se necessário
  }

  async refreshNotifications() {
    await this.loadData();
    this.render();
  }

  openNotificationSettings() {
    // Implementar configurações de notificação se necessário
  }

  toggleNotifications() {
    const wrapper = this.container.querySelector('.notifications-wrapper');
    wrapper.classList.toggle('show');
  }

  closeNotifications() {
    const wrapper = this.container.querySelector('.notifications-wrapper');
    wrapper.classList.remove('show');
  }

  updateUnreadCount() {
    this.unreadCount = this.notifications.filter(n => !n.lida).length + this.alerts.length;
    this.updateBadge();
  }

  updateBadge() {
    const badge = this.container.querySelector('.notification-badge');
    if (this.unreadCount > 0) {
      badge.textContent = this.unreadCount;
      badge.style.display = 'flex';
    } else {
      badge.style.display = 'none';
    }
  }

  handleNewNotification(id, data) {
    const existingIndex = this.notifications.findIndex(n => n.id === id);
    if (existingIndex >= 0) {
      this.notifications[existingIndex] = { id, ...data };
    } else {
      this.notifications.unshift({ id, ...data });
    }
    this.updateUnreadCount();
    this.render();
  }

  handleNewAlert(id, data) {
    const existingIndex = this.alerts.findIndex(a => a.id === id);
    if (existingIndex >= 0) {
      this.alerts[existingIndex] = { id, ...data };
    } else {
      this.alerts.unshift({ id, ...data });
    }
    this.updateUnreadCount();
    this.render();
  }

  async markAllAsRead() {
    try {
      const unreadNotifications = this.notifications.filter(n => !n.lida);
      const batch = writeBatch(db);

      unreadNotifications.forEach(notification => {
        const notificationRef = doc(db, "notificacoes", notification.id);
        batch.update(notificationRef, {
          lida: true,
          dataLeitura: Timestamp.now()
        });
        notification.lida = true;
      });

      await batch.commit();
      this.updateUnreadCount();
      this.render();
    } catch (error) {
      console.error('Erro ao marcar todas notificações como lidas:', error);
    }
  }
} 