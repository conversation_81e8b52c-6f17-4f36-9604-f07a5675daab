<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 Correção de Saldo de Estoque</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .correction-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .correction-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .search-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .search-input {
            width: 300px;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin: 10px;
            font-size: 16px;
        }
        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }
        .item-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .item-card.found {
            border-color: #28a745;
            background: #f8fff9;
        }
        .item-card.error {
            border-color: #dc3545;
            background: #fff8f8;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        .item-code {
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
        }
        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .detail-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .detail-label {
            font-weight: bold;
            color: #6c757d;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }
        .detail-value {
            color: #495057;
            font-size: 16px;
        }
        .current-stock {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .stock-value {
            font-size: 3em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        .correction-form {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #495057;
        }
        .form-input {
            width: 100%;
            max-width: 300px;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
        }
        .form-input:focus {
            outline: none;
            border-color: #ffc107;
        }
        .form-textarea {
            width: 100%;
            max-width: 500px;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }
        .correction-preview {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .preview-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .movement-type {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .type-ajuste { background: #fff3cd; color: #856404; }
        .type-correcao { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="correction-container">
        <div class="correction-header">
            <h1>📦 Correção de Saldo de Estoque</h1>
            <p>Ferramenta para corrigir saldos incorretos no estoque</p>
            <p><strong>Status:</strong> <span id="statusGeral">Pronto para busca</span></p>
        </div>

        <!-- Busca de Item -->
        <div class="search-section">
            <h3>🔍 Buscar Item para Correção</h3>
            <input type="text" id="itemCodigo" class="search-input" placeholder="Digite o código do produto...">
            <button onclick="buscarItem()" class="btn btn-primary">🔍 Buscar Item</button>
            <button onclick="buscarTodosProblemas()" class="btn btn-warning">📋 Listar Todos com Problemas</button>
        </div>

        <!-- Informações do Item -->
        <div class="item-card" id="itemCard" style="display: none;">
            <div class="item-header">
                <div class="item-code" id="itemTitle">Carregando...</div>
                <div>
                    <span class="movement-type type-ajuste">CORREÇÃO</span>
                </div>
            </div>
            
            <div class="item-details" id="itemDetails">
                <!-- Detalhes serão preenchidos aqui -->
            </div>

            <!-- Saldo Atual -->
            <div class="current-stock">
                <h4>📊 Saldo Atual no Estoque</h4>
                <div class="stock-value" id="currentStock">0</div>
                <div id="stockUnit">PC</div>
                <div style="font-size: 14px; color: #6c757d; margin-top: 10px;">
                    <strong>Armazém:</strong> <span id="stockWarehouse">N/A</span>
                </div>
            </div>

            <!-- Formulário de Correção -->
            <div class="correction-form">
                <h4>🔧 Correção de Saldo</h4>
                
                <div class="form-group">
                    <label class="form-label">Novo Saldo:</label>
                    <input type="number" id="novoSaldo" class="form-input" placeholder="Digite o saldo correto..." step="0.01" min="0">
                </div>

                <div class="form-group">
                    <label class="form-label">Tipo de Correção:</label>
                    <select id="tipoCorrecao" class="form-input">
                        <option value="ajuste">Ajuste de Inventário</option>
                        <option value="correcao">Correção de Erro</option>
                        <option value="duplicacao">Correção de Duplicação</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Motivo da Correção:</label>
                    <textarea id="motivoCorrecao" class="form-textarea" placeholder="Descreva o motivo da correção..."></textarea>
                </div>

                <div class="form-group">
                    <button onclick="calcularCorrecao()" class="btn btn-warning">📊 Calcular Correção</button>
                    <button onclick="executarCorrecao()" class="btn btn-success" id="btnExecutar" disabled>✅ Executar Correção</button>
                </div>
            </div>

            <!-- Preview da Correção -->
            <div class="correction-preview" id="correctionPreview" style="display: none;">
                <h4>📋 Preview da Correção</h4>
                <div id="previewContent">
                    <!-- Preview será mostrado aqui -->
                </div>
            </div>
        </div>

        <!-- Log -->
        <div class="log-area" id="logArea"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            doc,
            updateDoc,
            addDoc,
            query,
            where,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let currentItem = null;
        let currentStock = null;

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        window.buscarItem = async function() {
            const codigo = document.getElementById('itemCodigo').value.trim();
            if (!codigo) {
                alert('Digite um código de produto!');
                return;
            }

            log(`🔍 Buscando item ${codigo}...`, 'info');
            document.getElementById('statusGeral').textContent = 'Buscando item...';

            try {
                // Buscar produto
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                currentItem = produtos.find(p => p.codigo === codigo);

                if (!currentItem) {
                    log(`❌ Produto ${codigo} não encontrado`, 'error');
                    document.getElementById('itemCard').style.display = 'none';
                    return;
                }

                // Buscar no estoque
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Buscar TODOS os estoques do produto
                const estoquesDoItem = estoques.filter(e => e.produtoId === currentItem.id);

                if (estoquesDoItem.length === 0) {
                    log(`❌ Item ${codigo} não encontrado em nenhum estoque`, 'error');
                    document.getElementById('itemCard').style.display = 'none';
                    return;
                }

                // Se há múltiplos estoques, mostrar aviso
                if (estoquesDoItem.length > 1) {
                    log(`⚠️ Item ${codigo} encontrado em ${estoquesDoItem.length} armazéns diferentes`, 'warning');
                }

                // Usar o primeiro estoque (pode ser melhorado para escolher)
                currentStock = estoquesDoItem[0];

                // Buscar informações de armazéns
                const armazensSnap = await getDocs(collection(db, "armazens"));
                const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Criar lista de todos os estoques do item
                let todosEstoquesInfo = '';
                estoquesDoItem.forEach((est, index) => {
                    const armazem = armazens.find(a => a.id === est.armazemId);
                    const nomeArmazem = armazem ? armazem.nome : est.armazemId;
                    const saldo = est.saldo || 0;
                    const status = index === 0 ? ' (SELECIONADO)' : '';
                    todosEstoquesInfo += `• ${nomeArmazem}: ${saldo} ${currentItem.unidade || 'PC'}${status}\n`;
                });

                if (estoquesDoItem.length > 1) {
                    log(`📋 Estoques encontrados:\n${todosEstoquesInfo}`, 'info');
                }

                // Nome do armazém selecionado
                const armazemSelecionado = armazens.find(a => a.id === currentStock.armazemId);
                const nomeArmazem = armazemSelecionado ? armazemSelecionado.nome : currentStock.armazemId;

                // Mostrar informações
                document.getElementById('itemTitle').textContent = `${currentItem.codigo} - ${currentItem.descricao}`;
                document.getElementById('currentStock').textContent = currentStock.saldo || 0;
                document.getElementById('stockUnit').textContent = currentItem.unidade || 'PC';
                document.getElementById('stockWarehouse').textContent = nomeArmazem;

                // Preencher detalhes
                document.getElementById('itemDetails').innerHTML = `
                    <div class="detail-group">
                        <div class="detail-label">ID do Produto</div>
                        <div class="detail-value">${currentItem.id}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">ID do Estoque</div>
                        <div class="detail-value">${currentStock.id}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Última Movimentação</div>
                        <div class="detail-value">${currentStock.ultimaMovimentacao ? 
                            new Date(currentStock.ultimaMovimentacao.seconds * 1000).toLocaleString() : 'N/A'}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Unidade</div>
                        <div class="detail-value">${currentItem.unidade || 'PC'}</div>
                    </div>
                    ${estoquesDoItem.length > 1 ? `
                    <div class="detail-group">
                        <div class="detail-label">Outros Armazéns</div>
                        <div class="detail-value" style="font-size: 12px;">
                            ${estoquesDoItem.map((est, index) => {
                                if (index === 0) return ''; // Pular o selecionado
                                const arm = armazens.find(a => a.id === est.armazemId);
                                const nome = arm ? arm.nome : est.armazemId;
                                return `${nome}: ${est.saldo || 0} ${currentItem.unidade || 'PC'}`;
                            }).filter(Boolean).join('<br>')}
                        </div>
                    </div>
                    ` : ''}
                `;

                document.getElementById('itemCard').style.display = 'block';
                document.getElementById('itemCard').className = 'item-card found';
                
                log(`✅ Item encontrado: ${currentItem.codigo} - Saldo atual: ${currentStock.saldo}`, 'success');
                document.getElementById('statusGeral').textContent = 'Item carregado - Pronto para correção';

            } catch (error) {
                log(`❌ Erro na busca: ${error.message}`, 'error');
                document.getElementById('itemCard').style.display = 'none';
            }
        };

        window.calcularCorrecao = function() {
            if (!currentItem || !currentStock) {
                alert('Busque primeiro um item!');
                return;
            }

            const novoSaldo = parseFloat(document.getElementById('novoSaldo').value);
            const tipoCorrecao = document.getElementById('tipoCorrecao').value;
            const motivo = document.getElementById('motivoCorrecao').value.trim();

            if (isNaN(novoSaldo) || novoSaldo < 0) {
                alert('Digite um saldo válido (0 ou maior)!');
                return;
            }

            if (!motivo) {
                alert('Digite o motivo da correção!');
                return;
            }

            const saldoAtual = currentStock.saldo || 0;
            const diferenca = novoSaldo - saldoAtual;
            const tipoMovimentacao = diferenca > 0 ? 'ENTRADA' : diferenca < 0 ? 'SAÍDA' : 'SEM ALTERAÇÃO';

            // Mostrar preview
            const previewContent = document.getElementById('previewContent');
            previewContent.innerHTML = `
                <div class="preview-item">
                    <strong>Item:</strong>
                    <span>${currentItem.codigo} - ${currentItem.descricao}</span>
                </div>
                <div class="preview-item">
                    <strong>Saldo Atual:</strong>
                    <span>${saldoAtual} ${currentItem.unidade || 'PC'}</span>
                </div>
                <div class="preview-item">
                    <strong>Novo Saldo:</strong>
                    <span>${novoSaldo} ${currentItem.unidade || 'PC'}</span>
                </div>
                <div class="preview-item">
                    <strong>Diferença:</strong>
                    <span style="color: ${diferenca > 0 ? '#28a745' : diferenca < 0 ? '#dc3545' : '#6c757d'};">
                        ${diferenca > 0 ? '+' : ''}${diferenca} ${currentItem.unidade || 'PC'} (${tipoMovimentacao})
                    </span>
                </div>
                <div class="preview-item">
                    <strong>Tipo:</strong>
                    <span>${getTipoCorrecaoText(tipoCorrecao)}</span>
                </div>
                <div class="preview-item">
                    <strong>Motivo:</strong>
                    <span>${motivo}</span>
                </div>
                <div class="preview-item">
                    <strong>Usuário:</strong>
                    <span>${currentUser.nome}</span>
                </div>
                <div class="preview-item">
                    <strong>Data/Hora:</strong>
                    <span>${new Date().toLocaleString()}</span>
                </div>
            `;

            document.getElementById('correctionPreview').style.display = 'block';
            document.getElementById('btnExecutar').disabled = false;

            log(`📊 Correção calculada: ${saldoAtual} → ${novoSaldo} (${diferenca > 0 ? '+' : ''}${diferenca})`, 'info');
        };

        function getTipoCorrecaoText(tipo) {
            switch (tipo) {
                case 'ajuste': return 'Ajuste de Inventário';
                case 'correcao': return 'Correção de Erro';
                case 'duplicacao': return 'Correção de Duplicação';
                default: return tipo;
            }
        }

        window.executarCorrecao = async function() {
            if (!currentItem || !currentStock) {
                alert('Busque primeiro um item!');
                return;
            }

            const novoSaldo = parseFloat(document.getElementById('novoSaldo').value);
            const tipoCorrecao = document.getElementById('tipoCorrecao').value;
            const motivo = document.getElementById('motivoCorrecao').value.trim();

            if (isNaN(novoSaldo) || novoSaldo < 0) {
                alert('Digite um saldo válido!');
                return;
            }

            const saldoAtual = currentStock.saldo || 0;
            const diferenca = novoSaldo - saldoAtual;

            const confirmacao = confirm(`Confirma a correção do saldo?\n\nDe: ${saldoAtual} ${currentItem.unidade || 'PC'}\nPara: ${novoSaldo} ${currentItem.unidade || 'PC'}\nDiferença: ${diferenca > 0 ? '+' : ''}${diferenca} ${currentItem.unidade || 'PC'}\n\nEsta ação será registrada no histórico.`);

            if (!confirmacao) return;

            log(`🔧 Executando correção de saldo...`, 'info');
            document.getElementById('statusGeral').textContent = 'Executando correção...';

            try {
                // Atualizar saldo no estoque
                await updateDoc(doc(db, "estoques", currentStock.id), {
                    saldo: novoSaldo,
                    ultimaMovimentacao: Timestamp.now(),
                    usuarioUltimaMovimentacao: currentUser.nome
                });

                log(`✅ Saldo atualizado no estoque: ${saldoAtual} → ${novoSaldo}`, 'success');

                // Registrar movimentação de correção
                if (diferenca !== 0) {
                    const movimentacao = {
                        produtoId: currentItem.id,
                        armazemId: currentStock.armazemId,
                        tipo: diferenca > 0 ? 'ENTRADA' : 'SAIDA',
                        subTipo: 'CORRECAO',
                        quantidade: Math.abs(diferenca),
                        saldoAnterior: saldoAtual,
                        saldoNovo: novoSaldo,
                        dataMovimentacao: Timestamp.now(),
                        usuario: currentUser.nome,
                        observacoes: `${getTipoCorrecaoText(tipoCorrecao)}: ${motivo}`,
                        origem: 'CORRECAO_SALDO',
                        documentoOrigem: 'corrigir_saldo_estoque.html'
                    };

                    await addDoc(collection(db, "movimentacoesEstoque"), movimentacao);
                    log(`✅ Movimentação de correção registrada`, 'success');
                }

                // Atualizar display
                document.getElementById('currentStock').textContent = novoSaldo;
                currentStock.saldo = novoSaldo;

                log(`🎯 Correção concluída com sucesso!`, 'success');
                document.getElementById('statusGeral').textContent = 'Correção concluída com sucesso';

                // Limpar formulário
                document.getElementById('novoSaldo').value = '';
                document.getElementById('motivoCorrecao').value = '';
                document.getElementById('correctionPreview').style.display = 'none';
                document.getElementById('btnExecutar').disabled = true;

            } catch (error) {
                log(`❌ Erro na correção: ${error.message}`, 'error');
                document.getElementById('statusGeral').textContent = 'Erro na correção';
            }
        };

        window.buscarTodosProblemas = async function() {
            log('📋 Buscando todos os itens com problemas de saldo...', 'info');
            document.getElementById('statusGeral').textContent = 'Buscando problemas...';

            try {
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                let problemsList = '';

                estoques.forEach(estoque => {
                    if (estoque.saldo === undefined || estoque.saldo === null || estoque.saldo < 0) {
                        problemas++;
                        problemsList += `- ID: ${estoque.id}, Produto: ${estoque.produtoId}, Saldo: ${estoque.saldo}\n`;
                    }
                });

                if (problemas > 0) {
                    log(`⚠️ ${problemas} itens com problemas encontrados:`, 'warning');
                    log(problemsList, 'info');
                } else {
                    log('✅ Nenhum problema de saldo encontrado no estoque', 'success');
                }

                document.getElementById('statusGeral').textContent = `${problemas} problemas encontrados`;

            } catch (error) {
                log(`❌ Erro na busca: ${error.message}`, 'error');
            }
        };

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de correção de saldo de estoque carregado', 'info');
        };
    </script>
</body>
</html>
