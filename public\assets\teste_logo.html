<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Logo FYRON MRP</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .logo-test {
            border: 2px dashed #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: center;
            background: #f9f9f9;
        }
        
        .logo-test h3 {
            margin-top: 0;
            color: #555;
        }
        
        .logo-test img {
            max-width: 100%;
            height: auto;
            border: 1px solid #eee;
            border-radius: 5px;
            background: white;
            padding: 10px;
        }
        
        .dark-bg {
            background: #2c3e50;
            color: white;
        }
        
        .dark-bg h3 {
            color: white;
        }
        
        .instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #0c5460;
            margin-top: 0;
        }
        
        .file-input {
            margin: 20px 0;
            padding: 15px;
            border: 2px dashed #007bff;
            border-radius: 10px;
            text-align: center;
            background: #f8f9ff;
        }
        
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
            max-width: 300px;
        }
        
        .preview {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
        }
        
        .size-info {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Teste do Logo FYRON MRP</h1>
        
        <div class="instructions">
            <h3>📋 Como usar este teste:</h3>
            <ol>
                <li><strong>Selecione sua imagem</strong> do logo usando o campo abaixo</li>
                <li><strong>Visualize</strong> como ficará em diferentes contextos</li>
                <li><strong>Copie o arquivo</strong> para <code>assets/fyron_logo.png</code></li>
                <li><strong>Atualize</strong> o sistema principal</li>
            </ol>
        </div>
        
        <div class="file-input">
            <h3>📁 Selecione seu logo:</h3>
            <input type="file" id="logoInput" accept="image/*" onchange="previewLogo(this)">
            <p style="font-size: 12px; color: #666;">Formatos aceitos: PNG, JPG, GIF, SVG</p>
        </div>
        
        <div id="previewContainer" style="display: none;">
            <div class="logo-test">
                <h3>🖥️ Logo na Sidebar (Fundo Claro)</h3>
                <img id="preview1" style="width: 280px; height: auto; max-height: 120px; object-fit: contain;">
                <div class="size-info">Tamanho: 280px de largura</div>
            </div>
            
            <div class="logo-test dark-bg">
                <h3>🌙 Logo na Sidebar (Fundo Escuro)</h3>
                <img id="preview2" style="width: 280px; height: auto; max-height: 120px; object-fit: contain;">
                <div class="size-info">Tamanho: 280px de largura</div>
            </div>
            
            <div class="logo-test">
                <h3>📱 Logo Responsivo (Mobile)</h3>
                <img id="preview3" style="width: 200px; height: auto; max-height: 80px; object-fit: contain;">
                <div class="size-info">Tamanho: 200px de largura</div>
            </div>
            
            <div class="logo-test">
                <h3>🏷️ Logo Compacto (Cabeçalho)</h3>
                <img id="preview4" style="width: 150px; height: auto; max-height: 60px; object-fit: contain;">
                <div class="size-info">Tamanho: 150px de largura</div>
            </div>
            
            <div class="instructions">
                <h3>✅ Checklist de Qualidade:</h3>
                <ul>
                    <li>O logo está nítido em todos os tamanhos?</li>
                    <li>É legível em fundo claro e escuro?</li>
                    <li>As proporções estão corretas?</li>
                    <li>O arquivo tem boa qualidade?</li>
                </ul>
                
                <h3>📂 Próximos passos:</h3>
                <ol>
                    <li>Salve sua imagem como <code>fyron_logo.png</code></li>
                    <li>Copie para a pasta <code>c:\app_naliteck\assets\</code></li>
                    <li>Atualize o navegador no sistema principal (F5)</li>
                </ol>
            </div>
        </div>
    </div>
    
    <script>
        function previewLogo(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const imageSrc = e.target.result;
                    
                    // Atualiza todas as previews
                    document.getElementById('preview1').src = imageSrc;
                    document.getElementById('preview2').src = imageSrc;
                    document.getElementById('preview3').src = imageSrc;
                    document.getElementById('preview4').src = imageSrc;
                    
                    // Mostra o container de preview
                    document.getElementById('previewContainer').style.display = 'block';
                    
                    // Scroll suave para as previews
                    document.getElementById('previewContainer').scrollIntoView({
                        behavior: 'smooth'
                    });
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
