<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>👥 Cadastro de Usuários</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    /* ========================================
       🎨 CSS PADRONIZADO - CADASTRO USUÁRIOS
       Baseado em: gestao_compras_integrada.html
       ======================================== */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header h1::before {
      content: '👥';
      font-size: 32px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }
    
    .main-content {
      padding: 30px;
    }

    .search-container {
      margin-bottom: 25px;
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }
    
    .search-container input {
      flex: 1;
      min-width: 250px;
      padding: 12px 15px 12px 45px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>');
      background-repeat: no-repeat;
      background-position: 15px center;
    }

    .search-container input:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    
    .form-container {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }
    
    .form-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .form-col {
      flex: 1;
    }
    
    label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
      display: block;
    }
    
    .form-control, input, select {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      width: 100%;
    }
    
    .form-control:focus, input:focus, select:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }
    
    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }
    
    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }
    
    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }
    
    .table-container {
      background: white;
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      margin-bottom: 25px;
    }

    .users-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .users-table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 15px 12px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border: none;
    }

    .users-table th:first-child {
      border-radius: 8px 0 0 0;
    }

    .users-table th:last-child {
      border-radius: 0 8px 0 0;
    }

    .users-table td {
      padding: 15px 12px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .users-table tr:hover {
      background-color: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;
    }

    .edit-btn, .delete-btn, .permissions-btn {
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 5px;
    }

    .edit-btn {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .edit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(243, 156, 18, 0.3);
    }

    .delete-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
    }

    .permissions-btn {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .permissions-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
    }

    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
    }

    .status-active {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
    }

    .status-inactive {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      color: #721c24;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 2px solid #e9ecef;
    }

    .required::after {
      content: "*";
      color: #e74c3c;
      margin-left: 4px;
    }

    .info-text {
      font-size: 12px;
      color: #6c757d;
      margin-top: 5px;
      font-style: italic;
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .alert-warning {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      border: 2px solid #f39c12;
      color: #856404;
    }

    .alert-info {
      background: linear-gradient(135deg, #d1ecf1 0%, #b8daff 100%);
      border: 2px solid #3498db;
      color: #0c5460;
    }

    .level-info {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 25px;
      border: 2px solid #dee2e6;
    }

    .level-info h3 {
      color: #2c3e50;
      margin-bottom: 15px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .level-info h3::before {
      content: '🔐';
      font-size: 20px;
    }

    .level-info ul {
      list-style: none;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 12px;
    }

    .level-info li {
      background: white;
      padding: 12px 15px;
      border-radius: 8px;
      border-left: 4px solid #3498db;
      font-size: 13px;
      color: #495057;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .level-info li strong {
      color: #2c3e50;
      font-weight: 600;
    }

    .back-button {
      margin: 20px 30px;
    }

    @media (max-width: 768px) {
      .form-row {
        flex-direction: column;
        gap: 15px;
      }
      
      .search-container {
        flex-direction: column;
      }
      
      .level-info ul {
        grid-template-columns: 1fr;
      }
      
      .action-buttons {
        flex-direction: column;
        gap: 5px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Cadastro de Usuários</h1>
      <div class="header-actions">
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <div class="level-info">
        <h3>Níveis de Acesso</h3>
        <ul>
          <li><strong>Nível 1 - Básico:</strong> Apenas visualização de dados e relatórios básicos</li>
          <li><strong>Nível 2 - Operador:</strong> Visualização + operações básicas do dia a dia</li>
          <li><strong>Nível 3 - Supervisor:</strong> Operações + aprovações e gestão de equipe</li>
          <li><strong>Nível 4 - Analista:</strong> Supervisor + análise de dados e relatórios avançados</li>
          <li><strong>Nível 5 - Coordenador:</strong> Analista + gestão departamental</li>
          <li><strong>Nível 6 - Gerente:</strong> Coordenador + gestão estratégica</li>
          <li><strong>Nível 7 - Diretor:</strong> Gerente + decisões executivas</li>
          <li><strong>Nível 8 - Presidente:</strong> Diretor + gestão corporativa</li>
          <li><strong>Nível 9 - Administrador:</strong> Acesso total ao sistema</li>
        </ul>
      </div>
      
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="🔍 Buscar usuários..." oninput="filterUsers()">
      </div>
      
      <div class="form-container">
        <h2 class="form-title">
          <i class="fas fa-user-plus"></i> Cadastrar Novo Usuário
        </h2>
        <div id="adminWarning" class="alert alert-warning" style="display: none;">
          <i class="fas fa-exclamation-triangle"></i>
          <span>Atenção: Você está criando um usuário com nível de administrador. Este usuário terá acesso completo ao sistema.</span>
        </div>
      
      <form id="userForm">
        <input type="hidden" id="editingId">
        
        <div class="form-row">
          <div class="form-col">
            <label for="nome" class="required">Nome de Usuário</label>
            <input type="text" id="nome" required>
            <div class="info-text">Nome único para login no sistema</div>
          </div>
          <div class="form-col">
            <label for="senha" class="required">Senha</label>
            <input type="password" id="senha" required>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-col">
            <label for="nomeCompleto" class="required">Nome Completo</label>
            <input type="text" id="nomeCompleto" required>
          </div>
          <div class="form-col">
            <label for="email" class="required">Email</label>
            <input type="email" id="email" required>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-col">
            <label for="departamento">Departamento</label>
            <select id="departamento">
              <option value="">Selecione...</option>
              <option value="ADMINISTRATIVO">Administrativo</option>
              <option value="COMERCIAL">Comercial</option>
              <option value="COMPRAS">Compras</option>
              <option value="ENGENHARIA">Engenharia</option>
              <option value="FINANCEIRO">Financeiro</option>
              <option value="PRODUCAO">Produção</option>
              <option value="TI">TI</option>
            </select>
          </div>
          <div class="form-col">
            <label for="nivel" class="required">Nível de Acesso</label>
            <select id="nivel" required onchange="checkAdminLevel()">
              <option value="1">1 - Básico (Visualização)</option>
              <option value="2">2 - Operador</option>
              <option value="3">3 - Supervisor</option>
              <option value="4">4 - Analista</option>
              <option value="5">5 - Coordenador</option>
              <option value="6">6 - Gerente</option>
              <option value="7">7 - Diretor</option>
              <option value="8">8 - Presidente</option>
              <option value="9">9 - Administrador (Acesso Total)</option>
            </select>
            <div class="info-text">Define o nível de acesso do usuário no sistema</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-col">
            <label for="ativo">Status</label>
            <select id="ativo">
              <option value="true">Ativo</option>
              <option value="false">Inativo</option>
            </select>
          </div>
          <div class="form-col">
            <label for="telefone">Telefone</label>
            <input type="tel" id="telefone">
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
            <i class="fas fa-times"></i> Cancelar
          </button>
          <button type="submit" class="btn btn-success" id="submitButton">
            <i class="fas fa-save"></i> Cadastrar Usuário
          </button>
        </div>
      </form>
    </div>
    
    <div class="table-container">
      <h2 class="form-title">
        <i class="fas fa-users"></i> Usuários Cadastrados
      </h2>
      <table class="users-table">
        <thead>
          <tr>
            <th><i class="fas fa-user"></i> Nome de Usuário</th>
            <th><i class="fas fa-id-card"></i> Nome Completo</th>
            <th><i class="fas fa-envelope"></i> Email</th>
            <th><i class="fas fa-building"></i> Departamento</th>
            <th><i class="fas fa-shield-alt"></i> Nível</th>
            <th><i class="fas fa-toggle-on"></i> Status</th>
            <th><i class="fas fa-cogs"></i> Ações</th>
          </tr>
        </thead>
        <tbody id="usersTableBody">
        </tbody>
      </table>
    </div>
    
    <button onclick="window.location.href='index.html'" class="btn btn-secondary back-button">
      <i class="fas fa-home"></i> Voltar para o Menu
    </button>
    </div>
  </div>
  
  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs, 
      doc, 
      getDoc,
      updateDoc, 
      deleteDoc,
      query,
      where 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let usuarios = [];
    let currentUser = null;

    window.onload = async function() {
      // Verificar se o usuário atual é administrador
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }

      currentUser = JSON.parse(userSession);
      if (currentUser.nivel < 9) {
        alert('Acesso restrito. Apenas administradores podem acessar esta página.');
        window.location.href = 'index.html';
        return;
      }

      await loadUsers();
      displayUsers();
    };

    async function loadUsers() {
      try {
        const snapshot = await getDocs(collection(db, "usuarios"));
        usuarios = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        alert("Erro ao carregar usuários. Por favor, recarregue a página.");
      }
    }

    function displayUsers() {
      const tableBody = document.getElementById('usersTableBody');
      tableBody.innerHTML = '';

      usuarios.forEach(usuario => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${usuario.nome}</td>
          <td>${usuario.nomeCompleto || '-'}</td>
          <td>${usuario.email || '-'}</td>
          <td>${usuario.departamento || '-'}</td>
          <td>${getNivelDescricao(usuario.nivel)}</td>
          <td><span class="status-badge ${usuario.ativo ? 'status-active' : 'status-inactive'}">${usuario.ativo ? 'Ativo' : 'Inativo'}</span></td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editUser('${usuario.id}')">
              <i class="fas fa-edit"></i> Editar
            </button>
            <button class="permissions-btn" onclick="window.location.href='permissoes_usuario.html?id=${usuario.id}'">
              <i class="fas fa-key"></i> Permissões
            </button>
            <button class="delete-btn" onclick="deleteUser('${usuario.id}')">
              <i class="fas fa-trash"></i> Excluir
            </button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function getNivelDescricao(nivel) {
      switch (parseInt(nivel)) {
        case 1: return "1 - Básico";
        case 2: return "2 - Operador";
        case 3: return "3 - Supervisor";
        case 4: return "4 - Analista";
        case 5: return "5 - Coordenador";
        case 6: return "6 - Gerente";
        case 7: return "7 - Diretor";
        case 8: return "8 - Presidente";
        case 9: return "9 - Administrador";
        default: return nivel;
      }
    }

    window.checkAdminLevel = function() {
      const nivelSelect = document.getElementById('nivel');
      const adminWarning = document.getElementById('adminWarning');
      
      if (nivelSelect.value === '9') {
        adminWarning.style.display = 'block';
      } else {
        adminWarning.style.display = 'none';
      }
    };

    window.editUser = function(userId) {
      const usuario = usuarios.find(u => u.id === userId);
      if (usuario) {
        document.getElementById('editingId').value = userId;
        document.getElementById('nome').value = usuario.nome;
        document.getElementById('senha').value = ''; // Não exibir a senha atual
        document.getElementById('nomeCompleto').value = usuario.nomeCompleto || '';
        document.getElementById('email').value = usuario.email || '';
        document.getElementById('departamento').value = usuario.departamento || '';
        document.getElementById('nivel').value = usuario.nivel;
        document.getElementById('ativo').value = usuario.ativo.toString();
        document.getElementById('telefone').value = usuario.telefone || '';
        
        checkAdminLevel();
        
        document.getElementById('submitButton').innerHTML = '<i class="fas fa-edit"></i> Atualizar Usuário';
      }
    };

    window.deleteUser = async function(userId) {
      // Verificar se é o usuário admin padrão
      const usuario = usuarios.find(u => u.id === userId);
      if (usuario && usuario.nome === 'admin') {
        alert('O usuário administrador padrão não pode ser excluído.');
        return;
      }
      
      // Verificar se é o usuário atual
      if (userId === currentUser.id) {
        alert('Você não pode excluir seu próprio usuário.');
        return;
      }
      
      if (confirm('Tem certeza que deseja excluir este usuário?')) {
        try {
          await deleteDoc(doc(db, "usuarios", userId));
          await loadUsers();
          displayUsers();
          alert('Usuário excluído com sucesso!');
        } catch (error) {
          console.error("Erro ao excluir usuário:", error);
          alert("Erro ao excluir usuário. Por favor, tente novamente.");
        }
      }
    };

    window.cancelEdit = function() {
      document.getElementById('userForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('adminWarning').style.display = 'none';
      document.getElementById('submitButton').innerHTML = '<i class="fas fa-save"></i> Cadastrar Usuário';
    };

    window.filterUsers = function() {
      const searchText = document.getElementById('searchInput').value.toLowerCase();
      const filteredUsers = usuarios.filter(usuario => 
        usuario.nome.toLowerCase().includes(searchText) ||
        (usuario.nomeCompleto && usuario.nomeCompleto.toLowerCase().includes(searchText)) ||
        (usuario.email && usuario.email.toLowerCase().includes(searchText)) ||
        (usuario.departamento && usuario.departamento.toLowerCase().includes(searchText))
      );
      
      const tableBody = document.getElementById('usersTableBody');
      tableBody.innerHTML = '';

      filteredUsers.forEach(usuario => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${usuario.nome}</td>
          <td>${usuario.nomeCompleto || '-'}</td>
          <td>${usuario.email || '-'}</td>
          <td>${usuario.departamento || '-'}</td>
          <td>${getNivelDescricao(usuario.nivel)}</td>
          <td><span class="status-badge ${usuario.ativo ? 'status-active' : 'status-inactive'}">${usuario.ativo ? 'Ativo' : 'Inativo'}</span></td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editUser('${usuario.id}')">
              <i class="fas fa-edit"></i> Editar
            </button>
            <button class="permissions-btn" onclick="window.location.href='permissoes_usuario.html?id=${usuario.id}'">
              <i class="fas fa-key"></i> Permissões
            </button>
            <button class="delete-btn" onclick="deleteUser('${usuario.id}')">
              <i class="fas fa-trash"></i> Excluir
            </button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    };

    document.getElementById('userForm').addEventListener('submit', async function(event) {
      event.preventDefault();
      
      const editingId = document.getElementById('editingId').value;
      const nome = document.getElementById('nome').value;
      const senha = document.getElementById('senha').value;
      
      // Verificar se já existe um usuário com o mesmo nome
      if (!editingId) {
        const existingUser = usuarios.find(u => u.nome.toLowerCase() === nome.toLowerCase());
        if (existingUser) {
          alert('Já existe um usuário com este nome. Por favor, escolha outro nome de usuário.');
          return;
        }
      }
      
      // Se estiver editando e a senha estiver vazia, manter a senha atual
      let senhaFinal = senha;
      if (editingId && !senha) {
        const usuarioAtual = usuarios.find(u => u.id === editingId);
        if (usuarioAtual) {
          senhaFinal = usuarioAtual.senha;
        }
      }
      
      const userData = {
        nome: nome,
        senha: senhaFinal,
        nomeCompleto: document.getElementById('nomeCompleto').value,
        email: document.getElementById('email').value,
        departamento: document.getElementById('departamento').value,
        nivel: parseInt(document.getElementById('nivel').value),
        ativo: document.getElementById('ativo').value === 'true',
        telefone: document.getElementById('telefone').value,
        dataCadastro: new Date()
      };

      try {
        if (editingId) {
          // Atualizar usuário existente
          await updateDoc(doc(db, "usuarios", editingId), userData);
          alert('Usuário atualizado com sucesso!');
        } else {
          // Criar novo usuário
          await addDoc(collection(db, "usuarios"), userData);
          alert('Usuário cadastrado com sucesso!');
        }
        
        await loadUsers();
        displayUsers();
        cancelEdit();
      } catch (error) {
        console.error("Erro ao salvar usuário:", error);
        alert("Erro ao salvar usuário. Por favor, tente novamente.");
      }
    });
  </script>
</body>
</html>