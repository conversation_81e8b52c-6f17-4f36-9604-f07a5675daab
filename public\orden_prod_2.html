<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestão de Ordens de Produção</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 30px auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    h1, h2 {
      color: #333;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: bold;
    }

    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 10px;
    }

    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }

    button:hover {
      background-color: #45a049;
    }

    .button-group {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .secondary-button {
      background-color: #2196F3;
    }

    .danger-button {
      background-color: #f44336;
    }

    .back-button {
      background-color: #6c757d;
    }

    .op-list {
      margin-top: 30px;
    }

    .op-item {
      background-color: #f9f9f9;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 4px;
      border-left: 4px solid #4CAF50;
    }

    .child-op {
      margin-left: 30px;
      border-left: 4px solid #2196F3;
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      margin-left: 10px;
    }

    .status-pendente { background-color: #FFC107; color: #000; }
    .status-em-producao { background-color: #2196F3; color: #fff; }
    .status-concluida { background-color: #4CAF50; color: #fff; }
    .status-cancelada { background-color: #f44336; color: #fff; }

    .inventory-status {
      margin-top: 5px;
      font-size: 14px;
      color: #666;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      width: 80%;
      max-width: 600px;
      border-radius: 8px;
      position: relative;
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .components-list {
      margin: 10px 0;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .component-item {
      margin: 5px 0;
      padding: 5px;
      border-bottom: 1px solid #ddd;
    }

    .tab-container {
      margin-bottom: 20px;
    }

    .tab-buttons {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .tab-button {
      padding: 10px 20px;
      background-color: #f0f0f0;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      color: #333;
    }

    .tab-button.active {
      background-color: #4CAF50;
      color: white;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .filter-section {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .search-input {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .filter-options {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .filter-group {
      flex: 1;
      min-width: 200px;
    }

    .op-details {
      margin-top: 10px;
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
    }

    .op-actions {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 10px;
      overflow: hidden;
      margin-top: 5px;
    }

    .progress-fill {
      height: 100%;
      background-color: #4CAF50;
      transition: width 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header-actions">
  <h1>Gestão de Ordens de Produção</h1>
  <div class="button-group">
    <button id="newOrderButton" class="secondary-button">Nova Ordem Manual</button>
    <button id="orderFromSalesButton" class="secondary-button">Ordem de Pedido</button>
    <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
  </div>
</div>
    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" onclick="switchTab('active')">Ordens Ativas</button>
        <button class="tab-button" onclick="switchTab('completed')">Ordens Concluídas</button>
      </div>

      <div class="filter-section">
        <input type="text" class="search-input" placeholder="Buscar por produto, número da ordem..." oninput="filterOrders()">
        <div class="filter-options">
          <div class="filter-group">
            <label>Status:</label>
            <select onchange="filterOrders()">
              <option value="">Todos</option>
              <option value="pendente">Pendente</option>
              <option value="em-producao">Em Produção</option>
              <option value="concluida">Concluída</option>
              <option value="cancelada">Cancelada</option>
            </select>
          </div>
          <div class="filter-group">
            <label>Período:</label>
            <input type="date" onchange="filterOrders()">
          </div>
        </div>
      </div>

      <div id="activeOrders" class="tab-content active">
        <div class="op-list" id="activeOrdersList"></div>
      </div>

      <div id="completedOrders" class="tab-content">
        <div class="op-list" id="completedOrdersList"></div>
      </div>
    </div>
  </div>

  <!-- Modal Nova Ordem Manual -->
  <div id="newOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('newOrderModal')">&times;</span>
      <h2>Nova Ordem de Produção</h2>
      <form id="newOrderForm" onsubmit="createManualOrder(event)">
        <div class="form-group">
          <label for="productSearch">Produto:</label>
          <input type="text" id="productSearch" placeholder="Digite para buscar o produto...">
          <select id="productSelect" required>
            <option value="">Selecione o produto...</option>
          </select>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="quantity">Quantidade:</label>
            <input type="number" id="quantity" min="0.001" step="0.001" required>
          </div>
          <div class="form-col">
            <label for="dueDate">Data de Entrega:</label>
            <input type="date" id="dueDate" required>
          </div>
        </div>

        <div class="form-group">
          <label for="priority">Prioridade:</label>
          <select id="priority" required>
            <option value="normal">Normal</option>
            <option value="alta">Alta</option>
            <option value="urgente">Urgente</option>
          </select>
        </div>

        <div class="form-group">
          <label for="observations">Observações:</label>
          <textarea id="observations" rows="3"></textarea>
        </div>

        <div class="button-group">
          <button type="submit">Criar Ordem</button>
          <button type="button" onclick="closeModal('newOrderModal')" class="back-button">Cancelar</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal Ordem de Pedido -->
  <div id="salesOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('salesOrderModal')">&times;</span>
      <h2>Criar Ordem de Pedido</h2>
      <div class="form-group">
        <label>Pedido:</label>
        <select id="salesOrderSelect" onchange="loadSalesOrderDetails()">
          <option value="">Selecione o pedido...</option>
        </select>
      </div>
      <div id="salesOrderDetails"></div>
      <div class="button-group">
        <button onclick="createOrderFromSales()">Criar Ordem</button>
        <button onclick="closeModal('salesOrderModal')" class="back-button">Cancelar</button>
      </div>
    </div>
  </div>

  <!-- Modal de Apontamento -->
  <div id="appointmentModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('appointmentModal')">&times;</span>
      <h2>Apontamento de Produção</h2>
      <div id="appointmentDetails"></div>
      <form id="appointmentForm" onsubmit="submitAppointment(event)">
        <div class="form-row">
          <div class="form-col">
            <label for="producedQuantity">Quantidade Produzida:</label>
            <input type="number" id="producedQuantity" min="0.001" step="0.001" required>
          </div>
          <div class="form-col">
            <label for="scrapQuantity">Quantidade de Refugo:</label>
            <input type="number" id="scrapQuantity" min="0" step="0.001" value="0">
          </div>
        </div>

        <div class="form-group">
          <label for="appointmentObservations">Observações:</label>
          <textarea id="appointmentObservations" rows="3"></textarea>
        </div>

        <div class="button-group">
          <button type="submit">Confirmar Apontamento</button>
          <button type="button" onclick="closeModal('appointmentModal')" class="back-button">Cancelar</button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc,
      setDoc,
      runTransaction,
      getDoc,
      getDocs,
      query,
      where,
      doc, 
      updateDoc,
      Timestamp,
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let pedidosVenda = [];
    let ordensProducao = [];
    let estoques = [];
    let currentAppointmentOp = null;
    let counterRef;

    window.onload = async function() {
      await loadInitialData();
      setupProductSearch();
      await loadActiveOrders();
      setupSalesOrderSelect();
      
      // Initialize counter document if it doesn't exist
      counterRef = doc(db, "contadores", "ordens");
      const counterDoc = await getDoc(counterRef);
      if (!counterDoc.exists()) {
        await setDoc(counterRef, { valor: 0 });
      }
    };

    async function generateOrderNumber() {
      const date = new Date();
      const year = date.getFullYear().toString().substr(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');

      try {
        // Use transaction to ensure atomic increment
        const newCounter = await runTransaction(db, async (transaction) => {
          const counterDoc = await transaction.get(counterRef);
          if (!counterDoc.exists()) {
            throw new Error("Counter document does not exist!");
          }
          const newValue = counterDoc.data().valor + 1;
          
          transaction.update(counterRef, { valor: newValue });
          return newValue;
        });

        // Format number with 4 digits
        const sequence = newCounter.toString().padStart(4, '0');
        return `OP${year}${month}${sequence}`;
      } catch (error) {
        console.error("Erro ao gerar número da ordem:", error);
        throw error;
      }
    }

    async function loadInitialData() {
      try {
        const [produtosSnap, estruturasSnap, pedidosSnap, ordensSnap, estoquesSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "pedidosVenda")),
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "estoques"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        pedidosVenda = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais.");
      }
    }

    function setupProductSearch() {
      const productSearch = document.getElementById('productSearch');
      productSearch.addEventListener('input', () => {
        const searchText = productSearch.value.toLowerCase();
        updateProductSelect(searchText);
      });
    }

    function updateProductSelect(searchText = '') {
      const productSelect = document.getElementById('productSelect');
      productSelect.innerHTML = '<option value="">Selecione o produto...</option>';

      produtos
        .filter(p => (p.tipo === 'PA' || p.tipo === 'SP') &&
                    (!searchText || 
                     p.codigo.toLowerCase().includes(searchText) ||
                     p.descricao.toLowerCase().includes(searchText)))
        .forEach(produto => {
          productSelect.innerHTML += `
            <option value="${produto.id}">
              ${produto.codigo} - ${produto.descricao} (${produto.tipo})
            </option>`;
        });
    }

    function setupSalesOrderSelect() {
      const select = document.getElementById('salesOrderSelect');
      select.innerHTML = '<option value="">Selecione o pedido...</option>';

      const pendingOrders = pedidosVenda.filter(p => p.status === 'Pendente');
      pendingOrders.forEach(pedido => {
        const produto = produtos.find(p => p.id === pedido.produtoId);
        select.innerHTML += `
          <option value="${pedido.id}">
            ${pedido.numero} - ${produto.codigo} - ${produto.descricao}
          </option>`;
      });
    }

    window.loadSalesOrderDetails = function() {
      const pedidoId = document.getElementById('salesOrderSelect').value;
      const detailsDiv = document.getElementById('salesOrderDetails');
      
      if (!pedidoId) {
        detailsDiv.innerHTML = '';
        return;
      }

      const pedido = pedidosVenda.find(p => p.id === pedidoId);
      const produto = produtos.find(p => p.id === pedido.produtoId);

      detailsDiv.innerHTML = `
        <div class="order-details">
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade:</strong> ${pedido.quantidade} ${produto.unidade}</p>
          <p><strong>Data de Entrega:</strong> ${new Date(pedido.dataEntrega.seconds * 1000).toLocaleDateString()}</p>
        </div>`;
    };

    window.createOrderFromSales = async function() {
      const pedidoId = document.getElementById('salesOrderSelect').value;
      if (!pedidoId) {
        alert('Selecione um pedido.');
        return;
      }

      const pedido = pedidosVenda.find(p => p.id === pedidoId);
      const produto = produtos.find(p => p.id === pedido.produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === pedido.produtoId);

      if (!estrutura) {
        alert(`O produto ${produto.codigo} não possui estrutura cadastrada.`);
        return;
      }

      try {
        // Criar ordem pai
        const parentOp = {
          numero: await generateOrderNumber(),
          pedidoId: pedido.id,
          produtoId: pedido.produtoId,
          quantidade: pedido.quantidade,
          dataEntrega: pedido.dataEntrega,
          status: 'Pendente',
          nivel: 0,
          prioridade: 'normal',
          dataCriacao: Timestamp.now()
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        // Atualizar status do pedido
        await updateDoc(doc(db, "pedidosVenda", pedidoId), {
          status: 'Em Produção'
        });

        // Criar ordens filhas
        await explodeComponents(parentOp, estrutura);

        alert('Ordem de produção criada com sucesso!');
        closeModal('salesOrderModal');
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    };

    window.createManualOrder = async function(event) {
      event.preventDefault();

      const productId = document.getElementById('productSelect').value;
      const quantity = parseFloat(document.getElementById('quantity').value);
      const dueDate = document.getElementById('dueDate').value;
      const priority = document.getElementById('priority').value;
      const observations = document.getElementById('observations').value;

      if (!productId || !quantity || !dueDate) {
        alert('Preencha todos os campos obrigatórios.');
        return;
      }

      const estrutura = estruturas.find(e => e.produtoPaiId === productId);
      if (!estrutura) {
        const produto = produtos.find(p => p.id === productId);
        alert(`O produto ${produto.codigo} não possui estrutura cadastrada.`);
        return;
      }

      try {
        // Criar ordem pai
        const parentOp = {
          numero: await generateOrderNumber(),
          produtoId: productId,
          produtoPaiId: productId,
          quantidade: quantity,
          dataEntrega: new Date(dueDate),
          status: 'Pendente',
          nivel: 0,
          prioridade: priority,
          observacoes: observations,
          dataCriacao: Timestamp.now()
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        // Criar ordens filhas
        await explodeComponents(parentOp, estrutura);

        alert('Ordem de produção criada com sucesso!');
        closeModal('newOrderModal');
        document.getElementById('newOrderForm').reset();
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    };

    async function explodeComponents(parentOp, estrutura, level = 0) {
  const childOrders = [];
  const materialNeeds = [];
  const spNeeds = [];

  for (const componente of estrutura.componentes) {
    const produto = produtos.find(p => p.id === componente.componentId);
    const quantidadeNecessaria = parentOp.quantidade * componente.quantidade;
    
    const saldoDisponivel = await checkInventory(componente.componentId);
    const quantidadeReservada = Math.min(saldoDisponivel, quantidadeNecessaria);
    const necessidade = Math.max(0, quantidadeNecessaria - quantidadeReservada);

    const materialNeed = {
      produtoId: componente.componentId,
      quantidade: quantidadeNecessaria,
      saldoEstoque: saldoDisponivel + quantidadeReservada, // Saldo total antes da reserva
      quantidadeReservada: quantidadeReservada,
      necessidade: necessidade
    };

    if (quantidadeReservada > 0) {
      await updateInventoryReservation(componente.componentId, quantidadeReservada);
    }

    if (produto.tipo === 'SP') {
      spNeeds.push(materialNeed);
      // Lógica para semi-produtos continua igual
    } else {
      materialNeeds.push(materialNeed);
    }
  }

  if (materialNeeds.length > 0 || spNeeds.length > 0) {
    await updateDoc(doc(db, "ordensProducao", parentOp.id), {
      materiaisNecessarios: [...materialNeeds, ...spNeeds]
    });
  }

  return childOrders;
}

async function updateInventoryReservation(produtoId, quantidade) {
    const estoque = estoques.find(e => e.produtoId === produtoId);
    if (estoque) {
        const novoSaldoReservado = (estoque.saldoReservado || 0) + quantidade; // Quantidade pode ser negativa para liberar
        await updateDoc(doc(db, "estoques", estoque.id), {
            saldoReservado: Math.max(0, novoSaldoReservado), // Garante que não fique negativo
            ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldoReservado = Math.max(0, novoSaldoReservado);
    }
}

    async function checkInventory(produtoId) {
    const estoque = estoques.find(e => e.produtoId === produtoId);
    const saldoTotal = estoque ? estoque.saldo : 0;
    const saldoReservado = estoque ? (estoque.saldoReservado || 0) : 0;
    return saldoTotal - saldoReservado; // Saldo disponível
    }

    async function updateInventory(produtoId, quantidade, tipo) {
      const estoqueRef = estoques.find(e => e.produtoId === produtoId);
      
      if (estoqueRef) {
        const novoSaldo = tipo === 'entrada' ? 
          estoqueRef.saldo + quantidade : 
          estoqueRef.saldo - quantidade;

        await updateDoc(doc(db, "estoques", estoqueRef.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });

        estoqueRef.saldo = novoSaldo;
      } else {
        const novoEstoque = {
          produtoId,
          saldo: tipo === 'entrada' ? quantidade : -quantidade,
          ultimaMovimentacao: Timestamp.now()
        };

        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        novoEstoque.id = docRef.id;
        estoques.push(novoEstoque);
      }
    }

    async function loadActiveOrders() {
      const activeList = document.getElementById('activeOrdersList');
      const completedList = document.getElementById('completedOrdersList');
      
      activeList.innerHTML = '';
      completedList.innerHTML = '';

      // Ordenar ordens por nível e data de criação
      const sortedOrders = ordensProducao.sort((a, b) => {
        if (a.nivel === b.nivel) {
          // Se o nível for igual, ordena pela data de criação (mais recente primeiro)
          return b.dataCriacao.seconds - a.dataCriacao.seconds;
        }
        // Ordena pelo nível (OPs pai primeiro)
        return a.nivel - b.nivel;
      });

      for (const op of sortedOrders) {
        const produto = produtos.find(p => p.id === op.produtoId);
        const estoque = estoques.find(e => e.produtoId === op.produtoId);
        const saldoEstoque = estoque ? estoque.saldo : 0;

        const opElement = document.createElement('div');
        opElement.className = `op-item ${op.nivel > 0 ? 'child-op' : ''}`;
        
        let componentesHtml = '';
        if (op.materiaisNecessarios) {
          componentesHtml = '<div class="components-list"><strong>Materiais necessários:</strong><ul>';
          for (const material of op.materiaisNecessarios) {
            const materialProduto = produtos.find(p => p.id === material.produtoId);
            const disponibilidade = (material.saldoEstoque / material.quantidade * 100).toFixed(1);
            
            componentesHtml += `
              <li class="component-item">
                ${materialProduto.codigo} - ${materialProduto.descricao}
                <div class="inventory-status">
                  Necessário: ${material.quantidade} ${materialProduto.unidade}
                  | Estoque: ${material.saldoEstoque} ${materialProduto.unidade}
                  | Falta: ${material.necessidade} ${materialProduto.unidade}
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${Math.min(100, disponibilidade)}%"></div>
                </div>
              </li>`;
          }
          componentesHtml += '</ul></div>';
        }

        const progress = op.quantidadeProduzida ? 
          (op.quantidadeProduzida / op.quantidade * 100).toFixed(1) : 0;

        opElement.innerHTML = `
          <div class="op-header">
            <strong>${op.numero} - ${produto.codigo} - ${produto.descricao}</strong>
            <span class="status-badge status-${op.status.toLowerCase()}">${op.status}</span>
          </div>
          <div class="op-details">
            <div>Quantidade: ${op.quantidade} ${produto.unidade}</div>
            <div>Entrega: ${new Date(op.dataEntrega.seconds * 1000).toLocaleDateString()}</div>
            <div>Prioridade: ${op.prioridade || 'Normal'}</div>
            ${op.pedidoId ? `<div>Pedido: ${pedidosVenda.find(p => p.id === op.pedidoId)?.numero || ''}</div>` : ''}
            ${op.produtoPaiId ? `<div>Produto Pai: ${produtos.find(p => p.id === op.produtoPaiId)?.codigo || ''}</div>` : ''}
            <div class="inventory-status">Saldo em Estoque: ${saldoEstoque} ${produto.unidade}</div>
            ${op.quantidadeProduzida ? `
              <div>
                Progresso: ${progress}%
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
              </div>
            ` : ''}
            ${componentesHtml}
          </div>
          <div class="op-actions">
            ${op.status !== 'Concluída' && op.status !== 'Cancelada' ? `
              <button onclick="openAppointmentModal('${op.id}')" class="secondary-button">Apontar Produção</button>
              <button onclick="cancelOrder('${op.id}')" class="danger-button">Cancelar Ordem</button>
            ` : ''}
          </div>
        `;

        if (op.status === 'Concluída' || op.status === 'Cancelada') {
          completedList.appendChild(opElement);
        } else {
          activeList.appendChild(opElement);
        }
      }
    }

    window.openAppointmentModal = function(opId) {
      currentAppointmentOp = ordensProducao.find(op => op.id === opId);
      const produto = produtos.find(p => p.id === currentAppointmentOp.produtoId);
      
      document.getElementById('appointmentDetails').innerHTML = `

            <div class="order-info">
          <p><strong>Ordem:</strong> ${currentAppointmentOp.numero}</p>
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade Total:</strong> ${currentAppointmentOp.quantidade} ${produto.unidade}</p>
          <p><strong>Quantidade Já Produzida:</strong> ${currentAppointmentOp.quantidadeProduzida || 0} ${produto.unidade}</p>
        </div>`;
      
      document.getElementById('producedQuantity').value = '';
      document.getElementById('scrapQuantity').value = '0';
      document.getElementById('appointmentObservations').value = '';
      document.getElementById('appointmentModal').style.display = 'block';
    };

    window.submitAppointment = async function( event) {
      event.preventDefault();
      if (!currentAppointmentOp) return;

      const producedQuantity = parseFloat(document.getElementById('producedQuantity').value);
      const scrapQuantity = parseFloat(document.getElementById('scrapQuantity').value) || 0;
      const observations = document.getElementById('appointmentObservations').value;

      if (!producedQuantity) {
        alert('Por favor, informe a quantidade produzida.');
        return;
      }

      const totalProduzido = (currentAppointmentOp.quantidadeProduzida || 0) + producedQuantity;
      if (totalProduzido > currentAppointmentOp.quantidade) {
        alert('A quantidade total produzida não pode exceder a quantidade da ordem.');
        return;
      }

      try {
        // Baixar materiais do estoque
        if (currentAppointmentOp.materiaisNecessarios) {
    for (const material of currentAppointmentOp.materiaisNecessarios) {
      const consumoReal = (material.quantidade / currentAppointmentOp.quantidade) * producedQuantity;
      const reservaReal = (material.quantidadeReservada || 0) / currentAppointmentOp.quantidade * producedQuantity;

      // Consumir do estoque
      await updateInventory(material.produtoId, consumoReal, 'saida');
      // Liberar reserva (se houver excesso)
      if (reservaReal > 0) {
        await updateInventoryReservation(material.produtoId, -reservaReal);
      }
    }
  }

        // Atualizar estoque do produto produzido
        await updateInventory(currentAppointmentOp.produtoId, producedQuantity, 'entrada');

        // Determinar novo status
        let novoStatus = 'Em Produção';
        if (totalProduzido >= currentAppointmentOp.quantidade) {
          novoStatus = 'Concluída';
        }

        // Atualizar ordem de produção
        const opRef = doc(db, "ordensProducao", currentAppointmentOp.id);
        await updateDoc(opRef, {
          status: novoStatus,
          quantidadeProduzida: totalProduzido,
          quantidadeRefugo: (currentAppointmentOp.quantidadeRefugo || 0) + scrapQuantity,
          ultimoApontamento: {
            quantidade: producedQuantity,
            refugo: scrapQuantity,
            observacoes: observations,
            data: Timestamp.now()
          }
        });

        // Se a ordem foi concluída e veio de um pedido, atualizar o pedido
        if (novoStatus === 'Concluída' && currentAppointmentOp.pedidoId) {
          await updateDoc(doc(db, "pedidosVenda", currentAppointmentOp.pedidoId), {
            status: 'Concluído'
          });
        }

        closeModal('appointmentModal');
        await loadActiveOrders();
        alert('Apontamento registrado com sucesso!');
      } catch (error) {
        console.error("Erro ao registrar apontamento:", error);
        alert("Erro ao registrar apontamento.");
      }
    };

window.cancelOrder = async function(opId) {
    if (!confirm('Tem certeza que deseja cancelar esta ordem de produção?')) {
        return;
    }

    try {
        const ordem = ordensProducao.find(op => op.id === opId);

        // Liberar as reservas dos materiais associados, se houver
        if (ordem.materiaisNecessarios) {
            for (const material of ordem.materiaisNecessarios) {
                if (material.quantidadeReservada > 0) {
                    await updateInventoryReservation(material.produtoId, -material.quantidadeReservada);
                }
            }
        }

        // Atualizar o status da ordem para "Cancelada"
        await updateDoc(doc(db, "ordensProducao", opId), {
            status: 'Cancelada',
            dataCancelamento: Timestamp.now()
        });

        // Se a ordem veio de um pedido, atualizar o pedido para "Pendente"
        if (ordem.pedidoId) {
            await updateDoc(doc(db, "pedidosVenda", ordem.pedidoId), {
                status: 'Pendente'
            });
        }

        await loadActiveOrders();
        alert('Ordem cancelada com sucesso!');
    } catch (error) {
        console.error("Erro ao cancelar ordem:", error);
        alert("Erro ao cancelar ordem.");
    }
};
    

 window.openNewOrderModal = function() {
  document.getElementById('newOrderForm').reset();
  document.getElementById('newOrderModal').style.display = 'block';
};

window.openOrderFromSalesModal = function() {
  document.getElementById('salesOrderSelect').value = '';
  document.getElementById('salesOrderDetails').innerHTML = '';
  document.getElementById('salesOrderModal').style.display = 'block';
};

window.onload = async function() {
  await loadInitialData();
  setupProductSearch();
  await loadActiveOrders();
  setupSalesOrderSelect();
  
  counterRef = doc(db, "contadores", "ordens");
  const counterDoc = await getDoc(counterRef);
  if (!counterDoc.exists()) {
    await setDoc(counterRef, { valor: 0 });
  }

  // Adicionar event listeners
  document.getElementById('newOrderButton').addEventListener('click', openNewOrderModal);
  document.getElementById('orderFromSalesButton').addEventListener('click', openOrderFromSalesModal);
};

    window.openOrderFromSalesModal = function() {
      document.getElementById('salesOrderSelect').value = '';
      document.getElementById('salesOrderDetails').innerHTML = '';
      document.getElementById('salesOrderModal').style.display = 'block';
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
      if (modalId === 'appointmentModal') {
        currentAppointmentOp = null;
      }
    };

    window.switchTab = function(tab) {
      const tabs = ['active', 'completed'];
      tabs.forEach(t => {
        document.getElementById(`${t}Orders`).classList.toggle('active', t === tab);
        document.querySelector(`button[onclick="switchTab('${t}')"]`).classList.toggle('active', t === tab);
      });
    };

    window.filterOrders = function() {
      // Implementar filtro de ordens baseado nos critérios selecionados
      loadActiveOrders();
    };
  </script>
</body>
</html>