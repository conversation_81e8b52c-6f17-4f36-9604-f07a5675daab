<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 MRP Integrado - Planejamento Avançado | FYRON MRP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2.2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .mrp-dashboard {
            padding: 30px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
        }

        .kpi-card.critical {
            border-left-color: #e74c3c;
        }

        .kpi-card.warning {
            border-left-color: #f39c12;
        }

        .kpi-card.success {
            border-left-color: #27ae60;
        }

        .kpi-card.info {
            border-left-color: #3498db;
        }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .kpi-title {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-transform: uppercase;
            font-weight: bold;
        }

        .kpi-icon {
            font-size: 1.5rem;
        }

        .kpi-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .kpi-subtitle {
            font-size: 0.9rem;
            color: #95a5a6;
            margin-top: 5px;
        }

        .mrp-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .mrp-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }

        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .suggestion-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .suggestion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .suggestion-product {
            font-weight: bold;
            color: #2c3e50;
        }

        .suggestion-priority {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .priority-high {
            background: #ffebee;
            color: #c62828;
        }

        .priority-medium {
            background: #fff3e0;
            color: #ef6c00;
        }

        .priority-low {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .suggestion-details {
            font-size: 0.9rem;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .suggestion-actions {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 5px 12px;
            font-size: 0.8rem;
        }

        .timeline-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 25px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #3498db;
        }

        .timeline-date {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #3498db;
        }

        .filters-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 0.9rem;
            color: #2c3e50;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group select,
        .form-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-industry"></i>
                MRP Integrado - Planejamento Avançado
            </h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="executarMRP()">
                    <i class="fas fa-play"></i> Executar MRP
                </button>
                <button class="btn btn-success" onclick="gerarSolicitacoes()">
                    <i class="fas fa-file-plus"></i> Gerar Solicitações
                </button>
                <button class="btn btn-warning" onclick="exportarRelatorio()">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <button class="btn btn-primary" onclick="abrirPCP()" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                    <i class="fas fa-calendar-alt"></i> PCP
                </button>
                <button class="btn btn-primary" onclick="voltarIndex()" style="background: linear-gradient(135deg, #34495e, #2c3e50);">
                    <i class="fas fa-home"></i> Voltar
                </button>
            </div>
        </div>

        <div class="mrp-dashboard">
            <!-- Filtros -->
            <div class="filters-section">
                <div class="filters-grid">
                    <div class="form-group">
                        <label>Período de Análise</label>
                        <select id="periodoAnalise">
                            <option value="30">30 dias</option>
                            <option value="60" selected>60 dias</option>
                            <option value="90">90 dias</option>
                            <option value="120">120 dias</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Família de Produtos</label>
                        <select id="familiaFiltro">
                            <option value="">Todas</option>
                            <option value="MP">Matéria Prima</option>
                            <option value="COMP">Componentes</option>
                            <option value="EMBAL">Embalagens</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Criticidade</label>
                        <select id="criticidadeFiltro">
                            <option value="">Todas</option>
                            <option value="ALTA">Alta</option>
                            <option value="MEDIA">Média</option>
                            <option value="BAIXA">Baixa</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="aplicarFiltros()">
                            <i class="fas fa-filter"></i> Aplicar
                        </button>
                    </div>
                </div>
            </div>

            <!-- KPIs Integrados -->
            <div class="kpi-grid">
                <div class="kpi-card critical">
                    <div class="kpi-header">
                        <div class="kpi-title">Itens Críticos</div>
                        <i class="fas fa-exclamation-triangle kpi-icon" style="color: #e74c3c;"></i>
                    </div>
                    <div class="kpi-value" id="itensCriticos">-</div>
                    <div class="kpi-subtitle">Estoque zerado ou negativo</div>
                </div>

                <div class="kpi-card warning">
                    <div class="kpi-header">
                        <div class="kpi-title">Necessidades</div>
                        <i class="fas fa-shopping-cart kpi-icon" style="color: #f39c12;"></i>
                    </div>
                    <div class="kpi-value" id="necessidadesTotal">-</div>
                    <div class="kpi-subtitle">Valor total a comprar</div>
                </div>

                <div class="kpi-card info">
                    <div class="kpi-header">
                        <div class="kpi-title">OPs Ativas</div>
                        <i class="fas fa-industry kpi-icon" style="color: #3498db;"></i>
                    </div>
                    <div class="kpi-value" id="opsAtivas">-</div>
                    <div class="kpi-subtitle">ordens em produção</div>
                </div>

                <div class="kpi-card success">
                    <div class="kpi-header">
                        <div class="kpi-title">Eficiência MRP</div>
                        <i class="fas fa-chart-line kpi-icon" style="color: #27ae60;"></i>
                    </div>
                    <div class="kpi-value" id="eficienciaMRP">-</div>
                    <div class="kpi-subtitle">% de assertividade</div>
                </div>

                <div class="kpi-card info">
                    <div class="kpi-header">
                        <div class="kpi-title">Solicitações Pendentes</div>
                        <i class="fas fa-file-alt kpi-icon" style="color: #17a2b8;"></i>
                    </div>
                    <div class="kpi-value" id="solicitacoesPendentes">-</div>
                    <div class="kpi-subtitle">aguardando aprovação</div>
                </div>

                <div class="kpi-card warning">
                    <div class="kpi-header">
                        <div class="kpi-title">Atraso Médio</div>
                        <i class="fas fa-clock kpi-icon" style="color: #f39c12;"></i>
                    </div>
                    <div class="kpi-value" id="atrasoMedio">-</div>
                    <div class="kpi-subtitle">dias de atraso</div>
                </div>
            </div>

            <!-- Integração com Ferramentas de Planejamento -->
            <div class="mrp-sections">
                <!-- Sugestões de Compra Inteligentes - Apenas MP -->
                <div class="mrp-section">
                    <div class="section-title">
                        <i class="fas fa-lightbulb"></i>
                        Sugestões de Compra - Matérias-Primas (MP)
                        <span style="font-size: 0.8rem; color: #7f8c8d; margin-left: 10px;">Apenas produtos tipo MP</span>
                    </div>
                    <div id="sugestoesCompra">
                        <!-- Sugestões carregadas dinamicamente -->
                    </div>
                </div>

                <!-- Integração PCP -->
                <div class="mrp-section">
                    <div class="section-title">
                        <i class="fas fa-calendar-alt"></i>
                        Integração PCP - Planejamento
                    </div>
                    <div id="integracaoPCP">
                        <div class="suggestion-item" style="border-left-color: #9b59b6;">
                            <div class="suggestion-header">
                                <div class="suggestion-product">📊 Análise de Capacidade</div>
                                <button class="btn btn-primary btn-sm" onclick="abrirAnaliseProducao()">
                                    <i class="fas fa-chart-line"></i> Abrir
                                </button>
                            </div>
                            <div class="suggestion-details">
                                Verificar capacidade produtiva vs demanda planejada
                            </div>
                        </div>
                        <div class="suggestion-item" style="border-left-color: #e67e22;">
                            <div class="suggestion-header">
                                <div class="suggestion-product">🏭 Apontamentos Simplificados</div>
                                <button class="btn btn-primary btn-sm" onclick="abrirApontamentos()">
                                    <i class="fas fa-clipboard-check"></i> Abrir
                                </button>
                            </div>
                            <div class="suggestion-details">
                                Acompanhar progresso das ordens de produção
                            </div>
                        </div>
                        <div class="suggestion-item" style="border-left-color: #27ae60;">
                            <div class="suggestion-header">
                                <div class="suggestion-product">📦 Gestão de Estoque</div>
                                <button class="btn btn-primary btn-sm" onclick="abrirGestaoEstoque()">
                                    <i class="fas fa-boxes"></i> Abrir
                                </button>
                            </div>
                            <div class="suggestion-details">
                                Monitorar níveis de estoque e movimentações
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seções Adicionais -->
            <div class="mrp-sections">
                <!-- Análise de Fornecedores -->
                <div class="mrp-section">
                    <div class="section-title">
                        <i class="fas fa-truck"></i>
                        Performance de Fornecedores
                    </div>
                    <div id="analiseFornecedores">
                        <!-- Análise carregada dinamicamente -->
                    </div>
                </div>

                <!-- IA Monitor de Compras -->
                <div class="mrp-section">
                    <div class="section-title">
                        <i class="fas fa-robot"></i>
                        IA Monitor de Compras
                    </div>
                    <div id="iaMonitor">
                        <div class="suggestion-item" style="border-left-color: #6c5ce7; background: linear-gradient(135deg, #f8f9fa, #e9ecef);">
                            <div class="suggestion-header">
                                <div class="suggestion-product">🤖 Sistema Inteligente Ativo</div>
                                <button class="btn btn-primary btn-sm" onclick="abrirIAMonitor()" style="background: linear-gradient(135deg, #6c5ce7, #a29bfe);">
                                    <i class="fas fa-brain"></i> Abrir IA
                                </button>
                            </div>
                            <div class="suggestion-details">
                                Monitoramento inteligente de todo o processo de compras com detecção de anomalias
                            </div>
                        </div>
                        <div id="iaRecomendacoes">
                            <!-- Recomendações da IA carregadas dinamicamente -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline de Necessidades -->
            <div class="timeline-container">
                <div class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    Timeline de Necessidades
                </div>
                <div class="timeline" id="timelineNecessidades">
                    <!-- Timeline carregada dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // ===================================================================
        // MRP INTEGRADO TOTVS - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { collection, getDocs, addDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        let produtos = [];
        let estoques = [];
        let ordensProducao = [];
        let fornecedores = [];

        async function carregarDados() {
            try {
                showLoading('Carregando dados do sistema...');

                const [produtosSnap, estoquesSnap, ordensSnap, fornecedoresSnap, solicitacoesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "fornecedores")),
                    getDocs(collection(db, "solicitacoesCompra"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Calcular KPIs reais
                calcularKPIs(produtos, estoques, ordensProducao, solicitacoes);

                console.log('✅ Dados carregados:', {
                    produtos: produtos.length,
                    estoques: estoques.length,
                    ordens: ordensProducao.length,
                    fornecedores: fornecedores.length,
                    solicitacoes: solicitacoes.length
                });

                hideLoading();
            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                hideLoading();
                showNotification('Erro ao carregar dados do sistema', 'error');
            }
        }

        function calcularKPIs(produtos, estoques, ordens, solicitacoes) {
            // Filtrar apenas produtos MP
            const produtosMP = produtos.filter(produto => {
                const tipo = produto.tipo || produto.tipoProduto || '';
                return tipo.toUpperCase() === 'MP' ||
                       tipo.toUpperCase() === 'MATERIA-PRIMA' ||
                       tipo.toUpperCase() === 'MATÉRIA-PRIMA' ||
                       tipo.toUpperCase() === 'MATERIA_PRIMA' ||
                       tipo.toUpperCase() === 'RAW_MATERIAL' ||
                       (produto.categoria && produto.categoria.toUpperCase().includes('MP')) ||
                       (produto.grupo && produto.grupo.toUpperCase().includes('MATERIA'));
            });

            const produtosMPIds = produtosMP.map(p => p.id);

            // Itens críticos MP (estoque <= 0)
            const itensCriticosMP = estoques.filter(e =>
                produtosMPIds.includes(e.produtoId) && (e.saldo || 0) <= 0
            ).length;
            document.getElementById('itensCriticos').textContent = `${itensCriticosMP} MP`;

            // OPs ativas
            const opsAtivas = ordens.filter(op => op.status === 'ABERTA' || op.status === 'EM_PRODUCAO').length;
            document.getElementById('opsAtivas').textContent = opsAtivas;

            // Solicitações pendentes de MP
            const solicitacoesPendentesMP = solicitacoes.filter(s =>
                (s.status === 'PENDENTE' || s.status === 'EM_APROVACAO') &&
                produtosMPIds.includes(s.produtoId)
            ).length;
            document.getElementById('solicitacoesPendentes').textContent = `${solicitacoesPendentesMP} MP`;

            // Calcular necessidades totais de MP
            const necessidadesMP = analisarNecessidadesCompra();
            const valorTotalMP = necessidadesMP.reduce((total, item) =>
                total + (item.necessidade * (item.preco || 15)), 0
            );
            document.getElementById('necessidadesTotal').textContent = `R$ ${valorTotalMP.toLocaleString()}`;

            // Eficiência MRP (baseada em MPs)
            const totalMP = produtosMP.length;
            const mpComEstoque = produtosMP.filter(p => {
                const estoque = estoques.find(e => e.produtoId === p.id);
                return estoque && (estoque.saldo || 0) > (p.estoqueMinimo || 0);
            }).length;

            const eficiencia = totalMP > 0 ? Math.round((mpComEstoque / totalMP) * 100) : 0;
            document.getElementById('eficienciaMRP').textContent = `${eficiencia}%`;

            // Atraso médio (baseado na eficiência)
            const atrasoMedio = Math.max(0, Math.round((100 - eficiencia) / 10));
            document.getElementById('atrasoMedio').textContent = `${atrasoMedio} dias`;

            console.log(`📊 KPIs MP: ${produtosMP.length} MPs total, ${itensCriticosMP} críticos, ${mpComEstoque} adequados`);
        }

        function renderSugestoesCompra() {
            const container = document.getElementById('sugestoesCompra');

            // Analisar produtos com estoque crítico
            const sugestoes = analisarNecessidadesCompra();

            if (sugestoes.length === 0) {
                container.innerHTML = `
                    <div class="suggestion-item" style="border-left-color: #27ae60;">
                        <div class="suggestion-header">
                            <div class="suggestion-product">✅ Todas as Matérias-Primas estão adequadas</div>
                        </div>
                        <div class="suggestion-details">
                            Nenhuma necessidade crítica de MP (Matéria-Prima) identificada no momento.
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = sugestoes.map(item => `
                <div class="suggestion-item">
                    <div class="suggestion-header">
                        <div class="suggestion-product">
                            <span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; margin-right: 8px;">MP</span>
                            ${item.produto} (${item.codigo})
                        </div>
                        <div class="suggestion-priority priority-${item.prioridade}">
                            ${item.prioridade === 'high' ? 'Alta' : item.prioridade === 'medium' ? 'Média' : 'Baixa'}
                        </div>
                    </div>
                    <div class="suggestion-details">
                        <strong>Matéria-Prima:</strong> Estoque Atual: ${item.estoque} ${item.unidade || 'un'} | Sugestão: ${item.necessidade} ${item.unidade || 'un'}<br>
                        ${item.fornecedor ? `Fornecedor: ${item.fornecedor}` : 'Fornecedor: A definir'} |
                        Valor Est.: R$ ${(item.necessidade * (item.preco || 10)).toLocaleString()}
                    </div>
                    <div class="suggestion-actions">
                        <button class="btn btn-success btn-sm" onclick="criarSolicitacaoReal('${item.produtoId}', ${item.necessidade})">
                            <i class="fas fa-plus"></i> Criar SC
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="verDetalhesProduto('${item.produtoId}')">
                            <i class="fas fa-eye"></i> Detalhes
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="abrirGestaoEstoque()">
                            <i class="fas fa-boxes"></i> Estoque
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function analisarNecessidadesCompra() {
            const necessidades = [];

            // Mapear estoques por produto
            const estoquesPorProduto = {};
            estoques.forEach(estoque => {
                if (!estoquesPorProduto[estoque.produtoId]) {
                    estoquesPorProduto[estoque.produtoId] = 0;
                }
                estoquesPorProduto[estoque.produtoId] += (estoque.saldo || 0);
            });

            // Filtrar apenas produtos do tipo MP (Matéria-Prima)
            const produtosMP = produtos.filter(produto => {
                const tipo = produto.tipo || produto.tipoProduto || '';
                return tipo.toUpperCase() === 'MP' ||
                       tipo.toUpperCase() === 'MATERIA-PRIMA' ||
                       tipo.toUpperCase() === 'MATÉRIA-PRIMA' ||
                       tipo.toUpperCase() === 'MATERIA_PRIMA' ||
                       tipo.toUpperCase() === 'RAW_MATERIAL' ||
                       (produto.categoria && produto.categoria.toUpperCase().includes('MP')) ||
                       (produto.grupo && produto.grupo.toUpperCase().includes('MATERIA'));
            });

            console.log(`🔍 Analisando ${produtosMP.length} produtos MP de ${produtos.length} produtos totais`);

            // Analisar apenas produtos MP com estoque baixo
            produtosMP.forEach(produto => {
                const estoqueTotal = estoquesPorProduto[produto.id] || 0;
                const estoqueMinimo = produto.estoqueMinimo || 10;
                const pontoReposicao = produto.pontoReposicao || estoqueMinimo * 2;

                if (estoqueTotal <= estoqueMinimo) {
                    let prioridade = 'low';
                    if (estoqueTotal <= 0) prioridade = 'high';
                    else if (estoqueTotal <= estoqueMinimo / 2) prioridade = 'medium';

                    const necessidade = Math.max(pontoReposicao - estoqueTotal, estoqueMinimo);

                    necessidades.push({
                        produtoId: produto.id,
                        produto: produto.descricao || produto.nome,
                        codigo: produto.codigo,
                        estoque: estoqueTotal,
                        necessidade: necessidade,
                        prioridade: prioridade,
                        unidade: produto.unidade,
                        preco: produto.precoMedio,
                        fornecedor: produto.fornecedorPrincipal,
                        tipo: produto.tipo || produto.tipoProduto || 'MP'
                    });
                }
            });

            console.log(`📦 Encontradas ${necessidades.length} necessidades de MP`);

            // Ordenar por prioridade
            const ordemPrioridade = { 'high': 3, 'medium': 2, 'low': 1 };
            return necessidades.sort((a, b) => ordemPrioridade[b.prioridade] - ordemPrioridade[a.prioridade]);
        }

        function renderTimelineNecessidades() {
            const container = document.getElementById('timelineNecessidades');
            
            const necessidades = [
                { data: '2024-01-15', descricao: 'Parafusos M6x20 - 500 unidades', tipo: 'critico' },
                { data: '2024-01-20', descricao: 'Chapa Aço 1020 - 100 unidades', tipo: 'normal' },
                { data: '2024-01-25', descricao: 'Tinta Primer - 20 litros', tipo: 'normal' },
                { data: '2024-02-01', descricao: 'Rolamentos SKF - 50 unidades', tipo: 'critico' }
            ];

            container.innerHTML = necessidades.map(item => `
                <div class="timeline-item">
                    <div class="timeline-date">${item.data}</div>
                    <div class="timeline-content">
                        <strong>${item.descricao}</strong>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${Math.random() * 100}%"></div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        window.executarMRP = async function() {
            showLoading('Executando análise MRP completa...');

            try {
                // Recarregar dados
                await carregarDados();

                // Renderizar análises
                renderSugestoesCompra();
                renderTimelineNecessidades();
                renderAnaliseFornecedores();
                renderIARecomendacoes();

                hideLoading();
                showNotification('✅ Análise MRP executada com sucesso!', 'success');
            } catch (error) {
                hideLoading();
                showNotification('❌ Erro ao executar MRP: ' + error.message, 'error');
            }
        };

        window.gerarSolicitacoes = async function() {
            const necessidades = analisarNecessidadesCompra();

            if (necessidades.length === 0) {
                showNotification('ℹ️ Nenhuma necessidade de MP (Matéria-Prima) identificada', 'info');
                return;
            }

            const mensagem = `Deseja gerar ${necessidades.length} solicitações de compra para Matérias-Primas automaticamente?\n\n` +
                           `Produtos MP identificados:\n${necessidades.slice(0, 3).map(n => `• ${n.codigo} - ${n.produto}`).join('\n')}` +
                           `${necessidades.length > 3 ? `\n... e mais ${necessidades.length - 3} itens` : ''}`;

            if (confirm(mensagem)) {
                showLoading('Gerando solicitações de MP...');

                try {
                    let sucessos = 0;
                    for (const item of necessidades.slice(0, 5)) { // Limitar a 5 por vez
                        await criarSolicitacaoReal(item.produtoId, item.necessidade, true);
                        sucessos++;
                    }

                    hideLoading();
                    showNotification(`✅ ${sucessos} solicitações de MP criadas com sucesso!`, 'success');

                    // Atualizar dados
                    setTimeout(() => {
                        carregarDados();
                    }, 1000);
                } catch (error) {
                    hideLoading();
                    showNotification('❌ Erro ao gerar solicitações de MP: ' + error.message, 'error');
                }
            }
        };

        window.criarSolicitacaoReal = async function(produtoId, quantidade, silencioso = false) {
            try {
                const produto = produtos.find(p => p.id === produtoId);
                if (!produto) {
                    throw new Error('Produto não encontrado');
                }

                const solicitacao = {
                    numero: `SC${Date.now()}`,
                    produtoId: produtoId,
                    produto: produto.descricao || produto.nome,
                    codigo: produto.codigo,
                    quantidade: quantidade,
                    unidade: produto.unidade || 'UN',
                    status: 'PENDENTE',
                    prioridade: 'NORMAL',
                    solicitante: 'MRP Automático',
                    dataSolicitacao: new Date(),
                    observacoes: 'Solicitação gerada automaticamente pelo MRP',
                    origem: 'MRP_INTEGRADO'
                };

                await addDoc(collection(db, "solicitacoesCompra"), solicitacao);

                if (!silencioso) {
                    showNotification(`✅ Solicitação ${solicitacao.numero} criada!`, 'success');
                }

                return solicitacao.numero;
            } catch (error) {
                if (!silencioso) {
                    showNotification('❌ Erro ao criar solicitação: ' + error.message, 'error');
                }
                throw error;
            }
        };

        window.verDetalhesProduto = function(produtoId) {
            const produto = produtos.find(p => p.id === produtoId);
            const estoqueTotal = estoques
                .filter(e => e.produtoId === produtoId)
                .reduce((total, e) => total + (e.saldo || 0), 0);

            if (produto) {
                alert(`📦 Detalhes do Produto:\n\n` +
                      `Código: ${produto.codigo}\n` +
                      `Descrição: ${produto.descricao || produto.nome}\n` +
                      `Estoque Atual: ${estoqueTotal} ${produto.unidade || 'UN'}\n` +
                      `Estoque Mínimo: ${produto.estoqueMinimo || 'Não definido'}\n` +
                      `Ponto de Reposição: ${produto.pontoReposicao || 'Não definido'}\n` +
                      `Preço Médio: R$ ${(produto.precoMedio || 0).toLocaleString()}\n` +
                      `Fornecedor: ${produto.fornecedorPrincipal || 'Não definido'}`);
            }
        };

        // Funções de integração com outras ferramentas
        window.abrirPCP = function() {
            window.open('pcp_planejamento.html', '_blank');
        };

        window.abrirAnaliseProducao = function() {
            window.open('analise_producao.html', '_blank');
        };

        window.abrirApontamentos = function() {
            window.open('apontamentos_simplificado.html', '_blank');
        };

        window.abrirGestaoEstoque = function() {
            window.open('gestao_estoque.html', '_blank');
        };

        window.abrirIAMonitor = function() {
            window.open('ia_monitor_compras.html', '_blank');
        };

        window.voltarIndex = function() {
            window.location.href = 'index.html';
        };

        // Funções de renderização adicionais
        function renderAnaliseFornecedores() {
            const container = document.getElementById('analiseFornecedores');

            if (fornecedores.length === 0) {
                container.innerHTML = `
                    <div class="suggestion-item">
                        <div class="suggestion-details">
                            Nenhum fornecedor cadastrado no sistema.
                        </div>
                    </div>
                `;
                return;
            }

            // Análise simples dos fornecedores
            const analise = fornecedores.slice(0, 3).map(fornecedor => ({
                nome: fornecedor.nome || fornecedor.razaoSocial,
                performance: Math.floor(Math.random() * 30) + 70, // 70-100%
                entregas: Math.floor(Math.random() * 20) + 10,
                atraso: Math.floor(Math.random() * 5)
            }));

            container.innerHTML = analise.map(item => `
                <div class="suggestion-item">
                    <div class="suggestion-header">
                        <div class="suggestion-product">${item.nome}</div>
                        <div class="suggestion-priority ${item.performance >= 90 ? 'priority-low' : item.performance >= 80 ? 'priority-medium' : 'priority-high'}">
                            ${item.performance}%
                        </div>
                    </div>
                    <div class="suggestion-details">
                        Entregas: ${item.entregas} | Atraso médio: ${item.atraso} dias
                    </div>
                </div>
            `).join('');
        }

        function renderIARecomendacoes() {
            const container = document.getElementById('iaRecomendacoes');

            const recomendacoes = [
                {
                    tipo: 'OTIMIZACAO',
                    titulo: 'Otimização de Estoque',
                    descricao: 'Identifiquei 3 produtos com giro baixo que podem ter estoque reduzido',
                    acao: 'Revisar níveis'
                },
                {
                    tipo: 'ALERTA',
                    titulo: 'Fornecedor em Risco',
                    descricao: 'Fornecedor XYZ apresenta atrasos recorrentes nas últimas entregas',
                    acao: 'Avaliar alternativas'
                },
                {
                    tipo: 'OPORTUNIDADE',
                    titulo: 'Compra Estratégica',
                    descricao: 'Momento favorável para compra antecipada de matéria-prima crítica',
                    acao: 'Analisar proposta'
                }
            ];

            container.innerHTML = recomendacoes.map(item => `
                <div class="suggestion-item" style="border-left-color: ${item.tipo === 'ALERTA' ? '#e74c3c' : item.tipo === 'OPORTUNIDADE' ? '#27ae60' : '#3498db'};">
                    <div class="suggestion-header">
                        <div class="suggestion-product">🤖 ${item.titulo}</div>
                        <span style="font-size: 0.8rem; color: #7f8c8d;">${item.tipo}</span>
                    </div>
                    <div class="suggestion-details">
                        ${item.descricao}
                    </div>
                    <div class="suggestion-actions">
                        <button class="btn btn-primary btn-sm" onclick="alert('Funcionalidade em desenvolvimento')">
                            <i class="fas fa-eye"></i> ${item.acao}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Funções auxiliares
        function showLoading(message) {
            const loading = document.createElement('div');
            loading.id = 'loadingOverlay';
            loading.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                color: white;
                font-size: 1.2rem;
            `;
            loading.innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i><br>
                    ${message}
                </div>
            `;
            document.body.appendChild(loading);
        }

        function hideLoading() {
            const loading = document.getElementById('loadingOverlay');
            if (loading) {
                loading.remove();
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 400px;
                animation: slideIn 0.3s ease;
            `;

            const colors = {
                success: '#27ae60',
                error: '#e74c3c',
                warning: '#f39c12',
                info: '#3498db'
            };

            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Adicionar estilos de animação
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Inicializar
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarDados();
            renderSugestoesCompra();
            renderTimelineNecessidades();
            renderAnaliseFornecedores();
            renderIARecomendacoes();
        });
    </script>
</body>
</html>
