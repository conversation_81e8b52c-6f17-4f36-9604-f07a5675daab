
const express = require('express');
const router = express.Router();
const db = require('../db-config.js');

router.post('/login', (req, res) => {
  const { email, senha } = req.body;
  
  db.get('SELECT * FROM usuarios WHERE email = ? AND senha = ?', [email, senha], (err, row) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    if (!row) {
      res.status(401).json({ error: 'Usuário ou senha inválidos' });
      return;
    }
    res.json(row);
  });
});

router.get('/user/:id/permissions', (req, res) => {
  db.get('SELECT * FROM usuarios WHERE id = ?', [req.params.id], (err, row) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    if (!row) {
      res.status(404).json({ error: 'Usuário não encontrado' });
      return;
    }
    res.json({ nivel: row.nivel });
  });
});

module.exports = router;
