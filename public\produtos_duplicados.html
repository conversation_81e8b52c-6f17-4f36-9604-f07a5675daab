<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pro<PERSON><PERSON> Duplicados - MRP</title>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --danger-color: #bb0000;
            --warning-color: #e9730c;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        button {
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        button:hover {
            background-color: var(--primary-hover);
        }

        button.danger {
            background-color: var(--danger-color);
        }

        button.danger:hover {
            background-color: #990000;
        }

        button.success {
            background-color: var(--success-color);
        }

        button.success:hover {
            background-color: #096530;
        }

        .loading {
            display: none;
            margin: 20px 0;
            text-align: center;
            color: var(--text-secondary);
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ccc;
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
            vertical-align: middle;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .summary {
            background-color: var(--secondary-color);
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .summary p {
            margin-bottom: 8px;
        }

        .summary .highlight {
            font-weight: bold;
            color: var(--primary-color);
        }

        .summary .warning {
            font-weight: bold;
            color: var(--danger-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--secondary-color);
            font-weight: 600;
            color: var(--text-secondary);
        }

        tr:hover {
            background-color: #f9f9f9;
        }

        .duplicate-group {
            border-left: 3px solid var(--danger-color);
            margin-top: 5px;
        }

        .actions {
            display: flex;
            gap: 5px;
        }

        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-ativo {
            background-color: #e5f2e5;
            color: var(--success-color);
        }

        .status-inativo {
            background-color: #ffeaea;
            color: var(--danger-color);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
        }

        .notification.success {
            background-color: var(--success-color);
        }

        .notification.error {
            background-color: var(--danger-color);
        }

        .empty-message {
            padding: 30px;
            text-align: center;
            color: var(--text-secondary);
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Verificação de Produtos Duplicados</h1>

        <div class="controls">
            <button id="btnVerificar" onclick="verificarDuplicados()">Verificar Duplicados</button>
            <button id="btnExportar" onclick="exportarParaExcel()" class="success">Exportar para Excel</button>
            <button onclick="window.location.href='cadastro_produto.html'" class="danger">Voltar</button>
        </div>

        <div id="loading" class="loading">Analisando produtos, aguarde...</div>

        <div id="summary" class="summary" style="display: none;">
            <p>Total de produtos: <span id="totalProdutos" class="highlight">0</span></p>
            <p>Códigos duplicados encontrados: <span id="totalDuplicados" class="warning">0</span></p>
            <p>Total de registros com duplicidade: <span id="totalRegistrosDup" class="warning">0</span></p>
        </div>

        <div id="duplicatesContainer">
            <table id="duplicatesTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Tipo</th>
                        <th>Data Cadastro</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="duplicatesBody">
                    <!-- Preenchido dinamicamente -->
                </tbody>
            </table>

            <div id="emptyMessage" class="empty-message">
                Não foram encontrados produtos com códigos duplicados.
            </div>
        </div>

        <div id="notification" class="notification"></div>
    </div>

    <!-- Firebase -->
    <script type="module">
        // ===================================================================
        // PRODUTOS DUPLICADOS - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            deleteDoc
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let allProducts = [];
        let duplicateGroups = {};

        // Expor funções para o escopo global
        window.verificarDuplicados = async function() {
            showLoading(true);
            clearResults();

            try {
                // Buscar todos os produtos
                const querySnapshot = await getDocs(collection(db, "produtos"));
                allProducts = querySnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Identificar duplicados por código
                const productsByCode = {};
                allProducts.forEach(product => {
                    if (!product.codigo) return; // Ignorar produtos sem código

                    const code = product.codigo;
                    if (!productsByCode[code]) {
                        productsByCode[code] = [];
                    }
                    productsByCode[code].push(product);
                });

                // Filtrar apenas os que têm duplicados
                duplicateGroups = {};
                let totalDuplicateRecords = 0;

                Object.keys(productsByCode).forEach(code => {
                    if (productsByCode[code].length > 1) {
                        duplicateGroups[code] = productsByCode[code];
                        totalDuplicateRecords += productsByCode[code].length;
                    }
                });

                // Atualizar sumário
                document.getElementById('totalProdutos').textContent = allProducts.length;
                document.getElementById('totalDuplicados').textContent = Object.keys(duplicateGroups).length;
                document.getElementById('totalRegistrosDup').textContent = totalDuplicateRecords;
                document.getElementById('summary').style.display = 'block';

                // Exibir resultados
                if (Object.keys(duplicateGroups).length > 0) {
                    displayDuplicates(duplicateGroups);
                    document.getElementById('duplicatesTable').style.display = 'table';
                    document.getElementById('emptyMessage').style.display = 'none';
                } else {
                    document.getElementById('duplicatesTable').style.display = 'none';
                    document.getElementById('emptyMessage').style.display = 'block';
                }
            } catch (error) {
                console.error("Erro ao verificar duplicados:", error);
                showNotification("Erro ao verificar duplicados: " + error.message, "error");
            } finally {
                showLoading(false);
            }
        };

        window.exportarParaExcel = function() {
            if (Object.keys(duplicateGroups).length === 0) {
                showNotification("Não há dados para exportar", "error");
                return;
            }

            // Preparar dados para exportação
            const rows = [];

            Object.keys(duplicateGroups).forEach(code => {
                duplicateGroups[code].forEach(product => {
                    rows.push([
                        product.codigo || '',
                        product.descricao || '',
                        product.tipo || '',
                        formatDate(product.dataCadastro),
                        product.status || 'ativo',
                        product.id
                    ]);
                });
            });

            // Converter para CSV
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Código,Descrição,Tipo,Data Cadastro,Status,ID\n";

            rows.forEach(row => {
                csvContent += row.map(cell => `"${cell}"`).join(",") + "\n";
            });

            // Download do CSV
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "produtos_duplicados.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification("Exportação concluída com sucesso", "success");
        };

        window.removerProduto = async function(id, codigo) {
            if (!confirm(`Deseja remover o produto com ID ${id} (código ${codigo})?`)) {
                return;
            }

            showLoading(true);

            try {
                await deleteDoc(doc(db, "produtos", id));
                showNotification(`Produto removido com sucesso`, "success");
                verificarDuplicados(); // Atualizar a lista
            } catch (error) {
                console.error("Erro ao remover produto:", error);
                showNotification("Erro ao remover produto: " + error.message, "error");
            } finally {
                showLoading(false);
            }
        };

        window.manterApenasEste = async function(idToKeep, code) {
            if (!confirm(`Deseja manter apenas este produto e remover os demais com o código ${code}?`)) {
                return;
            }

            showLoading(true);

            try {
                const productsToRemove = duplicateGroups[code].filter(p => p.id !== idToKeep);

                // Remover cada produto duplicado exceto o selecionado
                for (const product of productsToRemove) {
                    await deleteDoc(doc(db, "produtos", product.id));
                }

                showNotification(`${productsToRemove.length} produtos duplicados foram removidos`, "success");
                verificarDuplicados(); // Atualizar a lista
            } catch (error) {
                console.error("Erro ao processar produtos:", error);
                showNotification("Erro ao processar produtos: " + error.message, "error");
            } finally {
                showLoading(false);
            }
        };

        // Funções auxiliares
        function displayDuplicates(duplicateGroups) {
            const tbody = document.getElementById('duplicatesBody');
            tbody.innerHTML = '';

            Object.keys(duplicateGroups).sort().forEach(code => {
                const products = duplicateGroups[code];

                products.forEach((product, index) => {
                    const row = document.createElement('tr');
                    if (index === 0) {
                        row.classList.add('duplicate-group');
                    }

                    row.innerHTML = `
                        <td>${product.codigo || ''}</td>
                        <td>${product.descricao || ''}</td>
                        <td>${product.tipo || ''}</td>
                        <td>${formatDate(product.dataCadastro)}</td>
                        <td><span class="status status-${product.status || 'ativo'}">${product.status || 'ativo'}</span></td>
                        <td class="actions">
                            <button onclick="manterApenasEste('${product.id}', '${product.codigo}')" class="success">Manter Este</button>
                            <button onclick="removerProduto('${product.id}', '${product.codigo}')" class="danger">Remover</button>
                        </td>
                    `;

                    tbody.appendChild(row);
                });
            });
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'Data não informada';

            try {
                const date = new Date(timestamp.seconds * 1000);
                return date.toLocaleDateString('pt-BR') + ' ' + date.toLocaleTimeString('pt-BR');
            } catch (e) {
                return 'Data inválida';
            }
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function clearResults() {
            document.getElementById('duplicatesBody').innerHTML = '';
            document.getElementById('duplicatesTable').style.display = 'none';
            document.getElementById('emptyMessage').style.display = 'none';
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = 'notification';
            if (type) notification.classList.add(type);

            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // Verificar duplicados ao carregar a página
        document.addEventListener('DOMContentLoaded', verificarDuplicados);
    </script>
</body>
</html> 