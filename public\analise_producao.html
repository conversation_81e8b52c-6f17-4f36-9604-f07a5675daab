<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON><PERSON> de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #28a745;
      --primary-hover: #20c997;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --header-bg: #28a745;
    }
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: var(--text-color);
    }
    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
      padding: 20px;
    }
    .header {
      background: var(--header-bg);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
    }
    .header h1 {
      font-size: 24px;
      margin: 0;
    }
    .nav-buttons {
      display: flex;
      gap: 10px;
    }
    .nav-btn {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }
    .nav-btn:hover {
      background: linear-gradient(135deg, #0a4d8c, #354a5f);
      transform: translateY(-2px);
    }
    .nav-btn.secondary {
      background: linear-gradient(135deg, #fd7e14, #e55a00);
    }
    .nav-btn.danger {
      background: linear-gradient(135deg, #dc3545, #c82333);
    }
    .analysis-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .analysis-card {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      border: 1px solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;
    }
    .analysis-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .analysis-card h3 {
      color: var(--primary-color);
      margin-bottom: 15px;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .analysis-card p {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.6;
    }
    .analysis-btn {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      width: 100%;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .analysis-btn:hover {
      background: linear-gradient(135deg, var(--primary-hover), #17a2b8);
      transform: translateY(-2px);
    }
    .analysis-btn.danger {
      background: linear-gradient(135deg, var(--danger-color), #c82333);
    }
    .analysis-btn.warning {
      background: linear-gradient(135deg, var(--warning-color), #e0a800);
      color: #212529;
    }
    .results-container {
      background: white;
      border-radius: 12px;
      padding: 25px;
      margin-top: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      border: 1px solid rgba(0,0,0,0.05);
    }
    .results-container h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(5px);
    }
    .modal-content {
      background: white;
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 1200px;
      border-radius: 16px;
      position: relative;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      display: flex;
      flex-direction: column;
    }
    .modal-header {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      padding: 25px 30px;
      border-radius: 16px 16px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .modal-header h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: 30px;
      max-height: calc(85vh - 150px);
    }
    .modal-footer {
      padding: 25px 30px;
      background: #f8f9fa;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      text-align: right;
      border-radius: 0 0 16px 16px;
    }
    .close-button {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .close-button:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: rotate(90deg) scale(1.1);
    }
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 1000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    }
    .notification.show {
      opacity: 1;
      transform: translateX(0);
    }
    .notification.success { background: #28a745; }
    .notification.error { background: #dc3545; }
    .notification.warning { background: #ffc107; color: #212529; }
    .notification.info { background: #17a2b8; }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
    }
    .stat-card {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      border: 1px solid rgba(0,0,0,0.05);
    }
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      color: var(--primary-color);
      margin-bottom: 5px;
    }
    .stat-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .analysis-summary {
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid rgba(0,0,0,0.05);
    }
    .analysis-summary h4 {
      color: var(--primary-color);
      margin-bottom: 15px;
      font-size: 18px;
    }
    .stat-card.success .stat-number { color: #28a745; }
    .stat-card.warning .stat-number { color: #ffc107; }
    .stat-card.danger .stat-number { color: #dc3545; }
    .stat-card.info .stat-number { color: #17a2b8; }
    .gargalos-section {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }
    .gargalos-section h4 {
      color: #856404;
      margin-bottom: 15px;
    }
    .gargalo-item {
      background: white;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 6px;
      border-left: 4px solid #ffc107;
    }
    .gargalo-item strong {
      color: #856404;
    }
    .gargalo-item small {
      color: #6c757d;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📊 Análise de Produção</h1>
      <div class="nav-buttons">
        <a href="apontamentos_simplificado.html" class="nav-btn">
          <i class="fas fa-industry"></i> Apontamentos
        </a>
        <a href="gestao_estoque.html" class="nav-btn secondary">
          <i class="fas fa-boxes"></i> Gestão de Estoque
        </a>
        <button onclick="forcarAtualizacao()" class="nav-btn danger">
          <i class="fas fa-sync-alt"></i> Atualizar
        </button>
      </div>
    </div>

    <!-- Estatísticas Rápidas -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number" id="totalOPsAnalise">0</div>
        <div class="stat-label">Total OPs</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="opsViaveis">0</div>
        <div class="stat-label">Viáveis</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="opsParciais">0</div>
        <div class="stat-label">Parciais</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="opsInviaveis">0</div>
        <div class="stat-label">Impossíveis</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="gargalosCriticos">0</div>
        <div class="stat-label">Gargalos</div>
      </div>
    </div>

    <!-- Cards de Análise -->
    <div class="analysis-grid">
      <div class="analysis-card">
        <h3><i class="fas fa-search-plus"></i> Análise de Viabilidade</h3>
        <p>Analisa quais OPs podem ser produzidas com o estoque atual, considerando reservas e disponibilidade por armazém.</p>
        <button class="analysis-btn" onclick="executarAnaliseViabilidade()">
          <i class="fas fa-play"></i> Executar Análise
        </button>
      </div>

      <div class="analysis-card">
        <h3><i class="fas fa-exclamation-triangle"></i> Diagnóstico de Impossibilidade</h3>
        <p>Confirma cientificamente se é impossível produzir qualquer OP, identificando gargalos críticos e sugestões.</p>
        <button class="analysis-btn danger" onclick="executarDiagnosticoCompleto()">
          <i class="fas fa-search"></i> Diagnóstico Completo
        </button>
      </div>

      <div class="analysis-card">
        <h3><i class="fas fa-lightbulb"></i> Sugestões Inteligentes</h3>
        <p>Gera sugestões automáticas de PCP baseadas na análise de estoque, prioridades e capacidade produtiva.</p>
        <button class="analysis-btn warning" onclick="gerarSugestoesPCP()">
          <i class="fas fa-magic"></i> Gerar Sugestões
        </button>
      </div>

      <div class="analysis-card">
        <h3><i class="fas fa-chart-line"></i> Relatório Executivo</h3>
        <p>Relatório completo com métricas, tendências e indicadores para tomada de decisão estratégica.</p>
        <button class="analysis-btn" onclick="gerarRelatorioExecutivo()">
          <i class="fas fa-file-alt"></i> Gerar Relatório
        </button>
      </div>

      <div class="analysis-card">
        <h3><i class="fas fa-layer-group"></i> Agrupamento Inteligente</h3>
        <p>Agrupa OPs por similaridade de materiais para otimizar setup e reduzir tempo de preparação.</p>
        <button class="analysis-btn" onclick="executarAgrupamentoOPs()">
          <i class="fas fa-object-group"></i> Agrupar OPs
        </button>
      </div>

      <div class="analysis-card">
        <h3><i class="fas fa-clock"></i> Análise de Prioridades</h3>
        <p>Reordena OPs por urgência, considerando datas de entrega, disponibilidade de material e capacidade.</p>
        <button class="analysis-btn" onclick="analisarPrioridades()">
          <i class="fas fa-sort-amount-down"></i> Priorizar
        </button>
      </div>
    </div>

    <!-- Container de Resultados -->
    <div class="results-container" id="resultsContainer" style="display: none;">
      <h3><i class="fas fa-chart-bar"></i> Resultados da Análise</h3>
      <div id="resultsContent">
        <!-- Resultados serão inseridos aqui -->
      </div>
    </div>
  </div>

  <!-- Modal para Análise Detalhada -->
  <div id="modalAnaliseDetalhada" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>📊 Análise Detalhada de Produção</h2>
        <span class="close-button" onclick="fecharModalAnalise()">&times;</span>
      </div>
      <div class="modal-body" id="conteudoAnaliseDetalhada">
        <!-- Conteúdo da análise será inserido aqui -->
      </div>
      <div class="modal-footer">
        <button onclick="exportarAnalise()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; margin-right: 10px;">
          📊 Exportar
        </button>
        <button onclick="fecharModalAnalise()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px;">
          Fechar
        </button>
      </div>
    </div>
  </div>

  <script type="module">
    // Importar configurações do Firebase
    import { getFirestore, collection, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
    import { db, firebaseConfig } from './firebase-config.js';

    // Firebase já inicializado no firebase-config.js

    // Variáveis globais
    let ordensProducao = [];
    let produtos = [];
    let estoques = [];
    let armazens = [];
    let estruturas = [];

    // Função para mostrar notificação
    function mostrarNotificacao(mensagem, tipo = 'info', duracao = 3000) {
      const notification = document.createElement('div');
      notification.className = `notification ${tipo}`;
      notification.textContent = mensagem;
      document.body.appendChild(notification);

      setTimeout(() => notification.classList.add('show'), 100);
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, duracao);
    }

    // Função para forçar atualização
    window.forcarAtualizacao = function() {
      mostrarNotificacao('🔄 Atualizando dados...', 'info', 2000);
      carregarDados();
    };

    // Carregar dados do Firebase
    async function carregarDados() {
      try {
        console.log('🔄 Carregando dados para análise...');
        
        const [opsSnapshot, produtosSnapshot, estoquesSnapshot, armazensSnapshot, estruturasSnapshot] = await Promise.all([
          getDocs(collection(db, 'ordensProducao')),
          getDocs(collection(db, 'produtos')),
          getDocs(collection(db, 'estoques')),
          getDocs(collection(db, 'armazens')),
          getDocs(collection(db, 'estruturas'))
        ]);

        ordensProducao = opsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`✅ Dados carregados para análise: ${ordensProducao.length} OPs`);
        atualizarEstatisticas();
        
      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
        mostrarNotificacao('❌ Erro ao carregar dados', 'error', 5000);
      }
    }

    // Atualizar estatísticas
    function atualizarEstatisticas() {
      const totalOPs = ordensProducao.length;
      const opsPendentes = ordensProducao.filter(op =>
        op.status === 'Pendente' || op.status === 'PENDENTE'
      ).length;

      document.getElementById('totalOPsAnalise').textContent = totalOPs;

      // Estatísticas iniciais (serão atualizadas após análises)
      document.getElementById('opsViaveis').textContent = '?';
      document.getElementById('opsParciais').textContent = '?';
      document.getElementById('opsInviaveis').textContent = '?';
      document.getElementById('gargalosCriticos').textContent = '?';

      console.log(`📊 Estatísticas carregadas: ${totalOPs} OPs total, ${opsPendentes} pendentes`);
    }

    // ===== ALGORITMOS DE ANÁLISE DE VIABILIDADE =====

    /**
     * 🎯 ANÁLISE DE VIABILIDADE PRINCIPAL
     * Analisa todas as OPs pendentes e determina viabilidade baseada no estoque atual
     */
    window.executarAnaliseViabilidade = async function() {
      try {
        mostrarNotificacao('📊 Executando análise de viabilidade...', 'info', 2000);

        // Filtrar apenas OPs pendentes
        const opsPendentes = ordensProducao.filter(op =>
          op.status === 'Pendente' || op.status === 'PENDENTE'
        );

        if (opsPendentes.length === 0) {
          mostrarNotificacao('ℹ️ Nenhuma OP pendente encontrada', 'warning', 3000);
          return;
        }

        console.log(`🔍 Analisando ${opsPendentes.length} OPs pendentes...`);

        // Executar análise
        const resultadoAnalise = await analisarViabilidadeOPs(opsPendentes);

        // Atualizar estatísticas
        atualizarEstatisticasAnalise(resultadoAnalise);

        // Exibir resultados
        exibirResultadosAnalise(resultadoAnalise);

        mostrarNotificacao(`✅ Análise concluída: ${resultadoAnalise.viaveis.length} viáveis, ${resultadoAnalise.parciais.length} parciais, ${resultadoAnalise.inviaveis.length} impossíveis`, 'success', 5000);

      } catch (error) {
        console.error('❌ Erro na análise de viabilidade:', error);
        mostrarNotificacao('❌ Erro na análise de viabilidade', 'error', 5000);
      }
    };

    /**
     * 🔍 DIAGNÓSTICO COMPLETO DE IMPOSSIBILIDADE
     * Identifica cientificamente por que OPs são impossíveis
     */
    window.executarDiagnosticoCompleto = async function() {
      try {
        mostrarNotificacao('🔍 Executando diagnóstico completo...', 'info', 2000);

        const opsInviaveis = ordensProducao.filter(op =>
          (op.status === 'Pendente' || op.status === 'PENDENTE') &&
          op.analiseViabilidade?.status === 'INVIAVEL'
        );

        if (opsInviaveis.length === 0) {
          mostrarNotificacao('ℹ️ Nenhuma OP impossível encontrada. Execute primeiro a análise de viabilidade.', 'warning', 4000);
          return;
        }

        const diagnostico = await diagnosticarImpossibilidade(opsInviaveis);
        exibirDiagnosticoCompleto(diagnostico);

        mostrarNotificacao(`🔍 Diagnóstico concluído: ${diagnostico.gargalosCriticos.length} gargalos críticos identificados`, 'info', 4000);

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
        mostrarNotificacao('❌ Erro no diagnóstico completo', 'error', 5000);
      }
    };

    /**
     * 💡 GERAÇÃO DE SUGESTÕES INTELIGENTES
     * Gera sugestões automáticas de PCP baseadas na análise
     */
    window.gerarSugestoesPCP = async function() {
      try {
        mostrarNotificacao('💡 Gerando sugestões inteligentes...', 'info', 2000);

        const sugestoes = await gerarSugestoesInteligentes();
        exibirSugestoesPCP(sugestoes);

        mostrarNotificacao(`💡 ${sugestoes.length} sugestões geradas`, 'success', 3000);

      } catch (error) {
        console.error('❌ Erro ao gerar sugestões:', error);
        mostrarNotificacao('❌ Erro ao gerar sugestões', 'error', 5000);
      }
    };

    /**
     * 📋 RELATÓRIO EXECUTIVO
     * Gera relatório completo com métricas e indicadores
     */
    window.gerarRelatorioExecutivo = async function() {
      try {
        mostrarNotificacao('📋 Gerando relatório executivo...', 'info', 2000);

        const relatorio = await gerarRelatorioExecutivoCompleto();
        exibirRelatorioExecutivo(relatorio);

        mostrarNotificacao('📋 Relatório executivo gerado', 'success', 3000);

      } catch (error) {
        console.error('❌ Erro ao gerar relatório:', error);
        mostrarNotificacao('❌ Erro ao gerar relatório', 'error', 5000);
      }
    };

    /**
     * 🔗 AGRUPAMENTO INTELIGENTE DE OPs
     * Agrupa OPs por similaridade para otimizar produção
     */
    window.executarAgrupamentoOPs = async function() {
      try {
        mostrarNotificacao('🔗 Executando agrupamento inteligente...', 'info', 2000);

        const grupos = await agruparOPsInteligente();
        exibirAgrupamentoOPs(grupos);

        mostrarNotificacao(`🔗 ${grupos.length} grupos criados`, 'success', 3000);

      } catch (error) {
        console.error('❌ Erro no agrupamento:', error);
        mostrarNotificacao('❌ Erro no agrupamento', 'error', 5000);
      }
    };

    /**
     * ⏰ ANÁLISE DE PRIORIDADES
     * Reordena OPs por urgência e viabilidade
     */
    window.analisarPrioridades = async function() {
      try {
        mostrarNotificacao('⏰ Analisando prioridades...', 'info', 2000);

        const prioridades = await analisarPrioridadesOPs();
        exibirAnaliseprioridades(prioridades);

        mostrarNotificacao('⏰ Análise de prioridades concluída', 'success', 3000);

      } catch (error) {
        console.error('❌ Erro na análise de prioridades:', error);
        mostrarNotificacao('❌ Erro na análise de prioridades', 'error', 5000);
      }
    };

    // ===== FUNÇÕES AUXILIARES DE ANÁLISE =====

    /**
     * 🔍 FUNÇÃO PRINCIPAL DE ANÁLISE DE VIABILIDADE
     * Analisa cada OP e determina se é viável, parcial ou inviável
     */
    async function analisarViabilidadeOPs(ops) {
      const resultado = {
        viaveis: [],
        parciais: [],
        inviaveis: [],
        gargalos: new Map(),
        estatisticas: {}
      };

      console.log('🎯 Iniciando análise detalhada de viabilidade...');

      for (const op of ops) {
        try {
          const analise = await analisarViabilidadeOP(op);

          // Classificar resultado
          switch (analise.status) {
            case 'VIAVEL':
              resultado.viaveis.push(analise);
              break;
            case 'PARCIAL':
              resultado.parciais.push(analise);
              break;
            case 'INVIAVEL':
              resultado.inviaveis.push(analise);
              break;
          }

          // Coletar gargalos
          if (analise.materiaisProblema && analise.materiaisProblema.length > 0) {
            analise.materiaisProblema.forEach(material => {
              const key = material.produtoId;
              if (!resultado.gargalos.has(key)) {
                resultado.gargalos.set(key, {
                  produtoId: material.produtoId,
                  codigo: material.codigo,
                  descricao: material.descricao,
                  deficit: 0,
                  opsAfetadas: 0
                });
              }
              const gargalo = resultado.gargalos.get(key);
              gargalo.deficit += material.deficit || 0;
              gargalo.opsAfetadas++;
            });
          }

        } catch (error) {
          console.error(`Erro ao analisar OP ${op.numero}:`, error);
        }
      }

      // Calcular estatísticas
      resultado.estatisticas = {
        total: ops.length,
        viaveis: resultado.viaveis.length,
        parciais: resultado.parciais.length,
        inviaveis: resultado.inviaveis.length,
        percentualViabilidade: ((resultado.viaveis.length + resultado.parciais.length) / ops.length * 100).toFixed(1),
        gargalosCriticos: Array.from(resultado.gargalos.values()).filter(g => g.opsAfetadas >= 2).length
      };

      console.log('✅ Análise concluída:', resultado.estatisticas);
      return resultado;
    }

    /**
     * 🔍 ANÁLISE DE VIABILIDADE DE UMA OP ESPECÍFICA
     * Verifica se uma OP pode ser produzida com o estoque atual
     */
    async function analisarViabilidadeOP(op) {
      const produto = produtos.find(p => p.id === op.produtoId);

      const analise = {
        op: op,
        produto: produto,
        status: 'VIAVEL', // VIAVEL | PARCIAL | INVIAVEL
        quantidadePossivel: op.quantidade,
        materiaisProblema: [],
        prioridade: op.prioridade || 'NORMAL',
        dataEntrega: op.dataEntrega,
        observacoes: [],
        armazemAnalise: op.armazemProducaoId || 'ALM01'
      };

      if (!produto) {
        analise.status = 'INVIAVEL';
        analise.quantidadePossivel = 0;
        analise.observacoes.push('Produto não encontrado');
        return analise;
      }

      // Se a OP tem materiaisNecessarios, analisar cada material
      if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
        await analisarMateriaisNecessarios(op, analise);
      } else {
        // Se não tem materiaisNecessarios, buscar estrutura e explodir
        const estrutura = estruturas.find(e => e.produtoPaiId === op.produtoId);
        if (estrutura && estrutura.componentes) {
          await analisarEstruturaProduto(op, estrutura, analise);
        } else {
          // Produto sem estrutura - verificar estoque direto
          await analisarEstoqueDireto(op, analise);
        }
      }

      return analise;
    }

    /**
     * 📦 ANÁLISE DE MATERIAIS NECESSÁRIOS
     * Verifica disponibilidade de cada material necessário
     */
    async function analisarMateriaisNecessarios(op, analise) {
      let quantidadeMaximaPossivel = op.quantidade;

      for (const material of op.materiaisNecessarios) {
        if (!material || !material.produtoId || !material.quantidade) {
          console.warn(`Material inválido na OP ${op.numero}:`, material);
          continue;
        }

        const produtoMaterial = produtos.find(p => p.id === material.produtoId);
        const saldoDisponivel = calcularSaldoDisponivel(material.produtoId, analise.armazemAnalise);
        const necessidadeTotal = material.quantidade * op.quantidade;

        if (saldoDisponivel < necessidadeTotal) {
          const quantidadePossivel = Math.floor(saldoDisponivel / material.quantidade);
          quantidadeMaximaPossivel = Math.min(quantidadeMaximaPossivel, quantidadePossivel);

          analise.materiaisProblema.push({
            produtoId: material.produtoId,
            codigo: produtoMaterial?.codigo || material.produtoId,
            descricao: produtoMaterial?.descricao || 'Produto não encontrado',
            necessario: necessidadeTotal,
            disponivel: saldoDisponivel,
            deficit: necessidadeTotal - saldoDisponivel,
            problema: saldoDisponivel === 0 ? 'Sem estoque' : 'Estoque insuficiente'
          });
        }
      }

      // Determinar status final
      if (quantidadeMaximaPossivel === 0) {
        analise.status = 'INVIAVEL';
        analise.quantidadePossivel = 0;
      } else if (quantidadeMaximaPossivel < op.quantidade) {
        analise.status = 'PARCIAL';
        analise.quantidadePossivel = quantidadeMaximaPossivel;
      } else {
        analise.status = 'VIAVEL';
        analise.quantidadePossivel = op.quantidade;
      }
    }

    /**
     * 🏗️ ANÁLISE DE ESTRUTURA DE PRODUTO
     * Explode a estrutura e verifica disponibilidade dos componentes
     */
    async function analisarEstruturaProduto(op, estrutura, analise) {
      let quantidadeMaximaPossivel = op.quantidade;

      for (const componente of estrutura.componentes) {
        const produtoComponente = produtos.find(p => p.id === componente.componentId);
        const necessidadeTotal = componente.quantidade * op.quantidade;

        if (produtoComponente?.tipo === 'SP') {
          // Semi-produto - precisa de análise recursiva da sua estrutura
          const estruturaComponente = estruturas.find(e => e.produtoPaiId === componente.componentId);
          if (estruturaComponente) {
            // Para simplificar, vamos considerar que SP sempre pode ser produzido
            // Em uma implementação mais complexa, faria análise recursiva
            analise.observacoes.push(`SP ${produtoComponente.codigo} requer análise recursiva`);
          }
        } else {
          // Matéria-prima - verificar estoque
          const saldoDisponivel = calcularSaldoDisponivel(componente.componentId, analise.armazemAnalise);

          if (saldoDisponivel < necessidadeTotal) {
            const quantidadePossivel = Math.floor(saldoDisponivel / componente.quantidade);
            quantidadeMaximaPossivel = Math.min(quantidadeMaximaPossivel, quantidadePossivel);

            analise.materiaisProblema.push({
              produtoId: componente.componentId,
              codigo: produtoComponente?.codigo || componente.componentId,
              descricao: produtoComponente?.descricao || 'Produto não encontrado',
              necessario: necessidadeTotal,
              disponivel: saldoDisponivel,
              deficit: necessidadeTotal - saldoDisponivel,
              problema: saldoDisponivel === 0 ? 'Sem estoque' : 'Estoque insuficiente'
            });
          }
        }
      }

      // Determinar status final
      if (quantidadeMaximaPossivel === 0) {
        analise.status = 'INVIAVEL';
        analise.quantidadePossivel = 0;
      } else if (quantidadeMaximaPossivel < op.quantidade) {
        analise.status = 'PARCIAL';
        analise.quantidadePossivel = quantidadeMaximaPossivel;
      } else {
        analise.status = 'VIAVEL';
        analise.quantidadePossivel = op.quantidade;
      }
    }

    /**
     * 📦 ANÁLISE DE ESTOQUE DIRETO
     * Para produtos sem estrutura, verifica estoque direto
     */
    async function analisarEstoqueDireto(op, analise) {
      const saldoDisponivel = calcularSaldoDisponivel(op.produtoId, analise.armazemAnalise);

      if (saldoDisponivel >= op.quantidade) {
        analise.status = 'VIAVEL';
        analise.quantidadePossivel = op.quantidade;
      } else if (saldoDisponivel > 0) {
        analise.status = 'PARCIAL';
        analise.quantidadePossivel = saldoDisponivel;
      } else {
        analise.status = 'INVIAVEL';
        analise.quantidadePossivel = 0;
        analise.materiaisProblema.push({
          produtoId: op.produtoId,
          codigo: analise.produto?.codigo || op.produtoId,
          descricao: analise.produto?.descricao || 'Produto não encontrado',
          necessario: op.quantidade,
          disponivel: 0,
          deficit: op.quantidade,
          problema: 'Sem estoque'
        });
      }
    }

    /**
     * 💰 CALCULAR SALDO DISPONÍVEL
     * Função centralizada para calcular saldo disponível de um produto
     */
    function calcularSaldoDisponivel(produtoId, armazemId = 'ALM01') {
      // Buscar em todos os armazéns se não especificado
      let estoquesRelevantes = estoques.filter(e => e.produtoId === produtoId);

      if (armazemId && armazemId !== 'TODOS') {
        estoquesRelevantes = estoquesRelevantes.filter(e => e.armazemId === armazemId);
      }

      let saldoTotal = 0;
      let saldoReservado = 0;
      let saldoEmpenhado = 0;

      estoquesRelevantes.forEach(estoque => {
        saldoTotal += Number(estoque.saldo) || 0;
        saldoReservado += Number(estoque.saldoReservado) || 0;
        saldoEmpenhado += Number(estoque.saldoEmpenhado) || 0;
      });

      return Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);
    }

    /**
     * 📊 ATUALIZAR ESTATÍSTICAS DA ANÁLISE
     * Atualiza os cards de estatísticas na interface
     */
    function atualizarEstatisticasAnalise(resultado) {
      document.getElementById('totalOPsAnalise').textContent = resultado.estatisticas.total;
      document.getElementById('opsViaveis').textContent = resultado.estatisticas.viaveis;
      document.getElementById('opsParciais').textContent = resultado.estatisticas.parciais;
      document.getElementById('opsInviaveis').textContent = resultado.estatisticas.inviaveis;
      document.getElementById('gargalosCriticos').textContent = resultado.estatisticas.gargalosCriticos;
    }

    /**
     * 📋 EXIBIR RESULTADOS DA ANÁLISE
     * Mostra os resultados na interface
     */
    function exibirResultadosAnalise(resultado) {
      const container = document.getElementById('resultsContainer');
      const content = document.getElementById('resultsContent');

      let html = `
        <div class="analysis-summary">
          <h4>📊 Resumo da Análise</h4>
          <div class="stats-grid">
            <div class="stat-card success">
              <div class="stat-number">${resultado.estatisticas.viaveis}</div>
              <div class="stat-label">OPs Viáveis</div>
            </div>
            <div class="stat-card warning">
              <div class="stat-number">${resultado.estatisticas.parciais}</div>
              <div class="stat-label">OPs Parciais</div>
            </div>
            <div class="stat-card danger">
              <div class="stat-number">${resultado.estatisticas.inviaveis}</div>
              <div class="stat-label">OPs Impossíveis</div>
            </div>
            <div class="stat-card info">
              <div class="stat-number">${resultado.estatisticas.percentualViabilidade}%</div>
              <div class="stat-label">Taxa de Viabilidade</div>
            </div>
          </div>
        </div>
      `;

      // Adicionar detalhes dos gargalos se existirem
      if (resultado.gargalos.size > 0) {
        html += `
          <div class="gargalos-section">
            <h4>🚨 Principais Gargalos</h4>
            <div class="gargalos-list">
        `;

        Array.from(resultado.gargalos.values())
          .sort((a, b) => b.opsAfetadas - a.opsAfetadas)
          .slice(0, 5)
          .forEach(gargalo => {
            html += `
              <div class="gargalo-item">
                <strong>${gargalo.codigo}</strong> - ${gargalo.descricao}
                <br><small>Afeta ${gargalo.opsAfetadas} OP(s) | Déficit: ${gargalo.deficit.toFixed(2)}</small>
              </div>
            `;
          });

        html += `
            </div>
          </div>
        `;
      }

      content.innerHTML = html;
      container.style.display = 'block';
    }

    // Funções do modal
    window.fecharModalAnalise = function() {
      document.getElementById('modalAnaliseDetalhada').style.display = 'none';
    };

    window.exportarAnalise = function() {
      mostrarNotificacao('📊 Exportando análise...', 'success', 2000);
    };

    // ===== FUNÇÕES COMPLEMENTARES =====

    /**
     * 🔍 DIAGNÓSTICO DE IMPOSSIBILIDADE
     * Analisa cientificamente por que OPs são impossíveis
     */
    async function diagnosticarImpossibilidade(opsInviaveis) {
      const diagnostico = {
        opsAnalisadas: opsInviaveis.length,
        gargalosCriticos: [],
        sugestoes: [],
        impactoFinanceiro: 0
      };

      const gargalosMap = new Map();

      // Analisar cada OP impossível
      for (const op of opsInviaveis) {
        if (op.analiseViabilidade?.materiaisProblema) {
          op.analiseViabilidade.materiaisProblema.forEach(material => {
            const key = material.produtoId;
            if (!gargalosMap.has(key)) {
              gargalosMap.set(key, {
                produtoId: material.produtoId,
                codigo: material.codigo,
                descricao: material.descricao,
                deficitTotal: 0,
                opsAfetadas: [],
                valorEstimado: 0
              });
            }

            const gargalo = gargalosMap.get(key);
            gargalo.deficitTotal += material.deficit;
            gargalo.opsAfetadas.push(op.numero);

            // Estimar valor do produto (se disponível)
            const produto = produtos.find(p => p.id === material.produtoId);
            if (produto?.precoUltimaCompra) {
              gargalo.valorEstimado += material.deficit * produto.precoUltimaCompra;
            }
          });
        }
      }

      diagnostico.gargalosCriticos = Array.from(gargalosMap.values())
        .sort((a, b) => b.opsAfetadas.length - a.opsAfetadas.length);

      diagnostico.impactoFinanceiro = diagnostico.gargalosCriticos
        .reduce((total, g) => total + g.valorEstimado, 0);

      // Gerar sugestões
      diagnostico.sugestoes = gerarSugestoesDiagnostico(diagnostico.gargalosCriticos);

      return diagnostico;
    }

    /**
     * 💡 GERAR SUGESTÕES INTELIGENTES
     * Cria sugestões automáticas baseadas na análise
     */
    async function gerarSugestoesInteligentes() {
      const sugestoes = [];

      // Sugestão 1: Priorizar OPs viáveis
      const opsViaveis = ordensProducao.filter(op =>
        op.analiseViabilidade?.status === 'VIAVEL'
      );

      if (opsViaveis.length > 0) {
        sugestoes.push({
          tipo: 'PRIORIZACAO',
          titulo: '🎯 Priorizar OPs Viáveis',
          descricao: `${opsViaveis.length} OPs podem ser produzidas imediatamente`,
          acao: 'Iniciar produção das OPs viáveis primeiro',
          impacto: 'ALTO',
          ops: opsViaveis.map(op => op.numero)
        });
      }

      // Sugestão 2: Transferências necessárias
      const transferencias = await identificarTransferenciasNecessarias();
      if (transferencias.length > 0) {
        sugestoes.push({
          tipo: 'TRANSFERENCIA',
          titulo: '📦 Transferências Recomendadas',
          descricao: `${transferencias.length} transferências podem viabilizar OPs`,
          acao: 'Executar transferências entre armazéns',
          impacto: 'MEDIO',
          transferencias: transferencias
        });
      }

      // Sugestão 3: Solicitações de compra urgentes
      const comprasUrgentes = await identificarComprasUrgentes();
      if (comprasUrgentes.length > 0) {
        sugestoes.push({
          tipo: 'COMPRA',
          titulo: '🛒 Compras Urgentes',
          descricao: `${comprasUrgentes.length} itens precisam ser comprados urgentemente`,
          acao: 'Gerar solicitações de compra prioritárias',
          impacto: 'CRITICO',
          itens: comprasUrgentes
        });
      }

      return sugestoes;
    }

    /**
     * 📋 GERAR RELATÓRIO EXECUTIVO
     * Cria relatório completo com métricas e indicadores
     */
    async function gerarRelatorioExecutivoCompleto() {
      const agora = new Date();

      const relatorio = {
        dataGeracao: agora,
        periodo: 'Situação Atual',
        metricas: {
          totalOPs: ordensProducao.length,
          opsPendentes: ordensProducao.filter(op => op.status === 'Pendente').length,
          opsEmProducao: ordensProducao.filter(op => op.status === 'Em Produção').length,
          opsFinalizadas: ordensProducao.filter(op => op.status === 'Finalizada').length
        },
        indicadores: {
          taxaViabilidade: 0,
          gargalosCriticos: 0,
          valorBloqueado: 0,
          tempoMedioEntrega: 0
        },
        tendencias: [],
        recomendacoes: []
      };

      // Calcular indicadores baseados na análise
      const opsComAnalise = ordensProducao.filter(op => op.analiseViabilidade);
      if (opsComAnalise.length > 0) {
        const viaveis = opsComAnalise.filter(op => op.analiseViabilidade.status === 'VIAVEL').length;
        const parciais = opsComAnalise.filter(op => op.analiseViabilidade.status === 'PARCIAL').length;
        relatorio.indicadores.taxaViabilidade = ((viaveis + parciais) / opsComAnalise.length * 100).toFixed(1);
      }

      return relatorio;
    }

    /**
     * 🔗 AGRUPAMENTO INTELIGENTE DE OPs
     * Agrupa OPs por similaridade de materiais
     */
    async function agruparOPsInteligente() {
      const opsViaveis = ordensProducao.filter(op =>
        op.analiseViabilidade?.status === 'VIAVEL' &&
        (op.status === 'Pendente' || op.status === 'PENDENTE')
      );

      const grupos = [];
      const opsProcessadas = new Set();

      for (const op of opsViaveis) {
        if (opsProcessadas.has(op.id)) continue;

        const grupo = {
          id: `GRUPO_${grupos.length + 1}`,
          ops: [op],
          materiaisComuns: new Set(),
          prioridadeMedia: obterPrioridadeNumerica(op.prioridade),
          dataEntregaMaisProxima: new Date(op.dataEntrega)
        };

        // Buscar OPs similares
        for (const outraOp of opsViaveis) {
          if (opsProcessadas.has(outraOp.id) || outraOp.id === op.id) continue;

          const similaridade = calcularSimilaridadeMateriais(op, outraOp);
          if (similaridade > 0.3) { // 30% de similaridade mínima
            grupo.ops.push(outraOp);
            opsProcessadas.add(outraOp.id);

            // Atualizar métricas do grupo
            grupo.prioridadeMedia = (grupo.prioridadeMedia + obterPrioridadeNumerica(outraOp.prioridade)) / 2;
            if (new Date(outraOp.dataEntrega) < grupo.dataEntregaMaisProxima) {
              grupo.dataEntregaMaisProxima = new Date(outraOp.dataEntrega);
            }
          }
        }

        opsProcessadas.add(op.id);
        grupos.push(grupo);
      }

      return grupos.sort((a, b) => b.prioridadeMedia - a.prioridadeMedia);
    }

    /**
     * ⏰ ANÁLISE DE PRIORIDADES
     * Reordena OPs por urgência, viabilidade e data de entrega
     */
    async function analisarPrioridadesOPs() {
      const opsParaAnalise = ordensProducao.filter(op =>
        op.status === 'Pendente' || op.status === 'PENDENTE'
      );

      const prioridades = opsParaAnalise.map(op => {
        const agora = new Date();
        const dataEntrega = new Date(op.dataEntrega);
        const diasParaEntrega = Math.ceil((dataEntrega - agora) / (1000 * 60 * 60 * 24));

        let score = 0;

        // Pontuação por prioridade
        const prioridadeScore = {
          'URGENTE': 100,
          'ALTA': 75,
          'NORMAL': 50,
          'BAIXA': 25
        };
        score += prioridadeScore[op.prioridade] || 50;

        // Pontuação por viabilidade
        if (op.analiseViabilidade) {
          const viabilidadeScore = {
            'VIAVEL': 50,
            'PARCIAL': 25,
            'INVIAVEL': 0
          };
          score += viabilidadeScore[op.analiseViabilidade.status] || 0;
        }

        // Pontuação por urgência de entrega
        if (diasParaEntrega <= 0) {
          score += 100; // Atrasada
        } else if (diasParaEntrega <= 3) {
          score += 75; // Muito urgente
        } else if (diasParaEntrega <= 7) {
          score += 50; // Urgente
        } else if (diasParaEntrega <= 15) {
          score += 25; // Normal
        }

        return {
          op: op,
          score: score,
          diasParaEntrega: diasParaEntrega,
          recomendacao: determinarRecomendacao(score, diasParaEntrega, op.analiseViabilidade)
        };
      });

      return prioridades.sort((a, b) => b.score - a.score);
    }

    // ===== FUNÇÕES AUXILIARES =====

    function gerarSugestoesDiagnostico(gargalos) {
      const sugestoes = [];

      gargalos.slice(0, 3).forEach(gargalo => {
        sugestoes.push(`Comprar ${gargalo.deficitTotal.toFixed(2)} unidades de ${gargalo.codigo}`);
        sugestoes.push(`Verificar fornecedores alternativos para ${gargalo.codigo}`);
      });

      return sugestoes;
    }

    async function identificarTransferenciasNecessarias() {
      // Implementação simplificada - buscar materiais disponíveis em outros armazéns
      return [];
    }

    async function identificarComprasUrgentes() {
      // Implementação simplificada - identificar materiais com déficit crítico
      return [];
    }

    function obterPrioridadeNumerica(prioridade) {
      const valores = {
        'URGENTE': 4,
        'ALTA': 3,
        'NORMAL': 2,
        'BAIXA': 1
      };
      return valores[prioridade] || 2;
    }

    function calcularSimilaridadeMateriais(op1, op2) {
      // Implementação simplificada - comparar materiais necessários
      if (!op1.materiaisNecessarios || !op2.materiaisNecessarios) return 0;

      const materiais1 = new Set(op1.materiaisNecessarios.map(m => m.produtoId));
      const materiais2 = new Set(op2.materiaisNecessarios.map(m => m.produtoId));

      const intersecao = new Set([...materiais1].filter(x => materiais2.has(x)));
      const uniao = new Set([...materiais1, ...materiais2]);

      return intersecao.size / uniao.size;
    }

    function determinarRecomendacao(score, diasParaEntrega, analiseViabilidade) {
      if (score >= 150) return 'PRODUZIR_IMEDIATAMENTE';
      if (score >= 100) return 'ALTA_PRIORIDADE';
      if (score >= 75) return 'PRIORIDADE_NORMAL';
      if (diasParaEntrega < 0) return 'ATRASADA';
      if (!analiseViabilidade || analiseViabilidade.status === 'INVIAVEL') return 'AGUARDAR_MATERIAIS';
      return 'PROGRAMAR_PRODUCAO';
    }

    // ===== FUNÇÕES DE EXIBIÇÃO =====

    function exibirDiagnosticoCompleto(diagnostico) {
      // Implementação da exibição do diagnóstico
      console.log('📊 Diagnóstico:', diagnostico);
      mostrarNotificacao(`🔍 ${diagnostico.gargalosCriticos.length} gargalos identificados`, 'info', 3000);
    }

    function exibirSugestoesPCP(sugestoes) {
      // Implementação da exibição das sugestões
      console.log('💡 Sugestões:', sugestoes);
    }

    function exibirRelatorioExecutivo(relatorio) {
      // Implementação da exibição do relatório
      console.log('📋 Relatório:', relatorio);
    }

    function exibirAgrupamentoOPs(grupos) {
      // Implementação da exibição dos grupos
      console.log('🔗 Grupos:', grupos);
    }

    function exibirAnaliseprioridades(prioridades) {
      // Implementação da exibição das prioridades
      console.log('⏰ Prioridades:', prioridades);
    }

    // Inicializar aplicação
    document.addEventListener('DOMContentLoaded', function() {
      carregarDados();
    });
  </script>
</body>
</html>
