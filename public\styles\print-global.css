/* ===================================================================
   CSS GLOBAL PARA IMPRESSÃO - WiZAR ERP
   ===================================================================
   Arquivo centralizado para padronizar impressão em todos os relatórios
   ================================================================= */

/* Configuração da página */
@page {
    size: A4 portrait;
    margin: 15mm 20mm;
}

/* Reset e configurações base para impressão */
@media print {
    /* Forçar cores exatas na impressão */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        box-sizing: border-box !important;
    }
    
    /* Configurações do body */
    body {
        margin: 0 !important;
        padding: 0 !important;
        font-family: Arial, sans-serif !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        background: white !important;
        color: #000 !important;
    }
    
    /* Ocultar elementos desnecessários na impressão */
    .no-print,
    .btn,
    .button,
    .filters,
    .action-buttons,
    .modal,
    .toast,
    .notification,
    .sidebar,
    .navbar,
    .pagination,
    .form-group,
    .filter-row,
    .filter-group,
    button,
    input[type="button"],
    input[type="submit"],
    .back-button {
        display: none !important;
    }
    
    /* Container principal */
    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border: none !important;
        border-radius: 0 !important;
        background: white !important;
    }
    
    /* Cabeçalho */
    .header {
        margin-bottom: 10mm !important;
        padding: 5mm 0 !important;
        border-bottom: 2px solid #000 !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }
    
    .header h1 {
        font-size: 18px !important;
        font-weight: bold !important;
        margin: 0 !important;
        color: #000 !important;
    }
    
    .header h2 {
        font-size: 14px !important;
        font-weight: normal !important;
        margin: 2px 0 !important;
        color: #333 !important;
    }
    
    /* Logo */
    .logo,
    .header img {
        max-width: 120px !important;
        max-height: 40px !important;
        object-fit: contain !important;
    }
    
    /* Rodapé */
    .footer {
        margin-top: 10mm !important;
        padding-top: 5mm !important;
        border-top: 1px solid #000 !important;
        font-size: 8px !important;
        text-align: center !important;
        color: #666 !important;
    }
    
    /* Quebras de página */
    .page-break {
        page-break-after: always !important;
    }
    
    .page-break-avoid {
        page-break-inside: avoid !important;
    }
    
    .page-break-before {
        page-break-before: always !important;
    }
    
    .order-page,
    .report-page {
        page-break-after: always !important;
        padding: 0 !important;
        margin: 0 !important;
        box-shadow: none !important;
    }
    
    .order-page:last-child,
    .report-page:last-child {
        page-break-after: avoid !important;
    }
    
    /* Títulos e subtítulos */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        margin: 5mm 0 3mm 0 !important;
        page-break-after: avoid !important;
    }
    
    h1 { font-size: 18px !important; }
    h2 { font-size: 16px !important; }
    h3 { font-size: 14px !important; }
    h4 { font-size: 12px !important; }
    
    /* Parágrafos */
    p {
        margin: 2mm 0 !important;
        orphans: 3 !important;
        widows: 3 !important;
    }
    
    /* Links */
    a {
        color: #000 !important;
        text-decoration: none !important;
    }
    
    /* Listas */
    ul, ol {
        margin: 2mm 0 !important;
        padding-left: 5mm !important;
    }
    
    li {
        margin: 1mm 0 !important;
    }
    
    /* Divisores */
    hr {
        border: none !important;
        border-top: 1px solid #000 !important;
        margin: 5mm 0 !important;
    }
    
    /* Caixas e seções */
    .box,
    .section {
        border: 1px solid #000 !important;
        border-radius: 2mm !important;
        padding: 3mm !important;
        margin-bottom: 3mm !important;
        page-break-inside: avoid !important;
    }
    
    /* Status e badges */
    .status-badge,
    .badge {
        border: 1px solid #000 !important;
        padding: 1mm 2mm !important;
        font-size: 8px !important;
        font-weight: bold !important;
        background: white !important;
        color: #000 !important;
    }
    
    /* Informações da empresa */
    .company-info {
        font-size: 10px !important;
        line-height: 1.2 !important;
        margin-bottom: 5mm !important;
    }
    
    /* Data e hora */
    .datetime {
        font-size: 8px !important;
        color: #666 !important;
        text-align: right !important;
    }
    
    /* Assinaturas */
    .signature-area {
        margin-top: 15mm !important;
        border-top: 1px solid #000 !important;
        padding-top: 2mm !important;
        text-align: center !important;
        font-size: 10px !important;
    }
    
    /* Observações */
    .observations {
        border: 1px solid #000 !important;
        padding: 3mm !important;
        margin-top: 5mm !important;
        font-size: 10px !important;
        min-height: 15mm !important;
    }
    
    /* Totais e valores */
    .total-section {
        border: 2px solid #000 !important;
        padding: 3mm !important;
        margin-top: 5mm !important;
        font-weight: bold !important;
        background: #f0f0f0 !important;
    }
    
    .value {
        font-weight: bold !important;
        text-align: right !important;
    }
    
    /* Códigos de barras e QR codes */
    .barcode,
    .qrcode {
        text-align: center !important;
        margin: 5mm 0 !important;
    }
    
    /* Avisos e alertas */
    .warning,
    .alert {
        border: 2px solid #000 !important;
        padding: 2mm !important;
        margin: 3mm 0 !important;
        font-weight: bold !important;
        text-align: center !important;
    }
    
    /* Numeração de páginas */
    .page-number {
        position: fixed !important;
        bottom: 10mm !important;
        right: 20mm !important;
        font-size: 8px !important;
        color: #666 !important;
    }
    
    /* Ocultar elementos específicos */
    .report-info,
    .order-warning,
    .error-message {
        display: none !important;
    }
}

/* ===================================================================
   UTILITÁRIOS PARA IMPRESSÃO
   ================================================================= */

/* Classes auxiliares para controle de impressão */
.print-only {
    display: none;
}

@media print {
    .print-only {
        display: block !important;
    }
}

/* Forçar quebra de página */
.force-page-break {
    page-break-before: always !important;
}

/* Evitar quebra de página */
.keep-together {
    page-break-inside: avoid !important;
}

/* Texto pequeno para impressão */
.print-small {
    font-size: 8px !important;
}

/* Texto grande para impressão */
.print-large {
    font-size: 14px !important;
    font-weight: bold !important;
}

/* Centralizar na impressão */
.print-center {
    text-align: center !important;
}

/* Alinhar à direita na impressão */
.print-right {
    text-align: right !important;
}

/* ===================================================================
   FIM DO ARQUIVO
   ================================================================= */
