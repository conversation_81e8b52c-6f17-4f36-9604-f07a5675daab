<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnóstico Específico das Suas Coleções</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .collection-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-sample {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .btn-test {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #007bff; font-weight: bold; }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 Diagnóstico Específico das Suas Coleções</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testarTodasColecoes()" class="btn-test">🔍 Testar Todas as Coleções</button>
            <button onclick="analisarEstruturaDados()" class="btn-test">📊 Analisar Estrutura dos Dados</button>
            <button onclick="testarFluxoSimples()" class="btn-test">🧪 Testar Fluxo Simples</button>
        </div>

        <div id="logArea" class="log-area"></div>
        <div id="resultsContainer"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            orderBy,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser'));
        let dadosColecoes = {};

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logArea.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema carregado. Usuário: ' + currentUser.nome, 'info');
        };

        window.testarTodasColecoes = async function() {
            log('🔄 Iniciando teste de todas as coleções...', 'info');
            
            const suasColecoes = [
                'solicitacoesCompra',
                'cotacoes',
                'pedidosCompra',
                'estoques',
                'estoqueQualidade',
                'movimentacoesEstoque',
                'transferenciasArmazem',
                'produtos',
                'armazens'
            ];

            dadosColecoes = {};
            let html = '';

            for (const nomeColecao of suasColecoes) {
                try {
                    log(`Testando coleção: ${nomeColecao}`, 'info');
                    
                    const snapshot = await getDocs(collection(db, nomeColecao));
                    const documentos = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    
                    dadosColecoes[nomeColecao] = {
                        nome: nomeColecao,
                        quantidade: documentos.length,
                        documentos: documentos.slice(0, 3) // Primeiros 3 para análise
                    };

                    if (documentos.length > 0) {
                        log(`✅ ${nomeColecao}: ${documentos.length} registros encontrados`, 'success');
                        
                        html += `
                            <div class="collection-card" style="border-left: 4px solid #28a745;">
                                <h3>✅ ${nomeColecao}</h3>
                                <p><strong>Registros encontrados:</strong> ${documentos.length}</p>
                                <h4>Amostra dos dados:</h4>
                                <div class="data-sample">
                        `;
                        
                        documentos.slice(0, 2).forEach((doc, index) => {
                            html += `<strong>📄 Documento ${index + 1} (ID: ${doc.id}):</strong><br>`;
                            Object.keys(doc).slice(0, 8).forEach(key => {
                                let value = doc[key];
                                if (typeof value === 'object' && value !== null) {
                                    if (value.seconds) {
                                        value = `[Timestamp: ${new Date(value.seconds * 1000).toLocaleString()}]`;
                                    } else if (Array.isArray(value)) {
                                        value = `[Array com ${value.length} itens]`;
                                    } else {
                                        value = `[Objeto: ${Object.keys(value).join(', ')}]`;
                                    }
                                }
                                html += `&nbsp;&nbsp;<strong>${key}:</strong> ${value}<br>`;
                            });
                            html += '<br>';
                        });
                        
                        html += '</div></div>';
                    } else {
                        log(`⚠️ ${nomeColecao}: Coleção vazia`, 'warning');
                        html += `
                            <div class="collection-card" style="border-left: 4px solid #ffc107;">
                                <h3>⚠️ ${nomeColecao}</h3>
                                <p><strong>Status:</strong> Coleção existe mas está vazia</p>
                            </div>
                        `;
                    }

                } catch (error) {
                    log(`❌ ${nomeColecao}: Erro - ${error.message}`, 'error');
                    html += `
                        <div class="collection-card" style="border-left: 4px solid #dc3545;">
                            <h3>❌ ${nomeColecao}</h3>
                            <p><strong>Erro:</strong> ${error.message}</p>
                        </div>
                    `;
                }
            }

            document.getElementById('resultsContainer').innerHTML = html;
            log('✅ Teste de coleções concluído', 'success');
        };

        window.analisarEstruturaDados = async function() {
            if (Object.keys(dadosColecoes).length === 0) {
                alert('Execute primeiro o teste de coleções!');
                return;
            }

            log('📊 Analisando estrutura dos dados...', 'info');
            
            let html = '<div class="collection-card"><h3>📊 Análise da Estrutura dos Dados</h3>';

            // Analisar solicitacoesCompra
            if (dadosColecoes['solicitacaesCompra']?.quantidade > 0) {
                const amostra = dadosColecoes['solicitacaesCompra'].documentos[0];
                html += `
                    <h4>🔍 Estrutura de solicitacaesCompra:</h4>
                    <div class="data-sample">
                        <strong>Campos principais encontrados:</strong><br>
                `;
                
                Object.keys(amostra).forEach(key => {
                    const value = amostra[key];
                    let tipo = typeof value;
                    if (Array.isArray(value)) tipo = `Array[${value.length}]`;
                    if (value && typeof value === 'object' && value.seconds) tipo = 'Timestamp';
                    
                    html += `&nbsp;&nbsp;• <strong>${key}:</strong> ${tipo}<br>`;
                });

                // Analisar itens se existir
                if (amostra.itens && Array.isArray(amostra.itens) && amostra.itens.length > 0) {
                    html += `<br><strong>Estrutura dos itens:</strong><br>`;
                    Object.keys(amostra.itens[0]).forEach(key => {
                        html += `&nbsp;&nbsp;&nbsp;&nbsp;• <strong>${key}:</strong> ${typeof amostra.itens[0][key]}<br>`;
                    });
                }
                
                html += '</div>';
            }

            // Analisar produtos
            if (dadosColecoes['produtos']?.quantidade > 0) {
                const amostra = dadosColecoes['produtos'].documentos[0];
                html += `
                    <h4>🔍 Estrutura de produtos:</h4>
                    <div class="data-sample">
                        <strong>Campos principais encontrados:</strong><br>
                `;
                
                Object.keys(amostra).forEach(key => {
                    const value = amostra[key];
                    let tipo = typeof value;
                    if (Array.isArray(value)) tipo = `Array[${value.length}]`;
                    if (value && typeof value === 'object' && value.seconds) tipo = 'Timestamp';
                    
                    html += `&nbsp;&nbsp;• <strong>${key}:</strong> ${tipo}<br>`;
                });
                
                html += '</div>';
            }

            // Analisar estoques
            if (dadosColecoes['estoques']?.quantidade > 0) {
                const amostra = dadosColecoes['estoques'].documentos[0];
                html += `
                    <h4>🔍 Estrutura de estoques:</h4>
                    <div class="data-sample">
                        <strong>Campos principais encontrados:</strong><br>
                `;
                
                Object.keys(amostra).forEach(key => {
                    const value = amostra[key];
                    let tipo = typeof value;
                    if (Array.isArray(value)) tipo = `Array[${value.length}]`;
                    if (value && typeof value === 'object' && value.seconds) tipo = 'Timestamp';
                    
                    html += `&nbsp;&nbsp;• <strong>${key}:</strong> ${tipo}<br>`;
                });
                
                html += '</div>';
            }

            html += '</div>';
            document.getElementById('resultsContainer').innerHTML = html;
            log('✅ Análise de estrutura concluída', 'success');
        };

        window.testarFluxoSimples = async function() {
            if (Object.keys(dadosColecoes).length === 0) {
                alert('Execute primeiro o teste de coleções!');
                return;
            }

            log('🧪 Testando fluxo simples...', 'info');
            
            let materiaisEncontrados = [];

            try {
                // Teste 1: Buscar solicitações recentes
                if (dadosColecoes['solicitacaesCompra']?.quantidade > 0) {
                    log('Testando solicitações...', 'info');
                    
                    const solicitacoes = dadosColecoes['solicitacaesCompra'].documentos;
                    
                    solicitacoes.forEach(sol => {
                        if (sol.itens && Array.isArray(sol.itens)) {
                            sol.itens.forEach(item => {
                                materiaisEncontrados.push({
                                    fonte: 'solicitacaesCompra',
                                    produtoId: item.produtoId || item.codigo,
                                    codigo: item.codigo,
                                    descricao: item.descricao,
                                    quantidade: item.quantidade || item.qtdSolicitada,
                                    status: 'SOLICITADO',
                                    data: sol.dataSolicitacao,
                                    documento: sol.id
                                });
                            });
                        }
                    });
                    
                    log(`✅ Encontrados ${materiaisEncontrados.length} materiais em solicitações`, 'success');
                }

                // Teste 2: Verificar produtos
                if (dadosColecoes['produtos']?.quantidade > 0) {
                    log('Verificando produtos...', 'info');
                    
                    const produtos = dadosColecoes['produtos'].documentos;
                    let produtosEncontrados = 0;
                    
                    materiaisEncontrados.forEach(material => {
                        const produto = produtos.find(p => 
                            p.id === material.produtoId || 
                            p.codigo === material.codigo ||
                            p.codigo === material.produtoId
                        );
                        
                        if (produto) {
                            material.produtoEncontrado = true;
                            material.codigoProduto = produto.codigo;
                            material.descricaoProduto = produto.descricao;
                            produtosEncontrados++;
                        }
                    });
                    
                    log(`✅ ${produtosEncontrados}/${materiaisEncontrados.length} produtos encontrados`, 'success');
                }

                // Teste 3: Verificar estoques
                if (dadosColecoes['estoques']?.quantidade > 0) {
                    log('Verificando estoques...', 'info');
                    
                    const estoques = dadosColecoes['estoques'].documentos;
                    let estoquesEncontrados = 0;
                    
                    materiaisEncontrados.forEach(material => {
                        const estoque = estoques.find(e => 
                            e.produtoId === material.produtoId ||
                            e.produtoId === material.codigo
                        );
                        
                        if (estoque) {
                            material.estoqueEncontrado = true;
                            material.saldoAtual = estoque.saldo;
                            material.armazemId = estoque.armazemId;
                            estoquesEncontrados++;
                        }
                    });
                    
                    log(`✅ ${estoquesEncontrados}/${materiaisEncontrados.length} estoques encontrados`, 'success');
                }

                // Mostrar resultados
                let html = `
                    <div class="collection-card">
                        <h3>🧪 Resultado do Teste de Fluxo</h3>
                        <p><strong>Materiais processados:</strong> ${materiaisEncontrados.length}</p>
                        
                        <h4>📋 Amostra dos Materiais Encontrados:</h4>
                        <div class="data-sample">
                `;

                materiaisEncontrados.slice(0, 5).forEach((material, index) => {
                    html += `
                        <strong>Material ${index + 1}:</strong><br>
                        &nbsp;&nbsp;• Código: ${material.codigo || 'N/A'}<br>
                        &nbsp;&nbsp;• Descrição: ${material.descricao || 'N/A'}<br>
                        &nbsp;&nbsp;• Quantidade: ${material.quantidade || 'N/A'}<br>
                        &nbsp;&nbsp;• Status: ${material.status}<br>
                        &nbsp;&nbsp;• Produto encontrado: ${material.produtoEncontrado ? '✅' : '❌'}<br>
                        &nbsp;&nbsp;• Estoque encontrado: ${material.estoqueEncontrado ? '✅' : '❌'}<br>
                        &nbsp;&nbsp;• Saldo atual: ${material.saldoAtual || 'N/A'}<br>
                        <br>
                    `;
                });

                html += '</div>';

                if (materiaisEncontrados.length === 0) {
                    html += `
                        <div style="background: #f8d7da; padding: 15px; border-radius: 6px; margin: 15px 0;">
                            <h4>❌ Nenhum Material Encontrado!</h4>
                            <p>Possíveis causas:</p>
                            <ul>
                                <li>Solicitações não têm campo 'itens'</li>
                                <li>Estrutura dos dados é diferente do esperado</li>
                                <li>Campos têm nomes diferentes</li>
                                <li>Dados são muito antigos</li>
                            </ul>
                        </div>
                    `;
                } else {
                    html += `
                        <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 15px 0;">
                            <h4>✅ Fluxo Funcionando!</h4>
                            <p>Encontrados ${materiaisEncontrados.length} materiais. O dashboard deve funcionar.</p>
                        </div>
                    `;
                }

                html += '</div>';
                document.getElementById('resultsContainer').innerHTML = html;
                log(`✅ Teste de fluxo concluído - ${materiaisEncontrados.length} materiais encontrados`, 'success');

            } catch (error) {
                log(`❌ Erro no teste de fluxo: ${error.message}`, 'error');
            }
        };
    </script>
</body>
</html>
