<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🩺 Diagnóstico Simples do Sistema</title>
    <link rel="stylesheet" href="styles/styles.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .diagnostic-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .test-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
            border-color: #e74c3c;
        }
        .test-card.running {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .test-card.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .test-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }
        .test-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .test-result {
            font-weight: bold;
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }
        .btn-success { background: #28a745; color: white; }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .log-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .controls-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .item-search {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
        }
        .search-input {
            width: 300px;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            margin: 10px;
            font-size: 16px;
        }
        .result-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="diagnostic-header">
            <h1>🩺 Diagnóstico Simples do Sistema</h1>
            <p>Verificação rápida e eficiente dos principais componentes</p>
            <p><strong>Status:</strong> <span id="statusGeral">Pronto para diagnóstico</span></p>
        </div>

        <!-- Busca Específica de Item -->
        <div class="item-search">
            <h3>🔍 Buscar Item Específico (Ex: 101133)</h3>
            <input type="text" id="itemCodigo" class="search-input" placeholder="Digite o código do produto..." value="101133">
            <button onclick="buscarItem()" class="btn btn-primary">🔍 Buscar Item</button>
            <div id="itemResult"></div>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <h3>🎛️ Diagnósticos Rápidos</h3>
            <button onclick="diagnosticoCompleto()" class="btn btn-primary">🩺 Diagnóstico Completo</button>
            <button onclick="verificarSaldos()" class="btn btn-success">📊 Verificar Saldos</button>
            <button onclick="limparLog()" class="btn btn-secondary">🗑️ Limpar Log</button>
        </div>

        <!-- Grid de Testes -->
        <div class="test-grid">
            <div class="test-card" onclick="testarConexao()">
                <div class="test-icon">🔗</div>
                <div class="test-title">Conexão Firebase</div>
                <div class="test-description">Testa conectividade com o banco</div>
                <div class="test-result" id="result-conexao"></div>
            </div>

            <div class="test-card" onclick="verificarProdutos()">
                <div class="test-icon">📦</div>
                <div class="test-title">Produtos</div>
                <div class="test-description">Verifica cadastro de produtos</div>
                <div class="test-result" id="result-produtos"></div>
            </div>

            <div class="test-card" onclick="verificarEstoques()">
                <div class="test-icon">🏪</div>
                <div class="test-title">Estoques</div>
                <div class="test-description">Verifica saldos de estoque</div>
                <div class="test-result" id="result-estoques"></div>
            </div>

            <div class="test-card" onclick="verificarQualidade()">
                <div class="test-icon">✅</div>
                <div class="test-title">Qualidade</div>
                <div class="test-description">Verifica itens na qualidade</div>
                <div class="test-result" id="result-qualidade"></div>
            </div>
        </div>

        <!-- Log -->
        <div class="log-area" id="logArea"></div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function setResult(testId, status, message) {
            const element = document.getElementById(`result-${testId}`);
            const card = element.closest('.test-card');
            
            card.className = `test-card ${status}`;
            element.textContent = message;
            element.className = `test-result ${status}`;
        }

        window.onload = function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }
            log('Sistema de diagnóstico simples carregado', 'info');
        };

        window.buscarItem = async function() {
            const codigo = document.getElementById('itemCodigo').value.trim();
            if (!codigo) {
                alert('Digite um código de produto!');
                return;
            }

            log(`🔍 Buscando item ${codigo}...`, 'info');
            const resultDiv = document.getElementById('itemResult');
            resultDiv.innerHTML = '<p>🔄 Buscando...</p>';

            try {
                // Buscar produto
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produto = produtos.find(p => p.codigo === codigo);

                if (!produto) {
                    resultDiv.innerHTML = `<div class="result-card" style="border-color: #dc3545;"><strong>❌ Produto ${codigo} não encontrado</strong></div>`;
                    log(`❌ Produto ${codigo} não encontrado`, 'error');
                    return;
                }

                log(`✅ Produto encontrado: ${produto.id}`, 'success');

                // Buscar na qualidade
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const itemQualidade = qualidade.find(q => q.produtoId === produto.id);

                // Buscar no estoque
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const itemEstoque = estoques.find(e => e.produtoId === produto.id);

                // Montar resultado
                let html = `
                    <div class="result-card">
                        <h4>📦 ${codigo} - ${produto.descricao}</h4>
                        <p><strong>ID do Produto:</strong> ${produto.id}</p>
                        <p><strong>Unidade:</strong> ${produto.unidade || 'N/A'}</p>
                `;

                if (itemQualidade) {
                    html += `
                        <p><strong>🔍 Na Qualidade:</strong></p>
                        <ul>
                            <li>ID: ${itemQualidade.id}</li>
                            <li>Saldo: ${itemQualidade.saldo || 'undefined'} ${produto.unidade || 'UN'}</li>
                            <li>Status: ${itemQualidade.status || 'PENDENTE'}</li>
                            <li>Data Entrada: ${itemQualidade.dataEntrada ? new Date(itemQualidade.dataEntrada.seconds * 1000).toLocaleString() : 'N/A'}</li>
                        </ul>
                    `;
                } else {
                    html += `<p><strong>🔍 Na Qualidade:</strong> Não encontrado</p>`;
                }

                if (itemEstoque) {
                    html += `
                        <p><strong>📦 No Estoque:</strong></p>
                        <ul>
                            <li>ID: ${itemEstoque.id}</li>
                            <li>Saldo: ${itemEstoque.saldo || 'undefined'} ${produto.unidade || 'UN'}</li>
                            <li>Armazém: ${itemEstoque.armazemId || 'N/A'}</li>
                            <li>Última Movimentação: ${itemEstoque.ultimaMovimentacao ? new Date(itemEstoque.ultimaMovimentacao.seconds * 1000).toLocaleString() : 'N/A'}</li>
                        </ul>
                    `;
                } else {
                    html += `<p><strong>📦 No Estoque:</strong> Não encontrado</p>`;
                }

                // Diagnóstico
                html += `<p><strong>🩺 Diagnóstico:</strong></p><ul>`;
                
                if (itemQualidade && (itemQualidade.saldo === undefined || itemQualidade.saldo === null)) {
                    html += `<li style="color: #dc3545;">❌ Saldo na qualidade está undefined</li>`;
                }
                
                if (itemEstoque && itemEstoque.saldo === 0 && itemQualidade && itemQualidade.saldo > 0) {
                    html += `<li style="color: #ffc107;">⚠️ Item na qualidade mas estoque zerado</li>`;
                }
                
                if (!itemQualidade && !itemEstoque) {
                    html += `<li style="color: #dc3545;">❌ Item não encontrado em lugar nenhum</li>`;
                }
                
                if (itemEstoque && itemEstoque.saldo > 0) {
                    html += `<li style="color: #28a745;">✅ Item disponível no estoque</li>`;
                }

                html += `</ul></div>`;
                resultDiv.innerHTML = html;

                log(`✅ Análise do item ${codigo} concluída`, 'success');

            } catch (error) {
                log(`❌ Erro ao buscar item: ${error.message}`, 'error');
                resultDiv.innerHTML = `<div class="result-card" style="border-color: #dc3545;"><strong>❌ Erro: ${error.message}</strong></div>`;
            }
        };

        window.testarConexao = async function() {
            log('🔗 Testando conexão...', 'info');
            try {
                const testSnap = await getDocs(query(collection(db, "produtos"), limit(1)));
                setResult('conexao', 'success', 'Conexão OK');
                log('✅ Conexão estabelecida', 'success');
            } catch (error) {
                setResult('conexao', 'error', `Erro: ${error.message}`);
                log(`❌ Erro de conexão: ${error.message}`, 'error');
            }
        };

        window.verificarProdutos = async function() {
            log('📦 Verificando produtos...', 'info');
            try {
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                produtos.forEach(produto => {
                    if (!produto.codigo) problemas++;
                    if (!produto.descricao) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setResult('produtos', status, `${produtos.length} produtos, ${problemas} problemas`);
                log(`📦 ${produtos.length} produtos verificados`, 'success');
            } catch (error) {
                setResult('produtos', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.verificarEstoques = async function() {
            log('🏪 Verificando estoques...', 'info');
            try {
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                estoques.forEach(estoque => {
                    if (estoque.saldo === undefined || estoque.saldo === null) problemas++;
                    if (estoque.saldo < 0) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setResult('estoques', status, `${estoques.length} registros, ${problemas} problemas`);
                log(`🏪 ${estoques.length} estoques verificados`, 'success');
            } catch (error) {
                setResult('estoques', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.verificarQualidade = async function() {
            log('✅ Verificando qualidade...', 'info');
            try {
                const qualidadeSnap = await getDocs(collection(db, "estoqueQualidade"));
                const qualidade = qualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let problemas = 0;
                qualidade.forEach(item => {
                    if (item.saldo === undefined || item.saldo === null) problemas++;
                    if (!item.produtoId) problemas++;
                });

                const status = problemas === 0 ? 'success' : 'warning';
                setResult('qualidade', status, `${qualidade.length} itens, ${problemas} problemas`);
                log(`✅ ${qualidade.length} itens verificados`, 'success');
            } catch (error) {
                setResult('qualidade', 'error', `Erro: ${error.message}`);
                log(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.diagnosticoCompleto = async function() {
            log('🩺 Iniciando diagnóstico completo...', 'info');
            document.getElementById('statusGeral').textContent = 'Executando diagnóstico...';

            await testarConexao();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarProdutos();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarEstoques();
            await new Promise(resolve => setTimeout(resolve, 500));
            await verificarQualidade();

            document.getElementById('statusGeral').textContent = 'Diagnóstico concluído';
            log('✅ Diagnóstico completo finalizado', 'success');
        };

        window.verificarSaldos = async function() {
            log('📊 Verificação específica de saldos...', 'info');
            await verificarEstoques();
            await verificarQualidade();
            log('✅ Verificação de saldos concluída', 'success');
        };

        window.limparLog = function() {
            document.getElementById('logArea').innerHTML = '';
            log('🗑️ Log limpo', 'info');
        };
    </script>
</body>
</html>
