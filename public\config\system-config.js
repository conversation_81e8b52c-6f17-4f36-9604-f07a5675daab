// Configuração centralizada do sistema
export const SystemConfig = {
  
  // Configurações de armazéns
  warehouses: {
    main: 'ALM01',           // Almoxarifado principal
    production: 'PROD01',    // Produção principal
    quality: 'QUAL01',       // Controle de qualidade
    shipping: 'EXP01'        // Expedição
  },
  
  // Configurações de documentos
  documents: {
    prefixes: {
      productionOrder: 'OP',
      transfer: 'TRF',
      purchaseRequest: 'SC',
      purchaseOrder: 'PC',
      invoice: 'NF'
    },
    numberLength: 4
  },
  
  // Configurações de estoque
  inventory: {
    minimumStock: {
      checkEnabled: true,
      alertLevel: 'MEDIA'
    },
    reservations: {
      autoReserve: true,
      releaseOnComplete: true
    },
    movements: {
      requireApproval: false,
      batchSize: 100
    }
  },
  
  // Configurações de produção
  production: {
    bom: {
      maxLevels: 10,
      autoExplode: true
    },
    orders: {
      autoCalculateRequirements: true,
      autoGenerateRequests: true,
      allowOverproduction: false
    },
    appointments: {
      requireOperator: true,
      allowPartialProduction: true
    }
  },
  
  // Configurações de compras
  purchasing: {
    requests: {
      autoApproval: false,
      groupBy: 'familia',
      requireJustification: true
    },
    orders: {
      requireMultipleQuotes: true,
      minimumQuotes: 3
    }
  },
  
  // Configurações de qualidade
  quality: {
    inspection: {
      required: ['MP', 'COMP'],  // Tipos que requerem inspeção
      autoApprove: false
    },
    sampling: {
      enabled: true,
      defaultPercentage: 10
    }
  },
  
  // Configurações de relatórios
  reports: {
    cache: {
      enabled: true,
      ttl: 300000  // 5 minutos
    },
    pagination: {
      defaultSize: 50,
      maxSize: 1000
    }
  },
  
  // Configurações de notificações
  notifications: {
    email: {
      enabled: false,
      smtp: {
        host: '',
        port: 587,
        secure: false
      }
    },
    system: {
      enabled: true,
      types: ['STOCK_LOW', 'ORDER_COMPLETE', 'APPROVAL_NEEDED']
    }
  },
  
  // Configurações de auditoria
  audit: {
    enabled: true,
    logLevel: 'INFO',
    retention: 365  // dias
  },
  
  // Configurações de performance
  performance: {
    cache: {
      products: 600000,      // 10 minutos
      warehouses: 1800000,   // 30 minutos
      structures: 900000     // 15 minutos
    },
    batch: {
      movements: 50,
      transfers: 20,
      calculations: 100
    }
  },
  
  // Configurações de segurança
  security: {
    session: {
      timeout: 3600000  // 1 hora
    },
    permissions: {
      strictMode: true
    }
  },
  
  // Configurações de integração
  integration: {
    erp: {
      enabled: false,
      endpoint: '',
      apiKey: ''
    },
    fiscal: {
      enabled: false,
      provider: ''
    }
  }
};

// Configurações específicas por ambiente
export const EnvironmentConfig = {
  development: {
    debug: true,
    logging: 'verbose',
    cache: {
      enabled: false
    }
  },
  
  production: {
    debug: false,
    logging: 'error',
    cache: {
      enabled: true
    },
    performance: {
      optimization: true
    }
  },
  
  testing: {
    debug: true,
    logging: 'info',
    mockData: true
  }
};

// Função para obter configuração atual
export function getConfig(environment = 'production') {
  return {
    ...SystemConfig,
    ...EnvironmentConfig[environment]
  };
}

// Configurações de validação
export const ValidationRules = {
  productionOrder: {
    quantity: { min: 0.001, max: 999999 },
    priority: ['BAIXA', 'NORMAL', 'ALTA', 'URGENTE'],
    status: ['PENDENTE', 'EM_PRODUCAO', 'FINALIZADA', 'CANCELADA']
  },
  
  movement: {
    quantity: { min: 0.001, max: 999999 },
    types: ['ENTRADA', 'SAIDA'],
    documents: ['COMPRA', 'VENDA', 'TRANSFERENCIA', 'PRODUCAO', 'AJUSTE']
  },
  
  transfer: {
    quantity: { min: 0.001, max: 999999 },
    reasons: ['SUPRIMENTO_OP', 'REPOSICAO', 'MANUTENCAO', 'OUTROS']
  },
  
  product: {
    code: { minLength: 3, maxLength: 20 },
    types: ['MP', 'SP', 'PA', 'COMP', 'SERV'],
    units: ['PC', 'KG', 'M', 'L', 'M2', 'M3']
  }
};

// Mensagens do sistema
export const SystemMessages = {
  success: {
    orderCreated: 'Ordem de produção criada com sucesso',
    transferCompleted: 'Transferência realizada com sucesso',
    movementRecorded: 'Movimentação registrada com sucesso',
    appointmentSaved: 'Apontamento salvo com sucesso'
  },
  
  error: {
    insufficientStock: 'Saldo insuficiente para realizar a operação',
    orderNotFound: 'Ordem de produção não encontrada',
    productNotFound: 'Produto não encontrado',
    warehouseNotFound: 'Armazém não encontrado',
    invalidQuantity: 'Quantidade inválida',
    duplicateDocument: 'Documento já existe no sistema'
  },
  
  warning: {
    lowStock: 'Estoque abaixo do mínimo',
    pendingApproval: 'Aguardando aprovação',
    partialTransfer: 'Transferência parcial realizada',
    qualityInspection: 'Material aguardando inspeção de qualidade'
  },
  
  info: {
    calculating: 'Calculando necessidades...',
    processing: 'Processando solicitação...',
    updating: 'Atualizando dados...',
    generating: 'Gerando relatório...'
  }
};

// Configurações de status e cores
export const StatusConfig = {
  productionOrder: {
    PENDENTE: { color: '#ffc107', icon: 'clock', label: 'Pendente' },
    EM_PRODUCAO: { color: '#17a2b8', icon: 'play', label: 'Em Produção' },
    FINALIZADA: { color: '#28a745', icon: 'check', label: 'Finalizada' },
    CANCELADA: { color: '#dc3545', icon: 'times', label: 'Cancelada' }
  },
  
  movement: {
    ENTRADA: { color: '#28a745', icon: 'arrow-down', label: 'Entrada' },
    SAIDA: { color: '#dc3545', icon: 'arrow-up', label: 'Saída' }
  },
  
  criticality: {
    CRITICA: { color: '#dc3545', icon: 'exclamation-triangle', label: 'Crítica' },
    ALTA: { color: '#fd7e14', icon: 'exclamation', label: 'Alta' },
    MEDIA: { color: '#ffc107', icon: 'minus', label: 'Média' },
    BAIXA: { color: '#28a745', icon: 'check', label: 'Baixa' }
  }
};

// Exportar configuração padrão
export default getConfig();
