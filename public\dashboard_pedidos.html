<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard - Pedidos de Compra</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card.success { border-left-color: #27ae60; }
        .card.warning { border-left-color: #f39c12; }
        .card.danger { border-left-color: #e74c3c; }
        .card.info { border-left-color: #3498db; }
        .card.primary { border-left-color: #9b59b6; }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-icon {
            font-size: 2em;
            opacity: 0.7;
        }

        .card-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .card-subtitle {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 15px;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-fill.success { background: #27ae60; }
        .progress-fill.warning { background: #f39c12; }
        .progress-fill.danger { background: #e74c3c; }

        .chart-container {
            grid-column: span 2;
            min-height: 400px;
        }

        .table-container {
            grid-column: span 3;
            max-height: 400px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-aprovado { background: #d4edda; color: #155724; }
        .status-enviado { background: #cce5ff; color: #004085; }
        .status-atrasado { background: #f8d7da; color: #721c24; }
        .status-recebido { background: #e2e3e5; color: #383d41; }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin: 20px 30px;
            border-radius: 8px;
            border-left: 5px solid;
        }

        .alert.danger {
            background: #f8d7da;
            border-left-color: #e74c3c;
            color: #721c24;
        }

        .alert.warning {
            background: #fff3cd;
            border-left-color: #f39c12;
            color: #856404;
        }

        .alert.success {
            background: #d4edda;
            border-left-color: #27ae60;
            color: #155724;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                padding: 15px;
            }
            
            .chart-container,
            .table-container {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Dashboard - Pedidos de Compra</h1>
            <p>Acompanhamento em tempo real do processo pós-aprovação</p>
        </div>

        <!-- Alertas Críticos -->
        <div id="alertasCriticos"></div>

        <!-- Loading -->
        <div class="loading" id="loading">
            <i class="fas fa-spinner"></i>
            <p>Carregando dados...</p>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid" id="dashboardGrid" style="display: none;">
            
            <!-- Card: Total de Pedidos Aprovados -->
            <div class="card success">
                <div class="card-header">
                    <div class="card-title">Pedidos Aprovados</div>
                    <i class="fas fa-check-circle card-icon" style="color: #27ae60;"></i>
                </div>
                <div class="card-value" id="totalAprovados">0</div>
                <div class="card-subtitle">Aguardando entrega</div>
                <div class="progress-bar">
                    <div class="progress-fill success" id="progressAprovados"></div>
                </div>
            </div>

            <!-- Card: Pedidos Enviados -->
            <div class="card info">
                <div class="card-header">
                    <div class="card-title">Pedidos Enviados</div>
                    <i class="fas fa-paper-plane card-icon" style="color: #3498db;"></i>
                </div>
                <div class="card-value" id="totalEnviados">0</div>
                <div class="card-subtitle">Comunicados ao fornecedor</div>
                <div class="progress-bar">
                    <div class="progress-fill info" id="progressEnviados"></div>
                </div>
            </div>

            <!-- Card: Pedidos em Atraso -->
            <div class="card danger">
                <div class="card-header">
                    <div class="card-title">Pedidos em Atraso</div>
                    <i class="fas fa-exclamation-triangle card-icon" style="color: #e74c3c;"></i>
                </div>
                <div class="card-value" id="totalAtrasados">0</div>
                <div class="card-subtitle">Requerem atenção urgente</div>
                <div class="progress-bar">
                    <div class="progress-fill danger" id="progressAtrasados"></div>
                </div>
            </div>

            <!-- Card: Próximos do Vencimento -->
            <div class="card warning">
                <div class="card-header">
                    <div class="card-title">Próximos do Vencimento</div>
                    <i class="fas fa-clock card-icon" style="color: #f39c12;"></i>
                </div>
                <div class="card-value" id="totalProximos">0</div>
                <div class="card-subtitle">Próximos 7 dias</div>
                <div class="progress-bar">
                    <div class="progress-fill warning" id="progressProximos"></div>
                </div>
            </div>

            <!-- Card: Valor Total em Trânsito -->
            <div class="card primary">
                <div class="card-header">
                    <div class="card-title">Valor em Trânsito</div>
                    <i class="fas fa-dollar-sign card-icon" style="color: #9b59b6;"></i>
                </div>
                <div class="card-value" id="valorTransito">R$ 0,00</div>
                <div class="card-subtitle">Pedidos aprovados/enviados</div>
            </div>

            <!-- Card: Performance de Entrega -->
            <div class="card info">
                <div class="card-header">
                    <div class="card-title">Performance de Entrega</div>
                    <i class="fas fa-chart-line card-icon" style="color: #3498db;"></i>
                </div>
                <div class="card-value" id="performanceEntrega">0%</div>
                <div class="card-subtitle">Entregas no prazo (últimos 30 dias)</div>
                <div class="progress-bar">
                    <div class="progress-fill info" id="progressPerformance"></div>
                </div>
            </div>

            <!-- Tabela: Pedidos Críticos -->
            <div class="card table-container">
                <div class="card-header">
                    <div class="card-title">🚨 Pedidos Críticos</div>
                    <i class="fas fa-list card-icon" style="color: #e74c3c;"></i>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Fornecedor</th>
                            <th>Valor</th>
                            <th>Dias Atraso</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="pedidosCriticosTable">
                        <tr>
                            <td colspan="5" style="text-align: center; color: #7f8c8d;">
                                Carregando dados...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>
    </div>

    <!-- Botão de Refresh -->
    <button class="refresh-btn" onclick="loadDashboardData()" title="Atualizar dados">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let pedidosCompra = [];
        let fornecedores = [];

        async function loadDashboardData() {
            try {
                showLoading(true);
                
                // Carregar dados
                const [pedidosSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                pedidosCompra = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 Dados carregados:', {
                    pedidos: pedidosCompra.length,
                    fornecedores: fornecedores.length
                });

                // Processar dados
                processarDados();
                
                showLoading(false);
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                showError('Erro ao carregar dados do dashboard');
                showLoading(false);
            }
        }

        function processarDados() {
            const hoje = new Date();
            
            // Filtrar pedidos relevantes (aprovados e enviados)
            const pedidosRelevantes = pedidosCompra.filter(p => 
                p.status === 'APROVADO' || p.status === 'ENVIADO'
            );

            let totalAprovados = 0;
            let totalEnviados = 0;
            let totalAtrasados = 0;
            let totalProximos = 0;
            let valorTransito = 0;
            let pedidosCriticos = [];

            pedidosRelevantes.forEach(pedido => {
                // Contadores por status
                if (pedido.status === 'APROVADO') totalAprovados++;
                if (pedido.status === 'ENVIADO') totalEnviados++;

                // Valor em trânsito
                valorTransito += pedido.valorTotal || 0;

                // Análise de prazos
                if (pedido.dataEntregaPrevista) {
                    const dataEntrega = new Date(pedido.dataEntregaPrevista.seconds * 1000);
                    const diffTime = hoje - dataEntrega;
                    const diasAtraso = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    if (diasAtraso > 0) {
                        totalAtrasados++;
                        
                        // Adicionar aos críticos se muito atrasado
                        if (diasAtraso > 3) {
                            const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                            pedidosCriticos.push({
                                ...pedido,
                                fornecedorNome: fornecedor?.razaoSocial || 'N/A',
                                diasAtraso: diasAtraso
                            });
                        }
                    } else if (diasAtraso >= -7 && diasAtraso <= 0) {
                        totalProximos++;
                    }
                }
            });

            // Atualizar interface
            atualizarCards(totalAprovados, totalEnviados, totalAtrasados, totalProximos, valorTransito, pedidosRelevantes.length);
            atualizarTabelaCriticos(pedidosCriticos);
            atualizarAlertas(totalAtrasados, totalProximos);
            calcularPerformance();
        }

        function atualizarCards(aprovados, enviados, atrasados, proximos, valor, total) {
            document.getElementById('totalAprovados').textContent = aprovados;
            document.getElementById('totalEnviados').textContent = enviados;
            document.getElementById('totalAtrasados').textContent = atrasados;
            document.getElementById('totalProximos').textContent = proximos;
            document.getElementById('valorTransito').textContent = `R$ ${valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;

            // Atualizar barras de progresso
            if (total > 0) {
                document.getElementById('progressAprovados').style.width = `${(aprovados/total)*100}%`;
                document.getElementById('progressEnviados').style.width = `${(enviados/total)*100}%`;
                document.getElementById('progressAtrasados').style.width = `${(atrasados/total)*100}%`;
                document.getElementById('progressProximos').style.width = `${(proximos/total)*100}%`;
            }
        }

        function atualizarTabelaCriticos(criticos) {
            const tbody = document.getElementById('pedidosCriticosTable');
            
            if (criticos.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; color: #27ae60;">
                            <i class="fas fa-check-circle"></i> Nenhum pedido crítico
                        </td>
                    </tr>
                `;
                return;
            }

            // Ordenar por dias de atraso (maior primeiro)
            criticos.sort((a, b) => b.diasAtraso - a.diasAtraso);

            tbody.innerHTML = criticos.slice(0, 10).map(pedido => `
                <tr>
                    <td><strong>${pedido.numero || 'N/A'}</strong></td>
                    <td>${pedido.fornecedorNome}</td>
                    <td>R$ ${(pedido.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
                    <td><span class="status-badge status-atrasado">${pedido.diasAtraso} dias</span></td>
                    <td><span class="status-badge status-${pedido.status.toLowerCase()}">${pedido.status}</span></td>
                </tr>
            `).join('');
        }

        function atualizarAlertas(atrasados, proximos) {
            const alertContainer = document.getElementById('alertasCriticos');
            let alertas = [];

            if (atrasados > 0) {
                alertas.push(`
                    <div class="alert danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Atenção:</strong> ${atrasados} pedidos estão em atraso e requerem ação imediata.
                    </div>
                `);
            }

            if (proximos > 5) {
                alertas.push(`
                    <div class="alert warning">
                        <i class="fas fa-clock"></i>
                        <strong>Aviso:</strong> ${proximos} pedidos vencem nos próximos 7 dias.
                    </div>
                `);
            }

            if (alertas.length === 0) {
                alertas.push(`
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Excelente:</strong> Todos os pedidos estão dentro do prazo!
                    </div>
                `);
            }

            alertContainer.innerHTML = alertas.join('');
        }

        function calcularPerformance() {
            // Calcular performance dos últimos 30 dias
            const trintaDiasAtras = new Date();
            trintaDiasAtras.setDate(trintaDiasAtras.getDate() - 30);

            const pedidosRecentes = pedidosCompra.filter(p => {
                if (!p.dataCriacao) return false;
                const dataCriacao = new Date(p.dataCriacao.seconds * 1000);
                return dataCriacao >= trintaDiasAtras && p.status === 'RECEBIDO';
            });

            let entregasNoPrazo = 0;
            const totalEntregas = pedidosRecentes.length;

            pedidosRecentes.forEach(pedido => {
                if (pedido.dataEntregaPrevista && pedido.dataRecebimento) {
                    const dataPrevista = new Date(pedido.dataEntregaPrevista.seconds * 1000);
                    const dataRecebimento = new Date(pedido.dataRecebimento.seconds * 1000);
                    
                    if (dataRecebimento <= dataPrevista) {
                        entregasNoPrazo++;
                    }
                }
            });

            const performance = totalEntregas > 0 ? Math.round((entregasNoPrazo / totalEntregas) * 100) : 0;
            
            document.getElementById('performanceEntrega').textContent = `${performance}%`;
            document.getElementById('progressPerformance').style.width = `${performance}%`;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('dashboardGrid').style.display = show ? 'none' : 'grid';
        }

        function showError(message) {
            const alertContainer = document.getElementById('alertasCriticos');
            alertContainer.innerHTML = `
                <div class="alert danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Erro:</strong> ${message}
                </div>
            `;
        }

        // Disponibilizar função globalmente
        window.loadDashboardData = loadDashboardData;

        // Carregar dados ao inicializar
        document.addEventListener('DOMContentLoaded', () => {
            loadDashboardData();
            
            // Auto-refresh a cada 5 minutos
            setInterval(loadDashboardData, 5 * 60 * 1000);
        });

    </script>
</body>
</html>
