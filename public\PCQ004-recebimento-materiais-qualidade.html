<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCQ004 - Recebimento de Materiais com Qualidade</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e67e22, #d35400);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .quality-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content {
            padding: 30px;
        }

        .quality-info {
            background: linear-gradient(135deg, #fef9e7, #fcf3cf);
            border: 2px solid #f39c12;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .quality-info h3 {
            color: #e67e22;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quality-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .quality-feature {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e67e22;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .quality-feature h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quality-feature p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .workflow-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .workflow-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .workflow-step {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
            border-top: 4px solid #e67e22;
        }

        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #bdc3c7;
        }

        .workflow-step:last-child::after {
            display: none;
        }

        .workflow-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #e67e22;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5em;
        }

        .workflow-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .workflow-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .destination-section {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .destination-section h3 {
            color: #27ae60;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .destination-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .destination-option {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .destination-option:hover {
            border-color: #27ae60;
            transform: translateY(-2px);
        }

        .destination-option.selected {
            border-color: #27ae60;
            background: #f0f8f0;
        }

        .destination-option h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .destination-option p {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 15px;
        }

        .destination-features {
            list-style: none;
        }

        .destination-features li {
            color: #27ae60;
            font-size: 0.8em;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #e67e22;
            box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
        }

        .form-group.required label::after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
        }

        .status-recebido {
            background: #cce5ff;
            color: #004085;
        }

        .status-inspecao {
            background: #f8d7da;
            color: #721c24;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f5c6cb;
            color: #721c24;
        }

        .quality-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .quality-active {
            background: #d4edda;
            color: #155724;
        }

        .quality-inactive {
            background: #e2e3e5;
            color: #383d41;
        }

        .destination-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .dest-qualidade {
            background: #fff3cd;
            color: #856404;
        }

        .dest-estoque {
            background: #cce5ff;
            color: #004085;
        }

        .item-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading i {
            font-size: 3em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            max-width: 900px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #e67e22;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .pedidos-list, .recebimento-details {
            display: grid;
            gap: 15px;
        }

        .pedido-item {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pedido-item:hover {
            border-color: #e67e22;
        }

        .pedido-checkbox {
            display: none;
        }

        .pedido-checkbox:checked + .pedido-label {
            border-color: #e67e22;
            background: #fef5e7;
        }

        .pedido-label {
            display: block;
            padding: 15px;
            cursor: pointer;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .ped-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .ped-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .ped-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .quality-tag {
            color: #27ae60;
            font-weight: 600;
            font-size: 0.8em;
        }

        .detail-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
        }

        .detail-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .detail-grid div {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .quality-section {
            background: #f0f8f0;
            border-color: #27ae60;
        }

        .quality-info-display {
            display: grid;
            gap: 15px;
        }

        .destino-info {
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #e67e22;
        }

        .destination-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .dest-qualidade {
            background: #d4edda;
            color: #155724;
        }

        .dest-estoque {
            background: #cce5ff;
            color: #004085;
        }

        .quality-config-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .config-item.active {
            background: #d4edda;
            color: #155724;
        }

        .config-item.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .itens-list {
            display: grid;
            gap: 10px;
        }

        .item-detail {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e67e22;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .item-qty {
            background: #e67e22;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .item-description {
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .item-lote, .item-validade {
            color: #27ae60;
            font-size: 0.9em;
            font-weight: 600;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .quality-features {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                grid-template-columns: 1fr;
            }
            
            .destination-options {
                grid-template-columns: 1fr;
            }
            
            .workflow-step::after {
                content: '↓';
                right: 50%;
                top: auto;
                bottom: -15px;
                transform: translateX(50%);
            }
            
            .workflow-step:last-child::after {
                display: none;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .ped-details, .detail-grid, .quality-config-display {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="quality-badge">
                <i class="fas fa-shield-alt"></i>
                Processo com Qualidade
            </div>
            <h1>📦 PCQ004 - Recebimento de Materiais</h1>
            <p>Recebimento de materiais integrado com controle de qualidade</p>
        </div>

        <div class="content">
            <!-- Informações sobre Qualidade -->
            <div class="quality-info">
                <h3><i class="fas fa-award"></i> Funcionalidades de Qualidade Integradas</h3>
                <div class="quality-features">
                    <div class="quality-feature">
                        <h4><i class="fas fa-warehouse"></i> Armazém de Qualidade</h4>
                        <p>Direcionamento automático para área de quarentena e inspeção</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-search"></i> Inspeção Automática</h4>
                        <p>Criação automática de inspeções baseadas nas especificações do pedido</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-barcode"></i> Rastreabilidade</h4>
                        <p>Controle de lotes e rastreabilidade completa desde o recebimento</p>
                    </div>
                    <div class="quality-feature">
                        <h4><i class="fas fa-clipboard-check"></i> Liberação Controlada</h4>
                        <p>Liberação para estoque principal apenas após aprovação da qualidade</p>
                    </div>
                </div>
            </div>

            <!-- Fluxo do Processo -->
            <div class="workflow-section">
                <h3><i class="fas fa-sitemap"></i> Fluxo do Recebimento com Qualidade</h3>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="workflow-title">1. Chegada</div>
                        <div class="workflow-description">Material chega conforme pedido de compra</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="workflow-title">2. Quarentena</div>
                        <div class="workflow-description">Direcionamento para Armazém de Qualidade</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="workflow-title">3. Inspeção</div>
                        <div class="workflow-description">Inspeção baseada nas especificações</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="workflow-title">4. Aprovação</div>
                        <div class="workflow-description">Liberação pela equipe de qualidade</div>
                    </div>

                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="workflow-title">5. Estoque</div>
                        <div class="workflow-description">Transferência para estoque principal</div>
                    </div>
                </div>
            </div>

            <!-- Seleção de Destino -->
            <div class="destination-section" id="destinationSection" style="display: none;">
                <h3><i class="fas fa-map-marker-alt"></i> Destino do Material</h3>
                <div class="destination-options">
                    <div class="destination-option" id="destQualidade" onclick="selecionarDestino('QUALIDADE')">
                        <h4><i class="fas fa-warehouse"></i> Armazém de Qualidade</h4>
                        <p>Material será direcionado para área de quarentena e inspeção</p>
                        <ul class="destination-features">
                            <li><i class="fas fa-check"></i> Inspeção obrigatória</li>
                            <li><i class="fas fa-check"></i> Controle de lotes</li>
                            <li><i class="fas fa-check"></i> Rastreabilidade completa</li>
                            <li><i class="fas fa-check"></i> Liberação controlada</li>
                        </ul>
                    </div>

                    <div class="destination-option" id="destEstoque" onclick="selecionarDestino('ESTOQUE')">
                        <h4><i class="fas fa-boxes"></i> Estoque Principal</h4>
                        <p>Material será direcionado diretamente para o estoque principal</p>
                        <ul class="destination-features">
                            <li><i class="fas fa-check"></i> Disponibilidade imediata</li>
                            <li><i class="fas fa-check"></i> Processo simplificado</li>
                            <li><i class="fas fa-check"></i> Sem inspeção adicional</li>
                            <li><i class="fas fa-check"></i> Fluxo padrão</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Alertas -->
            <div id="alertContainer"></div>

            <!-- Ações -->
            <div class="actions">
                <button class="btn btn-success" onclick="novoRecebimento()">
                    <i class="fas fa-plus"></i> Novo Recebimento
                </button>
                <button class="btn btn-warning" onclick="importarPedidos()">
                    <i class="fas fa-download"></i> Importar Pedidos
                </button>
                <button class="btn btn-primary" onclick="consultarInspecoes()">
                    <i class="fas fa-search"></i> Consultar Inspeções
                </button>
                <button class="btn btn-danger" onclick="relatorioRecebimentos()">
                    <i class="fas fa-chart-bar"></i> Relatório
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>Carregando recebimentos...</p>
            </div>

            <!-- Tabela de Recebimentos -->
            <div class="table-container" id="tableContainer" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Pedido</th>
                            <th>Fornecedor</th>
                            <th>Data Recebimento</th>
                            <th>Itens</th>
                            <th>Qualidade</th>
                            <th>Destino</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- Estado Vazio -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-truck-loading"></i>
                <h3>Nenhum recebimento encontrado</h3>
                <p>Não há recebimentos de materiais cadastrados no momento.</p>
                <button class="btn btn-primary" onclick="novoRecebimento()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Registrar Primeiro Recebimento
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, addDoc, getDocs, query, where, orderBy, Timestamp, updateDoc, doc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let recebimentos = [];
        let pedidos = [];
        let fornecedores = [];
        let parametrosQualidade = {};
        let destinoSelecionado = null;

        // Inicializar página
        document.addEventListener('DOMContentLoaded', async function() {
            await carregarParametrosQualidade();
            await carregarDados();
            renderizarTabela();
        });

        // Carregar parâmetros de qualidade
        async function carregarParametrosQualidade() {
            try {
                const doc = await db.collection('parametros').doc('sistema').get();
                if (doc.exists) {
                    parametrosQualidade = doc.data();
                    console.log('✅ PCQ004 - Parâmetros de qualidade carregados:', parametrosQualidade);

                    // Verificar se módulo está ativo
                    if (!parametrosQualidade.moduloQualidadeAtivo) {
                        mostrarAlerta('warning', '⚠️ Módulo de qualidade não está ativo. Redirecionando para versão padrão...');
                        setTimeout(() => {
                            window.location.href = 'recebimento_materiais_melhorado.html';
                        }, 3000);
                        return;
                    }
                }
            } catch (error) {
                console.error('❌ Erro ao carregar parâmetros:', error);
                mostrarAlerta('danger', 'Erro ao carregar configurações de qualidade');
            }
        }

        // Carregar todos os dados necessários
        async function carregarDados() {
            try {
                mostrarLoading(true);

                const [recebimentosSnap, pedidosSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "recebimentosMateriais")),
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                recebimentos = recebimentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('📊 PCQ004 - Dados carregados:', {
                    recebimentos: recebimentos.length,
                    pedidos: pedidos.length,
                    fornecedores: fornecedores.length
                });

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                mostrarAlerta('danger', 'Erro ao carregar dados dos recebimentos');
                mostrarLoading(false);
            }
        }

        // Renderizar tabela de recebimentos
        function renderizarTabela() {
            const tbody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('tableContainer');
            const emptyState = document.getElementById('emptyState');

            if (recebimentos.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = recebimentos.map(recebimento => {
                const pedido = pedidos.find(p => p.id === recebimento.pedidoId);
                const fornecedor = fornecedores.find(f => f.id === recebimento.fornecedorId);
                const dataRecebimento = recebimento.dataRecebimento ?
                    new Date(recebimento.dataRecebimento.seconds * 1000).toLocaleDateString() : 'N/A';

                // Verificar se tem processo de qualidade
                const temQualidade = recebimento.processoQualidade?.ativo ||
                                   pedido?.processoQualidade?.ativo || false;
                const destino = recebimento.destino || 'ESTOQUE_PRINCIPAL';

                return `
                    <tr>
                        <td><strong>${recebimento.numero || 'N/A'}</strong></td>
                        <td>${pedido?.numero || 'N/A'}</td>
                        <td>${fornecedor?.razaoSocial || 'N/A'}</td>
                        <td>${dataRecebimento}</td>
                        <td>${recebimento.itens?.length || 0} itens</td>
                        <td>
                            <span class="quality-indicator ${temQualidade ? 'quality-active' : 'quality-inactive'}">
                                <i class="fas ${temQualidade ? 'fa-shield-alt' : 'fa-info-circle'}"></i>
                                ${temQualidade ? 'Ativo' : 'Padrão'}
                            </span>
                        </td>
                        <td>
                            <span class="destination-indicator ${destino === 'ARMAZEM_QUALIDADE' ? 'dest-qualidade' : 'dest-estoque'}">
                                <i class="fas ${destino === 'ARMAZEM_QUALIDADE' ? 'fa-warehouse' : 'fa-boxes'}"></i>
                                ${destino === 'ARMAZEM_QUALIDADE' ? 'Qualidade' : 'Estoque'}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-${recebimento.status?.toLowerCase() || 'pendente'}">
                                ${recebimento.status || 'PENDENTE'}
                            </span>
                        </td>
                        <td>
                            <div class="item-actions">
                                <button class="btn btn-primary btn-action" onclick="visualizarRecebimento('${recebimento.id}')" title="Visualizar">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${recebimento.status === 'PENDENTE' ? `
                                    <button class="btn btn-success btn-action" onclick="confirmarRecebimento('${recebimento.id}')" title="Confirmar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                ` : ''}
                                ${temQualidade && recebimento.status === 'RECEBIDO' ? `
                                    <button class="btn btn-warning btn-action" onclick="verInspecoes('${recebimento.id}')" title="Inspeções">
                                        <i class="fas fa-search"></i>
                                    </button>
                                ` : ''}
                                ${recebimento.status === 'INSPECAO' ? `
                                    <button class="btn btn-danger btn-action" onclick="acompanharInspecao('${recebimento.id}')" title="Acompanhar">
                                        <i class="fas fa-clock"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Novo recebimento
        window.novoRecebimento = function() {
            // Verificar se há pedidos enviados disponíveis
            const pedidosEnviados = pedidos.filter(p => p.status === 'ENVIADO' || p.status === 'APROVADO');

            if (pedidosEnviados.length === 0) {
                mostrarAlerta('warning', '⚠️ Não há pedidos enviados disponíveis para recebimento.');
                return;
            }

            // Mostrar seleção de destino se módulo de qualidade estiver ativo
            if (parametrosQualidade.moduloQualidadeAtivo) {
                mostrarSelecaoDestino();
            } else {
                // Fluxo padrão - direto para estoque
                criarRecebimento('ESTOQUE');
            }
        };

        // Mostrar seleção de destino
        function mostrarSelecaoDestino() {
            document.getElementById('destinationSection').style.display = 'block';
            document.getElementById('tableContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';

            // Configurar destino padrão baseado nos parâmetros
            if (parametrosQualidade.armazemQualidade || parametrosQualidade.inspecaoRecebimento) {
                selecionarDestino('QUALIDADE');
            } else {
                selecionarDestino('ESTOQUE');
            }
        }

        // Selecionar destino
        window.selecionarDestino = function(destino) {
            // Remover seleção anterior
            document.querySelectorAll('.destination-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Selecionar novo destino
            if (destino === 'QUALIDADE') {
                document.getElementById('destQualidade').classList.add('selected');
                destinoSelecionado = 'ARMAZEM_QUALIDADE';
            } else {
                document.getElementById('destEstoque').classList.add('selected');
                destinoSelecionado = 'ESTOQUE_PRINCIPAL';
            }

            // Mostrar botão de confirmação
            if (!document.getElementById('btnConfirmarDestino')) {
                const btnConfirmar = document.createElement('button');
                btnConfirmar.id = 'btnConfirmarDestino';
                btnConfirmar.className = 'btn btn-success';
                btnConfirmar.innerHTML = '<i class="fas fa-check"></i> Confirmar Destino';
                btnConfirmar.onclick = () => criarRecebimento(destino);

                const destinationSection = document.getElementById('destinationSection');
                destinationSection.appendChild(btnConfirmar);
            }
        };

        // Criar recebimento
        async function criarRecebimento(tipoDestino) {
            try {
                mostrarLoading(true);

                // Simular criação de recebimento
                const novoRecebimento = {
                    numero: 'REC-' + Date.now(),
                    pedidoId: 'pedido_exemplo', // TODO: Implementar seleção de pedido
                    fornecedorId: 'fornecedor_exemplo',
                    dataRecebimento: Timestamp.now(),
                    destino: destinoSelecionado || 'ESTOQUE_PRINCIPAL',
                    status: 'RECEBIDO',
                    itens: [], // TODO: Implementar seleção de itens

                    // 🔍 PROCESSO DE QUALIDADE
                    processoQualidade: {
                        ativo: parametrosQualidade.moduloQualidadeAtivo && tipoDestino === 'QUALIDADE',
                        versao: 'PCQ004',
                        destino: destinoSelecionado,
                        inspecaoRequerida: parametrosQualidade.inspecaoRecebimento && tipoDestino === 'QUALIDADE',
                        armazemQualidade: parametrosQualidade.armazemQualidade && tipoDestino === 'QUALIDADE'
                    },

                    dataCriacao: Timestamp.now(),
                    usuarioCriacao: 'usuario_atual'
                };

                // 🔍 LÓGICA ESPECÍFICA DE QUALIDADE
                if (tipoDestino === 'QUALIDADE' && parametrosQualidade.moduloQualidadeAtivo) {
                    // Registrar no armazém de qualidade
                    await registrarArmazemQualidade(novoRecebimento);

                    // Criar inspeção automática se necessário
                    if (parametrosQualidade.inspecaoRecebimento) {
                        await criarInspecaoAutomatica(novoRecebimento);
                        novoRecebimento.status = 'INSPECAO';
                    }
                }

                await addDoc(collection(db, "recebimentosMateriais"), novoRecebimento);

                mostrarAlerta('success', `🚀 Recebimento criado com sucesso! Destino: ${destinoSelecionado === 'ARMAZEM_QUALIDADE' ? 'Armazém de Qualidade' : 'Estoque Principal'}`);

                // Recarregar dados e voltar para lista
                await carregarDados();
                document.getElementById('destinationSection').style.display = 'none';
                renderizarTabela();

                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao criar recebimento:', error);
                mostrarAlerta('danger', 'Erro ao criar recebimento');
                mostrarLoading(false);
            }
        }

        // Registrar no armazém de qualidade
        async function registrarArmazemQualidade(recebimento) {
            try {
                const registroQualidade = {
                    recebimentoId: recebimento.id,
                    pedidoId: recebimento.pedidoId,
                    fornecedorId: recebimento.fornecedorId,
                    itens: recebimento.itens,
                    area: 'QUARENTENA',
                    status: 'QUARENTENA',
                    dataEntrada: Timestamp.now(),
                    responsavel: 'usuario_atual'
                };

                await addDoc(collection(db, "estoqueQualidade"), registroQualidade);
                console.log('✅ Material registrado no armazém de qualidade');

            } catch (error) {
                console.error('❌ Erro ao registrar no armazém de qualidade:', error);
            }
        }

        // Criar inspeção automática
        async function criarInspecaoAutomatica(recebimento) {
            try {
                const inspecao = {
                    codigo: `INS-REC-${recebimento.numero}`,
                    tipo: 'RECEBIMENTO',
                    recebimentoId: recebimento.id,
                    pedidoId: recebimento.pedidoId,
                    fornecedorId: recebimento.fornecedorId,
                    itens: recebimento.itens,
                    status: 'PENDENTE',
                    prioridade: 'MEDIA',
                    dataProgramada: Timestamp.now(),
                    responsavel: 'equipe_qualidade',
                    dataCriacao: Timestamp.now()
                };

                await addDoc(collection(db, "inspecoesRecebimento"), inspecao);
                console.log('✅ Inspeção de recebimento criada automaticamente:', inspecao.codigo);

            } catch (error) {
                console.error('❌ Erro ao criar inspeção automática:', error);
            }
        }

        // Importar pedidos
        window.importarPedidos = function() {
            const pedidosDisponiveis = pedidos.filter(p =>
                p.status === 'ENVIADO' || p.status === 'APROVADO'
            );

            if (pedidosDisponiveis.length === 0) {
                mostrarAlerta('warning', 'Não há pedidos enviados para importar');
                return;
            }

            mostrarModalImportacaoPedidos(pedidosDisponiveis);
        };

        // Mostrar modal de importação de pedidos
        function mostrarModalImportacaoPedidos(pedidosDisponiveis) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-download"></i> Importar Pedidos para Recebimento</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>Selecione os pedidos que estão chegando para recebimento:</p>
                        <div class="pedidos-list">
                            ${pedidosDisponiveis.map(ped => {
                                const fornecedor = fornecedores.find(f => f.id === ped.fornecedorId);
                                const dataEntrega = ped.dataEntrega ? new Date(ped.dataEntrega.seconds * 1000).toLocaleDateString() : 'N/A';
                                return `
                                    <div class="pedido-item">
                                        <input type="checkbox" id="ped_${ped.id}" class="pedido-checkbox" checked>
                                        <label for="ped_${ped.id}" class="pedido-label">
                                            <div class="ped-header">
                                                <strong>${ped.numero}</strong>
                                                <span class="ped-status status-${ped.status?.toLowerCase()}">${ped.status}</span>
                                            </div>
                                            <div class="ped-details">
                                                <div>Fornecedor: ${fornecedor?.razaoSocial || 'N/A'}</div>
                                                <div>Data Entrega: ${dataEntrega}</div>
                                                <div>Itens: ${ped.itens?.length || 0}</div>
                                                <div>Valor: R$ ${(ped.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                                                <div>Destino: ${ped.destino === 'ARMAZEM_QUALIDADE' ? 'Armazém Qualidade' : 'Estoque Principal'}</div>
                                                ${ped.processoQualidade?.ativo ? '<div class="quality-tag">🔍 Com Qualidade</div>' : ''}
                                            </div>
                                        </label>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button class="btn btn-success" onclick="confirmarImportacaoPedidos()">
                            <i class="fas fa-check"></i> Criar Recebimentos
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Confirmar importação de pedidos
        window.confirmarImportacaoPedidos = async function() {
            const checkboxes = document.querySelectorAll('.pedido-checkbox:checked');
            const pedidosSelecionados = Array.from(checkboxes).map(cb =>
                cb.id.replace('ped_', '')
            );

            if (pedidosSelecionados.length === 0) {
                mostrarAlerta('warning', 'Selecione pelo menos um pedido para importar');
                return;
            }

            try {
                mostrarLoading(true);

                for (const pedidoId of pedidosSelecionados) {
                    const pedido = pedidos.find(p => p.id === pedidoId);
                    if (pedido) {
                        await criarRecebimentoAutomatico(pedido);
                    }
                }

                mostrarAlerta('success', `✅ ${pedidosSelecionados.length} recebimentos criados com sucesso!`);

                // Recarregar dados
                await carregarDados();
                renderizarTabela();

                fecharModal();
                mostrarLoading(false);

            } catch (error) {
                console.error('❌ Erro ao importar pedidos:', error);
                mostrarAlerta('danger', 'Erro ao importar pedidos');
                mostrarLoading(false);
            }
        };

        // Criar recebimento automático
        async function criarRecebimentoAutomatico(pedido) {
            const novoRecebimento = {
                numero: 'REC-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5),
                pedidoId: pedido.id,
                fornecedorId: pedido.fornecedorId,
                dataRecebimento: Timestamp.now(),
                destino: pedido.destino || 'ESTOQUE_PRINCIPAL',
                status: 'PENDENTE',
                itens: pedido.itens || [],
                valorTotal: pedido.valorTotal || 0,

                // 🔍 PROCESSO DE QUALIDADE TRANSFERIDO
                processoQualidade: {
                    ativo: pedido.processoQualidade?.ativo || false,
                    versao: 'PCQ004',
                    origem: 'PCQ003',
                    destino: pedido.destino,
                    inspecaoRequerida: pedido.processoQualidade?.configuracoes?.inspecaoRecebimento || false,
                    armazemQualidade: pedido.processoQualidade?.configuracoes?.armazemQualidade || false
                },

                dataCriacao: Timestamp.now(),
                usuarioCriacao: 'usuario_atual'
            };

            // Se tem qualidade e vai para armazém de qualidade
            if (novoRecebimento.processoQualidade.ativo && pedido.destino === 'ARMAZEM_QUALIDADE') {
                // Registrar no armazém de qualidade
                await registrarArmazemQualidade(novoRecebimento);

                // Criar inspeção automática se necessário
                if (novoRecebimento.processoQualidade.inspecaoRequerida) {
                    await criarInspecaoAutomatica(novoRecebimento);
                    novoRecebimento.status = 'AGUARDANDO_INSPECAO';
                }
            }

            await addDoc(collection(db, "recebimentosMateriais"), novoRecebimento);
            console.log('✅ Recebimento criado automaticamente:', novoRecebimento.numero);
        }

        // Consultar inspeções
        window.consultarInspecoes = function() {
            // Redirecionar para PQ001 com filtro de recebimento
            window.location.href = 'PQ001-inspecao-recebimento.html?tipo=RECEBIMENTO';
        };

        // Relatório de recebimentos
        window.relatorioRecebimentos = function() {
            alert('📊 Funcionalidade em desenvolvimento: Relatório de Recebimentos com Qualidade');
        };

        // Visualizar recebimento
        window.visualizarRecebimento = function(id) {
            const recebimento = recebimentos.find(r => r.id === id);
            if (!recebimento) {
                mostrarAlerta('danger', 'Recebimento não encontrado');
                return;
            }

            const pedido = pedidos.find(p => p.id === recebimento.pedidoId);
            const fornecedor = fornecedores.find(f => f.id === recebimento.fornecedorId);

            mostrarModalVisualizacaoRecebimento(recebimento, pedido, fornecedor);
        };

        // Mostrar modal de visualização do recebimento
        function mostrarModalVisualizacaoRecebimento(recebimento, pedido, fornecedor) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-eye"></i> Visualizar Recebimento ${recebimento.numero}</h3>
                        <button class="btn-close" onclick="fecharModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="recebimento-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-info-circle"></i> Dados Gerais</h4>
                                <div class="detail-grid">
                                    <div><strong>Número:</strong> ${recebimento.numero}</div>
                                    <div><strong>Status:</strong> <span class="status-badge status-${recebimento.status?.toLowerCase()}">${recebimento.status}</span></div>
                                    <div><strong>Pedido:</strong> ${pedido?.numero || 'N/A'}</div>
                                    <div><strong>Fornecedor:</strong> ${fornecedor?.razaoSocial || 'N/A'}</div>
                                    <div><strong>Data Recebimento:</strong> ${recebimento.dataRecebimento ? new Date(recebimento.dataRecebimento.seconds * 1000).toLocaleDateString() : 'N/A'}</div>
                                    <div><strong>Valor Total:</strong> R$ ${(recebimento.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                                </div>
                            </div>

                            ${recebimento.processoQualidade?.ativo ? `
                                <div class="detail-section quality-section">
                                    <h4><i class="fas fa-shield-alt"></i> Processo de Qualidade</h4>
                                    <div class="quality-info-display">
                                        <div class="destino-info">
                                            <strong>Destino:</strong>
                                            <span class="destination-indicator ${recebimento.destino === 'ARMAZEM_QUALIDADE' ? 'dest-qualidade' : 'dest-estoque'}">
                                                <i class="fas ${recebimento.destino === 'ARMAZEM_QUALIDADE' ? 'fa-warehouse' : 'fa-boxes'}"></i>
                                                ${recebimento.destino === 'ARMAZEM_QUALIDADE' ? 'Armazém de Qualidade' : 'Estoque Principal'}
                                            </span>
                                        </div>
                                        <div class="quality-config-display">
                                            <div class="config-item ${recebimento.processoQualidade.inspecaoRequerida ? 'active' : 'inactive'}">
                                                <i class="fas ${recebimento.processoQualidade.inspecaoRequerida ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                                Inspeção de recebimento
                                            </div>
                                            <div class="config-item ${recebimento.processoQualidade.armazemQualidade ? 'active' : 'inactive'}">
                                                <i class="fas ${recebimento.processoQualidade.armazemQualidade ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                                Armazém de qualidade
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}

                            <div class="detail-section">
                                <h4><i class="fas fa-list"></i> Itens Recebidos (${recebimento.itens?.length || 0})</h4>
                                <div class="itens-list">
                                    ${(recebimento.itens || []).map(item => `
                                        <div class="item-detail">
                                            <div class="item-header">
                                                <strong>${item.codigo || 'N/A'}</strong>
                                                <span class="item-qty">${item.quantidade || 0} ${item.unidade || 'UN'}</span>
                                            </div>
                                            <div class="item-description">${item.nome || item.descricao || 'N/A'}</div>
                                            ${item.lote ? `<div class="item-lote">Lote: ${item.lote}</div>` : ''}
                                            ${item.dataValidade ? `<div class="item-validade">Validade: ${new Date(item.dataValidade).toLocaleDateString()}</div>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>

                            ${recebimento.observacoes ? `
                                <div class="detail-section">
                                    <h4><i class="fas fa-comment"></i> Observações</h4>
                                    <p>${recebimento.observacoes}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="fecharModal()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                        ${recebimento.status === 'PENDENTE' ? `
                            <button class="btn btn-success" onclick="fecharModal(); confirmarRecebimento('${recebimento.id}');">
                                <i class="fas fa-check"></i> Confirmar Recebimento
                            </button>
                        ` : ''}
                        ${recebimento.processoQualidade?.ativo ? `
                            <button class="btn btn-primary" onclick="fecharModal(); verInspecoes('${recebimento.id}');">
                                <i class="fas fa-search"></i> Ver Inspeções
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Confirmar recebimento
        window.confirmarRecebimento = async function(id) {
            if (confirm('Confirmar o recebimento deste material?')) {
                try {
                    await updateDoc(doc(db, "recebimentosMateriais", id), {
                        status: 'RECEBIDO',
                        dataConfirmacao: Timestamp.now(),
                        usuarioConfirmacao: 'usuario_atual'
                    });

                    mostrarAlerta('success', '✅ Recebimento confirmado com sucesso');
                    await carregarDados();
                    renderizarTabela();

                } catch (error) {
                    console.error('❌ Erro ao confirmar recebimento:', error);
                    mostrarAlerta('danger', 'Erro ao confirmar recebimento');
                }
            }
        };

        // Ver inspeções
        window.verInspecoes = function(id) {
            // Redirecionar para PQ001 com filtro do recebimento
            window.location.href = `PQ001-inspecao-recebimento.html?recebimento=${id}`;
        };

        // Acompanhar inspeção
        window.acompanharInspecao = function(id) {
            // Redirecionar para PQ001 com filtro específico
            window.location.href = `PQ001-inspecao-recebimento.html?recebimento=${id}&status=PENDENTE`;
        };

        // Mostrar alerta
        function mostrarAlerta(tipo, mensagem) {
            const container = document.getElementById('alertContainer');
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.innerHTML = `
                <i class="fas fa-${tipo === 'success' ? 'check-circle' : tipo === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${mensagem}
            `;

            container.innerHTML = '';
            container.appendChild(alerta);
            alerta.style.display = 'block';

            // Auto-hide após 5 segundos
            setTimeout(() => {
                alerta.style.display = 'none';
            }, 5000);
        }

        // Fechar modal
        window.fecharModal = function() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        };

        // Mostrar/esconder loading
        function mostrarLoading(mostrar) {
            document.getElementById('loading').style.display = mostrar ? 'block' : 'none';
        }

        console.log('✅ PCQ004 - Recebimento de Materiais com Qualidade inicializada');
    </script>
</body>
</html>
