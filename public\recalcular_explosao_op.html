<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Recalcular Explosão de OP</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --header-bg: #354a5f;
    }
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f7f7f7; color: var(--text-color); margin: 0; }
    .container { max-width: 900px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); padding: 30px; }
    h1 { color: var(--primary-color); margin-bottom: 20px; }
    label { font-weight: 500; color: var(--text-secondary); }
    input[type="text"] { padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 15px; margin-right: 10px; margin-bottom: 8px; }
    button { background: var(--success-color); color: #fff; padding: 10px 22px; border: none; border-radius: 4px; cursor: pointer; font-weight: 500; font-size: 15px; transition: background 0.2s; }
    button:hover { background: var(--success-hover); }
    .erro { color: var(--danger-color); font-weight: bold; }
    .success { color: var(--success-color); font-weight: bold; }
    .op-block { border: 1px solid var(--border-color); border-radius: 8px; margin-bottom: 24px; padding: 18px 18px 10px 18px; background: var(--secondary-color); box-shadow: 0 1px 4px rgba(8,84,160,0.04); }
    .op-title { font-weight: bold; color: var(--primary-color); font-size: 18px; margin-bottom: 8px; }
    .material-list { margin: 10px 0 20px 30px; }
    .material-list li { margin-bottom: 4px; font-size: 15px; }
    .faltante { color: var(--danger-color); font-weight: bold; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Recalcular Explosão de OP</h1>
    <form id="formBusca">
      <label>Número da OP: <input type="text" id="numeroOP" required></label>
      <button type="submit">Recalcular</button>
    </form>
    <div id="resultado" style="margin-top:30px;"></div>
  </div>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, getDoc, query, where, doc, addDoc, updateDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function buscarOPPorNumero(numero) {
      const snap = await getDocs(query(collection(db, 'ordensProducao'), where('numero', '==', numero)));
      if (snap.empty) return null;
      return { id: snap.docs[0].id, ...snap.docs[0].data() };
    }
    async function buscarProduto(produtoId) {
      const d = await getDoc(doc(db, 'produtos', produtoId));
      return d.exists() ? { id: d.id, ...d.data() } : null;
    }
    async function buscarEstrutura(produtoId) {
      const snap = await getDocs(query(collection(db, 'estruturas'), where('produtoPaiId', '==', produtoId)));
      if (snap.empty) return null;
      return { id: snap.docs[0].id, ...snap.docs[0].data() };
    }
    async function buscarOPsFilhas(paiId) {
      const snap = await getDocs(query(collection(db, 'ordensProducao'), where('produtoPaiId', '==', paiId)));
      return snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }
    async function buscarOPFilhaPorProduto(paiId, produtoId) {
      const snap = await getDocs(query(collection(db, 'ordensProducao'), where('produtoPaiId', '==', paiId), where('produtoId', '==', produtoId)));
      return snap.empty ? null : { id: snap.docs[0].id, ...snap.docs[0].data() };
    }
    async function buscarEstoque(produtoId) {
      let saldo = 0;
      const snap = await getDocs(query(collection(db, 'estoque'), where('codigo', '==', produtoId)));
      snap.forEach(doc => { saldo += Number(doc.data().quantidade || 0); });
      return saldo;
    }

    document.getElementById('formBusca').onsubmit = async function(e) {
  e.preventDefault();
  const resultado = document.getElementById('resultado');
  resultado.innerHTML = 'Processando...';
  const numero = document.getElementById('numeroOP').value.trim();
  const compararEstrutura = document.getElementById('compararEstrutura').checked;
  try {
    const op = await buscarOPPorNumero(numero);
    if (!op) throw new Error('OP não encontrada!');
    const produto = await buscarProduto(op.produtoId);
    if (!produto) throw new Error('Produto da OP não encontrado!');
    const estrutura = await buscarEstrutura(produto.id);
    if (!estrutura) throw new Error('Estrutura do produto não encontrada!');
    const faltantes = [];
    const arvore = await recalcularExplosao(op, produto, estrutura, faltantes);
    let faltantesHtml = '';
    if (faltantes.length > 0) {
      faltantesHtml = '<div class="op-block"><b>Serão criadas as seguintes OPs filhas:</b><ul>';
      for (const f of faltantes) {
        if (f.tipo === 'OP_SP') {
          faltantesHtml += `<li>SP: ${f.produto.codigo} - ${f.produto.descricao} | Qtd: ${f.quantidade} | Pai: ${f.pai.numero}</li>`;
        }
      }
      faltantesHtml += '</ul></div>';
    }
    let comparacaoHtml = '';
    if (compararEstrutura && Array.isArray(op.materiaisNecessarios)) {
      comparacaoHtml = compararNecessidades(op.materiaisNecessarios, estrutura.componentes, produto, op.quantidade);
    }
    resultado.innerHTML = (compararEstrutura ? comparacaoHtml : '') + renderOPTree(arvore) + faltantesHtml + (faltantes.length > 0 ? `<button id="btnCriarFaltantes">Criar OPs/necessidades faltantes</button>` : '<div class="success">Nenhuma falha encontrada.</div>');
    if (faltantes.length > 0) {
      document.getElementById('btnCriarFaltantes').onclick = async function() {
        resultado.innerHTML = 'Criando OPs/necessidades faltantes...';
        await criarFaltantes(faltantes, op);
        resultado.innerHTML = '<div class="success">OPs/necessidades faltantes criadas com sucesso! Recalcule para conferir.</div>';
      };
    }
  } catch (e) {
    resultado.innerHTML = `<span class="erro">${e.message}</span>`;
  }
};

// Função para comparar necessidades originais x estrutura atual
function compararNecessidades(originais, componentesAtuais, produto, quantidade) {
  let html = '<div class="op-block"><b>Comparação de Necessidades (estrutura original x atual):</b><ul>';
  // Mapear originais e atuais por produtoId/componentId
  const mapOrig = {};
  for (const m of originais) mapOrig[m.produtoId] = m;
  const mapAtual = {};
  for (const c of componentesAtuais) mapAtual[c.componentId] = c;
  // Itens removidos ou alterados
  for (const pid in mapOrig) {
    if (!mapAtual[pid]) {
      html += `<li style="color:red;">Removido: ${pid} | Qtd: ${mapOrig[pid].quantidade}</li>`;
    } else {
      const qtdAtual = quantidade * mapAtual[pid].quantidade;
      if (Math.abs(qtdAtual - mapOrig[pid].quantidade) > 0.0001) {
        html += `<li style="color:red;">Alterado: ${pid} | Antes: ${mapOrig[pid].quantidade} | Agora: ${qtdAtual}</li>`;
      }
    }
  }
  // Itens adicionados
  for (const pid in mapAtual) {
    if (!mapOrig[pid]) {
      html += `<li style="color:red;">Adicionado: ${pid} | Qtd: ${quantidade * mapAtual[pid].quantidade}</li>`;
    }
  }
  html += '</ul></div>';
  return html;
}

    async function recalcularExplosao(op, produto, estrutura, faltantes, nivel=0, visited=new Set()) {
      if (nivel > 10) throw new Error('Estrutura muito profunda (possível ciclo)');
      if (visited.has(produto.id)) throw new Error('Ciclo detectado na estrutura: ' + produto.codigo);
      visited.add(produto.id);
      let materiais = [];
      let filhos = [];
      for (const comp of estrutura.componentes) {
        const compProd = await buscarProduto(comp.componentId);
        if (!compProd) continue;
        const qtdNecessaria = op.quantidade * comp.quantidade;
        if (compProd.tipo === 'SP') {
          // Verifica se já existe OP filha para esse SP
          const opFilha = await buscarOPFilhaPorProduto(op.produtoId, compProd.id);
          if (!opFilha) {
            faltantes.push({ tipo: 'OP_SP', pai: op, produto: compProd, quantidade: qtdNecessaria });
            materiais.push({ ...compProd, quantidade: qtdNecessaria, tipo: 'SP', faltante: true, opNumero: null });
          } else {
            const subEstrutura = await buscarEstrutura(compProd.id);
            if (subEstrutura) {
              const filho = await recalcularExplosao(opFilha, compProd, subEstrutura, faltantes, nivel+1, new Set(visited));
              filhos.push(filho);
            }
            materiais.push({ ...compProd, quantidade: qtdNecessaria, tipo: 'SP', opNumero: opFilha.numero });
          }
        } else {
          // MP
          let saldo = await buscarEstoque(compProd.codigo);
          const necessidade = Math.max(0, qtdNecessaria - saldo);
          materiais.push({ ...compProd, quantidade: qtdNecessaria, saldo, necessidade, tipo: 'MP' });
        }
      }
      return { op, produto, materiais, filhos, nivel };
    }

    function renderOPTree(node, pai=true) {
      let html = `<div class="op-block"><div class="op-title">${pai ? 'OP Pai' : 'OP SP'}: ${node.produto.codigo} - ${node.produto.descricao} (Qtd: ${node.op.quantidade})</div>`;
      html += '<ul class="material-list">';
      for (const mat of node.materiais) {
        if (mat.tipo === 'MP') {
          html += `<li>MP: ${mat.codigo} - ${mat.descricao} | Qtd: ${mat.quantidade} | Saldo: ${mat.saldo} | Necessidade: ${mat.necessidade}</li>`;
        } else if (mat.tipo === 'SP') {
          html += `<li${mat.faltante ? ' class="faltante"' : ''}>SP: ${mat.codigo} - ${mat.descricao} | Qtd: ${mat.quantidade}`;
          if (mat.opNumero) html += ` | OP filha: <b>${mat.opNumero}</b>`;
          if (mat.faltante) html += ' (OP filha faltante)';
          html += '</li>';
        }
      }
      html += '</ul>';
      for (const filho of node.filhos) {
        html += renderOPTree(filho, false);
      }
      html += '</div>';
      return html;
    }

    async function criarFaltantes(faltantes, opPai) {
      for (const f of faltantes) {
        if (f.tipo === 'OP_SP') {
          // Cria OP filha para SP faltante
          await addDoc(collection(db, 'ordensProducao'), {
            numero: 'OP' + Math.floor(Math.random()*100000000), // gere seu número conforme sua lógica
            produtoId: f.produto.id,
            produtoPaiId: opPai.produtoId,
            quantidade: f.quantidade,
            status: 'Pendente',
            nivel: (opPai.nivel || 0) + 1,
            dataCriacao: new Date(),
            armazemProducaoId: opPai.armazemProducaoId,
            centroCustoId: opPai.centroCustoId,
            prioridade: opPai.prioridade || 'normal',
            dataEntrega: opPai.dataEntrega
          });
        }
      }
    }
  </script>
</body>
</html>
